<?php
include("web_header_core.php");
include_once(base_path."/admin/config.php");
include_once"engine/admin/inc/lib.inc.php";// call functions
//get the school info
#$_SESSION['usergroup'] = $school_info['id'];

//GET COURSES INFO
$courses = new Courses;
$courses_args = array(
	'id'=>$_GET['id'],
	'school_id'=>$_SESSION['usergroup']
);

$course = $courses->get_all_courses($courses_args);
$_SESSION['course_of_interest'] = $_GET['id'];

dev_debug("test = $custom_enquiry_success_message");
?>

<style type="text/css">
    #enquire_form{ padding-top: 50px; }
    #enquire_form .course_title{ font-size: 34px; margin-bottom: 30px; }
</style>

<div id="enquire_form">

	<?php
	//session_start();
	
	// CHECK FOR SESSION
	//include("engine/admin/inc/lib.inc.php");
	list($schools_school_named)=get_schools($_SESSION['usergroup']);
	//echo $_SESSION['usergroup'];
	
	// GET FULL URL AND EXPLODE
	$checkSlug = explode("/", $_SERVER["REQUEST_URI"]); //analyze rewritten url
	
	// id an edit happens rearrange the slug pulls
	$current_id = find_slug(1,"lead_form");
	
	//echo $slug_name;
	
	// IF NO PAGE CAN BE FOUND, THEN SIMPLY SHOW HOMEPAGE
	if($current_id=="" || $current_id=="application"){
		$current_id="home";
	}
	
	include("reg_index.php");// this page handles all the login and sessions

	?>
    <style>
        .form_style_bkg{
            color:#666;
            padding:15px;
            margin:20px;
            border:1px #f0f0f0 solid;
            -moz-border-radius: 10px;
            -webkit-border-radius: 10px;
            background-color:#fff;
        }
        @media (max-width: 480px) {
            /*less than small*/
            .form_style_bkg{
                color:#666;
                padding:0px;
                /*margin:5px;*/
                border:0px #fff solid;
                -moz-border-radius: 0px;
                -webkit-border-radius: 0px;
            }
    </style>
    <!--REQUIRED IN HEADER FOR EACH CUSTOM HEADER START-->
    <script type="text/javascript" src="<?php echo $front_website_url; ?>/js/sorttable.js"></script>
    <link rel="stylesheet" href="<?php echo $front_website_url; ?>/css/bootstrap.min.css">

    <link rel="stylesheet" href="<?php echo $front_website_url; ?>/css/fontend.css" type="text/css" media="screen">
    <link rel="stylesheet" href="<?php echo $front_website_url; ?>/css/sortable.css" type="text/css" media="screen">
    <link rel="stylesheet" href="<?php echo $front_website_url; ?>/css/style.css" type="text/css" media="screen">
    <script src="<?php echo $front_website_url; ?>/js/datedropdown.js" type="text/javascript" charset="utf-8"></script>
    <script src="<?php echo $front_website_url; ?>/js/gotonextpage_check.js" type="text/javascript" charset="utf-8"></script>
    <script src="<?php echo $front_website_url; ?>/js/submit_appli_check.js" type="text/javascript" charset="utf-8"></script>
    <!--REQUIRED IN HEADER FOR EACH CUSTOM HEADER END-->
    </head>
    <body>
    <div>
		<?php
		
		
		$page_id=pull_field("form_schools","db32496","WHERE id = '$_SESSION[usergroup]' LIMIT 1 ");
		$int_rec='';
		$record_pull='0';
		$appli_submited_check='0';
		$kill_proceed_button='1';
		$button_name = 'Submit Your Form';
		$dbh = get_dbh();
		if(!$page_id || $page_id==''){
            #	echo '<meta http-equiv="refresh" content="0">';
		}
		$event_id=pull_field("sis_event_booking","db32400","WHERE username_id = '{$_GET['u']}' AND usergroup={$_SESSION['usergroup']} LIMIT 1 ");
		
		$event_name=pull_field("sis_event_schedule","db31841","WHERE id=$event_id AND usergroup={$_SESSION['usergroup']} LIMIT 1 ");
		
		$rel_id=pull_field("sis_event_booking","rel_id","WHERE username_id = '$_GET[u]' AND usergroup={$_SESSION['usergroup']} LIMIT 1 ");
		//include("forms_lead.php");
		
		if(!empty($_POST['response'])){
			$sql = "UPDATE sis_event_booking SET db31808 = '".$_POST['response']."' WHERE username_id = '{$_GET["u"]} AND usergroup={$_SESSION['usergroup']} LIMIT 1'";
            dev_debug($sql);
			$sth = $dbh->prepare($sql);
			$sth->execute();
			echo "<span style='font-size:23px;font-weight:bold;'>Thank you for your response.</span>";
			$msg= "I would like to re-arrange the booking to event ".$event_name." to day and time <br/>". $_POST['response'];
			//$msg=addslashes($message);
			$profile_info = explode("|",pull_field("core_students","CONCAT(rec_id,'|',db39,'|',db40,'|',db764)","WHERE id='".$rel_id."'"));
			
			$random_id = random();
			$sql = "INSERT INTO core_notes (username_id, rec_id, usergroup, rel_id, db76, db77, db80, db139) VALUES ('".$random_id."', '{$profile_info[0]}', '".$_SESSION["usergroup"]."', '".$rel_id."', '".$msg."', ' ', ' ', 'no')";
			$sth = $dbh->prepare($sql);
			$sth->execute();
		}
        elseif($_GET["act"]=="resp"){
            $responseQuery = "SELECT
                db31841 as 'event_name',
                db31851 as 'email_template',
                DATE_FORMAT(db31827,'%d/%m/%Y') as 'start_date',
                db31828 as 'start_time',
                (SELECT CONCAT(db14963,'<br/>',db14965,'<br/>',db14966) FROM sis_course_venues WHERE id = db31833) as 'venue',
                db31836 as 'info'
                FROM sis_event_schedule
                WHERE id={$event_id}
                AND usergroup={$_SESSION['usergroup']}
                LIMIT 1
            ";
            dev_debug($responseQuery);
			$sql = $dbh->prepare($responseQuery);
			$sql->execute();
			$event_infos = $sql->fetchAll(\PDO::FETCH_OBJ);
			$event_info = $event_infos[0];
			$event_name = $event_info->event_name;
			$email_template = $event_info->email_template;
			$event_start_date = $event_info->start_date;
			$event_start_time = $event_info->start_time;
			$event_start_time = substr($event_start_time,0,2 ).':'.substr($event_start_time,2,2);
			$event_venue = $event_info->venue;
			$additional_info = $event_info->info;
			
            
            $slotsQuery = "select
                s.db34195,
                s.db34196,
                s.db34448
                from sis_event_booking b
                INNER JOIN sis_event_slots s
                ON s.id = b.db32400
                WHERE b.username_id = {$rel_id}
                AND b.usergroup={$_SESSION['usergroup']} LIMIT 1
            ";
            dev_debug($slotsQuery);
			$sql = $dbh->prepare($slotsQuery);
			$sql->execute();
			$slot_infos = $sql->fetchAll(\PDO::FETCH_OBJ);
			$slot_info = $slot_infos[0];
			if(!empty($slot_info->db34448)){
				$event_start_date = $slot_info->db34448;
			}
			if(!empty($slot_info->db34195)){
				$event_start_time = $slot_info->db34195;
			}
			
			send_confirmation_response($event_id); // send email
			?>

            <form action="" class="form" method="POST">
                <h2>Invitation Details</h2>
                <label style="width: 100%;"><?php echo "Event Name : ".$event_name; ?></label>
                <label style="width: 100%;"><?php echo "Date : ".$event_start_date; ?></label>
                <label style="width: 100%;"><?php echo "Time : ".$event_start_time; ?></label>
                <label style="width: 100%;"><?php echo "Venue : ".$event_venue; ?></label>
                <label style="width: 100%;">Your Response : If you'd like to re-arrange please enter your desired date and time</label>
                <textarea class="form-control" id="response"  name="response"></textarea>
                <button class="btn btn-primary">Submit</button>

            </form>
			<?php
			
		}
        elseif($_GET["act"]=="rej"){
			$sql = "UPDATE sis_event_booking SET db31809 = '4' WHERE username_id = '{$_GET["u"]}' AND usergroup={$_SESSION['usergroup']}";
            dev_debug($sql);
			$sth = $dbh->prepare($sql);
			$sth->execute();
			echo "<span style='font-size:23px;font-weight:bold;'>Thank you for your response your booking has been cancelled</span>";
			$msg="Applicant Rejected Interview : ".$event_name;
			$profile_info = explode("|",pull_field("core_students","CONCAT(rec_id,'|',db39,'|',db40,'|',db764)","WHERE id='{$rel_id}' AND usergroup={$_SESSION['usergroup']} "));
			
			$random_id = random();
			$sql = "INSERT INTO core_notes (username_id, rec_id, usergroup, rel_id, db76, db77, db80, db139)
                VALUES ('{$random_id}', '{$profile_info[0]}', '".$_SESSION["usergroup"]."', '".$rel_id."', '".$msg."', ' ', ' ', 'no')
             ";
            dev_debug($sql);
			$sth = $dbh->prepare($sql);
			$sth->execute();
			
			//CHECK IF IT HAD SLOT $_GET["u"]
			//CHECK IF IT HAD SLOT $_GET["u"]
			$slot_id = pull_field("sis_event_booking","db34338","WHERE username_id = '{$_GET['u']}' AND usergroup={$_SESSION['usergroup']}  LIMIT 1 ");
			if(is_numeric($slot_id)){
				$sql = "UPDATE sis_event_slots SET db34641 = NULL, db34447 = NULL WHERE id = {$slot_id} AND usergroup={$_SESSION['usergroup']} ";
                dev_debug($sql);
				$sth = $dbh->prepare($sql);
				$sth->execute();
			}
			
			//send email
			send_confirmation_response($event_id);
		}
        elseif($_GET["act"]=="acc"){
			$sql = "UPDATE sis_event_booking SET db31809 = '3' WHERE username_id = '{$_GET["u"]}' AND usergroup={$_SESSION['usergroup']}";
            dev_debug($sql);
			$sth = $dbh->prepare($sql);
			$sth->execute();
			echo "<span style='font-size:23px;font-weight:bold;'>Thank you for confirming your booking</span>";
			$msg="Applicant Accepted Interview : ".$event_name;
			//GET USER EMAIL $rel_id
			$profile_info = explode("|",pull_field("core_students","CONCAT(rec_id,'|',db39,'|',db40,'|',db764)","WHERE id='{$rel_id}' AND usergroup={$_SESSION['usergroup']} "));
			$random_id = random();
			$sql = "INSERT INTO core_notes (username_id, rec_id, usergroup, rel_id, db76, db77, db80, db139)
			    VALUES ('{$random_id}', '{$profile_info[0]}', '{$_SESSION['usergroup']}', '{$rel_id}', '{$msg}', ' ', ' ', 'no')
			";
            dev_debug($sql);
			$sth = $dbh->prepare($sql);
			$sth->execute();
			
			//BOOK SLOT
			$slot_id = pull_field("sis_event_booking","db34338","WHERE username_id = '{$_GET['u']}' AND usergroup={$_SESSION['usergroup']} LIMIT 1 ");
			$email_student = $profile_info[3];
			//SET SLOT
			$sql = "UPDATE sis_event_slots SET db34641 = 'yes', db34447 = '".$email_student."' WHERE id ={$slot_id} AND usergroup={$_SESSION['usergroup']} ";
            dev_debug($sql);
			$sth = $dbh->prepare($sql);
			//echo $sql;
			//$sth->execute();
			
			send_confirmation_response($event_id); // send email
		}
		
		function send_confirmation_response($event_id){
			$event_name=pull_field("sis_event_schedule","db31841","WHERE id=$event_id LIMIT 1 ");
			
			if(isset($_POST['response'])){
				$select_email_template = pull_field("coms_template","id","WHERE db1320='rearrange_event_booking' AND usergroup='$_SESSION[usergroup]'");
				if($select_email_template==""){
					$select_email_template = pull_field("coms_template","id","WHERE db1320='rearrange_event_booking' AND usergroup='1'");
				}
			}elseif($_GET["act"]=="rej"){
				$select_email_template = pull_field("coms_template","id","WHERE db1320='reject_event_booking' AND usergroup='$_SESSION[usergroup]'");
				if($select_email_template==""){
					$select_email_template = pull_field("coms_template","id","WHERE db1320='reject_event_booking' AND usergroup='1'");
				}
				
			}elseif($_GET["act"]=="acc"){
				$select_email_template = pull_field("coms_template","id","WHERE db1320='accept_event_booking' AND usergroup='$_SESSION[usergroup]'");
				if($select_email_template==""){
					$select_email_template = pull_field("coms_template","id","WHERE db1320='accept_event_booking' AND usergroup='1'");
				}
				
			}
			
			// get user
			list($core_students_id,$core_students_rec_id,$core_students_usergroup,$core_students_rel_id,$core_students_first_name,$core_students_middle_name,$core_students_surname,$core_students_email_address,$core_students_telephone_number,$core_students_date_of_birth,$core_students_gender,$core_students_source_of_applicant,$core_students_cohort,$core_students_course_of_study,$core_students_level_of_entry,$core_students_country_of_origin,$core_students_application_status,$core_students_has_applied,$core_students_archive_record,$unique_id)=get_core_students($_SESSION['uid']);
			
			list($coms_template_id,$coms_template_rec_id,$coms_template_usergroup,$coms_template_rel_id,$coms_template_template_name,$coms_template_subject_line,$coms_template_plain_text_version,$coms_template_html_version,$coms_template_email_address_to_send_from)=get_coms_template($select_email_template);
			
			//search replace
			$message_plain = email_template_replace_values("{{name}}",$_SESSION['name'],$coms_template_plain_text_version);
			$message_html = email_template_replace_values("{{name}}",$_SESSION['name'],$coms_template_html_version);
			$message_plain = email_template_replace_values("{{student_first_name}}",$core_students_first_name,$message_plain);
			$message_html = email_template_replace_values("{{student_first_name}}",$core_students_first_name,$message_html);
			$message_plain = email_template_replace_values("{{first_name}}",$core_students_first_name,$message_plain);
			$message_html = email_template_replace_values("{{first_name}}",$core_students_first_name,$message_html);
			$message_plain = email_template_replace_values("{{student_surname}}",$core_students_surname,$message_plain);
			$message_html = email_template_replace_values("{{student_surname}}",$core_students_surname,$message_html);
			$message_plain = email_template_replace_values("{{student_course}}",$core_students_course_of_study,$message_plain);
			$message_html = email_template_replace_values("{{student_course}}",$core_students_course_of_study,$message_html);
			$message_plain = email_template_replace_values("{{student_portal}}", "<a href='$host_url'>student portal</a>", $message_plain);
			$message_html = email_template_replace_values("{{student_portal}}", "<a href='$host_url'>student portal</a>", $message_html);
			$message_plain = email_template_replace_values("{{course}}",$core_students_course_of_study,$message_plain);
			$message_html = email_template_replace_values("{{course}}",$core_students_course_of_study,$message_html);
			$message_plain = email_template_replace_values("{{cohort}}",$_SESSION['school_cycle'],$message_plain);
			$message_html = email_template_replace_values("{{cohort}}",$_SESSION['school_cycle'],$message_html);
			$message_plain = email_template_replace_values("{{event_name}}",$event_name,$message_plain);
			$message_html = email_template_replace_values("{{event_name}}",$event_name,$message_html);
			$message_plain = email_template_replace_values("{{middle_name}}", $core_students_middle_name?$core_students_middle_name:"",$message_plain);
			$message_html = email_template_replace_values("{{middle_name}}", $core_students_middle_name?$core_students_middle_name:"",$message_html);
			$emailFrom = master_email;
			$emailTo=pull_field("form_schools","db1118","WHERE id='$_SESSION[usergroup]'");//Main
			
			$message_plain = email_template_replace_values_from_db($message_plain, $_SESSION['uid'], "applicant");
			$message_html = email_template_replace_values_from_db($message_html, $_SESSION['uid'], "applicant");
			
			log_email($emailTo,$coms_template_subject_line,$message_plain,$message_html,$emailFrom,"Event Response Alert");
			
			
		}
		?>
    </div>

</div>


<?php
//check for uni template
if(file_exists(front_header_file_location."/web_footer.php")){
	include(front_header_file_location."/web_footer.php");
}else{
	include(default_theme_files_location."/web_footer.php");
}

?>

