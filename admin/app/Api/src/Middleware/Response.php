<?php
namespace Api\Middleware;

use Api\Middleware\Auth;
use \Json_helper;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response as BaseResponse;

class Response extends BaseResponse{
    private $data;
    private $success;
    private $message;
    public $headers;
    private $status = 200;
    public static $instance;

    public function __construct($headers=[])
    {
        parent::__construct();
        $this->headers = $headers;
    }

    public function redirect($uri)
    {
        return new RedirectResponse($uri);
    }

    public function json($data = [], $status = 200, $headers = [])
    {
        $this->data = $data;
        $this->status = $status;
        return new JsonResponse($this->data, $this->status, $headers);
    }
    /*    We are encrypting the response and just leaving success and message
    */
    public function encrypted_json($data = [], $status = 200, $headers = [], $success=true, $message="")
    {
        if(!empty($headers))$this->headers=$headers;
        $this->headers['X-Message']= $message;
        if($success){
            $auth = new Auth();
            $data = ["data"=>$auth->cryptoJsAesEncrypt($data), "success"=>$success, "message"=>$message, "extreme"=>true];
            return new JsonResponse($data, $status, $this->headers);
        }
        return new JsonResponse([], $status, $this->headers);
    }

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new Response();
        }
        return self::$instance;
    }
    /*
     * @Param $data: Any piece of data that can be json_encoded
     * */



}