<?php

namespace App\core\auth\middleware;

use Closure;
use App\core\auth\AuthenticationException;
use App\core\auth\interfaces\Factory as Auth;

class Authenticate
{
    /**
     * The authentication factory instance.
     *
     * @var \App\core\auth\interfaces\Factory
     */
    protected $auth;

    /**
     * Create a new middleware instance.
     *
     * @param  \App\core\auth\interfaces\Factory  $auth
     * @return void
     */
    public function __construct(Auth $auth)
    {
        $this->auth = $auth;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \App\core\http\Request  $request
     * @param  \Closure  $next
     * @param  string[]  ...$guards
     * @return mixed
     *
     * @throws \App\core\auth\AuthenticationException
     */
    public function handle($request, Closure $next, ...$guards)
    {
        $this->authenticate($request, $guards);

        return $next($request);
    }

    /**
     * Determine if the user is logged in to any of the given guards.
     *
     * @param  \App\core\http\Request  $request
     * @param  array  $guards
     * @return void
     *
     * @throws \App\core\auth\AuthenticationException
     */
    protected function authenticate($request, array $guards)
    {
        if (empty($guards)) {
            $guards = [null];
        }

        foreach ($guards as $guard) {
            if ($this->auth->guard($guard)->check()) {
                return $this->auth->shouldUse($guard);
            }
        }

        $this->unauthenticated($request, $guards);
    }

    /**
     * Handle an unauthenticated user.
     *
     * @param  \App\core\http\Request  $request
     * @param  array  $guards
     * @return void
     *
     * @throws \App\core\auth\AuthenticationException
     */
    protected function unauthenticated($request, array $guards)
    {
        throw new AuthenticationException(
            'Unauthenticated.', $guards, $this->redirectTo($request)
        );
    }

    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param  \App\core\http\Request  $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        if (! $request->expectsJson()) {
            return route('login');
        }
    }
}
