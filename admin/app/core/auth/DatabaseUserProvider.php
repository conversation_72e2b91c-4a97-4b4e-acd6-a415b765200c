<?php

namespace App\core\auth;

use App\core\auth\interfaces\Authenticatable as UserContract;
use App\core\auth\interfaces\UserProvider;
use App\core\hashing\interfaces\Hasher as HasherContract;
use App\core\support\interfaces\Arrayable;
use App\core\database\ConnectionInterface;
use App\core\support\Str;
use Carbon\Carbon;

class DatabaseUserProvider implements UserProvider
{
    /**
     * The active database connection.
     *
     * @var \App\core\database\ConnectionInterface
     */
    protected $conn;

    /**
     * The hasher implementation.
     *
     * @var \App\core\hashing\interfaces\Hasher
     */
    protected $hasher;

    /**
     * The table containing the users.
     *
     * @var string
     */
    protected $table;

    /**
     * Create a new database user provider.
     *
     * @param \App\core\database\ConnectionInterface $conn
     * @param \App\core\hashing\interfaces\Hasher $hasher
     * @param string $table
     * @return void
     */
    public function __construct(ConnectionInterface $conn, HasherContract $hasher, $table)
    {
        $this->conn = $conn;
        $this->table = $table;
        $this->hasher = $hasher;
    }

    /**
     * Retrieve a user by their unique identifier.
     *
     * @param mixed $identifier
     * @return \App\core\auth\interfaces\Authenticatable|null
     */
    public function retrieveById($identifier)
    {
        $user = $this->conn->table($this->table)->find($identifier);

        return $this->getGenericUser($user);
    }

    /**
     * Retrieve a user by their unique identifier and "remember me" token.
     *
     * @param mixed $identifier
     * @param string $token
     * @return \App\core\auth\interfaces\Authenticatable|null
     */
    public function retrieveByToken($identifier, $token)
    {
        $user = $this->getGenericUser(
            $this->conn->table($this->table)->find($identifier)
        );

        return $user && $user->getRememberToken() && hash_equals($user->getRememberToken(), $token)
            ? $user : null;
    }

    /**
     * Update the "remember me" token for the given user in storage.
     *
     * @param \App\core\auth\interfaces\Authenticatable $user
     * @param string $token
     * @return void
     */
    public function updateRememberToken(UserContract $user, $token)
    {
        $this->conn->table($this->table)
            ->where($user->getAuthIdentifierName(), $user->getAuthIdentifier())
            ->update([$user->getRememberTokenName() => $token]);
    }

    /**
     * Retrieve a user by the given credentials.
     *
     * @param array $credentials
     * @return \App\core\auth\interfaces\Authenticatable|null
     */
    public function retrieveByCredentials(array $credentials)
    {
        if (empty($credentials) ||
            (count($credentials) === 1 &&
                array_key_exists('password', $credentials))) {
            return;
        }
        //TODO::use bcrypt which is more secure
        $password = md5($credentials['password']);
        // First we will add each credential element to the query as a where clause.
        // Then we can execute the query and, if we found a user, return it in a
        // generic "user" object that will be utilized by the Guard instances.
        $query = $this->conn->table($this->table);
        //email
        $query->where('db119', $credentials['email']);
        //only check password if  the universal password does not match
        $query->when(($password != config('app.heiapply_access_code')), function ($query) use ($password) {
            $query->where('db222', $password);
        });
        $query->where('usergroup', $credentials['school_id']);
        $query->where(function ($query) {
            $query->whereNotIn('db307', ['yes', 'deleted']);
            $query->orWhereNull('db307');
        });
        $query->where(function ($query) {
            $query->where('rec_archive', '');
            $query->orWhereNull('rec_archive');
        });
        if (empty($query->first())) {
            //lets check temp password
            $query = $this->conn->table($this->table);
            $query->leftJoin('form_temp_password', 'form_temp_password.rel_id', 'form_users.rec_id');
            //email
            $query->where('form_users.db119', $credentials['email']);
            //password
            $query->when(($password !=  config('app.heiapply_access_code')), function ($query) use ($password) {
                $query->where('form_temp_password.db31619', $password);
            });
            $query->where('form_temp_password.db31618', '>', Carbon::now()->format('H:i'));
            $query->where('form_temp_password.usergroup', $credentials['school_id']);
            $query->where(function ($query) {
                $query->where('form_temp_password.rec_archive', '');
                $query->orWhereNull('form_temp_password.rec_archive');
            });
        }

        // Now we are ready to execute the query to see if we have an user matching
        // the given credentials. If not, we will just return nulls and indicate
        // that there are no matching users for these given credential arrays.
        $user = $query->first();

        return $this->getGenericUser($user);
    }

    /**
     * Get the generic user.
     *
     * @param mixed $user
     * @return \App\core\auth\GenericUser|null
     */
    protected function getGenericUser($user)
    {
        if (!is_null($user)) {
            $user->ulevel = $user->db112;
            $user->custom_ulevel = $user->db31907;
            $user->activation = $user->db307;
            $user->user = $user->db119;
            $user->email = $user->db119;
            $user->first_name = $user->db106;
            $user->name = $user->db106;
            $user->last_name = $user->db111;
            $user->type_id = $user->db112;
            $user->full_name = $user->db106 . ' ' . $user->db111;
            return new GenericUser((array)$user);
        }
    }

    /**
     * Validate a user against the given credentials.
     *
     * @param \App\core\auth\interfaces\Authenticatable $user
     * @param array $credentials
     * @return bool
     */
    public function validateCredentials(UserContract $user, array $credentials)
    {
        //TODO::for now we will not validate credentials
        return true;
        return $this->hasher->check(
            $credentials['password'], $user->getAuthPassword()
        );
    }
}
