<?php

namespace App\core\redis\interfaces;

interface Connector
{
    /**
     * Create a connection to a Redis cluster.
     *
     * @param  array  $config
     * @param  array  $options
     * @return \App\core\redis\connections\Connection
     */
    public function connect(array $config, array $options);

    /**
     * Create a connection to a Redis instance.
     *
     * @param  array  $config
     * @param  array  $clusterOptions
     * @param  array  $options
     * @return \App\core\redis\connections\Connection
     */
    public function connectToCluster(array $config, array $clusterOptions, array $options);
}
