<?php
$product_site_url= str_replace("/admin/", '', base_url());
    $order = $data['order'];
	if ($_SESSION['usergroup'] !== $order['usergroup']) {
		track_use("tried to view an order not in their usergroup", "hackattempt");
		die('<div class="alert alert-danger">Sorry this is a corrupt URL!<br/><small>Activity logged.</small></div>');
	}

	if (isset($_GET['vw'])) {
		if ($order['invoice_username'] !== $_GET['vw']) {
			track_use("tried to view an order with invalid username_id", "hackattempt");
			die('<div class="alert alert-danger">Sorry this is a corrupt URL!<br/><small>Activity logged.</small></div>');
		}
	}
	if (isset($_GET['ref'])) {
		if ($order['id'] !== $_GET['ref']) {
			track_use("tried to view an order with invalid id", "hackattempt");
			die('<div class="alert alert-danger">Sorry this is a corrupt URL!<br/><small>Activity logged.</small></div>');
		}
	}
?>
<style type="text/css">[v-cloak] {display: none;}</style>
<script src="/admin/assets/js/bootoast.js"></script>
<script src="/admin/assets/js/vuejs/vue-2.6.11.min.js"></script>
<script src="/admin/assets/js/vuejs/axios.min.js"></script>
<script src="/admin/assets/js/vuejs/flatpickr.min.js"></script>
<script src="/admin/assets/js/vuejs/<EMAIL>"></script>
<script src="/admin/assets/js/vuejs/vue-select.min.js"></script>
<script src="/admin/assets/js/vuejs/vue-json-excel.umd.js"></script>
<script src="/admin/assets/js/vuejs/quill.min.js"></script>
<script src="/admin/assets/js/vuejs/vue2-editor.umd.min.js"></script>
<script src="/admin/assets/js/vuejs/vue-form-wizard.js"></script>
<script src="/admin/assets/js/moment.min.js"></script>

<link href="/admin/assets/css/quill.snow.css" rel="stylesheet">
<link href="/admin/assets/css/bootstrap-4-utilities.min.css" rel="stylesheet">
<link href="/admin/assets/css/flatpickr.min.css" rel="stylesheet">
<link href="/admin/assets/css/vue-select.min.css" rel="stylesheet">
<link href="/admin/assets/css/vue-form-wizard.min.css" rel="stylesheet">
<link href="/admin/assets/css/bootoast.css" rel="stylesheet">
<link href="/admin/assets/css/bootstrap-3-loaders.css" rel="stylesheet">
  <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">

<style>
  /*.ui-tabs-vertical { width: 55em; }*/
  .ui-tabs-vertical .ui-tabs-nav { padding: .2em .1em .2em .2em; float: left; width: 3em; }
  .ui-tabs-vertical .ui-tabs-nav li { clear: left; width: 100%; border-bottom-width: 1px !important; border-right-width: 0 !important; margin: 0 -1px .2em 0; }
  .ui-tabs-vertical .ui-tabs-nav li a { display:block; }
  .ui-tabs-vertical .ui-tabs-nav li.ui-tabs-active { padding-bottom: 0; padding-right: .1em; border-right-width: 1px; }
  .ui-tabs-vertical .ui-tabs-panel { padding: 1em; float: left; width:96%;}
  .horiz{
  	 -webkit-writing-mode: vertical-lr;
      /* old Win safari */
      writing-mode: vertical-rl;
      writing-mode: tb-lr;
      /* writing-mode:sideways-lr;  could be the one */
      /* eventually untill sideways-lr is working everywhere */
      transform: scale(-1, -1);
      list-style: none;
      display: block;
  }

  /*******************************
* MODAL AS LEFT/RIGHT SIDEBAR
* Add "left" or "right" in modal parent div, after class="modal".
* Get free snippets on bootpen.com
*******************************/
    .modal.left .modal-dialog,
    .modal.right .modal-dialog {
        position: fixed;
        margin: auto;
        width: 320px;
        height: 100%;
        -webkit-transform: translate3d(0%, 0, 0);
            -ms-transform: translate3d(0%, 0, 0);
             -o-transform: translate3d(0%, 0, 0);
                transform: translate3d(0%, 0, 0);
    }

    .modal.left .modal-content,
    .modal.right .modal-content {
        height: 100%;
        overflow-y: auto;
    }
    
    .modal.left .modal-body,
    .modal.right .modal-body {
        padding: 15px 15px 80px;
    }

/*Left*/
    .modal.left.fade .modal-dialog{
        left: -320px;
        -webkit-transition: opacity 0.3s linear, left 0.3s ease-out;
           -moz-transition: opacity 0.3s linear, left 0.3s ease-out;
             -o-transition: opacity 0.3s linear, left 0.3s ease-out;
                transition: opacity 0.3s linear, left 0.3s ease-out;
    }
    
    .modal.left.fade.in .modal-dialog{
        left: 0;
    }
        
/*Right*/
    .modal.right.fade .modal-dialog {
        right: -320px;
        -webkit-transition: opacity 0.3s linear, right 0.3s ease-out;
           -moz-transition: opacity 0.3s linear, right 0.3s ease-out;
             -o-transition: opacity 0.3s linear, right 0.3s ease-out;
                transition: opacity 0.3s linear, right 0.3s ease-out;
    }
    
    .modal.right.fade.in .modal-dialog {
        right: 0;
    }

/* ----- MODAL STYLE ----- */
    .modal-content {
        border-radius: 0;
        border: none;
    }

    .modal-header {
        border-bottom-color: #EEEEEE;
        background-color: #FAFAFA;
    }

        /* Container around the input and button */
    .payment-link-cell .payment-link-wrapper {
      display: flex;
      align-items: center;
      gap: 0.5rem; /* Space between input and button */
    }

    /* The read-only text input */
    .payment-link-cell .payment-link-input {
      flex: 1;                /* Grow to fill available width */
      padding: 0.4rem 0.6rem; /* Spacing inside the input */
      border: 1px solid #ccc; /* Simple border */
      border-radius: 4px;     /* Rounded corners */
      background-color: #f9f9f9;
      cursor: default;        /* Shows this is not a standard editable field */
    }

    /* The copy button */
    .payment-link-cell .copy-url-button {
      background-color: #007bff;
      color: #fff;
      border: none;
      padding: 0.4rem 0.8rem;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s ease-in-out;
    }

    /* Hover state for the button */
    .payment-link-cell .copy-url-button:hover {
      background-color: #0056b3;
    }


  </style>
<div class="inner_container" id="vueapp" v-cloak>

	<div class="breadcrumbs">
		<a href="<?php echo $this->base_url("productorders"); ?>"><i class="fa fa-chevron-left" aria-hidden="true"></i> Product Orders</a>
	</div>

	<div class="top_section">
		<div class="buttons_wrap">

			<div class="dropdown">
				<button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
					Actions <span class="caret"></span>
				</button>
				<ul class="dropdown-menu dropdown-menu-right">
                    <li><a href="#compose_message" v-on:click="compose" data-target="#compose_message" data-toggle="modal">Send Message</a></li>
                    <li><a>
                            <download-excel :fetch="prepare_delivery_list" :name="'delivery_list_'+order.invoice_username+'.xls'" :fields="delivery_export_fields">Download Delivery List</download-excel>
                        </a>
                    </li>
                    <li><a>
                            <download-excel :fetch="prepare_finance_list" :name="'finance_list_'+order.invoice_username+'.xls'" :fields="finance_export_fields">Download Finance List</download-excel>
                        </a>
                    </li>
				</ul>
			</div>
		</div>
		<h1>
			Order <?php if(isset($order['id'])) echo $order['id'].' / '.$order['invoice_username'];?>
		</h1>
	</div>

	<div class="row">
		<div class="col-sm-4">
			<div class="panel panel-default no-padding grey profile_wrap">
				<div class="panel-heading">
					<h3 class="next-heading">Order Summary</h3>
				</div>
				<div class="panel-body">
					<table v-if="order" class="table table-borderless" style="margin-bottom: 0">
						<tbody>
						<tr>
							<th>Invoice Number</th>
							<td>
                                <a :href="'/engine/accounts/invoice/'+order.invoice_username" target="_blank" class="d-block">
                                    {{order.invoice_username}} <span class="fa fa-external-link float-right mt-1"></span>
                                </a>
                            </td>
						</tr>						
						<tr v-if="order.booking_id">
							<th>Short Course Booking</th>
							<td>
                                <a :href="'/admin/shortcourses/booking/'+order.booking_id+'?pg=253&vw='+order.booking_username+'&ref='+order.booking_id+'&invoice='+order.invoice_username" class="d-block">
                                    {{order.booking_id}} 
                                </a>
                            </td>
						</tr>
						<tr>
							<th>Order ID</th><td>{{order.id}}</td>
						</tr>
						<tr>
							<th>Order Total</th><td>{{order.symbol}}{{order.order_total|fixed}}</td>
						</tr>
						<tr>
							<th>Amount Paid</th><td>{{order.symbol}}{{order.amount_paid|fixed}}</td>
						</tr>
						<tr>
							<th>Amount Due</th><td>{{order.symbol}}{{order.order_total-order.amount_paid|fixed}}</td>
						</tr>
						<tr>
							<th>Payment Option selected</th><td>{{order.payment_method}}</td>
						</tr>
						<tr> 
							<th>Payment Status</th><td>{{order.payment_status_name}} <span v-if="order.payment_status_name=='Authorised'" style="font-style: italic;">(Capture Payment when you are happy with the Order)</span></td>
						</tr>
                        <tr>
                            <th>Total Order Items</th><td>{{order.total_items}}</td>
                        </tr>
                        <tr>
                            <th>Total Items Dispatched</th><td>{{items_dispatched}}</td>
                        </tr>
                        <tr>
                            <th>Total Items Returned</th><td>{{items_returned}}</td>
                        </tr>
                        <tr>
                            <th>Items Awaiting Shipment</th><td>{{order.total_items - items_dispatched}}</td>
                        </tr>
                        <tr>
                            <th>Date Order Placed</th>
                            <td>{{order.date|dateFormat}}</td>
                        </tr>
                        <tr>
                            <th>Time Since Order Placed</th>
                            <td>{{order.date|timeLapsed(server_time)}}</td>
                        </tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>

		<div class="col-sm-8">
			<div class="panel panel-default no-padding ">
				<div class="panel-body">
					<h2 class="next-heading">Order Status</h2>
                    <span  v-for="item in order_status" :key="item.order_status_id" data-bs-toggle="tooltip" :title="item.order_status_description"><button  type="button" class="btn profile_status btn-lg" :class="item.order_status_id===order.order_status?'profile_active':''" v-on:click="update_status(item.order_status_id)" data-toggle="modal" data-target="#change_order_status"> 
                        {{item.order_status_name}}
                    </button>
                    </span>
				</div>
			</div>

			<div class="panel panel-default no-padding ">
				<div class="panel-body">
					<h2 class="next-heading">
                        Customer Information
                        <span class="pull-right">
                            <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#edit_invoice_details">
                                <span class="fa fa-edit"></span> Edit
                            </button>
                        </span>
                    </h2>
					<div class="row">
                        <div class="px-4">
                            <table class="table table-borderless customer-info table-condensed">
                                <tr>
                                    <td>Purchase Order Number</td><td>{{order.details.purchase_order_number?order.details.purchase_order_number:'n/a'}}</td>
                                </tr>
                                <tr>
                                    <td>Organisation Type</td>
                                    <td>
                                        {{order.details.organisation_type==='Other (please specify)'?order.details.organisation_type_other:order.details.organisation_type}}
                                    </td>
                                </tr>
                                <tr>
                                    <td>Organisation</td>
                                    <td>{{order.details.organisation}}</td>
                                </tr>
                                <tr>
                                    <td>Customer Name</td>
                                    <td>{{order.first_name}} {{order.surname}}</td>
                                </tr>
                                 <tr>
                                    <td>External reference number</td>
                                    <td>{{order.details.ext_ref_nubmer}}</td>
                                </tr>
                            </table>
                        </div>
						<div class="col-sm-6 px-2">
							<table class="table table-condensed table-borderless" style="margin-bottom: 0;">
                                <thead><tr><th colspan="2"><h2 class="next-heading" style="margin-bottom: 0;">Delivery Details</h2></th></tr></thead>
                                <tbody>
                                <tr>
                                    <td>Contact Person</td>
                                    <td>{{order.details.delivery_contact_name}}</td>
                                </tr>
                                <tr>
                                    <td>Contact Email</td>
                                    <td>{{order.details.delivery_contact_email}}</td>
                                </tr>
                                <tr>
                                    <td>Contact Number</td>
                                    <td>{{order.details.delivery_contact_number}}</td>
                                </tr>
                                <tr>
                                    <td>Address</td>
                                    <td>
                                        {{order.details.delivery_address}}<br>
                                        {{order.details.delivery_city}}{{ order.details.delivery_postcode || order.details.delivery_county ? ',':'' }}
                                        {{ order.details.delivery_county}} {{order.details.delivery_postcode }}
                                    </td>
                                </tr>
                                <tr>
                                    <td>Country</td>
                                    <td>{{order.details.delivery_country_name}}</td>
                                </tr>
                                </tbody>
							</table>
						</div>
						<div class="col-sm-6">
							<table class="table table-condensed table-borderless" style="margin-bottom: 0;">
								<thead><tr><th colspan="2"><h2 class="next-heading" style="margin-bottom: 0;">Billing Details</h2></th></tr></thead>
                                <tbody v-if="order.details.billing_address">
                                <tr>
                                    <td>Contact Person</td>
                                    <td>{{order.details.billing_contact_name}}</td>
                                </tr>
                                <tr>
                                    <td>Contact Email</td>
                                    <td>{{order.details.billing_contact_email}}</td>
                                </tr>
                                <tr>
                                    <td>Contact Number</td>
                                    <td>{{order.details.billing_contact_number}}</td>
                                </tr>
                                <tr>
                                    <td>Address</td>
                                    <td>
                                        {{order.details.billing_address}}<br>
                                        {{order.details.billing_city}}{{ order.details.billing_postcode || order.details.billing_county ? ',':'' }}
                                        {{ order.details.billing_county}} {{order.details.billing_postcode }}
                                    </td>
                                </tr>
                                <tr>
                                    <td>Country</td>
                                    <td>{{order.details.billing_country_name}}</td>
                                </tr>
                                </tbody>
                                <tbody v-else="">
                                <tr>
                                    <td colspan="2">
                                        <p>n/a</p>
                                    </td>
                                </tr>
                                </tbody>
							</table>
						</div>
                        
                        <div class="col-sm-12" style="margin-top:15px;">
                         <span class="pull-right"><button type="button" data-toggle="modal" data-target="#basketModal" class="btn btn-sm btn-primary"  v-on:click="get_basket_data(0)"><span class="fa fa-plus"></span> Add Basket
                            </button></span>
                       <h2 class="next-heading"> Baskets</h2>
                        <div v-for="(order_basket,f) in order_baskets" :key="f" class="col-sm-4 basket-summary">
                            
                            <span class="pull-right">
                                <button type="button" data-toggle="modal" data-target="#deletebasketModal" class="btn btn-sm btn-primary" @click="prepare_delete_basket(order_basket.cart_basket_id)">
                                    <i class="fa fa-trash"></i> Delete
                                </button>

                                  
                            </span>

                            <span class="pull-right">
                                <button type="button" data-toggle="modal" data-target="#basketModal" class="btn btn-sm btn-primary" @click="get_basket_data(order_basket.cart_basket_id)">
                                    <span class="fa fa-edit"></span> Edit
                                </button>

                                
                            </span>
                            <address>
                                <strong>Basket Name: {{order_basket.basket_name}}.</strong>
                                <br>
                                <a href="#" @click.prevent="toggleBasketDetails(f)">
                                    {{ showBasketDetails[f] ? 'Hide details' : 'View details' }}
                                </a>
                            </address>
                            <div v-if="(showBasketDetails[f])">
                                <address>
                                Organization Name : {{order_basket.organization_name}}<br>
                                Contact Name : {{order_basket.contact_name}}<br>
                                Contact Email : {{order_basket.contact_email}}<br>
                                {{order_basket.house_number}}<br>
                                {{order_basket.city}}<br>
                                {{order_basket.region}},{{order_basket.postcode}}<br>
                                <abbr title="Phone">Phone:</abbr> {{order_basket.contact_number}}
                            </address>
                        </div>
                        </div>
                        </div>
					</div>
				</div>
			</div>

		</div>
	</div>

	<div style="margin-top: 15px;background: #fff;">
		<!-- Nav tabs -->
		<ul class="nav nav-tabs" role="tablist">
			<li role="presentation" class="active"><a href="#ordered_items" aria-controls="home" role="tab" data-toggle="tab">Ordered Items</a></li>
			<li role="presentation"><a href="#payments" aria-controls="profile" role="tab" data-toggle="tab">Payments</a></li>
			<li role="presentation"><a href="#history" aria-controls="history" role="tab" data-toggle="tab">Order History</a></li>
			<li role="presentation"><a href="#fulfilment_history" aria-controls="settings" role="tab" data-toggle="tab">Order Fulfilment History</a></li>
            <li v-if="order.pre_requisites.length>0" role="presentation"><a href="#order_prerequisites" aria-controls="order_prerequisites" role="tab" data-toggle="tab">Order Prerequisites</a></li>
            <li role="presentation"><a href="#invoices" aria-controls="invoices" role="tab" data-toggle="tab">Invoices</a></li>
		</ul>

		<!-- Tab panes -->
		<div class="tab-content">
			<div role="tabpanel" class="tab-pane active" id="ordered_items">

				<div id="tabs">
				  <ul style="margin-top: 55px;">
				    <li><a class="horiz" href="#tabs-1">View by Baskets</a></li>
                    <li><a class="horiz" href="#tabs-2">View by products</a></li>
				  </ul>
				  <div id="tabs-1">
                    <?php include("baskets_list_view.php"); ?>
				    
				  </div>
				  <div id="tabs-2">
				    <?php include("products_list_view.php"); ?>
				  </div>
				</div>
			</div>
			<div role="tabpanel" class="tab-pane" id="payments">
                <div style="margin-bottom: 15px;">
                    <button type="button" class="btn btn-default" style="opacity: 0;width: 1px;padding: 0;">&nbsp;</button>
                    <span class="pull-right">
						<button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#add_payment">
							<span class="fa fa-money"></span> Add Payment / Refund
						</button>
					</span>
                </div>
                <div class="table-responsive">
                    <table class="table table-condensed">
                        <thead>
                        <tr>
                            <th>#</th>
                            <th>User</th>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Payment/Refund</th>
                            <th>Payment Method</th>
                            <th>Payment Ref. / Token</th>
                            <th>Comment/Status</th>
                            <th>Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="(p,i) in payments" :key="p.id">
                            <td>{{i+1}}</td>
                            <td>{{p.first_name || 'n/a'}} {{p.surname || ''}}</td>
                            <td>{{p.date|dateFormat}}</td>
                            <td>{{p.symbol}}{{p.amount|fixed}}</td>
                            <td>{{p.payment_or_refund}}</td>
                            <td>{{p.payment_method}}</td>
                            <td>{{p.token}}</td>
                            <td><div v-html="p.comment"></div></td>
                            <td><button type="button" class="btn btn-sm btn-primary" v-on:click="edit_payment(p);" >
                            <span class="fa fa-pencil"></span></button></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
			<div role="tabpanel" class="tab-pane" id="history">
                <form class="form form-inline mb-5">
                    <label class="pr-3">Filter by activity</label>
                    <select class="form-control" v-model="history_filter">
                        <option value="">Show All</option>
                        <option value="Status Update">Status Updates</option>
                        <option value="New Message">Messages</option>
                        <option value="Products Update">Product Updates</option>
                        <option value="Invoice Details Update">Invoice Detail Updates</option>
                        <option value="payments">Payments / Refunds</option>
                    </select>
                </form>
                <table class="table">
                    <thead>
                    <tr>
                        <th>#</th>
                        <th>Date</th>
                        <th>User</th>
                        <th>Activity</th>
                        <th>Emails Sent</th>
                        <th>SMS Sent</th>
                        <th>Message/Comment</th>
                        <th>Order Status</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="(line,i) in filtered_history" :key="line.id">
                        <td>{{i+1}}</td>
                        <td>{{line.date|dateFormat}}</td>
                        <td>{{line.first_name}} {{line.surname}}</td>
                        <td>{{line.subject}}</td>
                        <td>
                            <p v-for="msg in line.email_log" class="mb-1">
                                <a href="#email_content" data-toggle="modal" data-target="#email_content" v-on:click="get_email_content(msg.id,msg.email)"
                                   title="click to view email content">
                                    {{msg.email}} <span class="fa fa-external-link"></span>
                                </a>
                                <i>({{msg.delivered==='yes'?'Delivered':(msg.processed==='yes'?'Sent':'Not sent')}})</i>
                            </p>
                            <span v-show="!line.email_log || line.email_log.length===0">none</span>
                        </td>
                        <td>
                            <p v-for="msg in line.sms_log" class="mb-1">
                                <a href="#history_modal" data-toggle="modal" data-target="#history_modal" v-on:click="history_msg=msg.message"
                                   title="click to view message content">{{msg.phone}} <span class="fa fa-external-link"></span></a>
                                <i :title="msg.status_message">({{msg.status}})</i>
                            </p>
                            <span v-show="!line.sms_log || line.sms_log.length===0">none</span>
                        </td>
                        <td>
                            <div v-if="line.comment&&line.comment.length < 200" v-html="line.comment"></div>
                            <button v-else type="button" class="btn btn-link btn-xs" data-toggle="modal" title="View Message / Comment" data-target="#history_modal" v-on:click="history_msg=line.comment">View Message <span class="fa fa-external-link"></span></button>
                        </td>
                        <td>{{line.order_status_name}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
			<div role="tabpanel" class="tab-pane" id="fulfilment_history">


                <table class="table">
                    <thead>
                    <tr>
                        <th>#</th>
                        <th>User</th>
                        <th>Product</th>
                        <th>Date</th>
                        <th>Action</th>
                        <th>Delivery Method</th>
                        <th>Quantity</th>
                        <th>Reference</th>
                        <th>Notes</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="(item,i) in fulfilment_history" :key="item.id">
                        <td>{{i+1}}</td>
                        <td>{{item.first_name}} {{item.surname}}</td>
                        <td>{{item.product_name}}</td>
                        <td>{{item.date_actioned|dateFormat}}</td>
                        <td>{{item.dispatch_or_return}}</td>
                        <td>{{item.shipping_method_name}}</td>
                        <td>{{item.quantity}}</td>
                        <td>{{item.reference}}</td>
                        <td><div v-html="item.notes"></div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div v-if="order.pre_requisites.length>0" role="tabpanel" class="tab-pane" id="order_prerequisites">
               <div style="display: inline-block;" class="pull-right">
                <a v-if="order.order_status_name=='Awaiting Prerequisite Approval'" style="color: #fff;" class="btn btn-primary btn-sm" v-on:click="update_prereq_status(<?php echo $order['id'] ?>,'Approved')"><i class="fa fa-check "></i>Approve All</a>
                </div>
               <div class="table-responsive py-3">
               
                <table v-if="order.pre_requisites.length>0" class="table align-middle">
                    <thead>
                    <tr>
                        <th>Product</th>
                        <th>Required Course</th>
                        <th>Training Date</th>
                        <th>Training Location</th>
                        <th>Uploaded Proof</th>
                        <th v-if="order.order_status_name=='Awaiting Prerequisite Approval'||order.order_status_name=='Need more information'"> Action</th>
                    </tr>
                    </thead> 
                    <tbody>
                        <tr v-for="(item,i) in order.pre_requisites" :key="i">
                            <td>{{item.product_title}}</td>
                            <td>{{item.course_name}}</td>
                            <td>{{item.short_course_date}}</td>
                            <td>{{item.short_course_location}}</td>
                            <td>
                                <span style="margin:2px;" v-if="item.attachments.length>0" v-for="(attachment,j) in item.attachments" :key="j" ><a target="_blank"  :href="attachment.uri"><i class="fa fa-download"></i></a></span>
                            </td>
                            <td v-if="order.order_status_name=='Awaiting Prerequisite Approval'||order.order_status_name=='Need more information'">
                                <a style="color: blue;" v-if="(item.status==''||item.status==null)"  v-on:click="pre_req_fill(item)" >View</a>
                            <a style="color: blue;" v-if="item.status=='Approved'"  v-on:click="pre_req_fill(item)" >Approved</a>
                            <a style="color: blue;" v-if="item.status=='Rejected'"   v-on:click="pre_req_fill(item)"  >Rejected</a>
                          </td>
                        </tr>
                    </tbody>
                </table>
               </div>
                
            </div>

            <div role="tabpanel" class="tab-pane" id="invoices">
                <div style="margin-bottom: 15px;">
                    <button type="button" class="btn btn-default" style="opacity: 0;width: 1px;padding: 0;">&nbsp;</button>
                    <span class="pull-right">
                        <a :href="'<?php echo $this->base_url("invoices/new/"); ?>'+'?once-off-payment=yes&order_id='+order.id" type="button" class="btn btn-sm btn-primary" target="_blank">
                            <span class="fa fa-plus"></span> Add One-off payment
                        </a>
                    </span>
                </div>


                <table class="table">
                    <thead>
                      <tr>
                        <th>#</th>
                        <th>Invoice Number</th>
                        <th>Issue Date</th>
                        <th>Invoice Total</th>
                        <th>Payments</th>
                        <th>Balance</th>
                        <th>Payment Link</th>
                      </tr>
                    </thead>
                    <tbody>
                      <!-- Loop through all_invoices -->
                      <tr v-for="(item, i) in all_invoices" :key="item.id">
                        <!-- 1) Actions (Edit & View) -->
                        <td>
                          <!-- Edit link -->
                          <a
                            :href="`/admin/invoices/${item.unique_id}?force_edit=yes&order_id=${order.id}`"
                            target="_blank"
                            class="btn btn-default btn-xs"
                            title="Edit Record"
                          >
                            <span class="glyphicon glyphicon-pencil" aria-hidden="true"></span>
                          </a>

                          <!-- View link -->
                          <a
                            :href="`/admin/invoices/${item.unique_id}?order_id=${order.id}`"
                            target="_blank"
                            class="btn btn-default btn-xs"
                            title="View Record"
                          >
                            <span class="glyphicon glyphicon-search" aria-hidden="true"></span>
                          </a>
                        </td>

                        <!-- 2) Invoice info fields -->
                        <td>{{ item.unique_id }}</td>
                        <td>{{ item.issue_date }}</td>
                        <td>{{ item.display_total }}</td>
                        <td>{{ item.total_payments }}</td>
                        <td>{{ item.amount_outstanding }}</td>

                        <!-- 3) Payment link and copy button -->
                        <td>
                          <div class="payment-link-wrapper">
                            <!-- Input containing the link -->
                            <input
                              type="text"
                              class="payment-link-input"
                              :value="'<?php echo $product_site_url; ?>' + '/products/invoices?trigger-payment=' + item.unique_id"
                              readonly
                            />
                            <!-- Button to copy URL -->
                            <button class="copy-url-button" @click="copyUrl(i)">
                              Copy URL
                            </button>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
            </div>
		</div>

	</div>

    <!-- Add Payment Modal -->
    <div class="modal fade" id="add_payment" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">Add Payment or Refund</h4>
                </div>
                <div class="modal-body">
                    <form class="form">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="control-label">Payment / Refund? <span class="required">*</span></label>
                                    <select class="form-control" v-model="payment.payment_or_refund">
                                        <option>Payment</option>
                                        <option>Refund</option>
                                        <option v-if="'invoice'==order.payment_method" value="invoice_update">Invoice update</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="control-label">Method of payment / refund <span class="required">*</span></label>
                                    <select class="form-control" v-model="payment.payment_method">
                                        <option v-if="'invoice'==order.payment_method" value="3rd_party_invoice">3rd Party Invoice</option>
                                        <option v-if="'invoice'==order.payment_method" value="3rd_party_credit_note">3rd Party credit note</option>
                                        <option value="cash">Cash</option>
                                        <option value="bank">Bank</option>
                                        <option value="credit card">Credit Card</option>
                                        <option value="stripe">Stripe</option>
                                        <option value="paypal">PayPal</option>
                                        <option value="Budget Code Transfer">Budget Code Transfer</option>
                                        <option value="Cheque">Cheque</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="control-label">Amount</label>
                                    <div class="input-group">
                                        <span class="input-group-addon">{{order.symbol}}</span>
                                        <input type="text" class="form-control" v-model="payment.amount">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="control-label">Payment / Refund Date</label>
                                    <flat-pickr v-model="payment.date" class="form-control" placeholder="Select date" :config="date_format"></flat-pickr>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label">Payment Ref. / Token</label>
                            <input type="text" class="form-control" v-model="payment.token">
                        </div>
                        <div class="form-group">
                            <label class="control-label">Comment <small>(optional)</small></label>
                            <!--<textarea class="form-control" v-model="status_update.message" rows="5"></textarea>-->
                            <vue-editor v-model="payment.comment" />
                        </div>
                        <div class="form-group">
                            <label class="control-label">Email To</label>
                            <v-select taggable="true" push-tags="true" multiple="true" :options="contact_emails" v-model="payment.email"></v-select>
                        </div>
                        <div class="form-group">
                            <label class="control-label">SMS To</label>
                            <v-select taggable="true" push-tags="true" multiple="true" :options="contact_phones" v-model="payment.phone"></v-select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary has-loader" v-on:click="add_payment"><span class="loader loader-light loader-sm"></span> Submit</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Invoice Details Modal -->
    <div class="modal fade" id="edit_invoice_details" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">Edit Customer Details</h4>
                </div>
                <div class="modal-body">
                    <invoice-details :inv_acc="order.details" :settings="settings" :countries="countries" :cust_refs="cust_refs"></invoice-details> 
                    <billing-details v-if="order.payment_method==='invoice'" :inv_acc="order.details" :countries="countries" class="form row py-3"></billing-details>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary has-loader" v-on:click="save_customer_changes"><span class="loader loader-light loader-sm"></span> Submit</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Change Order Status Modal -->
    <div class="modal fade" id="change_order_status" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">Change Order Status</h4>
                </div>
                <form class="modal-body">
                    <fieldset>
                        <div class="form-group">
                            <label class="control-label">Change Order Status To:</label>
                            <select class="form-control" v-model="status_update.status">
                                <option v-for="s in order_status" :value="s.order_status_id">{{s.order_status_name}}</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="control-label">Template</label>
                            <v-select :options="email_templates" v-model="status_update.template" label="name" @input="set_status_template"></v-select>
                        </div>
                        <div class="form-group">
                            <label class="control-label">Comment <small>(optional)</small></label>
                            <!--<textarea class="form-control" v-model="status_update.message" rows="5"></textarea>-->
                            <vue-editor v-model="status_update.message" />
                        </div>
                        <div class="form-group">
                            <label class="control-label">Email To</label>
                            <v-select taggable="true" push-tags="true" multiple="true" :options="contact_emails" v-model="status_update.email"></v-select>
                        </div>
                        <div class="form-group">
                            <label class="control-label">SMS To</label>
                            <v-select taggable="true" push-tags="true" multiple="true" :options="contact_phones" v-model="status_update.phone"></v-select>
                        </div>
                    </fieldset>
                </form>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary has-loader" v-on:click="update_order_status"><span class="loader loader-light loader-sm"></span> Submit without Notification</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary has-loader" v-on:click="update_order_status"><span class="loader loader-light loader-sm"></span> Submit</button>
                </div>
            </div>
        </div>
    </div>



    <!-- Change Order Status Modal -->
    <div class="modal fade" id="change_booking_status" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">Update Booking Status</h4>
                </div>
                <form class="modal-body">
                    <fieldset>
                        <div class="form-group">
                            <label class="control-label">Change Booking Status To:</label>
                            <select class="form-control" v-model="status_update.booking_status">
                                <option v-for="s in booking_status_options" :value="s.id">{{s.title}}</option>
                            </select>
                        </div>
                    </fieldset>
                </form>
                <div class="modal-footer">
                   
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary has-loader" v-on:click="update_booking_status"><span class="loader loader-light loader-sm"></span> Submit</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Send Message Modal -->
    <div class="modal fade" id="compose_message" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">Send Message</h4>
                </div>
                <form class="modal-body">
                    <form-wizard title="" subtitle="" step-size="xs" color="#428bca">
                        <tab-content title="Compose Message" :before-change="preview_message">
                            <fieldset>
                                <div class="form-group">
                                    <label class="control-label">Email To</label>
                                    <v-select taggable="true" push-tags="true" multiple="true" :options="contact_emails" v-model="compose_message.email"></v-select>
                                </div>
                                <div class="form-group">
                                    <label class="control-label">SMS To</label>
                                    <v-select taggable="true" push-tags="true" multiple="true" :options="contact_phones" v-model="compose_message.phone"></v-select>
                                </div>
                                <div class="form-group">
                                    <label class="control-label">Template</label>
                                    <v-select :options="email_templates" v-model="compose_message.template" label="name" @input="set_template"></v-select>
                                </div>
                                <div v-show="0>1" class="form-group">
                                    <label class="control-label">Subject</label>
                                    <input type="text" class="form-control" v-model="compose_message.subject">
                                </div>
                                <div class="form-group">
                                    <label class="control-label">Message <small class="text-muted"> no salutations, just type your message</small> <span class="text-danger">*</span> </label>
                                    <!--<textarea class="form-control cke_editable" v-model="compose_message.message"></textarea>-->
                                    <div><vue-editor v-model="compose_message.message" /></div>
                                    <p v-if="compose_message.phone.length>0" class="help-block">
                                        {{counter.remaining}} / {{counter.messages}}
                                    </p>
                                </div>
                            </fieldset>
                        </tab-content>
                        <tab-content title="Preview Message">
                            <div v-for="(p,i) in compose_message.previews" class="panel panel-default email-preview">
                                <div class="panel-heading"><span class="fa fa-envelope"></span> {{compose_message.email[i]}}</div>
                                <div class="panel-body" v-html="p"></div>
                            </div>
                            <div v-for="(p,i) in compose_message.sms" class="panel panel-default email-preview">
                                <div class="panel-heading"><span class="fa fa-comment"></span> {{compose_message.phone[i]}}</div>
                                <div class="panel-body" v-html="p"></div>
                                <div class="panel-footer">
                                    <span style="width: 49%; display: inline-block;">
                                        <strong>{{p.length}}</strong> characters
                                    </span>
                                    <span style="width: 50%; display: inline-block;" class="text-right">
                                        <strong>{{sms_count(p)}}</strong> SMS messages will be sent
                                    </span>
                                </div>
                            </div>
                        </tab-content>
                        <tab-content title="Send">
                            <div class="p-5">
                                <div v-if="message_status==='info'" class="text-center">
                                    <img src="/admin/assets/images/loading-dots.gif">
                                    <h3>Sending message, please wait...</h3>
                                </div>
                                <div v-else-if="message_response.length>0" :class="'alert alert-'+message_status">
                                    <ol>
                                        <li v-for="msg in message_response">{{msg.message}}</li>
                                    </ol>
                                </div>
                            </div>
                        </tab-content>
                        <template slot="footer" slot-scope="props">
                            <div class="wizard-footer-left">
                                <button type="button" class="btn btn-default" data-dismiss="modal"> Close </button>
                                <button type="button" v-if="props.activeTabIndex > 0 && !props.isLastStep" v-on:click="props.prevTab()" class="btn btn-primary ml-5">Back</button>
                            </div>
                            <div class="wizard-footer-right">
                                <wizard-button v-if="props.activeTabIndex == 0" @click.native="props.nextTab()" class="wizard-footer-right btn btn-primary" :style="props.fillButtonStyle">Preview Message</wizard-button>
                                <wizard-button v-if="props.activeTabIndex == 1" @click.native="send_message(props)" class="wizard-footer-right btn btn-primary" :style="props.fillButtonStyle">Send Message</wizard-button>
                                <wizard-button v-else-if="props.activeTabIndex == 2" @click.native="resetMessage(props)" class="wizard-footer-right btn btn-primary" :style="props.fillButtonStyle">Done</wizard-button>
                            </div>
                        </template>
                    </form-wizard>
                </form>
            </div>
        </div>
    </div>

    <!-- Order Item Fulfilment Modal -->
    <div class="modal fade" id="fulfilment_modal" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">Update Order Fulfilment Status</h4>
                </div>
                <div class="modal-body">
                    <form class="row" autocomplete="off">
                        <div class="form-group col-sm-6">
                            <label class="control-label">Fulfilment Status</label>
                            <select class="form-control" v-model="fulfilment.status">
                                <option v-for="fs in fulfilment_status" :disabled="(fs.id>=fulfil_from && fs.id<=fulfil_to)?false:'disabled'" :value="fs.id">
                                    {{fs.fulfilment_status_name}}
                                </option>
                            </select>
                        </div>
                        <div class="form-group col-sm-6">
                            <label class="control-label">Dispatch or Return?</label>
                            <select class="form-control" v-model="fulfilment.dispatch_or_return" v-on:change="dispatch_or_return">
                                <option>Dispatch</option><option>Return</option>
                            </select>
                        </div>
                        <div class="form-group col-sm-6">
                            <label class="control-label">Date dispatched / returned</label>
                            <flat-pickr :config="date_format" class="form-control" placeholder="Select date" v-model="fulfilment.date" ></flat-pickr>
                        </div>
                        <div class="form-group col-sm-6">
                            <label class="control-label">Delivery Method</label>
                            <v-select v-model="fulfilment.method" :options="shipping_methods" :reduce="shipping_method_name => shipping_method_name.id" label="shipping_method_name" taggable="true" push-tags="true"></v-select>
                        </div>
                        <div class="form-group col-sm-6">
                            <label class="control-label">Notes</label>
                            <textarea class="form-control" v-model="fulfilment.notes"></textarea>
                        </div>
                        <div class="form-group col-sm-6">
                            <label class="control-label">Shipping Reference</label>
                            <input type="text" class="form-control" v-model="fulfilment.reference">
                        </div>
                        <div class="clearfix"></div>
                        <div class="pl-3 pr-3 mt-5">
                            <legend>Line Items</legend>
                        </div>
                        <div class="table-responsive mt-5">
                            <table class="table table-condensed">
                                <thead>
                                <tr>
                                    <th>#</th><th>Product</th>
                                    <th title="Quantity shipped or returned nowrap">Quantity&nbsp;{{fulfilment.dispatch_or_return}}ed</th>
                                    <th>Reference</th><th>notes</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="(item,i) in fulfilment.items">
                                    <td>{{i+1}}</td>
                                    <td>{{item.product_name}}</td>
                                    <td>
                                        <div v-if="fulfilment.dispatch_or_return==='Dispatch'" class="form-group my-0" :class="item.quantity>item.to_ship?'has-error':'has-info'">
                                            <input type="number" class="form-control" v-model="item.quantity" min="0" :max="item.to_ship"
                                                   :title="'Hint: '+item.to_ship+' items need shipping.'">
                                            <p v-show="item.quantity>item.to_ship" class="help-block">Quantity to ship cannot be greater than {{item.to_ship}}</p>
                                        </div>
                                        <div v-else class="form-group my-0" :class="item.quantity>item.shipped?'has-error':'has-info'">
                                            <input type="number" class="form-control" v-model="item.quantity" min="0" :max="item.shipped"
                                                   :title="'Hint: '+item.shipped+' items were shipped.'">
                                            <p v-show="item.quantity>item.shipped" class="help-block">Quantity returned cannot be greater than {{item.shipped}}</p>
                                        </div>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control" v-model="item.reference" :placeholder="fulfilment.reference">
                                    </td>
                                    <td>
                                        <textarea class="form-control" v-model="item.notes" :placeholder="fulfilment.notes" rows="1"></textarea>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button v-if="fulfilment_error" type="button" class="btn btn-primary" disabled>Save Changes</button>
                    <button v-else type="button" class="btn btn-primary has-loader" v-on:click="fulfil_items"><span class="loader loader-light loader-sm"></span> Save changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Order History Modal -->
    <div class="modal fade" id="history_modal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">Message / Comment</h4>
                </div>
                <div class="modal-body">
                    <div v-html="history_msg"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Content Modal -->
    <div class="modal fade" id="email_content" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">{{email_to}}</h4>
                </div>
                <div class="modal-body">
                    <div v-html="email_content"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal" v-on:click="email_content=''">Close</button>
                </div>
            </div>
        </div>
    </div>

    <div v-show="show_reminder" class="row reminder mx-0 px-0 py-5">
        <div class="px-5"><button class="close" type="button" style="font-size: 4rem;" v-on:click="toggle_reminder(false)">&times</button></div>
        <div class="container px-3 text-muted">
            <p style="font-size: 2rem;"><i>Would you like to to <b>update the order status</b> or <b>notify the customer</b> about the changes you made?</i></p>
            <p>
                <button type="button" data-toggle="modal" data-target="#change_order_status" class="btn btn-primary"
                        v-on:click="update_status(order.order_status)">Update Order Status</button>
                <button type="button" class="btn btn-primary ml-5" v-on:click="compose"
                        data-target="#compose_message" data-toggle="modal">Send Message</button>
            </p>
        </div>
    </div>

    <div class="modal fade" id="basketModal" tabindex="-1" aria-hidden="true" >
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title"><span id="basketeditor_text">Add New Basket</span></h5>
          <button id="basketModalClose" type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">

          <form class="row form py-3" v-on:submit="add_basket()">
<!--            <div v-if="messages.length>0" class="alert mb-3" :class="flag" role="alert">-->
<!--              <p class="m-0" v-for="message in messages" v-html="message"></p>-->
<!--            </div>-->
            <div class="col-sm-12">
              <label id="basket name" for="basket name" class="form-label">Basket Name</label>
              <input type="text" id="basket_name"   class="form-control" aria-describedby="basket" v-model="basket_edit.basket_name" >
            </div>
         
            <div class="col-sm-12 mb-2">
              <h5 class="my-3">Basket delivery Details</h5>
            </div>

            <div class="col-sm-6 mb-3">
              <label title="required">Flat/House Number/Name/Road <span class="text-danger">*</span></label>
              <input type="text" id="house_number"  class="form-control" v-model="basket_edit.house_number">
            </div>

            <div class="col-sm-6 mb-3 mt-4">
              <label title="required">City / Town <span class="text-danger">*</span></label>
              <input type="text" id="city" class="form-control" v-model="basket_edit.city" >
            </div>
            <div class="col-sm-6 mb-3">
              <label>County</label>
              <input type="text" id="region" class="form-control" v-model="basket_edit.region" >
            </div>
            <div class="col-sm-6 mb-3">
              <label>Country <span class="text-danger">*</span></label>
              <v-select :options="countries" :reduce="country => country.id" label="name"  id="country" v-model="basket_edit.country"></v-select>
            </div>
            <div class="col-sm-6 mb-3">
              <label>Postcode <span class="text-danger">*</span></label>
              <input type="text" class="form-control"  id="postcode" v-model="basket_edit.postcode">
            </div>
            <div class="col-sm-6 mb-3">
              <label>Contact Name <span class="text-danger">*</span></label>
              <input type="text"  class="form-control" id="contact_name" v-model="basket_edit.contact_name">
            </div>
            <div class="col-sm-6 mb-3">
              <label>Contact Email <span class="text-danger">*</span></label>
              <input type="text"  class="form-control" id="contact_email" v-model="basket_edit.contact_email">
            </div>
            <div class="col-sm-6 mb-3">
              <label>Organization Name <span class="text-danger">*</span></label>
              <input type="text"  class="form-control" id="organization_name" v-model="basket_edit.organization_name">
            </div>
            <div class="col-sm-6 mb-3">
              <label>
                Contact Number
                <span class="text-primary" data-bs-toggle="tooltip" title="We recommend you enter a mobile number that can receive SMS to get instant updates about your order."><span class="fas fa-info-circle"></span></span>
                <span class="text-danger">*</span>
              </label>
              <input type="phone"  class="form-control" id="contact_number" v-model="basket_edit.contact_number">
            </div>
            <input id="ad_id" hidden v-model="basket_edit.ad_id">
            <input id="cart_basket_id" hidden v-model="basket_edit.cart_basket_id">
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary" v-on:click="add_basket()">Save Basket</button>
          <button type="button" id="closebasketmodal"  class="btn btn-secondary" data-dismiss="modal">Cancel</button>
        </div>
      </div>
    </div>
  </div>



  <div class="modal fade" id="deletebasketModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deletebasketModalLabel">Warning!</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p> Are you sure you want to move remove this basket and its contents?</p>
        <p class="alert alert-danger" id="delete_resp_message" hidden></p>
      </div>
      <div class="modal-footer">
        <input type="" name="" id="delete_cart_basket_id" hidden>
        <input type="" name="" id="order_id" hidden :value="order.id">
        <input type="" name="" id="refresh_when_done" value='yes' hidden>
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" v-on:click="delete_basket()"  class="btn btn-danger">yes</button>
      </div>
    </div>
  </div>
</div>


<!-- Modal -->
<div class="modal right fade" id="PrerequisitesModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel2">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="text-align: left;">

            <div class="modal-header" >
                <button style="font-size:21px;" type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel2">Uploaded Prerequisites</h4>
            </div>

            <div class="modal-body">
                <p>Order line item  : <span id="prereq_order"></span></p>
                <p>Prerequisite Training: <span id="prereq_training"></span></p>
                <p>Training date    : <span id="training_date"></span></p>
                <p>Training Location: <span id="training_location"></span></p>
                <p id="proof_tr">Proof of training: <span id="proof_of_training"></span></p>
                <div v-if="rejection_state" class="form-group" id="reject_reason">
                    <label for="rejectionreason">Enter Rejection Reason</label>
                    <textarea class="form-control" id="rejectionreason" rows="3"></textarea>
                </div>
                <p id="g_check">
                  <span id="approve_span">
                   <div id="ck-approve">
                    <label>
                      <input type="checkbox" v-on:click="update_prereq_status(order.id,'Approved')"/><span>Approve</span>
                    </label>
                   </div>
                  </span> 
                    <span id="reject_span">
                        <div id="ck-reject">
                            <label>
                               <input type="checkbox" v-if="!rejection_state" v-on:click="enter_rejection_state()"/><span v-if="!rejection_state">Reject</span>
                               <input type="checkbox" v-if="rejection_state" v-on:click="update_prereq_status(order.id,'Rejected')"/><span v-if="rejection_state">Reject Prerequisite</span>
                             </label>
                            </div>
                    </span >
                     </br>
                   <input type="" name="" id="prereq_item_id" hidden>
                </p>
            </div>

        </div><!-- modal-content -->
    </div><!-- modal-dialog -->
</div><!-- modal -->
</div>





<script type="application/javascript" src="/admin/assets/js/productorders/methods.js?v=<?php echo uniqid(); ?>"></script> 
<!-- <script type="application/javascript" src="/admin/assets/js/productorders/ProductOrder.js"></script> -->
<script type="application/javascript" src="/admin/assets/js/productorders/ProductBasket.js"></script>
<script type="application/javascript" src="/admin/assets/js/productorders/InvoiceDetails.js"></script>
<script type="application/javascript" src="/admin/assets/js/productorders/BillingDetails.js"></script>

<script type="application/javascript">
	 $( function() {
	    $( "#tabs" ).tabs().addClass( "ui-tabs-vertical ui-helper-clearfix" );
	    $( "#tabs li" ).removeClass( "ui-corner-top" ).addClass( "ui-corner-left" );
	  } );
    Vue.use(Vue2Editor);
    Vue.component("downloadExcel", JsonExcel);
    Vue.use(VueFormWizard);
	var vueapp = new Vue({
		el: '#vueapp',
		data: {
		    order: <?php echo json_encode($data['order']);?>,
			order_status: <?php echo json_encode($data['order_status']);?>,
			payment_status: <?php echo json_encode($data['payment_status']);?>,
            fulfilment_status: <?php echo json_encode($data['fulfilment_status']);?>,
            payments: <?php echo json_encode($data['payments']);?>,
            settings: <?php echo json_encode($data['settings']);?>,
            countries: <?php echo json_encode($data['countries']);?>,
            contacts: <?php echo json_encode($data['contacts']);?>,
            email_templates: <?php echo json_encode($data['email_templates']);?>,
            admin_name: <?php echo json_encode($_SESSION['fullname']);?>,
            fulfilment_history: <?php echo json_encode($data['fulfilment_history']);?>,
            all_invoices: <?php echo json_encode($data['all_invoices']);?>,
            order_history: <?php echo json_encode($data['order_history']);?>,
            message_header: <?php echo json_encode($data['order_message_lh']);?>,
            server_time: <?php echo json_encode($data['server_time']);?>,
            delivery_export_fields: <?php echo json_encode($data['delivery_export_fields']);?>,
            finance_export_fields: <?php echo json_encode($data['finance_export_fields']);?>,
            order_baskets: <?php echo json_encode($data['order_baskets']); ?>,
            order_prequisites: <?php echo json_encode($data['order_prequisites']); ?>,
            cust_refs: <?php echo json_encode($data['cust_refs']) ?>,
            booking_status_options: <?php echo json_encode($data['booking_status_options']) ?>,
            edit_products: false,
            edit_customer: false,
            og_products: false,
            past_edit_status: 20,
            past_edit: 'Order cannot be edited at this point. Please contact admin for assistance',
            past_edit_item: 14,
            paid: 6,
            item_cancelled: 26,
            fulfil_from: 11,
            fulfil_to: 14,
            payment: { token: '', payment_method:'', amount: <?php echo ($data['amount_due']); ?>, payment_or_refund: '', comment: '', date:'', email:[], phone:[] },
            payment_copy: { token: '', payment_method:'', amount: '', payment_or_refund: '', comment: '', date:'' },
            select_all: false,
            status_update: { status:'', message: '', email: [], phone: [], template: false },
            status_update_copy: { status:'', message: '', email: [], phone: [], template: false },
            compose_message: { email:[], phone:[], message:'', subject:'', template: false, previews: [], sms:[] },
            compose_message_copy: { email:[], phone:[], message:'', subject:'', template: false, previews: [], sms:[] },
            message_response: [],
            message_status: '',
            fulfilment: { status:'', dispatch_or_return:'Dispatch', date:'', method:'', reference:'', notes:'', items:[] },
            fulfilment_copy: { status:'', dispatch_or_return:'Dispatch', date:'', method:'', reference:'', notes:'', items:[] },
            shipping_methods: <?php echo json_encode($data['shipping_methods']);?>,
            cur_date: new Date(),
            history_filter: '',
            history_msg: '',
            email_content: '',
            email_to: '',
            show_reminder: false,
            reminder_timer: false,
            select_basket:[],
            selected_basket:[],
            prereq_item:{},
            basket_edit:{},
            rejection_state:false,
            showBasketDetails: [],
            showBasketItems: [],
		},
        methods:{
		    ...methods,
            prepare_delete_basket(id) {
                console.log(id);
                $('#delete_cart_basket_id').val(id);
                //$('#deletebasketmodal').modal('show');
            },
            toggleBasketDetails(index) {
              this.$set(this.showBasketDetails, index, !this.showBasketDetails[index]);
            },
            toggleBasketCollapse(index) {
              this.$set(this.showBasketItems, index, !this.showBasketItems[index]);
            },
            pre_req_fill: function(item){
                console.log(item)
                $('#prereq_order').html(item.product_title)
                $('#prereq_training').html(item.course_name)
                $('#training_date').html(item.short_course_date)
                $('#training_location').html(item.short_course_location)
                $('#proof_tr').hide()
                if (item.attachments.length>0) {
                    var html=""

                    $.each( item.attachments, function( key, value ) {
                      //alert( key + ": " + value );
                      html=html+'<a  style="margin:5px;" target="_blank" href="'+value.uri+'"><i class="fa fa-download"></a>'
                    });
                    $('#proof_of_training').html(html);
                  $('#proof_tr').show()
                }
                if (item.status=='Approved') {
                   $("#ck-approve input").prop('checked', true)
                   $("#ck-reject input").prop('checked', false)
                   $("#ck-reject").show()
                   $("#ck-approve").hide()
                }
                if (item.status=='Rejected') {
                   $("#ck-approve input").prop('checked', false)
                   $("#reject_span input").prop('checked', true)
                   $("#ck-reject").hide()
                   $("#ck-approve").show()
                }
                if (item.status==''||item.status==null) {
                   $("#ck-approve input").prop('checked', false)
                   $("#ck-reject input").prop('checked', false)
                   $("#ck-reject").show()
                   $("#ck-approve").show()
                }

                $("#prereq_item_id").val(item.pre_req_id)
                console.log($("#prereq_item_id").val())
                //$('#approve_span').html('')
                //$('#reject_span').html('<input type="checkbox"/>')
                $('#PrerequisitesModal').modal('show');
               console.log(item)
            },
            enter_rejection_state: function(){
            let vm =this
              Vue.set( vm, 'rejection_state', true);
              console.log(vm.rejection_state)
            },
            edit_payment:function(data){
                let vm=this
                vm.payment=data
                console.log( vm.payment);
                $('#add_payment').modal('show');
            },

             copyUrl(index) {
                // 1) Build the actual URL from all_invoices[index]
                const item = this.all_invoices[index];
                const invoiceLink = 
                  '<?php echo $product_site_url ?>' + '/products/invoices?trigger-payment=' + item.unique_id;

                // 2) Modern Clipboard API
                if (navigator.clipboard && navigator.clipboard.writeText) {
                  navigator.clipboard.writeText(invoiceLink)
                    .then(() => {
                      this.showBootstrapAlert('Copied to clipboard!', 'success');
                    })
                    .catch(() => {
                      this.fallbackCopy(invoiceLink);
                    });
                } else {
                  // 3) Fallback for older browsers
                  this.fallbackCopy(invoiceLink);
                }
              },

              fallbackCopy(text) {
                // Fallback uses a hidden textarea
                const temp = document.createElement('textarea');
                temp.value = text;
                document.body.appendChild(temp);
                temp.select();
                document.execCommand('copy');
                document.body.removeChild(temp);
                this.showBootstrapAlert('Copied to clipboard (fallback)!', 'success');
              },

              /**
               * Displays a Bootstrap 3 (or 4) alert inside #alert-container
               * Type can be 'success', 'danger', 'info', 'warning'
               */
              showBootstrapAlert(message, type) {
                // Create the alert HTML. For Bootstrap 3, use alert-dismissable + .close
                const alertHtml = `
                  <div class="alert alert-${type} alert-dismissable" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">
                      &times;
                    </button>
                    ${message}
                  </div>
                `;

                // Append it to #alert-container
                $('#alert-container').append(alertHtml);

                // Auto-remove the alert after 3s if you like
                setTimeout(function() {
                  $('#alert-container .alert').first().alert('close');
                }, 3000);
              },
                            

        },
        computed: {
            products_editable: function (){
                if(this.order.id===undefined) return false;
                if( this.order.payment_method==='card' ){
                    return this.order.order_status < this.past_edit_item && this.order.payment_status < this.paid;
                }
                return this.order.order_status < this.past_edit_item;
            },
            date_format: function (){
                let d = this.cur_date;
                return {
                    dateFormat : 'Y-m-d, H:i',
                    enableTime : true,
                    allowInput : true,
                    maxDate : d,
                    defaultDate: d
                }
            },
            selected: function (){
                 if(this.order.items) {
                    let s = this.order.items.map(function (item) {
                        if (item.selected===true) return item.id*1;
                        else return false;
                    });
                    console.log(s.filter( (item)=>item ))
                    if(s) return  s.filter( (item)=>item );
                }
                
                return [];
            },
            contact_emails: function (){
                if(this.contacts && this.contacts.email){
                    const contacts = this.contacts.email;
                    let emails = [];
                    for( const e in contacts){
                        emails.push(e);
                    }
                    return emails;
                }
                return [];
            },
            contact_phones: function (){
                if(this.contacts && this.contacts.phone){
                    const contacts = this.contacts.phone;
                    let p = [];
                    for( const e in contacts){
                        p.push(e);
                    }
                    return p;
                }
                return [];
            },
            items_returned: function (){
                if(this.fulfilment_history && this.fulfilment_history.length>0){
                    return this.fulfilment_history.reduce((acc, item) => acc + ( item.dispatch_or_return==='Return'?item.quantity*1:0 ) , 0);
                }
                return 0;
            },
            items_dispatched: function (){
                if(this.fulfilment_history && this.fulfilment_history.length>0){
                    let dispatched = this.fulfilment_history.reduce((acc, item) => acc + ( item.dispatch_or_return==='Dispatch'?item.quantity*1:0 ) , 0);
                    return dispatched - this.items_returned;
                }
                return 0;
            },
            filtered_history: function(){
                let vm=this
                if( typeof vm.order_history !== 'undefined'&& vm.order_history&& vm.order_history.length>0  ){
                    if(vm.history_filter){
                        const f = vm.history_filter;
                        if(f==='payments') return vm.order_history.filter( (item) => (item.subject==='Payment' || item.subject === 'Refund') );
                        return vm.order_history.filter( (item) => item.subject === f );
                    }
                    //console.log(vm.order_history)
                    return vm.order_history;
                }
                return [];
            },
            delivery_list: function (){ // function depracated, use to prepare_delivery_list
                let vm = this;
                if(vm.order.items){
                    const items = JSON.parse(JSON.stringify(vm.order.items));
                    const d = vm.order.details;
                    const address = [ d.organisation, d.delivery_contact_name, d.delivery_address, d.delivery_city+', '+d.delivery_county, d.delivery_country_name, d.delivery_postcode ];
                    items.forEach(function (item,i){
                        if(i===0){
                            item.
                            item.invoice_number = vm.order.invoice_username;
                            item.customer = vm.order.first_name +' '+ vm.order.surname;
                            item.address = address.join( '\n' );
                        }
                        else{
                            item.invoice_number = '';
                            item.customer = '';
                            item.address = '';
                        }
                        const fh = vm.fulfilment_history.filter( (f) => f.order_item_id == item.id );
                        if(fh){
                            item['dispatch_date'] = fh.reduce( (acc,f) => acc + f.date_actioned + '\n', '' );
                            item['dispatch_ref'] = fh.reduce( (acc,f) => acc + f.reference + '\n', '' );
                        }
                        else{
                            item['dispatch_date'] = '';
                            item['dispatch_ref'] = '';
                        }
                    });

                    return items;
                }
                return [];
            },
            q: function (){
                let vm = this;
                let qs = [];
                vm.order.items.forEach(function (item){
                    const id = item.id;
                    const fh = vm.fulfilment_history.filter( (f) => f.order_item_id === item.id );
                    if(fh){
                        const shipped = fh.reduce( (acc,f) => {
                            return f.dispatch_or_return==='Dispatch' ? acc + (f.quantity * 1) : acc - f.quantity;
                        }, 0 );
                        const to_ship = item.quantity - shipped;
                        qs[id] = {shipped:shipped,to_ship:to_ship};
                    }
                    else{
                        qs[id] = {shipped:0,to_ship:item.quantity};
                    }
                });
                return qs;
            },
            fulfilment_error: function (){
                let vm = this;
                let error = !(vm.fulfilment.items.length>0);
                if( vm.fulfilment.dispatch_or_return === 'Dispatch' ){
                    vm.fulfilment.items.forEach(function (item){
                        if( item.quantity > item.to_ship ) error = true;
                    });
                }
                else{
                    vm.fulfilment.items.forEach(function (item){
                        if( item.quantity > item.shipped ) error = true;
                    });
                }
                return error;
            },
            counter: function (){
                let vm = this;
                let m = { remaining: 160, messages: 1 };
                const msg = vm.compose_message.message.replace( /(<([^>]+)>)/ig, ''); // strip html
                m.messages = Math.ceil(msg.length/160);
                m.remaining = 160 - (msg.length%160);
                return m;
            }
        },
      mounted() {
          this.$nextTick(() => {
            if (Array.isArray(this.order_baskets)) {
              this.showBasketDetails = this.order_baskets.map(() => true);
            } else {
              this.showBasketDetails = [];
            }

            if (Array.isArray(this.order.baskets)) {
              this.showBasketItems = this.order.baskets.map(() => true);
            } else {
              this.showBasketItems = [];
            }
          });
        }
        ,
		filters: {
            fixed : function (input){
                if(!input) input = 0;
                input *= 1;
                return input.toFixed(2);
            },
            timeLapsed: function (from,to=false){
                if(!to) to = new Date();
                return moment( new Date(from) ).from( new Date(to), true );
            },
            dateFormat: function (input, format='lll'){
                return moment(input).format(format);
            },
		},
        components: {
            'flat-pickr': VueFlatpickr,
            'v-select': VueSelect.VueSelect
        }
	});

    $(document).ready(function() {
        CKEDITOR = null;
    });

    $('#PrerequisitesModal').on('hidden.bs.modal', function () {
      $("#order_item_id").val('')
    });


    $('#g_check input[type="checkbox"]').on('change', function() {
      $('input[type="checkbox"]').not(this).prop('checked', false);
   });
   
</script>

<style>
    a{
        cursor: pointer;
    }
	table.table-borderless > tbody > tr > th, table.table-borderless > tbody > tr > td{
		border: none!important;
		padding: 12px 0px;
	}
    table.table > tbody > tr > th{
	    font-weight: 450;
	    font-size: 14px;
	    user-select: all;
    }
    thead > tr > th, thead > tr > td{
        user-select: all;
    }
    .table-responsive{
        overflow-y: auto;
    }
    .table-responsive > .table{
        table-layout: auto;
    }
    .profile_status{
        font-size: 13.5px;
        padding: 20px 10px;
        min-width: 130px;
        height: 65px;
        margin-right: 10px;
        margin-bottom: 10px;
    }
    .profile_active{ border-bottom: 3px solid #57b157;}
    .tab-content{
	    margin-bottom: 30px;
	    padding-top: 0;
    }
    input.qty{
        border: none;
        width: 40px;
        text-align: center;
        display: table-cell;
        padding: 0;
    }
    td .badge {
        padding: 4px 7px;
    }
    #old_design .dropdown-menu {
        top: auto;
    }

    .p-title{
        cursor: pointer;
        display: inline-block;
    }

    .dropdown-menu img {
        width: 50px;
        max-height: 50px;
    }

    .loader-btn{
        position: absolute;
        right: 0;
        top: 0;
        background: transparent!important;
    }
    .vs__search, .vs__search:focus{
        padding: 2px 7px;
    }
    .checkbox-inline{
        line-height: 1.8;
    }
    .quillWrapper .ql-snow.ql-toolbar button svg{
        width: 18px;
        height: 18px;
    }
    .quillWrapper .ql-snow.ql-toolbar .ql-formats {
        margin-bottom: 2px;
    }
    .ql-toolbar.ql-snow .ql-formats {
        margin-right: 5px;
    }
    .ql-snow.ql-toolbar button, .ql-snow .ql-toolbar button {
        height: 22px;
        padding: 2px 4px;
        width: 24px;
    }
    .wizard-progress-with-circle{
        background-color: #e5e6ea;
    }
    .wizard-header{
        display: none;
    }
    .email-preview .panel-body{
        max-height: 300px;
        overflow-y: auto;
    }

    .email-preview.panel {
        margin-bottom: 20px;
        padding: 0;
        background-color: #fff;
        border: 1px solid transparent;
        border-radius: 4px;
        -webkit-box-shadow: 0 1px 1px rgba(0,0,0,.05);
        box-shadow: 0 1px 1px rgba(0,0,0,.05);
    }

    .email-preview.panel-default {
        border-color: #ddd;
    }

    .form-inline label{
        display: inline-block;
    }

    #history ol{
        margin-bottom: 0;
        padding-left: 12px;
    }

    #vueapp .reminder{
        background: rgba(255,255,255,0.8);
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        border-top: 5px solid dodgerblue;
    }


    #ck-approve {
    margin:4px;
    background-color:#4b9dcf;
    border-radius:4px;
    border:1px solid #D0D0D0;
    overflow:auto;
        float:left;
      width: 40%;
    padding-left: 11%;
    }

    #ck-approve:hover {
        background:blue;
    }

    #ck-approve label {
        float:left;
        width:4.0em;
    }

    #ck-approve label span {?
        text-align:center;
        padding:3px 0px;
        display:block;
    }

    #ck-approve label input {
        position:absolute;
        top:-20px;
    }

    #ck-approve input:checked + span {
        background-color:#245ea8;
        color:#245ea8;
    }

    #ck-approve input:checked  {
        background-color:#245ea8;
        color:#245ea8;
    }


    #ck-reject {
    margin:4px;
    background-color:#9a2618;
    border-radius:4px;
    border:1px solid #D0D0D0;
    overflow:auto;
        float:left;
        width: 40%;
    padding-left: 11%;
    }

    #ck-reject:hover {
        background:red;
    }

    #ck-reject label {
        float:left;
        width:4.0em;
    }

    #ck-reject label span {?
        text-align:center;
        padding:3px 0px;
        display:block;
    }

    #ck-reject label input {
        position:absolute;
        top:-20px;
    }

    #ck-reject input:checked + span {
        background-color:#911;
        color:#fff;
    }
    #ck-reject input:checked  {
        background-color:#911;
        color:#fff;
    }
    #ck-approve input{
        visibility: hidden;
    }

    #ck-reject input{
        visibility: hidden;
    }
</style>
