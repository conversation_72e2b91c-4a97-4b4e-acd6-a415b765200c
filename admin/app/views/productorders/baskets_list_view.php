<div class="row">
	<div style="margin-bottom: 15px;">
	<button type="button" class="btn btn-default" style="opacity: 0;width: 1px;padding: 0;">&nbsp;</button>
	<div style="display: inline-block;" class="pull-right">
		<a v-if="order.payment_status_name=='Authorised'" style="color: #fff;" class="btn btn-primary btn-sm" v-on:click="capture_order_payment(<?php echo $order['id'] ?>)"><i class="fa fa-money "></i>Capture Payment</a>
        <div style="display: inline-block;" v-if="selected_basket.length>0">
            <div class="dropdown" style="display:inline-block;">
                <button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Change Fulfilment Status&nbsp;&nbsp;&nbsp;<span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li v-for="fs in fulfilment_status">
                        <a v-if="fs.id>=fulfil_from && fs.id<=fulfil_to" v-on:click="fulfilment_modal(false,fs.id,true)" data-toggle="modal" data-target="#fulfilment_modal" href="#fulfilment_modal">{{fs.fulfilment_status_name}}<span class="fa fa-external-link float-right"></span></a>
                        <a v-else v-on:click="bulk_update_fulfilment_status(fs.id,true)">{{fs.fulfilment_status_name}}</a>
                    </li>
                </ul>
            </div>
        </div>
        <span v-else>
            <button type="button" class="btn btn-primary btn-sm" disabled>Change Fulfilment Status&nbsp;&nbsp;&nbsp;<span class="caret"></span></button>
        </span>
		<span v-if="edit_products">
			<button type="button" class="btn btn-sm btn-primary has-loader" v-on:click="save_product_changes">
				<span class="loader loader-light loader-sm"></span><span class="fa fa-save"></span> Save Changes
			</button>
			<button type="button" class="btn btn-sm btn-default" v-on:click="cancel_products"><span class="fa fa-undo"></span> Cancel</button>
		</span>
		<button v-else type="button" class="btn btn-sm btn-primary" title="Edit Products" v-on:click="enable_edit_products(true)">
			<span class="fa fa-edit"></span> Edit Products
		</button>
	</div>
</div>
</div>
<div class="row">
<div class="panel panel-default" v-for="(basket,b) in order.baskets" :key="b">
  <div class="panel-heading d-flex justify-content-between align-items-center">
    <div>
      <strong>{{basket.basket_name}}</strong> ({{ basket.items?.length || 0 }} items)
    </div>
    <div>
      <a href="#" @click.prevent="toggleBasketCollapse(b)">
        {{ showBasketItems[b] ? 'Hide items' : 'Show items' }}
      </a>
      &nbsp;
      

      <a class="pull-right" href="#" data-toggle="modal" data-target="#deletebasketModal">
        <span style="background-color: #999999;padding: 4px 7px;" class="badge" @click="prepare_delete_basket(basket.cart_basket_id)">
            <span class="fa fa-trash"></span>
        </span>
      </a>
      <a class="pull-right" href="#" data-toggle="modal" data-target="#basketModal">
        <span style="background-color: #999999;padding: 4px 7px;" class="badge" @click="get_basket_data(basket.cart_basket_id)">
            <span class="fa fa-edit"></span>
        </span>
      </a>
    </div>
  </div>
  <div class="panel-body" v-show="showBasketItems[b]">
    <div class="py-3">
        <table v-if="basket.items" class="table align-middle" style="table-layout: auto;">
			<thead>
			<tr>
	            <th>
	                <input type="checkbox"  v-on:click="select_all_basket_items(basket.items,b)" :class="'basket_selector'+b">
	            </th>
				<th class="px-2">#</th>
				<th>Product Name</th>
				<!-- <th class="pe-2">Prerequisites</th> -->
				<th class="pe-2">Fulfilment Status</th>
	            <th class="pe-2">Quantity Shipped</th>
	            <th class="pe-2">Quantity to Ship</th>
	            <th class="pe-2">Delivery Method</th>
				<th class="pe-2">Unit&nbsp;Price</th>
				<th class="pe-2">Ordered Quantity</th>
				<th class="pe-4">VAT</th>
				<th class="pe-2 text-end">Subtotal</th>
				<th class="pe-2">Office Notes</th>
			</tr>
			</thead>
			<tbody>
			<tr v-for="(item,i) in basket.items" :key="i">
	            <?php include('product_tr.php') ?>
			</tr>
			</tbody>
		</table>
	</div>
  </div>
</div>	
</div>

<?php 	#echo "<pre>".print_r($data['order'],1)."</pre>" ?> 
