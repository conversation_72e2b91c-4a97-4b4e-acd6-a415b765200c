<style>
    .language-icon {
        width: 50px;
        height: 50px;
    }

    .course-image {
        width: 100%;
        margin-top: 4px;
        margin-bottom: 4px;
        border-radius: 12px;
        height: 120px;
    }
</style>
<script>

    app.directive("ngFileSelect", function (fileReader, $timeout) {
        return {
            scope: {
                ngModel: '='
            },
            link: function ($scope, el) {
                function getFile(file) {
                    fileReader.readAsDataUrl(file, $scope)
                        .then(function (result) {
                            $timeout(function () {
                                $scope.ngModel = result;
                            });
                        });
                }

                el.bind("change", function (e) {
                    var file = (e.srcElement || e.target).files[0];
                    getFile(file);
                });
            }
        };
    });

    app.factory("fileReader", function ($q, $log) {
        var onLoad = function (reader, deferred, scope) {
            return function () {
                scope.$apply(function () {
                    deferred.resolve(reader.result);
                });
            };
        };

        var onError = function (reader, deferred, scope) {
            return function () {
                scope.$apply(function () {
                    deferred.reject(reader.result);
                });
            };
        };

        var onProgress = function (reader, scope) {
            return function (event) {
                scope.$broadcast("fileProgress", {
                    total: event.total,
                    loaded: event.loaded
                });
            };
        };

        var getReader = function (deferred, scope) {
            var reader = new FileReader();
            reader.onload = onLoad(reader, deferred, scope);
            reader.onerror = onError(reader, deferred, scope);
            reader.onprogress = onProgress(reader, scope);
            return reader;
        };

        var readAsDataURL = function (file, scope) {
            var deferred = $q.defer();

            var reader = getReader(deferred, scope);
            reader.readAsDataURL(file);

            return deferred.promise;
        };

        return {
            readAsDataUrl: readAsDataURL
        };
    });
    app.controller('demo', ['$scope', '$filter', '$http', 'fileUpload', function ($scope, $filter, $http, fileUpload) {

        $scope.course = <?php echo $data['entry_json']; ?>;
        $scope.all_survey_attached_modules_units = <?php echo json_encode($data['all_survey_attached_modules_units']); ?>;
        if (!Array.isArray($scope.all_survey_attached_modules_units) || !$scope.all_survey_attached_modules_units.length) {
            //do nothing
            $scope.all_survey_attached_modules_units_list = [];
        } else {
            $scope.all_survey_attached_modules_units_str = $scope.all_survey_attached_modules_units[0];
            if ($scope.all_survey_attached_modules_units_str != null) {
                $scope.all_survey_attached_modules_units_list = [];
                $scope.all_survey_attached_modules_units_list = $scope.all_survey_attached_modules_units_str.split(',');
            } else {
                $scope.all_survey_attached_modules_units_list = [];
            }
        }
        $scope.imageSrc = "";
        $scope.courseImageSrc = $scope.course.image ? $scope.course.image : "";
        $scope.courseThumbnailSrc = $scope.course.thumbnail ? $scope.course.thumbnail : "";
        $scope.existingActive = "active";
        $scope.newActive = "";
        $scope.data = <?php echo json_encode($data)?>;
        $scope.meta = $scope.data.course_info.language_meta;
        $scope.icon = $scope.meta.find(item => item.meta_key === "icon");
        if ($scope.icon !== undefined) {
            $scope.imageSrc = $scope.icon.meta_value
        }

        $scope.setSelectedMeta = function (item) {
            console.log(item);
            $scope.selectedMeta = item;
        }
        /** ===================================
         * Add a topic
         ====================================    */
        $scope.add_to_topics = function add_to_topics(topic, isNewTopic = false) {
            if (isNewTopic) {
                $scope.course.topics.push(topic.title);
            } else {
                $scope.course.topics.push(topic);
            }
            $scope.updateTopics();
            $("#topics_modal").modal("hide");
            return false;
        };
        console.log($scope.course)

        $scope.updateTopics = function () {
            console.log($scope.course.topics)

            $.ajax({
                data: {topics: $scope.course.topics},
                type: 'POST',
                url: '<?php echo $this->base_url(); ?>admin/online_courses/update_programme_topics/' + $scope.course.selected_course
            });
        }
        /** ===================================
         * Remove a topic
         ====================================    */
        $scope.remove_topic = function remove_topic(Index) {
            $scope.course.topics.splice(Index, 1);
            $scope.updateTopics();
            return false;
        }

        $scope.hasIcon = function () {
            return $scope.meta.filter(item => item.meta_key === "icon").length > 0;
        }
        $scope.uploadImage = function (type = '') {
            if ($scope.imageSrc === "") {
                pop_message("Please select an image to upload");
                return false;
            }
            if (type === 'header') {
                $scope.uploadCourseImage({
                    type: "image",
                    key: "image",
                    value: $('#courseImage')[0].files[0],
                });
            } else if (type === 'thumbnail') {
                $scope.uploadCourseThumbnail({
                    type: "thumbnail",
                    key: "image",
                    value: $('#courseThumbnail')[0].files[0],
                });
            } else {
                $scope.setMeta({
                    type: "image",
                    key: "icon",
                    value: $('#iconFile')[0].files[0],
                });
            }
        }
        $scope.setMeta = function (args) {
            let fd = new FormData();
            fd.append('meta_key', args.key);
            fd.append('type', args.type);
            fd.append('value', args.value);
            if (args.id !== null && args.id !== undefined) {
                fd.append('id', args.id);
            }
            $.ajax({
                url: '<?=base_url("languages/set_meta/" . $data['course_info']['course_language'])?>',
                type: 'post',
                data: fd,
                dataType: 'json',
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response.success) {
                        pop_message('Successfully saved');
                    } else {
                        pop_message('An error occurred');
                    }
                }
            });
        }
        $scope.uploadCourseImage = function (args) {
            let fd = new FormData();
            fd.append('value', args.value);
            fd.append('type', args.type);
            $.ajax({
                url: '<?=base_url("online_courses/course_image_upload/" . $data['course_info']['id'])?>',
                type: 'post',
                data: fd,
                dataType: 'json',
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response.success) {
                        pop_message('Successfully saved');
                    } else {
                        pop_message('An error occurred');
                    }
                }
            });
        }

        $scope.uploadCourseThumbnail = function (args) {
            let fd = new FormData();
            fd.append('value', args.value);
            fd.append('type', 'thumbnail');
            $.ajax({
                url: '<?=base_url("online_courses/course_thumbnail_upload/" . $data['course_info']['id'])?>',
                type: 'post',
                data: fd,
                dataType: 'json',
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response.success) {
                        pop_message('Successfully saved');
                    } else {
                        pop_message('An error occurred');
                    }
                }
            });
        }
        $scope.$on("fileProgress", function (e, progress) {
            $scope.progress = progress.loaded / progress.total;
        });
        <?php include('short_codes_js.php'); ?>

        if ($scope.course.excerpt) {
            $scope.show_summary_textarea = 1;
        }
    }]);

    app.filter('removeCharacter', function () {
        return function (text, filter) {
            var str = text.replace('>', '');
            str = str.replace('<', '');
            str = str.replace('=', '');
            filter.value = str;
            return str;
        };
    });
</script>
<div ng-controller="demo">
    <form method="POST" action="<?php echo $this->base_url('online_courses/' . $data['course_info']['id']); ?>"
          class="inner_container">
        <div class="breadcrumbs">
            <a href="<?php echo $this->base_url('online_courses'); ?>"><i class="fa fa-chevron-left"
                                                                          aria-hidden="true"></i> Online Courses</a>
        </div>
        <div class="top_section">
            <div class="buttons_wrap">
                <?php if ($data['course_info']['id'] == "new") { ?>
                    <input type="submit" name="add_course" value="Add course" class="btn btn-sm btn-primary">
                <?php } else { ?>
                    <input type="submit" name="save_course" value="Save" class="btn btn-sm btn-primary">
                <?php } ?>
            </div>
            <h1>
                <?php if ($data['course_info']['id'] != "new") { ?>
                    <span>{{ course.title }}</span>
                <?php } else { ?>
                    <span ng-hide="course.title">New Online course</span>
                    <span ng-show="course.title!=''">{{ course.title }}</span>
                <?php } ?>
            </h1>
        </div>

        <?php if ($_GET['added_course']) { ?>
            <div class="alert alert-success main_alert" role="alert">
                <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
                The course was added successfully. <a href="<?php echo $this->base_url("online_courses/new/"); ?>">Add a
                    new course</a>
            </div>
        <?php } ?>

        <?php if ($_GET['saved_course']) { ?>
            <div class="alert alert-success main_alert" role="alert">
                <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
                The course was saved successfully. <a href="<?php echo $this->base_url("online_courses/new/"); ?>">Add a
                    new course</a>
            </div>
        <?php } ?>

        <?php if ($_GET['deleted_module']) { ?>
            <div class="alert alert-success main_alert" role="alert">
                <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
                The module was deleted successfully.
            </div>
        <?php } ?>


        <div class="row">
            <div class="col-sm-8">

                <div class="panel panel-default no-padding ">
                    <div class="panel-body">
                        <label>Title</label>
                        <input autofocus="" dir="<?php echo $data['course_info']['language_direction']; ?>"
                               class="form-control ssb" name="title" type="text" ng-model="course.title"
                               value="<?php echo $data['course_info']['title']; ?>" required>
                        <label>Alternative Title</label>
                        <input dir="<?php echo $data['course_info']['language_direction']; ?>"
                               class="form-control ssb" name="alternative_title" type="text" ng-model="course.alternative_title"
                               value="<?php echo $data['course_info']['alternative_title']; ?>" required>

                        <label style="margin-top: 5px">Description <a href="#" class="pull-right" data-toggle="modal"
                                                                      data-target="#short_code_modal">Insert Short
                                code</a></label>
                        <textarea class="form-control ssb"
                                  dir="<?php echo $data['course_info']['language_direction']; ?>" name="description"
                                  style="height: 200px;"><?php echo $data['course_info']['description']; ?></textarea>

                        <script type="text/javascript">
                            editor =CKEDITOR.config.versionCheck = false;
                            editor = CKEDITOR.config.contentsLangDirection = '<?php echo $data['course_info']['language_direction']; ?>';
                            editor = CKEDITOR.replace('description', {height: '200px'});
                        </script>

                        <a href="#" ng-click="show_summary_textarea = !show_summary_textarea"
                           ng-hide="show_summary_textarea"><br>Add Excerpt/Summary</a>
                        <label ng-show="show_summary_textarea"><br>Excerpt/Summary</label>
                        <textarea class="form-control ssb"
                                  dir="<?php echo $data['course_info']['language_direction']; ?>" name="excerpt"
                                  style="height: 100px;"
                                  ng-show="show_summary_textarea"><?php echo $data['course_info']['excerpt']; ?></textarea>


                        <div class="row">

                            <div class="col-sm-12">
                                <hr>
                                <label>When do users see the next unit on the course? (required)</label>
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="next_unit_visibility"
                                               value="all_visible" <?php if ($data['course_info']['next_unit_visibility'] == "all_visible") {
                                            echo 'checked="checked"';
                                        } ?>> All Units Visible - All units are visible regardless of completion
                                        progress.
                                    </label>
                                </div>
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="next_unit_visibility"
                                               value="completed_only" <?php if ($data['course_info']['next_unit_visibility'] == "completed_only") {
                                            echo 'checked="checked"';
                                        } ?>> Only Completed/Next Units Visible - Only show units that have been
                                        completed, plus the next unit that the user can start.
                                    </label>
                                </div>

                                <hr>

                                <label>Allow learners to download course certificate on completion</label>
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="certificate"
                                               value="yes" <?php if ($data['course_info']['enable_download_certificate'] == "yes") {
                                            echo 'checked="checked"';
                                        } ?>> Yes
                                    </label>
                                </div>
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="certificate"
                                               value="no" <?php if ($data['course_info']['enable_download_certificate'] == "no") {
                                            echo 'checked="checked"';
                                        } ?>> No
                                    </label>
                                </div>

                                <hr>

                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" value="1"
                                               name="show_support_on_each_unit" <?php if ($data['course_info']['show_support_on_each_unit']) {
                                            echo 'checked="checked"';
                                        } ?>>
                                        Show access to support on each unit of the course
                                    </label>
                                </div>
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" value="1"
                                               name="show_feedback_on_each_unit" <?php if ($data['course_info']['show_feedback_on_each_unit']) {
                                            echo 'checked="checked"';
                                        } ?>>
                                        Show access to feedback on each unit of the course
                                    </label>
                                </div>

                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" value="1"
                                               name="featured" <?php if ($data['course_info']['featured']) {
                                            echo 'checked="checked"';
                                        } ?>>
                                        Mark as featured
                                    </label>
                                </div>

                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" value="1"
                                               name="publish" <?php if ($data['course_info']['publish'] == 'yes' || $data['course_info']['publish'] == '1') {
                                            echo 'checked="checked"';
                                        } ?>>
                                        Publish this course
                                    </label>
                                </div>

                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" value="1"
                                               name="rm_from_library" <?php if ($data['course_info']['rm_from_library'] == 'yes' || $data['course_info']['rm_from_library'] == '1') {
                                            echo 'checked="checked"';
                                        } ?>>
                                        Hide from main Course Library
                                    </label>
                                </div>
                            </div>

                            <div class="clear"></div>
                        </div>
                    </div>
                </div>


                <?php if ($data['course_info']['id'] != 'new') { ?>
                    <?php if (empty($data['course_info']['modules'])) { ?>
                        <div class="panel panel-default no-padding ">
                            <div class="panel-body">
                                <div class="no_results">
                                    <span class="glyphicon glyphicon-blackboard" aria-hidden="true"></span>
                                    <h2>No modules added yet</h2>
                                    <a class="btn btn-default"
                                       href="<?php echo $this->base_url('online-courses/' . $data['course_info']['id'] . '/modules/new'); ?>">Add
                                        a module</a>
                                </div>
                            </div>
                        </div>
                    <?php } ?>

                    <div id="modules_wrap">
                        <?php
                        foreach ($data['course_info']['modules'] as $module) {
                            ?>
                            <div class="admin_module" id="item_<?php echo $module['id']; ?>">
                                <div class="btn-group pull-right" role="group" aria-label="...">
                                    <a class="btn btn-default"
                                       href="<?php echo $this->base_url('online-courses/' . $data['course_info']['id'] . '/modules/' . $module['id']); ?>">Edit
                                        module</a>
                                    <a href="#" class="btn btn-default show_delete_module_modal"
                                       id="<?php echo $module['id']; ?>"><span class="glyphicon glyphicon-trash"
                                                                               aria-hidden="true"></span></a>
                                </div>
                                <!-- show icon for attached survey here i.e if attached to the module -->
                                <div class="pull-right"
                                     ng-show="all_survey_attached_modules_units_list.indexOf('<?php echo $module['id'] . '_'; ?>') !=-1">
                                    <span class="label label-primary"
                                          style="margin:5px;line-height:3">survey attached</span>
                                </div>

                                <h3><i class="fa fa-book" aria-hidden="true"></i> <?php echo $module['title']; ?></h3>
                                <ul class="connectedSortable sortable" id="<?php echo $module['id']; ?>">
                                    <?php
                                    $i = 0;
                                    foreach ($module['units'] as $unit) {
                                        $edit_unit_link = $this->base_url('online-courses/' . $data['course_info']['id'] . '/modules/' . $module['id'] . '/units/' . $unit['id']);
                                        ?>
                                        <li id="item_<?php echo $unit['id']; ?>">
                                            <i class="fa fa-bars" aria-hidden="true"></i>
                                            <div class="btn-group pull-right" role="group" aria-label="...">
                                                <!-- show icon for attached survey here i.e if attached to the unit -->
                                                <span ng-show="all_survey_attached_modules_units_list.indexOf('<?php echo $module['id'] . '_' . $unit['id']; ?>') !=-1"
                                                      class="label label-primary pull-left">survey attached</span>
                                                <a href="<?php echo $edit_unit_link; ?>?id=<?php echo $unit['id']; ?>"
                                                   class="btn btn-default btn-xs"><i class="fa fa-edit"
                                                                                     aria-hidden="true"></i></a>
                                                <a href="#" id="<?php echo $unit['id']; ?>"
                                                   class="btn  btn-default btn-xs show_delete_unit_modal"><i
                                                            class="fa fa-trash" aria-hidden="true"></i></a>
                                            </div>

                                            <div class="title">
                                                <a href="<?php echo $edit_unit_link; ?>">
                                                    <?php echo $unit['title']; ?>
                                                    <?php if ($unit['mp3_file']) { ?>
                                                        <i class="fa fa-volume-up" aria-hidden="true"></i>
                                                    <?php } ?>

                                                    <?php if (is_array($unit['quiz']) && $unit['quiz']['id'] && count($unit['quiz']['questions'])> 0 ) { ?>
                                                        <i class="fa fa-question-circle" aria-hidden="true"></i>
                                                    <?php } ?>
                                                </a>
                                                <?php if ($i == 6) { ?>
                                                    <a href="<?php echo $OL->admin_quiz_page; ?>"
                                                       class="quiz label label-success">Quiz: End of module
                                                        questions</a>
                                                <?php } ?>
                                            </div>
                                        </li>
                                    <?php } ?>
                                </ul>
                                <li class="add_unit">
                                    <a href="<?php echo $this->base_url('online-courses/' . $data['course_info']['id'] . '/modules/' . $module['id'] . '/units/new'); ?>"
                                       class="title"><i class="fa fa-plus" aria-hidden="true"></i> Add a unit</a>
                                </li>
                            </div>
                            </ul>
                        <?php } ?>
                    </div>

                    <?php if (count($data['course_info']['modules'])) { ?>
                        <div class="text-center">
                            <a class="btn btn-default"
                               href="<?php echo $this->base_url('online_courses/' . $data['course_info']['id'] . '/modules/new/'); ?>"><i
                                        class="fa fa-plus-square" aria-hidden="true"></i> Add a module</a>
                            <br><br>
                        </div>
                    <?php } ?>

                <?php } ?>

            </div>

            <div class="col-sm-4">

                <div class="panel panel-default no-padding grey hide">
                    <div class="panel-body">
                        <h2 class="next-heading">Visibility</h2>
                        <div class="radio">
                            <label>
                                <input type="radio" name="visibility" value="publish" checked="checked"> Publish
                            </label>
                        </div>
                        <div class="radio">
                            <label>
                                <input type="radio" name="visibility" value="publish" checked="checked"> Hidden
                            </label>
                        </div>
                    </div>
                </div>

                <div class="panel panel-default no-padding grey">
                    <div class="panel-body">
                        <h2 class="next-heading">Organisation</h2>
                        <label>Course Image</label>
                        <small style="color: red">Dimensions: 480 x 360</small>
                        <div class="course-image"
                             style="background-image: url({{courseImageSrc}}); background-position: top;background-repeat: no-repeat; background-size: cover"></div>
                        <div class="clearfix"></div>
                        <button type="button" class="pull-right mb-2 btn btn-primary me-2" style="margin-top: 5px"
                                data-toggle="modal"
                                data-target="#addCourseImageModal">Change Image
                        </button>
                        <div class="clearfix"></div>
                        <label>Course Thumbnail</label>
                        <small style="color: red">Dimensions: 150 x 300</small>
                        <div class="course-thumbnail"
                             style=" width: 150px; height:300px; background-image: url({{courseThumbnailSrc}}); background-position: top;background-repeat: no-repeat; background-size: cover"></div>
                        <div class="clearfix"></div>
                        <button type="button" class="pull-right mb-2 btn btn-primary me-2" style="margin-top: 5px"
                                data-toggle="modal"
                                data-target="#addCourseThumbnailModal">Change Image
                        </button>
                        <div class="clearfix"></div>
                        <label>Course Link</label>
                        <select class="form-control ssb" name="course_link" required>
                            <option value="">Choose...</option>
                            <?php foreach ($data["course_info"]['course_options'] as $option) { ?>
                                <option <?php if ($option['id'] == $data['course_info']['selected_course']) {
                                    echo 'selected="selected"';
                                } ?> value="<?php echo $option['id']; ?>"><?php echo $option['title']; ?></option>
                            <?php } ?>
                        </select>
                        <label>Course Slug</label>
                        <input class="form-control ssb" name="course_slug" type="text" id="course_order"
                               ng-model="course.course_slug">
                        <label>Course Url</label>
                        <input class="form-control ssb" name="course_external_url" type="text" id="course_order"
                               ng-model="course.course_external_url">
                        <label ng-if="course.language.id != '1'">English Course Link</label>
                        <small ng-if="course.language.id != '1'">Only for professionally translated courses</small>
                        <select ng-if="course.language.id != '1'" class="form-control ssb" name="parent_course"
                                ng-model="course.parent_course">
                            <option value="">Choose...</option>
                            <?php foreach ($data["english_courses"] as $option) { ?>
                                <option <?php if ($option['id'] == $data['course_info']['parent_course']) {
                                    echo 'selected="selected"';
                                } ?> value="<?php echo $option['id']; ?>"><?php echo $option['title']; ?></option>
                            <?php } ?>
                        </select>

                        <label>Order</label>
                        <input class="form-control ssb" name="order" type="number" id="course_order"
                               ng-model="course.order">

                        <label>Course For</label>
                        <select class="form-control ssb" name="course_level" required>
                            <option value="">Choose...</option>
                            <?php foreach ($data['courses_levels'] as $type) { ?>
                                <option <?php if ($type['id'] == $data['course_info']['course_level']) {
                                    echo 'selected="selected"';
                                } ?> value="<?php echo $type['id']; ?>"><?php echo $type['title']; ?></option>
                            <?php } ?>
                        </select>

                        <label>Group</label>
                        <select class="form-control ssb" name="group" required>
                            <option value="">Choose...</option>
                            <?php foreach ($data['course_groups'] as $group) { ?>
                                <option <?php if ($group['id'] == $data['course_info']['group']) {
                                    echo 'selected="selected"';
                                } ?> value="<?php echo $group['id']; ?>"><?php echo $group['title']; ?></option>
                            <?php } ?>
                        </select>

                        <label>Category</label>
                        <select class="form-control ssb" name="category" required>
                            <option value="">Choose...</option>
                            <?php foreach ($data['course_categories'] as $category) { ?>
                                <option <?php if ($category['id'] == $data['course_info']['category_new']['id']) {
                                    echo 'selected="selected"';
                                } ?> value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                            <?php } ?>
                        </select>

                        <label>Course Language</label>
                        <select class="form-control ssb" name="course_language" required>
                            <?php foreach ($data['all_languages'] as $language) { ?>
                                <option value="<?= $language['id'] ?>" <?php if ($data['course_info']['course_language'] == $language['id']) echo "selected"; ?> > <?php echo $language['name'] ?> </option>
                            <?php } ?>
                        </select>
                        <label>Course Language Icon</label>
                        <img class="language-icon" ng-src="{{imageSrc}}"/>
                        <button type="button" class="pull-right btn btn-primary me-2" data-toggle="modal"
                                data-target="#addIconModal">Change language icon
                        </button>

                        <label>Colour</label>
                        <input class="form-control ssb" name="colour" type="color"
                               value="<?php echo $data['course_info']['colour']; ?>" style="width: 40px; padding: 5px;"
                               required>
                    </div>
                </div>


                <div class="panel panel-default no-padding grey">
                    <div class="panel-body">
                        <h2 class="next-heading">Pre/Post Questionnaire</h2>

                        <p>
                            <a href="<?php echo $this->base_url('online-courses/pre_questions/' . $data['course_info']['id']); ?>">Click
                                here to manage pre-questions</a>
                        </p>

                        <p>
                            <a href="<?php echo $this->base_url('online-courses/post_questions/' . $data['course_info']['id']); ?>">Click
                                here to manage post-questions</a>
                        </p>

                        <p>
                            <a href="<?php echo $this->base_url('online-courses/pre_post_questions_settings/' . $data['course_info']['id']); ?>">Pre/Post
                                Questionnaire Settings</a>
                        </p>
                        <a href="<?php echo $this->base_url('online-courses/pre_post_questionnaire_report/' . $data['course_info']['id']); ?>">Pre/Post
                            Questionnaire Report</a>
                        </p>
                    </div>
                </div>

                <div class="panel panel-default no-padding grey">
                    <div class="panel-body">
                        <h2 class="next-heading">Survey Questionnaires</h2>
                        <?php foreach ($data['attached_quiz'] as $value) { ?>
                            <p><?php echo $value['title'] ?><a class="btn delete_survey"
                                                               data-delete_survey="<?php echo $value['id'] ?>"
                                                               style="padding: 0; margin-left: 6px; margin-top: -1px; display: inline-block;"><span
                                            class="glyphicon glyphicon-trash" aria-hidden="true"></span></a></p>
                        <?php } ?>
                        <p>
                            <a href="#" class="attach_survey">Click here to manage survey questionaire</a>
                        </p>


                    </div>
                </div>

                <div class="panel panel-default no-padding grey">
                    <div class="panel-body">
                        <h2 class="next-heading">Manage survey Questions</h2>

                        <p>
                            <a href="/admin/form_maker/ols_questions">Manage survey Questions</a>
                        </p>


                    </div>
                </div>
                <div class="panel panel-default no-padding grey">
                    <div class="panel-body">
                        <h2 class="next-heading">Manage Learner Feedback</h2>
                        <p>
                            <a href="<?php echo $this->base_url('online-courses/learner_feedback/' . $data['course_info']['id']); ?>">Manage
                                Learner Feedback</a>
                        </p>
                    </div>
                </div>
                <div class="panel panel-default no-padding grey">
                    <div class="panel-body np">
                        <div class="padding15 npb">
                            <a class="btn btn-default pull-right btn-xs" href="#" data-toggle="modal"
                               data-target="#topics_modal" ng-hide="course.topics.length<1">Add tag</a>
                            <h2 class="next-heading">Tags</h2>
                        </div>

                        <div class="text-center" ng-hide="course.topics.length">
                            <i class="fa fa-tags" style="font-size: 64px; margin-bottom: 10px; color: #999;"></i>
                            <p style="color: #999;">No tags added yet</p>
                            <a class="btn btn-default btn-xs" href="#" data-toggle="modal" data-target="#topics_modal">Add
                                tag</a>
                            <br>
                            <br>
                        </div>
                        <ul style="margin: 0; padding: 0;" class="variant_list">
                            <li ng-repeat="topic in course.topics track by $index" style="list-style-type: none;">
                                <span class="options"><i class="fa fa-trash" ng-click="remove_topic($index)"
                                                         style="cursor: pointer;"></i></span>
                                <a href="javascript:;">{{topic.title}}</a>
                                <input type="hidden" name="topics[]" value="{{topic.id}} ">
                            </li>
                        </ul>
                    </div>
                </div>


            </div>
        </div>

        <div class="bottom_buttons">
            <div class="btn btn-danger pull-left" ng-show="course.id!='new'" data-toggle="modal"
                 data-target="#delete_course_modal">Delete course
            </div>
            <div class="btn btn-primary pull-left" ng-show="course.id!='new'" data-toggle="modal"
                 data-target="#copy_course_modal">Copy course
            </div>
            <?php if ($data['course_info']['id'] == "new") { ?>
                <input type="submit" name="add_course" value="Add course" class="btn btn-sm btn-primary">
            <?php } else { ?>
                <input type="submit" name="save_course" value="Save" class="btn btn-sm btn-primary">
            <?php } ?>
        </div>

    </form>
    <div class="modal fade" id="topics_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">Tags</h4>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs">
                        <li role="presentation" ng-class="existingActive"
                            ng-click="existingActive='active'; newActive='';"><a href="#">Existing tags</a></li>
                        <li role="presentation" ng-class="newActive" ng-click="existingActive=''; newActive='active';">
                            <a href="#">Tags from search</a></li>
                    </ul>
                    <div class="saved_tabs" ng-show="existingActive==='active'">
                        <p>Choose a tag to add:</p>
                        <div style="padding: 5px; background-color: #f1f1f1; border-radius: 5px; height: 400px; overflow: auto;">
                            <table class="table table-striped" style="background-color: #fff; margin: 0;">
                                <tbody>
                                <tr ng-repeat="topic in course.topics">
                                    <td><a href="javascript: return false;"
                                           ng-click="add_to_topics(topic)">{{ topic.title }}</a></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="saved_tabs" ng-show="newActive==='active'">
                        <p>Choose a tag to add:</p>
                        <div style="padding: 5px; background-color: #f1f1f1; border-radius: 5px; height: 400px; overflow: auto;">
                            <table class="table table-striped" style="background-color: #fff; margin: 0;">
                                <tbody>
                                <tr ng-repeat="topic in course.recommended_topics">
                                    <td><a href="javascript: return false;"
                                           ng-click="add_to_topics(topic, true)">{{ topic.title }}</a></td>
                                    <td><a href="javascript: return false;"
                                           ng-click="add_to_topics(topic, true)">{{ topic.count }}</a></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- Assign Quiz Modal -->
    <div class="modal fade" id="quiz_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">Assign Quiz</h4>
                </div>
                <div class="modal-body np">
                    <div class="list-group">
                        <a href="#" class="list-group-item">Quiz 1</a>
                        <a href="#" class="list-group-item">Quiz 2</a>
                        <a href="#" class="list-group-item">Quiz 3</a>
                        <a href="#" class="list-group-item">Quiz 4</a>
                        <a href="#" class="list-group-item">Quiz 5</a>
                    </div>
                </div>

            </div>
        </div>
    </div>


    <!-- Delete Unit Message -->
    <div class="modal fade" id="delete_unit_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form method="POST" action="<?php echo $this->current_url(); ?>">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                    aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel">Delete Unit</h4>
                    </div>
                    <div class="modal-body text-center">
                        Are your sure you want to delete this Unit?
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="unit_id" class="unit_to_delete" value="">
                        <input type="hidden" name="action" value="delete_unit">
                        <input type="submit" class="btn btn-danger" value="Delete">
                    </div>
                </form>
            </div>
        </div>
    </div>


    <!-- Delete course modal -->
    <div class="modal fade" id="delete_course_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form method="POST" action="<?php echo $this->current_url(); ?>">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                    aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel">Delete Course</h4>
                    </div>
                    <div class="modal-body text-center">
                        Are your sure you want to delete this course?
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="action" value="delete_course">
                        <input type="submit" class="btn btn-danger" value="Delete">
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Copy course modal -->
    <div class="modal fade" id="copy_course_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form method="POST" action="<?php echo $this->current_url(); ?>">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                    aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel">Copy Course</h4>
                    </div>
                    <div class="modal-body text-center">
                        Are your sure you want to copy this course?
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="action" value="copy_course">
                        <input type="submit" class="btn btn-primary" value="Copy">
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div id="addIconModal" class="modal fade" role="dialog">
        <div class="modal-dialog">

            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Add / Change language icon</h4>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-sm-4">
                                <input id="iconFile" type="file" ng-file-select="onFileSelect($files)"
                                       ng-model="imageSrc">
                            </div>
                            <div class="col-sm-8">
                                <img class="language-icon" ng-src="{{imageSrc}}"/>
                            </div>
                        </div>
                    </form>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" ng-click="uploadImage()">Save</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>

        </div>
    </div>

    <div id="addCourseImageModal" class="modal fade" role="dialog">
        <div class="modal-dialog">

            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Add / Change Course Image</h4>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-sm-4">
                                <input id="courseImage" type="file" ng-file-select="onFileSelect($files)"
                                       ng-model="courseImageSrc">
                            </div>
                            <div class="col-sm-8">
                                <img ng-src="{{courseImageSrc}}"/>
                            </div>
                        </div>
                    </form>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" ng-click="uploadImage('header')">Save</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>

        </div>
    </div>

    <div id="addCourseThumbnailModal" class="modal fade" role="dialog">
        <div class="modal-dialog">

            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Add / Change Course Thumbnail</h4>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-sm-4">
                                <input id="courseThumbnail" type="file" ng-file-select="onFileSelect($files)"
                                       ng-model="courseThumbnailSrc">
                            </div>
                            <div class="col-sm-8">
                                <img ng-src="{{courseThumbnailSrc}}"/>
                            </div>
                        </div>
                    </form>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" ng-click="uploadImage('thumbnail')">Save</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>

        </div>
    </div>
    <!-- Delete module modal -->
    <div class="modal fade" id="delete_module_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form method="POST" action="<?php echo $this->current_url(); ?>">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                    aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel">Delete Module</h4>
                    </div>
                    <div class="modal-body text-center">
                        Are your sure you want to delete this module?
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="module_id" class="module_to_delete" value="">
                        <input type="hidden" name="action" value="delete_module">
                        <input type="submit" class="btn btn-danger" value="Delete">
                    </div>
                </form>
            </div>
        </div>
    </div>


    <!-- attach  questionaire modal -->
    <?php
    $OL = new OnlineLearning;
    $existing_questionaires = $OL->existing_questionaires();

    ?>
    <div class="modal fade" id="attach_survey_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form method="POST" action="<?php echo $this->current_url(); ?>">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                    aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel">Attach Survey</h4>
                    </div>
                    <div class="modal-body text-center">
                        <select class="form-control" name="quiz_id">
                            <?php foreach ($existing_questionaires as $key => $value) { ?>
                                <option value="<?php echo $value['quiz_id'] ?>"><?php echo $value['title'] ?></option>
                            <?php } ?>
                        </select>
                    </div>
                    <div class="modal-footer">
                        <!-- <input type="hidden" name="attach_survey" class="module_to_delete" value=""> -->
                        <input type="hidden" name="action" value="attach_survey">
                        <input type="submit" class="btn btn-success" value="Submit">
                    </div>
                </form>
            </div>
        </div>
    </div>


    <!-- Short codes modal -->
    <?php include('short_codes_modal.php'); ?>


    <script>
        $(function () {
            //Delete Unit Modal
            $(".show_delete_unit_modal").click(function () {
                var uid = $(this).attr("id");
                $(".unit_to_delete").val(uid);
                $("#delete_unit_modal").modal("show");
                return false;
            });


            //Delete Module Modal
            $(".show_delete_module_modal").click(function () {
                var uid = $(this).attr("id");
                $(".module_to_delete").val(uid);
                $("#delete_module_modal").modal("show");
                return false;
            });

            //Sortable Units
            $(".sortable").sortable({
                connectWith: ".connectedSortable",
                update: function (event, ui) {
                    var course_id = '<?php echo $slugs['3']; ?>';
                    var module_id = $(this).attr("id");
                    var order = $(this).sortable("serialize") + '&action=save_units_order' + '&course_id=' + course_id + '&module_id=' + module_id;
                    console.log($(this).sortable("serialize"));
                    $.ajax({
                        data: order,
                        type: 'POST',
                        url: '<?php echo $this->base_url(); ?>/ajax/online_learning.php'
                    });
                }
            }).disableSelection();

            //sortable Modules
            $("#modules_wrap").sortable({
                update: function (event, ui) {
                    var course_id = '<?php echo $slugs['3']; ?>'
                    var order = $(this).sortable("serialize") + '&action=save_modules_order' + '&course_id=' + course_id;
                    console.log($(this).sortable("serialize"));
                    $.ajax({
                        data: order,
                        type: 'POST',
                        url: '<?php echo $this->base_url(); ?>/ajax/online_learning.php'
                    });
                }
            }).disableSelection();
        });

        $(".attach_survey").click(function () {
            console.log($(this))
            $("#attach_survey_modal").modal("show");
        })

        $('.delete_survey').click(function (e) {
            e.preventDefault()
            var form = $(document.createElement('form'));
            //$(form).attr("action", "reserves.php");
            $(form).attr("method", "POST");
            var input = $("<input>").attr("type", "hidden").attr("name", "action").val("delete_survey");
            $(form).append($(input));
            var input1 = $("<input>").attr("type", "hidden").attr("name", "quiz_id").val($(this).data('delete_survey'));
            $(form).append($(input1));
            form.appendTo(document.body)
            $(form).submit();
        })
    </script>
</div>
  
