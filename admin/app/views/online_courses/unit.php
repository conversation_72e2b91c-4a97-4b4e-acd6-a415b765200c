<?php 

  // $data['course_info'] = "";
  // $data['entry_json'] = "";
  // $data['courses_categories'] = "";
  // $data['module'] = "";
  // echo '<pre>';
  // print_r($data);
  // echo '</pre>';
  // exit();
?>
<script src="<?php echo $this->base_url(); ?>assets/js/form_editor.js?v=1" type="text/javascript"></script>
<script>
  app.controller('demo', ['$scope','$rootScope','$filter','$http', 'formEditor','$window', function($scope,$rootScope,$filter,$http, formEditor,$window){
	  
  $scope.unit = <?php echo $data['entry_json']; ?>;


  $scope.remove_mp3_file = function remove_mp3_file() {
  	$scope.unit.mp3_file = "";
  };
  
  $scope.upload_mp3_file = function upload_mp3_file() {
  	var datastring = $(this).serializeArray();
    var data = {};
    $(datastring).each(function(index, obj){
        data[obj.name] = obj.value;
    });
    data['process'] =1;

    filesFields = $("#mp3_file_upload");
    picture_target = $("#upload_percentage");

    for (var i = 0; i < filesFields.get(0).files.length; ++i) {
      
      data['action'] = 'upload_mp3';
      data['file_input_id'] = '#mp3_file_upload';
      data['unit_id'] = $("#unit_id").val();
      data['file_position'] = i;
      data['upload_file'] =true;
      file_pos = i;
      var myfile = $(this);

      
      myfile.upload('<?php echo $this->site_info['url'];?>/admin/ajax/online_learning.php',data,function(content,file_pos){
      	//$scope.unit.mp3 = content.mp3;
      	angular.element('#mycontroller').scope().unit.mp3_file = content.mp3;
      	$scope.$apply();
      	$scope.$digest();
      },function(prog,value,file_pos){
        picture_target.find(".progress-bar").css({width : value+'%'}).html(value+'%');    
      });
    }

    return false;
  };

  $("#mp3_file_upload").change(function(){
  	angular.element('#mycontroller').scope().upload_mp3_file();
  });


 


  $scope.edit_quiz = function edit_quiz(quiz_id){
   

      $("#edit_custom_form_modal").modal({backdrop: 'static', keyboard: true,show: true});
      //$("#pageCustomFrom").html("");  


        /** ===================================
        * SHow Checklist Form
        ====================================  */
        $rootScope.form2 = new formEditor({
          'wrap': "#pageCustomFrom",
          'id':quiz_id,
          'show_table_view':true,
          'db_connection_name': '',
          'custom_form': false,
          'system_abbrv':'inst',
          'correct_answer': true,
          'answer_hints':true,
          'correct_answer_reasons': true,
          'incorrect_answer_reasons': true,
          'answer_weighting':false,
          'hide_rich_text':true,
          'hide_date':true,
          'hide_other':true,
          'hide_checkbox':true,
          'disabled_adding_fields':false,
          'disabled_editing_fields':false,
        });
      
    }


 	function copyToClipboard(elem) {
	  // create hidden text element, if it doesn't already exist
    var targetId = "_hiddenCopyText_";
    var isInput = elem.tagName === "INPUT" || elem.tagName === "TEXTAREA";
    var origSelectionStart, origSelectionEnd;
    if (isInput) {
        // can just use the original source element for the selection and copy
        target = elem;
        origSelectionStart = elem.selectionStart;
        origSelectionEnd = elem.selectionEnd;
    } else {
        // must use a temporary form element for the selection and copy
        target = document.getElementById(targetId);
        if (!target) {
            var target = document.createElement("textarea");
            target.style.position = "absolute";
            target.style.left = "-9999px";
            target.style.top = "0";
            target.id = targetId;
            document.body.appendChild(target);
        }
        target.textContent = elem.textContent;
    }
    // select the content
    var currentFocus = document.activeElement;
    target.focus();
    target.setSelectionRange(0, target.value.length);
    
    // copy the selection
    var succeed;
    try {
    	  succeed = document.execCommand("copy");
    } catch(e) {
        succeed = false;
    }
    // restore original focus
    if (currentFocus && typeof currentFocus.focus === "function") {
        currentFocus.focus();
    }
    
    if (isInput) {
        // restore prior selection
        elem.setSelectionRange(origSelectionStart, origSelectionEnd);
    } else {
        // clear temporary content
        target.textContent = "";
    }
    return succeed;
	}


	$(document).on('click', '.copy_to_clipboard', function () {
		var mid = $(this).attr("id");
		copyToClipboard(document.getElementById("media_"+mid));
	});


	$(document).on('click', '.delete_media_entry', function () {
		var mid = $(this).attr("id");
		$(this).parent().parent().parent().parent().remove();


		$.ajax({
	    type: 'POST',
	    url: '<?php echo $this->site_info['url'];?>/admin/ajax/online_learning.php',
	    data: { 
	        'action': 'delete_media', 
	        'entry_id': mid
	    },
	    success: function(msg){
	    }
		});
	});

  <?php include('short_codes_js.php'); ?>

}]);

	app.filter('removeCharacter', function () {
	    return function (text, filter) {
	        var str = text.replace('>', '');
	        str = str.replace('<', '');
	        str = str.replace('=', '');
	        filter.value = str;
	        return str;
	    };
	});


	$(document).ready(function(){
       $('#quiz_answerable_multiple_times').click(function(){
       	   if ( $('#show_user_answer').is(":checked")) {
       	   	  $('#show_user_answer').click()
       	   }
       })

       $('#show_user_answer').click(function(){
       	   if ( $('#quiz_answerable_multiple_times').is(":checked")) {
       	   	  $('#quiz_answerable_multiple_times').click()
       	   }
       })
	})
</script>

<div ng-controller="demo" id="mycontroller">
	<form method="POST" action="<?php echo $this->current_url(); ?>" class="inner_container">
		
			<div class="breadcrumbs">
				<a href="<?php echo $this->base_url('online-courses/'.$data['course_info']['id']); ?>"><i class="fa fa-chevron-left" aria-hidden="true"></i> <?php echo $data['course_info']['title']; ?></a>/<?php echo $data['module']['title'];?>
			</div>
			<div class="top_section">
				<div class="buttons_wrap">
					<?php if(!$data['unit']['id']){ ?>
						<input type="submit" name="add_unit" value="Add unit" class="btn btn-sm btn-primary">	
					<?php }else{ ?>
						<input type="submit" name="save_unit" value="Save" class="btn btn-sm btn-primary">	
					<?php } ?>
				</div>
				<h1><?php if($data['unit']['id']){ echo $data['unit']['title']; }else{ echo 'New'; } ?></h1>
			</div>


			<?php if($_GET['added_entry']){ ?>
        <div class="alert alert-success main_alert" role="alert">
          <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
          The unit was added successfully. <a href="<?php echo $this->base_url("online_courses/".$data['course_info']['id']."/modules/".$data['module']['id']."/units/new/"); ?>">Add another unit</a>
        </div>
      <?php }?>


      <?php if(!empty($_GET['deleted_quiz'])){ ?>
        <div class="alert alert-success main_alert" role="alert">
          <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
          <?php echo $_GET['deleted_quiz']==='true' ? 'The quiz was deleted successfully.' : $_GET['deleted_quiz'];?>
        </div>
      <?php }?>

			<?php if($_GET['saved_entry']){ ?>
				<div class="alert alert-success main_alert" role="alert">
					<span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
					The unit was saved successfully. <a href="<?php echo $this->base_url("online_courses/".$data['course_info']['id']."/modules/".$data['module']['id']."/units/new/"); ?>">Add another unit</a> or <a target="_blank" href="<?php echo $data['unit']['href'] ?>">View the unit live</a>
				</div>
			<?php }?>

			<div class="row">
				<div class="col-sm-8">

					<div class="panel panel-default no-padding ">
						<div class="panel-body">
										
							<label>Title</label>
							<input class="form-control input-lg ssb" autofocus="" dir = "<?php echo $data['course_info']['unit_slug']; ?>" required="" name="title" type="text" value="<?php echo $data['unit']['unit_slug']; ?>">


							<label>Unit Slug</label>
							<input class="form-control input-lg ssb" autofocus="" dir = "<?php echo $data['course_info']['language_direction']; ?>" required="" name="title" type="text" value="<?php echo $data['unit']['title']; ?>">


							<label>Description 
								<a href="#" class="pull-right" data-toggle="modal" data-target="#short_code_modal">Insert Short code</a>
								<a href="#" class="pull-right" data-toggle="modal" data-target="#pictures_library" style="margin-right: 15px;">Pictures Library</a>
							</label>
							<textarea class="form-control ssb tinymce" dir="<?php echo $data['course_info']['language_direction']; ?>"  name="description" type="text" style="height: 350px;max-height: 1000px"><?php echo $data['unit']['description']; ?> </textarea>
							<script type="text/javascript">
								editor = CKEDITOR.config.contentsLangDirection = '<?php echo $data['course_info']['language_direction']; ?>';
                                editor = CKEDITOR.replace('description',{
                                    height: '200px',
                                    versionCheck: false
                                });
                                editor.on('focus', function(event) {
                                    editor.resize( '100%', '1000', true );
                                });

	                        </script>

							<br>
							<label>Voiceover URL</label>
							<div class="btn btn-default" style="position: relative; overflow: hidden;" ng-hide="unit.mp3_file">
								<i class="fa fa-file-audio-o" aria-hidden="true"></i> Upload MP3 File
						  	<input type="file" id="mp3_file_upload" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0">
						  	<input type="hidden" id="unit_id" value="<?php echo $data['unit']['id']; ?>">
						  </div>

							<button class="btn btn-default" type="button"  ng-show="unit.mp3_file" ng-click="remove_mp3_file()"><i class="fa fa-file-audio-o" aria-hidden="true"></i> Remove MP3 file from unit</button>

							<input type="hidden" name="mp3_file" value="{{ unit.mp3_file }}">

														
							<!--<p class="subdued note ssb">The description of this course. Your trainees will be able to see this course description.</p>-->

							<label>Voiceover Autoplay</label>
							<div class="checkbox">
						    <label>
						      <input type="checkbox" <?php if($data['unit']['mp3_auto_play']){ echo 'checked="checked"'; } ?> name="mp3_auto_play" value="1"> Should the voiceover be played automatically when this course unit is loaded
						    </label>
						  </div>
						  <br>

						  <label>Is Unit part of Trial?</label>
							<div class="checkbox">
						    <label>
						      <input type="checkbox" <?php if($data['unit']['trail']){ echo 'checked="checked"'; } ?> name="trail" value="1"> Yes
						    </label>
						  </div>
						  <br>
							<label>Does this unit have a Progress Certificate?</label>
							<div class="checkbox">
								<label>
									<input type="checkbox" <?php if($data['unit']['progress_certificate']){ echo 'checked="checked"'; } ?> name="progress_certificate" value="1"> Yes
								</label>
							</div>
							<br>
							<label>Progress Certificate title</label>
							<input class="form-control input-lg ssb" name="progress_certificate_title" type="text" value="<?php echo $data['unit']['progress_certificate_title']; ?>">

							<br>


							<div class="panel panel-default hide">
							  <div class="panel-heading">Revisions</div>
							  <div class="panel-body">
							  	<p><i class="fa fa-user-circle" aria-hidden="true"></i> demo_author, 2 years ago (<u><a href="#">20 January, 2015 @ 10:13:12</a></u>)</p>

							  	<p><i class="fa fa-user-circle" aria-hidden="true"></i> demo_author, 2 years ago (<u><a href="#">20 January, 2015 @ 10:13:12</a></u>)</p>

							  	<p><i class="fa fa-user-circle" aria-hidden="true"></i> demo_author, 2 years ago (<u><a href="#">20 January, 2015 @ 10:13:12</a></u>)</p>

							  	<p><i class="fa fa-user-circle" aria-hidden="true"></i> demo_author, 2 years ago (<u><a href="#">20 January, 2015 @ 10:13:12</a></u>)</p>
							  </div>
							</div>

						</div>
					</div>


				</div>

			<div class="col-sm-4">

				<div class="panel panel-default no-padding grey">
					<div class="panel-body">
						<h2 class="next-heading">Visibility</h2>
						<div class="radio">
					    <label>
					      <input type="radio" <?php if($data['unit']['visibility']=="publish" || empty($data['unit']['visibility'])){ echo 'checked="checked"'; } ?> name="visibility" value="publish" > Publish
					    </label>
					  </div>
					  <div class="radio">
					    <label>
					      <input type="radio" <?php if($data['unit']['visibility']=="hidden"){ echo 'checked="checked"'; } ?> name="visibility" value="hidden" > Hidden
					    </label>
					  </div>
					</div>
				</div>

				<div class="panel panel-default no-padding grey">
					<div class="panel-body">
						<h2 class="next-heading">Manage Quiz Questions</h2>
						<?php if(!$data['unit']['quiz']['id']){ ?>
						<div class="no_results tiny">
							<span class="glyphicon glyphicon-question-sign" aria-hidden="true"></span>
							<h2>You currently don't have any quiz assigned to this unit</h2>
							<a class="btn btn-default" data-toggle="modal" data-target="#newFormModal">Add quiz questions</a>
						</div>
						<?php }else{ ?>
							<?php if(!$data['unit']['quiz']['questions']){ ?>
								<div class="no_results tiny">
									<span class="glyphicon glyphicon-question-sign" aria-hidden="true"></span>
									<h2>You currently don't have any quiz questions</h2>
									<button type="button" class="btn btn-default" ng-click="edit_quiz(unit.quiz.id)">Add a question</button>
								</div>
							<?php }else{ ?>
							<ul style="margin: 0px; padding: 0px; padding-left: 15px;">
								<?php foreach ($data['unit']['quiz']['questions'] as $field){ ?>
								<li><?php echo $field['title']; ?></li>
								<?php } ?>
							</ul>
							<br>
							<div class="clearfix"></div>
							<button type="button" class="btn btn-default" ng-click="edit_quiz(unit.quiz.id)">Manage Questions</button>

							<a class="btn btn-default" href="#" data-toggle="modal" data-target="#delete_quiz_modal">Delete Quiz Questions</a>
						<?php }} ?>

						<?php if($data['unit']['quiz']['questions']){ ?>
						<hr>
						<label>Is this quiz mandatory?</label>
						<div class="checkbox">
					    <label>
					      <input type="checkbox" <?php if($data['unit']['quiz_mandetory']){ echo 'checked="checked"'; } ?> name="quiz_mandetory" value="1"> Yes
					    </label>
					  </div>

					    
						<label>Can be answered multiple times?</label>
						<div class="checkbox">
					    <label>
					      <input type="checkbox" <?php if($data['unit']['quiz_answerable_multiple_times']){ echo 'checked="checked"'; } ?> name="quiz_answerable_multiple_times" id='quiz_answerable_multiple_times' value="1"> Yes
					    </label>
					    </div>

					    
						<label>Show the user the answer?</label>
						<div class="checkbox">
					    <label>
					      <input type="checkbox" <?php if($data['unit']['show_user_answer']){ echo 'checked="checked"'; } ?> name="show_user_answer" id='show_user_answer' value="1"> Yes
					    </label>
					    </div>
					  <?php } ?>


						
					</div>
				</div>




				<div class="panel panel-default no-padding grey">
					<div class="panel-body">
						<h2 class="next-heading">Questionnaire</h2>
						<?php if(!$data['unit']['questionnaire_form_id']){ ?>
						<div class="no_results tiny">
							<span class="glyphicon glyphicon-question-sign" aria-hidden="true"></span>
							<h2>You currently don't have any questions for this unit</h2>
							<a class="btn btn-default" href="<?php echo $this->base_url('online-courses/'.$data['course_info']['id'].'/modules/'.$data['module']['id'].'/units/'.$data['unit']['id'].'/questionnaire/'); ?>">Add questions</a>
						</div>
						<?php }else{ ?>
							<?php
								$form_templates = new FormTemplates;
								$page_args = array("id"=>$data['unit']['questionnaire_form_id'],"system_abbrv"=>"inst","school_id"=>$this->school_info['id']);
								$form = $form_templates->get($page_args);
								if(empty( $form['fields'])){
							?>
								<div class="no_results tiny">
									<span class="glyphicon glyphicon-question-sign" aria-hidden="true"></span>
									<h2>You currently don't have any questions</h2>
									<a class="btn btn-default" href="<?php echo $this->base_url('online-courses/'.$data['course_info']['id'].'/modules/'.$data['module']['id'].'/units/'.$data['unit']['id'].'/questionnaire/'); ?>">Add a question</a>
								</div>
							<?php }else{ ?>
							<ul style="margin: 0px; padding: 0px; padding-left: 15px;">
								<?php
									foreach ($form['fields'] as $field){
								?>
								<li><?php echo $field['title']; ?></li>
								<?php } ?>
							</ul>
							<br>
							<div class="clearfix"></div>
							<a class="btn btn-default" href="<?php echo $this->base_url('online-courses/'.$data['course_info']['id'].'/modules/'.$data['module']['id'].'/units/'.$data['unit']['id'].'/questionnaire/'); ?>">Manage Questions</a>

						<?php }} ?>

						<?php if(count($form['fields'])){ ?>
						<hr>
						<label>Is this questionnaire mandatory?</label>
						<div class="checkbox">
					    <label>
					      <input type="checkbox" <?php if($data['unit']['questionnaire_mandetory']){ echo 'checked="checked"'; } ?> name="questionnaire_mandetory" value="1"> Yes
					    </label>
					  </div>
					  <?php } ?>
						
					</div>
				</div>

				
				<div class="panel panel-default no-padding grey">
					<div class="panel-body">
						<h2 class="next-heading">Survey Questionnaire</h2>
						<?php foreach ($data['attached_quiz'] as $value) {?>
							<p><?php echo $value['title']?><a class="btn delete_survey" data-delete_survey="<?php echo $value['id']?>" style="padding: 0; margin-left: 6px; margin-top: -1px; display: inline-block;"><span class="glyphicon glyphicon-trash" aria-hidden="true"></span></a></p>
						<?php }  ?>
						<p>
						<a href="#" class="attach_survey">Click here to manage survey questionaire</a> 
						</p>
	
						
					</div>
				</div>

				<div class="panel panel-default no-padding grey">
					<div class="panel-body">
						<h2 class="next-heading">Manage survey Questions</h2>
						
						<p>
						<a href="/admin/form_maker/ols_questions" >Manage survey Questions</a> 
						</p>
	
						
					</div>
				</div>

			</div>

		</div>


		<div class="bottom_buttons">
			<!--<div class="btn btn-danger pull-left" ng-show="course.id!='new'" data-toggle="modal" data-target="#delete_course_modal">Delete unit</div>-->
			<?php if(!$data['unit']['id']){ ?>
				<input type="submit" name="add_unit" value="Add unit" class="btn btn-sm btn-primary">
			<?php }else{ ?>
				<input type="submit" name="save_unit" value="Save" class="btn btn-sm btn-primary">
			<?php } ?>
		</div>


	</form>



<!-- Short codes modal -->
<?php include('short_codes_modal.php'); ?>

	<!-- New From Modal -->
<div class="modal fade" id="newFormModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="myModalLabel">Add new quiz</h4>
      </div>
      <form method="POST" action="<?php echo $this->current_url(); ?>">
	      <div class="modal-body">
	        <label>Quiz title</label>
	        <input class="form-control ssb" type="text" name="table_name" required="required" autofocus="">
	        <input type="hidden" name="category" value="7">
	      </div>
	      <div class="modal-footer">
	        <button type="submit" class="btn btn-primary btn-block btn-lg">Add quiz</button>
	        <input type="hidden" name="action" value="add_new_form">
	      </div>
      </form>
    </div>
  </div>
</div>


<!-- Delete module modal -->
<div class="modal fade" id="delete_quiz_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
    	<form method="POST" action="<?php echo $this->current_url(); ?>">
	      <div class="modal-header">
	        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
	        <h4 class="modal-title" id="myModalLabel">Delete Quiz</h4>
	      </div>
	      <div class="modal-body text-center">
	      	Are your sure you want to delete these quiz questions?
	      </div>
	      <div class="modal-footer">
	      	<input type="hidden" name="action" value="delete_quiz">
          <input type="hidden" name="quiz_id" value="<?php echo $data['unit']['quiz']['id']; ?>">
	      	<input type="submit" class="btn btn-danger" value="Delete">
	      </div>
      </form>
    </div>
  </div>
</div>


	<!-- attach  questionaire modal -->
		<?php 
			$OL = new OnlineLearning;
			$existing_questionaires= $OL->existing_questionaires();

		?>
		<div class="modal fade" id="attach_survey_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<form method="POST" action="<?php echo $this->current_url(); ?>">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel">Attach Survey</h4>
				</div>
				<div class="modal-body text-center">
					<select  class="form-control"  name="quiz_id">
						<?php foreach ($existing_questionaires as $key => $value) { ?>
						<option value="<?php echo $value['quiz_id'] ?>"><?php echo $value['title'] ?></option>
						<?php } ?>
					</select>
				</div>
				<div class="modal-footer">
					<!-- <input type="hidden" name="attach_survey" class="module_to_delete" value=""> -->
					<input type="hidden" name="action" value="attach_survey">
					<input type="submit" class="btn btn-success" value="Submit">
				</div>
			</form>
			</div>
		</div>
	</div>


</div>


	<script type="text/javascript">
		$(".attach_survey").click(function(){
			console.log($(this))
			$("#attach_survey_modal").modal("show");
		})

		$('.delete_survey').click(function(e){
			e.preventDefault()
			var form = $(document.createElement('form'));
			//$(form).attr("action", "reserves.php");
			$(form).attr("method", "POST");
			var input = $("<input>").attr("type", "hidden").attr("name", "action").val("delete_survey");
			$(form).append($(input));
			var input1 = $("<input>").attr("type", "hidden").attr("name", "quiz_id").val($(this).data('delete_survey'));
			$(form).append($(input1));
				form.appendTo(document.body)
			$(form).submit();
		})
	</script>
