<script>
	app.controller('demo', ['$scope','$filter','$http', 'fileUpload', function($scope,$filter,$http, fileUpload){
 	  
	  $scope.module = <?php echo $data['entry_json']; ?>;
	}]);
	
	app.filter('removeCharacter', function () {
	    return function (text, filter) {
	        var str = text.replace('>', '');
	        str = str.replace('<', '');
	        str = str.replace('=', '');
	        filter.value = str;
	        return str;
	    };
	});
	</script>

	<form method="POST" action="<?php echo $this->base_url('online-courses/'.$data['course_info']['id'].'/modules/'.$data['module']['id']); ?>" class="inner_container" ng-controller="demo">

			<div class="breadcrumbs">
				<a href="<?php echo $this->base_url('online-courses/'.$data['course_info']['id']); ?>"><i class="fa fa-chevron-left" aria-hidden="true"></i> <?php echo $data['course_info']['title']; ?></a>
			</div>
			<div class="top_section">
				<div class="buttons_wrap">
					<?php if(!$data['module']['id']){ ?>
						<input type="submit" name="add_module" value="Add module" class="btn btn-sm btn-primary">	
					<?php }else{ ?>
						<input type="submit" name="save_module" value="Save" class="btn btn-sm btn-primary">	
					<?php } ?>
				</div>
				<h1>
					<?php if($data['module']['id']){ ?>
					<span>{{ module.title }}</span>
					<?php }else{ ?>
					<span ng-hide="module.title">New module</span>
					<span ng-show="module.title!=''">{{ module.title }}</span>
					<?php } ?>
				</h1>

			</div>

			<?php if($_GET['added_entry']){ ?>
				<div class="alert alert-success main_alert" role="alert">
					<span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
					The module was added successfully. <a href="<?php echo $this->base_url("online_courses/".$data['course_info']['id']."/modules/new/"); ?>">Add another module</a>
				</div>
			<?php }?>


			<div class="row">
				<div class="col-sm-8">
					<div class="panel panel-default no-padding ">
						<div class="panel-body">
							<label>Title</label>
							<input class="form-control ssb" dir="<?php echo $data['course_info']['language_direction']; ?>" autofocus="" ng-model="module.title" required="" name="title" type="text" value="<?php echo $data['module']['title']; ?>">

							<label>Module Slug</label>
							<input class="form-control ssb" dir="<?php echo $data['course_info']['language_direction']; ?>" autofocus="" ng-model="module.title" required="" name="title" type="text" value="<?php echo $data['module']['module_slug']; ?>">

							<label>Description</label>
							<textarea class="form-control ssb tinymce" dir="<?php echo $data['course_info']['language_direction']; ?>" name="description" type="text" style="height: 350px;"><?php echo $data['module']['description']; ?> </textarea>
							<script type="text/javascript">
								editor = CKEDITOR.config.contentsLangDirection = '<?php echo $data['course_info']['language_direction']; ?>';
	              editor = CKEDITOR.replace('description',{
                      height: '200px',
                      versionCheck: false
                  });
	            </script>
						</div>
					</div>
				</div>

				<div class="col-sm-4">
					<div class="panel panel-default no-padding grey">
						<div class="panel-body">
							<h2 class="next-heading">Visibility</h2>
							<div class="radio">
						    <label>
						      <input type="radio" name="visibility" value="publish" checked="checked"> Publish
						    </label>
						  </div>
						  <div class="radio">
						    <label>
						      <input type="radio" name="visibility" value="publish" checked="checked"> Hidden
						    </label>
						  </div>
						</div>
					</div>
				</div>
			</div>
		

		<div class="bottom_buttons">
			<div class="buttons_wrap">
				<?php if(!$data['module']['id']){ ?>
					<input type="submit" name="add_module" value="Add module" class="btn btn-sm btn-primary">
				<?php }else{ ?>
					<input type="submit" name="save_module" value="Save" class="btn btn-sm btn-primary">
				<?php } ?>
			</div>
		</div>
		</div>
	</form>
    


		<!-- attach  questionaire modal -->
		<?php 
			$OL = new OnlineLearning;
			$existing_questionaires= $OL->existing_questionaires();

		?>
		<div class="modal fade" id="attach_survey_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<form method="POST" action="<?php echo $this->current_url(); ?>">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel">Attach Survey</h4>
				</div>
				<div class="modal-body text-center">
					<select  class="form-control"  name="quiz_id">
						<?php foreach ($existing_questionaires as $key => $value) { ?>
						<option value="<?php echo $value['quiz_id'] ?>"><?php echo $value['title'] ?></option>
						<?php } ?>
					</select>
				</div>
				<div class="modal-footer">
					<!-- <input type="hidden" name="attach_survey" class="module_to_delete" value=""> -->
					<input type="hidden" name="action" value="attach_survey">
					<input type="submit" class="btn btn-success" value="Submit">
				</div>
			</form>
			</div>
		</div>
		</div>
   

   <script type="text/javascript">
   	 $(".attach_survey").click(function(){
	  	 console.log($(this))
	  	 $("#attach_survey_modal").modal("show");
	  })

	 $('.delete_survey').click(function(e){
	       e.preventDefault()
	  	   var form = $(document.createElement('form'));
		   //$(form).attr("action", "reserves.php");
		   $(form).attr("method", "POST");
		   var input = $("<input>").attr("type", "hidden").attr("name", "action").val("delete_survey");
		   $(form).append($(input));
		   var input1 = $("<input>").attr("type", "hidden").attr("name", "quiz_id").val($(this).data('delete_survey'));
		   $(form).append($(input1));
		   form.appendTo(document.body)
		   $(form).submit();
	  })
   </script>
