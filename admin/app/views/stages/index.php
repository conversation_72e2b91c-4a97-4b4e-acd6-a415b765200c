<?php 
	$filter_type = $data['filter_types'];


	$default_filters_list = array();
	foreach ($filter_type as $fff) {
		$default_filters_list[] = ucwords($fff['title']);
	}

	$default_filters_list = array_values($default_filters_list);

	$filter_types_json = json_encode($filter_type);

	$filter_type_small = array();
	foreach($filter_type as $ff){
		if(!$ff['extra']){
			$ff['options'] = '';
			$filter_type_small[] = $ff;
		}
	}


	$filter_type_small = json_encode($filter_type_small);


	$filters = $data['filter']['description'];
	if(!$filters){ $filters =array();}
	$filters_json = json_encode($filters);
?>
<script>
	app.controller('demo', ['$scope','$filter','$http','$compile', 'fileUpload', function($scope,$filter,$http,$compile, fileUpload){

	  $scope.dimensions = <?php echo json_encode($data['dimensions']); ?>;
	  $scope.filters = <?php echo $filters_json ?>;
	  $scope.main_filter_types = <?php if($data['filter_types']){ echo json_encode($data['filter_types']); }else{ echo "[]";} ?>;
	  $scope.filter_types = <?php if($filter_types_json){ echo $filter_types_json;}else{ echo "[]";} ?>;
	  $scope.filter_types_small = <?php if($filter_type_small){ echo $filter_type_small;}else{ echo "[]";} ?>;
	  $scope.filter = <?php if($this->filter){ echo json_encode($this->filter);}else{ echo "[]";} ?>;
	  $scope.default_filters_list = <?php if($default_filters_list){ echo json_encode($default_filters_list);}else{ echo "[]";} ?>;
	  $scope.filter_search_keyword = "";
	  $scope.total_entries = <?php echo $paginator->total ?: 0; ?>;
	 	

	  /* Load Filters
  	----------------------------------------------*/
 		filters_args = {
		  'search': '<?php echo $_REQUEST['search']; ?>',
		  'value': '<?php echo $_REQUEST['search']; ?>',
		  'filter': $scope.filter,
		 <?php if($data['hide_filters']){ ?> 'hide_filters':1 <?php } ?>
		};	
 		var filters_wrap_html = filter_html(filters_args);

 		angular.element(document.getElementById('filters_wrap')).append($compile(filters_wrap_html)($scope));
	  

 		/* View Filters
  	----------------------------------------------*/
	  $scope.view_filters = function view_filters() {
	  	$scope.create_temp_filters();
	  	if(typeof $scope.filters !== 'undefined') {
		  	if($scope.filters.length<1){
		  		$scope.add_filter();
		  	}
	  	}else{
	  		$scope.add_filter();
	  	}
	  	$("#filters_modal").modal("show");
	  }

	  $scope.delete_filter = function delete_filter(index) {
	  	$scope.filters.splice(index, 1); 
	  	$scope.create_temp_filters();
	  	$scope.apply_filters();
  		return false; 
	  }

	  $scope.delete_temp_filter = function delete_temp_filter(index) {
	  	$scope.edit_filters.splice(index, 1); 
  		return false; 
	  }

	  
	  $scope.get_field_options = function get_field_options(filter) {

	  	if(filter.type=="checklist_id"){
	  		$http({
			    url: "ajax/students.php",
			    method: "POST",
			    data: {action:"get_checklist_stage",route_id:filter.option}
				}).success(function(data, status, headers, config) {	
					
					angular.forEach($scope.main_filter_types, function(type){
		  			if(type.id=="application_stage"){
		  				console.log(type.id+"=="+"application_stage");
		  				type.option = "";
		  				type.options = data;
		  			}
				  });


				  angular.forEach($scope.edit_filters, function(fi){
		  			if(fi.type=="application_stage"){
		  				fi.option = "";
		  				fi.options = data;
		  			}
				  });
				}).error(function(data, status, headers, config) {
			    
				});
	  	}
	  }


	  $scope.filter_type_info = function filter_type_info(filter_type) {
	  	ftype = "";
	  	angular.forEach($scope.main_filter_types, function(type){
  			if(type.id==filter_type){
  				ftype = type;
  			}
		  });

		  if(!ftype){
		  	angular.forEach($scope.filter_types, function(type){
		  		console.log(type.id+"---"+filter_type);
	  			if(type.id==filter_type){
	  				ftype = type;
	  			}
			  });
		  }
		  return ftype;
	  }

	  $scope.option_title = function option_title(option,filter) {
	  	option_name = "";
	  	if(filter.options){
		  	angular.forEach(filter.options, function(option_info){
	  			if(option_info.value==option){
	  				option_name = option_info.label;
	  			}
			  });
	  	}

		  return option_name;
	  }

	  $scope.filter_title = function filter_title(filter) {
	  	type_info = $scope.filter_type_info(filter.type);
		  return type_info.title;
	  }

	  $scope.show_filter_dropdown = function show_filter_dropdown(filterIndex) {
			$(".filter_select#"+filterIndex+" ul").toggle();
			$(".filter_select#"+filterIndex+" ul input").focus();
		}

		$scope.apply_selected_filter = function apply_selected_filter(filterIndex,itemChoosen) {
			
			$scope.edit_filters[filterIndex].type = itemChoosen.id;
			$scope.fix_filter_options($scope.edit_filters[filterIndex]);
			$(".filter_select#"+filterIndex+" ul").hide();
			$scope.edit_filters[filterIndex].filter_search_keyword = "";
			$scope.$apply();
		}

	  $scope.fix_filter_options = function fix_filter_options(filter) {	
	  	type_info = $scope.filter_type_info(filter.type);
	  	filter.value = "";
	  	filter.option = "";
	  	filter.sql = type_info.sql;

	  	if(typeof type_info.field_type !== 'undefined'){
	  		filter.field_type = type_info.field_type
	  	}else{
	  		delete filter.field_type;
	  	}
	  	if(typeof type_info.extra !== 'undefined') {
		  	filter.extra = type_info.extra;
		  	filter.operator_type = 'Exactly matching' ;
			}else{
				delete filter.extra;
			}

			if(typeof type_info.options !== 'undefined') {
		  	filter.options = type_info.options;
			}else{
				delete filter.options;
			}

			if(typeof type_info.operators !== 'undefined') {
		  	filter.operators = type_info.operators;
			}else{
				delete filter.operators;
			}

			//Get dynamic listing
			if(filter.sql){

				$scope.form_data = {action: 'get_dynamic_field_values', sql: filter.sql }
		  	$("#SOMETHING").html("Loading...");

				$http({
			    url: "ajax/students.php",
			    method: "POST",
			    data: $scope.form_data
				}).success(function(data, status, headers, config) {
					filter.options = data;
				}).error(function(data, status, headers, config) {
			    
				});

			}

	  }


	  $scope.validate_operator_value = function validate_operator_value(filter) {
	  	if(filter.operator_type!='Is empty'){
	  		filter.value = "";
	  	}
	  }

	  $scope.add_filter = function add_filter() {
	  	var new_field = {type: '',title:'',status:'',join:'and'};
			$scope.edit_filters.push(new_field);
	  }

	  
	  $scope.create_temp_filters = function create_temp_filters() {
	  	$scope.edit_filters = angular.copy($scope.filters);
	  	fcount = 1;
	  	angular.forEach($scope.edit_filters, function(filter){

	  		console.log("||||");
		  	console.log(filter);


		  	if(!filter.join){
		  		filter.join = 'and';
		  	}

		  	if(fcount==1){
		  		filter.join = 'and';
		  	}
		  	fcount++;
		  });
	  }

	  $scope.apply_filters = function apply_filters() {
	  	
	  	pop_message('Loading...',false);

	  	angular.copy($scope.edit_filters, $scope.filters);
  		angular.forEach($scope.filters, function(filter){
		  	filter.status = 'active';
		  	//filter.options = '';
		  	if(filter.field_type === 'daterange'){
		  		filter.value = "between "+ filter.from + " AND " + filter.to ;
		  	}
		  	
		  	filter_title = "";
		  	angular.forEach($scope.dimensions[0]['All Fields'], function(option_info){
		  		console.log("BBBBBB");
	  			console.log(filter.type+"=="+option_info.id);
		  		if(filter.type==option_info.id){
		  			filter_title = option_info.title;
		  		}
		  	});
		  	filter.title = filter_title;
		  	console.log("AAAAA");
	  		console.log(filter);
		  });

  		// console.log("DDDD");
	  	// console.log(filter);
	  	// console.log($scope.dimensions[0]['All Fields']);
	  	// angular.forEach(dimensions[0]['All Fields'], function(option_info){
	  	// 	if(filter.id==option){
	  	// 		option_name = option_info.label;
	  	// 	}
	  	// });

		  
	  	

  		// return false;

		  $("#filters_modal .btn-primary").html("Loading...").addClass("disabled");

		  // console.log($scope.filters);
		  // return false;
		  // return false;
		  var filters_json = JSON.stringify($scope.filters);
		  
		  $("#main_search_form_filters").val(filters_json);
		  $("#main_search_form_action").val("update_filter");
		  $("#main_search_form").submit();
	  }

	  	
	  $scope.apply_fields = function apply_fields(dimension) {
	  	$scope.form_data = {fields: $scope.dimensions, action: 'set_session_student_fields', page:'<?php echo $data['filter_page_name'];?>', filter_id:'<?php echo $this->filter['id'];?>' }
	  	$("#dimensions_dropdown .bottom_apply_button .btn").html("Loading...");

			$http({
		    url: "ajax/students.php",
		    method: "POST",
		    data: $scope.form_data
			}).success(function(data, status, headers, config) {
				window.location.replace(location.href);
			}).error(function(data, status, headers, config) {
		    
			});
	  }




		

	}]);

	app.filter('removeCharacter', function () {
	    return function (text, filter) {
	        var str = text.replace('>', '');
	        str = str.replace('<', '');
	        str = str.replace('=', '');
	        filter.value = str;
	        return str;
	    };
	});
	$(document).ready(function(){

		$('#dimensions_dropdown').click(function(e) {
		    e.stopPropagation();
		});

		$('#export_btn').click(function(e) {
		    $(this).html("Loading...").addClass("disabled");
		});
		$('#export_to_ukvi_btn').click(function(e) {
			$(this).html("Loading...").addClass("disabled");
		});

		// $('body').on('click', '.dynamic_download', function(e){
		// 	///e.preventDefault();
		// 	var $scope = angular.element($('[ng-controller="demo"]')).scope();
		// 	///$scope.download_dynamic_file($(this).closest('a').attr('href'));
		// });

		jQuery(".main_table .table_wrap").on('scroll', function() {
			that = jQuery(this);
		  if(that.scrollLeft() >= 1) {
		  	$("#applicants_table .shadow").show();
			}else{
				$("#applicants_table .shadow").hide();
			}
		});

		var frozen_fields_width = $("#applicants_table .pics").width();
		$("#applicants_table .main_table").css({'margin-left':frozen_fields_width+"px"});
		$("#applicants_table .shadow").css({'left':frozen_fields_width+"px"});


		//$("#filters_modal").modal("show");		
        $(document).on("click","a[title='Copy Link']", function (e) {
        	e.preventDefault();
		    var hrefval = $( this).attr("href");
		 	var copiedHref = $('<input>').val(hrefval).appendTo('body').select();
		 	document.execCommand('copy');
		});

	});
</script>
<div ng-controller="demo" class="inner_container">
	<div class="top_section">
		<h1>
			By Application Status
		</h1>
	</div>

	<?php if($data['filtering_tools']){ require(ABSPATH."app/views/applicants/filter_tools_bar.php") ;}?>
	<div class="row programmes_boxes">
		<?php 
			foreach ($data['results'] as $entry){
				$link = $this->base_url("stages/applicants/".$entry["order"]);
				if($data['cohort']){
					$coh = "?cohort=".$data['cohort'];
					$link = $link. $coh;
				}
		?>
		<div class="col-xs-4 tile_box" id="box_<?php echo $entry['id'] ?>">
			<div class="box">
				<div class="title">
					<input type="checkbox" name="selected[]" class="tile_selected_box" value="<?php echo $entry['id'] ?>">
					<div class="btn-group btn-group-sm pull-right" style="margin-top: -4px; ">
					  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
					    <span class="glyphicon glyphicon-cog" aria-hidden="true"></span>
					  </button>
					  <ul class="dropdown-menu">
					    <li><a href="<?php echo $link ?>" id="<?php echo $entry["id"]; ?>">View Records</a></li>
					  </ul>
					</div>
					<a href="<?php echo $link; ?>" class="main_title"><?php echo $entry["stage_name"]; ?></a>
				</div>
				<div class="content" style="min-height: 150px;">
					<div class="row" style="min-height: 50px;">
						<div class="col-sm-12"><?php print $entry['description']?: $entry['stage_name']; ?></div>
					</div>
					<hr>
					<div class="row">
						<div class="col-sm-6"><strong>Total</strong></div>
						<div class="col-sm-6 pull-right text-right"><?php print $entry['count'] ?></div>
					</div>
				</div>
				
			</div>
		</div>
		<?php } ?>

		<?php
		// only show to tech admins
		if ($_SESSION['ulevel']==9){		
		?>
		<div class="col-xs-4">
			<a class="box add_box thickbox" href="<?php echo $this->engine_url('controller.php?width=850&height=600&pick_page=161&ref=&jqmRefresh=true'); ?>">
				<div class="title">
					Create intake
				</div>
				<div class="content text-center" style="min-height: 150px; padding-top: 5px;">
					<span class="glyphicon glyphicon-plus  add_icon" aria-hidden="true" style="padding-top: 0px; margin-top: 0px;"></span>
				</div>
				
			</a>
		</div>
		<?php } //end add element?>
	<!-- Modal -->
<?php include(ABSPATH."app/views/applicants/inc_filter_modals.php") ?>	
	</div>