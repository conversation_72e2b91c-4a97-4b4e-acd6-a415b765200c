    <!-- Modal -->
    <div id="archive_scedules" class="modal fade" role="dialog">
      <div class="modal-dialog">

        <!-- Modal content-->
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal">&times;</button>
            <h4 class="modal-title">Delete Lecture Schedules </h4>
          </div>
          <div class="modal-body">
            <p v-if="archive_scope=='all'">Are you sure you want to delete <span style="color:red;">ALL</span> lecture schedules for this schedule?</p>
            <p v-if="typeof archive_scope.clean_title!='undefined'">Are you sure you want to delete <span style="color:red;">THIS</span> lecture schedule for {{archive_scope.clean_title}}?</p>
            <p v-if ="typeof archive_scope.day_label!='undefined'">Are you sure you want to delete <span style="color:red;">ALL</span> lecture schedules for {{archive_scope.day_label}}?</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-danger" v-on:click="post_to_backend('archive_lecture_schedules',archive_scope,true)">Delete</button>
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div>

      </div>
    </div>



     <div id="confirm_action" class="modal fade" role="dialog">
      <div class="modal-dialog">

        <!-- Modal content-->
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal">&times;</button>
            <h4 v-if="'reject_student'==confirm_entity_url">Reject Application?</h4>
            <h4 v-else-if="'remove_desired_subject'==confirm_entity_url">Remove Subject</h4>
            <h4 v-else class="modal-title">Delete {{confirm_entity}}</h4>
          </div>
          <div class="modal-body">
            <p v-if="'reject_student'==confirm_entity_url">Are you sure you want to reject {{confirm_entity_args.name}} {{confirm_entity_args.surname}}?</p>
            <p v-else-if="'remove_desired_subject'==confirm_entity_url">Are you sure you want to remove {{confirm_entity_args.subj.subject_name}} from {{confirm_entity_args.h_student.name}} {{confirm_entity_args.h_student.surname}} list of desired subjects?</p>
            <p v-else-if="'archive_classes'==confirm_entity_url">Are you sure you want to remove <span style="color:red;">{{confirm_entity}}</span> <span>{{confirm_entity_name}}</span> fom  list of classes? This will move all applications assigned to this class back to the holding area!</p>
            <p v-else >Are you sure you want to delete <span style="color:red;">{{confirm_entity}}</span> <span>{{confirm_entity_name}}</span>?</p>
            <p v-if="''!=conform_entity_extra_warning"><span style="color:red;">{{conform_entity_extra_warning}}</span> </p>          
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-danger" v-on:click="post_to_backend(confirm_entity_url,confirm_entity_args,true)">
                <span v-if="'reject_student'==confirm_entity_url">Reject</span>
                <span v-else-if="'remove_desired_subject'==confirm_entity_url">Remove</span>
                <span v-else>Delete</span>
            </button>
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div>

      </div>
    </div>

    

    <!-- Modal -->
<div class="modal fade" id="add_lecture_modal" tabindex="-1" role="dialog" 
     aria-labelledby="myModalLabel" aria-hidden="true" >
    <div class="modal-dialog">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <button type="button" class="close" 
                   data-dismiss="modal">
                       <span aria-hidden="true">&times;</span>
                       <span class="sr-only">Close</span>
                </button>
                <h4 class="modal-title" id="myModalLabel">
                 <span v-if="typeof quick_add_lecture_details.lecture_schedule_id!='undefined'">EDIT</span> <span v-else>Add</span> lecture
                 </h4>
            </div>
            
            <!-- Modal Body -->
            <div class="modal-body">
                
                <form role="form">


        <input v-if="typeof quick_add_lecture_details.lecture_schedule_id!='undefined'" type="text" hidden  v-model="quick_add_lecture_details.lecture_schedule_id" id="lecture_schedule_id" name="lecture_schedule_id" placeholder="lecture_schedule_id">



        <div class="form-group">
            <label for="block_label">Day</label>
            <select class="form-control" v-model="quick_add_lecture_details.day_id">
              <option v-for="option in demoExample.schedular_days" :value="option.day_id">
                {{ option.day_label }}
              </option>
            </select>
            
        </div>


        <div class="form-group">
            <label for="block_label">Block</label>
            <select class="form-control" v-model="quick_add_lecture_details.block_id">
              <option v-for="option in demoExample.blocks" :value="option.block_id">
                {{ option.block_label }}
              </option>
            </select>
            
        </div>


         <div class="form-group">
            <label for="day_label">Class</label>
            <input type="text" class="form-control" v-model="quick_add_lecture_details.class_name" id="class_name" name="class_name" placeholder="Enter Class Name">
        </div>

        <div class="form-group">
            <label for="block_label">Subject</label>
            <select class="form-control" v-model="quick_add_lecture_details.subject_id">
              <option v-for="option in demoExample.subjects" :value="option.subject_id">
                {{ option.subject_name }}
              </option>
            </select>
            
        </div>


        <div class="form-group">
            <label for="day_label">Lecturer Name </label>
            <input type="text" class="form-control" v-model="quick_add_lecture_details.lecturer_name" id="lecturer_name" name="lecturer_name" placeholder="Enter Lecturer Name">
        </div>


        <div class="form-group">
            <label for="day_label">Room</label>
            <input type="text" class="form-control" v-model="quick_add_lecture_details.room_name" id="room_name" name="room_name" placeholder="Enter Room Label">
        </div>


        <div class="form-group">
            <label for="day_label">Max Enrollment</label>
            <input type="text" class="form-control" v-model="quick_add_lecture_details.max_enrollment" id="max_enrollment" name="max_enrollment" placeholder="Enter max enrollment">
        </div>

          </form>
                
                
            </div>
            
            <!-- Modal Footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-default"
                        data-dismiss="modal">
                            Close
                </button>
                <button  v-on:click="post_to_backend('add_new_lecture_with_details',quick_add_lecture_details)" type="button" class="btn btn-primary" :disabled="posting_tobackend">
                    Save changes
                </button>
            </div>
        </div>
    </div>
</div>

  <!-- Modal -->
  <div class="modal fade" id="group_size_update" tabindex="-1" role="dialog"  aria-labelledby="myModalLabel" aria-hidden="true"  data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <!-- Modal Header -->
                <div class="modal-header">
                    <button type="button" class="close" 
                      data-dismiss="modal">
                          <span aria-hidden="true">&times;</span>
                          <span class="sr-only">Close</span>
                    </button>
                    <h4 class="modal-title" id="myModalLabel">
                    <span v-if="typeof quick_add_lecture_details.lecture_schedule_id!='undefined'">EDIT</span> <span v-else>Add</span> lecture
                    </h4>
                </div>
                
                <!-- Modal Body -->
                <div class="modal-body">
                  <div class="form-group">
                      <label for="day_label">Enrollment</label>
                      <input type="text" class="form-control" v-model="editing_sc_class.max_enrollment" id="max_enrollment" name="max_enrollment" placeholder="Enter max enrollment">
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                  <button type="button" class="btn btn-primary sve_bttin" :disabled="posting_tobackend"  v-on:click="post_to_backend('save_classes',editing_sc_class)" >Save changes</button>
                </div>
              </div>
          </div>
    
</div>



<div class="modal fade" id="rules_update" tabindex="-1" role="dialog"  aria-labelledby="myModalLabel" aria-hidden="true"  data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <!-- Modal Header -->
                <div class="modal-header">
                    <button type="button" class="close" 
                      data-dismiss="modal">
                          <span aria-hidden="true">&times;</span>
                          <span class="sr-only">Close</span>
                    </button>
                    <h4 class="modal-title" id="myModalLabel">
                    Copy rules from another scedule
                    </h4>
                </div>
                
                <!-- Modal Body -->
                <div class="modal-body">
                  <div class="form-group">
                      <label for="day_label">Schedule to copy from</label>
                      <select v-model="copy_ing_rules.schedule_from">
                        <?php foreach ($data['schedules'] as $key => $value) {?>
                        <option value="<?php echo $value['schedule_id'] ?>"><?php echo $value['schedule_label'] ?></option>
                        <?php }?>
                      </select>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                  <button type="button" class="btn btn-primary sve_bttin" :disabled="posting_tobackend"  v-on:click="post_to_backend('copy_rules',copy_ing_rules)" >Copy Rules</button>
                </div>
              </div>
          </div>
    
</div>