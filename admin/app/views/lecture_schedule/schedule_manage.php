<div v-if="schedule_manage" style="margin-top:90px;">
    
    <h2 style="font-size:16px;margin-bottom: 10px;"> <span v-if="typeof editing_schedule.schedule_id == 'undefined'">Add</span> <span v-else>Edit</span>  schedule</h2>
    <input type="text" hidden  v-model="editing_schedule.schedule_id" id="schedule_id" name="schedule_id">
    <div class="form-group">
        <label for="day_label">Sceduler Label</label>
        <input type="text" class="form-control" v-model="editing_schedule.schedule_label" id="schedule_label" name="schedule_label" placeholder="Enter schedule Label">
    </div>

    <div class="form-group">
        <label for="day_label">Set as default scheduler</label>
        <input type="checkbox" class="form-control" v-model="default_for_ug" id="default_for_ug" name="default_for_ug" placeholder="" style="width: 300px;">
    </div> 


   <div v-if="typeof editing_schedule.schedule_id == 'undefined'" class="form-group">
        <label for="day_label">Copy rules from</label>
        <select v-model='editing_schedule.rules_source'  class="form-control" data-size="5" data-live-search="true" style="width: 300px;">
            <!-- <option value="add">Add  New </option> -->
            <?php foreach ($data['schedules'] as $key => $value) {?>
            <option value="<?php echo $value['schedule_id'] ?>"><?php echo $value['schedule_label'] ?></option>
            <?php }?>
        </select>
     </div> 
    <button  class="btn btn-primary sve_bttin" :disabled="posting_tobackend"  v-on:click="post_to_backend('save_schedule',editing_schedule)">Submit</button>
    <button class="btn btn-default" style="margin-left:10px;"   v-on:click="history_back()">Cancel</button>

</div>
