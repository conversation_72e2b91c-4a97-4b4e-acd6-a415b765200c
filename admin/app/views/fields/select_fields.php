<script src='https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.12.1/js/bootstrap-select.min.js'></script>
<link href='<?php echo $admin_url."assets/css/"; ?>bootstrap-select.min.css' rel='stylesheet' />
<style>
	        .dual-list .list-group {
            margin-top: 8px;
        }

        .list-left li, .list-right li {
            cursor: pointer;
        }

        .list-arrows {
            padding-top: 100px;
        }

            .list-arrows button {
                margin-bottom: 20px;
            }
</style>
<script>
	app.controller('demo', ['$scope','$filter','$http','$window', function($scope,$filter,$http,$window){
	 $scope.all_fields = <?php echo json_encode($data['form_fields']); ?>;
	 $scope.my_options = <?php echo json_encode($data['selected']); ?>;
	 $scope.form_fields = [];
		 angular.forEach($scope.all_fields, function(section){   
         var Okeys = Object.keys(section);   
	      angular.forEach(section, function(fields){
	  		angular.forEach(fields, function(field){
                field.origin = Okeys[0];
                already_added = $scope.form_fields.map(function(e) { return e.title; }).indexOf(field.title);
                if(field.title && field.title.indexOf("Ready to use") == -1 && field.title !== "new" && field.title !=="" )//&& already_added == -1)
	  			  $scope.form_fields.push(field);	
	      	});
	      });
	    });
		angular.forEach($scope.my_options, function(option){
			pos = $scope.form_fields.map(function(e) { return e.id; }).indexOf(option.id);
			if(pos != -1){
				$scope.form_fields.splice(pos, 1);
			}
		});
	}]);
</script>
<div ng-controller="demo" class="ng-cloak" id="main_controller" >
		<div class="breadcrumbs">
			<a href="<?php echo $this->base_url('settings/'); ?>"><i class="fa fa-chevron-left" aria-hidden="true"></i> Settings</a>
		</div>

		<div class="top_section">
			<div class="buttons_wrap">
				<a class="btn btn-default thickbox" href="<?php echo engine_url; ?>/controller.php?pg=3&add=1&width=700&height=550&jqmRefresh=true">Missing a field? Tell us</a>
			</div>
			<h1>
				Manage Select Fields when viewing Application profile data
			</h1>
		</div>

			<ul class="next-tab_list">
				<li>
					<a class="<?php if(!$data['type_id']){echo "active";}?>" href="#">
						Customise and reorder your 'Select fields' dropdown for analysing application profile related data
					</a>
				</li>		
			</ul>

			<div class="stnrd_box">

	<div class="row">

        <div class="dual-list list-left col-md-5">
            <div class="well" style="min-height: 525px;">
                <div class="row">
                	<h4 class="text-center">Inactive Fields</h4>
                    <div class="col-md-10">
                        <div class="input-group">
                            <span class="input-group-addon"><span class="glyphicon glyphicon-search"></span></span>
                            <input type="text" name="SearchDualList" class="form-control" placeholder="search" />
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="btn-group">
                            <a class="btn btn-default selector" title="select all"><i class="glyphicon glyphicon-unchecked"></i></a>
                        </div>
                    </div>
                </div>
                <ul class="list-group" style="max-height: 350px; overflow-y:scroll;">
                    <li class="list-group-item" ng-repeat="field in form_fields" data-id="{{field.id}}" data-origin="{{field.origin}}" data-title="{{field.title}}" data-type="{{field.type}}">
                        <span class="pull-right x-small">{{field.origin}}</span> {{field.title}}</li>
                </ul>
            </div>
        </div>

        <div class="list-arrows col-md-1 text-center">
            <button class="btn btn-default btn-sm move-left">
                <span class="glyphicon glyphicon-chevron-left"></span>
            </button>

            <button class="btn btn-default btn-sm move-right">
                <span class="glyphicon glyphicon-chevron-right"></span>
            </button>
        </div>

        <div class="dual-list list-right col-md-5">
            <div class="well" style="min-height: 525px;">
                <div class="row">
                	<h4 class="text-center">Active Fields</h4>
                    <div class="col-md-2">
                        <div class="btn-group">
                            <a class="btn btn-default selector" title="select all"><i class="glyphicon glyphicon-unchecked"></i></a>
                        </div>
                    </div>
                    <div class="col-md-10">
                        <div class="input-group">
                            <input type="text" name="SearchDualList" class="form-control" placeholder="search" />
                            <span class="input-group-addon"><span class="glyphicon glyphicon-search"></span></span>
                        </div>
                    </div>
                </div>
                <ul class="list-group" id="active_fields" style="max-height: 350px; overflow-y:scroll;">
                    <li class="list-group-item" ng-repeat="field in my_options" data-id="{{field.id}}" data-origin="{{field.origin}}" data-title="{{field.title}}" data-type="{{field.type}}"><span class="pull-right x-small">{{field.origin}}</span> {{field.title}}</li>
                </ul>
				<div class="row">
					<div class="col-sm-6 text-right"><button id="up2" class="btn"><span class="glyphicon glyphicon-arrow-up"></span></button></div>
					<div class="col-sm-6 text-left"><button id="down2" class="btn"><span class="glyphicon glyphicon-arrow-down"></span></button></div>
				</div>
            </div>
        </div>
		<div class="row">
			<div class="col-sm-12">
				<button style="margin-top: 20px; margin-right: 120px;" id="save_changes" class="btn btn-success pull-right">Save Changes</button>
			</div>
		</div>
	</div>
	</div>
</div>

</div>
<script>
$(function () {

    $('body').on('click', '.list-group .list-group-item', function () {
        $(this).toggleClass('active');
    });
    $('.list-arrows button').click(function () {
        var $button = $(this), actives = '';
        if ($button.hasClass('move-left')) {
            actives = $('.list-right ul li.active');
            actives.clone().appendTo('.list-left ul');
            actives.remove();
        } else if ($button.hasClass('move-right')) {
            actives = $('.list-left ul li.active');
            actives.clone().appendTo('.list-right ul');
            actives.remove();
        }
    });
    $('.dual-list .selector').click(function () {
        var $checkBox = $(this);
        if (!$checkBox.hasClass('selected')) {
            $checkBox.addClass('selected').closest('.well').find('ul li:not(.active)').addClass('active');
            $checkBox.children('i').removeClass('glyphicon-unchecked').addClass('glyphicon-check');
        } else {
            $checkBox.removeClass('selected').closest('.well').find('ul li.active').removeClass('active');
            $checkBox.children('i').removeClass('glyphicon-check').addClass('glyphicon-unchecked');
        }
    });
    $('[name="SearchDualList"]').keyup(function (e) {
        var code = e.keyCode || e.which;
        if (code == '9') return;
        if (code == '27') $(this).val(null);
        var $rows = $(this).closest('.dual-list').find('.list-group li');
        var val = $.trim($(this).val()).replace(/ +/g, ' ').toLowerCase();
        $rows.show().filter(function () {
            var text = $(this).text().replace(/\s+/g, ' ').toLowerCase();
            return !~text.indexOf(val);
        }).hide();
    });

    $("#save_changes").click(function(){
	  	pop_message('Saving Changes...');
	  	var $data = [];
	  	var $location = "<?php echo $this->current_url(); ?>";
	  	$("#active_fields > li").each(function (){
	  		var dt = $(this).data();
	  		var d = {
	  			id: dt.id,
	  			title: dt.title,
	  			type: dt.type,
                origin: dt.origin
	  		};
	  		$data.push(d);
	  	});
	  	$.ajax({
	  		url:$location,
	  		type:'post',
	  		dataType:'json',
	  		data:{"active": $data },
	  		success:function(resp){
	  			window.location.replace($location);
	  		}
  		});
		});

	let $active_fields = $('#active_fields');

	$('#up2').click(function () {
		$("#active_fields li.active").each(function(){
			var $prev = $(this).prev();
			while($prev.hasClass('active')){
				$prev = $prev.prev();
			}
			if(! $prev.hasClass('active'))
				$(this).insertBefore($prev);
		});
	});
	  
	$('#down2').click(function () {
		$("#active_fields li.active").each(function(){
			var $next = $(this).next();
			while($next.hasClass('active')){
				$next = $next.next();
			}
			if(! $next.hasClass('active'))
				$(this).insertAfter($next);
		});
	});

});
</script>

