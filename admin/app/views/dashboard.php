<div class="row">
	
	<div class="col-sm-12">
		<div class="dash_bkg">
			<div class="dash_wrap">

				<div class="dash_welcome">
					<h1><?php
				    /* This sets the $time variable to the current hour in the 24 hour clock format */
				    $time = date("H");
				    /* Set the $timezone variable to become the current timezone */
				    $timezone = date("e");
				    /* If the time is less than 1200 hours, show good morning */
				    if ($time < "12") {
				        echo "Good morning";
				    } else
				    /* If the time is grater than or equal to 1200 hours, but less than 1700 hours, so good afternoon */
				    if ($time >= "12" && $time < "17") {
				        echo "Good afternoon";
				    } else
				    /* Should the time be between or equal to 1700 and 1900 hours, show good evening */
				    if ($time >= "17" && $time < "19") {
				        echo "Good evening";
				    } else
				    /* Finally, show good night if the time is greater than or equal to 1900 hours */
				    if ($time >= "19") {
				      echo "Good evening";
				    }
				    ?>, <?php if($this->user_info['first_name'] || $user_info['last_name']){ echo $this->user_info['first_name']." ".$this->user_info['last_name'];}else{ echo "Your Name Here"; }; ?></h1>
					<p>Here's what's happening with your establishment right now.</p>
				</div>


				<div class="dash_top_stats">
					<div class="row">		
						<div class="col-sm-6">
							<a href="<?php echo $this->base_url('applicants'); ?>" class="panel panel-default">
								<span>Total Applicants</span>
								<div class="total"><?php echo $data['total_applicants']; ?></div>
								<img src="<?php echo $this->base_url(); ?>/assets/images/sales.png">
							</a>
						</div>
						<div class="col-sm-6">
							<a href="#" class="panel panel-default">
								<span>Today's visitors</span>
								<div class="total todays_visitors">-</div>
								<img src="<?php echo $this->base_url(); ?>/assets/images/visitors.png">
							</a>
						</div>
					</div>
				</div>


				<div class="panel panel-default np nof">
					<ul class="dashboard_list">
						<?php if(!$data['total_courses']){ ?>
						<li>
							<span class="circle"></span>
							<div class="text">
								You havent added any courses yet
							</div>
							<a class="btn btn-default" href="<?php echo $this->base_url("online_courses/new/"); ?>">Add a course</a>
						</li>
						<?php } ?>

						<?php if(!$data['total_plans']){ ?>
						<li>
							<span class="circle"></span>
							<div class="text">
								You havent added an access plan yet
							</div>
							<a class="btn btn-default" href="<?php echo $this->base_url("access_plans/new/"); ?>">Add a plan</a>
						</li>
						<?php } ?>

						<?php if($data['total_support_tickets']){ ?>
						<li>
							<span class="circle"></span>
							<div class="text">
								You have <strong><?php echo $data['total_support_tickets']; ?></strong> open support tickets
							</div>
							<a class="btn btn-default" href="<?php echo $this->base_url("online_courses_support/"); ?>">View open tickets</a>
						</li>
						<?php } ?>
						<li class="hide">
							<span class="circle"></span>
							<div class="text">
								<strong>2</strong> of the applicants memberships have now expired
							</div>
							<a class="btn btn-default">View Members</a>
						</li>
					</ul>
				</div>


				<div class="panel panel-default dash_content">
					<h3 class="next-heading">Last 30 days visitors stats</h3>
					<div id="curve_chart" style="width: 100%; height: 300px">
						<br><br><br><br><br>
						<p class="text-center subdued">Loading...</p>
					</div>
				</div>	

			</div>
		</div>
	</div>


	<div class="col-sm-3 hide">
			
			<div class="right_dash">
				
				

	


				<div class="card">
					<h3 class="next-heading">Recent activity</h3>
					<div class="">
						<?php 
							$events_args = array('limit'=>10);
							$recent_events = array(
								array('message'=>'Test activity 1'),
								array('message'=>'Test activity 2'),
								array('message'=>'Test activity 3'),
								array('message'=>'Test activity 4'),
								array('message'=>'Test activity 5'),
								array('message'=>'Test activity 6'),
								array('message'=>'Test activity 7'),
							);
							foreach ($recent_events as $event) {
						?>
						<p class="activity">
							<?php echo $event["message"]; ?>
						</p>
						<?php }?>
					</div>

					<a href="activities.php" class="hide">View all recent activity</a>

				</div>
			</div>
			
		</div>
</div>


<!-- Google Anaylitics Graph -->

<?php

$svn = explode(".",$_SERVER['SERVER_NAME']);
$domain_name =$svn[1].'.'.$svn[2].'.'.$svn[3];
if($domain_name=="heiapply.com"){

	function getService()
	{
	  // Creates and returns the Analytics service object.

	  // Load the Google API PHP Client Library.
	  require_once ABSPATH.'app/libs/google-api-php-client/src/Google/autoload.php';

	  // Use the developers console and replace the values with your
	  // service account email, and relative location of your key file.
	  $service_account_email = '<EMAIL>';
	  $key_file_location = ABSPATH.'app/libs/google-api-php-client/client_secrets.p12';

	  // Create and configure a new client object.
	  $configg = new Google_Config();
		$configg->setClassConfig('Google_Cache_File', array('directory' => '/tmp/cache'));

	  $client = new Google_Client();
	  $client->setApplicationName("HelloAnalytics");
	  $analytics = new Google_Service_Analytics($client);

	  // Read the generated client_secrets.p12 key.
	  $key = file_get_contents($key_file_location);
	  $cred = new Google_Auth_AssertionCredentials(
	      $service_account_email,
	      array(Google_Service_Analytics::ANALYTICS_READONLY),
	      $key
	  );
	  $client->setAssertionCredentials($cred);
	  if($client->getAuth()->isAccessTokenExpired()) {
	    $client->getAuth()->refreshTokenWithAssertion($cred);
	  }
	  
	  return $analytics;
	}


		$analytics = getService();
	  $hostname = $_SERVER['SERVER_NAME'];
	  $hostname = "askmotors.co.uk";

		if($this->school_info['id']==1){
			$optParams = array('dimensions' => 'ga:date','sort' => 'ga:date');
		}else{
			$optParams = array('dimensions' => 'ga:date','sort' => 'ga:date','filters' => 'ga:hostname=='.$hostname);
		}

		$ga_results = $analytics->data_ga->get(
		       'ga:********',
		       '30daysAgo',
		       'today',
		       'ga:sessions',$optParams);
		$ga_results = $ga_results["rows"];

		$today_ga_results = $analytics->data_ga->get(
		       'ga:********',
		       'today',
		       'today',
		       'ga:sessions',$optParams);
		$today_ga_results = $today_ga_results["rows"];
		$today_visitors = $today_ga_results[0][1]; 
	

	?>

	<script type="text/javascript"
      src="https://www.google.com/jsapi?autoload={
        'modules':[{
          'name':'visualization',
          'version':'1',
          'packages':['corechart','line']
        }]
      }"></script>

<script type="text/javascript">

	$(document).ready(function(){
		$(".todays_visitors").html('<?php echo $today_visitors; ?>');
	});
  google.setOnLoadCallback(drawChart);

  function drawChart() {
    var data = google.visualization.arrayToDataTable([
      ['Date', 'Visitors'],
      <?php
        foreach ($ga_results as $entry) { 
          $month = date("m", strtotime($entry[0]));
          $month=$month-1;
          $java_date = date("Y,".$month.",d", strtotime($entry[0]));
          echo '[new Date('.$java_date.'),'.$entry[1].'],';
        }
      ?>
    ]);

    var options = {
      curveType: 'function',


      legend: {position: 'none'},
      pointsVisible: true,
      dataOpacity: 1,
      pointSize: 6,
      'chartArea': {'width': '90%', 'height': '80%'},
      xAxis: {
        format: 'd M',
        baselineColor: '#e1e1e1',
        gridlines: {color: '#f1f1f1',count: 3}
      },
      hAxis: {
      	baselineColor: '#e1e1e1',
        gridlines: {color: '#fff',count: 5}
      }
    };

    var chart = new google.visualization.AreaChart(document.getElementById('curve_chart'));

    chart.draw(data, options);
  }
	
</script>
<?php } ?>
