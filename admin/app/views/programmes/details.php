<script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.4/lodash.min.js"></script>

<script>
	app.controller('demo', ['$scope', '$filter', '$http', 'fileUpload', function($scope, $filter, $http, fileUpload) {

		$scope.course = <?php echo json_encode($data['entry'], JSON_NUMERIC_CHECK); ?>;
		//console.dir($scope.course);
		$scope.course_levels = <?php echo $data['levels'] ? json_encode($data['levels']) : '""'; ?>;
		$scope.course_age_groups = <?php echo $data['entry']['course_age_groups'] ? json_encode($data['entry']['course_age_groups']) : '""'; ?>;
		$scope.course_level = <?php echo $data['entry']['course_level_id'] ? $data['entry']['course_level_id'] : '""'; ?>;
		$scope.waitinglists = <?php echo $data['waitinglist'] ? json_encode($data['waitinglist']) : '[]'; ?>;
		$scope.course_evals = <?php echo $data['course_evals'] ? json_encode($data['course_evals']) : '[]'; ?>;
		$scope.all_course_groups = <?php echo $data['course_groups'] ? json_encode($data['course_groups']) : '[]'; ?>;
		$scope.sortingOrder = sortingOrder;
		$scope.reverse = false;
		$scope.filteredItems = [];
		$scope.groupedItems = [];
		$scope.pagedItems = [];
		$scope.currentPage = 0;
		$scope.search_values = {
			eval_query: null,
			query: null
		};
		$scope.itemsPerPage = {
			eval: 10,
			waitinglist: 10
		};
		///course evaluation
		$scope.eval_sortingOrder = sortingOrder;
		$scope.eval_reverse = false;
		$scope.eval_filteredItems = [];
		$scope.eval_groupedItems = [];
		$scope.eval_pagedItems = [];
		$scope.eval_currentPage = 0;

		$scope.is_short_course = function() {
			let level = Array.isArray($scope.course_levels) ? $scope.course_levels.find(entry => entry.id == $scope.course_level) : false;
			if (level) {
				return level.short_course == 'yes';
			}
			return false;
		}

		//Fix Dates to work with angular
		if ($scope.course.start_date) {
			$scope.course.start_date = new Date($scope.course.start_date);
		}

		if ($scope.course.deadline_date) {
			$scope.course.deadline_date = new Date($scope.course.deadline_date);
		}

		if ($scope.course.references_deadline_date) {
			$scope.course.references_deadline_date = new Date($scope.course.references_deadline_date);
		}

		angular.forEach($scope.course.intakes, function(item) {
			if (item.start_date) {
				item.start_date = new Date(item.start_date);
				item.start_date = moment(item.start_date).format("YYYY-MM-DD");
			}


			if (item.end_date) {
				item.end_date = new Date(item.end_date);
				item.end_date = moment(item.end_date).format("YYYY-MM-DD");
			}


			if (item.application_deadline_date) {
				item.application_deadline_date = new Date(item.application_deadline_date);
				item.application_deadline_date = moment(item.application_deadline_date).format("YYYY-MM-DD");

			}

			item.applicant_type_id = item.applicant_type.id;

		});
		$scope.school = <?php echo json_encode($this->school_info); ?>;

		$scope.locations = <?php echo json_encode($data['locations']); ?>;
		$scope.cohorts = <?php echo json_encode($data['cohorts']); ?>;
		$scope.topics = <?php echo json_encode($data['topics']); ?>;
		$scope.requirements = <?php echo json_encode($data['requirements']); ?>;
		$scope.applicant_types = <?php echo json_encode($data['applicant_types']); ?>;
		$scope.course_sessions = <?php echo json_encode($data['course_sessions']); ?>;
		$scope.course_materials = <?php echo json_encode($data['course_materials']); ?>;
		$scope.course_material_types = <?php echo json_encode($data['course_material_types']); ?>;
		$scope.course_material_statuses = <?php echo json_encode($data['course_material_statuses']); ?>;
        
        if ($scope.course.deadline_time){
            let deadlineTime = $scope.course.deadline_time;
            let t = deadlineTime.split(':');
            $scope.course.deadline_time = new Date(1970, 0, 1,t[0],t[1]);

        }
        
        if ($scope.course.reference_deadline_time){
            let reference_deadline_time = $scope.course.reference_deadline_time;
            let t = reference_deadline_time.split(':');
            $scope.course.reference_deadline_time = new Date(1970, 0, 1,t[0],t[1]);

        }

        <?php include(ABSPATH . 'app/views/online_courses/short_codes_js.php'); ?>

		if ($scope.course.excerpt) {
			$scope.show_summary_textarea = 1;
		}

		$scope.add_to_locations = function add_to_locations(location) {
			$scope.course.locations.push(location);
			$("#locations_modal").modal("hide");
			return false;
		};


		$scope.add_to_topics = function add_to_topics(topic) {
			$scope.course.topics.push(topic);
			$("#topics_modal").modal("hide");
			return false;
		};

		$scope.add_to_requirements = function add_to_requirements(requirement) {
			$scope.course.requirements.push(requirement);
			$("#requirements_modal").modal("hide");
			return false;
		};

		/** ===================================
		 * Remove a topic
	 	====================================	*/
		$scope.remove_topic = function remove_topic(Index) {
			$scope.course.topics.splice(Index, 1);
			return false;
		}

		/** ===================================
		 * Remove a requirement
	 	====================================	*/
		$scope.remove_requirement = function remove_requirement(Index) {
			$scope.course.requirements.splice(Index, 1);
			return false;
		}


		/** ===================================
		 * Remove a location
	 	====================================	*/
		$scope.remove_location = function remove_location(Index) {
			$scope.course.locations.splice(Index, 1);
			return false;
		}


		/** ===================================
		 * Remove image
	 	====================================	*/
		$scope.remove_image = function remove_image() {
			$("#preview_image").css({
				"background-image": "url()"
			});
			$("#remove_image").val("1");
			$scope.course.image_name = "";
			return false;
		}

		/** ===================================
		 * Edit Intake modal
	 	====================================	*/
		$scope.show_edit_intake_modal = function show_edit_intake_modal(intake) {
			if (intake.applicant_type_id) {
				intake.applicant_type.id = intake.applicant_type_id;
			}
			$scope.intake = intake;
			if ($scope.intake.start_date !== null) {
				$scope.intake.start_date = new Date($scope.intake.start_date);
			}
			if ($scope.intake.end_date !== null) {
				$scope.intake.end_date = new Date($scope.intake.end_date);
			}
			if ($scope.intake.application_deadline_date !== null) {
				$scope.intake.application_deadline_date = new Date($scope.intake.application_deadline_date);
			}
			$scope.intake.applicant_type_id = $scope.intake.applicant_type.id;
			$("#intakes_modal").modal("show");
		}

		/** ===================================
		 * Duplicate Intake modal
	 	====================================	*/
		$scope.show_duplicate_intake_modal = function show_duplicate_intake_modal(intakeDuplucate) {
			let intake = Object.assign({}, intakeDuplucate);
			if (intake.applicant_type_id) {
				intake.applicant_type.id = intake.applicant_type_id;
			}
			delete intake.id;
			$scope.intake = intake;
			if ($scope.intake.start_date !== null) {
				$scope.intake.start_date = new Date($scope.intake.start_date);
			}
			if ($scope.intake.end_date !== null) {
				$scope.intake.end_date = new Date($scope.intake.end_date);
			}
			if ($scope.intake.application_deadline_date !== null) {
				$scope.intake.application_deadline_date = new Date($scope.intake.application_deadline_date);
			}
			$scope.intake.applicant_type_id = $scope.intake.applicant_type.id;
			$("#intakes_modal").modal("show");
		}


		/** ===================================
		 * New Intake modal
	 	====================================	*/
		$scope.show_new_intake_modal = function show_new_intake_modal() {
			document.getElementById("intake_modal_form").reset();
			$scope.intake = {
				'course': {
					id: $scope.course.id
				},
				'publish': 'yes'
			};
			$("#intakes_modal").modal("show");
		}


		/** ===================================
		 * Show Duplicate Modal
	 	====================================	*/
		$scope.show_duplicate_modal = function show_duplicate_modal() {
			$("#duplicate_course_modal .form-control").val($scope.course.title + " DUPLICATE");
			$("#duplicate_course_modal").modal("show");
		}

		/** ===================================
		 * Save intake
	 	====================================	*/
		$scope.save_intake = function save_intake() {

			if (!$scope.intake.title) {
				$("#intake_title").focus();
				return false;
			}

			$("#intakes_modal .btn-primary").addClass("disabled").val("Loading...");

			var ajax_url = "<?php echo $this->base_url("intakes"); ?>";
			if ($scope.intake.id) {
				var ajax_url = ajax_url + "/" + $scope.intake.id;
				var ajax_method = "PUT";
			} else {
				var ajax_method = "POST";
			}

			//convert date format from iso8601 format to yyyy-mm-dd
			if ($scope.intake.end_date != null) {
				$scope.intake.end_date = moment($scope.intake.end_date).format("YYYY-MM-DD");
				//$scope.intake.end_date = new Date($scope.intake.end_date);

			}

			if ($scope.intake.start_date != null) {
				$scope.intake.start_date = moment($scope.intake.start_date).format("YYYY-MM-DD");
				//$scope.intake.start_date = new Date($scope.intake.start_date);
			}

			if ($scope.intake.application_deadline_date != null) {
				$scope.intake.application_deadline_date = moment($scope.intake.application_deadline_date).format("YYYY-MM-DD");
				//$scope.intake.application_deadline_date = new Date($scope.intake.application_deadline_date);
			}


			$http({
				url: ajax_url + ".json",
				method: ajax_method,
				data: $scope.intake
			}).success(function(data, status, headers, config) {
				$("#intakes_modal .btn-primary").removeClass("disabled").val("Save intake");
				if ($scope.intake.id) {
					pop_message("Your intake has been saved successfully");
				} else {
					$scope.course.intakes.push(data);
					pop_message("Your intake has been added successfully");
				}
				$("#intakes_modal").modal("hide");
			}).error(function(data, status, headers, config) {

			});
		}

		/** ===================================
		 * Delete Intake
		 ====================================	*/
		$scope.delete_intake = function delete_intake(intake, Index) {

			$scope.course.intakes.splice(Index, 1);

			$http({
				url: siteURL + "/admin/intakes/" + intake.id + ".json",
				method: 'DELETE',
				data: {}
			}).success(function(data, status, headers, config) {
				pop_message("Your intake has been saved successfully");
			}).error(function(data, status, headers, config) {

			});



			return false;
		}

		/** ===================================
		 * Delete Course Session
		 ====================================	*/
		$scope.delete_course_session = function delete_course_session(course_session, Index) {

			$scope.course.course_sessions.splice(Index, 1);

			$http({
				url: siteURL + "/admin/course_sessions/" + course_session.id + ".json",
				method: 'DELETE',
				data: {}
			}).success(function(data, status, headers, config) {
				pop_message("Your course session has been deleted successfully");
			}).error(function(data, status, headers, config) {

			});



			return false;
		}

		/** ===================================
		 * Delete Course Material Link
		 ====================================	*/
		$scope.delete_course_material = function delete_course_material(course_material, Index) {

			$scope.course.course_materials.splice(Index, 1);

			$http({
				url: siteURL + "/admin/course_materials/" + course_material.id + ".json",
				method: 'DELETE',
				data: {}
			}).success(function(data, status, headers, config) {
				pop_message("Your course material link has been deleted successfully");
			}).error(function(data, status, headers, config) {

			});



			return false;
		}


		/** ===================================
		 * New Course Sessions modal
		 ====================================	*/
		$scope.show_new_course_session_modal = function show_new_course_session_modal() {
			document.getElementById("course_session_modal_form").reset();
			$scope.course_session = {
				'course': {
					id: $scope.course.id
				}
			};
			$("#course_sessions_modal").modal("show");
		}

		/** ===================================
		 * Edit Course Sessions modal
		 ====================================	*/
		$scope.show_edit_course_session_modal = function show_edit_course_session_modal(course_session) {

			$scope.course_session = course_session;
			$("#course_sessions_modal").modal("show");
		}

		/** ===================================
		 * Save course session
		 ====================================	*/
		$scope.save_course_session = function save_course_session() {

			if (!$scope.course_session.name) {
				$("#course_session_name").focus();
				return false;
			}

			$("#course_sessions_modal .btn-primary").addClass("disabled").val("Loading...");

			var ajax_url = "<?php echo $this->base_url("course_sessions"); ?>";
			if ($scope.course_session.id) {
				var ajax_url = ajax_url + "/" + $scope.course_session.id;
				var ajax_method = "PUT";
			} else {
				var ajax_method = "POST";
			}

			$http({
				url: ajax_url + ".json",
				method: ajax_method,
				data: $scope.course_session
			}).success(function(data, status, headers, config) {
				$("#course_sessions_modal .btn-primary").removeClass("disabled").val("Save course session");
				if ($scope.course_session.id) {
					pop_message("Your course session has been saved successfully");
				} else {
					$scope.course.course_sessions.push(data);
					pop_message("Your course session has been added successfully");
				}
				$("#course_sessions_modal").modal("hide");
			}).error(function(data, status, headers, config) {

			});
		}

		/** ===================================
		 * New Course Materials modal
		 ====================================	*/
		$scope.show_new_course_material_modal = function show_new_course_material_modal() {
			document.getElementById("course_material_modal_form").reset();
			$scope.course_material = {
				'course': {
					id: $scope.course.id
				}
			};
			$("#course_materials_modal").modal("show");
		}

		/** ===================================
		 * Edit Course Materials modal
		 ====================================	*/
		$scope.show_edit_course_material_modal = function show_edit_course_material_modal(course_material) {

			$scope.course_material = course_material;
			$("#course_materials_modal").modal("show");
		}

		/** ===================================
		 * Save course material
		 ====================================	*/
		$scope.save_course_material = function save_course_material() {

			if (!$scope.course_material.name) {
				$("#course_material_name").focus();
				return false;
			}

			$("#course_materials_modal .btn-primary").addClass("disabled").val("Loading...");

			var ajax_url = "<?php echo $this->base_url("course_materials"); ?>";
			if ($scope.course_material.id) {
				var ajax_url = ajax_url + "/" + $scope.course_material.id;
				var ajax_method = "PUT";
			} else {
				var ajax_method = "POST";
			}

			$http({
				url: ajax_url + ".json",
				method: ajax_method,
				data: $scope.course_material
			}).success(function(data, status, headers, config) {
				$("#course_materials_modal .btn-primary").removeClass("disabled").val("Save course material link");
				if ($scope.course_material.id) {
					pop_message("Your course material link has been saved successfully");
				} else {
					$scope.course.course_materials.push(data);
					pop_message("Your course material link has been added successfully");
				}
				$("#course_materials_modal").modal("hide");
			}).error(function(data, status, headers, config) {

			});
		}

		// init the filtered items
		$scope.search = function() {

			$scope.filteredItems = $filter('filter')($scope.waitinglists, function(item) {
				console.log($scope.waitinglists);
				for (var attr in item) {
					console.log(attr);
					console.log(item);
					if (searchMatch(item[attr], $scope.search_values.query))
						return true;
				}
				return false;
			});
			// take care of the sorting order
			if ($scope.sortingOrder !== '') {
				$scope.filteredItems = $filter('orderBy')($scope.filteredItems, $scope.sortingOrder, $scope.reverse);
			}
			$scope.currentPage = 0;
			// now group by pages
			$scope.groupToPages();
		};

		$scope.paginate = function() {

			// take care of the sorting order
			if ($scope.sortingOrder !== '') {
				$scope.filteredItems = $filter('orderBy')($scope.filteredItems, $scope.sortingOrder, $scope.reverse);
			}
			$scope.currentPage = 0;
			// now group by pages
			$scope.groupToPages();
		};

		// calculate page in place
		$scope.groupToPages = function() {
			$scope.pagedItems = [];

			for (var i = 0; i < $scope.filteredItems.length; i++) {
				if (i % $scope.itemsPerPage.waitinglist === 0) {
					$scope.pagedItems[Math.floor(i / $scope.itemsPerPage.waitinglist)] = [$scope.filteredItems[i]];
				} else {
					$scope.pagedItems[Math.floor(i / $scope.itemsPerPage.waitinglist)].push($scope.filteredItems[i]);
				}
			}
		};

		$scope.range = function(start, end) {
			var ret = [];
			if (!end) {
				end = start;
				start = 0;
			}
			for (var i = start; i < end; i++) {
				ret.push(i);
			}
			return ret;
		};

		$scope.prevPage = function() {
			if ($scope.currentPage > 0) {
				$scope.currentPage--;
			}
		};

		$scope.nextPage = function() {
			if ($scope.currentPage < $scope.pagedItems.length - 1) {
				$scope.currentPage++;
			}
		};

		$scope.setPage = function() {
			$scope.currentPage = this.n;
		};

		// functions have been describe process the data for display
		if ($scope.waitinglists.length > 0) {
			$scope.search();
		}

		// change sorting order
		$scope.sort_by = function(newSortingOrder) {
			if ($scope.sortingOrder == newSortingOrder)
				$scope.reverse = !$scope.reverse;

			$scope.sortingOrder = newSortingOrder;

			// icon setup
			$('th i').each(function() {
				// icon reset
				$(this).removeClass().addClass('icon-sort');
			});
			if ($scope.reverse)
				$('th.' + new_sorting_order + ' i').removeClass().addClass('icon-chevron-up');
			else
				$('th.' + new_sorting_order + ' i').removeClass().addClass('icon-chevron-down');
		};


		// the filtered items for evaluations
		$scope.eval_search = function() {

			$scope.eval_filteredItems = $filter('filter')($scope.course_evals.evaluations, function(item) {
				for (var attr in item) {
					if (searchMatch(item[attr], $scope.search_values.eval_query))
						return true;
				}
				return false;
			});

			// take care of the sorting order
			if ($scope.eval_sortingOrder !== '') {
				$scope.eval_filteredItems = $filter('orderBy')($scope.eval_filteredItems, $scope.eval_sortingOrder, $scope.eval_reverse);
			}
			$scope.eval_currentPage = 0;
			// now group by pages
			$scope.eval_groupToPages();
		};

		$scope.eval_paginate = function() {

			// take care of the sorting order
			if ($scope.eval_sortingOrder !== '') {
				$scope.eval_filteredItems = $filter('orderBy')($scope.eval_filteredItems, $scope.eval_sortingOrder, $scope.eval_reverse);
			}
			$scope.eval_currentPage = 0;
			// now group by pages
			$scope.eval_groupToPages();
		};

		// calculate page in place
		$scope.eval_groupToPages = function() {
			$scope.eval_pagedItems = [];

			for (var i = 0; i < $scope.eval_filteredItems.length; i++) {
				if (i % $scope.itemsPerPage.eval === 0) {
					$scope.eval_pagedItems[Math.floor(i / $scope.itemsPerPage.eval)] = [$scope.eval_filteredItems[i]];
				} else {
					$scope.eval_pagedItems[Math.floor(i / $scope.itemsPerPage.eval)].push($scope.eval_filteredItems[i]);
				}
			}
		};
		$scope.eval_range = function(start, end) {
			var ret = [];
			if (!end) {
				end = start;
				start = 0;
			}
			for (var i = start; i < end; i++) {
				ret.push(i);
			}
			return ret;
		};

		$scope.eval_prevPage = function() {
			if ($scope.eval_currentPage > 0) {
				$scope.eval_currentPage--;
			}
		};

		$scope.eval_nextPage = function() {
			if ($scope.eval_currentPage < $scope.eval_pagedItems.length - 1) {
				$scope.eval_currentPage++;
			}
		};

		$scope.eval_setPage = function() {
			$scope.eval_currentPage = this.n;
		};

		// functions have been describe process the data for display
		//must be done on short course only
		if ($scope.course_evals.evaluations.length > 0) {
			$scope.eval_search();
		}

		// change sorting order
		$scope.eval_sort_by = function(newSortingOrder) {
			if ($scope.eval_sortingOrder == newSortingOrder)
				$scope.eval_reverse = !$scope.eval_reverse;

			$scope.eval_sortingOrder = newSortingOrder;

			// icon setup
			$('th i').each(function() {
				// icon reset
				$(this).removeClass().addClass('icon-sort');
			});
			if ($scope.eval_reverse)
				$('th.' + new_sorting_order + ' i').removeClass().addClass('icon-chevron-up');
			else
				$('th.' + new_sorting_order + ' i').removeClass().addClass('icon-chevron-down');
		};

		demo.$inject = ['$scope', '$filter'];
	}]);


	app.filter('removeCharacter', function() {
		return function(text, filter) {
			var str = text.replace('>', '');
			str = str.replace('<', '');
			str = str.replace('=', '');
			filter.value = str;
			return str;
		};
	});


	function previewFile() {
		var preview = $("#preview_image");
		var file = document.querySelector('input[type=file]').files[0];
		var reader = new FileReader();

		reader.onloadend = function() {
			preview.css({
				"background-image": "url(" + reader.result + ")"
			});
		}

		if (file) {
			reader.readAsDataURL(file);
		} else {
			preview.css({
				"background-image": ""
			});
		}
	}

	var searchMatch = function(haystack, needle) {

		if (!needle) {
			return true;
		}
		return haystack.toLowerCase().indexOf(needle.toLowerCase()) !== -1;
	};

	var sortingOrder = 'date';
</script>
<?php
$admin_url = website_url . "/admin/";

?>
<div ng-controller="demo" ng-cloak>


    <!-- Delete course modal -->
    <div class="modal fade" id="delete_course_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form method="POST" action="<?php echo $this->current_url(); ?>">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel">Delete Course</h4>
                    </div>
                    <div class="modal-body text-center">
                        Are your sure you want to delete this course?
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="action" value="delete_course">
                        <input type="submit" class="btn btn-danger" value="Delete">
                    </div>
                </form>
            </div>
        </div>
    </div>



    <!-- Duplicate course modal -->
    <div class="modal fade" id="duplicate_course_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form method="POST" action="<?php echo $this->current_url(); ?>">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel">Duplicate Course</h4>
                    </div>
                    <div class="modal-body text-left">
                        <label for="title">Course name</label>
                        <input class="form-control" type="text" name="title" autofocus="">
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="action" value="duplicate_course">
                        <input type="submit" class="btn btn-primary" value="Duplicate">
                    </div>
                </form>
            </div>
        </div>
    </div>

    <form method="POST" action="<?php echo $this->base_url('programmes/' . $data['entry']['id']); ?>" class="inner_container" enctype="multipart/form-data">
        <div class="breadcrumbs">
            <a href="<?php echo $this->base_url('programmes'); ?>"><i class="fa fa-chevron-left" aria-hidden="true"></i> <?php terminology("Programmes", curPageURL(), "Programmes")?></a>
        </div>
        <div class="top_section">
            <div class="buttons_wrap">
                <?php if ($data['entry']['id'] == "new") { ?>
                    <input type="submit" name="add_course" value="Add course" class="btn btn-sm btn-primary">
                <?php } else { ?>
                    <a target="_blank" href="<?php echo $this->school_info['href']; ?>/catalogue/programmes/<?php echo $data['entry']['id']; ?>" class="btn btn-sm btn-default"><i class="fa fa-external-link-alt"></i> View Course</a>
                    <input type="submit" name="save_course" value="Save" class="btn btn-sm btn-primary">

                <?php } ?>
            </div>
            <h1>
                <?php if ($data['entry']['id'] != "new") { ?>
                    <span>{{ course.title }}</span>
                <?php } else { ?>
                    <span ng-hide="course.title">New <?php terminology("programme", curPageURL(), "programme")?></span>
                    <span ng-show="course.title!=''">{{ course.title }}</span>
                <?php } ?>
            </h1>

        </div>

        <?php if ($_GET['saved']) { ?>
            <div class="alert alert-success main_alert" role="alert">
                <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
                The course was saved successfully.
            </div>
        <?php } ?>

        <?php if ($_GET['deleted_module']) { ?>
            <div class="alert alert-success main_alert" role="alert">
                <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
                The module was deleted successfully.
            </div>
        <?php } ?>


        <div class="row">
            <div class="col-sm-8">

                <div class="panel panel-default no-padding ">
                    <div class="panel-body">
                        <label>Title</label>
                        <input autofocus="" dir="<?php echo $data['entry']['language_direction']; ?>" class="form-control ssb" name="title" type="text" ng-model="course.title" value="<?php echo $data['entry']['title']; ?>" required>

                        <label for="description">
                            Description
                            <a href="#" class="pull-right" data-toggle="modal" data-target="#short_code_modal">Insert Short code</a>
                            <a href="#" class="pull-right" data-toggle="modal" data-target="#pictures_library" style="margin-right: 15px;">Pictures Library</a>
                        </label>
                        <textarea class="form-control ssb" dir="<?php echo $data['entry']['language_direction']; ?>" name="description" style="height: 200px;"><?php echo $data['entry']['description']; ?></textarea>

                        <script type="text/javascript">
                            editor =CKEDITOR.config.versionCheck = false;
                            editor = CKEDITOR.config.contentsLangDirection = '<?php echo $data['entry']['language_direction']; ?>';
							editor = CKEDITOR.replace('description', {
								height: '200px',
								filebrowserImageUploadUrl: '../../engine/modules/inc_cke_upload.php?type=Images'
							});
							CKEDITOR.on("instanceReady", function(event) {
								event.editor.on("beforeCommandExec", function(event) {
									// Show the paste dialog for the paste buttons and right-click paste
									if (event.data.name == "paste") {
										event.editor._.forcePasteDialog = true;
									}
									// Don't show the paste dialog for Ctrl+Shift+V
									if (event.data.name == "pastetext" && event.data.commandData.from == "keystrokeHandler") {
										event.cancel();
									}
								})
							});
                        </script>

                        <a href="javascript:void(0);" ng-click="show_summary_textarea = !show_summary_textarea" ng-hide="show_summary_textarea"><br>Add Excerpt/Summary</a>
                        <div ng-show="show_summary_textarea||course.summary">
                            <label><br>Excerpt/Summary</label>
                            <textarea class="form-control ssb" name="summary" style="height: 200px;" id="summaryEditor"><?php echo $data['entry']['summary']; ?></textarea>

                            <script type="text/javascript">
								$("#summaryEditor").click(function() {
									editor = CKEDITOR.replace('summary', {
										height: '200px',
                                        versionCheck: false

                                    });
								});
                            </script>
                        </div>



                    </div>
                </div>

                <div class="panel panel-default no-padding"  ng-hide="school.type_ids == 12">
                    <div class="panel-body">

                        <div class="row">
                            <div class="col-sm-12 ssb"  ng-hide="school.type_ids == 12">
                                <label>Eligibility/Prerequisites</label>
                                <textarea class="form-control ssb" id="entry_requirementsEditor" name="entry_requirements" style="height: 100px;"><?php echo $data['entry']['entry_requirements']; ?></textarea>
                                <script type="text/javascript">
									$("#entry_requirementsEditor").click(function() {
										editor1 = CKEDITOR.replace('entry_requirements', {
											height: '100px',
                                            versionCheck: false

                                        });
										editor1.focusManager.focus();
									});
                                </script>
                            </div>

                            <div class="col-sm-12 ssb" ng-hide="school.type_ids == 12">
                                <label>Career Opportunities</label>
                                <textarea class="form-control ssb" name="career_opportunities" id="career_opportunities" style="height: 100px;"><?php echo $data['entry']['career_opportunities']; ?></textarea>
                                <script type="text/javascript">
									$("#career_opportunities").click(function() {
										editor2 = CKEDITOR.replace('career_opportunities', {
											height: '100px',
                                            versionCheck: false
                                        });
										editor2.focus();
									});
                                </script>
                            </div>

                            <div class="col-sm-12 ssb"  ng-hide="school.type_ids == 12">
                                <label>Delivery and Assessment</label>
                                <textarea class="form-control ssb" id="delivery_and_assesment" name="delivery_and_assesment" style="height: 100px;"><?php echo $data['entry']['delivery_and_assesment']; ?></textarea>
                                <script type="text/javascript">
									$("#delivery_and_assesment").click(function() {
										editor3 = CKEDITOR.replace('delivery_and_assesment', {
											height: '100px',
                                            versionCheck: false
                                        });
									});
                                </script>
                            </div>

                            <div class="col-sm-12 ssb"  ng-hide="school.type_ids == 12">
                                <label><?php terminology("Programme", curPageURL(), "Programme")?> Inclusions</label>
                                <textarea class="form-control" id="program_inclusions" name="program_inclusions" style="height: 100px;"><?php echo $data['entry']['program_inclusions']; ?></textarea>
                                <script type="text/javascript">
									$("#program_inclusions").click(function() {
										editor4 = CKEDITOR.replace('program_inclusions', {
											height: '100px',
                                            versionCheck: false

                                        });
									});
                                </script>
                            </div>


                            <div class="col-sm-12 ssb"  ng-hide="school.type_ids == 12">
                                <label>Testimonials</label>
                                <textarea class="form-control" id="testimonials" name="testimonials" style="height: 100px;"><?php echo $data['entry']['testimonials']; ?></textarea>
                                <script type="text/javascript">
									$("#testimonials").click(function() {
										editor55 = CKEDITOR.replace('testimonials', {
											height: '100px',
                                            versionCheck: false

                                        });
									});
                                </script>
                            </div>

                        </div>


                    </div>
                </div>


                <div class="panel panel-default no-padding">
                    <div class="panel-body">
                        <h2 class="next-heading">Other</h2>

                        <div class="row">
                            <div class="col-sm-6">
                                <label>Abbreviation</label>
                                <input type="text" class="form-control ssb" name="abbreviation" ng-model="course.abbreviation">
                            </div>
                            <div class="col-sm-6"  ng-hide="school.type_ids == 12">
                                <label>Internal Name/Internal Code</label>
                                <input type="text" class="form-control ssb" name="internal_code" ng-model="course.internal_code">
                            </div>

                            <?php if (pull_field('form_schools', 'db30', "WHERE id = {$_SESSION['usergroup']}") !=12 && strpos($data['ageGroupColumn']->figures, 'core_age_group') === false) { ?>
                                <div class="col-sm-6">
                                    <label>Age Group</label>
                                    <input type="text" class="form-control ssb" name="age_group" ng-model="course.age_group">
                                </div>
                            <?php }else { ?>
                                <div class="col-sm-6">
                                    <label for="age_group">Age Group</label>
                                    <select class="form-control" name="age_group" >
                                        <option value="">Choose...</option>
                                        <?php foreach ($data['entry']['course_age_groups'] as $group) :  ?>
                                            <option <?php echo ($group['id'] == $data['entry']['age_group'])? 'selected="selected"': '' ?> value="<?php echo $group['id']?>"> <?php echo $group['name'] ?></option>
                                        <?php endforeach; ?>
                                    </select>

                                </div>
                            <?php } ?>

                            <div class="col-sm-6"  ng-hide="school.type_ids == 12">
                                <label>Study Mode</label>
                                <select class="form-control" name="study_mode" ng-model="course.study_mode">
                                    <option value="">Select....</option>
                                    <option value="full time">full time</option>
                                    <option value="part time">part time</option>
                                    <option value="distance learning">distance learning</option>
                                    <option value="online learning">online learning</option>
                                    <option value="not specified">not specified</option>
                                </select>
                            </div>

                            <div class="clear"></div>

                            <div class="col-sm-6"  ng-hide="school.type_ids == 12">
                                <label>Participation mode</label>
                                <input type="text" class="form-control ssb" name="participation_mode" ng-model="course.participation_mode">
                            </div>

                            <div class="col-sm-6 hide">
                                <label>Examining Board</label>
                                <input type="text" class="form-control ssb" name="examining_board" ng-model="course.examining_board">
                            </div>


                            <div class="col-sm-6"  ng-hide="school.type_ids == 12">
                                <label>Application Fee</label>
                                <input type="text" class="form-control ssb" name="fee" ng-model="course.fee">
                            </div>


                            <div class="col-sm-6" >
                                <label>Duration</label>
                                <input type="text" class="form-control ssb" name="duration" ng-model="course.duration">
                            </div>



                            <div class="col-sm-6"  ng-hide="school.type_ids == 12">
                                <label>Language</label>
                                <input type="text" class="form-control ssb" name="language" ng-model="course.language">
                            </div>
                            <div class="col-sm-6"  ng-hide="school.type_ids == 12">
                                <label>Credits</label>
                                <input type="text" class="form-control ssb" name="credits" ng-model="course.credits">
                            </div>

                            <div class="col-sm-6">
                                <label>Course Groups</label>
                                <select class="form-control ssb" name="course_groups">
                                    <option value="">Choose...</option>
                                    <?php foreach ($data['course_groups'] as $status) { ?>
                                        <option
                                            <?php if ($status['value'] == $data['entry']['course_groups']) {
                                                echo 'selected="selected"';
                                            } ?>
                                                value="<?php echo $status['value']; ?>"><?php echo $status['label']; ?>
                                        </option>
                                    <?php } ?>
                                </select>
                            </div>
                            
                            <div class="col-sm-6">
                                
                                <label>Course Type </label>
                                <select  name="core_course_type" ng-model="course.core_course_type" class="form-control ssb">
                                    <option value="">Choose...</option>
                                    <option value="{{form.id}}" ng-repeat="form in course.courseTypes" ng-selected="form.id === course.core_course_type">
                                        {{form.name}}
                                    </option>
                                </select>
                                
                            </div>
                            
                            <div class="clearfix"></div>

                            <div class="col-sm-6" ng-show="school.type_ids == 12">
                                <label> Show Course on Booking Portal Based on </label>
                                <select class="form-control ssb" name="bookingPortalCondition">
                                    <option value="">Choose...</option>
                                    <?php foreach ($data['sisProfileFields'] as $field) { ?>
                                        <option
                                            <?php if ($field['form_id'] == $data['entry']['selectField']) {
                                                echo 'selected="selected"';
                                            } ?>
                                                value="<?php echo $field['form_id']; ?>"><?php echo $field['name']; ?>
                                        </option>
                                    <?php } ?>
                                </select>
                            </div>

                            <div class="col-sm-6" ng-show="school.type_ids == 12">
                                <label> When Condition is set to </label>
                                <input  class="form-control ssb" type="text" value="<?php echo $data['entry']['courseCondition'] ?>" name="courseCondition">
                            </div>
                        </div>

                        <hr>

                        <div class="row"  ng-hide="school.type_ids == 12">
                            <div class="col-sm-6">
                                <label>Start Date</label>
                                <input type="date" class="form-control ssb" name="start_date" ng-model="course.start_date">
                            </div>

                            <div class="col-sm-3">
                                <label>Deadline Date</label>
                                <input type="date" class="form-control ssb" name="deadline_date" ng-model="course.deadline_date">
                            </div>

                            <div class="col-sm-3">
                                <label>Deadline Time</label>

                                <input type="time" class="form-control ssb" name="deadline_time" ng-model="course.deadline_time">

                            </div>


                            <div class="col-sm-6">
                                <label>Reference Deadline Date</label>
                                <input type="date" class="form-control ssb" name="references_deadline_date" ng-model="course.references_deadline_date">
                            </div>

                            <div class="col-sm-3">
                                <label>Reference Deadline Time</label>

                                <input type="time" class="form-control ssb" name="reference_deadline_time" ng-model="course.reference_deadline_time">

                            </div>
                        </div>

                    </div>
                </div>

                <div ng-if="course.short_course=='yes'" class="panel panel-default no-padding">
                    <div class="panel-body">
                        <h2 class="next-heading">
                            Waiting List for
                            <span ng-hide="course.title">New <?php terminology("Programme", curPageURL(), "Programme")?></span>
                            <span ng-show="course.title!=''">{{course.title}}</span>
                        </h2>
                        <p>
                            To book the students please visit the filtered scheduled courses available for booking <a href="<?php echo $admin_url; ?>shortcourses?course_id={{course.id}}">page here</a>.
                        </p>
                        <div class="row">

                            <div class="col-sm-6">
                                <label class="" for="">Total Records: {{waitinglists.length}}</label>
                                <input type="text" ng-model="search_values.query" ng-change="search()" class="form-control" placeholder="Search">
                            </div>

                            <div class="col-sm-6">
                                <label for="">Records to show</label>
                                <select ng-model="itemsPerPage.waitinglist" id="pageSize" ng-change="paginate()" class="form-control col-sm-2">
                                    <option value="5">5</option>
                                    <option value="10">10</option>
                                    <option value="15">15</option>
                                    <option value="20">20</option>
                                </select>
                            </div>
                        </div>

                        <table class="table table-responsive table-striped table-hover">
                            <thead>
                            <th></th>
                            <th>Date added on waitinglist</th>
                            <th>First Name</th>
                            <th>Surname</th>
                            <th>Geographical Area</th>
                            <th>Preferred Time</th>
                            </thead>
                            <tbody>
                            <tr ng-if="waitinglists.length > 0" ng-repeat="waitinglist in  pagedItems[currentPage] |  orderBy:sortingOrder:reverse">
                                <td>
                                    <a href="<?php echo engine_url(); ?>controller.php?pg=265&rec={{waitinglist.id}}&del={{waitinglist.id}}&width=850&height=600&jqmRefresh=true" class="thickbox btn btn-default btn-xs" title="Delete Record" alt="Delete Record">
                                        <span class="glyphicon glyphicon-trash" aria-hidden="true"></span>
                                    </a>
                                </td>
                                <td>{{waitinglist.date}}</td>
                                <td>{{waitinglist.db39}}</td>
                                <td>{{waitinglist.db40}}</td>
                                <td>{{waitinglist.geog_area}}</td>
                                <td>{{waitinglist.db62946}}</td>
                            </tr>

                            <tr ng-if="waitinglists.length < 1">
                                <td style="text-align:center" colspan="7">
                                    <b><?php echo "No Students Have Been Added To The Waitinglist."; ?></b>
                                </td>
                            </tr>

                            </tbody>
                            <tfoot>
                            <td ng-if="waitinglists.length > 0" colspan="7">
                                <div class="pagination pull-right">
                                    <ul>
                                        <li ng-class="{disabled: currentPage == 0}">
                                            <a href ng-click="prevPage()">« Prev</a>
                                        </li>
                                        <li ng-repeat="n in range(pagedItems.length)" ng-class="{active: n == currentPage}" ng-click="setPage()">
                                            <a href ng-bind="n + 1">1</a>
                                        </li>
                                        <li ng-class="{disabled: currentPage == pagedItems.length - 1}">
                                            <a href ng-click="nextPage()">Next »</a>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                            </tfoot>
                        </table>
                    </div>
                </div>

                <div ng-if="course.short_course =='yes'" class="panel panel-default no-padding">
                    <div class="panel-body">
                        <h2 class="next-heading">
                            Course Evaluation or Feedback for
                            <span ng-hide="course.title">New <?php terminology("Programme", curPageURL(), "Programme")?></span>
                            <span ng-show="course.title!=''">{{course.title}}</span>
                        </h2>
                        <p>
                            To view enhanced course evaluation or feedback please click visit the <a target="_blank" rel="noopener" href="<?php echo $admin_url; ?>programmes/evaluation_report/{{course.id}}">page here</a>.
                        </p>
                        <div class="row">

                            <div class="col-sm-6">
                                <label class="" for="">Total Records: {{course_evals.evaluations.length}} </label>
                                <input type="text" ng-model="search_values.eval_query" ng-change="eval_search()" class="form-control" placeholder="Search">
                            </div>

                            <div class="col-sm-6">
                                <label for="">Records to show</label>
                                <select ng-model="itemsPerPage.eval" id="pageSize" ng-change="eval_paginate()" class="form-control col-sm-2">
                                    <option value="5">5</option>
                                    <option value="10">10</option>
                                    <option value="15">15</option>
                                    <option value="20">20</option>
                                </select>
                            </div>
                        </div>
                        <div style="overflow: auto;">
                            <table class="table table-responsive table-striped table-hover" style="white-space: nowrap;">
                                <thead>
                                <th></th>
                                <th>Date Submitted</th>
                                <th>Scheduled Course Id</th>
                                <th>Scheduled Course Date</th>
                                <th ng-repeat="(key, value) in course_evals.fields"> {{value}}</th>
                                <th>Tutors</th>

                                </thead>
                                <tbody>
                                <tr ng-if="course_evals.evaluations.length > 0" ng-repeat="evaluation in  eval_pagedItems[eval_currentPage] |  orderBy:eval_sortingOrder:eval_reverse ">
                                    <td>
                                        <a href="<?php echo engine_url(); ?>controller.php?pg=713&rec={{evaluation.id}}&del={{evaluation.id}}&width=850&height=600&jqmRefresh=true" class="thickbox btn btn-default btn-xs" title="Delete Record" alt="Delete Record">
                                            <span class="glyphicon glyphicon-trash" aria-hidden="true"></span>
                                        </a>
                                    </td>
                                    <td>
                                        {{evaluation.date}}
                                    </td>
                                    <td>
                                        <a title="View Course Linked To Evaluation" href="<?php echo $admin_url; ?>/shortcourses/scheduled/{{evaluation.sched_id}}?vw={{evaluation.sched_uid}}&ref={{evaluation.sched_id}}&event=1" target="_blank" rel="noopener">
                                            {{evaluation.sched_id}}
                                        </a>
                                    </td>
                                    <td>
                                        <a title="View Course Linked To Evaluation" href="<?php echo $admin_url; ?>/shortcourses/scheduled/{{evaluation.sched_id}}?vw={{evaluation.sched_uid}}&ref={{evaluation.sched_id}}&event=1" target="_blank" rel="noopener">
                                            {{evaluation.sched_start_date}}
                                        </a>
                                    </td>
                                    <td ng-repeat="(key, value) in course_evals.fields">
                                        {{evaluation[key]}}
                                    </td>
                                    <td>
                                        {{evaluation.tutors}}
                                    </td>

                                </tr>

                                <tr ng-if="course_evals.evaluations.length < 1">
                                    <td style="text-align:center" colspan="7">
                                        <b><?php echo "No course evaluation or feedback submitted."; ?></b>
                                    </td>

                                </tr>

                                </tbody>
                            </table>
                        </div>
                        <table class="table table-responsive">
                            <tfoot>
                            <td ng-if="course_evals.evaluations.length > 0" colspan="7">
                                <div class="pagination pull-right">
                                    <ul>
                                        <li ng-class="{disabled: eval_currentPage == 0}">
                                            <a href ng-click="prevPage()">« Prev</a>
                                        </li>
                                        <li ng-repeat="n in eval_range(eval_pagedItems.length)" ng-class="{active: n == eval_currentPage}" ng-click="eval_setPage()">
                                            <a href ng-bind="n + 1">1</a>
                                        </li>
                                        <li ng-class="{disabled: eval_currentPage == eval_pagedItems.length - 1}">
                                            <a href ng-click="eval_nextPage()">Next »</a>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-sm-4">

                <div class="panel panel-default no-padding grey hide">
                    <div class="panel-body">
                        <h2 class="next-heading">Visibility</h2>
                        <div class="radio">
                            <label>
                                <input type="radio" name="visibility" value="publish" checked="checked"> Publish
                            </label>
                        </div>
                        <div class="radio">
                            <label>
                                <input type="radio" name="visibility" value="publish" checked="checked"> Hidden
                            </label>
                        </div>
                    </div>
                </div>

                <div class="panel panel-default no-padding grey">
                    <div class="panel-body">

                        <h2 class="next-heading">Availability</h2>
                        <label>Status</label>
                        <select class="form-control ssb" name="state">
                            <option value="">Choose...</option>
                            <?php foreach ($data['status_list'] as $status) { ?>
                                <option <?php if ($status == $data['entry']['state']) {
                                    echo 'selected="selected"';
                                } ?> value="<?php echo $status; ?>"><?php echo $status; ?></option>
                            <?php } ?>
                        </select>

                        <span  ng-show="is_short_course()">
                            <label>Course Status </label>
								<select class="form-control ssb" name="course_status">
									<option value="">Choose...</option>
									<?php foreach ($data['course_status'] as $status) { ?>
                                        <option <?php if ($status == $data['entry']['course_status']) {
                                            echo 'selected="selected"';
                                        } ?> value="<?php echo $status; ?>"><?php echo ucfirst($status); ?>
										</option>
                                    <?php } ?>
								</select>
                        </span>

                        <label>Publish</label>
                        <select class="form-control ssb" name="publish">
                            <option value="">Choose...</option>
                            <?php foreach ($data['publish_types'] as $publish) { ?>
                                <option <?php if ($publish == $data['entry']['publish']) {
                                    echo 'selected="selected"';
                                } ?> value="<?php echo $publish; ?>"><?php echo $publish; ?></option>
                            <?php } ?>
                        </select>

                        <label>Course QA Status </label>
                        <select class="form-control ssb" name="course_qa_status">
                            <option value="">Choose...</option>
                            <?php foreach ($data['course_qa_status'] as $status) { ?>
                                <option <?php if ($status['id'] == $data['entry']['course_qa_status']) {
                                    echo 'selected="selected"';
                                } ?>
                                        value="<?php echo $status['id']; ?>"><?php echo ucfirst($status['status']); ?>
                                </option>
                            <?php } ?>
                        </select>

                        <label  ng-hide="school.type_ids == 12">Send User To</label>
                        <select  ng-hide="school.type_ids == 12" name="uses_eoi_or_enquiries" ng-model="course.uses_eoi_or_enquiries" class="form-control ssb" name="publish">
                            <option value="">Choose...</option>
                            <option value="enquiries">Enquiry Form</option>
                            <option value="eoi">EOI Form</option>
                        </select>
                        <label ng-hide="school.type_ids == 12" ng-if="course.uses_eoi_or_enquiries!==''">Select Form</label>
                        <select  ng-hide="school.type_ids == 12" name="enquiry_form" ng-if="course.uses_eoi_or_enquiries!==''" ng-model="course.enquiry_form" class="form-control ssb" name="publish">
                            <option value="">Choose...</option>
                            <option value="{{form.id}}" ng-if="course.uses_eoi_or_enquiries==='enquiries'" ng-repeat="form in course.enquiry_forms" ng-selected="form.id === course.enquiry_form">
                                {{form.name}}
                            </option>
                            <option value="{{form.id}}" ng-if="course.uses_eoi_or_enquiries==='eoi'" ng-repeat="form in course.eoi_forms">
                                {{form.name}}
                            </option>
                        </select>

                        <label>Select Course Evaluation or Feedback Form</label>
                        <select name="eval_form" ng-model="course.eval_form" class="form-control ssb">
                            <option value="">Choose...</option>
                            <option value="{{form.id}}" ng-repeat="form in course.eval_forms" ng-selected="form.id === course.eval_form">
                                {{form.name}}
                            </option>
                        </select>
                        <label  ng-show="school.type_ids == 12">Mandatory Induction Course</label>
                        <select  ng-show="school.type_ids == 12" name="mandatory_course" ng-model="course.mandatory_course" class="form-control ssb">
                            <option value="">Choose...</option>
                            <option value="yes">Yes</option>
                            <option value="no">No</option>
                        </select>
                        <div  ng-hide="school.type_ids == 12" class="checkbox">
                            <label>
                                <input type="checkbox" value="1" ng-true-value="1" ng-checked="course.hide_apply_button" name="hide_apply_button" ng-model="course.hide_apply_button">
                                Hide apply button
                            </label>
                        </div>

                        <hr>

                        <h2 ng-hide="school.type_ids == 12" class="next-heading">Categories</h2>

                        <?php if (count($data['levels']) && $this->school_info['type_ids'] !='12' ) { ?>
                            <label>Course Level</label>
                            <select class="form-control ssb" name="course_level" ng-model="course_level">
                                <option value="">Choose...</option>
                                <option ng-repeat="level in course_levels" value="{{level.id}}" ng-selected="level.id == course_level">{{level.title}}</option>
                            </select>
                        <?php } ?>

                        <?php if (count($data['departments'])) { ?>
                            <label>Department</label>
                            <select class="form-control ssb" name="department">
                                <option value="">Choose...</option>
                                <?php foreach ($data['departments'] as $category) { ?>
                                    <option <?php if ($category['id'] == $data['entry']['department']['id']) {
                                        echo 'selected="selected"';
                                    } ?> value="<?php echo $category['id']; ?>"><?php echo $category['title']; ?></option>
                                <?php } ?>
                            </select>
                        <?php } ?>

                        <label  ng-hide="school.type_ids == 12">Route</label>
                        <select  ng-hide="school.type_ids == 12" class="form-control ssb" name="route">
                            <option value="">Choose...</option>
                            <?php foreach ($data['routes'] as $category) { ?>
                                <option <?php if ($category['id'] == $data['entry']['route']['id']) {
                                    echo 'selected="selected"';
                                } ?> value="<?php echo $category['id']; ?>"><?php echo $category['rule_name']; ?></option>
                            <?php } ?>
                        </select>

                        <hr ng-hide="school.type_ids == 12">
                        <label  ng-hide="school.type_ids == 12" >Tutors Responsible for Academic Review</label>
                        <select  ng-hide="school.type_ids == 12" class="form-control ssb" name="tutor_id">
                            <option value="">Choose...</option>
                            <?php foreach ($data['tutors'] as $tutor) { ?>
                                <option <?php if ($tutor['id'] == $data['entry']['tutor_id']) {
                                    echo 'selected="selected"';
                                } ?> value="<?php echo $tutor['id']; ?>"><?php echo $tutor['first_name'] . " " . $tutor['last_name']; ?></option>
                            <?php } ?>
                        </select>
                        <div  ng-hide="school.type_ids == 12" ng-show="is_short_course()">
                            <hr>
                            <label>No. of Confirmed Tutors</label>
                            <select class="form-control ssb" name="no_confirmed_tutors">
                                <option value="">Choose...</option>
                                <?php for ($i = 0; $i < 10; $i++) { ?>
                                    <option <?php if ($i == $data['entry']['no_confirmed_tutors']) {
                                        echo 'selected="selected"';
                                    } ?> value="<?php echo $i; ?>"><?php echo $i; ?></option>
                                <?php } ?>
                            </select>
                        </div>
                        <hr  ng-hide="school.type_ids == 12">
                        <label  ng-hide="school.type_ids == 12" >Payment Confirmation Email Template</label>
                        <select  ng-hide="school.type_ids == 12" class="form-control ssb" name="email_template_id">
                            <option value="">Choose...</option>
                            <?php echo "chosen_id".$data['entry']['chosen_email_template_id']; foreach ($data['email_templates'] as $template) { ?>
                                <option <?php if ($template['id'] == $data['entry']['chosen_email_template_id']) {
                                    echo 'selected="selected"';
                                } ?> value="<?php echo $template['id']; ?>"><?php echo $template['email_template_name']; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>


                <div ng-show="!is_short_course()" class="panel panel-default no-padding grey">
                    <div class="panel-body np">
                        <div class="padding15 npb">
                            <a class="btn btn-default pull-right btn-xs" href="javascript:;" ng-click="show_new_intake_modal()" ng-hide="course.intakes.length<1">Add intake</a>
                            <h2 class="next-heading">Intakes</h2>
                        </div>

                        <div class="text-center" ng-hide="course.intakes.length">
                            <i class="fa fa-tags" style="font-size: 64px; margin-bottom: 10px; color: #999;"></i>
                            <p style="color: #999;">No intakes added yet</p>
                            <a class="btn btn-default btn-xs" href="javascript:;" ng-click="show_new_intake_modal()">Add intake</a>
                            <br>
                            <br>
                        </div>
                        <ul style="margin: 0; padding: 0;" class="variant_list">
                            <li ng-repeat="intake in course.intakes track by $index" style="list-style-type: none;">
								<span class="options">
									<i class="fa fa-pencil" ng-click="show_edit_intake_modal(intake)" style="cursor: pointer;" title="Edit"></i>
									<i class="fa fa-copy" ng-click="show_duplicate_intake_modal(intake)" style="cursor: copy;" title="Duplicate"></i>
									<i class="fa fa-trash" ng-click="delete_intake(intake,$index)" style="cursor: pointer;" title="Delete"></i>

								</span>
                                <a href="javascript:;" ng-click="show_edit_intake_modal(intake)">{{intake.title}}</a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div ng-if="is_short_course()" class="panel panel-default no-padding grey">
                    <div class="panel-body np">
                        <div class="padding15 npb">
                            <a class="btn btn-default pull-right btn-xs" href="#" data-toggle="modal" ng-click="show_new_course_session_modal()" ng-hide="course.course_sessions.length<1">Add course session</a>
                            <h2 class="next-heading">Course Sessions</h2>
                        </div>

                        <div class="text-center" ng-hide="course.course_sessions.length">
                            <i class="fa fa-tags" style="font-size: 64px; margin-bottom: 10px; color: #999;"></i>
                            <p style="color: #999;">No course sessions added yet</p>
                            <a class="btn btn-default btn-xs" href="#" data-toggle="modal" ng-click="show_new_course_session_modal()">Add course session</a>
                            <br>
                            <br>
                        </div>
                        <ul style="margin: 0; padding: 0;" class="variant_list">
                            <li ng-repeat="course_session in course.course_sessions track by $index" style="list-style-type: none;">
								<span class="options">
									<i class="fa fa-pencil" ng-click="show_edit_course_session_modal(course_session)" style="cursor: pointer;" title="Edit"></i>
									<i class="fa fa-trash" ng-click="delete_course_session(course_session,$index)" style="cursor: pointer;"></i>
								</span>
                                <a href="javascript:;" ng-click="show_edit_course_session_modal(course_session)">{{course_session.name}}</a>
                            </li>
                        </ul>
                    </div>
                </div>

                <div ng-if="is_short_course()" class="panel panel-default no-padding grey">
                    <div class="panel-body np">
                        <div class="padding15 npb">
                            <a class="btn btn-default pull-right btn-xs" href="#" data-toggle="modal" ng-click="show_new_course_material_modal()" ng-hide="course.course_materials.length<1">Add course material link</a>
                            <h2 class="next-heading">Course Material Links</h2>
                        </div>

                        <div class="text-center" ng-hide="course.course_materials.length">
                            <i class="fa fa-tags" style="font-size: 64px; margin-bottom: 10px; color: #999;"></i>
                            <p style="color: #999;">No course material links added yet</p>
                            <a class="btn btn-default btn-xs" href="#" data-toggle="modal" ng-click="show_new_course_material_modal()">Add course material link</a>
                            <br>
                            <br>
                        </div>
                        <ul style="margin: 0; padding: 0;" class="variant_list">
                            <li ng-repeat="course_material in course.course_materials track by $index" style="list-style-type: none;">
								<span class="options">
									<i class="fa fa-pencil" ng-click="show_edit_course_material_modal(course_material)" style="cursor: pointer;" title="Edit"></i>
									<i class="fa fa-trash" ng-click="delete_course_material(course_material,$index)" style="cursor: pointer;"></i>
								</span>
                                <a href="javascript:;" ng-click="show_edit_course_material_modal(course_material)">{{course_material.name}}</a>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="panel panel-default no-padding grey"  >
                    <div class="panel-body">
                        <h2 class="next-heading">Image</h2>

                        <div style="background-color: #e1e1e1; height: 120px; position: relative; text-align: center;">
                            <i class="fa fa-image" style="font-size: 64px; margin-bottom: 10px; color: #999; margin-top: 30px; "></i>
                            <div style="opacity: 1; background-size: contain; background-repeat: no-repeat; background-position: center; position: absolute; top: 0; right: 0; bottom: 0; left: 0; z-index: 1; background-image: url('<?php echo $data['entry']['image']; ?>');" id="preview_image"></div>
                            <input type="hidden" name="current_image_extension" value="<?php echo end((explode(".", $data['entry']['image_name']))); ?>">
                        </div>
                        <div class="btn btn-default" style="margin-top: 10px; position: relative;">
                            <a href="#" data-toggle="modal" data-target="#pictures_library_exsisting" ng-click="remove_image()">Select from Gallery</a>
                            <input type="hidden" id="reuse_image" name="reuse_image" value="">
                        </div>
                        <div class="btn btn-default" ng-click="remove_image()" style="margin-top: 10px" ng-show="course.image_name">Remove Image</div>
                        <div class="btn btn-default" ng-click="remove_image()" style="margin-top: 10px; position: relative;">
                            <span ng-show="course.image_name">Change Image</span>
                            <span ng-hide="course.image_name">Upload a picture</span>
                            <input type="file" name="file_upload" onchange="previewFile()" style="opacity: 0; position: absolute; top: 0; right: 0; bottom: 0; left: 0; z-index: 2;">
                            <input type="hidden" id="remove_image" name="remove_image" value="">
                        </div>

                    </div>
                </div>

                <div class="panel panel-default no-padding grey"  ng-hide="school.type_ids == 12">
                    <div class="panel-body">
                        <h2 class="next-heading">URL Name</h2>

                        <div class="input-group">
							<span class="input-group-addon">
								<i class="fa fa-link"></i>
							</span>
                            <input type="text" class="form-control" name="url_name" ng-model="course.url_name">
                        </div>
                    </div>
                </div>

                <div class="panel panel-default no-padding grey"  ng-hide="school.type_ids == 12">
                    <div class="panel-body">
                        <h2 class="next-heading">Youtube Video ID</h2>

                        <div class="input-group">
							<span class="input-group-addon">
								<i class="fa fa-youtube"></i>
							</span>
                            <input type="text" class="form-control" name="youtube" ng-model="course.youtube">
                        </div>


                    </div>
                </div>


                 <div class="panel panel-default no-padding grey"  ng-hide="school.type_ids == 12">
                    <div class="panel-body">
                        <h2 class="next-heading">Enquiry URL</h2>

                        <div class="input-group">
							<span class="input-group-addon">
								<i class="fa fa-link"></i>
							</span>
                            <input type="text" class="form-control" name="enquiry_form_url" ng-model="course.enquiry_form_url">
                        </div>
                    </div>
                </div>


                <div class="panel panel-default no-padding grey"  ng-hide="school.type_ids == 12">
                    <div class="panel-body np">
                        <div class="padding15 npb">
                            <a class="btn btn-default pull-right btn-xs" href="#" data-toggle="modal" data-target="#locations_modal" ng-hide="course.locations.length<1">Add location</a>
                            <h2 class="next-heading">Locations</h2>
                        </div>

                        <div class="text-center" ng-hide="course.locations.length"  ng-hide="school.type_ids == 12">
                            <i class="fa fa-compass" style="font-size: 64px; margin-bottom: 10px; color: #999;"></i>
                            <p style="color: #999;">No locations added yet</p>
                            <a class="btn btn-default btn-xs" href="#" data-toggle="modal" data-target="#locations_modal">Add location</a>
                            <br>
                            <br>
                        </div>
                        <ul style="margin: 0; padding: 0;" class="variant_list">
                            <li ng-repeat="location in course.locations track by $index" style="list-style-type: none;">
                                <span class="options"><i class="fa fa-trash" ng-click="remove_location($index)" style="cursor: pointer;"></i></span>
                                <a href="javascript:;"><span ng-show="location.city">{{location.city}},</span> <span ng-if="location.address_line_1">{{ location.address_line_1}},</span> {{location.country}} </a>
                                <input type="hidden" name="locations[]" value="{{location.id}} ">
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="panel panel-default no-padding grey"  ng-hide="school.type_ids == 12">
                    <div class="panel-body np">
                        <div class="padding15 npb">
                            <a class="btn btn-default pull-right btn-xs" href="#" data-toggle="modal" data-target="#topics_modal" ng-hide="course.topics.length<1">Add tag</a>
                            <h2 class="next-heading">Tags</h2>
                        </div>

                        <div class="text-center" ng-hide="course.topics.length">
                            <i class="fa fa-tags" style="font-size: 64px; margin-bottom: 10px; color: #999;"></i>
                            <p style="color: #999;">No tags added yet</p>
                            <a class="btn btn-default btn-xs" href="#" data-toggle="modal" data-target="#topics_modal">Add tag</a>
                            <br>
                            <br>
                        </div>
                        <ul style="margin: 0; padding: 0;" class="variant_list">
                            <li ng-repeat="topic in course.topics track by $index" style="list-style-type: none;">
                                <span class="options"><i class="fa fa-trash" ng-click="remove_topic($index)" style="cursor: pointer;"></i></span>
                                <a href="javascript:;">{{topic.title}}</a>
                                <input type="hidden" name="topics[]" value="{{topic.id}} ">
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="panel panel-default no-padding grey" ng-hide="school.type_ids == 12">
                    <div class="panel-body np">
                        <div class="padding15 npb">
                            <a class="btn btn-default pull-right btn-xs" href="#" data-toggle="modal" data-target="#requirements_modal" ng-hide="course.requirements.length<1">Add assignment</a>
                            <h2 class="next-heading">Assignments</h2>
                        </div>

                        <div class="text-center" ng-hide="course.requirements.length">
                            <i class="fa fa-bullseye" style="font-size: 64px; margin-bottom: 10px; color: #999;"></i>
                            <p style="color: #999;">No assignments added yet</p>
                            <a class="btn btn-default btn-xs" href="#" data-toggle="modal" data-target="#requirements_modal">Add assignment</a>
                            <br>
                            <br>
                        </div>
                        <ul style="margin: 0; padding: 0;" class="variant_list">
                            <li ng-repeat="requirement in course.requirements track by $index" style="list-style-type: none;">
                                <span class="options"><i class="fa fa-trash" ng-click="remove_requirement($index)" style="cursor: pointer;"></i></span>
                                <a href="javascript:;">{{requirement.title}}</a>
                                <input type="hidden" name="requirements[]" value="{{requirement.id}} ">
                            </li>
                        </ul>
                    </div>
                </div>

            </div>
        </div>

        <div class="bottom_buttons">
            <div class="btn btn-danger pull-left" ng-show="course.id!='new'" data-toggle="modal" data-target="#delete_course_modal" style="margin-right: 15px;">Delete course</div>
            <div class="btn btn-default pull-left" ng-show="course.id!='new'" ng-click="show_duplicate_modal()">Duplicate course</div>
            <?php if ($data['entry']['id'] == "new") { ?>
                <input type="submit" name="add_course" value="Add course" class="btn btn-sm btn-primary">
            <?php } else { ?>
                <input type="submit" name="save_course" value="Save" class="btn btn-sm btn-primary">
            <?php } ?>
        </div>

    </form>

    <div class="modal fade" id="pictures_library_exsisting" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">Picture Gallery</h4>
                </div>
                <div class="modal-body" style="overflow-y: scroll;max-height: 390px;">

                    <?php
                    $path = "/var/www/vhosts/heiapply.com/httpdocs/static/" . $this->school_info['subdomain'] . "/blog_media/programmes/";
                    //$path = "/var/www/heiapplylocal.co.uk/html/static/".$this->school_info[subdomain]."/blog_media/programmes/";
                    $files = glob($path . "*.{jpg,png,jpeg,JPG,PNG,JPEG}", GLOB_BRACE);
                    foreach ($files as $media) {
                        $path_parts = pathinfo($media);
                        $url = $this->school_info["href"] . "/static/" . $this->school_info["subdomain"] . "/blog_media/programmes/" . $path_parts['basename'];
                        ?>
                        <img id='<?php echo $path_parts['basename']; ?>' class="image" src="<?php echo $url; ?>">
                        <?php
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    <style type="text/css">
        .image {
            width: auto;
            height: 100px;
            float: left;
            margin: 10px;
        }

        .selected {
            border: 3px solid #00ff00;
        }
    </style>
    <script type="text/javascript">
		$(document).ready(function() {
			$(".image").on('click', function() {
				$('.image').removeClass('selected');
				$(this).addClass('selected');
				sendToDatabase($(this).attr('id'), $(this).attr('src'));
			});

			function sendToDatabase(id, src) {
				$('.test').text("adding to database element " + id + " " + src);
				$('#reuse_image').val("programmes/" + id);
				var preview = $("#preview_image");
				preview.css({
					"background-image": "url(" + src + ")"
				});

				$('#pictures_library_exsisting').modal('hide');
				//$.post(url,{id: data}, function(){
				//
				//});
			}
		});
    </script>
    <!------------------------------------
    ** Locations
    -------------------------------------->
    <div class="modal fade" id="locations_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">Locations</h4>
                </div>
                <div class="modal-body">
                    <p>Choose a location to add:</p>
                    <div style="padding: 5px; background-color: #f1f1f1; border-radius: 5px; height: 400px; overflow: auto;">
                        <table class="table table-striped" style="background-color: #fff; margin: 0;">
                            <tbody>
                            <tr ng-repeat="location in locations">
                                <td><a href="javascript: return false;" ng-click="add_to_locations(location)"><span ng-show="location.city">{{location.city}},</span> {{ location.address_line_1}} - {{ location.country }}</a></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!------------------------------------
    ** Intakes
    -------------------------------------->
    <div class="modal fade" id="intakes_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">
                        <span ng-show="intake.id">Edit intake</span>
                        <span ng-hide="intake.id">Add new intake</span>
                    </h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <form id="intake_modal_form">
                            <div class="col-sm-12">
                                <label>Title</label>
                                <input type="text" ng-model="intake.title" class="form-control ssb" id="intake_title">
                            </div>

                            <div class="col-sm-6">
                                <label>Start date</label>
                                <input type="date" ng-model="intake.start_date" id="intake_start_date" class="form-control ssb" value="{{intake.start_date}}" />
                            </div>

                            <div class="col-sm-6">
                                <label>End date</label>
                                <input type="date" ng-model="intake.end_date" class="form-control ssb">
                            </div>

                            <div class="col-sm-6 hide">
                                <label>End time</label>
                                <select class="form-control ssb">
                                    <option value="">Choose</option>
                                    <option value="100">0100</option>
                                    <option value="200">0200</option>
                                    <option value="300">300</option>
                                    <option value="400">0400</option>
                                    <option value="500">0500</option>
                                    <option value="600">0600</option>
                                    <option value="700">0700</option>
                                    <option value="800">0800</option>
                                    <option value="900">0900</option>
                                    <option value="1000">1000</option>
                                    <option value="1100">1100</option>
                                    <option value="1200">1200</option>
                                    <option value="1300">1300</option>
                                    <option value="1400">1400</option>
                                    <option value="1500">1500</option>
                                    <option value="1600">1600</option>
                                    <option value="1700">1700</option>
                                    <option value="1800">1800</option>
                                    <option value="1900">1900</option>
                                    <option value="2000">2000</option>
                                    <option value="2100">2100</option>
                                    <option value="2200">2200</option>
                                    <option value="2300">2300</option>
                                    <option value="2400">2400</option>
                                </select>
                            </div>

                            <div class="col-sm-6">
                                <label>Application deadline date</label>
                                <input type="date" ng-model="intake.application_deadline_date" class="form-control ssb">
                            </div>

                            <div class="col-sm-6">
                                <label>Application deadline time</label>
                                <select class="form-control ssb" ng-model="intake.application_deadline_time">
                                    <option value="">Choose...</option>
                                    <option value="100">0100</option>
                                    <option value="200">0200</option>
                                    <option value="300">0300</option>
                                    <option value="400">0400</option>
                                    <option value="500">0500</option>
                                    <option value="600">0600</option>
                                    <option value="700">0700</option>
                                    <option value="800">0800</option>
                                    <option value="900">0900</option>
                                    <option value="1000">1000</option>
                                    <option value="1100">1100</option>
                                    <option value="1200">1200</option>
                                    <option value="1300">1300</option>
                                    <option value="1400">1400</option>
                                    <option value="1500">1500</option>
                                    <option value="1600">1600</option>
                                    <option value="1700">1700</option>
                                    <option value="1800">1800</option>
                                    <option value="1900">1900</option>
                                    <option value="2000">2000</option>
                                    <option value="2100">2100</option>
                                    <option value="2200">2200</option>
                                    <option value="2300">2300</option>
                                    <option value="2400">2400</option>
                                </select>
                            </div>

                            <div class="col-sm-6">
                                <label>Year/Cohort</label>
                                <select ng-model="intake.year" class="form-control ssb">
                                    <option ng-repeat="c in cohorts" value="{{c.title}}">{{c.title}}</option>
                                </select>
                            </div>

                            <div class="col-sm-6">
                                <label>Term</label>
                                <select ng-model="intake.term" class="form-control ssb">
                                    <option value="">Choose...</option>
                                    <option value="Summer">Summer</option>
                                    <option value="Fall">Fall</option>
                                    <option value="Spring">Spring</option>
                                    <option value="Spring_Break">Spring Break</option>
                                    <option value=" Winter_Break"> Winter Break</option>
                                </select>
                            </div>

                            <div class="col-sm-6">
                                <label>Location</label>
                                <select ng-model="intake.location" class="form-control ssb">
                                    <option ng-repeat="l in locations" value="{{l.id}}">{{l.title}}</option>
                                </select>
                            </div>

                            <div class="col-sm-6">
                                <label>Applicant Type</label>
                                <select ng-model="intake.applicant_type_id" class="form-control ssb">
                                    <option></option>
                                    <option ng-repeat="type in applicant_types" ng-selected="intake.applicant_type.id == type.id" value="{{type.id}}">{{type.title}}</option>
                                </select>
                                <!-- <select ng-model="intake.applicant_type_id"  ng-options="option.id as option.title for option in applicant_types" class="form-control ssb">
                          </select> -->
                            </div>

                            <div class="col-sm-6">
                                <label>Study Mode</label>
                                <select ng-model="intake.study_mode" class="form-control ssb">
                                    <option value=""></option>
                                    <option value="full_time">Full Time</option>
                                    <option value="part_time_day">Part Time Day</option>
                                    <option value="part_time_evening">Part Time Evening</option>
                                    <option value="part_time_weekend">Part Time Weekend</option>
                                    <option value="work-based">Work Based</option>
                                    <option value="distance_learning">Distance Learning</option>
                                </select>
                            </div>

                            <div class="col-sm-6">
                                <label>Max. Capacity</label>
                                <input type="number" string-to-number ng-model="intake.max_capacity" class="form-control ssb">
                            </div>

                            <div class="col-sm-6 hide">
                                <label>Course Link</label>
                                <input type="text" ng-model="intake.course.id" class="form-control ssb">
                            </div>

                            <div class="col-sm-6">
                                <label>Course Amount</label>
                                <div class="input-group ssb" ng-if="school.id !=27">
                                    <span class="input-group-addon"><?php echo $this->school_info['currency']['symbol']; ?></span>
                                    <input type="number" ng-model="intake.amount" class="form-control ">
                                </div>
                                <input type="text" ng-if="school.id == 27" ng-model="intake.amount" class="form-control" />
                            </div>

                            <div class="col-sm-6">
                                <label>Code</label>
                                <input type="text" ng-model="intake.code" class="form-control ssb">
                            </div>

                            <div class="col-sm-6">
                                <label>Duration</label>
                                <input type="text" ng-model="intake.duration" class="form-control ssb">
                            </div>

                            <div class="col-sm-6">
                                <label>Publish</label>
                                <select ng-model="intake.publish" class="form-control ssb">
                                    <option value="">Choose..</option>
                                    <option value="yes">Yes</option>
                                    <option value="no">No</option>
                                </select>
                            </div>
                        </form>
                    </div>

                </div>
                <div class="modal-footer">
                    <input type="button" class="btn btn-primary" ng-click="save_intake()" value="Save intake">
                </div>
            </div>
        </div>
    </div>


    <!------------------------------------
    ** Topics
    -------------------------------------->
    <div class="modal fade" id="topics_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">Tags</h4>
                </div>
                <div class="modal-body">
                    <p>Choose a tag to add:</p>
                    <div style="padding: 5px; background-color: #f1f1f1; border-radius: 5px; height: 400px; overflow: auto;">
                        <table class="table table-striped" style="background-color: #fff; margin: 0;">
                            <tbody>
                            <tr ng-repeat="topic in topics">
                                <td><a href="javascript: return false;" ng-click="add_to_topics(topic)">{{ topic.title }}</a></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!------------------------------------
    ** Topics
    -------------------------------------->
    <div class="modal fade" id="requirements_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">Assignments</h4>
                </div>
                <div class="modal-body">
                    <p>Choose an assignment to add:</p>
                    <div style="padding: 5px; background-color: #f1f1f1; border-radius: 5px; height: 400px; overflow: auto;">
                        <table class="table table-striped" style="background-color: #fff; margin: 0;">
                            <tbody>
                            <tr ng-repeat="requirement in requirements">
                                <td><a href="javascript: return false;" ng-click="add_to_requirements(requirement)">{{ requirement.title }}</a></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!------------------------------------
    ** Sessions
    -------------------------------------->
    <div class="modal fade" id="course_sessions_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">
                        <span ng-show="session.id">Edit session</span>
                        <span ng-hide="session.id">Add new session</span>
                    </h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <form id="course_session_modal_form">
                            <div class="col-sm-12">
                                <label>Name</label>
                                <input type="text" ng-model="course_session.name" class="form-control ssb" id="course_session_name">
                            </div>

                            <div class="col-sm-12">
                                <label>Description</label>
                                <textarea ng-model="course_session.description" id="course_session_description" class="form-control ssb">{{course_session.description}}</textarea>
                            </div>

                            <div class="col-sm-12">
                                <label>Order</label>
                                <input type="number" ng-model="course_session.order" min="1" class="form-control ssb" id="course_session_order">
                            </div>
                            <div class="col-sm-12 hide">
                                <label>Course Link</label>
                                <input type="text" ng-model="course_session.course.id" class="form-control ssb">
                            </div>

                        </form>
                    </div>

                </div>
                <div class="modal-footer">
                    <input type="button" class="btn btn-primary" ng-click="save_course_session()" value="Save session">
                </div>
            </div>
        </div>
    </div>

    <!------------------------------------
    ** Material Links
    -------------------------------------->
    <div class="modal fade" id="course_materials_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">
                        <span ng-show="session.id">Edit material link</span>
                        <span ng-hide="session.id">Add new material link</span>
                    </h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <form id="course_material_modal_form">
                            <div class="col-sm-12">
                                <label>Name</label>
                                <input type="text" ng-model="course_material.name" class="form-control ssb" id="course_material_name">
                            </div>

                            <div class="col-sm-12">
                                <label>Type</label>
                                <select ng-model="course_material.type" class="form-control ssb" id="course_material_type">
                                    <option></option>
                                    <option ng-repeat="material_type in course.course_material_types" ng-selected="course_material.type == material_type.id" value="{{material_type.id}}">{{material_type.name}}</option>
                                </select>
                            </div>

                            <div class="col-sm-12">
                                <label>Link</label>
                                <input type="text" ng-model="course_material.link" class="form-control ssb" id="course_material_link">
                            </div>
                            <div class="col-sm-12">
                                <label>Password</label>
                                <input type="text" ng-model="course_material.password" class="form-control ssb" id="course_material_password">
                            </div>
                            <div class="col-sm-12">
                                <label>Status</label>
                                <select ng-model="course_material.status" class="form-control ssb" id="course_material_status">
                                <option></option>
                                <option ng-repeat="material_status in course.course_material_statuses" ng-selected="course_material.status == material_status" value="{{material_status}}">{{material_status}}</option>
                                </select>

                            </div>
                            <div class="col-sm-12">
                                <label>Link to Session</label>
                                <select ng-model="course_material.course_session" class="form-control ssb" id="course_material_course_session">
                                <option></option>
                                <option ng-repeat="course_session in course.course_sessions" ng-selected="course_material.course_session == course_session.id" value="{{course_session.id}}">{{course_session.name}}</option>
                                </select>

                            </div>
                            <div class="col-sm-12 hide">
                                <label>Course</label>
                                <input type="text" ng-model="course_material.course.id" class="form-control ssb" id="course_material_course">
                            </div>

                        </form>
                    </div>

                </div>
                <div class="modal-footer">
                    <input type="button" class="btn btn-primary" ng-click="save_course_material()" value="Save material link">
                </div>
            </div>
        </div>
    </div>





    <!-- Short codes modal -->
    <?php include(ABSPATH . 'app/views/online_courses/short_codes_modal.php'); ?>







    <script>
		$(function() {
			//Delete Unit Modal
			$(".show_delete_unit_modal").click(function() {
				var uid = $(this).attr("id");
				$(".unit_to_delete").val(uid);
				$("#delete_unit_modal").modal("show");
				return false;
			});


			//Delete Module Modal
			$(".show_delete_module_modal").click(function() {
				var uid = $(this).attr("id");
				$(".module_to_delete").val(uid);
				$("#delete_module_modal").modal("show");
				return false;
			});

			//Sortable Units
			$(".sortable").sortable({
				connectWith: ".connectedSortable",
				update: function(event, ui) {
					var course_id = '<?php echo $slugs['3']; ?>';
					var module_id = $(this).attr("id");
					var order = $(this).sortable("serialize") + '&action=save_units_order' + '&course_id=' + course_id + '&module_id=' + module_id;
					//console.log($(this).sortable("serialize"));
					$.ajax({
						data: order,
						type: 'POST',
						url: '<?php echo $this->base_url(); ?>/ajax/online_learning.php'
					});
				}
			}).disableSelection();

			//sortable Modules
			$("#modules_wrap").sortable({
				update: function(event, ui) {
					var course_id = '<?php echo $slugs['3']; ?>'
					var order = $(this).sortable("serialize") + '&action=save_modules_order' + '&course_id=' + course_id;
					//console.log($(this).sortable("serialize"));
					$.ajax({
						data: order,
						type: 'POST',
						url: '<?php echo $this->base_url(); ?>/ajax/online_learning.php'
					});
				}
			}).disableSelection();
		});

		$('#intakes_modal').on('hide.bs.modal', function(e) {
			// document.getElementById("intake_modal_form").reset();

		});
    </script>
</div>
<style>
    .pagination {
        height: 40px;
        margin: 20px 0;
    }

    .pagination ul {
        display: inline-block;
        *display: inline;
        *zoom: 1;
        margin-left: 0;
        margin-bottom: 0;
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
        -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        -moz-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .pagination ul>li {
        display: inline;
    }

    .pagination ul>li>a,
    .pagination ul>li>span {
        float: left;
        padding: 0 14px;
        line-height: 38px;
        text-decoration: none;
        background-color: #ffffff;
        border: 1px solid #dddddd;
        border-left-width: 0;
    }

    .pagination ul>li>a:hover,
    .pagination ul>.active>a,
    .pagination ul>.active>span {
        background-color: #f5f5f5;
    }

    .pagination ul>.active>a,
    .pagination ul>.active>span {
        color: #999999;
        cursor: default;
    }

    .pagination ul>.disabled>span,
    .pagination ul>.disabled>a,
    .pagination ul>.disabled>a:hover {
        color: #999999;
        background-color: transparent;
        cursor: default;
    }

    .pagination ul>li:first-child>a,
    .pagination ul>li:first-child>span {
        border-left-width: 1px;
        -webkit-border-radius: 3px 0 0 3px;
        -moz-border-radius: 3px 0 0 3px;
        border-radius: 3px 0 0 3px;
    }

    .pagination ul>li:last-child>a,
    .pagination ul>li:last-child>span {
        -webkit-border-radius: 0 3px 3px 0;
        -moz-border-radius: 0 3px 3px 0;
        border-radius: 0 3px 3px 0;
    }

    .pagination-centered {
        text-align: center;
    }

    .pagination-right {
        text-align: right;
    }
</style>