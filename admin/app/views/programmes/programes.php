<?php 
	$filter_type = $data['filter_types'];
    $published_status = $data['published_status'] == 1 ?1:0;

	$default_filters_list = array();
	foreach ($filter_type as $fff) {
		$default_filters_list[] = ucwords($fff['title']);
	}

	$default_filters_list = array_values($default_filters_list);

	$filter_types_json = json_encode($filter_type);

	$filter_type_small = array();
	foreach($filter_type as $ff){
		if(!$ff['extra']){
			$ff['options'] = '';
			$filter_type_small[] = $ff;
		}
	}


	$filter_type_small = json_encode($filter_type_small);


	$filters = $data['filter']['description'];
	if(!$filters){ $filters =array();}
	$filters_json = json_encode($filters);
?>
<script>
	app.controller('demo', ['$scope','$filter','$http','$compile', 'fileUpload', function($scope,$filter,$http,$compile, fileUpload){

	  $scope.dimensions = <?php echo json_encode($data['dimensions']); ?>;
	  $scope.filters = <?php echo $filters_json ?>;
	  $scope.main_filter_types = <?php if($data['filter_types']){ echo json_encode($data['filter_types']); }else{ echo "[]";} ?>;
	  $scope.filter_types = <?php if($filter_types_json){ echo $filter_types_json;}else{ echo "[]";} ?>;
	  $scope.filter_types_small = <?php if($filter_type_small){ echo $filter_type_small;}else{ echo "[]";} ?>;
	  $scope.filter = <?php if($this->filter){ echo json_encode($this->filter);}else{ echo "[]";} ?>;
	  $scope.default_filters_list = <?php if($default_filters_list){ echo json_encode($default_filters_list);}else{ echo "[]";} ?>;
	  $scope.filter_search_keyword = "";
	  $scope.total_entries = <?php echo $paginator->total ?: 0; ?>;
	  console.log($scope.default_filters_list);
	 	

	  /* Load Filters
  	----------------------------------------------*/
 		filters_args = {
		  'search': '<?php echo $_REQUEST['search']; ?>',
		  'value': '<?php echo $_REQUEST['search']; ?>',
		  'filter': $scope.filter,
		 <?php if($data['hide_filters']){ ?> 'hide_filters':1 <?php } ?>
		};	
 		var filters_wrap_html = filter_html(filters_args);

 		angular.element(document.getElementById('filters_wrap')).append($compile(filters_wrap_html)($scope));
	  

 		/* View Filters
  	----------------------------------------------*/
	  $scope.view_filters = function view_filters() {
	  	$scope.create_temp_filters();
	  	if(typeof $scope.filters !== 'undefined') {
		  	if($scope.filters.length<1){
		  		$scope.add_filter();
		  	}
	  	}else{
	  		$scope.add_filter();
	  	}
	  	$("#filters_modal").modal("show");
	  }

	  $scope.delete_filter = function delete_filter(index) {
	  	$scope.filters.splice(index, 1); 
	  	$scope.create_temp_filters();
	  	$scope.apply_filters();
  		return false; 
	  }

	  $scope.delete_temp_filter = function delete_temp_filter(index) {
	  	$scope.edit_filters.splice(index, 1); 
  		return false; 
	  }

	  
	  $scope.get_field_options = function get_field_options(filter) {

	  	if(filter.type=="checklist_id"){
	  		$http({
			    url: "ajax/students.php",
			    method: "POST",
			    data: {action:"get_checklist_stage",route_id:filter.option}
				}).success(function(data, status, headers, config) {	
					
					angular.forEach($scope.main_filter_types, function(type){
		  			if(type.id=="application_stage"){
		  				console.log(type.id+"=="+"application_stage");
		  				type.option = "";
		  				type.options = data;
		  			}
				  });


				  angular.forEach($scope.edit_filters, function(fi){
		  			if(fi.type=="application_stage"){
		  				fi.option = "";
		  				fi.options = data;
		  			}
				  });
				}).error(function(data, status, headers, config) {
			    
				});
	  	}
	  }


	  $scope.filter_type_info = function filter_type_info(filter_type) {
	  	ftype = "";
	  	angular.forEach($scope.main_filter_types, function(type){
  			if(type.id==filter_type){
  				ftype = type;
  			}
		  });

		  console.log("ftype");
		  console.log(ftype);

		  if(!ftype){
		  	angular.forEach($scope.filter_types, function(type){
		  		console.log(type.id+"---"+filter_type);
	  			if(type.id==filter_type){
	  				ftype = type;
	  			}
			  });
		  }
		  return ftype;
	  }

	  $scope.option_title = function option_title(option,filter) {
	  	option_name = "";
	  	if(filter.options){
		  	angular.forEach(filter.options, function(option_info){
	  			if(option_info.value==option){
	  				option_name = option_info.label;
	  			}
			  });
	  	}

		  return option_name;
	  }

	  $scope.filter_title = function filter_title(filter) {
	  	type_info = $scope.filter_type_info(filter.type);
		  return type_info.title;
	  }

	  $scope.show_filter_dropdown = function show_filter_dropdown(filterIndex) {
			$(".filter_select#"+filterIndex+" ul").toggle();
			$(".filter_select#"+filterIndex+" ul input").focus();
		}

		$scope.apply_selected_filter = function apply_selected_filter(filterIndex,itemChoosen) {
			
			$scope.edit_filters[filterIndex].type = itemChoosen.id;
			$scope.fix_filter_options($scope.edit_filters[filterIndex]);
			$(".filter_select#"+filterIndex+" ul").hide();
			$scope.edit_filters[filterIndex].filter_search_keyword = "";
			$scope.$apply();
		}

	  $scope.fix_filter_options = function fix_filter_options(filter) {	
	  	type_info = $scope.filter_type_info(filter.type);

	  	
	  	filter.value = "";
	  	filter.option = "";
	  	filter.sql = type_info.sql;
	  	if(typeof type_info.extra !== 'undefined') {
		  	filter.extra = type_info.extra;
		  	filter.operator_type = 'Exactly matching' ;
			}else{
				delete filter.extra;
			}

			if(typeof type_info.options !== 'undefined') {
		  	filter.options = type_info.options;
			}else{
				delete filter.options;
			}

			if(typeof type_info.operators !== 'undefined') {
		  	filter.operators = type_info.operators;
			}else{
				delete filter.operators;
			}

			//Get dynamic listing
			if(filter.sql){

				$scope.form_data = {action: 'get_dynamic_field_values', sql: filter.sql }
		  	$("#SOMETHING").html("Loading...");

				$http({
			    url: "ajax/students.php",
			    method: "POST",
			    data: $scope.form_data
				}).success(function(data, status, headers, config) {
					filter.options = data;
				}).error(function(data, status, headers, config) {
			    
				});

			}

	  }


	  $scope.validate_operator_value = function validate_operator_value(filter) {
	  	if(filter.operator_type!='Is empty'){
	  		filter.value = "";
	  	}
	  }

	  $scope.add_filter = function add_filter() {
	  	var new_field = {type: '',title:'',status:'',join:'and'};
			$scope.edit_filters.push(new_field);
	  }

	  
	  $scope.create_temp_filters = function create_temp_filters() {
	  	$scope.edit_filters = angular.copy($scope.filters);
	  	fcount = 1;
	  	angular.forEach($scope.edit_filters, function(filter){

	  		console.log("||||");
		  	console.log(filter);


		  	if(!filter.join){
		  		filter.join = 'and';
		  	}

		  	if(fcount==1){
		  		filter.join = 'and';
		  	}
		  	fcount++;
		  });
	  }

	  $scope.apply_filters = function apply_filters() {
	  	
	  	pop_message('Loading...',false);

	  	angular.copy($scope.edit_filters, $scope.filters);
  		angular.forEach($scope.filters, function(filter){
		  	filter.status = 'active';
		  	//filter.options = '';
		  	
		  	filter_title = "";
		  	angular.forEach($scope.dimensions[0]['All Fields'], function(option_info){
		  		console.log("BBBBBB");
	  			console.log(filter.type+"=="+option_info.id);
		  		if(filter.type==option_info.id){
		  			filter_title = option_info.title;
		  		}
		  	});
		  	filter.title = filter_title;
		  	console.log("AAAAA");
	  		console.log(filter);
		  });

  		// console.log("DDDD");
	  	// console.log(filter);
	  	// console.log($scope.dimensions[0]['All Fields']);
	  	// angular.forEach(dimensions[0]['All Fields'], function(option_info){
	  	// 	if(filter.id==option){
	  	// 		option_name = option_info.label;
	  	// 	}
	  	// });

		  
	  	

  		// return false;

		  $("#filters_modal .btn-primary").html("Loading...").addClass("disabled");

		  // console.log($scope.filters);
		  // return false;
		  // return false;
		  var filters_json = JSON.stringify($scope.filters);
		  
		  $("#main_search_form_filters").val(filters_json);
		  $("#main_search_form_action").val("update_filter");
		  $("#main_search_form").submit();
	  }

	  	
	  $scope.apply_fields = function apply_fields(dimension) {
	  	$scope.form_data = {fields: $scope.dimensions, action: 'set_session_student_fields', page:'<?php echo $data['filter_page_name'];?>', filter_id:'<?php echo $this->filter['id'];?>' }
	  	$("#dimensions_dropdown .bottom_apply_button .btn").html("Loading...");

			$http({
		    url: "ajax/students.php",
		    method: "POST",
		    data: $scope.form_data
			}).success(function(data, status, headers, config) {
				window.location.replace(location.href);
			}).error(function(data, status, headers, config) {
		    
			});
	  }




		

	}]);

	app.filter('removeCharacter', function () {
	    return function (text, filter) {
	        var str = text.replace('>', '');
	        str = str.replace('<', '');
	        str = str.replace('=', '');
	        filter.value = str;
	        return str;
	    };
	});
	$(document).ready(function(){

		$('#dimensions_dropdown').click(function(e) {
		    e.stopPropagation();
		});

		$('#export_btn').click(function(e) {
		    $(this).html("Loading...").addClass("disabled");
		});
		$('#export_to_ukvi_btn').click(function(e) {
			$(this).html("Loading...").addClass("disabled");
		});

		// $('body').on('click', '.dynamic_download', function(e){
		// 	///e.preventDefault();
		// 	var $scope = angular.element($('[ng-controller="demo"]')).scope();
		// 	///$scope.download_dynamic_file($(this).closest('a').attr('href'));
		// });

		jQuery(".main_table .table_wrap").on('scroll', function() {
			that = jQuery(this);
		  if(that.scrollLeft() >= 1) {
		  	$("#applicants_table .shadow").show();
			}else{
				$("#applicants_table .shadow").hide();
			}
		});

		var frozen_fields_width = $("#applicants_table .pics").width();
		$("#applicants_table .main_table").css({'margin-left':frozen_fields_width+"px"});
		$("#applicants_table .shadow").css({'left':frozen_fields_width+"px"});


		//$("#filters_modal").modal("show");		
        $(document).on("click","a[title='Copy Link']", function (e) {
        	e.preventDefault();
		    var hrefval = $( this).attr("href");
		 	var copiedHref = $('<input>').val(hrefval).appendTo('body').select();
		 	document.execCommand('copy');
		});

	});
</script> 
<div  ng-controller="demo" class="inner_container">
	<div class="breadcrumbs">
		<?php
			$back_link = $this->base_url('course_levels/');
			if($data['cohort']){
				$back_link = $back_link."?cohort=".$data['cohort'];
			}
		?>
		<a href="<?php echo $back_link; ?>"><i class="fa fa-chevron-left" aria-hidden="true"></i> <?= terminology('Course Levels', $this->current_url(), 'Back Link (Breadcrumb) Text') ?></a>
	</div>

	<div class="top_section">
        <div class="buttons_wrap">

            <div class="btn-group pull-right" style="margin-bottom: 20px; margin-right: 5px;">
              <?php $page = 'course_levels/programmes/'.$data['level_id']?>
              <?php $published_text = $data['meta_title']=='Programmes'?'View On':'View Published'?>
              <?php if($_SESSION['usergroup'] == 52 && isset($published_status)) echo'<a href="'.$this->base_url($page.'?published_status=').($published_status==1?0:1).'" class="btn btn-default btn-lg" id="toggle_published"><span class="glyphicon glyphicon-'.(($published_status==1)?'asterisk':'ok').'"></span> '.(($published_status==1)?'View All':$published_text).'</a>' ?>
            </div>

        </div>
		<h1>
			<?php echo $data['level']['title']; ?> Programmes
		</h1>
	</div>

	<?php if($data['filtering_tools']){ require(ABSPATH."app/views/applicants/filter_tools_bar.php") ;}?>

	<div class="row programmes_boxes">
		<?php 
			foreach ($data['results'] as $entry){
				$link = $this->base_url('course_levels/applicants/'.$data['level_id'].'/'.$entry["id"]);
				$link_fv = $this->base_url('course_levels/applicants/'.$data['level_id'].'/'.$entry["id"].'/fv');
				$submitted_fv = $link .'/submitted_fv';
				$submitted = $link .'/submitted';
				$offers = $link .'/offers';
				$offers_fv = $link .'/offers_fv';
				$accepted = $link .'/accepted';
				$accepted_fv = $link .'/accepted_fv';
				if($data['cohort']){
					$coh = "?cohort=".$data['cohort'];
					$link = $link. $coh;
					$link_fv = $link_fv. $coh;
					$submitted .= $coh;
					$submitted_fv .= $coh;
					$offers .= $coh;
					$offers_fv .= $coh;
					$accepted .= $coh;
					$accepted_fv .= $coh;
				}
		?>
		<div class="col-xs-4 tile_box" id="box_<?php echo $entry['id'] ?>">
			<div class="box">
				<div class="title">
					<input type="checkbox" name="selected[]" class="tile_selected_box" value="<?php echo $entry['id'] ?>">
					<div class="btn-group btn-group-sm pull-right" style="margin-top: -4px; ">
					  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
					    <span class="glyphicon glyphicon-cog" aria-hidden="true"></span>
					  </button>
					  <ul class="dropdown-menu">
					    <li><a href="<?php echo $link; ?>"><?= terminology('View Applicants', $this->current_url(), 'Programme Box Dropdown on Settings Icon') ?></a></li>
					    <li><a href="#" form_modal="18" id="<?php echo $entry["id"]; ?>"><?= terminology('Edit Programme', $this->current_url(), 'Programme Box Dropdown on Settings Icon') ?></a></li>
					  </ul>
					</div>
					<a href="<?php echo $link; ?>" class="main_title" title="<?php echo $entry["title"] ?>"><?php echo $entry["title"] ?></a>
				</div>
				<div  class="content">
					<div class="row">

					  <div class="tab-content row col-sm-12" style="min-height: 345px;">
					    <div id="overall_<?php echo $entry["id"]; ?>" class="tab-pane fade in active col-sm-12">
						      <div class="row">
								<div class="col-sm-6"><a href="<?php echo $link; ?>">Profiles Created</a></div>
								<div class="col-sm-6"><div class="stat"><?php if($entry["total_applicants"]){ echo $entry["total_applicants"]; }else{ echo "-"; } ?></div></div>

								<div class="col-sm-6"><a href="<?php echo $submitted; ?>">Applications Submitted</a></div>
								<div class="col-sm-6"><div class="stat"><?php if($entry["total_submitted"]){ echo $entry["total_submitted"]; }else{ echo "-"; } ?></div></div>
		                        
								<div class="col-sm-6"><a href="<?php echo $offers; ?>">Offers Made</a></div>
								<div class="col-sm-6"><div class="stat"><?php if($entry["total_offers"]){ echo $entry["total_offers"]; }else{ echo "0"; } ?></div></div>
		                        
								<div class="col-sm-6"><a href="<?php echo $accepted; ?>">Offers Accepted</a></div>
								<div class="col-sm-6"><div class="stat"><?php if($entry["total_offers_accepted"]){ echo $entry["total_offers_accepted"]; }else{ echo "-"; } ?></div></div>
		                        
								<div class="col-sm-6"><a href="<?php echo $enrolled; ?>">Applicants Enrolled</a></div>
								<div class="col-sm-6"><div class="stat"><?php if($entry["total_enrolled"]){ echo $entry["total_enrolled"]; }else{ echo "-"; } ?></div></div>
								<div class="col-sm-6"><a href="<?php echo $enrolled; ?>">Rejected</a></div>
								<div class="col-sm-6"><div class="stat"><?php if($entry["total_rejected"]){ echo $entry["total_rejected"]; }else{ echo "-"; } ?></div></div>
								<div class="col-sm-6"><a href="<?php echo $enrolled; ?>">Withdrawn</a></div>
								<div class="col-sm-6"><div class="stat"><?php if($entry["total_withdrawn"]){ echo $entry["total_withdrawn"]; }else{ echo "-"; } ?></div></div>
							</div>
							<hr>
							<span class="active_pill_content_<?php echo $entry["id"]; ?> small">The total number of profiles and applications at each specific application status</span>
					    </div>
					    <div id="current_<?php echo $entry["id"]; ?>" class="tab-pane fade hidden col-sm-12">
						      <div class="row">
								<div class="col-sm-6"><a href="<?php echo $link_fv; ?>">Profiles Created</a></div>
								<div class="col-sm-6"><div class="stat"><?php if($entry["total_applicants_fv"]){ echo $entry["total_applicants_fv"]; }else{ echo "-"; } ?></div></div>

								<div class="col-sm-6"><a href="<?php echo $submitted_fv; ?>">Applications Submitted</a></div>
								<div class="col-sm-6"><div class="stat"><?php if($entry["total_submitted_fv"]){ echo $entry["total_submitted_fv"]; }else{ echo "-"; } ?></div></div>
		                        
								<div class="col-sm-6"><a href="<?php echo $offers_fv; ?>">Offers Made</a></div>
								<div class="col-sm-6"><div class="stat"><?php if($entry["total_offers_fv"]){ echo $entry["total_offers_fv"]; }else{ echo "0"; } ?></div></div>
		                        
								<div class="col-sm-6"><a href="<?php echo $accepted_fv; ?>">Offers Accepted</a></div>
								<div class="col-sm-6"><div class="stat"><?php if($entry["total_offers_accepted_fv"]){ echo $entry["total_offers_accepted_fv"]; }else{ echo "-"; } ?></div></div>
		                        
								<div class="col-sm-6"><a href="<?php echo $enrolled; ?>">Applicants Enrolled</a></div>
								<div class="col-sm-6"><div class="stat"><?php if($entry["total_enrolled"]){ echo $entry["total_enrolled"]; }else{ echo "-"; } ?></div></div>
							</div><hr>
							<span class="active_pill_content_<?php echo $entry["id"]; ?> small"></span>
					    </div>
					</div>
                    
                <!--and or buttons -->
				<div class="ui-group-buttons hidden">
                <a href="#current_<?php echo $entry["id"]; ?>" data-toggle="pill" class="btn btn-xs btn-primary  pills_<?php echo $entry["id"]; ?>" data-pills="<?php echo $entry["id"]; ?>" title="The total number of profiles and applications at each specific application status">Current View</a>
                <div class="or or-xs"></div>
                <a href="#overall_<?php echo $entry["id"]; ?>" data-pills="<?php echo $entry["id"]; ?>" data-toggle="pill" class="btn btn-default btn-xs pills_<?php echo $entry["id"]; ?>" title="The total number of profiles and applications in your selected recruitment cycle">Funnel View</a>
                 </div>
                <!--and or buttons end -->
                
						<div class="col-sm-12"><hr/></div>

						<div class="col-sm-6">Launch Date</div>
						<div class="col-sm-6"><div class="stat"><?php if($_GET['deadline_time']){ echo $_GET['deadline_time']; }else{ echo "-"; }; ?></div></div>

						<div class="col-sm-6">Close Date/Time</div>
						<div class="col-sm-6">
							<div class="stat">
								<?php
									if($entry['three_year_route']){
										if(strpos($entry['three_year_route'], '-') !== false){
											if($entry['three_year_route']=="0000-00-00"){
												echo "-";
											}else{
												echo $entry['three_year_route'];
											}
										}else{
											echo "-";
										}
									}else{
										echo "-";
									};
								?>
							</div>
						</div>
					</div>
				</div>
				
			</div>
		</div>
		<?php } ?>

		<div class="col-xs-4">
			<a class="box add_box thickbox" href="<?php echo $this->engine_url('controller.php?width=850&height=600&pick_page=18&ref=&jqmRefresh=true'); ?>">
				<div class="title">
					<?= terminology('Create Programme', $this->current_url(), 'New Programme Box Title') ?>
				</div>
				<div class="content text-center">
					<span class="glyphicon glyphicon-plus  add_icon" aria-hidden="true"></span>
				</div>
				
			</a>
		</div>

	<!-- Modal -->
<?php include(ABSPATH."app/views/applicants/inc_filter_modals.php") ?>
	
</div>