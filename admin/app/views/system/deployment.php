<?php 
	$filter_type = $data['filter_types'];

	$default_filters_list = array();
	foreach ($filter_type as $fff) {
		$default_filters_list[] = ucwords($fff['title']);
	}

	$default_filters_list = array_values($default_filters_list);

	$filter_types_json = json_encode($filter_type);

	$filter_type_small = array();
	foreach($filter_type as $ff){
		if(!$ff['extra']){
			$ff['options'] = '';
			$filter_type_small[] = $ff;
		}
	}


	$filter_type_small = json_encode($filter_type_small);


	$filters = $data['filter']['description'];
	if(!$filters){ $filters =array();}
	$filters_json = json_encode($filters);
?>
<script>
	app.controller('demo', ['$scope','$filter','$http','$compile', 'fileUpload', function($scope,$filter,$http,$compile, fileUpload){

	  $scope.dimensions = <?php echo json_encode($data['dimensions']); ?>;
	  $scope.filters = <?php echo $filters_json ?>;
	  $scope.main_filter_types = <?php if($data['filter_types']){ echo json_encode($data['filter_types']); }else{ echo "[]";} ?>;
	  $scope.filter_types = <?php if($filter_types_json){ echo $filter_types_json;}else{ echo "[]";} ?>;
	  $scope.filter_types_small = <?php if($filter_type_small){ echo $filter_type_small;}else{ echo "[]";} ?>;
	  $scope.filter = <?php if($this->filter){ echo json_encode($this->filter);}else{ echo "[]";} ?>;
	  $scope.default_filters_list = <?php if($default_filters_list){ echo json_encode($default_filters_list);}else{ echo "[]";} ?>;
	  $scope.filter_visibility = $scope.filter.visibility ? $scope.filter.visibility : 'only_me';
		$scope.filter_name = $scope.filter.title && $scope.filter.title != 'Temp' ? $scope.filter.title : '';
		$scope.filter_lock = $scope.filter.locked && $scope.filter.locked != '' ? $scope.filter.locked : '';
	  $scope.filter_search_keyword = "";
	  $scope.total_entries = <?php echo $paginator->total ?: 0; ?>;
	  //console.log($scope.default_filters_list);
	 	

	  /* Load Filters
  	----------------------------------------------*/
 		filters_args = {
		  'search': '<?php echo $_REQUEST['search']; ?>',
		  'value': '<?php echo $_REQUEST['search']; ?>',
		  'filter': $scope.filter,
		 <?php if($data['hide_filters']){ ?> 'hide_filters':1 <?php } ?>
		};	
 		var filters_wrap_html = filter_html(filters_args);

 		angular.element(document.getElementById('filters_wrap')).append($compile(filters_wrap_html)($scope));
	  

 		/* View Filters
  	----------------------------------------------*/
	  $scope.view_filters = function view_filters() {
	  	$scope.create_temp_filters();
	  	if(typeof $scope.filters !== 'undefined') {
		  	if($scope.filters.length<1){
		  		$scope.add_filter();
		  	}
	  	}else{
	  		$scope.add_filter();
	  	}
	  	$("#filters_modal").modal("show");
	  }

	  $scope.delete_filter = function delete_filter(index) {
	  	$scope.filters.splice(index, 1); 
	  	$scope.create_temp_filters();
	  	$scope.apply_filters();
  		return false; 
	  }

	  $scope.delete_temp_filter = function delete_temp_filter(index) {
	  	$scope.edit_filters.splice(index, 1); 
  		return false; 
	  }

	  
	  $scope.get_field_options = function get_field_options(filter) {

	  	if(filter.type=="checklist_id"){
	  		$http({
			    url: "ajax/students.php",
			    method: "POST",
			    data: {action:"get_checklist_stage",route_id:filter.option}
				}).success(function(data, status, headers, config) {	
					
					angular.forEach($scope.main_filter_types, function(type){
		  			if(type.id=="application_stage"){
		  				console.log(type.id+"=="+"application_stage");
		  				type.option = "";
		  				type.options = data;
		  			}
				  });


				  angular.forEach($scope.edit_filters, function(fi){
		  			if(fi.type=="application_stage"){
		  				fi.option = "";
		  				fi.options = data;
		  			}
				  });
				}).error(function(data, status, headers, config) {
			    
				});
	  	}
	  }


	  $scope.filter_type_info = function filter_type_info(filter_type) {
	  	ftype = "";
	  	angular.forEach($scope.main_filter_types, function(type){
  			if(type.id==filter_type){
  				ftype = type;
  			}
		  });

		  console.log("ftype");
		  console.log(ftype);

		  if(!ftype){
		  	angular.forEach($scope.filter_types, function(type){
		  		console.log(type.id+"---"+filter_type);
	  			if(type.id==filter_type){
	  				ftype = type;
	  			}
			  });
		  }
		  return ftype;
	  }

	  $scope.option_title = function option_title(option,filter) {
	  	option_name = "";
	  	if(filter.options){
		  	angular.forEach(filter.options, function(option_info){
	  			if(option_info.value==option){
	  				option_name = option_info.label;
	  			}
			  });
	  	}

		  return option_name;
	  }

	  $scope.filter_title = function filter_title(filter) {
	  	type_info = $scope.filter_type_info(filter.type);
		  return type_info.title;
	  }

	  $scope.show_filter_dropdown = function show_filter_dropdown(filterIndex) {
			$(".filter_select#"+filterIndex+" ul").toggle();
			$(".filter_select#"+filterIndex+" ul input").focus();
		}

		$scope.apply_selected_filter = function apply_selected_filter(filterIndex,itemChoosen) {
			
			$scope.edit_filters[filterIndex].type = itemChoosen.id;
			$scope.fix_filter_options($scope.edit_filters[filterIndex]);
			$(".filter_select#"+filterIndex+" ul").hide();
			$scope.edit_filters[filterIndex].filter_search_keyword = "";
			$scope.$apply();
		}

	  $scope.fix_filter_options = function fix_filter_options(filter) {	
	  	type_info = $scope.filter_type_info(filter.type);

	  	
	  	filter.value = "";
	  	filter.option = "";
	  	filter.sql = type_info.sql;
	  	if(typeof type_info.field_type !== 'undefined'){
	  		filter.field_type = type_info.field_type
	  	}else{
	  		delete filter.field_type;
	  	}
	  	if(typeof type_info.extra !== 'undefined') {
		  	filter.extra = type_info.extra;
		  	filter.operator_type = 'Exactly matching' ;
			}else{
				delete filter.extra;
			}

			if(typeof type_info.options !== 'undefined') {
		  	filter.options = type_info.options;
			}else{
				delete filter.options;
			}

			if(typeof type_info.operators !== 'undefined') {
		  	filter.operators = type_info.operators;
			}else{
				delete filter.operators;
			}

			//Get dynamic listing
			if(filter.sql){

				$scope.form_data = {action: 'get_dynamic_field_values', sql: filter.sql }
		  	$("#SOMETHING").html("Loading...");

				$http({
			    url: "ajax/students.php",
			    method: "POST",
			    data: $scope.form_data
				}).success(function(data, status, headers, config) {
					filter.options = data;
				}).error(function(data, status, headers, config) {
			    
				});

			}

	  }


	  $scope.validate_operator_value = function validate_operator_value(filter) {
	  	if(filter.operator_type!='Is empty'){
	  		filter.value = "";
	  	}
	  }

	  $scope.add_filter = function add_filter() {
	  	var new_field = {type: '',title:'',status:'',join:'and'};
			$scope.edit_filters.push(new_field);
	  }


	  $scope.add_dimension = function add_dimension(dimension) {
	  	if(dimension.active==1){
	  		dimension.active=0;
	  	}else{
	  		dimension.active=1;
	  	}
	  };

	  
	  $scope.create_temp_filters = function create_temp_filters() {
	  	$scope.edit_filters = angular.copy($scope.filters);
	  	fcount = 1;
	  	angular.forEach($scope.edit_filters, function(filter){

	  		console.log("||||");
		  	console.log(filter);


		  	if(!filter.join){
		  		filter.join = 'and';
		  	}

		  	if(fcount==1){
		  		filter.join = 'and';
		  	}
		  	fcount++;
		  });
	  }

	  $scope.apply_filters = function apply_filters() {
	  	
	  	pop_message('Loading...',false);

	  	angular.copy($scope.edit_filters, $scope.filters);
  		angular.forEach($scope.filters, function(filter){
		  	filter.status = 'active';
		  	//filter.options = '
		  	if(filter.field_type === 'daterange'){

		  		filter.value = "between "+ filter.from + " AND " + filter.to ;
		  	}
		  	
		  	filter_title = "";
		  	angular.forEach($scope.dimensions[0]['All Fields'], function(option_info){
		  		console.log("BBBBBB");
	  			console.log(filter.type+"=="+option_info.id);
		  		if(filter.type==option_info.id){
		  			filter_title = option_info.title;
		  		}
		  	});
		  	filter.title = filter_title;
		  	console.log("AAAAA");
	  		console.log(filter);
		  });

  		// console.log("DDDD");
	  	// console.log(filter);
	  	// console.log($scope.dimensions[0]['All Fields']);
	  	// angular.forEach(dimensions[0]['All Fields'], function(option_info){
	  	// 	if(filter.id==option){
	  	// 		option_name = option_info.label;
	  	// 	}
	  	// });

		  
	  	

  		// return false;

		  $("#filters_modal .btn-primary").html("Loading...").addClass("disabled");

		  // console.log($scope.filters);
		  // return false;
		  // return false;
		  var filters_json = JSON.stringify($scope.filters);
		  
		  $("#main_search_form_filters").val(filters_json);
		  $("#main_search_form_action").val("update_filter");
		  $("#main_search_form").submit();
	  }

	  	
	  $scope.apply_fields = function apply_fields(dimension) {
	  	$scope.form_data = {fields: $scope.dimensions, action: 'set_session_student_fields', page:'<?php echo $data['filter_page_name'];?>', filter_id:'<?php echo $this->filter['id'];?>' }
	  	$("#dimensions_dropdown .bottom_apply_button .btn").html("Loading...");

			$http({
		    url: "ajax/students.php",
		    method: "POST",
		    data: $scope.form_data
			}).success(function(data, status, headers, config) {
				location.reload();
			}).error(function(data, status, headers, config) {
		    
			});
	  }


       $scope.calc_selected_entries = function calc_selected_entries() {
			if($scope.select_all_entries){
				$scope.selected_count = $scope.total_entries;
			}else{
				selected_rows = $('#applicants_table .select_row:checked').length;	
				$scope.selected_count = selected_rows;
				//alert($scope.selected_count);
			}

			$scope.$apply();
		};


		$scope.select_all_on_page = function select_all_on_page() {

			if($scope.select_all){
				//alert("all please");
				$('#applicants_table .select_row').each(function( index ) {
					row_id = $(this).attr("id")
					row_name = "table_row_"+row_id;

					$scope[row_name]=1;
					$scope.calc_selected_entries();
				});

			}else{
				//alert("normal");
				$('#applicants_table .select_row').each(function( index ) {
					row_id = $(this).attr("id")
					row_name = "table_row_"+row_id;

					$scope[row_name]=0;
					$scope.calc_selected_entries();
				});
			}
			

			$scope.calc_selected_entries();
		};


		$scope.select_row = function select_row() {
	  	    $scope.calc_selected_entries();
		};

		$scope.submit_assign_form = function submit_assign_form(action) {
	  	$scope.bulk_action = "bulk_assign";

	  	users_list = [];
	  	$( "#assign_modal form input:checked" ).each(function(i, val){
	  		users_list.push($(this).val());
	  		console.log($(this).val())
	  	});

	  	if(users_list.length){
		  	users_list = users_list.join(", ");
            $("#table_form .tempinput").html("")
		  	$("#table_form .tempinput").prepend('<input type="hidden" name="assign_users_ids" value="'+users_list+'">');

		  	setTimeout(function(){
                  if (action=="export") {
			  		  $("#table_form").attr('target', '_blank');
			  		  $("#table_form").attr('action', '/admin/events/exportattencdancesheetcsv');
		  		  }
				  $("#table_form").submit();


				}, 1000);
	  	}
		};




		

	}]);
	$(document).ready(function(){

		$('#dimensions_dropdown').click(function(e) {
		    e.stopPropagation();
		});

		$('#export_btn').click(function(e) {
		    $(this).html("Loading...").addClass("disabled");
		});
		$('#export_to_ukvi_btn').click(function(e) {
			$(this).html("Loading...").addClass("disabled");
		});

		// $('body').on('click', '.dynamic_download', function(e){
		// 	///e.preventDefault();
		// 	var $scope = angular.element($('[ng-controller="demo"]')).scope();
		// 	///$scope.download_dynamic_file($(this).closest('a').attr('href'));
		// });

		jQuery(".main_table .table_wrap").on('scroll', function() {
			that = jQuery(this);
		  if(that.scrollLeft() >= 1) {
		  	$("#applicants_table .shadow").show();
			}else{
				$("#applicants_table .shadow").hide();
			}
		});

		var frozen_fields_width = $("#applicants_table .pics").width();
		$("#applicants_table .main_table").css({'margin-left':frozen_fields_width+"px"});
		$("#applicants_table .shadow").css({'left':frozen_fields_width+"px"});


		//$("#filters_modal").modal("show");		
        $(document).on("click","a[title='Copy Link']", function (e) {
        	e.preventDefault();
		    var hrefval = $( this).attr("href");
		 	var copiedHref = $('<input>').val(hrefval).appendTo('body').select();
		 	document.execCommand('copy');
		});

		$("#pdfexport").click(function(){
			$('#csvexport').attr('checked', false);
		})

		$("#csvexport").click(function(){
			$('#pdfexport').attr('checked', false);
		})

	});


function popup(value){
  cuteLittleWindow = window.open("<?php echo $this->base_url("system/"); ?>get_email/" + value, "littleWindow", "location=no,width=800,height=800"); 
}

function popup_content(value){
  let w = window.open("<?php echo $this->base_url("system/"); ?>deployment/" + value, "", "location=no,width=1200,height=800");
  w.onbeforeunload = function() {
     window.location.reload();
  };
}

</script>
<style type="text/css">
	.status{ width: 10px; height: 10px; background-color: #529214; border-radius: 90px; display: inline-block; }
	.status.grey{ background-color: #999; }
	td{ color: #999; }
	a.grey, a.grey:hover{color: #999;}
</style>
<div class="inner_container"  ng-controller="demo" >
	<div class="breadcrumbs">
		<a href="<?php echo $this->base_url('settings');?>"><i class="fa fa-chevron-left" aria-hidden="true"></i> Settings</a>
	</div>
	<div class="top_section">
		<h1>
			Deployment Requests
		</h1>
	</div>

	<ul class="next-tab_list">
		<li>
			<a class="<?php if(!$data['type']){echo "active";}?>" href="<?php echo $this->base_url("system/deployment"); ?>">
				Unprocessed
			</a>
		</li>

		<li>
			<a class="<?php if($data['type']=='processed'){echo "active";}?>" href="<?php echo $this->base_url("system/deployment/processed"); ?>">
				Processed
			</a>
		</li>	

		<li>
			<a class="<?php if($data['type']=='suspended'){echo "active";}?>" href="<?php echo $this->base_url("system/deployment/suspended"); ?>">
				Suspended
			</a>
		</li>	

        <li>
			<a class="<?php if($data['type']=='reverted'){echo "active";}?>" href="<?php echo $this->base_url("system/deployment/reverted"); ?>">
				Reverted
			</a>
		</li>	
		
	</ul>

	<div class="stnrd_box">
		<section id="filters_wrap"></section>

		<?php if(count($data['results'])){ ?>
    	
			<table class="table">
			<thead>
				<tr>
					<th style="width: 150px;">Date/Time</th>
					<th>Requested By</th>
					<th>Ticket ID</th>
					<th>No. of Commits</th>
					<th>Ticket Priority</th>
					<th></th>
				</tr>
			</thead>
			<tbody class="sortable_objects">
				<?php 
				foreach ($data['results'] as $entry){
                    $link = $this->base_url('system/deployment/'.$entry["id"]); 
                    $commits = json_decode($entry['description'],true);
                    ?>
				    <tr id="row_<?php echo $entry["id"]; ?>">
					<td>						
						<span style="font-size:12px; color: #999;"><?php echo $entry["date"]; ?></span>						
					</td>
					<td><?php echo $entry["first_name"]." ".$entry['last_name']; ?></td>
					<td>#<?php echo $entry["source"]; ?></td>
					<td><?php echo count($commits['commits']); ?> </td>
					<td><?php echo $entry["priority"]; ?></td>
					<td><a href="javascript:popup_content(<?php echo $entry["id"]; ?>)"><button type="button"  class="btn btn-default">View Content</button></a></td>
				</tr>
				<!-- Modal -->
				<?php } ?>
			</tbody>
		</table>

		<?php global $paginator; echo $paginator->links(); ?>
		<?php }else{ ?>

			<div class="no_results">
				
				<?php if($_GET['search']){ ?>
					<span class="glyphicon glyphicon-search"></span>
					<h2>No match for your search criteria</h2>
					<a href="#" class="btn btn-default">Back to emails</a>
				<?php }else{?>
					<span class="glyphicon glyphicon-envelope"></span>
					<h2>You don't have any emails sent yet</h2>
				<?php }?>
			</div>
		<?php } ?>

	<?php require_once(ABSPATH."/app/views/applicants/inc_filter_modals.php"); ?>	
</div>

