
<link href="<?php echo engine_url(); ?>/css/styles_enquiry.css" rel="stylesheet">

<!--LIST OF PANELS-->

        <style type="text/css">
         
			 /* you need to set the size of the ul otherwise it may not detect the dropped item */
            .list li {
                display: inline-block;
                list-style-type: none;
                padding-right: 20px;
            }


/*TIMELINE*/
.bkg_buttons {
    background-color: rgb(240, 240, 240);
    padding: 10px;
}

.btn-white {
    background-image: -webkit-linear-gradient(top, #fff 0, #e0e0e0 100%);
    background-image: -o-linear-gradient(top, #fff 0, #e0e0e0 100%);
    background-image: linear-gradient(to bottom, #fff 0, #ffffff 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#ffe0e0e0', GradientType=0);
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
    background-repeat: repeat-x;
    border-color: #dbdbdb;
    text-shadow: 0 1px 0 #fff;
    border-color: #ccc;
}

.timeline {
    list-style: none;
    padding: 20px 0 20px;
    position: relative;
}

.timeline-date-heading {
    width: 20%;
    margin: 0 17%;
	border:#FFF solid 5px;
    background-color: #F0AD4E;
    padding: 10px;
    color: #fff;
    border-radius: 15px;
}

.timeline-date-heading .subdate_sml{
	text-align:left !important;;
}

.timeline-heading-inverted{
	width:70%;	
}
.timeline .date{
	font-size:38px;
	margin-right:10px;
	font-weight:bold;
}
.timeline .subdate{
	font-size:15px;
	font-weight:bold;
}
.timeline .subdate_sml{
	font-size:11px;
	text-align: right;
}

    .timeline:before {
        top: 0;
        bottom: 0;
        position: absolute;
        content: " ";
        width: 3px;
        background-color: #F0AD4E;
        left: 25%;
        margin-left: -1.5px;
    }

    .timeline > li {
        margin-bottom: 20px;
        position: relative;
    }

        .timeline > li:before,
        .timeline > li:after {
            content: " ";
            display: table;
        }

        .timeline > li:after {
            clear: both;
        }

        .timeline > li:before,
        .timeline > li:after {
            content: " ";
            display: table;
        }

        .timeline > li:after {
            clear: both;
        }

        .timeline > li > .timeline-panel {
            width: 72%;
            float: left;
            border: 1px solid #ccc;
			background-color: #fff;
            border-radius: 2px;
            padding: 10px;
            position: relative;
            -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, 0.175);
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.175);
        }

            .timeline > li > .timeline-panel:before {
                position: absolute;
                top: 26px;
                right: -15px;
                display: inline-block;
                border-top: 15px solid transparent;
                border-left: 15px solid #ccc;
                border-right: 0 solid #ccc;
                border-bottom: 15px solid transparent;
                content: " ";
            }

            .timeline > li > .timeline-panel:after {
                position: absolute;
                top: 27px;
                right: -14px;
                display: inline-block;
                border-top: 14px solid transparent;
                border-left: 14px solid #fff;
                border-right: 0 solid #fff;
                border-bottom: 14px solid transparent;
                content: " ";
            }

        .timeline > li > .timeline-badge {
            color: #F0AD4E;
            width: 40px;
            height: 40px;
            line-height: 20px;
            font-size: 1.4em;
            text-align: center;
            position: absolute;
            top: 16px;
            left:23.68%;
            margin-left: -5px;
            background-color: #F0AD4E;
            z-index: 100;
            border-top-right-radius: 50%;
            border-top-left-radius: 50%;
            border-bottom-right-radius: 50%;
            border-bottom-left-radius: 50%;
			border:solid white 10px;
        }
		
        .timeline > li > .timeline-badge_text .fa{
            color: #F0AD4E;
        }

        .timeline > li > .timeline-badge_text h2{
            text-align:right;
        }

        .timeline > li > .timeline-panel {
            float: right;
        }

            .timeline > li > .timeline-panel:before {
                border-left-width: 0;
                border-right-width: 15px;
                left: -15px;
                right: auto;
            }

            .timeline > li > .timeline-panel:after {
                border-left-width: 0;
                border-right-width: 14px;
                left: -14px;
                right: auto;
            }
			
			.timeline > li > .timeline-badge_text{
				color: #F0AD4E;
				font-size: 1.4em;
				text-align: center;
				position: absolute;
				top: 16px;
				left:8px;
				margin-left: -5px;
				z-index: 100;
				width:22%;
			}
			.timeline > li > .timeline-badge_text-inverted .fa{
				color: #F0AD4E;
			}

.timeline-badge.primary {
    background-color: #2e6da4 !important;
}

.timeline-badge.success {
    background-color: #3f903f !important;
}

.timeline-badge.warning {
    background-color: #f0ad4e !important;
}

.timeline-badge.danger {
    background-color: #d9534f !important;
}

.timeline-badge.info {
    background-color: #5bc0de !important;
}

.timeline-title {
    margin-top: 0;
    color: inherit;
}

.timeline-body > p,
.timeline-body > ul {
    margin-bottom: 0;
	color:#333;
}
/*color old timelines*/
.timeline .old {
    background-color: #777 !important;
}

.timeline .old-text {
    color: #777 !important;
}

.timeline .old-text .fa{
    color: #777 !important;
}

.action_buttons{
    /*background-color: rgb(240, 240, 240);*/
    padding: 6px;
    width: 20%;
    position: absolute;
    right: 0;
    top: 0;
    margin-top: -20px;
    margin-right: -10px;
}

    .timeline-body > p + p {
        margin-top: 5px;
    }

@media (max-width: 767px) {
    ul.timeline:before {
        left: 40px;
    }

    ul.timeline > li > .timeline-panel {
        width: calc(100% - 90px);
        width: -moz-calc(100% - 90px);
        width: -webkit-calc(100% - 90px);
    }

    ul.timeline > li > .timeline-badge {
        left: 15px;
        margin-left: 0;
        top: 16px;
    }

    ul.timeline > li > .timeline-panel {
        float: right;
    }

        ul.timeline > li > .timeline-panel:before {
            border-left-width: 0;
            border-right-width: 15px;
            left: -15px;
            right: auto;
        }

        ul.timeline > li > .timeline-panel:after {
            border-left-width: 0;
            border-right-width: 14px;
            left: -14px;
            right: auto;
        }
}

.center_with_text_boxes{
	margin-left: 15px;
}

.orange_text{
	border-radius: 4px;
}



/* lead Profile Page
------------------- */
.lead_profile_left{ float: left; width:400px;}
.lead_profile_right{ float: right; width:0px; padding-top:10px; width:550px}
.lead_profile_pic{ float: left; background-color: #fff; margin-bottom: 110px; border:1px solid #f0f0f0; margin-right:5px}
.lead_profile_info h1{ color: #333; margin-bottom: 5px; font-weight: bold; font-size:18px}
.lead_profile_info h2{ color: #666; font-size: 14px; font-weight: bold;}
.lead_profile_info ul{ margin:0; padding:0}
.lead_profile_info li{ float: left; width:70%; padding:2px 0;}
.lead_profile_info .title{ font-weight:bold; float:left;}
.lead_profile_info .info_detail{float: left; height: 22px; width: 150px;}
.lead_profile_info .titles{ font-weight:bold; float: left; width:90px}
.lead_profile_info .info_details{float: left; width: 200px;}


/* lead Profile Detail
------------------- */
.lead_profile_detail_left{ float: left; width:400px;}
.lead_profile_detail_right{ float: right; width:0px; padding-top:10px; width:550px}
.lead_profile_detail_pic{ float: left; width: 150px; background-color: #fff; margin-bottom: 100px; border:1px solid #f0f0f0; margin-right:5px}

.lead_profile_detail_info .bow, .short{
	  display: -webkit-box;
	  display: -webkit-flex;
	  display: -ms-flexbox;
	  display: flex;
	}
.lead_profile_detail_info .bow{padding:10px}
.lead_profile_detail_info .short{width:100%; padding:10px}
.lead_profile_detail_info .short .text-center{ font-size: 15px;}
.lead_profile_detail_info .short .date{ font-size: 30px;    font-weight: bold;
    line-height: 50px;}
.lead_profile_detail_info .short .days{ font-size: 10px; line-height:10px}
.lead_profile_detail_info .red{background:rgb(212, 226, 234); padding:10px; font-weight:bold; color:#000}
.lead_profile_detail_info .blue{ background-color:rgb(240, 240, 240); padding:10px;  color:#333}
.lead_profile_detail_info .outline{ background-color:#fff; padding:10px; border: rgb(240, 240, 240) 1px solid;}
	
.lead_profile_detail_info h1{ color: #333; margin-bottom: 5px; font-weight: bold; font-size:18px}
.lead_profile_detail_info h2{ color: #666; font-size: 14px; font-weight: bold;}
.lead_profile_detail_info ul{ margin:0; padding:0}
.lead_profile_detail_info li {
    vertical-align: middle;
    float: left;
    width: 30%;
    /* height: 50px; */
    /* padding: 5px; */
    margin-bottom: 5px;
    background-color: rgb(191, 219, 215);
    margin-right: 5px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    margin-bottom: 10px;
    font-weight: bold;
}
.lead_profile_detail_info li:hover{ background:#CCC}
.lead_profile_detail_info .titles{ font-weight:bold; float: left; width:90px}
.lead_profile_detail_info .info_details{float: left; width: 200px;}
.lead_profile_detail_info .title {
    float: left;
    width: 85px;
    font-weight: bold;
    padding: 10px;
    color: rgb(255, 255, 255);
    background-color: rgb(255, 102, 0);
    margin-right: 10px;
}
.lead_profile_detail_info .info_detail{float: left; min-height: 22px; width: 190px;}

/*LEAD SETTINGS PAGE*/
.lead_settings .wrap{ border:1px solid #f0f0f0; padding:10px; min-height:150px; margin-bottom:10px}
.lead_settings .wrap:hover{ background-color:#f0f0f0}
.lead_settings button{ margin-bottom:10px}
            
        </style>
        
      <?php
$colorfilter = $_GET['statusfilter'];

// switch ($colorfilter) {
//     case "1":
//         echo "color green";
//         break;
//     case "2":
//         echo "color yellow";
//         break;
//     case "3":
//         echo "color red";
//         break;
//     default:
//         echo " ";
// }
?>  
        
        
 
        

<?php 
	$args = array();
	function user_block($args=array()){
	
	//variable
	$status = "AND db1056 IN (".$args['status'].")";
		
	//if admin is logged in only show them their leads
	 if($_SESSION['userlevel']==3){
	  $assigned_users_ids = pull_field("core_assignations","group_concat(rel_id)","WHERE core_assignations.db69836 = $_SESSION[uid] and (core_assignations.db69743 is null or core_assignations.db69743 ='') AND (core_assignations.rec_archive IS NULL OR core_assignations.rec_archive = '')");
	  $assigned_users = "AND id IN ($assigned_users_ids)";
	 }
	
		//switch variables
	

$colornumber = $_GET['color-red']+$_GET['color-yellow']+$_GET['color-green'];


		switch ($colornumber) {
			    case "10":
				$colors2 = "color = 'color red'";
				break;
				
				case "24":
				$colors2 = "color = 'color green'";
				break;
				
				case "36":
				$colors2 = "color = 'color yellow'";
				break;
				
				case "34":
				$colors2 = "color = 'color red' or color = 'color green'";
				break;
				
				case "46":
				$colors2 = "color = 'color red' or color = 'color yellow'";
				break;
				
				case "60":
				$colors2 = "color = 'color green' or color = 'color yellow'";
				break;
				
				case "84":
				$colors2 = "color = 'color green' or color = 'color red'";
				break;
				
				default:
	 echo " ";
				
		}
		
		//colours
	if($colors2){
		$having2 = "HAVING $colors2 ";
	}

		//sortby variables
	

$sortby = $_GET['sortby'];


		switch ($sortby) {
			    case "1":
				$orderbyvalue = "first_name asc,surname asc";
				break;
				
				case "2":
				$orderbyvalue = "surname asc,first_name asc";
				break;
				
				case "3":
				$orderbyvalue = "color asc";
				break;
				
				case "4":
				$orderbyvalue = "age asc";
				break;
				
				case "5":
				$orderbyvalue = "age desc";
				break;
				
				default:
	 $orderbyvalue = "age asc";
				break;
				
		}
		
		//orderby
	if($orderbyvalue){
		$orderby = "order by $orderbyvalue ";
	}
		
	//additional variables
	switch ($args['color']) {
		case "1":
		$colors = "color green";
	break;
		case "2":
		$colors = "color yellow";
	break;
		case "3":
		$colors = "color red";
	break;
		default:
	 echo " ";
	}
	
	//colours
	if($colors){
		$having = "HAVING color = '".$colors."' ";
	}
		
	$studentsearch = $_GET['studentsearch'];
		
	//search
	if ($studentsearch){
		$student = "AND Concat(db1041,db1043) like '%$studentsearch%'";
	}

	//get data
	$dbh = get_dbh();
	$sql_all_leads = "SELECT
	id,
	username_id AS 'hdn_username',
	DATE_FORMAT(date, '%d/%m/%Y @%H%i') AS 'Date Created',
	DATE_FORMAT(date, '%d/%m/%Y @%H%i') AS 'DateAdded',
	datediff(now(),date) AS 'age',
	db1046 as 'phone',
	db1043 AS surname,
	id AS student_id,
	db1041 AS 'first_name',
	(SELECT db343 FROM core_course_level WHERE core_course_level.id = lp.db1053 LIMIT 1) AS 'Level Of Entry',
	db1051 AS 'Course Of Interest',
	db1058 AS 'Email',
	(SELECT db1733 FROM lead_stages WHERE id = db1056) AS 'Status',
	IFNULL((SELECT p.id FROM core_assignations AS p where p.rel_id=lp.id and (core_assignations.db69743 is null or core_assignations.db69743 ='') AND (core_assignations.rec_archive IS NULL OR core_assignations.rec_archive = '')),0) AS 'coreassignid',
	IFNULL((SELECT Count(g.id)FROM lead_interactions AS g where g.rel_id=lp.id GROUP BY g.rel_id),0) AS 'total_interactions',
	IFNULL((SELECT Count(k.id)FROM form_email_log AS k where k.rel_id=lp.id GROUP BY k.rel_id),0) AS 'total_emails',
	CASE WHEN (SELECT Count(j.id) FROM core_assignations AS j where j.rel_id=lp.id and db69884=1 and (core_assignations.db69743 is null or core_assignations.db69743 ='') AND (core_assignations.rec_archive IS NULL OR core_assignations.rec_archive = '') GROUP BY j.rel_id) is null then 'unassgined' else 'assigned' end as 'assigned',
	CASE WHEN (SELECT Count(j.id) FROM core_assignations AS j where j.rel_id=lp.id and db69884=1 and (core_assignations.db69743 is null or core_assignations.db69743 ='') AND (core_assignations.rec_archive IS NULL OR core_assignations.rec_archive = '') GROUP BY j.rel_id) is null then 'primary' else 'success' end as 'assignedcolor',
	(SELECT CASE WHEN TIMESTAMPDIFF(HOUR,c.date,NOW()) = d.db32501/2 then 'color yellow' WHEN TIMESTAMPDIFF(HOUR,c.date,NOW()) > d.db32501/2 then 'color red' else 'color green' end AS 'color'
  FROM lead_stage_tracker AS c, lead_stage_settings AS d
  where c.rel_id is not null AND c.rel_id = lp.id AND c.db27514=db1056 AND c.db27514 = d.rel_id and d.usergroup = '$_SESSION[usergroup]'
   GROUP BY c.id desc,c.rel_id, c.db27514, d.db32500
  limit 1) AS 'color'
	FROM lead_profiles lp
	WHERE (rec_archive IS NULL OR rec_archive = '')
	AND usergroup = '$_SESSION[usergroup]'
	$student
	$status
	$having2
	$assigned_users
	$orderby
	LIMIT 10";
		
		
	dev_debug($sql_all_leads);
	
	$sql = $dbh->prepare($sql_all_leads);
	$sql->execute();
	$leads_list= $sql->fetchAll(PDO::FETCH_OBJ);
		
		foreach($leads_list as $applicants){
			$colorfilter = $_GET['statusfilter'];

			// switch ($colorfilter) {
			//     case "1":
			//         echo "color green";
			//         break;
			//     case "2":
			//         echo "color yellow";
			//         break;
			//     case "3":
			//         echo "color red";
			//         break;
			//     default:
			//         echo " ";
			// }

			// echo '<pre>';
			// print_r($applicants);
			// echo '</pre>';
			// exit();

		?>
			<li id=<?php echo $applicants->id; ?> class="<?php echo $applicants->color; ?>">
				<a href="#"  data-toggle="modal" data-target="#myModal" class="pic" style="background-image: url(<?php echo engine_url(); ?>/images/profile_square3-270x270.jpg);"></a>
				<div class="info">
					<h3><a href="#" class="show_student_profile" student_id="<?php echo $applicants->student_id ?>" data-toggle="modal" data-target="#myModal"><?php echo $applicants->first_name; ?>&nbsp;<?php echo $applicants->surname; ?></span></a></h3>
					<div class="options">
						<a href="#">
							<span class="badge"><?php echo $applicants->total_emails; ?></span>
							<span class="glyphicon glyphicon-envelope" aria-hidden="true"></span>
						</a>
						<a href="#">
							<span class="badge"><?php echo $applicants->total_interactions; ?></span>
							<span class="glyphicon glyphicon-comment" aria-hidden="true"></span>
						</a>
						
						
						<a href="<?=engine_url()?>/controller.php?&pg=167&rec=<?php echo $applicants->coreassignid; ?>&height=300&width=620&jqmRefresh=false&ref=<?php echo $applicants->id; ?>&module_slug=1" alt="Assign" class="thickbox" title="Assign">
							
							<span><i class="fa fa-user-plus"></i></span>
							
						</a>
						</br>
						
						
						<span class="label label-<?php echo $applicants->assignedcolor; ?>"><?php echo $applicants->assigned; ?></span>
						<span class="label label-default">Age | <?php echo $applicants->age; ?> day(s)</span>
						
						
						
					</div>
				</div>
			</li>
		<?php
		}
		
	}//end of function
 ?>
 
 <?php 
	$stages = array();
	function stages_block($stages=array()){
	
	//variable
	$stageid =  "AND db1056 IN (".$stages['status'].")";

	//get data
	$dbh = get_dbh();
	$sql_all_stages = "SELECT lead_profiles.usergroup,CASE WHEN lead_profiles.db1056 < 1 THEN 1 else lead_profiles.db1056 end as statusvalue, lead_stages.db1733,count(lead_profiles.id) AS totalprofiles
FROM lead_stages LEFT JOIN lead_profiles ON lead_stages.id = CASE WHEN lead_profiles.db1056 < 1 THEN 1 else lead_profiles.db1056 end
WHERE lead_profiles.usergroup ='$_SESSION[usergroup]' AND (lead_profiles.rec_archive IS NULL OR lead_profiles.rec_archive = '')
$stageid
Group By lead_profiles.usergroup, CASE WHEN lead_profiles.db1056 < 1 THEN 1 else lead_profiles.db1056 end, lead_stages.db1733";


//not sure we need the below
//Group By lead_profiles.usergroup, lead_profiles.db1056, lead_stages.db1733";
		
	dev_debug($sql_all_stages);
	
	$sql = $dbh->prepare($sql_all_stages);
	$sql->execute();
	$stages_list= $sql->fetchAll(PDO::FETCH_OBJ);
	$stagetotals = $stages_list[0];

	echo '<div class="count">'.$stagetotals->totalprofiles.'</div>';

	}//end of function
 ?>
 
 <?php 
	$mystages = array();
	function mystages_block($mystages=array()){
	
	

	//get data
	$dbh = get_dbh();
	$sql_all_mystages = "SELECT lss.id, ls.db11041, ls.db1733 as stagename, lss.rec_archive FROM lead_stage_settings lss inner join lead_stages ls on lss.rel_id = ls.id
where lss.usergroup='$_SESSION[usergroup]'
order by ls.db11041";

		


		
	dev_debug($sql_all_mystages);
	
	$sql = $dbh->prepare($sql_all_mystages);
	$sql->execute();
	$mystages_list= $sql->fetchAll(PDO::FETCH_OBJ);
	
	foreach($mystages_list as $stagedetails){
	?>		
  
 <tr>
      <form>
      <td><?php echo $stagedetails->stagename; ?></td>
      <td><input type="checkbox"></td>
      <td><input class="btn btn-primary btn-xs" value="update" type="submit"></td>
      </form>
 </tr>
    
 <?php
		}
		
	}//end of function
 ?>
 

<script type="text/javascript">
	$(document).ready(function(){
		var dh = $(window).height();
		var new_height = dh-Number(300);

		$(".items .column ul").height(new_height+'px');
	});
</script>

	
	<!-- 
<li><a href="'.engine_url.'/controller.php?pg='.$popview_page.'&add=1&module_id='.get_module_id().'&width=850&height=550&jqmRefresh=true" title="Add Record" id="tour_addnewbutton" class="thickbox" alt="Add Record"><i class="fa fa-file-text-o"></i> Add via webform</a>
-->
	
	

<div class="inner_container">
	<div class="top_section">
		<h1>
		Enquiries
		</h1>
		Organise and manage leads (	<a href="<?=base_url('enquiries')?>">Switch to list view</a>)
	</div>

		<div id="enquiries">
		  <div class="top_section">
				<div class="buttons">
                   
                   
                   <button type="button" class="btn btn-default hidden" data-toggle="modal" data-target=".bs-example-modal-sm"><i class="fa fa-list"></i> Select Columns</button>
                   
                    <?php
						echo '
								<div class="btn-group">
								<button type="button" class="btn btn-warning"><i class="fa fa-plus-square-o"></i> Add New</button>
								<button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
								<span class="caret"></span>
								<span class="sr-only">Toggle Dropdown</span>
								</button>
								  <ul class="dropdown-menu" role="menu">
									<li><a href="'.engine_url().'/controller.php?pg=97&add=1&module_id=1&width=850&height=550&jqmRefresh=true" title="Add Record" id="tour_addnewbutton" class="thickbox" alt="Add Record"><i class="fa fa-file-text-o"></i> Add via webform</a></li> 
								';

								echo'
									<li><a href="'.engine_url().'/dynamic_view.php?id=276" title="Load Own Data"  alt="Load Own Data"><i class="fa fa-cloud-upload"></i> Import from spreadsheet</a> </li>  
									';

								echo'    
								  </ul>
								</div>';
					?>
				</div>
				
				
				
				
			
				
								
		    <div class="colors">
		    
		    <style>
				
				input.bigCheckbox { width: 40px; height: 40px; }
				
				.btn span.glyphicon {    			
	opacity: 0;				
}
.btn.active span.glyphicon {				
	opacity: 1;				
}
				</style>
				
			  <form method="get" class="form-inline">
				<label>Filters</label>
				<input name="filtered" type="hidden" id="filtered" value="1">
				
				 
				
				  <label class="btn btn-success active">
				    <input  name="color-green" type="checkbox" value="24" <?php if ($_GET['color-green'] == 24) {
    echo "checked";
} 
						   elseif (is_null($_GET['filtered']))  {
    echo "checked";
}						   
		else{
    echo " ";
} ?> >
			    </label>
				 
				  <label class="btn btn-warning active">
				    <input  name="color-yellow" type="checkbox" value="36" <?php if ($_GET['color-yellow'] == 36) {
    echo "checked";
} elseif (is_null($_GET['filtered']))  {
    echo "checked";
}						   
		else{
    echo " ";
} ?> >
			    </label>
				 
			    <label class="btn btn-danger active">
				    <input  name="color-red" type="checkbox" value="10" <?php if ($_GET['color-red'] == 10) {
    echo "checked";
} elseif (is_null($_GET['filtered'])) {
    echo "checked";
}						   
		else{
    echo " ";
} ?>  >
			    </label>
			    
			     <select name="sortby" class="form-control" id="sortby" value="<?php echo $_GET['sortby']; ?>" >
			      <option>Sort By...</option>
			  <option value="1" <?php if ($_GET['sortby'] == 1) {
    echo "selected";} else{echo " "; } ?>>Sort By First Name</option>
			  <option value="2" <?php if ($_GET['sortby'] == 2) {
    echo "selected";} else{echo " "; } ?>>Sort By Last Name</option>
			  <option value="3" <?php if ($_GET['sortby'] == 3) {
    echo "selected";} else{echo " "; } ?>>Sort By Colour</option>
			  <option value="4" <?php if ($_GET['sortby'] == 4) {
    echo "selected";} else{echo " "; } ?>>Sort By Age Asc</option>
    			<option value="5" <?php if ($_GET['sortby'] == 5) {
    echo "selected";} else{echo " "; } ?>>Sort By Age Desc</option>
			</select>
			 
			 <input name="studentsearch" type="search" class="form-control" id="studentsearch" value="<?php echo($_GET['studentsearch']); ?>" placeholder="Search By Student Name">
				 
			    
			    <button type="submit" class="btn btn-default" aria-label="Left Align">
  <i class="fa fa-filter"></i> Filter
</button>
		    
		    

			    
			   
		    
			    
				
				
			  </form>
		    </div>
		    
		    <div class="colors">
		    <form method="get" class="form-inline" style="padding-top: 26px">
		    <button type="submit" class="btn btn-default" aria-label="Left Align">
  <i class="fa fa-refresh"></i> Reset
</button>
		    
		    
		    
		    
		    </form>
		    </div>
			
			
			
			
			
			<div class="clearfix"></div>
			</div>
			
			
			<div class="items">
			
			


			
			
				<div class="column mr">
					<div class="title" >
                       <?php // pull_field //tablename, field you want, where 
						$rec1=pull_field("lead_stage_settings","id", "WHERE usergroup='$_SESSION[usergroup]' AND rel_id=1");
						?>
                       
                      
                        
                        <a href="<?=engine_url()?>/controller.php?pg=461&add=1&module_id=1&width=850&height=550&title=New&rec=<?php echo $rec1; ?>&jqmRefresh=true&ref=1" title="Stage Settings" id="tour_stage_settings" class="thickbox opt" alt="Stage Settings"><i class="fa fa-cog"></i></a>
						<?php
						// get new
						stages_block(array('status'=>"1,''"));
						?>
						<div class="t_text">New</div>
						<div class="sub_text">Recently added Enquiries</div>
					</div>

					<ul id="1" class="free">
						<?php 
						// get new
						user_block(array('status'=>"1,''",'color'=>$colorfilter));
						?>
					</ul>
					<div class="view_more">
						<a page="1" id="1" href="#">View more</a>
					</div>
				</div>

				<div class="column mr">
					<div class="title">
					
					<?php // pull_field //tablename, field you want, where 
						$rec2=pull_field("lead_stage_settings","id", "WHERE usergroup='$_SESSION[usergroup]' AND rel_id=4");
						?>
					
						<a href="<?=engine_url()?>/controller.php?pg=461&add=1&module_id=1&width=850&height=550&title=New&rec=<?php echo $rec2; ?>&jqmRefresh=true&ref=4" title="Stage Settings" id="tour_stage_settings" class="thickbox opt" alt="Stage Settings"><i class="fa fa-cog"></i></a>
						
						<?php // get new
						stages_block(array('status'=>'4'));
						?>
						<div class="t_text">Engaged</div>
						<div class="sub_text">Enquiries who are engaged</div>
					</div>

					<ul id="4" class="free">
						<?php 
						// get new
						user_block(array('status'=>'4','color'=>$colorfilter));
						?>
					</ul>
					<div class="view_more">
						<a page="1" id="4" href="#">View more</a>
					</div>
				</div>

				<div class="column mr">
					<div class="title">
					<?php // pull_field //tablename, field you want, where 
						$rec17=pull_field("lead_stage_settings","id", "WHERE usergroup='$_SESSION[usergroup]' AND rel_id=3");
						?>
					
						<a href="<?=engine_url()?>/controller.php?pg=461&add=1&module_id=1&width=850&height=550&title=New&rec=<?php echo $rec17; ?>&jqmRefresh=true&ref=3" title="Stage Settings" id="tour_stage_settings" class="thickbox opt" alt="Stage Settings"><i class="fa fa-cog"></i></a>
						<?php // get new
						stages_block(array('status'=>'3'));
						?>
						<div class="t_text">Keep Warm</div>
						<div class="sub_text">Enquiries to keep warm</div>
					</div>

					<ul id="3" class="free">
						<?php 
						// get new
						user_block(array('status'=>'3','color'=>$colorfilter));
						?>
					</ul>
					<div class="view_more">
						<a page="1" id="3" href="#">View more</a>
					</div>
				</div>
				
				
				
				

				<div class="column">
					<div class="title">
					
					<?php // pull_field //tablename, field you want, where 
						$rec25=pull_field("lead_stage_settings","id", "WHERE usergroup='$_SESSION[usergroup]' AND rel_id=8");
						?>
					
						<a href="<?=engine_url()?>/controller.php?pg=461&add=1&module_id=1&width=850&height=550&title=New&rec=<?php echo $rec25; ?>&jqmRefresh=true&ref=8" title="Stage Settings" id="tour_stage_settings" class="thickbox opt" alt="Stage Settings"><i class="fa fa-cog"></i></a>
						<?php // get new
						stages_block(array('status'=>'8'));
						?>

						<div class="t_text">Ready to apply</div>
						<div class="sub_text">Enquiries ready to apply</div>
					</div>

					<ul id="8" class="free">
							<?php 
              // get new
              user_block(array('status'=>'8','color'=>$colorfilter));
              ?>
					</ul>
					<div class="view_more">
						<a page="1" id="8" href="#">View more</a>
					</div>
				</div>

				<div class="clearfix"></div>


			</div>
		</div>


		
		
		
		
		<div class="modal fade bs-example-modal-sm" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel">
  <div class="modal-dialog modal-sm" role="document">
    <div class="modal-content">
      <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table">
  <thead>
  	<tr>
      <th>Stage</th>
      <th>Show</th>
      <th>&nbsp;</th>
    </tr>
  	
  </thead>    
     
  <tbody>
  
  </tbody>
</table>

    </div>
  </div>
</div>

		
	<!--Applicnt details Modal-->
	<div class="modal fade" id="myModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h2 class="modal-title">Enquiry Summary</h2>
      </div>
      <div class="modal-body">
  
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->


<script type="text/javascript">
	$(document).ready(function(){

		$(document).on('click', '#myModal2 .modal-header .close', function(e){
			$('#myModal2').modal("hide");
			$('#myModal').modal("show");
		});

		var getUrlParameter = function getUrlParameter(sParam,sUrl) {
		    var sPageURL = sUrl,
		        sURLVariables = sPageURL.split('&'),
		        sParameterName,
		        i;

		    for (i = 0; i < sURLVariables.length; i++) {
		        sParameterName = sURLVariables[i].split('=');

		        if (sParameterName[0] === sParam) {
		            return sParameterName[1] === undefined ? true : sParameterName[1];
		        }
		    }
		};

		
    $(document).on('click', '.modal_link', function(e){
		  e.preventDefault();
		  $('#myModal').modal("hide");
		  var title = $(this).attr("title");
		  var height = getUrlParameter('height',$(this).attr('href'));
		  $('#myModal2').find('.modal-header h2').html(title);
		  $('#myModal2').find('.modal-body').html('<iframe src="'+$(this).attr('href')+'" height="'+height+'px" width="100%"></iframe>');
		  //$('#myModal2').find('.modal-body').load($(this).attr('href'));
		  $('#myModal2').modal('show');
		});
	});
</script>

<style type="text/css">
	.modal-content .container{ width: 100%; padding: 30px; padding-top: 15px; padding-bottom: 0; margin: 0; }
</style>
	<div class="modal fade" id="myModal2" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h2 class="modal-title"></h2>
      </div>
      <div class="modal-body">
        
      </div>
    </div>
  </div>
</div>
	
	
	<!-- 
Lead Stages

New = 1
Engaged = 2
Gone Cold =3
Keep Warm =17
Ready to Apply = 25


-->

<script type="text/javascript">
            $(function () {
                $(document).ready(function () {


                		function update_numbers(){
                			li_count = origin.find("li").length;
                			//origin.parent().find(".title .count").html(receiver);
                		}

                		$(document).on('click', '.view_more a', function(e){
                			e.preventDefault();
                			more_btn = $(this);
                			$(this).html("Loading...")
                			var status = $(this).attr("id");
                			var page = $(this).attr("page");
                			var results_color = $("#results_color").val();
                			next_page = Number(page)+1;

                			$(this).attr("page",next_page);

                			ul_list_wrap = $(this).parent().parent().find("ul");
                			$.ajax({
                        type: "POST",
                        url: "<?php echo engine_url(); ?>/ajax/ajax_enquiry_results.php",
                        data: {status: status,color: results_color,page:next_page},
                        cache: "false",
                        success: function (data) {
                        	more_btn.html("View More");
                        	if (data.indexOf("li") >= 0){
                        		ul_list_wrap.append(data);
                        	}else{
                        		more_btn.hide();
                        	}
                          
                        }
                      });
                		});

                		$(document).on('click', '.show_student_profile', function(e){
                			$('#myModal .modal-body').html('<div class="text-center">Loading...</div>');
                			var student_id = $(this).attr("student_id");
                			/*alert(student_id);*/
										  $.ajax({
                        type: "POST",
                        url: "<?php echo engine_url(); ?>/ajax/ajax_enquiry_profile.php?student_id="+student_id,
                        data: {student_id: student_id},
                        cache: "false",
                        success: function (data) {
                          $('#myModal .modal-body').html(data);
                        }
                      });
										});

                    $("#1, #4, #3, #8").sortable({
                        helper: "clone",
                        opacity: 0.5,
                        connectWith: ".free",
                        start: function (event, ui) {
                        	var item_id = $(ui.item).attr('id');
                        	origin = $('#' + item_id).parent();
                        	li_count = origin.find("li").length;
                        	li_count = li_count-3;
                        	origin.parent().find(".title .count").html(li_count);
                        	//update_numbers();
                        	console.log(li_count+"?????");
                        },
                        stop: function (event, ui) {
                        	var item_id = $(ui.item).attr('id');
                        	var receiver = $('#' + item_id).parent().find("li").length;
                        	$('#' + item_id).parent().parent().find(".title .count").html(receiver);
                        	console.log(receiver+"****");
                        },

                        receive: function (event, ui) {
                            var item_id = $(ui.item).attr('id');
                            var receiver = $('#' + item_id).parent().attr('id');
                            var URL = $("#URL").val();
                            $.ajax({
                                type: "POST",
                                url: "<?php echo engine_url(); ?>/ajax/ajax_mrn_enquiries.php",
                                data: {item_id: item_id, new_status: receiver},
                                cache: "false",

                                success: function (data) {
                                    if (receiver == 'completed') {
                                        $(ui.item).attr("data-status","seven_days");
                                    }

                                }

                            });
                        }
                    });
                    $("#1, #4, #3, #8").disableSelection();
                });
            });
        </script>

