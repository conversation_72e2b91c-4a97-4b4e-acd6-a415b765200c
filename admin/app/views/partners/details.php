<link rel="stylesheet" href="<?php echo $this->base_url(); ?>assets/js/scrolling_tabs/jquery.scrolling-tabs.min.css">
<script src="<?php echo $this->base_url(); ?>assets/js/scrolling_tabs/jquery.scrolling-tabs.min.js"></script>
<script>
	$(document).ready(function (e) {
		$('.scrollable-tabs').scrollingTabs({
		});
	})
</script>
<style>

</style>
<script>
	app.controller('demo', ['$scope','$rootScope','$filter','$http','$window', function($scope,$rootScope,$filter,$http,$window){}]);
</script>

<div ng-controller="demo" class="ng-cloak" id="main_controller" >		
	<div class="breadcrumbs">
		<a href="<?php echo $this->base_url("partners"); ?>">
			<i class="fa fa-chevron-left" aria-hidden="true"></i> 
			Partners
		</a>
	</div>

	<div class="top_section">
		<h1>
			Partner Details
		</h1>
	</div>

	<div class="">
		<div class="row container">
			<div class="col-sm-12">
				<div class="panel panel-default">
					<div class="row">
						<div class="col-md-2">
							<?php
							if($data["entry"]["logo"]){
								$image = $data["entry"]["logo"];
							}else{
								$image = $this->base_url("assets/images/no_image.jpg");
							}
							?>
							<img src="<?php echo $image; ?>" class="img-circle" alt="no image" width="100" border="0"
							id="tour_profile_photo" onerror="this.src='/engine/images/blank_user_icon_fallback.png'">
						</div>
						<div class="col-md-4">
							<h2><?php echo $data['entry']['partner_name']; ?></h2>
							<p><strong>Email: </strong> <?php echo $data['entry']['email_address']; ?></p>
							<p><strong>Main Contact: </strong> <?php echo $data['entry']['first_name'] . ' ' . $data['entry']['surname']; ?></p>
							<p><strong>Telephone: </strong> <?php echo $data['entry']['telephone_number']; ?></p>
							<p><strong>Type: </strong> <?php echo $data['entry']['type']; ?></p>
							<p><strong>Website :</strong> <?php echo $data['entry']['website']; ?></p>
						</div>
						<div class="btn-group  btn-group-sml pull-right">
							<!--Start button group -->
							<div class="btn-group">
								<button type="button" class="btn btn-warning">Actions</button>
								<button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown" style="height: 32px">
									<span class="caret"></span>
									<span class="sr-only">Toggle Dropdown</span>
								</button>
								<ul class="dropdown-menu" role="menu">
									<li>
										<a class="thickbox"
										href="<?php echo engine_url ?>/controller.php?pg=126&rec=<?php echo $data["entry"]["id"]; ?>&width=850&height=600&jqmRefresh=true">
										<i class="fa fa-edit"></i> Edit Partner</a>
									</li>
									<li>
										<?php
										if ($data['entry']['email_address']) {
											?>
											<a class="thickbox"
											href="<?php echo engine_url ?>/modules/inc_send_email.php?vw=<?php echo $data["entry"]["id"]; ?>&ref=<?php echo $data["entry"]["id"]; ?>&partner_id=<?php echo $data["entry"]["id"]; ?>&width=850&height=600&jqmRefresh=true">
											<i class="fa fa-envelope"></i>
											Email Partner
										</a>
										<?php
									} else {
										?>
										<a class=""
										href="#" onclick="alert('Partner has no email address')">
										<i class="fa fa-envelope"></i>
										Email Partner
									</a>
									<?php

								}
								?>
							</li>

							<li>
								<a href="<?= engine_url ?>/controller.php?width=700&height=500&vw=<?= $data["entry"]["id"]; ?>&pick_page=148&ref=<?= $data["entry"]["id"]; ?>&jqmRefresh=true" class="thickbox" title="Add Task" alt="Add Task">
									<i class="fa fa-tasks"></i> 
									Add Task
								</a>
							</li>
						</ul>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">
					<div id="dynamic_tabs_container" class="container">
						<ul id="dynamic_tabs" class="nav nav-tabs scrollable-tabs" role="tablist">
							<?php  get_view_loop(10,'tabs', false, $data['entry']['views']); ?>
						</ul>
						<div class="tab-content">
							<?php get_view_loop(10,'blocks',false,$data['entry']['views']); ?>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
</div>
	<!------------------------------------
	** Add a page options
	-------------------------------------->
	<div class="modal fade" id="addPageOptions" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
						aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel">Add a page</h4>
					</div>
					<div class="modal-body" style="padding: 30px;">
						<div class="row">
							<div class="col-sm-6">
								<div class="btn btn-default btn-block" style="height: 150px; font-size: 16px; color: #666;"
								data-dismiss="modal" data-toggle="modal" data-target="#available_pages_modal">
								<i class="fa fa-file"
								style="font-size: 54px; margin-top: 30px; margin-bottom: 10px;"></i>
								<div class="clearfix"></div>
								Existing Page
							</div>
						</div>
						<div class="col-sm-6">
							<div class="btn btn-default btn-block" style="height: 150px; font-size: 16px; color: #666;"
							ng-click="show_create_new_page_modal()">
							<i class="fa fa-plus"
							style="font-size: 54px; margin-top: 30px; margin-bottom: 10px;"></i>
							<div class="clearfix"></div>
							New Page
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>


	<!------------------------------------
	** Add a landing page modal
	-------------------------------------->
	<div class="modal fade" id="add_landing_page_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
						aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel">Landing Page</h4>
					</div>
					<div class="modal-body" style="padding: 30px;">
						<div class="row">
							<div class="col-sm-6">
								<div class="btn btn-default btn-block"
								style="height: 150px; font-size: 16px; color: #666; display: none;"
								data-dismiss="modal" data-toggle="modal" data-target="#available_checklists_modal">
								<i class="fa fa-file"
								style="font-size: 54px; margin-top: 30px; margin-bottom: 10px;"></i>
								<div class="clearfix"></div>
								Existing Landing Page
							</div>
						</div>
						<div class="col-sm-6">
							<div class="btn btn-default btn-block" style="height: 150px; font-size: 16px; color: #666;"
							ng-click="add_new_landing_page()">
							<i class="fa fa-plus"
							style="font-size: 54px; margin-top: 30px; margin-bottom: 10px;"></i>
							<div class="clearfix"></div>
							New Landing Page
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>


	<!------------------------------------
	** Assign form to page
	-------------------------------------->
	<div class="modal fade" id="assignFormModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
						aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel">Attach a form to page</h4>
					</div>
					<div class="modal-body" style="padding: 30px;">
						<div class="row" ng-hide="form_type">

							<div class="col-sm-4">
								<div class="btn btn-default btn-block" style="height: 150px; font-size: 16px; color: #666;"
								ng-click="set_form_type('system_form')">
								<i class="fa fa-cube"
								style="font-size: 54px; margin-top: 20px; margin-bottom: 10px;"></i>
								<div class="clearfix"></div>
								Standard Forms
							</div>
						</div>

						<div class="col-sm-4">
							<div class="btn btn-default btn-block" style="height: 150px; font-size: 16px; color: #666;"
							ng-click="set_form_type('existing_custom_form')">
							<i class="fa fa-file"
							style="font-size: 54px; margin-top: 20px; margin-bottom: 10px;"></i>
							<div class="clearfix"></div>
							Existing Custom<br>Form
						</div>
					</div>

					<div class="col-sm-4">
						<div class="btn btn-default btn-block" style="height: 150px; font-size: 16px; color: #666;"
						ng-click="set_form_type('new_custom_form')">
						<i class="fa fa-plus"
						style="font-size: 54px; margin-top: 20px; margin-bottom: 10px;"></i>
						<div class="clearfix"></div>
						New Custom<br> Form
					</div>
				</div>

			</div>


			<div class="row" ng-show="form_type=='system_form'">
				<h4 class="next-heading">Choose a standard form to attach:</h4>
				<div style="padding: 5px; background-color: #f1f1f1; border-radius: 5px; height: 400px; overflow: auto;">
					<table class="table table-striped" style="background-color: #fff; margin: 0;">
						<tbody>
							<tr ng-repeat="cForm in standard_forms">
								<td><a href="#" ng-click="attach_form(cForm)">{{ cForm.title}}</a></td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>

			<div class="row" ng-show="form_type=='existing_custom_form'">
				<h4 class="next-heading">Choose a form to attach:</h4>
				<div style="padding: 5px; background-color: #f1f1f1; border-radius: 5px; height: 400px; overflow: auto;">
					<table class="table table-striped" style="background-color: #fff; margin: 0;">
						<tbody>
							<tr ng-repeat="cForm in custom_forms">
								<td><a href="#" ng-click="attach_form(cForm)">{{ cForm.title}}</a></td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>


			<div class="row" ng-show="form_type=='new_custom_form'">

				<h4 class="next-heading" style="margin-top: 0;">What is the name of the form?</h4>
				<input type="text" style="font-size: 18px; height: 42px;" ng-model="new_custom_form_name"
				id="new_custom_form_name" class="form-control ssb">
				<input type="button" ng-click="assign_new_custom_form()" value="Create form"
				class="btn btn-block btn-primary">
			</div>

		</div>
	</div>
</div>
</div>


	<!------------------------------------
	** Show available Pages Modal
	-------------------------------------->
	<div class="modal fade" id="available_pages_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
						aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel">Available Pages</h4>
					</div>
					<div class="modal-body">
						<input class="form-control ssb" type="text" placeholder="Search for a page" ng-model="search_pages">
						<div id="modules_wrap">
							<div class="admin_module" id="item_<?php echo $module['id']; ?>"
								style=" height: 500px; overflow: auto;">
								<ul class="connectedSortable sortable" id="<?php echo $module['id']; ?>">
									<li id="item_{{ field.id }}" class="{{field.type}}"
									ng-repeat="page in all_pages | filter:{title: search_pages } | orderBy:'navigation_title'"
									style="padding-left: 10px; padding-right: 40px;">
									<div class="btn-group pull-right" role="group" aria-label="...">
										<a href="javascript:;" class="btn btn-default btn-xs"
										ng-click="add_page_to_selected($index,page)"><i class="fa fa-plus"
										aria-hidden="true"></i></a>
									</div>
									
									<div class="title" ng-show="page.navigation_title">
										<a href="javascript:;" ng-click="add_page_to_selected($index,page)">
											{{ page.navigation_title }}
										</a>
									</div>
								</li>
							</ul>
							<li class="add_unit">
								<a href="javascript:;" ng-click="show_create_new_page_modal()" class="title"><i
									class="fa fa-plus" aria-hidden="true"></i> Create a new page</a>
								</li>
							</div>	
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

</div>

