<div ng-controller="demo" class="inner_container">
	<div class="top_section">
		<div class="buttons_wrap">
		
		</div>
		<div class="breadcrumbs">
                <a href="<?php echo website_url; ?>/admin/bulk_imports/dashboard"><i class="fa fa-chevron-left" aria-hidden="true"></i> Dashboard</a>
            </div>
		<h1>
				<?php echo $data['tablename']; ?> Records
		</h1>
	</div>
	<ul class="next-tab_list">
		<li>
			<a class="">All Temporarily imported <?=$data['tablename']?></a>
		</li>
	</ul>
	<div class="stnrd_box">
			<?php if(count($data['table_data']) || count($data['filters_args'])){
	 ?>
	 			<?php //print_r($data["results"]); ?>
	 			
				<?php require(ABSPATH."app/views/bulk_imports/inc_dynamic_table.php"); ?>
			<?php }else{ ?>

				<div class="no_results">
					
					<?php if($_REQUEST['search']){ ?>
						<span class="glyphicon glyphicon-search"></span>
						<h2>No results match you search criteria</h2>
					<?php }else{ ?>
						<span class="glyphicon glyphicon-user"></span>
						<h2>You haven't imported any <?=$data['tablename']?> records yet</h2>
						<!-- ADD LINK TO UPLOADER -->
						<a href="<?php echo website_url."/engine/dynamic_view.php?id=bulk_import&tlb=".$this->uri_segement(3); ?>" class="btn btn-primary">Bulk Import <?=$data['tablename']?></a>
					<?php } ?>
					
				</div>
			<?php } ?>
		</div>
</div>


<!--SELECT `name`,db_field_name,CONCAT(sc.sys_cat_abv,'_',page_name) as tb,sp.page_id,`type`  FROM system_table st INNER JOIN system_pages sp ON st.pg_id = sp.page_id INNER JOIN system_cat sc ON sc.sys_cat_id = sp.project  WHERE st.usergroup = 64 AND st.db_field_name IN ('db888'
,'db39','db40','db46','db763','db53','db890','db1682','db889','db764','db770')-->