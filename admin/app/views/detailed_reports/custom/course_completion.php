<?php

$dbh = get_dbh();

$code = "SELECT id, db232 as 'coursename' FROM core_courses WHERE usergroup='$_SESSION[usergroup]' and (rec_archive is null or rec_archive ='')";
$sql = $dbh->prepare("$code");
$sql->execute();
$active_courses = $sql->fetchAll(PDO::FETCH_OBJ);

$specific_usergroups = pull_field("sis_primary_groups","count(id)","where usergroup = '$_SESSION[usergroup]'");
if (!empty ($specific_usergroups)) {
   $usergroup_specific = " AND usergroup = '$_SESSION[usergroup]'";
}
else {
    $usergroup_specific = " AND usergroup = '1'";
}
?>

<div id="main_controller" >
    <div class="inner_container">
        <div class="breadcrumbs">
            <a href="<?php echo $this->base_url('detailed_reports'); ?>"><i class="fa fa-chevron-left" aria-hidden="true"></i>Overview</a>
        </div>

        <div class="top_section">
            <h1><?php echo $data['meta_title']; ?></h1>
</div>

<ul class="next-tab_list">
    <li><a class="active" href="?">All results</a></li>
</ul>
<div class="stnrd_box dynamic_wrap">
    <div class="clear"></div>
    <div  class="pull-right">
        <select id='course_dropdown' class="my_pickers text-white bg-primary bigger-text" data-size="8" >
            <option value="">Select a course to view</option>
            <?php foreach ($active_courses as $active_course) { ?>
                <option value="<?php echo $active_course['id'];?>"><?php echo $active_course['coursename'];?> ></option>
            <?php } ?>
        </select>
    </div>
    <br>
    <br>
    <hr>
 <?php
 echo"
	<script>
	
		$(function () 
		{
		$('#course_dropdown').change(function(){
			
			if(this.value){
				var course_id = this.value;
			}
			$.ajax({                                      
			  url: '/admin/detailed_reports/custom/student_course_completion_report',      
			  data: \"course_id=\"+course_id\", 
			  dataType: 'html',
			  type:'POST',   
			  success: function(rows)
			  {		
				console.log ('SUCCESS');
			  }
			});
		  }); 
		}); 
	</script>
	";
echo"<div class=\"stnrd_box\">";

//echo '<table id="data_table" class="sortable table table-striped" cellspacing="0">
echo '<table id="data_table 1" style="border-spacing:10px;border-collapse: separate;">
		<thead>
		<tr>
            <th>Course Name</th>
            <th>Student group</th>
            <th>Number of Incidents</th>
            <th>Current Month</th>
            <th>'. date("F t, Y", strtotime("-1 months")).'</th>
            <th>'. date("F t, Y", strtotime("-2 months")).'</th>
            <th>'. date("F t, Y", strtotime("-3 months")).'</th>
            <th>'. date("F t, Y", strtotime("-4 months")).'</th>
            <th>'. date("F t, Y", strtotime("-5 months")).'</th>
		</tr>
		</thead>
		<tbody>';

foreach($active_courses AS $active_course){
    /*$code =
        "SELECT  ssg.id, db48829 as 'group', 
                (SELECT count(*) FROM sis_scheduled_booking JOIN sis_profiles on sis_scheduled_booking.rel_id = sis_profiles.id JOIN sis_course_schedule on sis_scheduled_booking.db14978 = sis_course_schedule.id WHERE sis_scheduled_booking.usergroup='$_SESSION[usergroup]' AND (sis_scheduled_booking.rec_archive is null or sis_scheduled_booking.rec_archive = '') and (db14979='3' or db14979 ='4' or db14979='9') AND ssg.id = db48609 AND db14977 = '$active_course->id' AND sis_course_schedule.db14947 <= last_day(now()-INTERVAL 0 MONTH)) as 'now',
                (SELECT count(*) FROM sis_scheduled_booking JOIN sis_profiles on sis_scheduled_booking.rel_id = sis_profiles.id JOIN sis_course_schedule on sis_scheduled_booking.db14978 = sis_course_schedule.id WHERE sis_scheduled_booking.usergroup='$_SESSION[usergroup]' AND (sis_scheduled_booking.rec_archive is null or sis_scheduled_booking.rec_archive = '') and (db14979='3' or db14979 ='4' or db14979='9') AND (db14981=1 OR db14981 =2) AND ssg.id = db48609 AND db14977 = '$active_course->id' AND sis_course_schedule.db14947 <= last_day(now()-INTERVAL 0 MONTH)) as 'attendednow',
                (SELECT count(*) FROM sis_scheduled_booking JOIN sis_profiles on sis_scheduled_booking.rel_id = sis_profiles.id JOIN sis_course_schedule on sis_scheduled_booking.db14978 = sis_course_schedule.id WHERE sis_scheduled_booking.usergroup='$_SESSION[usergroup]' AND (sis_scheduled_booking.rec_archive is null or sis_scheduled_booking.rec_archive = '') and (db14979='3' or db14979 ='4' or db14979='9') AND ssg.id = db48609 AND db14977 = '$active_course->id' AND sis_course_schedule.db14947 <= last_day(now()-INTERVAL 1 MONTH)) as 'month1',
                (SELECT count(*) FROM sis_scheduled_booking JOIN sis_profiles on sis_scheduled_booking.rel_id = sis_profiles.id JOIN sis_course_schedule on sis_scheduled_booking.db14978 = sis_course_schedule.id WHERE sis_scheduled_booking.usergroup='$_SESSION[usergroup]' AND (sis_scheduled_booking.rec_archive is null or sis_scheduled_booking.rec_archive = '') and (db14979='3' or db14979 ='4' or db14979='9') AND (db14981=1 OR db14981 =2) AND ssg.id = db48609 AND db14977 = '$active_course->id' AND sis_course_schedule.db14947 <= last_day(now()-INTERVAL 1 MONTH)) as 'attendedmonth1',
                (SELECT count(*) FROM sis_scheduled_booking JOIN sis_profiles on sis_scheduled_booking.rel_id = sis_profiles.id JOIN sis_course_schedule on sis_scheduled_booking.db14978 = sis_course_schedule.id WHERE sis_scheduled_booking.usergroup='$_SESSION[usergroup]' AND (sis_scheduled_booking.rec_archive is null or sis_scheduled_booking.rec_archive = '') and (db14979='3' or db14979 ='4' or db14979='9') AND ssg.id = db48609 AND db14977 = '$active_course->id' AND sis_course_schedule.db14947 <= last_day(now()-INTERVAL 2 MONTH)) as 'month2',
                (SELECT count(*) FROM sis_scheduled_booking JOIN sis_profiles on sis_scheduled_booking.rel_id = sis_profiles.id JOIN sis_course_schedule on sis_scheduled_booking.db14978 = sis_course_schedule.id WHERE sis_scheduled_booking.usergroup='$_SESSION[usergroup]' AND (sis_scheduled_booking.rec_archive is null or sis_scheduled_booking.rec_archive = '') and (db14979='3' or db14979 ='4' or db14979='9') AND (db14981=1 OR db14981 =2) AND ssg.id = db48609 AND db14977 = '$active_course->id' AND sis_course_schedule.db14947 <= last_day(now()-INTERVAL 2 MONTH)) as 'attendedmonth2',
                (SELECT count(*) FROM sis_scheduled_booking JOIN sis_profiles on sis_scheduled_booking.rel_id = sis_profiles.id JOIN sis_course_schedule on sis_scheduled_booking.db14978 = sis_course_schedule.id WHERE sis_scheduled_booking.usergroup='$_SESSION[usergroup]' AND (sis_scheduled_booking.rec_archive is null or sis_scheduled_booking.rec_archive = '') and (db14979='3' or db14979 ='4' or db14979='9') AND ssg.id = db48609 AND db14977 = '$active_course->id' AND sis_course_schedule.db14947 <= last_day(now()-INTERVAL 3 MONTH)) as 'month3',
                (SELECT count(*) FROM sis_scheduled_booking JOIN sis_profiles on sis_scheduled_booking.rel_id = sis_profiles.id JOIN sis_course_schedule on sis_scheduled_booking.db14978 = sis_course_schedule.id WHERE sis_scheduled_booking.usergroup='$_SESSION[usergroup]' AND (sis_scheduled_booking.rec_archive is null or sis_scheduled_booking.rec_archive = '') and (db14979='3' or db14979 ='4' or db14979='9') AND (db14981=1 OR db14981 =2) AND ssg.id = db48609 AND db14977 = '$active_course->id' AND sis_course_schedule.db14947 <= last_day(now()-INTERVAL 3 MONTH)) as 'attendedmonth3',
                (SELECT count(*) FROM sis_scheduled_booking JOIN sis_profiles on sis_scheduled_booking.rel_id = sis_profiles.id JOIN sis_course_schedule on sis_scheduled_booking.db14978 = sis_course_schedule.id WHERE sis_scheduled_booking.usergroup='$_SESSION[usergroup]' AND (sis_scheduled_booking.rec_archive is null or sis_scheduled_booking.rec_archive = '') and (db14979='3' or db14979 ='4' or db14979='9') AND ssg.id = db48609 AND db14977 = '$active_course->id' AND sis_course_schedule.db14947 <= last_day(now()-INTERVAL 4 MONTH)) as 'month4',
                (SELECT count(*) FROM sis_scheduled_booking JOIN sis_profiles on sis_scheduled_booking.rel_id = sis_profiles.id JOIN sis_course_schedule on sis_scheduled_booking.db14978 = sis_course_schedule.id WHERE sis_scheduled_booking.usergroup='$_SESSION[usergroup]' AND (sis_scheduled_booking.rec_archive is null or sis_scheduled_booking.rec_archive = '') and (db14979='3' or db14979 ='4' or db14979='9') AND (db14981=1 OR db14981 =2) AND ssg.id = db48609 AND db14977 = '$active_course->id' AND sis_course_schedule.db14947 <= last_day(now()-INTERVAL 4 MONTH)) as 'attendedmonth4',
                (SELECT count(*) FROM sis_scheduled_booking JOIN sis_profiles on sis_scheduled_booking.rel_id = sis_profiles.id JOIN sis_course_schedule on sis_scheduled_booking.db14978 = sis_course_schedule.id WHERE sis_scheduled_booking.usergroup='$_SESSION[usergroup]' AND (sis_scheduled_booking.rec_archive is null or sis_scheduled_booking.rec_archive = '') and (db14979='3' or db14979 ='4' or db14979='9') AND ssg.id = db48609 AND db14977 = '$active_course->id' AND sis_course_schedule.db14947 <= last_day(now()-INTERVAL 5 MONTH)) as 'month5',
                (SELECT count(*) FROM sis_scheduled_booking JOIN sis_profiles on sis_scheduled_booking.rel_id = sis_profiles.id JOIN sis_course_schedule on sis_scheduled_booking.db14978 = sis_course_schedule.id WHERE sis_scheduled_booking.usergroup='$_SESSION[usergroup]' AND (sis_scheduled_booking.rec_archive is null or sis_scheduled_booking.rec_archive = '') and (db14979='3' or db14979 ='4' or db14979='9') AND (db14981=1 OR db14981 =2) AND ssg.id = db48609 AND db14977 = '$active_course->id' AND sis_course_schedule.db14947 <= last_day(now()-INTERVAL 5 MONTH)) as 'attendedmonth5'
                 FROM sis_primary_groups ssg 
                where (rec_archive is null or rec_archive ='') ";*/

    $code =
        "SELECT  ssg.id, db48829 as 'group',
            (SELECT count(*)
            FROM sis_sched_booking_detail 
            LEFT JOIN sis_scheduled_booking on sis_scheduled_booking.id = cast( sis_sched_booking_detail.db15052 AS UNSIGNED)  
            LEFT JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED)
            LEFT JOIN core_students ON core_students.id = sis_sched_booking_detail.rel_id and core_students.usergroup = '$_SESSION[usergroup]'
            LEFT JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id  
            WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]' 
            AND (sis_sched_booking_detail.rec_archive is null or sis_sched_booking_detail.rec_archive = '') 
            AND (sis_profiles.rec_archive is null or sis_profiles.rec_archive = '')
            AND db14959 = '7'
            AND db59978 IN ('2','3','14') 
            AND ssg.id = db48609  
            AND db14946 = '$active_course->id'
            AND sis_course_schedule.db14949 <= DATE(now())) as 'now',
 
            (SELECT count(*)
            FROM sis_sched_booking_detail 
            LEFT JOIN sis_scheduled_booking on sis_scheduled_booking.id = CAST( sis_sched_booking_detail.db15052 AS UNSIGNED)  
            LEFT JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED)
            LEFT JOIN core_students ON core_students.id = sis_sched_booking_detail.rel_id and core_students.usergroup = '$_SESSION[usergroup]'
            LEFT JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id 
            WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]' 
            AND (sis_sched_booking_detail.rec_archive is null or sis_sched_booking_detail.rec_archive = '') 
            AND (sis_profiles.rec_archive is null or sis_profiles.rec_archive = '')
            AND db14959 = '7' AND (db64664 IN ('1','2' )) 
            AND db59978 IN ('2','3','14') 
            AND ssg.id = db48609 
            AND db14946 = '$active_course->id'  
            AND sis_course_schedule.db14949 <= DATE(now())) as 'attendednow',

            (SELECT count(*)
            FROM sis_sched_booking_detail 
            LEFT JOIN sis_scheduled_booking on sis_scheduled_booking.id = cast( sis_sched_booking_detail.db15052 AS UNSIGNED)  
            LEFT JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED)
            LEFT JOIN core_students ON core_students.id = sis_sched_booking_detail.rel_id and core_students.usergroup = '$_SESSION[usergroup]'
            LEFT JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id   
            WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]' 
            AND (sis_sched_booking_detail.rec_archive is null or sis_sched_booking_detail.rec_archive = '') 
            AND (sis_profiles.rec_archive is null or sis_profiles.rec_archive = '')
            AND db14959 = '7'
            AND db59978 IN ('2','3','14')   
            AND ssg.id = db48609 
            AND db14946 = '$active_course->id'
            AND sis_course_schedule.db14949 <= last_day(now()-INTERVAL 1 MONTH)) as 'month1',
                
                
           (SELECT count(*)
            FROM sis_sched_booking_detail 
            LEFT JOIN sis_scheduled_booking on sis_scheduled_booking.id = CAST( sis_sched_booking_detail.db15052 AS UNSIGNED)  
            LEFT JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED)
            LEFT JOIN core_students ON core_students.id = sis_sched_booking_detail.rel_id and core_students.usergroup = '$_SESSION[usergroup]'
            LEFT JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id 
            WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]' 
            AND (sis_sched_booking_detail.rec_archive is null or sis_sched_booking_detail.rec_archive = '') 
            AND (sis_profiles.rec_archive is null or sis_profiles.rec_archive = '')
            AND db14959 = '7' AND (db64664 IN ('1','2' )) 
            AND db59978 IN ('2','3','14') 
            AND ssg.id = db48609 
            AND db14946 = '$active_course->id'  
            AND sis_course_schedule.db14949 <= last_day(now()-INTERVAL 1 MONTH)) as 'attendedmonth1',
            
                                                                                                                                                                                                                       
            (SELECT count(*)
            FROM sis_sched_booking_detail 
            LEFT JOIN sis_scheduled_booking on sis_scheduled_booking.id = cast( sis_sched_booking_detail.db15052 AS UNSIGNED)  
            LEFT JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED)
            LEFT JOIN core_students ON core_students.id = sis_sched_booking_detail.rel_id and core_students.usergroup = '$_SESSION[usergroup]'
            LEFT JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id  
            WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]' 
            AND (sis_sched_booking_detail.rec_archive is null or sis_sched_booking_detail.rec_archive = '') 
            AND (sis_profiles.rec_archive is null or sis_profiles.rec_archive = '')
            AND db14959 = '7'
            AND db59978 IN ('2','3','14')  
            AND ssg.id = db48609 
            AND db14946 = '$active_course->id'
            AND sis_course_schedule.db14949 <= last_day(now()-INTERVAL 2 MONTH)) as 'month2',
                
                
            (SELECT count(*)
            FROM sis_sched_booking_detail 
            LEFT JOIN sis_scheduled_booking on sis_scheduled_booking.id = CAST( sis_sched_booking_detail.db15052 AS UNSIGNED)  
            LEFT JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED)
            LEFT JOIN core_students ON core_students.id = sis_sched_booking_detail.rel_id and core_students.usergroup = '$_SESSION[usergroup]'
            LEFT JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id 
            WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]' 
            AND (sis_sched_booking_detail.rec_archive is null or sis_sched_booking_detail.rec_archive = '') 
            AND (sis_profiles.rec_archive is null or sis_profiles.rec_archive = '')
            AND db14959 = '7' AND (db64664 IN ('1','2' )) 
            AND db59978 IN ('2','3','14') 
            AND ssg.id = db48609 
            AND db14946 = '$active_course->id'  
            AND sis_course_schedule.db14949 <= last_day(now()-INTERVAL 2 MONTH)) as 'attendedmonth2',
                
                                                                                                                                                                                                                       
                (SELECT count(*)
            FROM sis_sched_booking_detail 
            LEFT JOIN sis_scheduled_booking on sis_scheduled_booking.id = cast( sis_sched_booking_detail.db15052 AS UNSIGNED)  
            LEFT JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED)
            LEFT JOIN core_students ON core_students.id = sis_sched_booking_detail.rel_id and core_students.usergroup = '$_SESSION[usergroup]'
            LEFT JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id  
            WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]' 
            AND (sis_sched_booking_detail.rec_archive is null or sis_sched_booking_detail.rec_archive = '') 
            AND (sis_profiles.rec_archive is null or sis_profiles.rec_archive = '')
            AND db14959 = '7'
            AND db59978 IN ('2','3','14') 
            AND ssg.id = db48609 
            AND db14946 = '$active_course->id'
            AND sis_course_schedule.db14949 <= last_day(now()-INTERVAL 3 MONTH)) as 'month3',
                
            (SELECT count(*)
            FROM sis_sched_booking_detail 
            LEFT JOIN sis_scheduled_booking on sis_scheduled_booking.id = CAST( sis_sched_booking_detail.db15052 AS UNSIGNED)  
            LEFT JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED)
            LEFT JOIN core_students ON core_students.id = sis_sched_booking_detail.rel_id and core_students.usergroup = '$_SESSION[usergroup]'
            LEFT JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id 
            WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]' 
            AND (sis_sched_booking_detail.rec_archive is null or sis_sched_booking_detail.rec_archive = '') 
            AND (sis_profiles.rec_archive is null or sis_profiles.rec_archive = '')
            AND db14959 = '7' AND (db64664 IN ('1','2' )) 
            AND db59978 IN ('2','3','14') 
            AND ssg.id = db48609 
            AND db14946 = '$active_course->id'  
            AND sis_course_schedule.db14949 <= last_day(now()-INTERVAL 3 MONTH)) as 'attendedmonth3',
                
            (SELECT count(*)
            FROM sis_sched_booking_detail 
            LEFT JOIN sis_scheduled_booking on sis_scheduled_booking.id = cast( sis_sched_booking_detail.db15052 AS UNSIGNED)  
            LEFT JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED)
            LEFT JOIN core_students ON core_students.id = sis_sched_booking_detail.rel_id and core_students.usergroup = '$_SESSION[usergroup]'
            LEFT JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id  
            WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]' 
            AND (sis_sched_booking_detail.rec_archive is null or sis_sched_booking_detail.rec_archive = '') 
            AND (sis_profiles.rec_archive is null or sis_profiles.rec_archive = '')
            AND db14959 = '7'
            AND db59978 IN ('2','3','14')  
            AND ssg.id = db48609 
            AND db14946 = '$active_course->id'
            AND sis_course_schedule.db14949 <= last_day(now()-INTERVAL 4 MONTH)) as 'month4',
                
            (SELECT count(*)
            FROM sis_sched_booking_detail 
            LEFT JOIN sis_scheduled_booking on sis_scheduled_booking.id = CAST( sis_sched_booking_detail.db15052 AS UNSIGNED)  
            LEFT JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED)
            LEFT JOIN core_students ON core_students.id = sis_sched_booking_detail.rel_id and core_students.usergroup = '$_SESSION[usergroup]'
            LEFT JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id 
            WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]' 
            AND (sis_sched_booking_detail.rec_archive is null or sis_sched_booking_detail.rec_archive = '') 
            AND (sis_profiles.rec_archive is null or sis_profiles.rec_archive = '')
            AND db14959 = '7' AND (db64664 IN ('1','2' )) 
            AND db59978 IN ('2','3','14') 
            AND ssg.id = db48609 
            AND db14946 = '$active_course->id'  
            AND sis_course_schedule.db14949 <= last_day(now()-INTERVAL 4 MONTH)) as 'attendedmonth4',
            
            (SELECT count(*)
            FROM sis_sched_booking_detail 
            LEFT JOIN sis_scheduled_booking on sis_scheduled_booking.id = cast( sis_sched_booking_detail.db15052 AS UNSIGNED)  
            LEFT JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED)
            LEFT JOIN core_students ON core_students.id = sis_sched_booking_detail.rel_id and core_students.usergroup = '$_SESSION[usergroup]'
            LEFT JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id  
            WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]' 
            AND (sis_sched_booking_detail.rec_archive is null or sis_sched_booking_detail.rec_archive = '') 
            AND (sis_profiles.rec_archive is null or sis_profiles.rec_archive = '')
            AND db14959 = '7'
            AND db59978 IN ('2','3','14') 
            AND ssg.id = db48609 
            AND db14946 = '$active_course->id'
            AND sis_course_schedule.db14949 <= last_day(now()-INTERVAL 5 MONTH)) as 'month5',
            
            (SELECT count(*)
            FROM sis_sched_booking_detail 
            LEFT JOIN sis_scheduled_booking on sis_scheduled_booking.id = CAST( sis_sched_booking_detail.db15052 AS UNSIGNED)  
            LEFT JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED)
            LEFT JOIN core_students ON core_students.id = sis_sched_booking_detail.rel_id and core_students.usergroup = '$_SESSION[usergroup]'
            LEFT JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id 
            WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]' 
            AND (sis_sched_booking_detail.rec_archive is null or sis_sched_booking_detail.rec_archive = '') 
            AND (sis_profiles.rec_archive is null or sis_profiles.rec_archive = '')
            AND db14959 = '7' AND (db64664 IN ('1','2' )) 
            AND db59978 IN ('2','3','14') 
            AND ssg.id = db48609 
            AND db14946 = '$active_course->id'  
            AND sis_course_schedule.db14949 <= last_day(now()-INTERVAL 5 MONTH)) as 'attendedmonth5'
                 
                 
                 FROM sis_primary_groups ssg 
                where (rec_archive is null or rec_archive ='') $usergroup_specific";



//echo "<pre>$code</pre>";
    dev_debug($code);
    $sql = $dbh->prepare("$code");
    $sql->execute();
    $active_groups = $sql->fetchAll(PDO::FETCH_OBJ);

    foreach($active_groups AS $active_group) {
        $nowpercent= 0;
        $month1percent = 0;
        $month2percent = 0;
        $month3percent = 0;
        $month4percent = 0;
        $month5percent = 0;

        if ($active_group->now > 0) {
            $nowpercent = round(($active_group->attendednow / $active_group->now) * 100,2);
            $month1percent = round(($active_group->attendedmonth1 / $active_group->month1) * 100,2);
            $month2percent = round(($active_group->attendedmonth2 / $active_group->month2) * 100,2);
            $month3percent = round(($active_group->attendedmonth3 / $active_group->month3) * 100,2);
            $month4percent = round(($active_group->attendedmonth4 / $active_group->month4) * 100,2);
            $month5percent = round(($active_group->attendedmonth5 / $active_group->month5) * 100,2);
        }

        echo "<tr>
                   
                    <td>$active_course->coursename</td>
                    <td>$active_group->group</td>
                    <td>Enrolled</td>
                    <td>$active_group->now</td>
                    <td>$active_group->month1</td>
                    <td>$active_group->month2</td>
                    <td>$active_group->month3</td>
                    <td>$active_group->month4</td>
                    <td>$active_group->month5</td>
                  </tr>";
        echo "<tr>
                   
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>Attended/partially attended</td>
                    <td>$active_group->attendednow</td>
                    <td>$active_group->attendedmonth1</td>
                    <td>$active_group->attendedmonth2</td>
                    <td>$active_group->attendedmonth3</td>
                    <td>$active_group->attendedmonth4</td>
                    <td>$active_group->attendedmonth5</td>
                  </tr>";

        echo "<tr>
                   
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>Percent</td>
                    <td>$nowpercent%</td>
                    <td>$month1percent%</td>
                    <td>$month2percent%</td>
                    <td>$month3percent%</td>
                    <td>$month4percent%</td>
                    <td>$month5percent%</td>
                  </tr>";

    }
    echo "<tr>
                   
                    <td><hr/></td>
                    <td><hr/></td>
                    <td><hr/></td>
                    <td><hr/></td>
                    <td><hr/></td>
                    <td><hr/></td>
                    <td><hr/></td>
                    <td><hr/></td>
                    <td><hr/></td>
                  </tr>";
}
echo'</tbody>
         <tfoot>
             <tr>
                <!--<th>Student ID</th>-->
		        <th>Course Name</th>
                <th>Student group</th>
                <th>Number of Incidents</th>
		        <th>Current month</th>
		        <th>'. date("F t, Y", strtotime("-1 months")).'</th>
		        <th>'. date("F t, Y", strtotime("-2 months")).'</th>
		        <th>'. date("F t, Y", strtotime("-3 months")).'</th>
		        <th>'. date("F t, Y", strtotime("-4 months")).'</th>
		        <th>'. date("F t, Y", strtotime("-5 months")).'</th>
		       
             </tr>
         </tfoot>
         </table>';

echo"</div></div>"; ?>

 </div>
    </div>
</div>
