<?php
/**
 * Created by PhpStorm.
 * User: andrew
 * Date: 9/27/19
 * Time: 9:19 AM
 */
$ki = 1;
foreach ($data['results'][0] as $key => $value) {
    if ($ki == 1) {
        $main_key = $key;
    }
    $ki++;
}

$include_archived = $_GET['include_archived'];
$filter_type = $data['filter_types'];


$default_filters_list = array();
foreach ($filter_type as $fff) {
    $default_filters_list[] = ucwords($fff['title']);
}

$default_filters_list = array_values($default_filters_list);

$filter_types_json = json_encode($filter_type);

$filter_type_small = array();
foreach($filter_type as $ff){
    if(!$ff['extra']){
        $ff['options'] = '';
        $filter_type_small[] = $ff;
    }
}

$include_untimed = $data['include_untimed'] == 1 ? 1:0;
$filter_type_small = json_encode($filter_type_small);


$filters = $data['filter']['description'];
if(!$filters){ $filters =array();}
$filters_json = json_encode($filters);
?>
<style>
    .date_filters .col-sm-3{
        text-align: center;
    }
    .date_filters .col-sm-3:not(:last-child){
        border-right: #777 3px solid;
    }
</style>
<div id="main_controller" >
    <div class="inner_container">
        <div class="breadcrumbs">
            <a href="<?php echo $this->base_url('detailed_reports'); ?>"><i class="fa fa-chevron-left" aria-hidden="true"></i>Overview</a>
        </div>

        <div class="top_section">
            <h1><?php echo $data['meta_title']; ?></h1>
            <p><?php echo $data['description']; ?></p>
        </div>

            <ul class="next-tab_list">
            <li><a class="active" href="?">All results</a></li>
        </ul>
        <div class="stnrd_box dynamic_wrap">
            <div class="clear"></div>
            <?php

$usergroups=usergroups_management();

function prev_date( $months)
{

    $date = new DateTime("now", new DateTimeZone('Europe/London') );

    // We extract the day of the month as $start_day
    $start_day = $date->format('j');

    // We add 1 month to the given date
    $date->modify("-{$months} month");

    // We extract the day of the month again so we can compare
    $end_day = $date->format('j');

    if ($start_day != $end_day)
    {

        // The day of the month isn't the same anymore, so we correct the date
        $date->modify('last day of last month');

    }

    return $date;
}
//Report should be the date that BOTH initial IPL and Recovery College Induction course have been completed

//Select all students that have both completed


$dbh = get_dbh();
$high_now_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED)
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now())
AND (
case WHEN (
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) >= 27.5";

$high_minus_1_month_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED) 
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now()-INTERVAL 1 MONTH)
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END ) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546)) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END ) ,0) END
) >= 27.5";

$high_minus_2_month_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED) 
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now()-INTERVAL 2 MONTH)
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END ) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546)) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) >= 27.5";

$high_minus_3_month_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED) 
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now()-INTERVAL 3 MONTH)
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546)) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) >= 27.5";

$high_minus_4_month_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED) 
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now()-INTERVAL 4 MONTH)
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END ) ,0) END
) >= 27.5";

$high_minus_5_month_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED) 
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now()-INTERVAL 5 MONTH)
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) >= 27.5";

$average_now_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED) 
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now())
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END ) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546)  ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) < 27.5
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) >= 19.5";

$average_minus_1_month_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED) 
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now()-INTERVAL 1 MONTH )
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) < 27.5
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) >= 19.5";

$average_minus_2_month_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED) 
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now()-INTERVAL 2 MONTH )
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +

(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) < 27.5
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END ) ,0) END
) >= 19.5";

$average_minus_3_month_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED) 
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now()-INTERVAL 3 MONTH )
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) < 27.5
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) >= 19.5";

$average_minus_4_month_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED) 
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now()-INTERVAL 4 MONTH )
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) < 27.5
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) >= 19.5";

$average_minus_5_month_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED) 
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now()-INTERVAL 5 MONTH )
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +

(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) < 27.5
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +

(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) >= 19.5";

$low_now_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED) 
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now())
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) < 19.5";

$low_minus_1_month_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED) 
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now()-INTERVAL 1 MONTH)
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) < 19.5";

$low_minus_2_month_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED) 
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now()-INTERVAL 2 MONTH)
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) < 19.5";

$low_minus_3_month_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED) 
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now()-INTERVAL 3 MONTH)
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) < 19.5";

$low_minus_4_month_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED) 
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now()-INTERVAL 4 MONTH)
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) < 19.5";

$low_minus_5_month_sql = "select count(*) from sis_ind_learner_plan tt
JOIN sis_profiles on sis_profiles.rel_id = CAST(tt.rel_id AS SIGNED) 
INNER JOIN
(SELECT id,rel_id, MAX(db61500) AS MaxDateTime
    FROM sis_ind_learner_plan
    GROUP BY rel_id) groupedtt 
ON tt.rel_id = groupedtt.rel_id
AND tt.db61500 = groupedtt.MaxDateTime 
WHERE tt.usergroup = '$_SESSION[usergroup]'
AND sis_profiles.usergroup = $_SESSION[usergroup]
AND (tt.rec_archive IS NULL OR tt.rec_archive = '')
AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')
AND tt.db61500 < last_day(now()-INTERVAL 5 MONTH)
AND (
case WHEN (
    (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) = 7) THEN

((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) ELSE
FORMAT(
    ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
(SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) ) / 
(CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END + 
CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61542 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END) ,0) END
) < 19.5";

$high_now_sth = $dbh->prepare($high_now_sql);
$high_now_sth->execute();
$high_now = $high_now_sth->fetchColumn();

$high_minus_1_month_sth = $dbh->prepare($high_minus_1_month_sql);
$high_minus_1_month_sth->execute();
$high_minus_1_month = $high_minus_1_month_sth->fetchColumn();

$high_minus_2_month_sth = $dbh->prepare($high_minus_2_month_sql);
$high_minus_2_month_sth->execute();
$high_minus_2_month = $high_minus_2_month_sth->fetchColumn();

$high_minus_3_month_sth = $dbh->prepare($high_minus_3_month_sql);
$high_minus_3_month_sth->execute();
$high_minus_3_month = $high_minus_3_month_sth->fetchColumn();

$high_minus_4_month_sth = $dbh->prepare($high_minus_4_month_sql);
$high_minus_4_month_sth->execute();
$high_minus_4_month = $high_minus_4_month_sth->fetchColumn();

$high_minus_5_month_sth = $dbh->prepare($high_minus_5_month_sql);
$high_minus_5_month_sth->execute();
$high_minus_5_month = $high_minus_5_month_sth->fetchColumn();

$average_now_sth = $dbh->prepare($average_now_sql);
$average_now_sth->execute();
$average_now = $average_now_sth->fetchColumn();

$average_minus_1_month_sth = $dbh->prepare($average_minus_1_month_sql);
$average_minus_1_month_sth->execute();
$average_minus_1_month = $average_minus_1_month_sth->fetchColumn();

$average_minus_2_month_sth = $dbh->prepare($average_minus_2_month_sql);
$average_minus_2_month_sth->execute();
$average_minus_2_month = $average_minus_2_month_sth->fetchColumn();

$average_minus_3_month_sth = $dbh->prepare($average_minus_3_month_sql);
$average_minus_3_month_sth->execute();
$average_minus_3_month = $average_minus_3_month_sth->fetchColumn();

$average_minus_4_month_sth = $dbh->prepare($average_minus_4_month_sql);
$average_minus_4_month_sth->execute();
$average_minus_4_month = $average_minus_4_month_sth->fetchColumn();

$average_minus_5_month_sth = $dbh->prepare($average_minus_5_month_sql);
$average_minus_5_month_sth->execute();
$average_minus_5_month = $average_minus_5_month_sth->fetchColumn();

$low_now_sth = $dbh->prepare($low_now_sql);
$low_now_sth->execute();
$low_now = $low_now_sth->fetchColumn();

$low_minus_1_month_sth = $dbh->prepare($low_minus_1_month_sql);
$low_minus_1_month_sth->execute();
$low_minus_1_month = $low_minus_1_month_sth->fetchColumn();

$low_minus_2_month_sth = $dbh->prepare($low_minus_2_month_sql);
$low_minus_2_month_sth->execute();
$low_minus_2_month = $low_minus_2_month_sth->fetchColumn();

$low_minus_3_month_sth = $dbh->prepare($low_minus_3_month_sql);
$low_minus_3_month_sth->execute();
$low_minus_3_month = $low_minus_3_month_sth->fetchColumn();

$low_minus_4_month_sth = $dbh->prepare($low_minus_4_month_sql);
$low_minus_4_month_sth->execute();
$low_minus_4_month = $low_minus_4_month_sth->fetchColumn();

$low_minus_5_month_sth = $dbh->prepare($low_minus_5_month_sql);
$low_minus_5_month_sth->execute();
$low_minus_5_month = $low_minus_5_month_sth->fetchColumn();

$total_now = $low_now + $average_now + $high_now;
$total_minus_1_month = $low_minus_1_month + $average_minus_1_month + $high_minus_1_month;
$total_minus_2_month = $low_minus_2_month + $average_minus_2_month + $high_minus_2_month;
$total_minus_3_month = $low_minus_3_month + $average_minus_3_month + $high_minus_3_month;
$total_minus_4_month = $low_minus_4_month + $average_minus_4_month + $high_minus_4_month;
$total_minus_5_month = $low_minus_5_month + $average_minus_5_month + $high_minus_5_month;



echo"<div class=\"table-responsive\">";

    echo '<table id="data_table" class="sortable table table-striped" cellspacing="0">
		<thead>
		<tr>
            
            <th></th>
            <th>up to</th>
            <th>up to last day of</th> 
            <th>up to last day of</th> 
            <th>up to last day of</th> 
            <th>up to last day of</th> 
            <th>up to last day of</th> 
		</tr>
		<tr>
            
            <th>WB Category</th>
            <th>Today</th>
            <th>'. prev_date(1)->format('F t, Y').'</th> 
            <th>'. prev_date(2)->format('F t, Y').'</th> 
            <th>'. prev_date(3)->format('F t, Y').'</th> 
            <th>'. prev_date(4)->format('F t, Y').'</th> 
            <th>'. prev_date(5)->format('F t, Y').'</th> 
		</tr>
		</thead>
		<tbody>';

echo "<tr>
                        
            <td>High</td>
            <td>$high_now (".number_format(($high_now/$total_now) *100)."%)</td>
            <td>$high_minus_1_month (".number_format(($high_minus_1_month/$total_minus_1_month) *100)."%)</td>
            <td>$high_minus_2_month (".number_format(($high_minus_2_month/$total_minus_2_month) *100)."%)</td>
            <td>$high_minus_3_month (".number_format(($high_minus_3_month/$total_minus_3_month) *100)."%)</td>
            <td>$high_minus_4_month (".number_format(($high_minus_4_month/$total_minus_4_month) *100)."%)</td>
            <td>$high_minus_5_month (".number_format(($high_minus_5_month/$total_minus_5_month) *100)."%)</td>
          </tr>
          <tr>
            
            <td>Average</td>
            <td>$average_now (".number_format(($average_now/$total_now) *100)."%)</td>
            <td>$average_minus_1_month (".number_format(($average_minus_1_month/$total_minus_1_month) *100)."%)</td>
            <td>$average_minus_2_month (".number_format(($average_minus_2_month/$total_minus_2_month) *100)."%)</td>
            <td>$average_minus_3_month (".number_format(($average_minus_3_month/$total_minus_3_month) *100)."%)</td>
            <td>$average_minus_4_month (".number_format(($average_minus_4_month/$total_minus_4_month) *100)."%)</td>
            <td>$average_minus_5_month (".number_format(($average_minus_5_month/$total_minus_5_month) *100)."%)</td>
          </tr>
        <tr>
            <td>Low</td>
            <td>$low_now (".number_format(($low_now/$total_now) *100)."%)</td>
            <td>$low_minus_1_month (".number_format(($low_minus_1_month/$total_minus_1_month) *100)."%)</td>
            <td>$low_minus_2_month (".number_format(($low_minus_2_month/$total_minus_2_month) *100)."%)</td>
            <td>$low_minus_3_month (".number_format(($low_minus_3_month/$total_minus_3_month) *100)."%)</td>
            <td>$low_minus_4_month (".number_format(($low_minus_4_month/$total_minus_4_month) *100)."%)</td>
            <td>$low_minus_5_month (".number_format(($low_minus_5_month/$total_minus_5_month) *100)."%)</td>
          </tr>";



    echo'</tbody>
         <tfoot>
             <tr>
                
		        <th>WB Category</th>
		        <th>Current month</th>
		        <th>'. date("F 1, Y", strtotime("-1 months")).'</th>
		        <th>'. date("F 1, Y", strtotime("-2 months")).'</th>
		        <th>'. date("F 1, Y", strtotime("-3 months")).'</th>
		        <th>'. date("F 1, Y", strtotime("-4 months")).'</th>
		        <th>'. date("F 1, Y", strtotime("-5 months")).'</th>
             </tr>
         </tfoot>
         </table>';

echo"</div>";
?>
        </div>
    </div>
    <!-- Export Message -->
    <div class="modal" id="export_message" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">Export data</h4>
                </div>
                <div class="modal-body text-left">
                    <p style="font-size: 14px;">HEIApply contains your data, in your environment, in our system. We can protect access to your data to only those authorised users that Super Admins have given access credentials to. You are about to export your data out of HEIApply, and therefore outside of our control. If you proceed with exporting this data, you are creating a "copy" of this data and you agree that you are relinquishing our responsibility to maintain security to the "copied" data you have exported, and you continue to be responsible for keeping the "copied" data secure, along with other responsibilities as outlines in the "Terms of Use". This does not affect our continued responsibility to maintain security of your data that remains in our system.<br><br>

                        As it is your data, we advise that you process it in line with the consent your users have given you, but cannot enforce this. If you cannot keep the data secure AND have intention to process it outside of any consent you have received then please "go back" now".
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default leave_page" data-dismiss="modal">Go Back</button>
                    <a class="btn btn-danger" ng-click="ajax_get(0,'',1)" id="export_btn">I Agree</a>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $('body').on('click', function (e) {
        //did not click a popover toggle, or icon in popover toggle, or popover
        if ($(e.target).data('toggle') !== 'popover'
            && $(e.target).parents('[data-toggle="popover"]').length === 0
            && $(e.target).parents('.popover.in').length === 0) {
            $('[data-toggle="popover"]').popover('hide');
        }
    });
    $('.input-daterange input').each(function() {
        $(this).datepicker({
            format:"yyyy-mm-dd"
        });
    });

</script>
