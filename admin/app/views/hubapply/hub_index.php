<?php

echo "<h3>Hubapply API Results</h3>";
echo "<p></p>";

$curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => "https://wcbs-demo.heiapply.com/admin/hubapply.json",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 30,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "GET",
  CURLOPT_HTTPHEADER => array(
    "Authorization: Bearer B9Sz00VESs2853zY0d18sGm2RDIt",
	"Content-Type: application/json"    
  ),
));

$response = curl_exec($curl);
$err = curl_error($curl);

curl_close($curl);

if ($err) {
  echo "cURL Error #:" . $err;
} else {
  echo $response;
}