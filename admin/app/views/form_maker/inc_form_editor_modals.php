<?php 

	$users = new Users;
	$templates = new EmailTemplates;
	$letter_templates=new LetterTemplates();
	//Get the Admins
	$users_args = array("limit"=>100,'type_id_in'=>array(3,2,8),'school_id'=>$_SESSION['usergroup']);
	$admins = $users->get($users_args);

	//Get the Reviewers
	// $users_args = array("limit"=>100,'type_id'=>8,'school_id'=>$_SESSION['usergroup']);
	// $reviewers = $users->get($users_args);

	//Get email templates
	$templates_args = array("limit"=>150, "school_id"=>$_SESSION['usergroup'],'language_id'=>1);
	$email_templates = $templates->get($templates_args);
	$letter_templates=$letter_templates->get_templates(['language_id'=>1]);

?>

<div class="modal fade" id="editEventModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" style="z-index: 12052;">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close"  ng-click="close_events_modal()" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="myModalLabel">Event condition</h4>
      </div>
      <div class="modal-body">
        <div class="hide">
					<label>Events name</label>
					<input type="text" autofocus="" class="form-control ssb" required="" ng-model="hook.title">
				</div>

				<div class="hide">
					<label>Events categories</label>
					<select class="form-control ssb"  ng-model="hook.category">
						<option value="New">New</option>
						<option value="">---</option>
						<option value="Category 1">Category 1</option>
						<option value="Category 2">Category 2</option>
						<option value="Category 3">Category 3</option>
					</select>
					<input type="text" ng-show="hook.category=='New'" class="form-control ssb" ng-model="hook.new_category" placeholder="New category name">
				</div>

				<div class="hide">
					<label>Events Type</label>
					<select class="form-control ssb" required="" ng-model="hook.event_type">
						<option value="data_change">Data Change</option>
						<option value="time_related">Time Related</option>
					</select>
				</div>

				<div ng-show="hook.event_type=='time_related'">
					<label>Time type</label>
					<select class="form-control ssb" ng-model="hook.time_type">
						<option value="date">Date</option>
						<option value="time_period">Time Period</option>
					</select>
				</div>

				<div ng-show="hook.time_type=='date' && hook.event_type=='time_related'">
					<label>Enter the date</label>
					<input type="text" class="form-control ssb"  ng-model="hook.date">
				</div>

				<div ng-show="hook.time_type=='time_period' && hook.event_type=='time_related'">
					<label>Choose period</label>
					<select class="form-control ssb" ng-model="hook.time_period">
						<option value="daily">Daily</option>
						<option value="weekly">Weekly</option>
						<option value="every_month">Every Month</option>
					</select>
				</div>

				<div ng-show="hook.event_type=='location'">
					<label>File Name</label>
					<select class="form-control ssb" ng-model="hook.location">
						<?php foreach ($files as $file) { ?>
						<option value="<?php echo $file['id'];?>"><?php echo $file['file_name'];?></option>
						<?php } ?>
					</select>
				</div>

				<div class="data_change_conditions" ng-show="hook.event_type=='data_change'">

					<div ng-repeat="condition in hook.conditions">
						<label class="hide">Condition</label>
						<div class="row" style="position: relative;">

							<div class="col-sm-5">
								<div style="padding-top: 10px;">{{active_field_info.title}}</div>
							</div>
							<div class="col-sm-3">
								<select class="form-control ssb" ng-model="condition.con" ng-change="empty_event_condition(condition)">
									<option value="not_null">Not Empty</option>
									<option value="is_null">If Empty</option>
									<option value="equal_to">Equal to</option>
									<option value="greater_than">Greater</option>
									<option value="contains">Contains</option>
									<option value="less_than">Less than</option>
								</select>
							</div>
							<div class="col-sm-4" ng-show="condition.con=='equal_to' || condition.con=='greater_than' ||  condition.con=='contains' || condition.con=='less_than'">
								<input type="text" id="condition_value" ng-model="condition.value" class="form-control ssb" autofocus>

							</div>
						</div>
					</div>
					<div class="text-center hide">
						<br>
						<button type="button" class="btn btn-default" href="#" ng-click="add_condition()"><span class="glyphicon glyphicon-plus-sign" aria-hidden="true"></span> Add a condition</button>
					</div>
				</div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" ng-click="close_events_modal()">Close</button>
        <button type="button" class="btn btn-primary" ng-click="save_automated_event()">Save changes</button>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="deleteAutomatedEventModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" style="z-index: 12052;">>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close"  ng-click="close_delete_event_modal()" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="myModalLabel">Delete event</h4>
      </div>
      <div class="modal-body text-center" style="font-size: 16px;">
        Are you sure you want to delete this event?
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" ng-click="close_delete_event_modal()">Close</button>
        <button type="button" class="btn btn-danger" ng-click="delete_automated_event()">Delete event</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal -->
<div class="modal fade" id="deleteActionModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" style="z-index: 12052;">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" ng-click="close_delete_actions_modal()" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="myModalLabel">Delete action</h4>
      </div>
      <div class="modal-body text-center" style="font-size: 16px;">
        Are you sure you want to delete this action?
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" ng-click="close_delete_actions_modal()">Close</button>
        <button type="button" class="btn btn-danger" ng-click="delete_action()">Delete action</button>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="actionModal" role="dialog" aria-labelledby="myModalLabel" style="z-index: 12052;">
    <script src='https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.12.1/js/bootstrap-select.min.js'></script>
    <link href='/admin/assets/css/bootstrap-select.min.css' rel='stylesheet'/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/js/select2.min.js"></script>
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" ng-click="close_actions_modal()" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">Action details</h4>
            </div>
            <div class="modal-body">
                <ul class="list-unstyled">

                    <div class="action_fields">
                        <div>
                            <li>
                                <label>Action Title</label>
                                <input type="text" class="form-control ssb" name="" ng-model="action.title">
                            </li>

                            <div class="row hide">
                                <div class="col-sm-6 ">
                                    <label>Action Type</label>
                                    <select class="form-control ssb" name="" ng-model="action.type">
                                        <option value="communication">Communication</option>
                                        <option value="modify_data">Modify Data</option>
                                    </select>
                                </div>
                            </div>

                            <div class="communication_hook_wrap" ng-show="action.type=='modify_data'">
                                <li ng-repeat="modification in action.fields_list">
                                    <div class="row">
                                        <div class="col-sm-4">
                                            <label>Table Name:</label>
                                            <select class="form-control ssb" ng-model="modification.table"
                                                    ng-change="get_table_fields($index,modification.table,'modification',$parent.$index)">
                                                <?php foreach ($tables as $table) { ?>
                                                    <option value="<?php echo $table['id']; ?>"><?php echo $table['slug']; ?></option>
                                                <?php } ?>
                                            </select>
                                        </div>
                                        <div class="col-sm-4">
                                            <label>Field Name:</label>
                                            <select class="form-control ssb" name="" ng-model="modification.field_id"
                                                    ng-options="i.value as (i.label) for i in modification.fields">
                                            </select>
                                        </div>
                                        <div class="col-sm-4">
                                            <label>New Value:</label>
                                            <input type="text" autofocus="" class="form-control ssb"
                                                   ng-model="modification.name" style="width: 190px;">
                                            <span class="glyphicon glyphicon-trash"
                                                  ng-click="remove_action_field($parent.$index,$index)"
                                                  aria-hidden="true"
                                                  style="position: absolute; top: 35px; right: -10px; cursor: pointer;"></span>
                                        </div>
                                    </div>
                                </li>


                                <div class="text-center">
                                    <button type="button" class="btn btn-default" ng-click="add_table_fields($index)">
                                        <span class="glyphicon glyphicon-plus-sign" aria-hidden="true"></span> Add a
                                        field
                                    </button>
                                </div>

                            </div>

                            <div class="communication_hook_wrap"
                                 ng-show="action.type=='communication' || action.type=='resources'">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <label>Type</label>
                                        <select class="form-control ssb" name="" ng-model="action.message_type">
                                            <option value="email">Email</option>
                                            <option value="email_from_template">Email From Template</option>
                                            <option value="sms">SMS</option>
                                            <option value="letter">Letter</option>
                                            <option value="letter_from_template">Letter From Template</option>
                                            <option value="notification">Notification</option>
                                            <option value="tagged_internal_note">Tagged Internal Note (Internal Comment)</option>
                                            <option value="task">Task</option>
                                            <option value="resources">Shared Resources</option>
                                            <option value="execute_a_url">Call a URL</option>
                                        </select>
                                    </div>
                                     <div class="col-sm-6" ng-show="action.message_type=='execute_a_url'">
                                        <label>URL Link (to be supplied by Heipply)</label>
                                        <input type="text" class="form-control ssb" name=""
                                               ng-model="action.url">
                                    </div>
                                    <div class="col-sm-12" ng-if="action.message_type == 'resources'">
                                        <div class="alert alert-info fade in alert-dismissible">
                                            <i class="fa fa-bell"></i>&nbsp;&nbsp;Please Create a seperate email template
                                            to notify applicants (via email) of New Resource allocation! Set the template's category to "Shared Resources - Template used for shared resources"
                                        </div>
                                    </div>
                                    <div class="col-sm-6" ng-show="action.message_type=='task'">
                                        <label>Due By Days</label>
                                        <input type="text" class="form-control ssb" name=""
                                               ng-model="action.task_due_date">
                                    </div>

                                    <div class="col-sm-6" ng-show="action.message_type=='task'">
                                        <label>Publish in how many days</label>
                                        <input type="text" class="form-control ssb" name=""
                                               ng-model="action.publish_days">
                                    </div>
                                    <div class="col-sm-6" ng-repeat="(key,item) in action.task_options" ng-show="action.message_type=='task'">
                                        <label>{{item.title}}</label>
                                        <select class="form-control ssb" name="" ng-model="action.task_options[key].value">
                                            <option value="yes">yes</option>
                                            <option value="no">no</option>
                                        </select>
                                    </div>

                                    <style type="text/css">
                                        .ui-tooltip {
                                            background: #000;
                                            color: #fff;
                                        }
                                    </style>

                                    <div class="col-sm-6" ng-show="action.message_type=='task'">
                                        <label>Task Status <span class="glyphicon glyphicon-info-sign tip"
                                                                 aria-hidden="true" data-toggle="tooltip"
                                                                 data-placement="top" title="Guidance notes:
Set to “Draft” > if you want to create the task automatically, but then make some edits to it before setting.
Set to “Active” > choose this if all elements of the automated action are set, and it is the same for everyone on this route.
Set “On hold” > choose this if you want to create the task, and know that it needs to be created with a status of “on hold” as a way of warning the"></span></label>
                                        <select class="form-control ssb" name="" ng-model="action.task_status">
                                            <option value="draft">draft</option>
                                            <option value="active">active</option>
                                            <option value="on_hold">on hold</option>
                                        </select>
                                    </div>
                                    <div class="col-sm-6" ng-show="action.message_type=='task'">
                                        <label>Notify Applicant</label>
                                        <select select class="form-control ssb" name="" ng-model="action.task_notify_applicant">
                                            <option value="yes">yes</option>
                                            <option value="no">no</option>
                                        </select>
                                    </div>
                                    <div class="col-sm-12 checkbox" ng-show="action.message_type=='resources'">
                                        <label>
                                            <input type="checkbox" value="false" ng-model="action.task_notify_applicant" ng-true-value="'yes'" ng-false-value="'no'" />
                                            DO NOT Notify Applicant
                                        </label>
                                    </div>
                                    <script type="text/javascript">
                                        $(document).ready(function () {
                                            $('.tip').tooltip({
                                                html: true,
                                                placement: 'top',
                                                container: 'body',
                                                delay: {"show": 10500, "hide": 10500}
                                            });
                                        });
                                    </script>


                                    <div class="hide">
                                        <div class="col-sm-6" ng-show="action.message_type=='email'">
                                            <label>Email From:</label>
                                            <select class="form-control ssb" name="" ng-model="action.email_from">
                                                <option value="<?php echo $form_schools_main_general_email_address; ?>"><?php echo $form_schools_main_general_email_address; ?></option>
                                                <option value="">-----</option>
                                                <?php foreach ($admins as $admin) { ?>
                                                    <option value="<?php echo $admin['email']; ?>"><?php echo $admin['email']; ?></option>
                                                <?php } ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <li ng-show="action.message_type=='email' || action.message_type=='email_from_template' || action.message_type=='task'">
                                    <label ng-hide="action.message_type=='task'">Email To:</label>
                                    <label ng-show="action.message_type=='task'">Assigned to</label>
                                    <select class="form-control ssb" name="" ng-model="action.email_to_type">
                                        <option value="applicant">Applicant</option>
                                        <!-- <option value="reviewer">Reviewer</option> -->
                                        <option value="admin">Admin</option>
                                        <option ng-show="action.message_type=='task'" value="patner">Patner</option>
                                        <option ng-show="action.message_type=='task'" value="other">Other</option>
                                    </select>
                                </li>


                               <!--  <li ng-show="action.email_to_type=='reviewer'">
                                    <label ng-hide="action.message_type=='task'">Reviewer Email Address</label>
                                    <label ng-show="action.message_type=='task'">Assigned to</label>
                                    <select class="form-control ssb" name="" ng-model="action.email_to">
                                        <?php foreach ($reviewers as $reviewer) { ?>
                                            <option value="<?php echo $reviewer['id']; ?>"><?php echo $reviewer['first_name'] . " " . $reviewer['last_name']; ?></option>
                                        <?php } ?>
                                    </select>
                                </li> -->

                                <li ng-show="action.email_to_type=='admin'||action.email_to_type=='reviewer'">
                                    <label ng-hide="action.message_type=='task'">Admin/Reviewer Email Address</label>
                                    <label ng-show="action.message_type=='task'">Assigned to</label>

                                    <select class="form-control ssb" name="" ng-model="action.email_to">
                                        <?php foreach ($admins as $admin) { ?>
                                            <option value="<?php echo $admin['id']; ?>"><?php echo $admin['first_name'] . " " . $admin['last_name']; ?></option>
                                        <?php } ?>
                                    </select>
                                </li>
                                <li ng-show="action.message_type=='email_from_template'">
                                    <label>Choose a template</label>
                                    <select class="form-control ssb" name="" ng-model="action.template">
                                        <?php foreach ($email_templates as $template) { ?>
                                            <option
                                                value="<?php echo $template['id']; ?>"><?php echo $template['title']; ?></option>
                                        <?php } ?>
                                    </select>
                                </li>
                                <li ng-show="action.message_type=='letter_from_template'">
                                    <label>Choose a template</label>
                                    <select class="form-control ssb" name="" ng-model="action.template">
                                        <?php foreach ($letter_templates as $template) { ?>
                                            <option
                                                value="<?php echo $template['id']; ?>"><?php echo $template['title']; ?></option>
                                        <?php } ?>
                                    </select>
                                </li>

                                <li ng-show="action.message_type=='tagged_internal_note'" >
                                            <label>Tagged Users</label>
                                            <select name="email_to" ng-model="action.email_to"
                                                    class="form-control ssb" >
                                                <option value="assigned_users"
                                                        ng-selected="action.email_to== 'assigned_users'">
                                                    Assigned Users
                                                </option>
                                            </select>
                              </li>
                                <ul class="nav nav-tabs" id="myTab" role="tablist" ng-show="action.message_type=='email' ||action.message_type=='sms'  ||action.message_type=='notification' ||action.message_type=='tagged_internal_note' || action.message_type=='letter'  || action.message_type=='task' || action.message_type=='resources'">
                                    <li class="nav-item" ng-class="{active:key==0}"
                                        ng-repeat="(key,value) in languages">
                                        <a class="nav-link" ng-class="{active:key==0}" id="lang_tab_{{value.id}}"
                                           data-toggle="tab" href="#lang_tabcontent_{{value.id}}" role="tab"
                                           aria-controls="home" aria-selected="true">{{value.db21280}}</a>
                                    </li>
                                </ul>
                                <div class="tab-content" id="myTabContent" ng-show="action.message_type=='email' ||action.message_type=='sms'  ||action.message_type=='notification' ||action.message_type=='tagged_internal_note' || action.message_type=='letter'  || action.message_type=='task' || action.message_type=='resources'">
                                    <div class="tab-pane fade" ng-class="{active:key==0,in:key==0}"
                                         id="lang_tabcontent_{{value.id}}" ng-repeat="(key,value) in languages"
                                         role="tabpanel" aria-labelledby="home-tab">
                                        <li ng-show="action.message_type=='email' || action.message_type=='letter' || action.message_type=='task' || action.message_type=='resources'">
                                            <label ng-hide="action.message_type=='resources' || action.message_type=='task'">Subject</label>
                                            <label ng-show="action.message_type=='resources' || action.message_type=='task'">Title</label>
                                            <input type="text" class="form-control ssb" name=""
                                                   ng-model="action.translations[value.id].db51136">
                                        </li>
                                        <li ng-show="action.message_type=='resources'" >
                                            <style>
                                                .ui-select-multiple .ui-select-match-item {
                                                    padding-right: 25px;
                                                    position: relative;
                                                }

                                                .ui-select-multiple .ui-select-match-close {
                                                    position: absolute;
                                                    top: 10px;
                                                    right: 5px;
                                                    font-size: 16px;
                                                    color: #888;
                                                    cursor: pointer;
                                                    line-height: 1;
                                                }

                                                .ui-select-multiple .ui-select-match-close:hover {
                                                    color: #d9534f;
                                                }
                                            </style>
                                            <label>Resources</label>
                                            we are here {{value.id}}
                                            {{action.resources}}
                                            {{action.resources[value.id]}}

                                            <ui-select multiple ng-model="action.resources[value.id]" theme="bootstrap" title="Select resources" tagging="false">
                                                <ui-select-match placeholder="Select...">
                                                    {{$item.title}}
                                                </ui-select-match>

                                                <ui-select-choices repeat="file.id as file in form_files track by $index | filter:$select.search">
                                                    {{file.title}}
                                                </ui-select-choices>
                                            </ui-select>
<!--                                            <select name="resources[{{value.id}}]" ng-model="action.resources[value.id]"-->
<!--                                                    class="selectpicker form-control ssb" data-live-search="true"-->
<!--                                                    data-size="8" multiple data-actions-box="true">-->
<!--                                                <option value="{{file.id}}" ng-repeat="file in form_files track by $index">-->
<!--                                                    {{file.title}}-->
<!--                                                </option>-->
<!--                                            </select>-->

                                        </li>
                                         
                                        <li ng-show="action.message_type">
                                            <label ng-hide="action.message_type=='resources'">Message</label>
                                            <label ng-show="action.message_type=='resources'">Description</label>
                                            <div ng-show="action.message_type=='email' || action.message_type=='letter'"
                                                 style="background-color: #fff; border: solid 1px #e1e1e1; border-bottom: none; padding: 10px; text-align: right;">
                                                <button type="button" class="btn btn-default btn-xs" data-toggle="modal"
                                                        data-target="#email_variable_modal">View email variables
                                                </button>
                                            </div>
                                            <textarea  ng-if="action.message_type=='email' || action.message_type=='letter'" editor-options="{ versionCheck: false, height: 300 }" ckeditor="" id="editor{{$instance}}"
                                                      class="form-control ssb" style="height: 300px"
                                                      ng-model="action.translations[value.id].db51138"
                                                      placeholder="Write your {{ action.message_type }} here"></textarea>
                                            <textarea ng-if="action.message_type=='sms' || action.message_type=='notification' || action.message_type=='tagged_internal_note' || action.message_type=='resources'"
                                                      class="form-control ssb" style="height: 300px"
                                                      ng-model="action.translations[value.id].db51138"
                                                      placeholder="Write your {{ action.message_type }} here"></textarea>

                                            <textarea  ng-if="action.message_type=='task'" ckeditor="" editor-options="{ versionCheck: false, height: 300 }" ng-model="$parent.action.translations[value.id].db51138" ></textarea>

                                        </li>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" ng-click="close_actions_modal()">Close</button>
                <button type="button" class="btn btn-primary" ng-click="save_automated_action()">Save action</button>
            </div>
        </div>
    </div>
</div>


</div></div>


<div class="modal fade" id="email_variable_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" style="z-index: 12053;">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close close_email_variables_modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="myModalLabel">Email Variables</h4>
      </div>
      <div class="modal-body text-left">
      	<h4>Applicant Information</h4>
      	<table class="table table-striped">
      		<tr>
      			<td>id</td>
      			<td>{{ applicant.id }}</td>
      		</tr>
      		<tr>
      			<td>Cohort</td>
      			<td>{{ applicant.cohort }}</td>
      		</tr>
      		<tr>
      			<td>Internal Ref Number</td>
      			<td>{{ applicant.internal_reference }}</td>
      		</tr>
      		<tr>
      			<td>Username_id</td>
      			<td>{{ applicant.username_id }}</td>
      		</tr>
      		<tr>
      			<td>First name</td>
      			<td>{{ applicant.first_name }}</td>
      		</tr>
      		<tr>
      			<td>Middle name</td>
      			<td>{{ applicant.middle_name }}</td>
      		</tr>
      		<tr>
      			<td>Last name</td>
      			<td>{{ applicant.last_name }}</td>
      		</tr>
      		<tr>
      			<td>Country</td>
      			<td>{{ applicant.country }}</td>
      		</tr>
      		<tr>
      			<td>Telephone</td>
      			<td>{{ applicant.telephone }}</td>
      		</tr>
      		<tr>
      			<td>Email Address</td>
      			<td>{{ applicant.email_address }}</td>
      		</tr>
      		<tr>
      			<td>Intake</td>
      			<td>{{ applicant.intake }}</td>
      		</tr>
            <tr>
                <td>Intake Start Date</td>
                <td>{{applicant.intake.start_date}}</td>
            </tr>
            <tr>
                <td>Intake End Date</td>
                <td>{{ applicant.intake.end_date }}</td>
            </tr>
      		<tr>
      			<td>Picture</td>
      			<td>{{ applicant.pic }}</td>
      		</tr>
      		<tr>
      			<td>School name</td>
      			<td>{{ applicant.school }}</td>
      		</tr>
      		<tr>
      			<td>Files Count</td>
      			<td>{{ applicant.files_count }}</td>
      		</tr>
      		<tr>
      			<td>References count</td>
      			<td>{{ applicant.references_count }}</td>
      		</tr>
      		<tr>
      			<td>Date Submitted</td>
      			<td>{{ applicant.date_submitted }}</td>
      		</tr>
      		<tr>
      			<td>Has Submitted</td>
      			<td>{{ applicant.has_submitted }}</td>
      		</tr>
      		<tr>
      			<td>Has made a payment</td>
      			<td>{{ applicant.has_made_a_payment }}</td>
      		</tr>
      		<tr>
      			<td>Paid amount currency</td>
      			<td>{{ applicant.paid_amount_currency }}</td>
      		</tr>

      		<tr>
      			<td>Widthrawn</td>
      			<td>{{ applicant.widthrawn }}</td>
      		</tr>
      		<tr>
      			<td>Rejected?</td>
      			<td>{{ applicant.rejected }}</td>
      		</tr>
      		<tr>
      			<td>On short course</td>
      			<td>{{ applicant.on_short_course }}</td>
      		</tr>
      		<tr>
      			<td><?=terminology("Gender", current_url())?></td>
      			<td>{{ applicant.gender }}</td>
      		</tr>
      		<tr>
      			<td>Level of entry</td>
      			<td>{{ applicant.level_of_entry }}</td>
      		</tr>
      		<tr>
      			<td>Source</td>
      			<td>{{ applicant.source }}</td>
      		</tr>
      		<tr>
      			<td>Date of birth</td>
      			<td>{{ applicant.date_of_birth }}</td>
      		</tr>
      		<tr>
      			<td>Age</td>
      			<td>{{ applicant.age }}</td>
      		</tr>
      	</table>

      	<h4>Course Info</h4>
      	<table class="table table-striped">
      		<tr>
      			<td>ID</td>
      			<td>{{ applicant.course.id }}</td>
      		</tr>
      		<tr>
      			<td>Title</td>
      			<td>{{ applicant.course.title }}</td>
      		</tr>
      		<tr>
      			<td>Tutors</td>
      			<td>{{ applicant.course.tutors }}</td>
      		</tr>
      		<tr>
      			<td>Category</td>
      			<td>{{ applicant.course.category }}</td>
      		</tr>
      		<tr>
      			<td>Abbreviation</td>
      			<td>{{ applicant.course.abbreviation }}</td>
      		</tr>
      		<tr>
      			<td>Is short course?</td>
      			<td>{{ applicant.course.short_course }}</td>
      		</tr>
      		<tr>
      			<td>Deadline Time</td>
      			<td>{{ applicant.course.deadline_time }}</td>
      		</tr>
      		<tr>
      			<td>Fee</td>
      			<td>{{ applicant.course.fee }}</td>
      		</tr>
      		<tr>
      			<td>Course level id</td>
      			<td>{{ applicant.course.course_level_id }}</td>
      		</tr>
      		<tr>
      			<td>Course level title</td>
      			<td>{{ applicant.course.course_level }}</td>
      		</tr>
      		<tr>
      			<td>Department ID</td>
      			<td>{{ applicant.course.department.id }}</td>
      		</tr>
      		<tr>
      			<td>Department Title</td>
      			<td>{{ applicant.course.department.title }}</td>
      		</tr>
      	</table>

      	<h4>Assigned personnel</h4>
      	<p>This is an array of all the assigned personnel. <strong>{{ applicant.assigned_users }}</strong>.<br>Here is an example of looping throught the array</p>
      	<pre>
{% for user in applicant.assigned_users %}
  {{ user.first_name }} {{ user.last_name }}
{% endfor %}</pre>
      	<table class="table table-striped">
      		<tr>
      			<td>First name</td>
      			<td>{{ user.first_name }}</td>
      		</tr>
      		<tr>
      			<td>Last name</td>
      			<td>{{ user.last_name }}</td>
      		</tr>
      		<tr>
      			<td>Email</td>
      			<td>{{ user.email }}</td>
      		</tr>
      		<tr>
      			<td>User level id</td>
      			<td>{{ user.user_level.id }}</td>
      		</tr>
      		<tr>
      			<td>User level title</td>
      			<td>{{ user.user_level.id }}</td>
      		</tr>
      	</table>
        <pre class="hide">{
  

  "application_stage_id": "9",
  "application_stage": "Stage 10 - Enrolled",

  "course_id": 330,
  "": 18,
  "": "21/07/1999",
  "archieve_record": "Direct",
  "assigned_to": null,
  "source": "Direct",
  "department": {
    "title": null
  },
  "course": null,
  "course_code": "",
  "second_course": null,
  "country": "United Kingdom",
  "email_address": "<EMAIL>",
  "telephone": "7957654892",
  "internal_reference": "CNC0801",
  "cohort": "2016",
  "ucas": null,
  "send_ucas_letter": null,
  "period": "34",
  "application_route": "244",
  "cohort_intake": null,
  "route": {
    "id": null,
    "course": null,
    "usergroup": null,
    "course_level": "27",
    "order": 2,
    "applicant_country": "not specified",
    "applicant_source": "not specified",
    "route_id": 244,
    "rule_name": "Short Course",
    "school": "Condé Nast College",
    "age_group": null,
    "checklist_to_create": null,
    "checklist_to_copy": null
  },
  "offers": [
    {
      "id": 890,
      "status": "Rejected",
      "course_completion": null,
      "response": "Accepted",
      "offer_title": "Unconditional Offer",
      "template": null,
      "fee": null,
      "academic_session": null,
      "enrolment_and_inductions_date": null,
      "duration_of_course": null,
      "course_commencement_date": null,
      "ucas_code": null,
      "level_of_entry": null,
      "course_level": "27",
      "course_code": "",
      "course_offered": null,
      "terms_of_offer": null,
      "offer_type": "Unconditional",
      "select_payment_options_available": null,
      "invoice_reference": null,
      "selected_payment_method": null,
      "send_applicant_notification": null,
      "intake": "34",
      "course_fee_display_only": null
    }
  ],
  "has_offer": 1,
  "user_info": {
    "id": 18058,
    "school": "Condé Nast College",
    "student_id": "18472",
    "school_id": "21",
    "type": "Applicant",
    "type_id": "4",
    "first_name": "Imogen",
    "last_name": "johnson",
    "email": "<EMAIL>",
    "institution": "no",
    "suspended": "no",
    "username": "johnsonImogen",
    "manager": "21",
    "organisation": null,
    "agent_company": null,
    "profile_picture_filename": null,
    "profile_picture": "https://condenastcollege.heiapply.com/engine/media/dl.php?fl=cyUzQTMyJTNBJTIyOGUzNTUyYzZlZjZlMmJkZjlmOTIyM2U1YWVlNDlhODIlMjIlM0I=",
    "last_login": null
  },
  "assigned": null,
  "intake": "Miss Vogue Course 16th & 17th July 2016",
  
}</pre>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default close_email_variables_modal">Close</button>
      </div>
    </div>
  </div>
</div>






