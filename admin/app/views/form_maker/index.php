<script src="<?php echo $this->base_url(); ?>assets/js/form_editor.js?v=1" type="text/javascript"></script>
<script>
	app.controller('demo', ['$scope','$rootScope','$filter','$http', 'formEditor','$window', function($scope,$rootScope,$filter,$http, formEditor,$window){
         console.log('debugging1')
		$scope.checklists = <?php echo json_encode($data['results']); ?>;

		$scope.show_form = function show_form(form_id){
			$("#edit_custom_form_modal").modal({backdrop: 'static', keyboard: true,show: true});
		  setTimeout(function(){
			  $rootScope.form1 = new formEditor({
					'wrap': "#pageCustomFrom",
					'id':form_id,
					'show_table_view':true,
					'automated_events':false,
					'db_connection_name': '',
					'custom_form': false,
					'system_abbrv':'',
					'correct_answers': false,
					'correct_answer_reasons': false,
					'incorrect_answer_reasons': false,
					'answer_weighting':false,
				});

				$scope.checklist_form_name = $rootScope.form1.form_name;
			},500);
		};


		 $scope.add_custom_form = function add_custom_form(){
		    
		    $http({
		      url: siteURL+"/admin/custom_forms.json",
		      method: "POST",
		      data: {name:$scope.form_name},
		    }).success(function(data, status, headers, config) {
		    	$("#newFormModal").modal("hide");
		      $scope.show_form(data.id);
		    }).error(function(data, status, headers, config) {
		      
		    });
		  };

	}]);
	
	app.filter('removeCharacter', function () {
	    return function (text, filter) {
	        var str = text.replace('>', '');
	        str = str.replace('<', '');
	        str = str.replace('=', '');
	        filter.value = str;
	        return str;
	    };
	});
</script>


<div ng-controller="demo">

<div class="inner_container">
	<div class="top_section">
		<div class="buttons_wrap">
			<?php if(!$data['hide_add_new_entry_button']){ ?>
			<button type="button" class="btn btn-primary" data-toggle="modal" data-target="#newFormModal" >Add a new <?php if($data['category']['entry_single_title']){ echo $data['category']['entry_single_title']; }else{ echo 'entry'; } ?></button>
			<?php } ?>
		</div>
		<h1>
			<?php
				if($data['category']['entry_plural_title']){
					echo $data['category']['entry_plural_title'];
				}else{
					if($data['category']['title']){
						echo $data['category']['title'];
					}else{
						if($data['main_title']){
							echo $data['main_title'];
						}else{
							echo 'All forms';
						}
					}
				}
			?>
		</h1>
	</div>

	<?php if($_GET['deleted']){ ?>
		<div class="alert alert-success main_alert" role="alert">
			<span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
			The plan was deleted successfully.
		</div>
	<?php }?>

	<ul class="next-tab_list">
		<li>
			<a class="<?php if(!$_GET["type"]){echo "active";}?>" href="#">All <?php echo $data['category']['entry_plural_title']; ?></a>
		</li>		
	</ul>

	<div class="stnrd_box">
		
		<?php if(count($data['results'])){ ?>
    	<div class="top_filter">
	      <form method="post" action="<?php echo $doc_name; ?>">
	      	<input type="text" class="form-control searchtext" placeholder="Start typing to search..." name="search" value="">
	      </form>
	    </div>
			<table class="table">
			<thead>
				<tr>
					<th width="50px;">ID</th>
					<th>Title</th>
					<?php if(!$data['category']){ ?>
					<th>Category</th>
					<?php } ?>
				</tr>
			</thead>
			<tbody class="sortable_objects">
				<?php 
					foreach ($data['results'] as $entry){

						$link = $data['main_link'].'/'.$entry["id"];
				?>
				<tr id="row_<?php echo $entry["id"]; ?>">
					<td><a href="javascript:;" ng-click="show_form(<?php echo $entry["id"]; ?>)">#<?php echo $entry["id"]; ?></a></td>
					<td><a href="javascript:;" ng-click="show_form(<?php echo $entry["id"]; ?>)"><?php if($entry["title"]){ echo $entry["title"]; }else{ echo $entry["table_name"]; } ?></a></td>
					<?php if(!$data['category']){ ?>
					<td><a href="<?php echo $link; ?>"><?php echo $entry["category"]['title']; ?> Fields</a></td>
					<?php } ?>
					<td>
						<a href="#"></a>
					</td>
					
				</tr>
				<?php } ?>
			</tbody>
		</table>
		<?php }else{ ?>

			<div class="no_results">
				<span class="glyphicon glyphicon-time"></span>
				<h2>You haven't added any <?php if($data['category']['entry_plural_title']){ echo $data['category']['entry_plural_title']; }else{ echo "entry"; } ?> yet</h2>
				<button type="button" class="btn btn-primary" data-toggle="modal" data-target="#newFormModal" >Add a new <?php if($data['category']['entry_single_title']){ echo $data['category']['entry_single_title']; }else{ echo "entry"; } ?></button>
			</div>
		<?php } ?>

		
	</div>


	<!-- New From Modal -->
	<div class="modal fade" id="newFormModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
	  <div class="modal-dialog" role="document">
	    <div class="modal-content">
	      <div class="modal-header">
	        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
	        <h4 class="modal-title" id="myModalLabel">Add a new <?php if($data['category']['entry_single_title']){ echo $data['category']['entry_single_title']; }else{ echo "entry"; } ?></h4>
	      </div>
	      <form method="POST" action="<?php echo $this->current_url(); ?>">
		      <div class="modal-body">
		        <label><?php if($data['category']['entry_single_title']){ echo $data['category']['entry_single_title']; }else{ echo "entry"; } ?> title</label>
		        <input class="form-control ssb" type="text" name="table_name" required="required">

		        <?php if($data['category_id']){ ?>
		        <input type="hidden" name="category" value="<?php echo $data['category_id']; ?>">
		        <?php }else{ ?>
		        <label>Table Category</label>
		        <select class="form-control ssb" name="category" required="required">
		        	<option value="">Choose...</option>
		        	<?php foreach ($data['form_categories'] as $category){ ?>
		        		<option value="<?php echo $category['id']; ?>"><?php echo $category['title']; ?></option>
		        	<?php }?>
		        </select>
		        <?php } ?>
		      </div>
		      <div class="modal-footer">
		        <button type="submit" class="btn btn-primary btn-block btn-lg">Add <?php if($data['category']['entry_single_title']){ echo $data['category']['entry_single_title']; }else{ echo "entry"; } ?></button>
		        <input type="hidden" name="action" value="add_new_form">
		      </div>
	      </form>
	    </div>
	  </div>
	</div>
</div>
