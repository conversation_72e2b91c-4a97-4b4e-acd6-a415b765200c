<script src="/admin/assets/js/inc_cont_fields.js"></script>
<?php 
if (!function_exists('table_fields_formless')) {
function table_fields_formless($pg_id,$exclude_fields=[],$args=[]){
   $form_templates= new FormTemplates;
   $form_args=['id'=>$pg_id,'locked_fields_only'=>true];
   if (!empty($exclude_fields)) {
      $form_args['exclude_fields']=$exclude_fields;
   }

   if (!empty($args['usergroup'])) {
     $form_args['school_id']=$args['usergroup'];
   }

   if (!empty($args['record_id'])) {
     $form_args['record_id']=$args['record_id'];
   }

   $template= $form_templates->get($form_args);
   $template['include_js']=true;


   $form_templates->form_html_only($template);


   //$fields= new Fields();
    }
}
 ?>
 <style type="text/css">
     .hidden{
        display: none;
}
 </style>
<div class="modal fade" id="enrollment-modal" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="font-size: 18px;">Bulk Enrol</h4>
                <button type="button" class="close" data-dismiss="modal">×</button>
         </div>
            <form id="bulk_save_table884">
                <div class="modal-body">
                    <div class="alert alert-danger hidden" id="share_resources_error"></div>
                    <input type="hidden" value="<?= $_GET['ref'] ?>" name="record_id">
                    <?php table_fields_formless(884,["db86348","db86351"]) ?>
                    <!-- <div class="form-group">
                        
                        <label>New Term (of Intake)</label>
                       <select  class="form-control" name="new_term" id="new_term" ></select>
                    </div> -->
                   

                </div>
                <style type="text/css">
                /*	
                Commented out by Kuda 25/08/2023 20:59 PM
                Reason : This unsets the top value for datepicker modals
                #old_design .dropdown-menu{
                		top: unset!important;
                	} */

                </style>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary"  ng-click="bulk_save_table(884)">Enroll</button>
                </div>
            </form>

        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->


<div class="modal fade" id="extend_visa_learning-modal" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="font-size: 18px;">Extend Visa/Study Length </h4>
                <button type="button" class="close" data-dismiss="modal">×</button>
         </div>
            <form id="bulk_save_table890">
                <div class="modal-body">
                    <div class="alert alert-danger hidden" id="share_resources_error"></div>
                    <input type="hidden" value="<?= $_GET['ref'] ?>" name="record_id">
                    <?php table_fields_formless(890) ?>


                    <!-- <div class="form-group">
                        
                        <label>New Term (of Intake)</label>
                       <select  class="form-control" name="new_term" id="new_term" ></select>
                    </div> -->
                   

                </div>
                <style type="text/css">
                    /*  
                Commented out by Kuda 25/08/2023 20:59 PM
                Reason : This unsets the top value for datepicker modals
                	#old_design .dropdown-menu{
                		top: unset!important;
                	}
                    */
                </style>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary"  ng-click="bulk_save_table(890)">Extend</button>
                </div>
            </form>

        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div class="modal fade" id="graduate-modal" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="font-size: 18px;">Graduate </h4>
                <button type="button" class="close" data-dismiss="modal">×</button>
         </div>
            <form id="bulk_save_table911">
                <div class="modal-body">
                    <?php table_fields_formless(911) ?>
                   

                </div>
                <style type="text/css">
                    /*  
                Commented out by Kuda 25/08/2023 20:59 PM
                Reason : This unsets the top value for datepicker modals
                	#old_design .dropdown-menu{
                		top: unset!important;
                	}*/
                </style>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary"  ng-click="bulk_save_table(911)">Save</button>
                </div>
            </form>

        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

