<?php
	/************************
	# This page shows the results of a 
	# specific course for the Online Learning System
	*************************/
?>

<div class="inner_container">
	<div class="breadcrumbs">
		<a href="<?php echo $this->base_url('applicants/'.$data['applicant']['id']); ?>"><i class="fa fa-chevron-left" aria-hidden="true"></i> <?php echo $data['applicant']['first_name']." ".$data['applicant']['last_name']; ?> Info</a>
	</div>
	<div class="top_section">
		<h1>
			<?php echo $data['course']['title']; ?>
		</h1>
	</div>

	<link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,600,700" rel="stylesheet">
	<style type="text/css">

	#module_complete{ text-align: center; background: #384047; padding: 100px 30px; padding-bottom: 15px; font-family: 'Open Sans', sans-serif;}
	#module_complete .icon{ font-size: 72px; color: #fff; font-weight: 600;}
	#module_complete hr{ width: 50%; margin: 0px auto; margin-bottom: 20px; margin-top: 20px; border-top: solid 1px #999; }
	#module_complete h2{ color: #fff; font-weight: 400;}
	#module_complete p{ color: #fff; font-size: 18px; width: 60%; margin: 0 auto; margin-bottom: 30px;font-weight: 300; color: #87929d;}
	#module_complete .btn{ border: none; padding: 10px 20px; font-size: 15px; font-weight: 600; margin: 0px 5px; opacity: 0.9;}
	#module_complete .btn.btn-default{ background: #fff; color: #000;}
	#module_complete .btn.btn-success{ }

	#module_complete .unit_quiz_responses{ text-align: left; width: 600px; margin: 0px auto; background-color: transparent; color: #87929d; font-size: 16px; }
	#module_complete .unit_quiz_responses li{ border-bottom: solid 1px #555; padding-bottom: 20px; }
	#module_complete .unit_quiz_responses li .icon{ height: 40px; font-size: 28px; }
</style>


	<div class="row">
			<div class="col-sm-3">

				<div class="panel panel-default no-padding grey profile_wrap">
					<div class="panel-body np">
							<ul class="variant_list">
							<?php foreach ($data['online_courses'] as $course){ ?>
							<li>
								<a href="<?php echo $this->base_url('applicants/'.$data['applicant']['id'].'/course_results/'.$course["id"]); ?>" class="current">
									<div class="row">
										<div class="col-sm-12">
											<span>
										  	<?php echo $course["title"]; ?>
										  </span>
										</div>
									</div>
								</a>
							</li>
							<?php }?>
						</ul>
					</div>

				</div>
			</div>


			<div class="col-sm-9">

				<div class="panel panel-default no-padding">
					<div class="panel-body">
						
						<div id="module_complete">
							<?php if($current_module_count==$all_modules_count){ ?>
								<i class="fa fa-graduation-cap icon" aria-hidden="true"></i>
								<h2>End of course!</h2>

								<div style="color: #fff; font-size: 18px; font-weight: 400; margin-bottom: 30px;"><?php echo $course['title'];?></div>

								<p class="hide">Congratulations, you have successfully completed all the modules in this course.</p>
								

								<p style="margin-bottom: 50px;">You completed <?php echo $course['completed_units_count']; ?> out of <?php echo $course['units_count']; ?> units (<?php echo number_format(($course['completed_units_count']/$course['units_count'])*100,0); ?>% of the course)!<br>See below for the results</p>

								<div class="course_results">
									<?php
										$module_count = 1;
										foreach ($course['modules'] as $module_info){
									?>
									<h4>Module #<?php echo $module_count++; ?>: <?php echo $module_info['title']; ?></h4>
									<table class="table">
										<thead>
											<tr>
												<th>Unit Title</th>
												<th width="17%">Status</th>
												<th width="15%">Quiz Results</th>
											</tr>
										</thead>
										<tbody>
											<?php foreach ($module_info['units'] as $unit_info) { ?>
											<tr>
												<td><a class="title" href="<?php echo $unit_info['href']; ?>"><?php echo $unit_info['title']; ?></a></td>
												<td><?php if($unit_info['completed_date']){ ?> <a href="<?php echo $unit_info['href']; ?>" class="label label-success">Completed</a> <?php }else{ ?> <a href="<?php echo $unit_info['href']; ?>" class="label label-default">Not completed</a> <?php } ?></td>
												<td>
													<?php
														if($unit_info['completed_date']){
															if($unit_info['quiz_response']){
																//print_r($unit_info['quiz_results']);
																$tt = $unit_info['quiz_results']['total_questions'];
																//$tt = $tt+1;
																$cc = $unit_info['quiz_results']['correct_responses'];
																//$cc = $cc+1;
																$final_percentage = number_format(($cc/$tt)*100,0);
																echo $final_percentage."%";
															}else{
																echo "-";
															}
														}else{
															echo "-";
														}
													?>
												</td>
											</tr>
											<?php } ?>
										</tbody>
									</table>
									<?php } ?>
								</div>

								<style type="text/css">
									.course_results{}
									.course_results h4{ text-align: left; color: #fff; }
									.course_results .table{ margin-bottom: 50px; }
									.course_results .table th{ color: #ccc; }
									.course_results .table th{ border-bottom: solid 1px #555; }
									.course_results .table td{ color: #ccc; text-align: left; border-bottom: solid 1px #555; }
									.course_results .table td a.title{ color: #ccc; }
									.course_results .table td a.title:hover{ color: #fff; text-decoration: underline; }
								</style>

								<a href="<?php echo home_url("/dashboard"); ?>" class="btn btn-success" style="margin-top: 15px;">Back to Dashboard <i class="fa fa-arrow-right" aria-hidden="true"></i></a>
							<?php }else{ ?>
								<i class="fa fa-check-circle icon" aria-hidden="true"></i>
								<h2>Module complete!</h2>
								<p>Congratulations, you have completed all the units in this module!<br>Click Next Module button to keep moving through the Course.</p>
								<?php if($module['quiz_results']['total_questions']){ ?>
								<p>You got <?php echo $module['quiz_results']['correct_responses']; ?> out of <?php echo $module['quiz_results']['total_questions']; ?> questions (<?php echo $module['quiz_results']['percentage']; ?>%) correct!<span class="hide"><br>The pass grade for this quiz is (100%)</span></p>
								<?php } ?>
								<p  class="hide">Congratulations, you have passed this quiz!<br>Click Next Unit to keep moving through the Course</p>
								<a class="btn btn-default" href="<?php echo $module['units'][0][href]; ?>">Try Again <i class="fa fa-refresh" aria-hidden="true"></i></a>
								<a href="<?php echo $next_module['href']; ?>" class="btn btn-success">Continue to next module <i class="fa fa-arrow-right" aria-hidden="true"></i></a>
								
								
								<?php foreach ($module['units'] as $unit_info){ if($unit_info['quiz_response']){?>
							<div class="unit_quiz_responses">
								<h3>Quiz: <?php echo $unit_info['quiz']['title']; ?></h3>
								<ul>
									<?php
										$question_count = 0;
										// echo '<pre>';
										// $module[units]='';
										// print_r($module);
										// echo '</pre>';
										foreach ($unit_info['quiz_response'] as $question){
									?>
										<li>
											<div class="icon">
												<?php if($question['response']==$question['answer']){ ?>
												<i class="fa fa-check-circle" aria-hidden="true"></i>
												<?php }else{ ?>
												<i class="fa fa-times-circle" aria-hidden="true"></i>
												<?php } ?>
											</div>
											<div class="info">
												<div class="unit_question"><strong>Question: </strong><?php echo $question['question']; ?></div>
												<div class="your_answer"><strong>Your answer: </strong><?php echo $question['response']; ?></div>
												<?php if($question['response']!=$question['answer']){ ?>
													<?php if (isset($question['incorrect_reason']) && $question['incorrect_reason'] !='') { ?>
														<div class="incorrect_reason"><strong>Incorrect Feedback: </strong><?php echo $question['incorrect_reason']; ?></div>
													<?php } ?>
												<?php } ?>
												<div class="correct_answer"><strong>Correct answer: </strong><?php echo $question['answer']; ?></div>
												<?php if (isset($question['answer_reason']) && $question['answer_reason'] !='') { ?>
													<div class="answer_reason"><strong>Correct Feedback: </strong><?php echo $question['answer_reason']; ?></div>
												<?php } ?>
											</div>
										</li>
									<?php	} ?>
								</ul>
							</div>
							<?php }} ?>

							<?php } ?>

							
						</div>

					</div>
				</div>

				

			</div>

		</div>
</div>


<?php if (!empty($_POST)) {
// scroll to top on submitting a quiz response ?>
<script type="text/javascript">
	$(document).ready(function(){
	    var target = $(".unit_quiz_responses:first");

	    if( target.length ) {
	        event.preventDefault();
	        $('html, body').animate({
	            scrollTop: target.offset().top
	        }, 0);
	    }
	})
</script>

<?php } ?>