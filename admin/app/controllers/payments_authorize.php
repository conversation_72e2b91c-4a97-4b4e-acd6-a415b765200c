<?php 

include $_SERVER['DOCUMENT_ROOT'].'/engine/tools/authorize_net/vendor/autoload.php';
use net\authorize\api\contract\v1 as AnetAPI;
use net\authorize\api\controller as AnetController;

/**
 * 
 */
class Payments_authorize extends Controller
{
	
	function __construct()
	{
	    $this->payment=new PaymentsAuthorizeModel;
	    $this->usergroup=$this->payment->getusergroup_from_host();
	    $this->payment_data= $this->payment->fetch_authorize_client_details($this->usergroup);
	}


	function getTransactionDetails()
	{
	    /* Create a merchantAuthenticationType object with authentication details
	       retrieved from the constants file */

	    $transactionId=isset($_POST['transactionId'])?$_POST['transactionId']:null;
	    $student_id=isset($_POST['student_id'])?$_POST['student_id']:null;
	    if ($transactionId=="") {
	    	echo json_encode(array('error'=>"transaction id supplied not supplied"));exit();
	    }
	    $merchantAuthentication = new AnetAPI\MerchantAuthenticationType();
	    $merchantAuthentication->setName($this->payment_data[0]["live_api_login_id"]);
	    $merchantAuthentication->setTransactionKey($this->payment_data[0]["live_transaction_key"]);
	    
	    // Set the transaction's refId
	    // The refId is a Merchant-assigned reference ID for the request.
	    // If included in the request, this value is included in the response. 
	    // This feature might be especially useful for multi-threaded applications.
	    $refId = 'ref' . time();

	    $request = new AnetAPI\GetTransactionDetailsRequest();
	    $request->setMerchantAuthentication($merchantAuthentication);
	    $request->setTransId($transactionId);

	    $controller = new AnetController\GetTransactionDetailsController($request);

	    $response = $controller->executeWithApiResponse( \net\authorize\api\constants\ANetEnvironment::PRODUCTION);

	    if (($response != null) && ($response->getMessages()->getResultCode() == "Ok"))
	    {

	    	if ($response->getTransaction()->getTransactionStatus()!="settledSuccessfully") {
	    		echo json_encode(array('error'=>"payment not settled successifully"));exit();
	    	}
	        // echo "SUCCESS: Transaction Status:" . $response->getTransaction()->getTransactionStatus() . "<pre>";
	        // echo "                Auth Amount:" . $response->getTransaction()->getAuthAmount() . "<pre>";
	        // echo "                   Trans ID:" . $response->getTransaction()->getTransId() . "<pre>";
	        // echo "                   Auth Code:" . $response->getTransaction()->getAuthCode(). "<pre>";
	        // echo "Json data <pre>".print_r(($response->getTransaction()->jsonSerialize()),1)."</pre>";
	        // echo "Customer id  data <pre>".print_r(($response->getTransaction()->getCustomer()),1)."</pre>";
            $existing_payment=$this->payment->get_user_with_authcode($response->getTransaction()->getAuthCode());
	        if (isset($existing_payment[0])) {
	        	echo json_encode(array('error' => "payment auth code ".$response->getTransaction()->getAuthCode()." exists for user by name <a target='_blank' href='/engine/direct/proc?pg=4&vw=".$existing_payment[0]['core_students_username_id']."&ref=".$existing_payment[0]['core_students_id']."'> ".$existing_payment[0]['full_name']." <a>" ));exit();
	        }
            $paymentdate = new DateTime($response->getTransaction()->jsonSerialize()['submitTimeUTC']);
	        $sis_student['rel_id']=$student_id;
	        $sis_student['usergroup']=$this->usergroup;
	        $sis_student['date']=$paymentdate->format('Y-m-d H:i:s');
	        $sis_student['db15459']=$paymentdate->format('Y-m-d');
	        $sis_student['db1495']=$response->getTransaction()->getAuthAmount();
	        $sis_student['db1494']="";

	        $sis_student['db1492']="authorize.net";
	        $sis_student['db1493']="Pay With Your Credit Card";
	        $sis_student['db34450']="Payment";
	        $sis_student['db37345']=$response->getTransaction()->getAuthCode();

	        //echo "<pre>".print_r($sis_student,1)."</pre>";

	        $id=$this->payment->insert_payment($sis_student);
            //echo $id;
            if ($id) {
            	echo json_encode(array('success'=>"payment attached to the timeline"));exit();
            }

	     }
	    else
	    {
	        //echo "ERROR :  Invalid response\n";
	        $errorMessages = $response->getMessages()->getMessage();
	        echo json_encode(array("error"=>"Response : " . $errorMessages[0]->getCode() . "  " .$errorMessages[0]->getText() . "\n"));exit();
	    }

	    return $response;
	  }


}

 ?>