<?php

use App\jobs\ProcessApplicantExports;
use App\jobs\ProcessBulkApplicantActions;


/**
 *
 */
class Applicants extends Controller
{
    private $cohorts;
    private $current_cohort;

    public function __construct()
    {
        parent::__construct();
        if (!$this->permissions->has_permission('navigation', 'applications')) {
            $data = array(
                'meta_title' => 'Access Denied',
                'view_file' => 'error_pages/permissions',
            );
            $this->view($this->layout, $data);
            exit();
        }
        $students = new Students;
        $this->cohorts = $students->cohorts_list(array('school_id' => $this->school_info['id']));
        $this->current_cohort = (!empty($_GET['cohort']) ?$_GET['cohort']: ((strtolower($this->school_info['preferences']['db48560']) == 'all' ? 'all' : (isset($_SESSION['school_cycle']) ? $_SESSION['school_cycle'] : $this->school_info['cohort']))));
    }

    public function index($applicant_id = '', $type = '')
    {

        $applicants = new Students;
        $OL = new OnlineLearning;
        $bookings = new EventsModel;
        $form_cms = new FrontendPages;


        if ($applicant_id) {

            /** ===================================
             * Applicant Details
             * ====================================  */
            if ($applicant_id != "new") {
                $applicants_args = array("id" => $applicant_id, 'school_id' => $this->school_info['id']);
                $entry_info = $applicants->get($applicants_args);

                if (!$entry_info['id']) {
                    $data = array(
                        'meta_title' => 'Not found',
                        'view_file' => 'error_pages/404',
                    );

                    $this->view($this->layout, $data);
                    exit();
                }
            } else {

                $entry_info = array();
                $data = array(
                    'meta_title' => 'Not found',
                    'view_file' => 'error_pages/404',
                );

                $this->view($this->layout, $data);
                exit();
            }


            /** ===================================
             * JSON result
             * ==================================== */
            $entry_json = json_encode($entry_info);
            if (strpos($this->current_url(), '.json') !== false) {
                echo $entry_json;
                exit();
            }

            /** ===================================
             * Get Online Courses
             * ====================================  */
            $course_args = array(
                'student_id' => $entry_info['id'],
                'active_courses' => true,
                'raw_html' => true
            );
            $online_courses = $OL->get_courses($course_args);


            /** ===================================
             * Get Access Plans
             * ====================================  */
            $access_plan_args = array('student_id' => $entry_info['id']);
            $access_plans = $OL->get_plans($access_plan_args);

            /** ===================================
             * Get Support tickets
             * ====================================  */
            $support_args = array('student_id' => $entry_info['id']);
            $support_tickets = $OL->get_support($support_args);


            /** ===================================
             * Get Courses Bookings
             * ====================================  */
            $bookings_args = array();
            $all_booking_stages = array();
            $bookings_args = array('candidateid' => $entry_info['id']);
            //$all_bookings = $bookings->get_applicant_bookings($bookings_args);
            //$all_booking_stages = $bookings->get_booking_stages();
            /** ===================================
             * Details
             * ====================================  */
            $data = array(
                'meta_title' => 'Dashboard',
                'view_file' => 'applicants/details',
                'applicant' => $entry_info,
                'online_courses' => $online_courses,
                'access_plans' => $access_plans,
                'support_tickets' => $support_tickets,
                'all_bookings' => $all_bookings,
                'all_booking_stages' => $all_booking_stages
            );

            $this->view($this->layout, $data);

        } else {

            /** ===================================
             * Applicants Results
             * ====================================  */
            $applicants = new students;
            $courses = new courses;
            $form_templates = new FormTemplates;
            $users = new Users;

            $applicants_args = array('school_id' => $this->school_info['id'], 'no_one_entry' => true, 'cohort' => $this->current_cohort);

            //Check if there are any extra args on the url
            if (count($_GET)) {
                $applicants_args = array_merge($applicants_args, $_GET);
            }


            if (request('search')) {
                $applicants_args['search'] = request('search');
            }

            if (request('order')) {
                $applicants_args['order'] =request('order');
            } else {
                $applicants_args['order'] = pull_field("lead_preferences", "db254591", "WHERE usergroup='$_SESSION[usergroup]'") ?? 'last_name';
            }
            $applicants_args['paginate'] = true;
            if ($type == 'has_submitted' || isset($_GET['submitted'])) {
                $applicants_args['has_submitted'] = true;
            }
            if(request('assigned_to_me') || request('reviewer_id')){
                $applicants_args['reviewer_id'] = $_SESSION['uid'];
            }

            if ($this->filter['description']) {
                $filters_args['exploded'] = $applicants->filters_students_args($this->filter['description']);
                //if($_SESSION['usergroup'] == 3){
                $applicants_args['filter_sql_lite'] = prepare_filter_sql($this->filter['description'], $applicants->filter_columns());
                //var_dump($applicants_args);
                //}
                $applicants_args['filter'] = $this->filter;
                $applicants_args['search'] = $this->filter['get_params']['search'];
                $applicants_args = array_merge($applicants_args, $filters_args);
                //die(json_encode($this->filter));
            } else {
                $this->filter = array("page" => 'applicants');
            }

            //Check if the default columns exist
            $columns_args = array('page' => 'applicants');
            $page_columns = $form_templates->get_page_columns($columns_args);

            if (!$page_columns) {


                //check if the HEIAPPLY super admin user has a list
                $user_info = $users->get(array('search' => '_superadmin', 'school_id' => $this->school_info['id'], 'order' => 'form_users.id ASC'));
                $user_info = $user_info[0];

                if ($user_info['id']) {
                    $columns_args['user_id'] = $user_info['id'];

                    $columns = $form_templates->get_page_columns($columns_args);
                    $df = json_encode($columns);
                } else {
                    $df = '["id","first_name","middle_name","email_address"]';
                }

                // echo '<pre>';
                // 	print_r($df);
                // 	exit();

                $column_args = array(
                    'columns' => $df,
                    'page' => 'applicants'
                );
                $form_templates->add_page_columns($column_args);
            }
            $results = $applicants->get($applicants_args);
            /** ===================================
             * Export Results
             * ====================================  */
            if (request('export') ||request('export_data') || request('export_to_ukvi')) {

                $payload=array_merge(request()->all(),$applicants_args);
                $payload['usergroup']=$_SESSION['usergroup'];
                $payload['uid']=$_SESSION['uid'];
                $selected_rows = [];
                if (empty(request('select_all_entries'))) {
                    foreach ($_POST as $key => $value) {
                        if (strpos($key, 'table_row_') !== false) {
                            $row_id = str_replace("table_row_", "", $key);
                            $selected_rows[] = $row_id;
                        }
                    }
                }
                $payload['selected_rows']=$selected_rows;
                $payload['session']=$_SESSION;
                //unset big session keys
                unset($payload['session']['permissions']);
                unset($payload['session']['unlimited_query']);
                unset($payload['session']['unlimited_query_enq']);
                unset($payload['session']['custom_filter_49']);
                if (request('export_to_ukvi')){
                    $payload['type']='ukvi';
                }
                ProcessApplicantExports::dispatch($payload);
                echo 'Your export has begun in the background. You will be notified when it completes.';
                exit();
            }


            /** ===================================
             * Do a bulk action
             * ====================================  */
            if (request('bulk_action')) {
                $payload=array_merge(request()->all(),$applicants_args);
                $payload['usergroup']=$_SESSION['usergroup'];
                $payload['uid']=$_SESSION['uid'];
                $selected_rows = [];
                if (empty(request('select_all_entries'))) {
                    foreach ($_POST as $key => $value) {
                        if (strpos($key, 'table_row_') !== false) {
                            $row_id = str_replace("table_row_", "", $key);
                            $selected_rows[] = $row_id;
                        }
                    }
                }
                $payload['selected_rows']=$selected_rows;
                $payload['session']=$_SESSION;
                //unset big session keys
                unset($payload['session']['permissions']);
                unset($payload['session']['unlimited_query']);
                unset($payload['session']['unlimited_query_enq']);
                unset($payload['session']['custom_filter_49']);
                ProcessBulkApplicantActions::dispatch($payload);
                $message="The action is now being processed.";
                if (request('bulk_action')==='bulk_email'){
                    $url = "<p><br><br><br><a href = '" . base_url("system/email_logs/scheduled") . "' class = 'btn btn-primary thickbox' target = '_blank'>Click here to see the queued emails</a><br><br><br>Emails will be sent out automatically in 15 minutes</p>";
                    $message = "<h4>Congratulations!</h4><p>Your messages are now being processed. You can check the status of your messages in the email logs.</p>" . $url;
                    echo json_encode(['message' => $message]);
                    exit();
                }

            }
            if (isset($_POST['bulk_action']) && $_POST['bulk_action']) {
                $cur = $this->current_url();
                $this->redirect($cur, 'refresh');
            }
            if ('yes' == pull_field("lead_preferences", "db106277", "WHERE usergroup='$_SESSION[usergroup]'")) {

                $allPagesArgs = [
                    'school_id' => $_SESSION['usergroup'],
                    'includeMultiStagePages' => true,
                    'stages' => true,
                    'allCampaigns' => true

                ];
                $print_pages_args = [
                    "school_id" => $_SESSION['usergroup'],
                    "order_by" => "db748",
                    "english_language_pages" => 1,
                    'has_form' => 1,
                    'eligible_pages_only' => true,
                    'id_in' => $form_cms->get_all_pages($allPagesArgs)
                ];
                if (107 == $_SESSION['usergroup']) {
                    $print_pages_args['id_in'] = $print_pages_args['id_in'] . ",4925,4928,4931,4934,5234,5231,5237,5240,4226";
                }
                if (!empty($_SESSION['custom_ulevel'])) {
                    $print_pages_args['form_ids_not_in'] = pull_field('lead_preferences', 'db129971', 'where usergroup=' . $_SESSION['usergroup']);
                }

                $print_pages = $form_cms->get($print_pages_args);
            }

            $data = array(
                'meta_title' => 'Applicants',
                'view_file' => 'applicants/index',
                'type_id' => $type_id,
                'programme' => $programme,
                'results' => $results,
                'paginator' => $paginator,
                'filter' => $this->filter,
                'filters_args' => $filters_args,
                'filter_page_name' => 'applicants',
                'active_tab' => 'applicants',
                'cohort' => $this->current_cohort,
                'cohorts' => $this->cohorts,
                'screening_option' => false,
                'saved_filters_dropdown' => true,
                'fetchsavedprintsectionsview' => $this->fetchsavedprintsectionsview(),
                'print_pages' => $print_pages,
                'message' => $bulk
            );

            if (!empty($_GET['fetchsavedprintsectionsview'])) {
                echo "<pre>" . print_r($data['fetchsavedprintsectionsview'], 1) . "</pre>";
            }

            if ($_GET['variable']) {
                echo json_encode($data['results']);
                die();
            }

            if (!empty($_POST['bulk_action'] == "bulk_save_table")) {
                $data['alert_message'] = "Data saved successfully!";
            }
            $this->view($this->layout, $data);

        }
    }

    public function bulk_email_applicants()
    {
        $students = new Students();

        $entries = ($_POST['entries']);

        $applicants = [];
        if (!empty($entries)) {
            foreach ($entries as $key => $value) {
                $applicants[] = ['id' => $value];
            }
        }

        $students->bulk_email_applicants([], $applicants);

    }

    private function background_export($applicants_args)
    {
        $session = $_SESSION;
        if (!empty($session['debugbar'])) unset($session['debugbar']);
        unset($applicants_args['paginate']);
        $payload = ['category' => 'applicants_export', 'request' => $_REQUEST, 'get' => $_GET, 'session' => $session, 'applicant_args' => $applicants_args, 'filter' => $this->filter];

        $dir = media_store . 'reports/';
        $store_link = $dir . uniqid() . ".json";
        file_put_contents($store_link, json_encode($payload));

        $dbh = get_dbh();
        $stmt = $dbh->prepare("INSERT INTO form_jobs(usergroup,date,rec_lstup,db57005) VALUES (?,NOW(),NOW(),?)");
        $stmt->execute([$_SESSION['usergroup'], json_encode(['store_link' => $store_link])]);
        $id = $dbh->lastInsertId();
        $started = $this->start_form_jobs('admin/cronjob/export_custom_report?id=' . $id);
        exit($started === true ? '<h3>Your report is being generated in the background</h3><p trackid=' . $id . '>We will notify you via email as soon as the report is ready for downloading.</p>' : $started);
    }

    private function start_form_jobs($uri)
    {
        require_once(__DIR__ . '/../../../vendor/autoload.php');
        $base = "https://{$_SERVER['HTTP_HOST']}";
        try {
            $client = new GuzzleHttp\Client(['base_uri' => $base, 'verify' => false, 'timeout' => 5]); // maybe 2 seconds was too short on uat for the connection to be actually established.
            //$client = new GuzzleHttp\Client(['base_uri' => $base]);
            $response = $client->request('GET', $uri);
            //$promise = $client->getAsync('api/cronjobs/Cron_run_scheduled_user_imports.php'); // job not starting
            error_log("response $base **" . $response->getBody());
            return true;
        } catch (Exception $e) {
            error_log("Exception $base **" . $e->getMessage());
            return $e->getMessage();
        }
    }

    public function has_submitted()
    {
        $this->index(false, 'has_submitted');
    }

    public function send_bulk_message()
    {

        if ($_POST["select_all_entries"] == 1) {
            SendBulkApplicantEmails::dispatch();
            $applicants = new students;
            $courses = new courses;
            $form_templates = new FormTemplates;
            $users = new Users;
            if ($_GET["nocohort"]) {
                $applicants_args = array('school_id' => $this->school_info['id'], 'no_one_entry' => true);
            } else {
                $applicants_args = array('school_id' => $this->school_info['id'], 'no_one_entry' => true, 'cohort' => $this->current_cohort);
            }


            //Check if there are any extra args on the url
            if (count($_GET)) {
                $applicants_args = array_merge($applicants_args, $_GET);
            }
            if ($_GET["stage"]) {
                $current_stage = $form_templates->get_custom_form_core_stages(array("use_order" => $_GET["stage"]));
                $applicants_args['stage_id'] = $current_stage['id'];
                $applicants_args['show_rejected'] = true;
                $applicants_args['show_withdrawn'] = true;
            }
            if ($_GET["course_level_id"]) {
                $applicants_args['show_rejected'] = true;
                $applicants_args['show_withdrawn'] = true;
            }
            if ($_REQUEST['search']) {
                $applicants_args['search'] = $_REQUEST['search'];
            }

            if ($_GET['order']) {
                $applicants_args['order'] = $_GET['order'];
            } else {
                $applicants_args['order'] = 'last_name';
            }

            if ($_GET['export'] || $_GET['export_to_ukvi'] || $_GET['export_data'] || $_POST['select_all_entries']) {
                ini_set('memory_limit', '-1');
            } else {
                $applicants_args['paginate'] = true;
            }

            if ($type == 'has_submitted') {
                $applicants_args['has_submitted'] = true;
            }

            if ($this->filter['description']) {
                $filters_args['exploded'] = $applicants->filters_students_args($this->filter['description']);
                //if($_SESSION['usergroup'] == 3){
                $applicants_args['filter_sql_lite'] = prepare_filter_sql($this->filter['description'], $applicants->filter_columns());
                //var_dump($applicants_args);
                //}
                $applicants_args['filter'] = $this->filter;
                $applicants_args['search'] = $this->filter['get_params']['search'];
                $applicants_args = array_merge($applicants_args, $filters_args);
                //die(json_encode($this->filter));
            } else {
                $this->filter = array("page" => 'applicants');
            }

            //Check if the default columns exist
            $columns_args = array('page' => 'applicants');
            $page_columns = $form_templates->get_page_columns($columns_args);

            if (!$page_columns) {


                //check if the HEIAPPLY super admin user has a list
                $user_info = $users->get(array('search' => '_superadmin', 'school_id' => $this->school_info['id'], 'order' => 'form_users.id ASC'));
                $user_info = $user_info[0];

                if ($user_info['id']) {
                    $columns_args['user_id'] = $user_info['id'];

                    $columns = $form_templates->get_page_columns($columns_args);
                    $df = json_encode($columns);
                } else {
                    $df = '["id","internal_reference","first_name","middle_name","email_address"]';
                }

                // echo '<pre>';
                // 	print_r($df);
                // 	exit();

                $column_args = array(
                    'columns' => $df,
                    'page' => 'applicants'
                );
                $form_templates->add_page_columns($column_args);
            }

            $results = $applicants->get($applicants_args);
        }
        $applicants = new students();
        $sent = $applicants->process_bulk_action(array('results' => $results));
        echo json_encode(['message' => $sent]);
    }

    /**All Applications By Applicant**/
    public function applications($applicant_id = '')
    {
        $applicants = new Students;
        $applicants_args = $applicant_id;
        $student_info = $applicants->get_student_data($applicant_id);
        $_SESSION['rfid_logged'] = true;

        //print_r($student_info);
        /** ===================================
         * Get Applications
         * ====================================  */
        $userid = $student_info[0]['id'];
        $all_applications = $applicants->get_applicant_applications($userid);
        $all_booking_stages = $applicants->get_application_stages();

        /** ===================================
         * Get Enquiries Based On The Preferences
         * ====================================  */
        $enq = new enquirieModel;

        //check Match Enquiry Forms Based On from preference
        $enq_pref = trim($enq->get_enq_pref());

        if ($enq_pref == 'name,surname') {
            $enq_args = array(
                'paginate' => true,
                'profile_name' => $student_info[0]['db106'],
                'profile_surname' => $student_info[0]['db111'],
            );
        } elseif ($enq_pref == 'name,email') {
            $enq_args = array(
                'paginate' => true,
                'profile_name' => $student_info[0]['db106'],
                'profile_email' => $student_info[0]['db119'],
            );
        } elseif ($enq_pref == 'surname,email') {
            $enq_args = array(
                'paginate' => true,
                'profile_surname' => $student_info[0]['db111'],
                'profile_email' => $student_info[0]['db119'],
            );
        } elseif ($enq_pref == 'name,surname,email') {
            $enq_args = array(
                'paginate' => true,
                'profile_email' => $student_info[0]['db119'],
                'profile_name' => $student_info[0]['db106'],
                'profile_surname' => $student_info[0]['db111'],
            );
        } elseif ($enq_pref == 'surname') {
            $enq_args = array(
                'paginate' => true,
                'profile_surname' => $student_info[0]['db111'],

            );
        } elseif ($enq_pref == 'name') {
            $enq_args = array(
                'paginate' => true,
                'profile_name' => $student_info[0]['db106'],

            );
        } else {
            $enq_args = array(
                'paginate' => true,
                'profile_email' => $student_info[0]['db119'],

            );
        }

        //--start set filters
        $final_results = $enq->get_enq($enq_args);


        $p = new parents_model;
        $parents = [];
        foreach ($p->get(['user_id' => $userid]) as $parent) {
            $parents[] = array(
                'ID' => $parent['id'],
                'Title' => $parent['db12158'],
                'First Name' => $parent['db12157'],
                'Last Name' => $parent['db12160'],
                'Email' => $parent['db12163']
            );
        }

        $f = new Feedback;
        $feedback = $f->get(['student_ids' => array_column($all_applications, 'application_id'), 'enquiries' => array_column($final_results, 'id')]);
        $f_data = $f->form_results($feedback);

        /** ===================================
         * Details
         * ====================================  */
        $data = array(
            'meta_title' => 'Dashboard',
            'view_file' => 'applicants/all_applications',
            'applicant' => $student_info,
            'enq_results' => $final_results,
            'all_applications' => $all_applications,
            'all_application_stages' => $all_booking_stages,
            'reviews' => $f_data,
            'parents' => $parents,
            'user_id' => $userid
        );

        $this->view($this->layout, $data);
    }


    /** ===================================
     * HOD Applicants
     * ====================================  */
    public function hod($applicant_id = '')
    {
        $applicants = new Students;
        $OL = new OnlineLearning;


        /** ===================================
         * Applicants Results
         * ====================================  */
        $applicants = new students;
        $courses = new courses;
        $form_templates = new FormTemplates;
        $users = new Users;


        $applicants_args = array('school_id' => $this->school_info['id'], 'no_one_entry' => true);

        //Check if there are any extra args on the url
        if (count($_GET)) {
            $applicants_args = array_merge($applicants_args, $_GET);
        }


        if ($_REQUEST['search']) {
            $applicants_args['search'] = $_REQUEST['search'];
        }
        if ($_GET['order']) {
            $applicants_args['order'] = $_GET['order'];
        } else {
            $applicants_args['order'] = 'last_name';
        }

        if ($_GET['export'] || $_GET['export_to_ukvi'] || $_POST['select_all_entries']) {
            ini_set('memory_limit', '-1');
        } else {
            $applicants_args['paginate'] = true;
        }


        if ($this->filter['description']) {
            $filters_args = $applicants->filters_students_args($this->filter['description']);
            $applicants_args['filter'] = $this->filter;
            $applicants_args = array_merge($applicants_args, $filters_args);
        } else {
            $this->filter = array("page" => 'hod_applicants');
        }

        //Check if the default columns exist
        $columns_args = array('page' => 'hod_applicants');
        $page_columns = $form_templates->get_page_columns($columns_args);

        if (!$page_columns) {


            //check if the HEIAPPLY super admin user has a list
            $user_info = $users->get(array('search' => '_hod', 'school_id' => $this->school_info['id'], 'order' => 'form_users.id ASC'));
            $user_info = $user_info[0];


            if ($user_info['id']) {
                $columns_args['user_id'] = $user_info['id'];

                $columns = $form_templates->get_page_columns($columns_args);
            }

            if ($columns) {
                $df = json_encode($columns);
            } else {
                $df = '["internal_reference","first_name","last_name","date_of_birth","application_stage","course","course_abbreviation_code","department","assigned_to"]';
            }

            $column_args = array(
                'columns' => $df,
                'page' => 'hod_applicants'
            );
            $form_templates->add_page_columns($column_args);
        }

        $results = $applicants->get($applicants_args);

        /** ===================================
         * Export Results
         * ====================================  */
        if ($_GET['export']) {

            if (!$this->filter['id']) {
                $this->filter = array("page" => 'hod_applicants');
            }
            $export_args = array(
                'filter' => $this->filter,
                'results' => $results
            );

            $applicants->export_student_data($export_args);
            exit();
        }

        /** ===================================
         * Export Results
         * ====================================  */
        if ($_GET['export_to_ukvi']) {

            if (!$this->filter['id']) {
                $this->filter = array("page" => 'hod_applicants');
            }
            $export_args = array(
                'filter' => $this->filter,
                'results' => $results
            );

            $applicants->export_student_data_to_ukvi($export_args);
            exit();
        }

        /** ===================================
         * Do a bulk action
         * ====================================  */
        $applicants->process_bulk_action(array('results' => $results));


        /** ===================================
         * Programme info
         * ====================================  */
        $programmes_args = array("id" => $_GET['course_id'], 'school_id' => $this->school_info['id']);
        $programmes = $courses->get($programmes_args);

        $data = array(
            'meta_title' => 'HOD Applicants',
            'custom_title' => $programmes['title'] . ' Applicants',
            'view_file' => 'applicants/index',
            'type_id' => $type_id,
            'programme' => $programme,
            'results' => $results,
            'paginator' => $paginator,
            'filter' => $this->filter,
            'filters_args' => $filters_args,
            'filter_page_name' => 'applicants',
            'active_tab' => 'applicants',
            'hide_export_button' => true,
            'hide_edit_columns' => true,
            'hide_filters' => 1,
            'saved_filters_dropdown' => true
        );
        $this->view($this->layout, $data);
    }

    /**
     * Saves table column order in database
     * @date    31/05/2019 12:43. Do not delete on conflict
     * @param none
     * @return      string / JSON.
     */
    public function save_table_col_order()
    {
        if (isset($_POST['col_order'])) {
            //echo "data received";

            $option_name = "table_col_order";
            //$option_value = sanitise($_POST['col_order']);
            $data = $_POST['col_order'];
            $option_value = [];
            foreach ($data as $v) {
                $option_value[] = str_replace('col-', '', $v);
            }

            $add_non_existant = true;
            $logged_in_user = true;
            $results = update_option($option_name, $option_value, $add_non_existant, $logged_in_user);
            $options = get_option($option_name);
            echo json_encode(array('options' => $options));
        } else echo "No data sent";
    }


    /** ===================================
     * Online Leaners
     * ====================================  */
    public function online_courses_learners()
    {

        /** ===================================
         * Applicants Results
         * ====================================  */
        $applicants = new students;
        $courses = new courses;
        $form_templates = new FormTemplates;


        $applicants_args = array('school_id' => $this->school_info['id'], 'no_one_entry' => true);

        if ($_REQUEST['search']) {
            $applicants_args['search'] = $_REQUEST['search'];
        }
        if ($_GET['order']) {
            $applicants_args['order'] = $_GET['order'];
        } else {
            $applicants_args['order'] = 'last_name';
        }
        if (!$_GET['export'] && !$_GET['export_to_ukvi']) {
            $applicants_args['paginate'] = true;
        } else {
            ini_set('memory_limit', '-1');
        }

        if ($this->filter['description']) {
            $filters_args = $applicants->filters_students_args($this->filter['description']);
            $applicants_args['filter'] = $this->filter;
            $applicants_args = array_merge($applicants_args, $filters_args);
        } else {
            $this->filter = array("page" => 'applicants');
        }

        //Check if the default columns exist
        $columns_args = array('page' => 'applicants');
        $page_columns = $form_templates->get_page_columns($columns_args);
        if (!$page_columns) {

            if (get_option('applicants_page_fields')) {
                $df = json_encode(get_option('applicants_page_fields'));
            } else {
                $df = '["id","internal_reference","first_name","middle_name","email_address"]';
            }
            $column_args = array(
                'columns' => $df,
                'page' => 'applicants'
            );
            $form_templates->add_page_columns($column_args);
        }

        $results = $applicants->get($applicants_args);


        if ($_GET['export']) {

            if (!$this->filter['id']) {
                $this->filter = array("page" => 'applicants');
            }
            $export_args = array(
                'filter' => $this->filter,
                'results' => $results
            );

            $applicants->export_student_data($export_args);
            exit();
        }
        if ($_GET['export_to_ukvi']) {

            if (!$this->filter['id']) {
                $this->filter = array("page" => 'applicants');
            }
            $export_args = array(
                'filter' => $this->filter,
                'results' => $results
            );

            $applicants->export_student_data_to_ukvi($export_args);
            exit();
        }

        $data = array(
            'meta_title' => 'Online Courses Applicants',
            'view_file' => 'applicants/index',
            'type_id' => $type_id,
            'programme' => $programme,
            'results' => $results,
            'paginator' => $paginator,
            'filter' => $this->filter,
            'filters_args' => $filters_args,
            'filter_page_name' => 'applicants',
            'online_learners_screen' => true,
            'active_tab' => 'applicants',
            'hide_export_button' => true,
            'saved_filters_dropdown' => true
        );
        $this->view($this->layout, $data);

    }


    /** ===================================
     * Online Course Results
     * ====================================  */
    public function course_results($course_id = '')
    {

        $applicants = new students;
        $courses = new courses;
        $OL = new OnlineLearning;

        $course_args = array(
            'student_id' => $this->uri_segement(2),
            'active_courses' => true,
            'raw_html' => true
        );
        $online_courses = $OL->get_courses($course_args);

        $course_info = $OL->get_courses(array('id' => $course_id, 'raw_html' => true, 'student_id' => $this->uri_segement(2)));

        $applicants_args = array("id" => $this->uri_segement(2), 'school_id' => $this->school_info['id']);
        $applicant = $applicants->get($applicants_args);

        $data = array(
            'meta_title' => 'Users',
            'view_file' => 'applicants/course_results',
            'course' => $course_info,
            'online_courses' => $online_courses,
            'applicant' => $applicant,
            'active_tab' => 'applicants'
        );
        $this->view($this->layout, $data);

    }


    /** ===================================
     * Download Media Files
     * ====================================  */
    public function download_media_files($course_id = '')
    {

        $applicants = new students;
        $courses = new courses;

        $files = $applicants->get_files(array('student_id' => $this->uri_segement(2)));

        $applicants_args = array("id" => $this->uri_segement(2), 'school_id' => $this->school_info['id']);
        $applicant = $applicants->get($applicants_args);


        if (HOSTNAME == "localhost") {
            $ImagesDirectory = "/Applications/XAMPP/xamppfiles/htdocs/images/";
            $zips_folder = "/Applications/XAMPP/xamppfiles/htdocs/zips/";
        } else {
            $ImagesDirectory = "/var/www/vhosts/heiapply.com/private/media/";
            $zips_folder = "/var/www/vhosts/heiapply.com/httpdocs/zips/";
        }
        $zip_file_name = $this->uri_segement(2) . ".zip";
        $zip_destination = $zips_folder . $zip_file_name;

        //Create the directory if it doesn't exist
        if (!is_dir($path)) {
            mkdir($zips_folder, 0777, true);
        }

        // $files = array(
        // 	'1.jpg',
        // 	'2.jpg',
        // 	'3.jpeg',
        // 	'4.png',
        // 	'5.jpg',
        // );

        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);


        $zip = new ZipArchive();
        $zip->open($zip_destination, ZIPARCHIVE::CREATE);

        //Add files
        $files_added = array();
        foreach ($files as $file) {
            $file_url = $ImagesDirectory . $file['file_name'];
            if (file_exists($file_url) && is_file($file_url)) {
                if (!in_array($file_url, $files_added)) {
                    $files_added[] = $file_url;
                    $fn = basename($file_url);
                    echo $fn . "<br>";
                    $zip->addFile($ImagesDirectory . $file, $fn);
                }
            } else {
                echo "NOT FOUND:" . $file_url;
                exit();
            }
        }

        echo '<pre>';
        print_r($zip);
        echo '</pre>';

        echo '<pre>';
        print_r($files_added);
        echo '</pre>';

        //Close zip
        $closed = $zip->close();
        echo $applicants->ZipStatusString($zip->status);
        echo "----<br>";
        echo $applicants->ZipStatusString($zip->statusSys);
        echo '<pre>ZIP COLSE:';
        print_r($closed);
        print_r($zip);
        echo '</pre>';
        exit();

        //Download the zip
        if (file_exists($zip_destination)) {
            header('Content-Description: File Transfer');
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . basename($zip_destination) . '"');
            header('Expires: 0');
            header('Cache-Control: must-revalidate');
            header('Pragma: public');
            header('Content-Length: ' . filesize($zip_destination));
            flush(); // Flush system output buffer
            readfile($zip_destination);
        }
        exit;


    }


    /** ===================================
     * Applicants Types
     * ====================================  */
    public function type($type = '')
    {

        $applicants = new students;
        $courses = new courses;


        //Update the Filters
        if ($_POST['action'] == "update_filter") {
            $applicants_filters = json_decode($_POST['filters']);
            add_option("applicants_filters", $applicants_filters);
        }


        $course_id = $this->uri_segement(4);
        if ($course_id) {
            $programme = $courses->get(array('id' => $course_id));
        }


        $applicants_args = array('paginate' => true, 'school_id' => $this->school_info['id'], 'no_one_entry' => true, 'short_course_only' => true);

        if ($_REQUEST['search']) {
            $applicants_args['search'] = $_REQUEST['search'];
        }
        if ($_GET['order']) {
            $applicants_args['order'] = $_GET['order'];
        }

        $filters_args = $applicants->filters_students_args();
        $applicants_args = array_merge($applicants_args, $filters_args);
        $results = $applicants->get($applicants_args);


        $data = array(
            'meta_title' => 'Users',
            'view_file' => 'applicants/index',
            'type_id' => $type_id,
            'programme' => $programme,
            'results' => $results,
            'paginator' => $paginator,
            'active_tab' => 'applicants',
            'saved_filters_dropdown' => true
        );
        $this->view($this->layout, $data);

    }


    /** ===================================
     * Applicantion Form Data
     * ====================================  */
    public function application_form()
    {

        $applicants = new students;

        /** ===================================
         * Applicant Details
         * ====================================  */
        $applicant_id = $this->uri_segement(2);

        if ($applicant_id != "new") {
            $applicants_args = array("id" => $applicant_id, 'school_id' => $this->school_info['id'], 'show_form_answers' => true);
            $entry_info = $applicants->get($applicants_args);

            if (!$entry_info['id']) {
                $data = array(
                    'meta_title' => 'Not found',
                    'view_file' => 'error_pages/404',
                );
                $this->view($this->layout, $data);
                exit();
            }
        } else {

            $entry_info = array();
            $data = array(
                'meta_title' => 'Not found',
                'view_file' => 'error_pages/404',
            );
            $this->view($this->layout, $data);
            exit();
        }


        /** ===================================
         * Details
         * ====================================  */
        $data = array(
            'meta_title' => 'Dashboard',
            'view_file' => 'applicants/application_form',
            'applicant' => $entry_info,
            'online_courses' => $online_courses,
            'access_plans' => $access_plans,
            'support_tickets' => $support_tickets
        );

        $this->view($this->layout, $data);

    }


    function get_booking_status()
    {
        $bookings = new EventsModel;
        $bookingid = $_POST['bookingid'];
        $current_status = $bookings->get_booking_status($bookingid);
        $data = array(
            'current_status' => $current_status
        );
        echo json_encode($data);
    }


    function log_rfid()
    {
        $_SESSION['rfid_logged'] = !$_SESSION['rfid_logged'];
        $data = array(
            'status' => $_SESSION['rfid_logged']
        );
        echo json_encode($data);
    }

    function change_rfid()
    {
        //$_SESSION['rfid_logged'] = ! $_SESSION['rfid_logged'];
        $data = array(
            'response' => 'RFID CHANGED'
        );
        echo json_encode($data);
    }


    /** ===================================
     * Assigned to currently logged in user
     * ====================================  */
    public function assigned_to_me($course_id = '')
    {

        $applicants = new students;
        $courses = new courses;
        $students = new Students;
        $form_templates = new FormTemplates;
        $users = new Users;
        $form_cms = new FrontendPages;


        //Prep the applicants screen
        $prep_args = array(
            "extra_applicants_args" => array(
                "reviewer_id" => $_SESSION['uid'],
                "cohort" => $this->current_cohort
            ),
            "school_id" => $this->school_info['id'],
            "filter" => $this->filter,
            "cohort" => $this->current_cohort,
            "reviewer_id" => $_SESSION['uid'],
        );
        if ($this->current_cohort == "all") {
            unset($prep_args['extra_applicants_args']['cohort']);
            unset($prep_args['cohort']);
        }

        if ($_GET['export'] || $_GET['export_to_ukvi'] || $_GET['export_data'] || $_POST['select_all_entries']) {
            ini_set('memory_limit', '-1');
        } else {

        }
        $prep_args['paginate'] = true;

        if ($this->filter['description']) {

            $filters_args['exploded'] = $applicants->filters_students_args($this->filter['description']);
            //if($_SESSION['usergroup'] == 3){
            $prep_args['filter_sql_lite'] = prepare_filter_sql($this->filter['description'], $applicants->filter_columns());
            //var_dump($applicants_args);
            //}
            $prep_args['filter'] = $this->filter;
            $prep_args['search'] = $this->filter['get_params']['search'];
            $prep_args = array_merge($prep_args, $filters_args);

        } else {
            $this->filter = array("page" => 'my_assigned_applicants');
        }


        $columns_args = array('page' => 'my_assigned_applicants');
        $page_columns = $form_templates->get_page_columns($columns_args);

        if (!$page_columns) {


            //check if the HEIAPPLY super admin user has a list
            $user_info = $users->get(array('search' => '_superadmin', 'school_id' => $this->school_info['id'], 'order' => 'form_users.id ASC'));
            $user_info = $user_info[0];

            if ($user_info['id']) {
                $columns_args['user_id'] = $user_info['id'];

                $columns = $form_templates->get_page_columns($columns_args);
                $df = json_encode($columns);
            } else {
                $df = '["id","first_name","middle_name","email_address"]';
            }

            // echo '<pre>';
            // 	print_r($df);
            // 	exit();

            $column_args = array(
                'columns' => $df,
                'page' => 'my_assigned_applicants'
            );
            $form_templates->add_page_columns($column_args);
        }

        $prep_args['filter_page_name'] = $this->filter['page'];

        $results_prep = $students->get($prep_args);
        if ('yes' == pull_field("lead_preferences", "db106277", "WHERE usergroup='$_SESSION[usergroup]'")) {
            $print_pages_args = ["school_id" => $_SESSION['usergroup'], "order_by" => "db748", "english_language_pages" => 1, 'has_form' => 1, 'eligible_pages_only' => true, 'id_in' => $form_cms->get_all_pages(['school_id' => $_SESSION['usergroup']])];
            if (107 == $_SESSION['usergroup']) {
                $print_pages_args['id_in'] = $print_pages_args['id_in'] . ",4925,4928,4931,4934,5234,5231,5237,5240,4226";
            }
            if (!empty($_SESSION['custom_ulevel'])) {
                $print_pages_args['form_ids_not_in'] = pull_field('lead_preferences', 'db129971', 'where usergroup=' . $_SESSION['usergroup']);
            }
            $form_cms = new FrontendPages;
            $print_pages = $form_cms->get($print_pages_args);
        }


        if (request('bulk_action')) {
            $payload=array_merge(request()->all(),$prep_args);
            $payload['usergroup']=$_SESSION['usergroup'];
            $payload['uid']=$_SESSION['uid'];
            $selected_rows = [];
            if (empty(request('select_all_entries'))) {
                foreach ($_POST as $key => $value) {
                    if (strpos($key, 'table_row_') !== false) {
                        $row_id = str_replace("table_row_", "", $key);
                        $selected_rows[] = $row_id;
                    }
                }
            }
            $payload['selected_rows']=$selected_rows;
            $payload['session']=$_SESSION;
            //unset big session keys
            unset($payload['session']['permissions']);
            unset($payload['session']['unlimited_query']);
            unset($payload['session']['unlimited_query_enq']);
            unset($payload['session']['custom_filter_49']);
            ProcessBulkApplicantActions::dispatch($payload);
            $message="The action is now being processed.";
            if (request('bulk_action')==='bulk_email'){
                $url = "<p><br><br><br><a href = '" . base_url("system/email_logs/scheduled") . "' class = 'btn btn-primary thickbox' target = '_blank'>Click here to see the queued emails</a><br><br><br>Emails will be sent out automatically in 15 minutes</p>";
                $message = "<h4>Congratulations!</h4><p>Your messages are now being processed. You can check the status of your messages in the email logs.</p>" . $url;
            }
            echo json_encode(['message' => $message]);
            exit();
        }
        $data = array(
            'meta_title' => 'Applicants assigned to me',
            'custom_title' => 'Applicants assigned to me',
            'view_file' => 'applicants/index',
            'type_id' => $type_id,
            'cohort' => $this->current_cohort,
            'cohorts' => $this->cohorts,
            'results' => $results_prep,
            'paginator' => $paginator,
            'filter' => $this->filter,
            'filters_args' => $filters_args,
            'filter_page_name' => 'my_assigned_applicants',
            'active_tab' => 'assigned_to_me',
            'alert_message' => $results_prep['alert_message'],
            //'hide_export_button'=>true,
            'page_default_colums' => $df,
            'hide_send_private_messages' => true,
            'bulk_emails_offline' => true,
            'show_tabs' => true,
            'tabs_base_link' => 'applicants',
            'hide_edit_checklist' => true,
            'hide_review_media' => true,
            'back_link' => false,
            'back_link_title' => false,
            'saved_filters_dropdown' => true,
            'print_pages' => $print_pages,
            'fetchsavedprintsectionsview' => $this->fetchsavedprintsectionsview(),
            'controller' => 'applicants/assigned_to_me'

        );
        if (in_array($_SESSION['usergroup'], array(49))) {
            unset($data['hide_export_button']);
            unset($data['hide_send_private_messages']);
            unset($data['hide_edit_checklist']);
            unset($data['hide_review_media']);
            unset($data['hide_review_media']);
        }
        $this->view($this->layout, $data);

    }


    /** ===================================
     * Assigned to currently logged in user
     * ====================================  */
    public function assigned_to_me_stats($course_id = '')
    {

        $applicants = new students;
        $courses = new courses;
        $students = new Students;
        $form_templates = new FormTemplates;


        //Prep the applicants screen
        $prep_args = array(
            "extra_applicants_args" => array(
                "reviewer_id" => $_SESSION['uid'],
                "cohort" => $this->current_cohort
            ),
            "school_id" => $this->school_info['id'],
            "filter" => $this->filter,
            "filter_page_name" => "my_assigned_applicants"
        );


        $results_prep = $students->prepare_applicants_screen($prep_args);
        $this->filter = $results_prep['filter'];

        $data = array(
            'meta_title' => 'Applicants assigned to me - Stats',
            'custom_title' => 'Applicants assigned to me - Stats',
            'view_file' => 'applicants/stats',
            'type_id' => $type_id,
            "cohort" => $this->current_cohort,
            "cohorts" => $this->cohorts,
            'results' => $results_prep['results'],
            'paginator' => $paginator,
            'filter' => $this->filter,
            'filters_args' => $filters_args,
            'filter_page_name' => 'course_applicants',
            'active_tab' => 'stats',
            'alert_message' => $results_prep['alert_message'],
            'hide_export_button' => true,
            'hide_send_private_messages' => true,
            'hide_edit_checklist' => true,
            'hide_review_media' => true,
            'back_link' => false,
            'show_tabs' => true,
            'back_link_title' => false,
            'saved_filters_dropdown' => true
        );
        $this->view($this->layout, $data);

    }


    /** ===================================
     * Assigned to currently logged in user
     * ====================================  */
    public function assigned_to_me_messages($type = '')
    {

        $applicants = new students;
        $courses = new courses;
        $students = new Students;
        $form_templates = new FormTemplates;
        $users = new users;
        $messages = new CommunicationMessages;
        $hooks = new Hooks;
        $disabled_notifications = explode(',', pull_field("form_notifications_config", "db37245", "WHERE usergroup='" . $_SESSION['usergroup'] . "'"));


        //Cohort
        if ($_GET['cohort']) {
            $cohort = $this->current_cohort;
        } else {
            $cohort = $this->school_info['cohort'];
        }

        $filter_sql = $hooks->get_filter();

        /** ===================================
         * Mark Selected items as read
         * ====================================  */
        if ($_POST['action'] == "mark_selected_as_read") {
            $types = $_POST['messages_types'];
            foreach ($_POST['messages_ids'] as $key => $message) {
                $msg_args = array(
                    'id' => $message,
                    'mark_as_read' => true,
                    'school_id' => $this->school_info['id']
                );
                if ($types[$key] == 'core_notes')
                    $messages->update_or_insert_note($msg_args);
                mark_as_read_notification(array(
                    'id' => $message,
                    'type' => $types[$key]
                ));
            }

            header("Location: " . $this->base_url($this->uri_string()) . "?marking_done=1");
            exit();
        }

        $message = array();

        /** ===================================
         * Bulk Reply
         * ====================================  */
        if ($_POST['action'] == "bulk_reply") {
            foreach ($_POST['user_ids'] as $user_id) {
                $msg_args = array(
                    'message' => $_POST['bulk_message'],
                    'student_id' => $user_id,
                    'template_name' => $_POST['bulk_template_name'],
                );


                $messages->new_general_message($msg_args);
            }


            header("Location: " . $this->base_url($this->uri_string()) . "?bulk_reply_done=1");
            exit();
        }

        if ($_GET['read']) {
            $unread_messages_only = false;
        } else {
            $unread_messages_only = true;
        }

        /** ===================================
         * All message
         * ====================================  */
        if (!$type) {
            $all_tasks_replies_sql = "";
            if (!in_array('applicant_task_replies_tab', $disabled_notifications)) {

                $task_replies = array('assigned_to_me' => $_SESSION['uid'], 'no_replies' => true, 'type_id' => 9, 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'order' => $_GET['order'], 'messages_tab' => true, 'filter_sql' => $filter_sql, 'ulevel' => 'applicant', "return_sql" => true);
                $tasks_sql = $hooks->get_task_notes($task_replies);
                if (isset($_GET['is_counting'])) {
                    if (isset($_GET['mark_all_as_read'])) {
                        $noti_args = $task_replies;
                        unset($noti_args['count']);
                        unset($noti_args['paginate']);
                        unset($noti_args['return_sql']);
                        $noti = $hooks->get_task_notes($noti_args);
                        //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                        if (isset($noti[0])) {
                            foreach ($noti as $key => $note) {
                                mark_as_read_notification(['id' => $note['id'], 'type' => 'task_replies']);
                                //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                            }
                        }

                    }
                }
                if ("" == $all_tasks_replies_sql) {
                    $all_tasks_replies_sql = $tasks_sql;
                } else {
                    $all_tasks_replies_sql .= " UNION " . $tasks_sql;
                }

            }

            if (!in_array('admin_task_replies_tab', $disabled_notifications)) {

                $task_replies = array('assigned_to_me' => $_SESSION['uid'], 'no_replies' => true, 'type_id' => 9, 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'order' => $_GET['order'], 'messages_tab' => true, 'filter_sql' => $filter_sql, 'ulevel' => 'admin', "return_sql" => true);
                $tasks_sql = $hooks->get_task_notes($task_replies);
                if (isset($_GET['is_counting'])) {
                    if (isset($_GET['mark_all_as_read'])) {
                        $noti_args = $task_replies;
                        unset($noti_args['count']);
                        unset($noti_args['paginate']);
                        unset($noti_args['return_sql']);
                        $noti = $hooks->get_task_notes($noti_args);
                        //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                        if (isset($noti[0])) {
                            foreach ($noti as $key => $note) {
                                mark_as_read_notification(['id' => $note['id'], 'type' => 'task_replies']);
                                //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                            }
                        }

                    }
                }
                if ("" == $all_tasks_replies_sql) {
                    $all_tasks_replies_sql = $tasks_sql;
                } else {
                    $all_tasks_replies_sql .= " UNION " . $tasks_sql;
                }

            }


            if (!in_array('my_tasks_replies_tab', $disabled_notifications)) {
                $task_replies = array('assigned_to_me' => $_SESSION['uid'], 'tasks_assigned_to' => $_SESSION['uid'], 'no_replies' => true, 'type_id' => 9, 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'order' => $_GET['order'], 'messages_tab' => true, 'filter_sql' => $filter_sql, 'ulevel' => 'admin', "return_sql" => true);
                $tasks_sql = $hooks->get_task_notes($task_replies);
                if (isset($_GET['is_counting'])) {
                    if (isset($_GET['mark_all_as_read'])) {
                        $noti_args = $task_replies;
                        unset($noti_args['count']);
                        unset($noti_args['paginate']);
                        unset($noti_args['return_sql']);
                        $noti = $hooks->get_task_notes($noti_args);
                        //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                        if (isset($noti[0])) {
                            foreach ($noti as $key => $note) {
                                mark_as_read_notification(['id' => $note['id'], 'type' => 'task_replies']);
                                //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                            }
                        }

                    }
                }
                if ("" == $all_tasks_replies_sql) {
                    $all_tasks_replies_sql = $tasks_sql;
                } else {
                    $all_tasks_replies_sql .= " UNION " . $tasks_sql;
                }
            }

            $all_messages = array(
                'reviewer_id' => $_SESSION['uid'],
                'no_replies' => true,
                'paginate' => true,
                'unread' => $unread_messages_only,
                'school_id' => $this->school_info['id'],
                'cohort' => $this->current_cohort,
                'order' => $_GET['order'],
                'messages_tab' => true,
                'type_id_in' => '1,6,8',
                'filter_sql' => $filter_sql,
                'union_sql' => $all_tasks_replies_sql
            );
            //echo json_encode($all_messages, JSON_PRETTY_PRINT);
            if ($course_id != 'level') {
                $all_messages['course_id'] = $course_id;
            }
            $message = $messages->get($all_messages);
            if (isset($_GET['is_counting'])) {
                if (isset($_GET['mark_all_as_read'])) {
                    $noti_args = $all_messages;
                    unset($noti_args['count']);
                    unset($noti_args['paginate']);
                    unset($noti_args['return_sql']);
                    unset($noti_args['union_sql']);
                    $noti = $messages->get($noti_args);
                    //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                    if (isset($noti[0])) {
                        foreach ($noti as $key => $note) {
                            mark_as_read_notification(['id' => $note['id'], 'type' => 'core_notes']);
                            //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                        }
                    }

                }
            }

            if (isset($_GET['is_counting']) && isset($_GET['mark_all_as_read'])) {
                echo json_encode(['success' => 'done']);
                exit();

            }
        }

        /** ===================================
         * General Messages
         * ====================================  */
        if ($type == "general_questions") {
            $general_questions = array('reviewer_id' => $_SESSION['uid'], 'no_replies' => true, 'type_id_in' => '1,6', 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'cohort' => $this->current_cohort, 'order' => $_GET['order'], 'messages_tab' => true, 'filter_sql' => $filter_sql);
            if ($course_id != 'level') {
                $general_questions['course_id'] = $course_id;
            }
            $message = $messages->get($general_questions);
            if (isset($_GET['is_counting'])) {
                if (isset($_GET['mark_all_as_read'])) {
                    $noti_args = $general_questions;
                    unset($noti_args['count']);
                    unset($noti_args['paginate']);
                    $noti = $messages->get($noti_args);
                    //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                    if (isset($noti[0])) {
                        foreach ($noti as $key => $note) {
                            mark_as_read_notification(['id' => $note['id'], 'type' => 'core_notes']);
                            //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                        }
                    }

                    echo json_encode(['success' => 'done']);
                    exit();

                }
            }

        }

        /** ===================================
         * Submission Notification
         * ====================================  */
        if ($type == "submission_notifications") {
            $submission_notifications = array('reviewer_id' => $_SESSION['uid'], 'no_replies' => true, 'type_id' => 8, 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'cohort' => $this->current_cohort, 'order' => $_GET['order'], 'messages_tab' => true, 'filter_sql' => $filter_sql);
            if ($course_id != 'level') {
                $submission_notifications['course_id'] = $course_id;
            }
            $message = $messages->get($submission_notifications);

            if (isset($_GET['is_counting'])) {
                if (isset($_GET['mark_all_as_read'])) {
                    $noti_args = $submission_notifications;
                    unset($noti_args['count']);
                    unset($noti_args['paginate']);
                    $noti = $messages->get($noti_args);
                    //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                    if (isset($noti[0])) {
                        foreach ($noti as $key => $note) {
                            mark_as_read_notification(['id' => $note['id'], 'type' => 'core_notes']);
                            //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                        }
                    }

                    echo json_encode(['success' => 'done']);
                    exit();

                }
            }
        }

        /** ===================================
         * File Upload Notifications
         * ====================================  */
        if ($type == "applicant_task_replies") {
            $applicant_task_replies = array('assigned_to_me' => $_SESSION['uid'], 'no_replies' => true, 'type_id' => 9, 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'order' => $_GET['order'], 'messages_tab' => true, 'filter_sql' => $filter_sql, 'ulevel' => 'applicant');
            $message = $hooks->get_task_notes($applicant_task_replies);

            if (isset($_GET['is_counting'])) {
                if (isset($_GET['mark_all_as_read'])) {
                    $noti_args = $applicant_task_replies;
                    unset($noti_args['count']);
                    unset($noti_args['paginate']);
                    $noti = $hooks->get_task_notes($noti_args);
                    //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                    if (isset($noti[0])) {
                        foreach ($noti as $key => $note) {
                            mark_as_read_notification(['id' => $note['id'], 'type' => 'applicant_task_replies']);
                            //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                        }
                    }

                    echo json_encode(['success' => 'done']);
                    exit();

                }
            }


        }

        if ($type == "admin_task_replies") {
            $admin_task_replies = array('assigned_to_me' => $_SESSION['uid'], 'no_replies' => true, 'type_id' => 9, 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'order' => $_GET['order'], 'messages_tab' => true, 'filter_sql' => $filter_sql, 'ulevel' => 'admin');
            $message = $hooks->get_task_notes($admin_task_replies);

            if (isset($_GET['is_counting'])) {
                if (isset($_GET['mark_all_as_read'])) {
                    $noti_args = $admin_task_replies;
                    unset($noti_args['count']);
                    unset($noti_args['paginate']);
                    $noti = $hooks->get_task_notes($noti_args);
                    //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                    if (isset($noti[0])) {
                        foreach ($noti as $key => $note) {
                            mark_as_read_notification(['id' => $note['id'], 'type' => 'admin_task_replies']);
                            //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                        }
                    }

                    echo json_encode(['success' => 'done']);
                    exit();

                }
            }
        }

        if ($type == "my_tasks_replies") {
            $my_tasks_replies = array('assigned_to_me' => $_SESSION['uid'], 'tasks_assigned_to' => $_SESSION['uid'], 'no_replies' => true, 'type_id' => 9, 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'order' => $_GET['order'], 'messages_tab' => true, 'filter_sql' => $filter_sql, 'ulevel' => 'admin');
            $message = $hooks->get_task_notes($my_tasks_replies);
            if (isset($_GET['is_counting'])) {
                if (isset($_GET['mark_all_as_read'])) {
                    $noti_args = $my_tasks_replies;
                    unset($noti_args['count']);
                    unset($noti_args['paginate']);
                    $noti = $hooks->get_task_notes($noti_args);
                    //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                    if (isset($noti[0])) {
                        foreach ($noti as $key => $note) {
                            mark_as_read_notification(['id' => $note['id'], 'type' => 'my_tasks_replies']);
                            //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                        }
                    }

                    echo json_encode(['success' => 'done']);
                    exit();

                }
            }

        }


        if ($type == 'json_count') {
            if (strtotime("now") > $_SESSION['notifications']['messages']['expiry'] || !empty($_GET['fetch_updates'])) {
                // $applicant_task_replies = array('assigned_to_me'=>$_SESSION['uid'],'no_replies'=>true,'type_id'=>9,'school_id'=>$this->school_info['id'],'messages_tab' => true, 'count' => true,'unread'=> true,'filter_sql'=>$filter_sql);
                // $applicant_task_replies_count = $hooks->get_task_notes($applicant_task_replies);
                // $admin_task_replies = array('assigned_to_me'=>$_SESSION['uid'],'no_replies'=>true,'type_id'=>9,'school_id'=>$this->school_info['id'],'messages_tab' => true, 'count' => true,'unread'=> true,'filter_sql'=>$filter_sql);
                // $admin_task_replies_count = $hooks->get_task_notes($admin_task_replies);
                // $message = ((int) $messages->get(array('reviewer_id'=> $_SESSION['uid'] ,'no_replies'=>true,'count'=>true,'unread'=> true ,'school_id'=>$this->school_info['id'] ,'messages_tab' => true,'type_id_in' => '1,6,8','filter_sql'=>$filter_sql)) + $applicant_task_replies[0]['count']+ $admin_task_replies_count[0]['count']);

                $message_int = 0;

                if (!in_array('applicant_task_replies_tab', $disabled_notifications)) {

                    $task_replies = array('assigned_to_me' => $_SESSION['uid'], 'no_replies' => true, 'type_id' => 9, 'count' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'messages_tab' => true, 'filter_sql' => $filter_sql, 'ulevel' => 'applicant');
                    $tasks_sql = $hooks->get_task_notes($task_replies);
                    $message_int = $message_int + $tasks_sql[0]['count'];

                }

                if (!in_array('admin_task_replies_tab', $disabled_notifications)) {

                    $task_replies = array('assigned_to_me' => $_SESSION['uid'], 'no_replies' => true, 'type_id' => 9, 'count' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'messages_tab' => true, 'filter_sql' => $filter_sql, 'ulevel' => 'admin');
                    $tasks_sql = $hooks->get_task_notes($task_replies);

                    $message_int = $message_int + $tasks_sql[0]['count'];

                }


                if (!in_array('my_tasks_replies_tab', $disabled_notifications)) {
                    $task_replies = array('assigned_to_me' => $_SESSION['uid'], 'tasks_assigned_to' => $_SESSION['uid'], 'no_replies' => true, 'type_id' => 9, 'count' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'messages_tab' => true, 'filter_sql' => $filter_sql, 'ulevel' => 'admin');
                    $tasks_sql = $hooks->get_task_notes($task_replies);
                    $message_int = $message_int + $tasks_sql[0]['count'];

                }

                $all_messages = array('reviewer_id' => $_SESSION['uid'], 'no_replies' => true, 'count' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'cohort' => $this->current_cohort, 'order' => $_GET['order'], 'messages_tab' => true, 'type_id_in' => '1,6,8', 'filter_sql' => $filter_sql);
                $message_int = (int)$messages->get($all_messages) + $message_int;


                $array = array(
                    'success' => true,
                    'data' => $message_int,
                    'method' => __FUNCTION__
                );

                $_SESSION['notifications']['messages']['value'] = $array['data'];
                $_SESSION['notifications']['messages']['expiry'] = strtotime("+30 seconds");
                echo json_encode($array);
            } else {

                $array = array(
                    'success' => true,
                    'data' => $_SESSION['notifications']['messages']['value'],
                    'method' => __FUNCTION__
                );

                echo json_encode($array);

            }

            exit();
        }

        $view = 'applicants/messages';
        $data = array(
            'meta_title' => 'Applicants assigned to me - Messages',
            'custom_title' => 'Applicants assigned to me - Messages',
            'view_file' => $view,
            'results' => $message,
            'type' => $type,
            'paginator' => $paginator,
            'read' => $unread_messages_only,
            'filter' => $this->filter,
            'filters_args' => $filters_args,
            'filter_page_name' => 'course_applicants',
            'active_tab' => 'messages',
            'alert_message' => $results_prep['alert_message'],
            'hide_export_button' => true,
            'hide_send_private_messages' => true,
            'hide_edit_checklist' => true,
            'hide_review_media' => true,
            'back_link' => false,
            'show_tabs' => true,
            'back_link_title' => false,
            'saved_filters_dropdown' => true
        );
        $this->view($this->layout, $data);

    }


    public function assigned_to_me_messages_count($type = '')
    {
        $min = 0;
        $max = 0;
        $disabled_notifications = explode(',', pull_field("form_notifications_config", "db37245", "WHERE usergroup='" . $_SESSION['usergroup'] . "'"));

        //Cohort
        if ($_GET['cohort']) {
            $cohort = $this->current_cohort;
        } else {
            $cohort = $this->school_info['cohort'];
        }

        if ($_GET['read']) {
            $unread_messages_only = false;
        } else {
            $unread_messages_only = true;
        }

        // echo "type :".$type."<br>";
        // echo "expiry :".$_SESSION['notifications']['messages']['expiry'] ."<br>";
        // echo "now  :".strtotime('now')."<br>";

        if ($type == 'json_count' && $_SESSION['notifications']['messages']['expiry'] > strtotime('now') && empty($_GET['fetch_updates'])) {

            //$count=$data['all_messages_count'];

            $array = array(
                "fromsess" => true,
                'success' => true,
                'data' => $_SESSION['notifications']['messages']['value'],
                'method' => __FUNCTION__
            );


            echo json_encode($array);
            exit();
        }


        $messages = new CommunicationMessages;

        $hooks = new Hooks;

        $filter_sql = $hooks->get_filter();
        if (!in_array('applicant_task_replies_tab', $disabled_notifications)) {
            $applicant_task_replies = array('assigned_to_me' => $_SESSION['uid'], 'no_replies' => true, 'type_id' => 9, 'school_id' => $this->school_info['id'], 'messages_tab' => true, 'count' => true, 'unread' => $unread_messages_only, 'filter_sql' => $filter_sql, 'ulevel' => 'applicant');
            $applicant_task_replies_count = $hooks->get_task_notes($applicant_task_replies);
            $data['applicant_task_replies_count'] = $applicant_task_replies_count[0]['count'];

            if ($unread_messages_only) {
                $min = $data['applicant_task_replies_count'];
                $max = $data['applicant_task_replies_count'];
            }
        }

        if (!in_array('admin_task_replies_tab', $disabled_notifications)) {
            $admin_task_replies = array('assigned_to_me' => $_SESSION['uid'], 'no_replies' => true, 'type_id' => 9, 'school_id' => $this->school_info['id'], 'messages_tab' => true, 'count' => true, 'unread' => $unread_messages_only, 'filter_sql' => $filter_sql, 'ulevel' => 'admin');
            $admin_task_replies_count = $hooks->get_task_notes($admin_task_replies);


            $data['admin_task_replies_count'] = $admin_task_replies_count[0]['count'];

            if ($unread_messages_only) {
                $min = $data['admin_task_replies_count'] < $min ? $data['admin_task_replies_count'] : $min;
                $max = $data['admin_task_replies_count'] > $max ? $data['admin_task_replies_count'] : $max;
            }
        }

        if (!in_array('my_tasks_replies_tab', $disabled_notifications)) {
            $my_tasks_replies = array('assigned_to_me' => $_SESSION['uid'], 'tasks_assigned_to' => $_SESSION['uid'], 'no_replies' => true, 'type_id' => 9, 'school_id' => $this->school_info['id'], 'messages_tab' => true, 'count' => true, 'unread' => $unread_messages_only, 'filter_sql' => $filter_sql, 'ulevel' => 'admin');
            $my_tasks_replies_count = $hooks->get_task_notes($my_tasks_replies);

            $data['my_tasks_replies_count'] = $my_tasks_replies_count[0]['count'];

            if ($unread_messages_only) {
                $min = $data['my_tasks_replies_count'] < $min ? $data['my_tasks_replies_count'] : $min;
                $max = $data['my_tasks_replies_count'] > $max ? $data['my_tasks_replies_count'] : $max;
            }
        }


        if (!in_array('all_messages_tab', $disabled_notifications)) {
            $allMessagesArgs = array(
                'reviewer_id' => $_SESSION['uid'],
                'no_replies' => true,
                'count' => true,
                'unread' => $unread_messages_only,
                'school_id' => $this->school_info['id'],
                'messages_tab' => true,
                'type_id_in' => '1,6,8',
                'filter_sql' => $filter_sql,
                'messagesCount' => true
            );
            $data['all_messages_count'] = ((int)$messages->get($allMessagesArgs) + $admin_task_replies_count[0]['count'] + $applicant_task_replies_count[0]['count'] + $my_tasks_replies_count[0]['count']);

            if ($unread_messages_only) {
                $min = $data['all_messages_count'] < $min ? $data['all_messages_count'] : $min;
                $max = $data['all_messages_count'] > $max ? $data['all_messages_count'] : $max;
            }
        }

        if (!in_array('general_questions_tab', $disabled_notifications)) {
            $generalMessagesCountArgs = array(
                'reviewer_id' => $_SESSION['uid'],
                'messages_tab' => true,
                'no_replies' => true,
                'type_id_in' => '1,6',
                'count' => true,
                'unread' => $unread_messages_only,
                'school_id' => $this->school_info['id'],
                'filter_sql' => $filter_sql,
                'messagesCount' => true
            );
            $data['general_messages_count'] = $messages->get($generalMessagesCountArgs);

            if ($unread_messages_only) {
                $min = $data['general_messages_count'] < $min ? $data['general_messages_count'] : $min;
                $max = $data['general_messages_count'] > $max ? $data['general_messages_count'] : $max;
            }
        }

        if (!in_array('submission_notifications_tab', $disabled_notifications)) {
            $submissionNotificationsTabArgs = array(
                'reviewer_id' => $_SESSION['uid'],
                'messages_tab' => true,
                'no_replies' => true,
                'type_id' => 8,
                'count' => true,
                'unread' => $unread_messages_only,
                'school_id' => $this->school_info['id'],
                'filter_sql' => $filter_sql,
                'messagesCount' => true
            );
            $data['submission_notifications_count'] = $messages->get($submissionNotificationsTabArgs);

            if ($unread_messages_only) {
                $min = $data['submission_notifications_count'] < $min ? $data['submission_notifications_count'] : $min;
                $max = $data['submission_notifications_count'] > $max ? $data['submission_notifications_count'] : $max;
            }
        }


        if ($unread_messages_only) {
            $data['applicant_task_replies_css'] = numberToColorHsl($max - $data['applicant_task_replies_count'], $min, $max);
            if (!in_array('my_tasks_replies_tab', $disabled_notifications)) {
                $data['my_tasks_replies_css'] = numberToColorHsl($max - $data['my_tasks_replies_count'], $min, $max);
            }
            if (!in_array('admin_task_replies_tab', $disabled_notifications)) {
                $data['admin_task_replies_css'] = numberToColorHsl($max - $data['admin_task_replies_count'], $min, $max);
            }
            if (!in_array('all_messages_tab', $disabled_notifications)) {
                $data['all_messages_css'] = numberToColorHsl($max - $data['all_messages_count'], $min, $max);
            }
            if (!in_array('general_questions_tab', $disabled_notifications)) {
                $data['general_messages_css'] = numberToColorHsl($max - $data['general_messages_count'], $min, $max);
            }
            if (!in_array('submission_notifications_tab', $disabled_notifications)) {
                $data['submission_notifications_css'] = numberToColorHsl($max - $data['submission_notifications_count'], $min, $max);
            }

        }


        if ($type == 'json_count') {

            $count = $data['all_messages_count'];

            $array = array(
                'success' => true,
                'data' => $count,
                'method' => __FUNCTION__
            );

            $_SESSION['notifications']['messages']['value'] = $array['data'];
            $_SESSION['notifications']['messages']['expiry'] = strtotime("+30 seconds");
            echo json_encode($array);
            exit();
        }
        echo json_encode($data);

    }

    /** ===================================
     * Assigned to currently logged in user
     * ====================================  */
    public function assigned_to_me_notifications($type = 'priority')
    {
        $applicants = new students;
        $courses = new courses;
        $students = new Students;
        $form_templates = new FormTemplates;
        $users = new users;
        $hooks = new Hooks;
        $messages = new CommunicationMessages;
        $files = new FilesClass;
        $e_chats = new enq_chats;
        $payments = new PaymentsModel;
        $disabled_notifications = explode(',', pull_field("form_notifications_config", "db37245", "WHERE usergroup='" . $_SESSION['usergroup'] . "'"));

        //Cohort
        if ($_GET['cohort']) {
            $cohort = $_GET['cohort'];
        } else {
            $cohort = $this->school_info['cohort'];
        }

        if ($cohort == 'all_cohorts') {
            $cohort = '';
        }

        if (isset($_GET['is_counting']) || $type == 'json_count') {
            $is_counting = true;
        } else {
            $is_counting = false;
        }


        if ($is_counting && $_SESSION['notifications']['notifications']['expiry'] > strtotime('now') && empty($_GET['fetch_updates'])) {
            $array = array(
                "fromsess" => true,
                'success' => true,
                'data' => $_SESSION['notifications']['notifications']['value'],
                'method' => __FUNCTION__,
                'infos' => $_SESSION['notifications']['notifications']['infos'] = $array['infos']
            );

            // $_SESSION['notifications']['notifications']['value'] = $array['data'];
            // $_SESSION['notifications']['notifications']['expiry'] = strtotime("+30 seconds");
            if ($type == 'json_count') {
                echo json_encode($array);
                exit();
            }
        }


        $filter_sql = $hooks->get_filter();

        /** ===================================
         * Mark Selected items as read
         * ====================================  */
        if ($_POST['action'] == "mark_selected_as_read") {
            foreach ($_POST['messages_ids'] as $message) {
                $msg_args = array(
                    'id' => $message,
                    'mark_as_read' => true,
                    'school_id' => $this->school_info['id']
                );
                $messages->update_or_insert_note($msg_args);
                mark_as_read_notification(array(
                    'id' => $message,
                    'type' => $type
                ));
            }

            header("Location: " . $this->base_url($this->uri_string()) . "?marking_done=1");
            exit();
        }
        /** ===================================
         * Bulk Reply
         * ====================================  */
        if ($_POST['action'] == "bulk_reply") {
            foreach ($_POST['user_ids'] as $user_id) {
                $msg_args = array(
                    'message' => $_POST['bulk_message'],
                    'student_id' => $user_id,
                    'template_name' => $_POST['bulk_template_name'],
                );

                $messages->new_general_message($msg_args);
            }
            header("Location: " . $this->base_url($this->uri_string()) . "?bulk_reply_done=1");
            exit();
        }

        if ($_GET['read']) {
            $unread_messages_only = false;
        } else {
            $unread_messages_only = true;
        }


        if ($_GET['order']) {
            $order = "date " . $_GET['order'];
        } else {
            $order = "date ASC";
        }

        /** ===================================
         * Tagged Internal Notes
         * ====================================  */
        if (!in_array('tagged_internal_notes_tab', $disabled_notifications)) {
            if ($type == "tagged" && !$is_counting) {
                $general_questions = array('reviewer_id' => $_SESSION['uid'], 'no_replies' => true, 'type_id' => 1, 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'notifications' => true, 'order' => $order, 'filter_sql' => $filter_sql);
                $message = $messages->get_internal_notes($general_questions);
            }

            if ($is_counting) {
                $tag_args = array('reviewer_id' => $_SESSION['uid'], 'no_replies' => true, 'type_id' => 1, 'notifications' => true, 'count' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'filter_sql' => $filter_sql);
                if ($type == "tagged" && isset($_GET['mark_all_as_read'])) {
                    $noti_args = $tag_args;
                    unset($noti_args['count']);
                    $noti = $messages->get_internal_notes($noti_args);
                    //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                    if (isset($noti[0])) {
                        foreach ($noti as $key => $note) {
                            mark_as_read_notification(['id' => $note['id'], 'type' => $type]);
                            //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                        }
                    }

                    echo json_encode(['success' => 'done']);
                    exit();
                }
                $tag_count = $messages->get_internal_notes($tag_args);
            }
        }


        /** ===================================
         * Submission Notification
         * ====================================  */
        if (!in_array('upcoming_tasks_tab', $disabled_notifications)) {
            if ($type == "reminders" && !$is_counting) {
                $reminders = array('active_tasks' => true, 'notifications_display' => true, 'assigned_to_me' => $_SESSION['uid'], 'due_by' => 'soon', 'no_replies' => true, 'type_id' => 8, 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'notifications' => true, 'order' => $order, 'filter_sql' => $filter_sql);
                $message = $hooks->get_tasks($reminders);
            }
            if ($is_counting) {
                $reminders_count_args = array('active_tasks' => true, 'assigned_to_me' => $_SESSION['uid'], 'notifications' => true, 'due_by' => 'soon', 'no_replies' => true, 'type_id' => 8, 'count' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'filter_sql' => $filter_sql);
                if ($type == "reminders" && isset($_GET['mark_all_as_read'])) {
                    $noti_args = $reminders_count_args;
                    unset($noti_args['count']);
                    $noti = $hooks->get_tasks($noti_args);
                    //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                    if (isset($noti[0])) {
                        foreach ($noti as $key => $note) {
                            mark_as_read_notification(['id' => $note['id'], 'type' => $type]);
                            //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                        }
                    }

                    echo json_encode(['success' => 'done']);
                    exit();
                }
                $reminders_count = $hooks->get_tasks($reminders_count_args);
            }
        }

        if (!in_array('priority_notifications_tab', $disabled_notifications)) {
            if ($type == "priority" && !$is_counting) {
                $reminders_p = array('active_tasks' => true, 'notifications_display' => true, 'assigned_to_me' => $_SESSION['uid'], 'notifications' => true, 'due_by' => 'today', 'no_replies' => true, 'type_id' => 8, 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'order' => $order, 'filter_sql' => $filter_sql);
                $message = $hooks->get_tasks($reminders_p);
            }

            if ($is_counting) {
                $reminders_p_count_args = array('active_tasks' => true, 'assigned_to_me' => $_SESSION['uid'], 'notifications' => true, 'due_by' => 'today', 'no_replies' => true, 'type_id' => 8, 'count' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'filter_sql' => $filter_sql);
                if ($type == "priority" && isset($_GET['mark_all_as_read'])) {
                    $noti_args = $reminders_p_count_args;
                    unset($noti_args['count']);
                    $noti = $hooks->get_tasks($noti_args);
                    //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                    if (isset($noti[0])) {
                        foreach ($noti as $key => $note) {
                            mark_as_read_notification(['id' => $note['id'], 'type' => $type]);
                            //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                        }
                    }

                    echo json_encode(['success' => 'done']);
                    exit();
                }
                $reminders_p_count = $hooks->get_tasks($reminders_p_count_args);
            }
        }


        /**===================================
         * Enquiry Chat Notifications
         * ====================================  */
        /*if($type=="enquirychat"){
			$e_args = array('assigned_to_me'=>$_SESSION['uid'], 'notifications'=> true,'due_by'=>'today','no_replies'=>true,'type_id'=>8,'paginate'=>true,'unread'=>$unread_messages_only,'school_id'=>$this->school_info['id'],'order' => $order);
			$message = $e_chats->get_notifications($e_args);
		}
		$e_chats_count_args = array('assigned_to_me'=>$_SESSION['uid'], 'notifications'=> true,'due_by'=>'today','no_replies'=>true,'type_id'=>8,'count'=>true,'unread'=>$unread_messages_only, 'school_id'=>$this->school_info['id']);
		$e_chats_count = $e_chats->get_notifications($e_chats_count_args);*/


        /** ===================================
         * File Upload Notifications
         * ====================================  */
        if (!in_array('general_notifications_tab', $disabled_notifications)) {
            if ($type == "general" && !$is_counting) {
                $file_upload_notifications = array('reviewer_id' => $_SESSION['uid'], 'notifications' => true, 'no_replies' => true, 'type_id' => 9, 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'order' => $order, 'filter_sql' => $filter_sql);
                $message = $files->get_file_notifications($file_upload_notifications);
            }


            if ($is_counting) {
                $file_upload_count = array('reviewer_id' => $_SESSION['uid'], 'notifications' => true, 'no_replies' => true, 'type_id' => 9, 'count' => true, 'school_id' => $this->school_info['id'], 'unread' => $unread_messages_only, 'filter_sql' => $filter_sql);
                if ($type == "general" && isset($_GET['mark_all_as_read'])) {
                    $noti_args = $file_upload_count;
                    unset($noti_args['count']);
                    $noti = $files->get_file_notifications($noti_args);
                    //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                    if (isset($noti[0])) {
                        foreach ($noti as $key => $note) {
                            mark_as_read_notification(['id' => $note['id'], 'type' => $type]);
                            //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                        }
                    }

                    echo json_encode(['success' => 'done']);
                    exit();
                }
                $files_count = $files->get_file_notifications($file_upload_count);
            }
        }


        /** ===================================
         * Payment Notifications
         * ====================================  */
        if (!in_array('payment_notifications_tab', $disabled_notifications)) {
            if ($type == "payment" && !$is_counting) {
                $payment_notifications = array('reviewer_id' => $_SESSION['uid'], 'notifications' => true, 'no_replies' => true, 'type_id' => 10, 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'order' => $order, 'filter_sql' => $filter_sql);
                $message = $payments->get_payment_notifications($payment_notifications);
            }

            if ($is_counting) {
                $payment_count_args = array('reviewer_id' => $_SESSION['uid'], 'notifications' => true, 'no_replies' => true, 'type_id' => 10, 'count' => true, 'school_id' => $this->school_info['id'], 'unread' => $unread_messages_only, 'filter_sql' => $filter_sql);
                if ($type == "payment" && isset($_GET['mark_all_as_read'])) {
                    $noti_args = $payment_count_args;
                    unset($noti_args['count']);
                    $noti = $payments->get_payment_notifications($noti_args);
                    //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                    if (isset($noti[0])) {
                        foreach ($noti as $key => $note) {
                            mark_as_read_notification(['id' => $note['id'], 'type' => $type]);
                            //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                        }
                    }

                    echo json_encode(['success' => 'done']);
                    exit();
                }
                $payment_count = $payments->get_payment_notifications($payment_count_args);
            }
        }


        if (!in_array('applicant_tasks_due_today_tab', $disabled_notifications)) {
            if ($type == "applicant_tasks_due_today" && !$is_counting) {
                $applicant_tasks_due_today_p = array('active_tasks' => true, 'notifications_display' => true, 'for_applicants_assigned_to_me' => $_SESSION['uid'], 'notifications' => true, 'due_by' => 'this_day', 'no_replies' => true, 'type_id' => 8, 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'order' => $order, 'filter_sql' => $filter_sql);
                $message = $hooks->get_tasks($applicant_tasks_due_today_p);
            }

            if ($is_counting) {
                $applicant_tasks_due_today_p_count_args = array('active_tasks' => true, 'for_applicants_assigned_to_me' => $_SESSION['uid'], 'notifications' => true, 'due_by' => 'this_day', 'no_replies' => true, 'type_id' => 8, 'count' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'filter_sql' => $filter_sql);
                if ($type == "applicant_tasks_due_today" && isset($_GET['mark_all_as_read'])) {
                    $noti_args = $applicant_tasks_due_today_p_count_args;
                    unset($noti_args['count']);
                    $noti = $hooks->get_tasks($noti_args);
                    //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                    if (isset($noti[0])) {
                        foreach ($noti as $key => $note) {
                            mark_as_read_notification(['id' => $note['id'], 'type' => $type]);
                            //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                        }
                    }

                    echo json_encode(['success' => 'done']);
                    exit();
                }
                $applicant_tasks_due_today_count = $hooks->get_tasks($applicant_tasks_due_today_p_count_args);
            }
        }

        if (!in_array('applicant_tasks_due_tommorrow_tab', $disabled_notifications)) {
            if ($type == "applicant_tasks_due_tommorrow" && !$is_counting) {
                $applicant_tasks_due_tommorrow_p = array('active_tasks' => true, 'notifications_display' => true, 'for_applicants_assigned_to_me' => $_SESSION['uid'], 'notifications' => true, 'due_by' => 'soon', 'no_replies' => true, 'type_id' => 8, 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'order' => $order, 'filter_sql' => $filter_sql);
                $message = $hooks->get_tasks($applicant_tasks_due_tommorrow_p);
            }

            if ($is_counting) {
                $applicant_tasks_due_tommorrow_p_count_args = array('active_tasks' => true, 'for_applicants_assigned_to_me' => $_SESSION['uid'], 'notifications' => true, 'due_by' => 'soon', 'no_replies' => true, 'type_id' => 8, 'count' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'filter_sql' => $filter_sql);
                if ($type == "applicant_tasks_due_tommorrow" && isset($_GET['mark_all_as_read'])) {
                    $noti_args = $applicant_tasks_due_tommorrow_p_count_args;
                    unset($noti_args['count']);
                    $noti = $hooks->get_tasks($noti_args);
                    //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                    if (isset($noti[0])) {
                        foreach ($noti as $key => $note) {
                            mark_as_read_notification(['id' => $note['id'], 'type' => $type]);
                            //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                        }
                    }

                    echo json_encode(['success' => 'done']);
                    exit();
                }
                $applicant_tasks_due_tommorrow_count = $hooks->get_tasks($applicant_tasks_due_tommorrow_p_count_args);
            }
        }

        if (!in_array('applicant_tasks_over_due_tab', $disabled_notifications)) {
            if ($type == "applicant_tasks_over_due" && !$is_counting) {
                $applicant_tasks_over_due_p = array('active_tasks' => true, 'notifications_display' => true, 'for_applicants_assigned_to_me' => $_SESSION['uid'], 'notifications' => true, 'due_by' => 'over_due', 'no_replies' => true, 'type_id' => 8, 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'order' => $order, 'filter_sql' => $filter_sql);
                $message = $hooks->get_tasks($applicant_tasks_over_due_p);
            }

            if ($is_counting) {
                $applicant_tasks_over_due_p_count_args = array('active_tasks' => true, 'for_applicants_assigned_to_me' => $_SESSION['uid'], 'notifications' => true, 'due_by' => 'over_due', 'no_replies' => true, 'type_id' => 8, 'count' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'filter_sql' => $filter_sql);
                if ($type == "applicant_tasks_over_due" && isset($_GET['mark_all_as_read'])) {
                    $noti_args = $applicant_tasks_over_due_p_count_args;
                    unset($noti_args['count']);
                    $noti = $hooks->get_tasks($noti_args);
                    //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                    if (isset($noti[0])) {
                        foreach ($noti as $key => $note) {
                            mark_as_read_notification(['id' => $note['id'], 'type' => $type]);
                            //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                        }
                    }

                    echo json_encode(['success' => 'done']);
                    exit();
                }
                $applicant_tasks_over_due_count = $hooks->get_tasks($applicant_tasks_over_due_p_count_args);
            }
        }

        if (!in_array('admin_task_replies_notifications_tab', $disabled_notifications)) {

            $admin_task_replies = array('assigned_to_me' => $_SESSION['uid'], 'no_replies' => true, 'type_id' => 9, 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'order' => $_GET['order'], 'messages_tab' => true, 'filter_sql' => $filter_sql, 'ulevel' => 'admin');
            if ($type == "admin_task_replies" && !$is_counting) {
                $message = $hooks->get_task_notes($admin_task_replies);
            }

            if ($is_counting) {
                $admin_task_replies_count_args = $admin_task_replies;
                $admin_task_replies_count_args['count'] = true;
                if ($type == "admin_task_replies" && isset($_GET['mark_all_as_read'])) {
                    $noti_args = $admin_task_replies_count_args;
                    unset($noti_args['count']);
                    $noti = $hooks->get_task_notes($noti_args);
                    //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                    if (isset($noti[0])) {
                        foreach ($noti as $key => $note) {
                            mark_as_read_notification(['id' => $note['id'], 'type' => $type]);
                            //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                        }
                    }

                    echo json_encode(['success' => 'done']);
                    exit();
                }
                $admin_task_replies_count = $hooks->get_task_notes($admin_task_replies_count_args);
            }

        }

        if (!in_array('my_tasks_replies_notifications_tab', $disabled_notifications)) {

            $my_tasks_replies = array('tasks_assigned_to' => $_SESSION['uid'], 'no_replies' => true, 'type_id' => 9, 'paginate' => true, 'unread' => $unread_messages_only, 'school_id' => $this->school_info['id'], 'order' => $_GET['order'], 'filter_sql' => $filter_sql, 'ulevel' => 'admin');
            if ($type == "my_tasks_replies" && !$is_counting) {
                $message = $hooks->get_task_notes($my_tasks_replies);
            }

            if ($is_counting) {
                $my_tasks_replies_count_args = $my_tasks_replies;
                $my_tasks_replies_count_args['count'] = true;
                if ($type == "my_tasks_replies" && isset($_GET['mark_all_as_read'])) {
                    $noti_args = $my_tasks_replies_count_args;
                    unset($noti_args['count']);
                    $noti = $hooks->get_task_notes($noti_args);
                    //echo "<pre>".print_r($noti[0],1)."</pre>";exit();
                    if (isset($noti[0])) {
                        foreach ($noti as $key => $note) {
                            mark_as_read_notification(['id' => $note['id'], 'type' => $type]);
                            //echo json_encode(['success'=>'done' ,'note'=>$note]);exit();
                        }
                    }

                    echo json_encode(['success' => 'done']);
                    exit();
                }
                $my_tasks_replies_count = $hooks->get_task_notes($my_tasks_replies_count_args);
            }


        }


        if ($is_counting) {
            $array = array(
                'success' => true,
                'data' => ($files_count[0]['count'] + $reminders_count[0]['count'] + $reminders_p_count[0]['count'] + $tag_count[0]['count'] + $payment_count[0]['count'] + $applicant_tasks_due_today_count[0]['count'] + $applicant_tasks_due_tommorrow_count[0]['count'] + $applicant_tasks_over_due_count[0]['count'] + $admin_task_replies_count[0]['count'] + $my_tasks_replies_count[0]['count']),
                'method' => __FUNCTION__,
                'infos' => [
                    'files_count' => $files_count,
                    'reminders_count' => $reminders_count,
                    'reminders_p_count' => $reminders_p_count,
                    'tag_count' => $tag_count,
                    'payment_count' => $payment_count,
                    'applicant_tasks_due_today_count' => $applicant_tasks_due_today_count,
                    'applicant_tasks_due_tommorrow_count' => $applicant_tasks_due_tommorrow_count,
                    'applicant_tasks_over_due_count' => $applicant_tasks_over_due_count,
                    'admin_task_replies_count' => $admin_task_replies_count,
                    'my_tasks_replies_count' => $my_tasks_replies_count,
                ]
            );
            $_SESSION['notifications']['notifications']['value'] = $array['data'];
            $_SESSION['notifications']['notifications']['infos'] = $array['infos'];
            $_SESSION['notifications']['notifications']['expiry'] = strtotime("+30 seconds");

            // $_SESSION['notifications']['notifications']['value'] = $array['data'];
            // $_SESSION['notifications']['notifications']['expiry'] = strtotime("+30 seconds");
            if ($type == 'json_count') {
                echo json_encode($array);
                exit();
            }
            $data = [];

            $data = array(
                //'file_upload_count' => $files_count[0]['count'],
                //'reminders_count' => $reminders_count[0]['count'],
                //'reminders_p_count' => $reminders_p_count[0]['count'],
                //'applicant_tasks_due_today_count'=>$applicant_tasks_due_today_count[0]['count'],
                // 'applicant_tasks_due_tommorrow_count'=>$applicant_tasks_due_tommorrow_count[0]['count'],
                // 'applicant_tasks_over_due_count'=>$applicant_tasks_over_due_count[0]['count'],
                // 'tag_count' => $tag_count[0]['count'],
                // 'payment_count'=>$payment_count[0]['count'],
                // 'all_messages_count'=>0,
            );
            $min = 0;
            $max = 0;
            if (!in_array('general_notifications_tab', $disabled_notifications)) {
                $data['file_upload_count'] = $files_count[0]['count'];
                $min = $data['file_upload_count'];
                $max = $data['file_upload_count'];
            }

            if (!in_array('upcoming_tasks_tab', $disabled_notifications)) {
                $data['reminders_count'] = $reminders_count[0]['count'];
                $min = $data['reminders_count'] < $min ? $data['reminders_count'] : $min;
                $max = $data['reminders_count'] > $max ? $data['reminders_count'] : $max;
            }

            if (!in_array('priority_notifications_tab', $disabled_notifications)) {
                $data['reminders_p_count'] = $reminders_p_count[0]['count'];
                $min = $data['reminders_p_count'] < $min ? $data['reminders_p_count'] : $min;
                $max = $data['reminders_p_count'] > $max ? $data['reminders_p_count'] : $max;
            }

            if (!in_array('applicant_tasks_due_today_tab', $disabled_notifications)) {
                $data['applicant_tasks_due_today_count'] = $applicant_tasks_due_today_count[0]['count'];
                $min = $data['applicant_tasks_due_today_count'] < $min ? $data['applicant_tasks_due_today_count'] : $min;
                $max = $data['applicant_tasks_due_today_count'] > $max ? $data['applicant_tasks_due_today_count'] : $max;
            }
            if (!in_array('applicant_tasks_due_tommorrow_tab', $disabled_notifications)) {
                $data['applicant_tasks_due_tommorrow_count'] = $applicant_tasks_due_tommorrow_count[0]['count'];
                $min = $data['applicant_tasks_due_tommorrow_count'] < $min ? $data['applicant_tasks_due_tommorrow_count'] : $min;
                $max = $data['applicant_tasks_due_tommorrow_count'] > $max ? $data['applicant_tasks_due_tommorrow_count'] : $max;
            }

            if (!in_array('applicant_tasks_over_due_count_tab', $disabled_notifications)) {
                $data['applicant_tasks_over_due_count'] = $applicant_tasks_over_due_count[0]['count'];
                $min = $data['applicant_tasks_over_due_count'] < $min ? $data['applicant_tasks_over_due_count'] : $min;
                $max = $data['applicant_tasks_over_due_count'] > $max ? $data['applicant_tasks_over_due_count'] : $max;
            }

            if (!in_array('admin_task_replies_notifications_tab', $disabled_notifications)) {
                $data['admin_task_replies_count'] = $admin_task_replies_count[0]['count'];
                $min = $data['admin_task_replies_count'] < $min ? $data['admin_task_replies_count'] : $min;
                $max = $data['admin_task_replies_count'] > $max ? $data['admin_task_replies_count'] : $max;
            }

            if (!in_array('my_tasks_replies_notifications_tab', $disabled_notifications)) {
                $data['my_tasks_replies_count'] = $my_tasks_replies_count[0]['count'];
                $min = $data['my_tasks_replies_count'] < $min ? $data['my_tasks_replies_count'] : $min;
                $max = $data['my_tasks_replies_count'] > $max ? $data['my_tasks_replies_count'] : $max;
            }

            if (!in_array('tagged_internal_notes_tab', $disabled_notifications)) {
                $data['tag_count'] = $tag_count[0]['count'];
                $min = $data['tag_count'] < $min ? $data['tag_count'] : $min;
                $max = $data['tag_count'] > $max ? $data['tag_count'] : $max;
            }
            if (!in_array('payment_notifications_tab', $disabled_notifications)) {
                $data['payment_count'] = $payment_count[0]['count'];
                $min = $data['payment_count'] < $min ? $data['payment_count'] : $min;
                $max = $data['payment_count'] > $max ? $data['payment_count'] : $max;
            }

            if (!in_array('all_messages_tab', $disabled_notifications)) {
                $data['all_messages_count'] = 0;
                $min = $data['all_messages_count'] < $min ? $data['all_messages_count'] : $min;
                $max = $data['all_messages_count'] > $max ? $data['all_messages_count'] : $max;
            }
            if (!in_array('general_notifications_tab', $disabled_notifications)) {
                $data['file_upload_css'] = numberToColorHsl($max - $data['file_upload_count'], $min, $max);
            }
            if (!in_array('priority_notifications_tab', $disabled_notifications)) {
                $data['reminders_css'] = numberToColorHsl($max - $data['reminders_count'], $min, $max);
            }
            if (!in_array('priority_notifications_tab', $disabled_notifications)) {
                $data['reminders_p_css'] = numberToColorHsl($max - $data['reminders_p_count'], $min, $max);
            }
            if (!in_array('applicant_tasks_due_today_tab', $disabled_notifications)) {
                $data['applicant_tasks_due_today_css'] = numberToColorHsl($max - $data['applicant_tasks_due_today_count'], $min, $max);
            }
            if (!in_array('applicant_tasks_due_tommorrow_tab', $disabled_notifications)) {
                $data['applicant_tasks_due_tommorrow_css'] = numberToColorHsl($max - $data['applicant_tasks_due_tommorrow_count'], $min, $max);
            }
            if (!in_array('applicant_tasks_over_due_count_tab', $disabled_notifications)) {
                $data['applicant_tasks_over_due_css'] = numberToColorHsl($max - $data['applicant_tasks_over_due_count'], $min, $max);
            }
            if (!in_array('tagged_internal_notes_tab', $disabled_notifications)) {
                $data['tag_css'] = numberToColorHsl($max - $data['tag_count'], $min, $max);
            }
            if (!in_array('payment_notifications_tab', $disabled_notifications)) {
                $data['payment_css'] = numberToColorHsl($max - $data['payment_count'], $min, $max);
            }
            if (!in_array('all_messages_tab', $disabled_notifications)) {
                $data['all_messages_css'] = numberToColorHsl($max - $data['all_messages_count'], $min, $max);
            }


            if (!in_array('my_tasks_replies_notifications_tab', $disabled_notifications)) {
                $data['my_tasks_replies_css'] = numberToColorHsl($max - $data['my_tasks_replies_count'], $min, $max);
            }

            if (!in_array('admin_task_replies_notifications_tab', $disabled_notifications)) {
                $data['admin_task_replies_css'] = numberToColorHsl($max - $data['admin_task_replies_count'], $min, $max);
            }


            echo json_encode($data);
            exit();
        }


        $view = 'applicants/notifications';
        $data = array(
            'meta_title' => 'Applicants assigned to me - Notifications',
            'custom_title' => 'Applicants assigned to me - Notifications',
            'view_file' => $view,
            'results' => $message,
            'type' => $type,
            'cohort' => $cohort,
            'paginator' => $paginator,
            'filter' => $this->filter,
            'filters_args' => $filters_args,
            'filter_page_name' => 'course_applicants',
            'active_tab' => 'notifications',
            'alert_message' => $results_prep['alert_message'],
            'hide_export_button' => true,
            'hide_send_private_messages' => true,
            'hide_edit_checklist' => true,
            'hide_review_media' => true,
            'back_link' => false,
            'show_tabs' => true,
            'back_link_title' => false,
            'saved_filters_dropdown' => true
        );
        $this->view($this->layout, $data);

    }


    public function assigned_to_me_reviews()
    {

        $students = new Students;

        //Cohort
        if ($_GET['cohort']) {
            $cohort = $_GET['cohort'];
        } else {
            $cohort = $this->school_info['cohort'];
        }

        /** ===================================
         * Reviews Results
         * ====================================  */
        $reviews_args = array(
            "reviewer_id" => $_SESSION['uid'],
            'school_id' => $this->school_info['id'],
            'paginate' => true,
        );
        $reviews_args['cohort'] = $cohort;
        if ($_GET['search']) {
            $reviews_args['search'] = $_GET['search'];
        }

        if ($_GET['order']) {
            $reviews_args['order'] = $_GET['order'];
        }
        $results = $students->reviews($reviews_args);

        //Links
        $back_link = false;
        $back_link_title = "Programmes";


        $data = array(
            'meta_title' => 'Applicants assigned to me - Reviews',
            'view_file' => 'applicants/reviews',
            'results' => $results,
            'active_tab' => 'reviews',
            'level' => $level,
            'cohorts' => $cohorts,
            'cohort' => $cohort,
            'programme' => $programme,
            'back_link' => $back_link,
            'back_link_title' => false,
            'saved_filters_dropdown' => true
        );
        $this->view($this->layout, $data);
    }

    /** ===================================
     * Screening New
     * ====================================  */
    public function assigned_to_me_screening()
    {

        $applicants = new students;
        $courses = new courses;
        $students = new Students;
        $form_templates = new FormTemplates;

        //Prep the applicants screen
        $prep_args = array(
            "extra_applicants_args" => array("reviewer_id" => $_SESSION['uid'], 'no_one_entry' => true, 'has_submitted' => true),
            "school_id" => $this->school_info['id'],
            "filter" => $this->filter,
            "filter_page_name" => "screening_applicants"
        );
        $results_prep = $students->prepare_applicants_screen($prep_args);
        $this->filter = $results_prep['filter'];


        //Links
        //$back_link = $this->base_url('course_levels/'.$this->uri_segement(2).'/programmes/');
        $back_link = false;
        $back_link_title = "Programmes";

        $data = array(
            'main_title' => 'Applicants assigned to me - Screening',
            'meta_title' => 'Applicants assigned to me - Screening',
            'view_file' => 'applicants/screening_new',
            'type_id' => $type_id,
            'programme' => $programme,
            'results' => $results_prep['results'],
            'active_tab' => 'screening',
            'paginator' => $paginator,
            'filter' => $this->filter,
            'filters_args' => $filters_args,
            'filter_page_name' => 'screening_applicants',
            'active_tab' => 'screening',
            'alert_message' => $results_prep['alert_message'],
            'hide_export_button' => false,
            'back_link' => $back_link,
            'back_link_title' => $back_link_title,
            'saved_filters_dropdown' => true
        );
        $this->view($this->layout, $data);

    }

    /** ===================================
     * Get Unassigned Applicants
     * ====================================    */
    public function unassigned_applicants_demo()
    {
        $students = new Students;
        $unassigned_applicants_args = array(
            "school_id" => $this->school_info['id']
        );
        $results = $students->get_unassigned_applicants($unassigned_applicants_args);

        $data = array(
            'main_title' => 'Unassigned Applicants',
            'meta_title' => 'Unassigned Applicants',
            'view_file' => 'applicants/unassigned_applicants',
            'type_id' => $type_id,
            'programme' => $programme,
            'results' => $results,
            'active_tab' => 'unassigned',
            'paginator' => $paginator,
            'filter' => $this->filter,
            'filters_args' => $filters_args,
            'filter_page_name' => 'screening_applicants',
            'active_tab' => 'screening',
            'alert_message' => $results_prep['alert_message'],
            'hide_export_button' => false,
            'back_link' => $back_link,
            'back_link_title' => $back_link_title,
            'saved_filters_dropdown' => true
        );

        $this->view($this->layout, $data);
    }


    public function unassigned_applicants()
    {

        /** ===================================
         * Applicants Results
         * ====================================  */
        $applicants = new students;
        $courses = new courses;
        $form_templates = new FormTemplates;
        $users = new Users;


        $applicants_args = array('school_id' => $this->school_info['id'], 'no_one_entry' => true, 'cohort' => $this->current_cohort, 'unassigned_apps' => true);

        //Check if there are any extra args on the url
        if (count($_GET)) {
            $applicants_args = array_merge($applicants_args, $_GET);
        }


        if ($_REQUEST['search']) {
            $applicants_args['search'] = $_REQUEST['search'];
        }

        if ($_GET['order']) {
            $applicants_args['order'] = $_GET['order'];
        } else {
            $applicants_args['order'] = 'last_name';
        }

        if ($_GET['export'] || $_GET['export_to_ukvi'] || $_POST['select_all_entries']) {
            ini_set('memory_limit', '-1');
        } else {
            $applicants_args['paginate'] = true;
        }

        if ($this->filter['description']) {
            $filters_args['exploded'] = $applicants->filters_students_args($this->filter['description']);
            //if($_SESSION['usergroup'] == 3){
            $applicants_args['filter_sql_lite'] = prepare_filter_sql($this->filter['description'], $applicants->filter_columns());
            //var_dump($applicants_args);
            //}
            $applicants_args['filter'] = $this->filter;
            $applicants_args = array_merge($applicants_args, $filters_args);
        } else {
            $this->filter = array("page" => 'applicants');
        }

        //Check if the default columns exist
        $columns_args = array('page' => 'applicants');
        $page_columns = $form_templates->get_page_columns($columns_args);

        if (!$page_columns) {


            //check if the HEIAPPLY super admin user has a list
            $user_info = $users->get(array('search' => '_superadmin', 'school_id' => $this->school_info['id'], 'order' => 'form_users.id ASC'));
            $user_info = $user_info[0];

            if ($user_info['id']) {
                $columns_args['user_id'] = $user_info['id'];

                $columns = $form_templates->get_page_columns($columns_args);
                $df = json_encode($columns);
            } else {
                $df = '["id","internal_reference","first_name","middle_name","email_address"]';
            }

            // echo '<pre>';
            // 	print_r($df);
            // 	exit();

            $column_args = array(
                'columns' => $df,
                'page' => 'applicants'
            );
            $form_templates->add_page_columns($column_args);
        }

        $results = $applicants->get($applicants_args);

        /** ===================================
         * Export Results
         * ====================================  */
        if ($_GET['export']) {

            if (!$this->filter['id']) {
                $this->filter = array("page" => 'applicants');
            }
            $export_args = array(
                'filter' => $this->filter,
                'results' => $results
            );

            $applicants->export_student_data($export_args);
            exit();
        }

        /** ===================================
         * Export Results
         * ====================================  */
        if ($_GET['export_to_ukvi']) {

            if (!$this->filter['id']) {
                $this->filter = array("page" => 'applicants');
            }
            $export_args = array(
                'filter' => $this->filter,
                'results' => $results
            );

            $applicants->export_student_data_to_ukvi($export_args);
            exit();
        }

        /** ===================================
         * Do a bulk action
         * ====================================  */
        $applicants->process_bulk_action(array('results' => $results));


        $data = array(
            'meta_title' => 'Applicants',
            'view_file' => 'applicants/index',
            'type_id' => $type_id,
            'programme' => $programme,
            'results' => $results,
            'paginator' => $paginator,
            'filter' => $this->filter,
            'filters_args' => $filters_args,
            'filter_page_name' => 'applicants',
            'active_tab' => 'applicants',
            'cohort' => $this->current_cohort,
            'cohorts' => $this->cohorts,
            'screening_option' => false,
            'saved_filters_dropdown' => true
        );
        $this->view($this->layout, $data);

    }

    /**
     * Get applicant courses
     * @return false|string
     */
    public function get_applicant_applications()
    {
        $user_id = sanitise($_REQUEST['user_id']);
        $applicants = new Students;
        echo json_encode($applicants->get_applicant_applications($user_id));
    }

    function generate_temp_emails()
    {
        $students = new Students();
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $limit = 10;
            $subject = empty($_POST['subject']) ? FALSE : strip_tags($_POST['subject']);
            if (strpos($_POST['rows'], 'table_row') === false) {
                $students_args = [
                    'limit' => 2,
                ];
                $temps = $students->get($students_args);
                foreach ($temps as $temp) {
                    $_POST['rows'] = $_POST['rows'] . '&table_row_' . $temp['id'] . '=1';
                }

            }
            $enq_args = array('rows' => $_POST["rows"], 'selected' => $_POST["selected"], 'message' => $_POST["message"], 'subject' => $subject, 'limit' => $limit);
            $enq_args['partner_id'] = isset($_POST['partner_id']) ? $_POST['partner_id'] : '';

            $results = $students->generate_temp_emails($enq_args);
        }
        if (strpos($this->current_url(), '.json') !== false) {
            $entry_json = json_encode($results);
            echo $entry_json;
            exit();
        }
    }

    function generate_temp_email()
    {
        $students = new Students();
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $results = $students->generate_temp_email();
        }
        if (strpos($this->current_url(), '.json') !== false) {
            $entry_json = json_encode($results);
            echo $entry_json;
            exit();
        }
    }

    //get accommodation properties

    function get_properties()
    {
        $students = new Students();
        $args = $this->school_info['id'];
        $properties = $students->get_properties($args);
        echo json_encode($properties);

    }

    //function get_accommodation_data
    function get_accommodation_data()
    {
        $students = new Students;
        $args = $_GET['rel_id'];
        $accommodation_data = $students->get_accommodation_data($args);
        echo json_encode($accommodation_data);

    }

    function assign_accommodation()
    {
        $students = new Students();
        $accommodation_args = array(
            'student_id' => $_POST['rel_id'],
            'accommodation' => $_POST['bulk_accommodation_id'],
            'db48010' => !empty($_POST['start']) ? date('Y-m-d H:i:s', strtotime($_POST['start'])) : pull_field('dir_cohorts', 'db1678', "WHERE id=(SELECT db1682 FROM core_students WHERE id='{$_POST['rel_id']}')"),
            'db48011' => !empty($_POST['start']) ? date('Y-m-d H:i:s', strtotime($_POST['end'])) : pull_field('dir_cohorts', 'db1679', "WHERE id=(SELECT db1682 FROM core_students WHERE id='{$_POST['rel_id']}')"),
        );

        return $students->allocate_accommodation($accommodation_args);
    }

    function check_accommodation()
    {
        $students = new Students();
        $args = $_GET['rel_id'];
        $student = $students->check_accommodation($args);
        echo json_encode($student);
    }

    function check_assignment_type()
    {
        $app_user_profile_id = $_REQUEST['app_user_profile_id'];
        $selected_assignment_id = $_REQUEST['selected_assignment_id'];
        $type = $_REQUEST['type'];
        if ($type == "itinerary") {
            $table = "app_assign_itinerary";
            $field = "db95159";
        } elseif ($type == "guide") {
            $table = "app_assign_guides";
            $field = "db95165";
        } else {
            $table = "app_assign_courses";
            $field = "db95171";
        }
        $id = pull_field($table, "id", "WHERE rel_id='$app_user_profile_id' AND $field = '$selected_assignment_id'");
        echo json_encode($id);
    }

    function make_app_assignment()
    {
        $students = new Students();
        $rec_id = pull_field("core_students", "rec_id", "WHERE id='{$_REQUEST['student_id']}'");
        switch ($_REQUEST['type']) {
            case "itinerary":
                $result = $students->assign_app_itinerary($rec_id, ["itinerary_id" => $_REQUEST['selected_assignment_id']]);
                break;
            case "guide":
                $result = $students->assign_app_guide($rec_id, ["guide_id" => $_REQUEST['selected_assignment_id']]);
                break;
            case "course":
                $result = $students->assign_app_course($rec_id, ["course_id" => $_REQUEST['selected_assignment_id']]);
                break;
            default:
                break;
        }
        return $result;
    }

    function update_accommodation()
    {
        $students = new Students();
        $accommodation_args = array(
            'student_id' => $_POST['rel_id'],
            'accommodation' => $_POST['bulk_accommodation_id'],
            'db48010' => date('Y-m-d H:i:s', strtotime($_POST['start'])),
            'db48011' => date('Y-m-d H:i:s', strtotime($_POST['end'])),
        );
        $student = $students->update_accommodation($accommodation_args);
        return $student;
    }


    public function more_options($type = 'educational_history')
    {
        if ($_GET['show_errors']) {
            ini_set('display_errors', 1);
            ini_set('display_startup_errors', 1);
            error_reporting(E_ALL);
        }

        $applicants = new Students;
        $OL = new OnlineLearning;

        /** ===================================
         * Applicants Results
         * ====================================  */
        $applicants = new students;
        $courses = new courses;
        $form_templates = new FormTemplates;

        $applicants_args = array('school_id' => $this->school_info['id'], 'no_one_entry' => true, 'cohort' => $this->current_cohort);
        $applicants_args[$type] = true;

        if ($_REQUEST['search']) {
            $applicants_args['search'] = trim($_REQUEST['search']);
        }
        if ($_GET['order']) {
            $applicants_args['order'] = $_GET['order'];
        }
        if ($_GET['export'] || $_GET['export_data'] || $_GET['export_to_ukvi'] || $_POST['select_all_entries']) {
            ini_set('memory_limit', '-1');
        } else {
            $applicants_args['paginate'] = true;
        }

        if ($this->filter['description']) {
            $filters_args['exploded'] = $applicants->filters_students_args($this->filter['description']);
            $applicants_args['filter_sql_lite'] = prepare_filter_sql($this->filter['description'], $applicants->filter_columns());
            $applicants_args['filter'] = $this->filter;
            $applicants_args = array_merge($applicants_args, $filters_args);
        } else {
            $this->filter = array("page" => 'applicants');
        }

        //Check if the default columns exist
        $columns_args = array('page' => 'applicants');
        $page_columns = $form_templates->get_page_columns($columns_args);

        if (empty($page_columns)) {

            if (get_option('applicants_page_fields')) {
                $df = json_encode(get_option('applicants_page_fields'));
            } else {
                $df = '["id","internal_reference","first_name","middle_name","email_address"]';
            }
            $column_args = array(
                'columns' => $df,
                'page' => 'applicants'
            );
            $form_templates->add_page_columns($column_args);
        }

        if ($this->filter['description'] || $applicants_args['search']) {
            $applicants_args['filter_sql_lite'] = prepare_filter_sql($this->filter['description'], $applicants->filter_columns());
            $filters_args['exploded'] = $applicants->filters_students_args($this->filter['description']);
            $applicants_args = array_merge($applicants_args, $filters_args['exploded']);

        }

        $results = $applicants->get($applicants_args);
        //die(json_encode($results[0]));

        if ($_GET['export'] || $_GET['export_data']) {

            if (!$this->filter['id']) {
                $this->filter = array("page" => 'applicants');
            }
            $export_args = array(
                'filter' => $this->filter,
                'results' => $results,
                'return_results' => true
            );

            //$applicants->export_student_data($export_args);
            $results = $applicants->export_student_data($export_args);
            if (isset($_GET['type'])) {
                $applicants->export_formated2($results, $_GET['type']);
            }
            exit();
        }
        /** ===================================
         * Export to UKVI
         * ==================================== */
        if ($_GET['export_to_ukvi']) {
            if (!$this->filter['id']) {
                $this->filter = array("page" => 'custom_reports_educational');
            }
            $export_args = array(
                'filter' => $this->filter,
                'results' => $results
            );

            $applicants->export_student_data_to_ukvi($export_args);
            exit();
        }
        $title = ucwords(str_replace('_', " ", $type));
        $cntrl = $type == 'parents' ? 'applicants/more_options/parents/' : 'report/more/' . $type;
        $data = array(
            'meta_title' => "Custom Reports - {$title}",
            'view_file' => 'applicants/more_options',
            'type_id' => $type_id,
            'title' => $title,
            'programme' => $programme,
            'results' => $results,
            'paginator' => $paginator,
            'filter' => $this->filter,
            'filters_args' => $filters_args,
            'show_filters_message' => true,
            'filter_page_name' => 'applicants',
            'active_tab' => 'custom_reports',
            'saved_filters_dropdown' => true,
            'controller' => $cntrl
        );
        dev_debug("REPORT VIEW $this->layout $data[view_file]");
        $this->view($this->layout, $data);
    }

    //reset application
    function reset_application()
    {
        $student = new Students;
        $reset_application_data = $student->reset_application($_POST);
        echo json_encode($reset_application_data);
    }

    function get_template()
    {
        $student = new Students;

        $reset_application_data = $student->get_template();

        return $reset_application_data;

    }

//get intake start date and end date
    function get_intake_dates()
    {
        $student = new Students;

        $id = $_GET['rel_id'];

        $intake_dates = $student->get_intake_dates($id);

        echo json_encode($intake_dates);

    }


    public function bulk_letter()
    {

        $student = new Students;

        $student->process_bulk_action();

    }

    function fetchsavedprintsectionsview()
    {
        $dbh = get_dbh();

        $sql = "SELECT description FROM page_columns WHERE usergroup='" . $_SESSION['usergroup'] . "' AND rel_id='" . $_SESSION['uid'] . "' 
                AND page='chosen_bulk_print_sections_view'";

        $sth = $dbh->prepare($sql);
        $sth->execute();
        $selected_options = $sth->fetch(PDO::FETCH_ASSOC);
        dev_debug($sql);
        if (isset($selected_options['description'])) {
            return json_decode($selected_options['description']);
        } else {
            return false;
        }
    }


    public function filters()
    {
        $custom = new CustomFilters("custom_filter_{$_SESSION['usergroup']}", []);
        $fields = $custom->get_fields();
        $data = array(
            'meta_title' => 'Custom Filters',
            'view_file' => 'applicants/filters',
            'fields' => json_encode($fields) ?? []
        );
        $this->view('layout/elementui', $data);
    }

    public function filters_update()
    {
        $data = $_POST;
        $response = array('message' => '');
        $custom = new CustomFilters("custom_filter_{$_SESSION['usergroup']}", []);
        $custom->set_fields($data);
        if ($custom->save()) {
            $custom->reload()->dynamic(true);
            $response['message'] = 'Successfully updated';
        }
        echo json_encode($response);
    }

    public function create_app_user_profiles()
    {
        $dbh = get_dbh();
        $existing_ids = pull_field("app_user_profiles", "GROUP_CONCAT(rec_id)", "WHERE usergroup={$_SESSION['usergroup']}");
        $select_query = "SELECT core_students.id as id, core_students.rel_id as rel_id, core_students.rec_id as rec_id 
FROM core_students 
    left join dir_stage_tracker on core_students.id = dir_stage_tracker.rel_id and dir_stage_tracker.db1142= 12
    WHERE core_students.usergroup={$_SESSION['usergroup']} AND core_students.rel_id NOT IN ($existing_ids)";
        dev_debug($select_query);
        $stmt = $dbh->prepare($select_query);
        $stmt->execute();
        $results = $stmt->fetchAll(2);
        foreach ($results as $record) {
            $username_id = random();
            $query = "INSERT INTO app_user_profiles (username_id, rec_id, rel_id, usergroup, rec_archive) VALUES ('$username_id', {$record['rec_id']}, {$record['rel_id']}, {$_SESSION["usergroup"]}, 'appl-2890')";
            dev_debug($query);
            $dbh = get_dbh();
            $sth = $dbh->prepare($query);
            $sth->execute();
        }
    }

    public function add_applicant_address()
    {
        $students = new Students;
        $update = $students->add_applicant_address($_POST);
        echo json_encode($update);
    }

    public function send_wa_message()
    {
        $messages = new CommunicationMessages;
        $chat = $messages->send_whatsapp_message();
        echo json_encode($chat);
    }


    public function fetch_progress()
    {
        $progressfile = new Bulk_pdf_export;
        $progressfile->get_progress($_POST['progress_file']);
    }

    public function mark_as_reviewed(): string
    {
        $applicant_args = [
            'applicant_id' => $_POST['rel_id'],
            'reviewer_id' => $_SESSION['uid'],
            'email_sent' => "no"
        ];
        mark_applicant_as_reviewed($applicant_args);
        return "Reviewed";
    }
}



