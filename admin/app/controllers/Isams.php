<?php
ini_set('memory_limit',-1);
//ini_set('display_errors', 1);
//ini_set('display_startup_errors', 1);
//error_reporting(E_ALL);
/**
 * Isams Controller
 */
class Isams extends Controller{
    private $isams_test_username;
    private $isams_test_password;
    private $isams_test_url;
    private $isams_live_username;
    private $isams_live_password;
    private $isams_live_url;
    private $isams_api_url;
    private $mapper_model;
    private $isamsModel;


    public function __construct()
    {
        parent::__construct();
        $isamsModel = new \App\models\Isams_model();
        $isams_api_data = $isamsModel->getIsamsApiDetails($_SESSION['usergroup']);
        $this->isamsModel= $isamsModel;
        $this->mapper_model=new ApiMapper();
        $this->isams_api_mode = $isams_api_data->api_mode;
//        $this->isams_api_mode = 'test';
        $this->isams_test_client_id = $isams_api_data->test_client_id;
//        $this->isams_test_client_id = "D7E0E874-6ECD-4800-832E-F507A3DBBF47";
        $this->isams_test_client_secret = $isams_api_data->test_client_secret;
//        $this->isams_test_client_secret = "D5725C06-A7FF-448A-8265-F92DBE331139";
        $this->isams_test_url = $isams_api_data->test_api_url;
//        $this->isams_test_url = 'https://developerdemo.isams.cloud/auth/connect/token';
        $this->isams_live_client_id = $isams_api_data->live_client_id;
        $this->isams_live_client_secret = $isams_api_data->live_client_secret;
        $this->isams_live_url = $isams_api_data->live_api_url;
//        if($this->isams_api_mode=='sandbox'){
//            $this->isams_api_url=$this->isams_test_url;
//            $this->isams_client_id= $this->isams_test_client_id;
//            $this->isams_client_secret= $this->isams_test_client_secret;
//        }else{
//            $this->isams_api_url=$this->isams_live_url;
//            $this->isams_client_id= $this->isams_live_client_id;
//            $this->isams_client_secret= $this->isams_live_client_secret;
//        }

    }

    public function enrol_on_isams(){
        $data = array(
            'meta_title'=>'iSAMS API Enrolment',
            'view_file'=>'isams/enrol_isams_students'
        );

        //$students= 
        $this->view($this->layout,$data);
    }

    function getIsamsToken($core_student_id=0){
        $token = "";
        ///generate the encoded string
        if($this->isams_api_mode=='sandbox'){
            $this->isams_api_url=$this->isams_test_url;
            $this->isams_client_id= $this->isams_test_client_id;
            $this->isams_client_secret= $this->isams_test_client_secret;
        }else{
            if($core_student_id>0){
                $is_test_student = pull_field("core_students", "db135", "WHERE id ='$core_student_id'");
                if($is_test_student=='yes'){
                    $this->isams_api_url=$this->isams_test_url;
                    $this->isams_client_id= $this->isams_test_client_id;
                    $this->isams_client_secret= $this->isams_test_client_secret;
                }else{
                    $this->isams_api_url=$this->isams_live_url;
                    $this->isams_client_id= $this->isams_live_client_id;
                    $this->isams_client_secret= $this->isams_live_client_secret;
                }
            }else{
                $this->isams_api_url=$this->isams_live_url;
                $this->isams_client_id= $this->isams_live_client_id;
                $this->isams_client_secret= $this->isams_live_client_secret;
            }

        }
        //echo "<pre>".$this->isams_api_url."</pre>";


        $isams_string_to_encode = $this->isams_client_id.":".$this->isams_client_secret;
        $isams_encoded_string = base64_encode($isams_string_to_encode);

        //Check if "curl" can be found in the array of loaded extensions.
        if(function_exists('curl_init') === true){

            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => $this->isams_api_url."/auth/connect/token",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => "grant_type=client_credentials&scope=restapi&client_id=$this->isams_client_id&client_secret=$this->isams_client_secret",
                CURLOPT_HTTPHEADER => array(
                    "Authorization: Basic ".$isams_encoded_string,
                    "Content-Type: application/x-www-form-urlencoded",
                    "cache-control: no-cache"
                ),
            ));

            $response = curl_exec($curl);
            $err = curl_error($curl);
//            echo "<pre>";
//            print_r($response);
//            echo "</pre>";
//            exit();
            curl_close($curl);

            ///return these results
            if ($err) {
                echo "cURL Error #:" . $err;
            } else {
                $response = json_decode($response);
                $token = $response->access_token;
                $expires_in = $response->expires_in;
                $token_type = $response->token_type;

//                if(isset($token)){
//                    $data = array("access_token" => $token,"success" => true,"message" => 'All Successful');
////                    echo json_encode($data);
//                    return $token;
//                }
//                else{
//                    $data = array("access_token" => $response,"success" => false,"message" => 'Token Generation Failed');
//                   // echo json_encode($data);
//
//                }

            }
        } else{
            echo "Curl has not been loaded on the server";
        }

        return $token;
    }
    function createIsamsStudent($student_mandatory_data=array())
    {
        //first get the token
        $log_data = array();
//        echo "<pre>" . "token=" . $token . "</pre>";
        ###get the student data here
        $student_ids = $_REQUEST['student_id'];
        ///check if single enrol or multiple
        $number_of_students = count($student_ids);
        if($number_of_students <2 ) {
            $isamsModel = new \App\models\Isams_model();
            $isams_args['usergroup'] = $_SESSION['usergroup'];
            ///get current cohort for this school
//        $isams_args['school_cohort'] = $this->school_info['cohort'];
            $isams_args['school_cohort'] = $_POST['cohort'];
            //$isams_args['not_admin_users'] = 1;///comment this when on localhost
            $isams_args['not_in_logs'] = 1;///comment this when on localhost
            //$isams_args['not_test_records'] = 1;///comment this when on localhost
            $isams_args['student_id'] = $student_ids['0'];///comment this when on localhost
            $enrolment_id = $isams_args['student_id'];
            $core_student_id = pull_field('enrol_enrolment_form','rel_id',"WHERE id=".$student_ids['0']);
            $token = $this->getIsamsToken($core_student_id);
            $endpoint_args['endpoint_id']=1;
            $endpoint_args['join_type']='inner';
            //$_GET['debug_mode']="yes";

            #$enrol_student_data = $this->isamsModel->getIsamsStudentData_2($isams_args);
            $enrol_student_data = $this->isamsModel->getIsamsStudentData($isams_args);

            //echo "<pre>".print_r($enrol_student_data,1)."<pre>";exit();
//            $student_fields = array(
//                'forename' => $enrol_student_data['forename'],
//                'surname' => $enrol_student_data['surname'],
//                'boardingStatus' => 'Day',
//                'gender' => $enrol_student_data['gender'],
//                'dateOfBirth' => $enrol_student_data['dateOfBirth'],
//                'enquiryDate' => $enrol_student_data['enquiryDate'],
//                'enrolmentSchoolYear' => $enrol_student_data['yearOnEntry'],
//               // 'enrolmentSchoolYearGroup' => $enrol_student_data['schoolYearGroup'],
//                'mobileNumber' => $enrol_student_data['parentOneTelephone'],
//                'schoolEmailAddress' => $enrol_student_data['parentOneEmail']
//            );

            //echo "<pre>".print_r($student_fields,1)."<pre>";
            $mapper_fields=$this->mapper_model->get_api_endpoints_field($endpoint_args);
            //echo "<pre>".print_r($mapper_fields,1)."<pre>";exit();
            //$student_fields=[];
            //$mapper_fields=[];
            $errors=[];
            if (!empty($mapper_fields)) {

                foreach ($mapper_fields as $mkey => $mvalue) {
                    if (!empty($mvalue['field_id'])|| !empty($mvalue['source_free_text'])) {
                        //$_GET['debug_mode']="yes";
                        $db_field_name=pull_field('system_table','db_field_name',"WHERE form_id=".$mvalue['field_id']);
                        $st_id=pull_field('enrol_enrolment_form','rel_id',"WHERE id=".$student_ids['0']);
                        //$st_id=$student_ids['0'];//this works if the id coming from UI is core_students.id

                        if (!empty($mvalue['source_free_text'])) {
                            $info=$mvalue['source_free_text'];
                        }else{
                            $info=general_get_data_by_field($db_field_name,$st_id);
                        }

                        if (!empty($info)) {
                            $student_fields[$mvalue['api_field_name']]=$info;
                        }

                        if ($mvalue['destination_required_field']&& empty($student_fields[$mvalue['api_field_name']])) {
                            $errors[]="Required ".$mvalue['api_field_name']. " is missing on applicant's data";
                        }
                    }

                }



                //echo "<pre>".print_r($student_fields,1)."<pre>";exit();

            }

            if (!empty($errors)) {
                echo json_encode([
                    "studentSchoolId" => 0,
                    "success" => false,
                    "message" => implode("<br>", $errors),
                    "curl_response" => "",
                ]);exit();

            }
            if (!empty($student_fields['gender'])) {
                $student_fields['gender']=($student_fields['db1536']=='Female' OR $student_fields['db1536']=='female')?"F":"M";
            }


            //echo "<pre>".print_r($student_fields,1)."<pre>";exit();
            $student_fields_string = json_encode($student_fields);
            $ret1= $this->post_data("/api/admissions/applicants",$student_fields_string,'POST',$core_student_id);
            if (!empty($ret1['studentSchoolId'])) {
                $student_c_fields=[];
                $endpoint_args['endpoint_id']=2;
                $mapper_fields=$this->mapper_model->get_api_endpoints_field($endpoint_args);
                //echo "<pre>".print_r($mapper_fields,1)."<pre>";
                foreach ($mapper_fields as $mkey => $mvalue) {
                    if (!empty($mvalue['field_id'])) {
                        $db_field_name=pull_field('system_table','db_field_name',"WHERE form_id=".$mvalue['field_id']);
                        $st_id=pull_field('enrol_enrolment_form','rel_id',"WHERE id=".$student_ids['0']);
                        if (!empty($mvalue['source_free_text'])) {
                            $info=$mvalue['source_free_text'];
                        }else{
                            $info=general_get_data_by_field($db_field_name,$st_id);
                        }
                        if (!empty($info)) {
                            //$student_fields[$mvalue['api_field_name']]=$info;
                            $student_c_fields[]=['id'=>$mvalue['api_end_points_remote_field_id'],'value'=>$info];
                        }


                        if ($mvalue['destination_required_field']&& empty($info)) {
                            $errors[]="Required ".$mvalue['api_field_name']. " is missing on applicant's data";
                        }
                    }


                }
                //echo "<pre>".print_r($student_c_fields,1)."<pre>";exit();

                if (!empty($errors)) {
                    echo json_encode([
                        "studentSchoolId" => 0,
                        "success" => false,
                        "message" => implode("<br>", $errors),
                        "curl_response" => "",
                    ]);exit();

                }

                $student_fields_string = json_encode($student_c_fields);
                $ret= $this->post_data("/api/admissions/applicants/".$ret1['studentSchoolId']."/customFields",$student_fields_string,'patch',$core_student_id,$ret1['studentSchoolId']);

            }

            //echo "<pre>".print_r($ret,1)."<pre>";echo "<pre>".print_r($ret1,1)."<pre>";exit();

            if (!empty($ret1)) {
                $ret1['studentName'] = $student_fields['forename'] . " " . $student_fields['surname'];
                if(!empty($ret) && $ret['customFields']=='Yes'){
                    $ret1['customFields'] = $ret['customFields'];
                }
                echo json_encode($ret1);
            }


        }else{
            $create_error_data = array("studentSchoolId" => 0, "success" => false, "message" => 'Student Id not available');
            echo json_encode($create_error_data);
        }
    }

    ####BULK CREATE HERE####



    function post_data($url, $postjson, $httpverb = 'POST',$core_student_id,$passed_school_id=0) {
        $create_data = [];
        $log_data = [];

        $token = $this->getIsamsToken($core_student_id);
        $client = new GuzzleHttp\Client();

        try {
            // Prepare request options
            $options = [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'Authorization' => 'Bearer ' . $token,
                ],
                'body' => $postjson,
            ];

            // Send the request
            $response = $client->request($httpverb, $this->isams_api_url . $url, $options);
            //echo "<pre>".print_r($response,1)."<pre>";
            $statusCode = $response->getStatusCode();
            $headers = $response->getHeaders();
            //echo "<pre>".print_r($statusCode,1)."<pre>";
            //echo "<pre>".print_r($headers,1)."<pre>";


            // Check if the response is successful (HTTP status code 201)
            if ($statusCode === 201||$statusCode === 200) {
                if(strtolower($httpverb)=='post') {
                    $locationHeader = $headers['Location'][0];
                    $studentSchoolId = basename($locationHeader);
                    //$student_name = $enrol_student_data['forename'] . " " . $enrol_student_data['surname'];
                    $contactId = 0;

                    $create_data = [
                        "studentSchoolId" => $studentSchoolId,
                        //"studentName" => $student_name,
                        "customFields" => 'No',
                        "studentLink" => $locationHeader,
                        "success" => true,
                        "message" => 'Student Successful Created',
                        "token" => $token,
                        'body' => $postjson
                    ];

                    // echo "<pre>".print_r($create_data,1)."<pre>";

                    // Log the successful enrolment
                    $log_data = [
                        'rec_id' => $_SESSION['uid'] ?? "1",
                        'usergroup' => $_SESSION['usergroup'] ?? "1",
                        'rel_id' => $core_student_id,
                        'date' => date("Y-m-d H:i:s"),
                        'rec_lstup' => "0000-00-00 00:00:00",
                        'rec_lstup_id' => "0",
                        'db264209' => "isams",
                        'db260927' => $studentSchoolId,
                        'db176963' => $locationHeader,
                        'db265385' => 'yes',
                        'db176966' => json_encode($headers),
                    ];


                }else if(strtolower($httpverb)=='patch'){
                    $create_data = [
                        "studentSchoolId" => $passed_school_id,
                        //"studentName" => $student_name,
                        "customFields" => 'Yes',
                        "studentLink" => $url,
                        "success" => true,
                        "message" => 'Student Custom Fields Patched',
                        "token" => $token,
                        'body' => $postjson
                    ];

                    $log_data = [
                        'rec_id' => $_SESSION['uid'] ?? "1",
                        'usergroup' => $_SESSION['usergroup'] ?? "1",
                        'rel_id' => $core_student_id,
                        'date' => date("Y-m-d H:i:s"),
                        'rec_lstup' => "0000-00-00 00:00:00",
                        'rec_lstup_id' => "0",
                        'db264209' => "isams",
                        'db260927' => $passed_school_id,
                        'db176963' => $url,
                        'db265385' => 'yes',
                        'db176966' => $postjson,
                    ];

                }
                $this->isamsModel->insert_isams_log($log_data);
                return $create_data;
            } else {
                // Handle error response
                $create_error_data = [
                    "studentSchoolId" => 0,
                    "success" => false,
                    "message" => 'Student was not created',
                    "curl_response" => (string)$response->getBody(),
                    "token"=> $token,
                    'body' => $postjson
                ];

                $log_data = [
                    'db260927' => "0",
                    'db260930' => "0",
                    'db176963' => "",
                    'db265385' => 'error',
                    'db176966' => json_encode($create_error_data),
                ];

                $this->isamsModel->insert_isams_log($log_data);
                echo json_encode($create_error_data);
            }
        } catch (GuzzleHttp\Exception\RequestException $e) {
            // Handle exception
            $create_error_data = [
                "studentSchoolId" => 0,
                "success" => false,
                "message" => 'Request failed: ' . $e->getMessage(),
                "token"=> $token,
                'body' => $postjson
            ];
            echo json_encode($create_error_data);
        }
    }

    function createIsamsContact($token,$contact_mandatory_data){

    }

    function getIsamsStudentData(){
        $isamsModel = new \App\models\Isams_model();
        $isams_args['usergroup'] = $_SESSION['usergroup'];
        ///get current cohort for this school
//        $isams_args['school_cohort'] = $this->school_info['cohort'];
        $isams_args['cohort'] = $_REQUEST['cohort'];
        //$isams_args['not_admin_users']=1;///comment this when on localhost
        $isams_args['not_in_logs'] = 1;///comment this when on localhost
        //$isams_args['not_test_records'] = 1;///comment this when on localhost
//        $isams_args['student_id'] = 1;///comment this when on localhost

        #$enrol_student_data = $this->isamsModel->getIsamsStudentData_2($isams_args);
        $enrol_student_data = $this->isamsModel->getIsamsStudentData($isams_args);
        ///construct the rows here
        //set the number of rows you'd like each table to have
        $final_students = '';
        $count = count($enrol_student_data);
        for($p = 0; $p < $count; $p++) {
            $final_students = $final_students.'<tr >';
            $final_students = $final_students.'<td><input type="text" readonly class="form-control" name="student_id[]" id="student_id" value="' . (isset($enrol_student_data[$p]['student_id']) ? $enrol_student_data[$p]['student_id'] : 'N/A') . '"</td>';
            $final_students = $final_students.'<td>' . (isset($enrol_student_data[$p]['forename']) ? $enrol_student_data[$p]['forename'] : 'N/A' ) . '</td>';
            $final_students = $final_students.'<td>' . (isset($enrol_student_data[$p]['surname']) ? $enrol_student_data[$p]['surname'] : 'N/A' ) . '</td>';
            $final_students = $final_students.'<td>' . (isset($enrol_student_data[$p]['dateOfBirth']) ? $enrol_student_data[$p]['dateOfBirth'] : 'N/A' ) . '</td>';
            //$final_students = $final_students.'<td>' . (isset($enrol_student_data[$p]['studentAddressLineOne']) ? $enrol_student_data[$p]['studentAddressLineOne'] : 'N/A' ) . '</td>';
            $final_students = $final_students.'<td>' . (isset($enrol_student_data[$p]['gender']) ? $enrol_student_data[$p]['gender'] : 'N/A' ) . '</td>';
            $final_students = $final_students.'<td>' . (isset($enrol_student_data[$p]['course']) ? $enrol_student_data[$p]['course'] : 'N/A' ) . '</td>';
            $final_students = $final_students.'<td><button type = "button" id="'.(isset($enrol_student_data[$p]['student_id']) ? $enrol_student_data[$p]['student_id'] : '0' ).'" class = "btn btn-link single_enrol" data-toggle="modal" data-target="#confirm-enrol" title="Enrol Student on iSMAS"><span class="fa fa-user-plus" style="color: green"></span></button></td>';
//            $final_students = $final_students.'<td><button type = "button" id="'.(isset($enrol_student_data[$p]['student_id']) ? $enrol_student_data[$p]['student_id'] : '0' ).'" class = "btn btn-link sent_to_ebs" data-toggle="modal" data-target="#confirm-sent" title="Mark Student as Sent to iSMAS"><span class="fa fa-check-square-o" style="color: green"></span></button></td>';
            $final_students = $final_students.'</tr>';
        }
        echo json_encode($final_students);

    }

    function ismas_mapping(){
        $isams_module = new \App\models\Isams_model();

        $data = array(
            'meta_title'=>'Add Permission Categories',
            'view_file'=>'users/perm_cats_details',
            'action'=>'edit',
            'enrolment_fields' => $isams_module->get_enrolment_fields(),
            'isams_fields'=>$isams_module->get_isams_fields()
        );
        $this->view($this->layout,$data);
    }

    function get_isams_custom_fields($schoolId=0){
        $token = $this->getIsamsToken();
        echo "<pre>".print_r($token,true)."</pre>";
        if (function_exists('curl_init') === true) {
            $curl_get_cust = curl_init();
            curl_setopt_array($curl_get_cust, array(
                CURLOPT_URL => $this->isams_api_url . "/api/admissions/applicants/".$schoolId."/customFields",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'Accept: application/json',
                    "Authorization: Bearer " . $token,
                ),
            ));
            $create_response = curl_exec($curl_get_cust);
            $create_err = curl_error($curl_get_cust);
            $curl_info = curl_getinfo($curl_get_cust);
            $httpcode = $curl_info['http_code'];
            curl_close($curl_get_cust);
            if ($create_err) {
                echo "cURL Error #:" . $create_err;
            } else {
                //print_r($httpcode);
                if($httpcode === 200) {
                    print_r($create_response);
                }
            }
        }else{
            $create_error_data = array("studentSchoolId" => $schoolId, "success" => false, "message" => 'Curl could not be initialized in GET custom fields');
            echo json_encode($create_error_data);
        }

    }


    function bulk_create_enrolment_forms($ug=1,$cohort=2020,$course_id='',$limit='',$stage=''){
        $isams_module = new \App\models\Isams_model();
        $enrol_args=array(
            'ug'=>$ug,
            'cohort'=>$cohort,
            'course_id'=>$course_id,
            'limit'=>$limit,
            'stage'=>$stage,
        );
        $bulk_enrol_response = $isams_module->bulk_create_enrolment_forms($enrol_args);
        //echo "<pre>".print_r($bulk_enrol_response,true)."</pre>";
        $all_bulk_responses = [];
        if(!empty($bulk_enrol_response)){
            foreach ($bulk_enrol_response as $row){
                //echo "<pre>".$row."</pre>";
                if(!empty($row)){
                    $bulk_isams_res[] = $isams_module->bulkCreateIsamsStudent($row);
                    $all_bulk_responses[] = $bulk_isams_res;
                }
            }
        }
        echo json_encode($all_bulk_responses);
    }

}
