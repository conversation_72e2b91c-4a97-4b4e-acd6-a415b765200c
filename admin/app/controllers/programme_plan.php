<?php
/**
 * Created by PhpStorm.
 * User: andrew
 * Date: 6/4/20
 * Time: 3:20 PM
 */

class programme_plan extends Controller{
  private $filter_fields = array(
    'form_internal_projects.ID',
    'db54088',
    'db33637',
    'db31891',
    'db31889',
    'db41046',
    'db54087',
    'db54072',
    'db31890',
    'db31892'
  );
  private $extras = array(
    'db31890' => array('title'=>'Actual Start Date' , 'type' => 'daterange'),
    'db54072' => array('title'=>'Planned Start Date' , 'type' => 'daterange'),
    'db31892' => array('title'=>'Expected End Date' , 'type' => 'daterange'),
    "db31889" => array('title'=>'Allocated To' , 'type' => 'dynamic_list_group', 'default' => "form_users WHERE usergroup=1 And db112=9 ORDER BY db106,db106+db111"),
    "db33637" => array('title'=>'Epic' , 'type' => 'dynamic_list_group', 'default' => 'form_epic_profile order by db41048,db41049'),
    "db54088" => array('title'=>'Internal Track' , 'type' => 'dynamic_list_group', 'default' => 'form_internal_track order by db54082,db54082'),
    "db54087" => array('title'=>'Internal Programme' , 'type' => 'dynamic_list_group', 'default' => 'form_internal_programme order by db54077,db54077'),
    "db31891" => array('title'=>'Status' , 'type' => 'dropdown', 'default' => 'draft,ready,active,active_config,completed,internal_review,released_on_dev,released_on_uat,pushed_to_live,excluded'),
  );

  public function index()
  {
      $programme = new InternalProgramme;
    $params = array(
      'paginate' => true,
      'search' => trim($_POST['search']) ?: trim($_GET['search']),
      'order' => $_GET['order'],
      'programme' => $_GET['programme']
    );
    if(!empty($_REQUEST['pushed_to_live'])){$params['pushed_to_live'] = 'yes';}
    $filters = new Filter; if(empty($this->filter['id'])){$this->filter =array('page' => get_class($this).'_'.__FUNCTION__, 'description' => array());}
    $setup = $filters->setup_view_filters($this->filter_fields, $this->extras);
    $params['filter_sql'] = $filters->description_to_sql($this->filter['description']);
    if(!empty($_REQUEST['search']))$params['search'] = trim($_POST['search']) ?: trim($_GET['search']);
    $results = $programme->get_ip_programming($params);;

    $data = array(
      'meta_title' => 'Internal Programmes',
      'view_file' => 'internal_programmes/index',
      'results'    => $results,
        'programmes'    => $programme->get_programmes($params),
        'programme_name' => empty($_GET['programme'])?"": pull_field("form_internal_programme", "db54077", " WHERE id='{$_GET['programme']}'"),
      'filter'=>$this->filter,
      'dimensions' => $setup[1],
      'filter_types' => $setup[0],
      'filter_page_name'=>'internal_pogrammes',
    );
    $this->view($this->layout, $data);
  }
  public function programmes($id='')
  {
    $programme= new InternalProgramme();
    $params = array(
      'paginate' => true,
      'search' => trim($_POST['search']) ?: trim($_GET['search']),
      'order' => $_GET['order']
    );
    if(!empty($id) && is_numeric($id)){
      $programmes = $programme->get_details(['id'=>$id]);
      $data = array(
        'meta_title' => 'Epic',
        'view_file' => 'internal_programmes/programmes/details',
        'allowed' => in_array($_SESSION['uid'], explode(",", pull_field("form_internal_teams", "db50639", "Where id=6"))),
        'status_classes' => Form_Product::$status_classes,
        'results' => $programmes,
      );
      $this->view($this->layout, $data);
    }else {
    $filters = new Filter; if(empty($this->filter['id'])){$this->filter =array('page' => get_class($this).'_'.__FUNCTION__, 'description' => array());}
    $setup = $filters->setup_view_filters(array('db54078'));
    $params['filter_sql'] = $filters->description_to_sql($this->filter['description']);
    if(!empty($_REQUEST['search']))$params['search'] = trim($_POST['search']) ?: trim($_GET['search']);
    $results = $programme->get_programmes($params);

    $data = array(
      'meta_title' => 'Internal Programmes',
      'view_file' => 'internal_programmes/programmes/index',
      'results'    => $results,
      'filter'=>$this->filter,
      'dimensions' => $setup[1],
      'filter_types' => $setup[0],
      'filter_page_name'=>'internal_projects',
    );
    $this->view($this->layout, $data);
    }
  }
  public function update_track_order(){
    $data = json_decode($_REQUEST['data'], true);
    $db_helper = new Db_helper();
    foreach ($data as $datum){
      $db_helper->update("form_internal_track", ['db54083'=>$datum['order']],['id'=>$datum['id']]);
    }
  }
}