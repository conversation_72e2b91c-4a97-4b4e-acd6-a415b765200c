<?php 
/**
* 
STAGES CONTROLLER
*/
class stages extends Controller
{
	private $cohorts;
	private $current_cohort;
	private $stages;
	private $params;
	public function __construct(){
		parent::__construct();
		$students = new Students;
		$form_templates = new FormTemplates;
		$this->cohorts = $students->cohorts_list(array('school_id'=>$this->school_info['id']));
		$this->current_cohort = $this->school_info['cohort'];
		if($_GET['cohort']){
			$this->current_cohort = $_GET['cohort'];
		}
		$this->params = array("all"=>true,'school_id'=>$this->school_info['id'],"total_applicants"=>true, 'cohort' => $this->current_cohort);

		if(isset($_GET['order'])){
			$this->params['order'] = str_replace("+", " " , $_GET['order']);
		}

		if(isset($_POST['search'])){
			$this->params['search'] = trim($_POST['search']);
		}

		$filter_ids = array(
			'stage_name' =>'db1130',
			'description' => 'db34446',
		);

		if($this->filter['description']){
				$this->params['filter_sql_lite'] = prepare_filter_sql($this->filter['description'], $filter_ids);
				$filter_args = prepare_filter_args($this->filter['description']);
				unset($filter_args['stage_name']);
				unset($filter_args['description']);
			}

		$this->stages = $form_templates->get_custom_form_core_stages($this->params);

	}
	
	public function index($intake_id=null){

		global $request;

		$exceptions = array('id', 'order');
		$rename_fields = array('count' => 'total');
		unset($this->params['filter_sql_lite']);
		$dropdown_options = array(
			'stage_name' => 'FormTemplates|get_custom_form_core_stages|'.json_encode($this->params).'|["order", "stage_name"]'
		);
		$filter_types = setup_filter_data($this->stages, 'filter_types', $exceptions , $rename_fields , $dropdown_options, $field_types);
			//echo json_encode($filter_types);
		$dimensions = setup_filter_data($this->stages, 'dimensions' , $exceptions, $rename_fields, $dropdown_options, $field_types);

		$order = explode(" ",  $_GET['order']);
		if(! $_GET['order'] || $order[0] != "stage_name"  || ($order[0] == "stage_name" && $order[1] =="desc"))
			$sort_by[] = array('title' => 'Stage Name ASC' , 'value' => 'stage_name+asc');
		else
			$sort_by[] = array('title' => 'Stage Name DESC' , 'value' => 'stage_name+desc');

		if(! $_GET['order'] || $order[0] != "total"  || ($order[0] == "total" && $order[1] =="desc"))
			$sort_by[] = array('title' => 'Total ASC' , 'value' => 'total+asc');
		else
			$sort_by[] = array('title' => 'Total DESC' , 'value' => 'total+desc');
		$data = array(
			'meta_title'=>'Application Status',
			'view_file'=>'stages/index',
			'results'=>$this->stages,
			'cohorts'=>$this->cohorts ,
			'cohort'=>$this->current_cohort,
			'filtering_tools' => true,
			'filter'=>$this->filter,
			'sort_by' => $sort_by,
			'dimensions' => $dimensions,
			'filter_types' => $filter_types,
			'filters_args'=>$filters_args,
			'filter_page_name'=>'intakes',
			'location' => $this->base_url('stages/custom')
		);

		$this->view($this->layout,$data);
	
	
	}




/** ===================================
	* Applicants
	====================================  */
	public function applicants($stage_id='', $app_cat = ''){

		$applicants = new students;
		$courses = new courses;
		$students = new Students;
		$form_templates = new FormTemplates;
        $users = new Users;
		$current_stage = $form_templates->get_custom_form_core_stages(array("use_order"=> $stage_id));
		$active_tab = 'stage_applicants';
		if($app_cat){
			$active_tab = '';
			$ft = array(
				'id' => $this->filter['id'] ?: '',
				'title' => ucfirst($app_cat),
				'page' => 'stage_applicants',
				'test' => true,
				'description' => $this->filter['description'] ?: array()
			);
			$has_submitted = array('type' => 'has_submitted', 'title' => '', 'status' => 'active' , 'join' => 'and' , 'value' => '' , 'option' => '1' , 'options' => array(array('value' => '1' , 'label' => 'True' ), array('value' => '0' , 'label' => 'False')));
			$has_offer = array('type' => 'has_offer', 'title' => '', 'status' => 'active' , 'join' => 'and' , 'value' => '' , 'option' => '1' , 'options' =>array(array('value' => '1' , 'label' => 'True' ), array('value' => '0' , 'label' => 'False')));
			$accepted_offers= array('type' => 'accepted_offers', 'title' => '', 'status' => 'active' , 'join' => 'and' , 'value' => '' , 'option' => '1' , 'options' =>array(array('value' => '1' , 'label' => 'True' ), array('value' => '0' , 'label' => 'False')));

			if($app_cat == "submitted" && !in_array($has_submitted, $ft['description'])){
				array_push($ft['description'], $has_submitted);
				$click_through = "Applications Submitted";
			}

			if($app_cat == "offers" && !in_array($has_offer, $ft['description'])){
				array_push($ft['description'], $has_offer);
				$click_through = "Offers Made";
			}


			if($app_cat == "accepted" && !in_array($accepted_offers, $description)){
				array_push($ft['description'], $accepted_offers);
				$click_through = "Offers Accepted";
			}

			$this->filter = $ft;
		}

		if($_GET['cohort']){
			$this->current_cohort = $_GET['cohort'];
		}

		//Prep the applicants screen
		$prep_args = array(
			"extra_applicants_args"=>array('stage_id'=> $current_stage['id'], 'cohort'=>$this->current_cohort ),
			"school_id"=>$this->school_info['id'],
			"filter"=>$this->filter,
			"filter_page_name"=>"stage_applicants"
		);


        //Check if the default columns exist
        $columns_args = array('page'=>'stage_applicants');
        $page_columns = $form_templates->get_page_columns($columns_args);

        if(isset($_GET['temp_debug'])){
        	echo "<pre>".print_r($page_columns,1)."</pre>";exit();
        }

        if(!$page_columns){


            //check if the HEIAPPLY super admin user has a list
            $user_info = $users->get(array('search'=>'_superadmin','school_id'=>$this->school_info['id'],'order'=>'form_users.id ASC'));
            $user_info = $user_info[0];

            if($user_info['id']){
                $columns_args['user_id']=  $user_info['id'];

                $columns = $form_templates->get_page_columns($columns_args);
                $df = json_encode($columns);
            }else{
                $df = '["id","internal_reference","first_name","middle_name","email_address"]';
            }

            // echo '<pre>';
            // 	print_r($df);
            // 	exit();

            $column_args = array(
                'columns'=>$df,
                'page'=>'stage_applicants'
            );
            $form_templates->add_page_columns($column_args);
        }

        $prep_args['extra_applicants_args']['show_rejected'] = true;
		$prep_args['extra_applicants_args']['show_withdrawn'] = true;

		$results_prep = $students->prepare_applicants_screen($prep_args);
		$this->filter = $results_prep['filter'];


		//Links
		$back_link = $this->base_url('stages') . '/?cohort='. $_GET['cohort'] ?: $this->current_cohort;
		$back_link_title = "By Application Status";
		$main_title = $intake['title'];

		$data = array(
			'meta_title'=>'Applicants',
			'view_file'=>'stages/applicants',
			'type_id'=>$type_id,
			'stage' => $current_stage,
			'stage_order' => $stage_id,
			'cohort' => $this->current_cohort,
			'stages'=>$this->stages,
			'results'=>$results_prep['results'],
			'paginator'=>$paginator,
			'filter'=>$this->filter,
			'filters_args'=>$filters_args,
			'filter_page_name'=>'stage_applicants',
			'active_tab'=>$active_tab,
			'alert_message'=>$results_prep['alert_message'],
			'hide_export_button'=>true,
			'bulk_emails_offline' => true,
			'back_link'=>$back_link,
			'back_link_title'=>$back_link_title,
			'main_title'=>$main_title,
			'click_through' => $click_through,
            'page_default_colums' => $df,
            'cohorts'=>$this->cohorts

		);
		$this->view($this->layout,$data);

	}

	public function programmes($level_id='')
	{

		$users = new users;
		$courses = new Courses;
		$students = new Students;

		//Cohort
		if($_GET['cohort']){
			$this->current_cohort = $_GET['cohort'];
		}

		/** ===================================
		* Programmes Results
		====================================  */
		$courses_args = array("level_id"=>$this->uri_segement(2),"include_profiles_created"=>true,"include_total_submitted"=>true,"include_offers_made"=>true,"include_offers_accepted"=>true,'include_enrolled'=>true,'school_id'=>$this->school_info['id'],'order'=>'db232 ASC');		
		$courses_args['cohort'] = $this->current_cohort;
		$results = $courses->get($courses_args);

		$level = $courses->get_course_levels(array("id"=>$this->uri_segement(2),'school_id'=>$this->school_info['id']));



		$data = array(
			'meta_title'=>'Programmes',
			'view_file'=>'programmes/programes',
			'results'=>$results,
			'level'=>$level,
			'cohorts'=>$this->cohorts ,
			'cohort'=>$this->current_cohort 
		);
		$this->view($this->layout,$data);
	}

		/** ===================================
	* Messages 
	====================================  */
	public function messages($stage_id = '', $type=''){

		$users = new users;
		$courses = new Courses;
		$students = new Students;
		$messages = new CommunicationMessages;
		$form_templates = new FormTemplates;
		$current_stage = $form_templates->get_custom_form_core_stages(array("use_order"=> $stage_id));

		/** ===================================
		* Mark Selected items as read
		====================================  */
		if($_POST['action']=="mark_selected_as_read"){
			foreach ($_POST['messages_ids'] as $message) {
				$msg_args = array(
					'id'=>$message,
					'mark_as_read'=>true,
					'school_id'=>$this->school_info['id']
				);
				$messages->update_or_insert_note($msg_args);
			}

			header("Location: ".$this->base_url($this->uri_string())."?marking_done=1");
			exit();
		}



		/** ===================================
		* Bulk Reply
		====================================  */
		if($_POST['action']=="bulk_reply"){
			foreach($_POST['user_ids'] as $user_id) {
				$msg_args = array(
					'message'=>$_POST['bulk_message'],
					'student_id'=>$user_id,
					'template_name'=>$_POST['bulk_template_name'],
				);
				
	
				$messages->new_general_message($msg_args);
			}


			header("Location: ".$this->base_url($this->uri_string())."?bulk_reply_done=1");
			exit();
		}

		if($_GET['read']){
			$unread_messages_only = 'read';
		}else{
			$unread_messages_only = true;
		}

		/** ===================================
		* All message
		====================================  */
		if(!$type){
			$message = $messages->get(array('stage_id'=>$current_stage['id'] , 'no_replies'=>true,'paginate'=>true,'unread'=>$unread_messages_only,'school_id'=>$this->school_info['id']));
		}

		/** ===================================
		* General Messages
		====================================  */
		if($type=="general_questions"){
			$message = $messages->get(array('stage_id'=>$current_stage['id'] , 'no_replies'=>true,'type_id'=>1,'paginate'=>true,'unread'=>$unread_messages_only,'school_id'=>$this->school_info['id']));
		}

		/** ===================================
		* Submission Notification
		====================================  */
		if($type=="submission_notifications"){
			$message = $messages->get(array('stage_id'=>$current_stage['id'] , 'no_replies'=>true,'type_id'=>8,'paginate'=>true,'unread'=>$unread_messages_only,'school_id'=>$this->school_info['id']));
		}

		/** ===================================
		* File Upload Notifications
		====================================  */
		if($type=="file_upload_notifications"){
			$message = $messages->get(array('stage_id'=>$current_stage['id'] ,'no_replies'=>true,'type_id'=>9,'paginate'=>true,'unread'=>$unread_messages_only,'school_id'=>$this->school_info['id']));
		}

		


		$view = 'stages/messages';
		$data = array(
			'meta_title'=>'Messages',
			'view_file'=>$view,
			'stage' => $current_stage,
			'results'=>$message,
			'stage_order' => $stage_id,
			'cohort' =>$this->current_cohort,
			'stages'=>$this->stages,
			'type'=>$type,
            'cohorts'=>$this->cohorts
		);
		$this->view($this->layout,$data);

	}


	/** ===================================
	* Stats
	====================================  */
	public function stats($stage_id=''){

		
		$form_templates = new FormTemplates;
		$current_stage = $form_templates->get_custom_form_core_stages(array("use_order"=> $stage_id));
		$data = array(
			'meta_title'=>'Stats',
			'view_file'=>'stages/stats',
			'results'=>$results,
			'intake_id' => $intake_id,
			'stage_order' => $stage_id,
			'stages' => $this->stages,
			'stage' => $current_stage,
			'cohort' =>$_GET['cohort'] ?: $this->current_cohort,
			'intake'=>$intake,
            'cohorts'=>$this->cohorts
		);
		$this->view($this->layout,$data);

	}

		/** ===================================
	* Custom Selection
	====================================  */
	public function custom($stage_order=0){

		$applicants = new students;
		$courses = new courses;
		$students = new Students;
		$form_templates = new FormTemplates;

		
		$prep_args = array(
			"extra_applicants_args"=>array('stage_in'=> explode(',', $_GET['tiles'])),
			"school_id"=>$this->school_info['id'],
			"filter"=>$this->filter,
			"filter_page_name"=>"course_applicants"
		);


		$results_prep = $students->prepare_applicants_screen($prep_args);
		$this->filter = $results_prep['filter'];

		//Links
		$back_link = $this->base_url('stages') . '/?cohort='. $_GET['cohort'] ?: $this->current_cohort;
		$back_link_title = "Stages";
		$main_title = "Stages";

		$data = array(
			'meta_title'=>'Applicants',
			'view_file'=>'stages/custom',
			'type_id'=>$type_id,
			'intake_id' => $intake_id,
			'cohort' => $_GET['cohort'] ?: $this->current_cohort,
			'results'=>$results_prep['results'],
			'paginator'=>$paginator,
			'filter'=>$this->filter,
			'filters_args'=>$filters_args,
			'filter_page_name'=>'custom_stages',
			'active_tab'=>$active_tab,
			'alert_message'=>$results_prep['alert_message'],
			'hide_export_button'=>true,
			'back_link'=>$back_link,
			'back_link_title'=>$back_link_title,
			'main_title'=>$main_title,
			'click_through' => $click_through
		);
		$this->view($this->layout,$data);

	}

}
 ?>
