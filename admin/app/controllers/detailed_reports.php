<?php
/**
// * Created by PhpStorm.
// * User: andrew
// * Date: 9/23/20
// * Time: 09:02 AM
// */

class detailed_reports extends Controller
{
    private $date_format;
    private $reports;
    private $nav_parents;
  /** @var $product - Can be MRN or HEIApply */
    private $product;
  /** @var $main_ug - Can be 75 for MRN or 1 for HEIApply */
    private $main_ug;
    public function __construct(){
        parent::__construct(); 
        if(!$this->permissions->menu('charts_&_reports', 'new_reporting_suite')){
            $data = array(
                'meta_title'=>'Access Denied',
                'view_file'=>'error_pages/permissions',
            );
            $this->view($this->layout,$data);
            exit();
        }
        $this->layout = 'layout/reports_master';
        global $db;
        $school_type = pull_field("form_schools", "db30", "WHERE id={$_SESSION['usergroup']}");
        if (is_numeric(strpos(",$school_type,", ",12,"))){
            $this->main_ug = 75;
            $this->product = 'MRN';
            $product_id = 3;
        }else{
            $this->main_ug = 1;
            $this->product = 'HEIApply';
            $product_id = 1;
        }
        if($_SESSION['usergroup']!=1) {
            $this->nav_parents = $db->rawQuery("SELECT id,db68345 as title FROM core_report_product_nav WHERE (rec_archive is null OR rec_archive ='') AND usergroup IN ($_SESSION[usergroup]) AND db68342 = {$product_id} ORDER BY db68348");
            $parent_nav_ids = pull_field("core_report_product_nav", "GROUP_CONCAT(id)", "WHERE (rec_archive is null OR rec_archive ='') AND usergroup IN ($_SESSION[usergroup],$this->main_ug) ORDER BY db68348");
            if(!empty($parent_nav_ids)){
                $sql = "SELECT id, username_id,
                        db850 as title,
                        db68351 as short_title,
                        db68351 as url_name,
                        db68474 as parent,
                         db851 as description,
                         db14909 as link,
                         db1287 as tool,
                         db14895 as link_field,
                         db1285 as icon,
                         db14903 as chart_title,
                         db14904 as chart_description,
                         db1288 as button_name,
                         db1289 as permissions
                         FROM core_reports 
                        WHERE (rec_archive is null OR rec_archive ='') AND CONCAT(',',db1289,',') LIKE '%,$_SESSION[ulevel],%' 
                        AND usergroup IN ($_SESSION[usergroup]) AND db68474 IN ($parent_nav_ids)  ORDER BY db68354";
				$detailed_report = $db->rawQuery($sql);
				if(empty($detailed_report)){
					$sql = "SELECT id, username_id,
                        db850 as title,
                        db68351 as short_title,
                        db68351 as url_name,
                        db68474 as parent,
                         db851 as description,
                         db14909 as link,
                         db1287 as tool,
                         db14895 as link_field,
                         db1285 as icon,
                         db14903 as chart_title,
                         db14904 as chart_description,
                         db1288 as button_name,
                         db1289 as permissions
                         FROM core_reports
                        WHERE (rec_archive is null OR rec_archive ='') AND CONCAT(',',db1289,',') LIKE '%,$_SESSION[ulevel],%'
                        AND usergroup IN ($this->main_ug) AND db68474 IN ($parent_nav_ids)  ORDER BY db68354";
					$detailed_report = $db->rawQuery($sql);
				}
                $this->reports =$detailed_report;
            }else{
                $this->reports = [];
            }
        }else{
            $parents = pull_field("system_table", "figures"," WHERE db_field_name='db73262'");
            $parents = explode(',',$parents) ;
            $this->nav_parents = array_map(function ($item){ return ['id'=>$item,'title' => str_replace('_', ' ', ucwords($item))]; }, $parents);

            $sql = "SELECT id, username_id,
                        db46324 as title,
                        db73259 as short_title,
                        db73268 as url_name,
                        db73262 as parent,
                         db46325 as description
                         FROM form_internal_report
                        WHERE (rec_archive is null OR rec_archive ='')";
            $this->reports = $db->rawQuery($sql);
        }
        dev_debug($sql);
        $my_date_format = get_preferred_date_format();
        dev_debug("Date Format: $my_date_format");
        if ($my_date_format=='d/m/Y') {
            $this->date_format = 'dd/mm/yy';
        }
        elseif ($my_date_format=='m/d/Y') {
            $this->date_format = 'mm/dd/yy';
        }
        elseif ($my_date_format=='Y/m/d') {
            $this->date_format = 'yy/mm/dd';
        }
        elseif ($my_date_format=='Y-m-d') {
            $this->date_format = 'yy-mm-dd';
        }
        else {
            $this->date_format = 'dd/mm/yy';
        }


    }
    public function index($url_name=false)
	{
	    if($_SESSION['usergroup'] == 1){return $this->get_internal_projects($url_name);}
        $crm = new InternalCrm();
        $model = new DetailedReport();
        $filters = new Filter;
        //            defaulting to current cohort
        if(!$url_name){
            if ($this->product=="MRN") {
                $date_fields = [
                    ["date_field"=>"sis_profiles.date","title"=>"Date Created"],
                    ["date_field"=>"sis_profiles.rec_lstup","title"=>"Last Updated Date"],
                ];
            } else {

                $date_fields = [
                    ["date_field"=>"date","title"=>"Date Created"],
                    ["date_field"=>"rec_lstup","title"=>"Last Updated Date"],
                ];
            }
        }else{
            $dates_data  = pull_field("core_reports","db87251","WHERE db68351 = '$url_name' AND usergroup IN ($_SESSION[usergroup])");
            if (empty($dates_data)){
                $dates_data  = pull_field("core_reports","db87251","WHERE db68351 = '$url_name' AND usergroup IN ($this->main_ug)");
            }
            $date_fields = json_decode($dates_data, 1);
        }

        if(empty($_REQUEST['year']) && empty($_REQUEST['time_frame']) && empty($_REQUEST['time_range'])){
            $cohort = pull_field("form_schools", "db36", " WHERE id={$_SESSION['usergroup']}");
            $_REQUEST['date_field'] = $date_fields[0]['date_field'];
            if (!empty($cohort)){
                $_REQUEST['year'] = $cohort;
                $selected_year = $cohort;
            }else{
                $_REQUEST['year'] = date('Y');
                $selected_year = date('Y');
            }
        }
        if(!$url_name){
            $model->set_time_sql($url_name);
                $year = $_REQUEST['year'];
                $current_year = date("Y");
                $months_from_january = intval(date("m"));
                if($current_year-$year==0){
                    $months_from_now = 0;
                    $num_of_months = $months_from_january;
                }elseif($current_year-$year<0){
                    $months_from_now = 0;
                    $num_of_months = 0;
                }else{
                    $months_from_now = $months_from_january + (($current_year-($year+1))*12);
                    $num_of_months = 12;
                }

            $dash_stats = $model->get_dashboard_stats(["isMRN" => ($this->product=="MRN"?"yes":"no"), 'num_of_months'=>$num_of_months, 'months_from_now'=> $months_from_now]);

            $data = array(
                'meta_title' => 'Reports',
                'view_file' => 'detailed_reports/index',
                'enabled' => in_array($_SESSION['uid'],explode(",", pull_field("form_internal_teams", "db50639", "Where id=6"))),
                'report_type' => 'overview',
                'results' => [['test'=>0]],
                'filter' => [],
                'reports' => $this->reports,
                'new_reports' => 'yes',
                'nav_parents' => $this->nav_parents,
                'dash_stats' => $dash_stats,
                'isMRN' => $this->product=="MRN"?"yes":"no",
                'display_date_filter' => true,
                'date_fields' => $date_fields,
                'years' => $model->get_years(),
                'selected_year' => $cohort,
                'date_format' => $this->date_format,
            );
        }else{
            $args = ['url_name' => $url_name];
//        1. Establish report type
            $report_type = pull_field("core_reports", "db63844", "WHERE db68351 = '$url_name' AND usergroup IN ($_SESSION[usergroup])");
			if (empty($report_type)){
				$report_type = pull_field("core_reports", "db63844", "WHERE db68351 = '$url_name' AND usergroup IN ($this->main_ug)");
			}
//        2. Set Up Filter
            if(is_countable($this->filter['description']) && count($this->filter['description'])){
                $args['filter_sql'] = $filters->description_to_sql($this->filter['description']);
            }
            $_SESSION['last_filter_sql_for_'.$url_name.'_report'] = $args['filter_sql'];
            $model->set_time_sql($url_name);
//        3. Get report data by report type
            switch ($report_type){
                case "list":
	                dev_debug('list');
	                $results = $model->get_list_view_data($args);
                    $view = "list";
                    break;
                case "time":
                    $year = $_REQUEST['year'];
                    $current_year = date("Y");
                    $months_from_january = intval(date("m"));
                    if($current_year-$year==0){
                        $months_from_now = 0;
                        $num_of_months = $months_from_january;
                    }elseif($current_year-$year<0){
                        $months_from_now = 0;
                        $num_of_months = 0;
                    }else{
                        $months_from_now = $months_from_january + (($current_year-($year+1))*12);
                        $num_of_months = 12;
                    }
                    $args['num_of_months'] = $num_of_months;
                    $args['months_from_now'] = $months_from_now;

                    $results = $model->get_line_chart_data($args);
                    $view = "line";
                    break;
                case "aggregated_list":
	                dev_debug('aggregated_list');
	
	                $results = $model->get_custom_aggregated_data($args);
                    $view = "aggregated_list";
                    break;
                default:
	                dev_debug('default');
	
	                if(!empty($_REQUEST['comparing_periods'])) {
						dev_debug('comparing_periods');
                        $results = $this->get_comparisons($args);
                    }else{
						dev_debug('not_comparing_periods');
                        $results = $model->get_aggregated_data($args);
                        dev_debug("COUNT results" . count($results));
                    }
                    $view = "aggregated";
                    break;
            }

            $filters = $this->get_filters($url_name);

            if(!$results){
                $data = array(
                    'meta_title'=>'Access Denied',
                    'view_file'=>'error_pages/permissions',
                );
                $this->view($this->layout,$data);
                exit();
            }
            if(!empty($_REQUEST['json'])){
                dev_debug("JSON IN REQUEST" );
                echo json_encode($results);
                exit();
            }

            dev_debug("detailed reports here" );
            $dbh = get_dbh();
            $report_info_sth = $dbh->prepare("SELECT * FROM core_reports WHERE db68351 = '$url_name' AND usergroup IN ($_SESSION[usergroup])");
            $report_info_sth->execute();
            $report_info = $report_info_sth->fetchAll(2)[0];
			if (empty($report_info)){
				$report_info_sth = $dbh->prepare("SELECT * FROM core_reports WHERE db68351 = '$url_name' AND usergroup IN ($this->main_ug)");
                $report_info_sth->execute();
                $report_info = $report_info_sth->fetchAll(2)[0];
			}
            dev_debug("detailed reports here1" );
            $data = array(
                'meta_title' => pull_field("core_reports", "db850", "WHERE db68351 = '$url_name' AND usergroup='$_SESSION[usergroup]'"),
                'meta_description' => pull_field("core_reports", "db851", "WHERE db68351 = '$url_name' AND usergroup='$_SESSION[usergroup]'"),
                'view_file' => "detailed_reports/$view",
                'report_type' => $report_type,
                'results' => $results,
                    'new_reports' => 'yes',
                    'display_chart'=>$report_info["db14906"] == "yes",
                    'display_filter'=> $report_info["db1286"] == "yes",
                    'display_date_filter'=>$report_info["db87248"] == "yes",
                    'date_fields'=> $date_fields,
                    'filter'=>$this->filter,
                'dimensions' => $filters['dimensions'],
                'filter_types' => $filters['types'],
                'filter_page_name'=> $results['db14895'],
                    'reports' => $this->reports,
                    'years' => $model->get_years(),
                    'selected_year' => $selected_year,
                    'nav_parents' => $this->nav_parents,
                    'report_id' => $url_name,
                'date_format' => $this->date_format,
	            'show_export_button'=> $report_info["db15883"]
            );
            dev_debug(print_r($data,true));
            dev_debug("detailed reports here2" );
        }

        $this->view($this->layout, $data);
	}
    public function get_internal_projects($url_name=false)
	{

        $model = new DetailedReport();
        $filters = new Filter;
        if(!$url_name){
            $dash_stats = $model->get_dashboard_stats(['num_of_months'=>6, "isMRN" => ($this->product=="MRN"?"yes":"no")]);

            $data = array(
                'meta_title' => 'Reports',
                'view_file' => 'detailed_reports/index',
                'enabled' => in_array($_SESSION['uid'],explode(",", pull_field("form_internal_teams", "db50639", "Where id=6"))),
                'report_type' => 'overview',
                'reports' => $this->reports,
                'new_reports' => 'yes',
                'nav_parents' => $this->nav_parents,
                'dash_stats' => $dash_stats,
                'date_format' => $this->date_format,
            );
        }else{
            $args = ['url_name'=> $url_name];
            $results = $model->get_custom_aggregated_data($args);
            if(!$results){
                $data = array(
                    'meta_title'=>'Access Denied',
                    'view_file'=>'error_pages/permissions',
                );
                $this->view($this->layout,$data);
                exit();
            }
            if(!empty($_REQUEST['json'])){
                echo json_encode($results);
                exit();
            }
            $data = array(
                'meta_title' => pull_field("form_internal_report", "db46324", "WHERE db73268 = '$url_name'"),
                'view_file' => "detailed_reports/aggregated_list",
                'report_type' => 'aggregated_list',
                'filter'=>$this->filter,
                'dimensions' => [],
                'filter_types' => [],
                'filter_page_name'=> $results['db14895'],
                'results' => $results,
                'new_reports' => 'yes',
                'reports' => $this->reports,
                'nav_parents' => $this->nav_parents,
                'report_id' => $url_name,
                'date_format' => $this->date_format,
            );
        }

        $this->view($this->layout, $data);
	}
    public function key_stats()
	{
        $data = array(
            'meta_title' => 'Key Statistics',
            'reports' => $this->reports,
            'new_reports' => 'yes',
            'nav_parents' => $this->nav_parents,
            'view_file' => "detailed_reports/key_stats",
            'date_format' => $this->date_format,
        );
        $this->view($this->layout, $data);
	}
	public function get_tab_data($id){
        $model = new DetailedReport();
        if(isset($_GET['direct'])){
            switch ($_REQUEST['parent_id']){
                case 375:
                    echo json_encode($model->get_course_completion_stats());
                    exit();
                    break;
                default:
                    break;
            }

        }
        return $model->get_aggregated_data($id);
    }
    public function ilp_analysis()
    {

        list($title,$description) = pull_field("core_reports", "CONCAT(db850,'|||||',db851)", "WHERE db68351='ilp_analysis' AND usergroup='$_SESSION[usergroup]");
        if (empty($title)) {
            $title = 'ILP Analysis Report';
        }
        if (empty($description)) {
            $description = "High 60-70 <br/> Medium 40 -59 <br/> Low 14-39";
        }
        $data = array(
            'meta_title' => $title,
            'description' => $description,
            'view_file' => 'detailed_reports/custom/ilp_analysis',
            'filter_page_name' => 'ILP Analysis Report',
            'reports' => $this->reports,
            'nav_parents' => $this->nav_parents,
            'report_id' => 381,
            'date_format' => $this->date_format,
        );
        $this->view($this->layout, $data);
    }

    public function wemwbs_analysis()
    {
        list($title,$description) = pull_field("core_reports", "CONCAT(db850,'|||||',db851)", "WHERE db68351='wemwbs_analysis' AND usergroup='$_SESSION[usergroup]");
        if (empty($title)) {
        $title = 'WEMWBS Analysis Report';
        }
        if (empty($description)) {
            $description = "High 60-70 <br/> Medium 40 -59 <br/> Low 14-39";
        }

        $data = array(
            'meta_title' => $title,
            'description' => $description,
            'view_file' => 'detailed_reports/custom/wemwbs_analysis',
            'filter_page_name' => 'WEMWBS Analysis Report',
            'reports' => $this->reports,
            'nav_parents' => $this->nav_parents,
            'report_id' => 381,
            'date_format' => $this->date_format,
        );
        $this->view($this->layout, $data);
    }

    public function swemwbs_analysis()
    {
        list($title,$description) = pull_field("core_reports", "CONCAT(db850,'|||||',db851)", "WHERE db68351='swemwbs_analysis' AND usergroup='$_SESSION[usergroup]");
        if (empty($title)) {
            $title = 'SWEMWBS Analysis Report';
        }
        if (empty($description)) {
            $description = "High 27.5  - 35 <br/> Medium < 27.5 - >= 19.5 <br/> Low 7- < 19.5";
        }
        $data = array(
            'meta_title' => $title,
            'description' => $description,
            'view_file' => 'detailed_reports/custom/swemwbs_analysis',
            'filter_page_name' => 'SWEMWBS Analysis Report',
            'reports' => $this->reports,
            'nav_parents' => $this->nav_parents,
            'report_id' => 381,
            'date_format' => $this->date_format,
        );
        $this->view($this->layout, $data);
    }
    public function course_of_interest()
    {
        $data = array(
            'meta_title' => 'ILP Analysis Report',
            'view_file' => 'detailed_reports/custom/course_of_interest',
            'filter_page_name' => 'ILP Analysis Report',
            'reports' => $this->reports,
            'nav_parents' => $this->nav_parents,
            'report_id' => 381,
            'date_format' => $this->date_format,
        );
        $this->view($this->layout, $data);
    }
    public function course_of_interest_report()
    {
        $data = array(
            'meta_title' => 'All ILP Report',
            'view_file' => 'detailed_reports/custom/course_of_interest',
            'reports' => $this->reports,
            'new_reports' => 'yes',
            'nav_parents' => $this->nav_parents,
            'report_id' => 382,
            'date_format' => $this->date_format,
        );
        $this->view($this->layout, $data);
    }
    public function summary_report()
    {
        $data = array(
            'meta_title' => 'Summary Report',
            'view_file' => 'detailed_reports/custom/summary_report',
            'reports' => $this->reports,
            'nav_parents' => $this->nav_parents,
            'report_id' => 382,
            'date_format' => $this->date_format,
        );
        $this->view($this->layout, $data);
    }
    public function summary_report_end_date()
    {
        $data = array(
            'meta_title' => 'Summary Report',
            'view_file' => 'detailed_reports/custom/summary_report_end_date',
            'reports' => $this->reports,
            'nav_parents' => $this->nav_parents,
            'report_id' => 382,
            'date_format' => $this->date_format,
        );
        $this->view($this->layout, $data);
    }
    public function course_demand_from_learning_goals()
    {
        $data = array(
            'meta_title' => 'All ILP Report',
            'view_file' => 'detailed_reports/course_of_interest',
            'reports' => $this->reports,
            'nav_parents' => $this->nav_parents,
            'report_id' => 372,
            'date_format' => $this->date_format,
        );
        $this->view($this->layout, $data);
    }
    public function student_course_completion_report()
    {
        $data = array(
            'meta_title' => 'Course Completion Report',
            'view_file' => 'detailed_reports/custom/course_completion',
            'reports' => $this->reports,
            'nav_parents' => $this->nav_parents,
            'report_id' => 375,
            'date_format' => $this->date_format,
        );
        $this->view($this->layout, $data);
    }
    function get_filters($report){
        $show_filters = pull_field("core_reports", "db1286", " WHERE db68351 = '$report' AND usergroup IN ({$_SESSION['usergroup']},{$this->main_ug})");
        if ($show_filters == 'yes'){
            $filter_data = pull_field("core_reports", "db16142", " WHERE db68351 = '$report' AND usergroup IN ({$_SESSION['usergroup']},{$this->main_ug})");
            $filter_data = json_decode($filter_data, 1);
            $filter_fields = $filter_data['fields'];
            $extras = $filter_data['extras'];
            $filters = new Filter;
            $setup = $filters->setup_view_filters($filter_fields, $extras);
            return ['types' => $setup[0], 'dimensions'=>$setup[1]];
        }else {
            return false;
        }
    }

    function get_comparisons($args){
        $num_periods = $_REQUEST['selected_number_of_periods'];
        $period_length = $_REQUEST['selected_comparison_period']; //period length in months
        $start_date = $_REQUEST['comparison_start_date'];
        $counter = 1;
        $result_object = ['categories'=>[], 'series'=>[]];
        $results=[];
        $my_series=[];
        while ($counter <= $num_periods) {
            $from = date("Y-m-d",strtotime(date("Y-m-d",strtotime($start_date)) . "-" .(intval($counter) * intval($period_length))." month"));
            $to = date("Y-m-d",strtotime($from . "+" .intval($period_length)." month"));
            $_REQUEST['time_sql'] = " AND DATE({$_REQUEST['date_field']}) between '{$from}' AND '{$to}' ";
            $model = new DetailedReport();
            $res = $model->get_aggregated_data($args);
            $period = date("M-y", strtotime($from)). " - ". date("M-y", strtotime($to));
            $results[]= $res['series'];

            array_unshift($result_object['categories'], $period);
            foreach ($res['series'] as $series){
                $series_name = $series['name'];
                if(!is_numeric(array_search($series_name, array_column($my_series, 'name')))){
                    array_push($my_series, ['name'=>$series_name]);
                }
            }

            $counter++;
        }
//        echo "<b>The categories array == </b>".json_encode($result_object['categories'])."</br></br></br>";
//        echo "<b>The result array == </b>".json_encode($results)."</br></br></br>";
//        echo "<b>The my_series array == </b>".json_encode($my_series)."</br></br></br>";
        $table = [];

        foreach ($my_series as $series){
            $series['data'] =[];
            $index = 0;
            $cats = [];
            for ($i=count($results); $i>=1; $i--) {
                $period = $results[$i-1];
                $series_index = array_search($series['name'], array_column($period, 'name'));
//                echo "<b>The series == </b>".$series['name']."<br>";
//                echo "<b>The period == </b>".json_encode($series_index)."<br>";
//                echo $series['name']. " -----> " .intval($period[$series_index]['y'])."<br>";
                array_push($series['data'], is_numeric($series_index)?intval($period[$series_index]['y']):0);
                $cats[$result_object['categories'][$index]] = is_numeric($series_index)?intval($period[$series_index]['y']):0;
                $index++;
            }
            $cats['label'] = $series['name'];
            array_push($table, $cats);
            array_push($result_object['series'], $series);
        }
        $table_headings = $result_object['categories'];
        array_unshift($table_headings, "Categories");
        $result_object['headings']= $table_headings;
        $result_object['table']= $table;
        return $result_object;
    }
    function get_ajax_data($url_name){
        $model =  new DetailedReport();
        $args = ['url_name'=> $url_name];
        $model->set_time_sql($url_name);
        $report_type = pull_field("core_reports", "db63844", "WHERE db68351 = '$url_name' AND usergroup IN ($_SESSION[usergroup])");

        if($url_name!="overview" && $report_type!="time"){
        if(!empty($this->filter['description'])){
            $args['filter_sql'] = prepare_filter_sql($this->filter['description']);
        }
        $_SESSION['last_filter_sql_for_'.$url_name.'_report'] = $args['filter_sql'];
        if(!empty($_REQUEST['clicked_column'])){
            echo json_encode($model->get_clickthrough_data($args));
            exit();
        }

            echo json_encode($model->get_ajax_data($args));
        }
        else{
            $args = ['url_name'=> $url_name, "isMRN" => ($this->product=="MRN"?"yes":"no"), 'months_from_now'=>0];
            $months = 12;
            if(!empty($_REQUEST['time_frame'])){
                $months = $_REQUEST['time_frame'];
            }
            if(!empty($_REQUEST['time_range'])){
                $dates = explode(" AND ", str_replace("'", "", $_REQUEST['time_range']));
                $date_from = str_replace('/', '-', $dates[0]);
                $date_to = str_replace('/', '-', $dates[1]);
                $d1=new DateTime($date_from);
                $d2=new DateTime($date_to);
                $Months = $d2->diff($d1);
                $months = (($Months->y) * 12) + ($Months->m)+2;
                $now = new DateTime();
                $diff = $now->diff($d2);
                $months_from_now = (($diff->y) * 12) + ($diff->m);
                $args['months_from_now'] = $months_from_now;

            }
            if(!empty($_REQUEST['year'])){
                $year = $_REQUEST['year'];
                $current_year = date("Y");
                $months_from_january = intval(date("m"));
                if($current_year-$year==0){
                    $months_from_now = 0;
                    $months = $months_from_january;
                }elseif($current_year-$year<0){
                    $months_from_now = 0;
                    $months = 0;
                }else{
                    $months_from_now = $months_from_january + (($current_year-($year+1))*12);
                    $months = 12;
                }
                $args['months_from_now'] = $months_from_now;
            }
            $args['num_of_months'] = $months;
            if($report_type=="time"){ 
                echo json_encode($model->get_line_chart_data($args));
            }else{
                echo json_encode($model->get_dashboard_stats($args));
            }
        }
        exit();
    }
}

