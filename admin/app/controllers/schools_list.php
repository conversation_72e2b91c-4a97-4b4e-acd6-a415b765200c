<?php 

/**
* 
*/
class schools_list extends Controller
{



	public function index($school_id='')
	{

		if($this->school_info['id']!=1){
			exit();
		}
		
		if($school_id){

			/** ===================================
			* Get single school info
			====================================  */
			$schools = new Schools;
			$shool_args = array('id'=>$school_id);
			$entry = $schools->get($shool_args);
			$engagement_stats = $schools->get_engagement_stats($shool_args);
			/** ===================================
			* Total Applicants Count
			====================================  */
			$students = new Students;
			$students_args = array("total"=>true,"school_id"=>$school_id);
			$total_applicants = $students->get($students_args);

			/* Total Applicants Count
			====================================  */
			$students = new enquirieModel();
			$students_args = array("usergroup"=>$school_id);
			$total_enquiries = $students->get_total_enquiries($students_args);


			/** ===================================
			* Total Users Count
			====================================  */
			$users = new Users;
			$users_args = array("total"=>true,"school_id"=>$school_id);
			$total_users = $users->get($users_args);

			/** ===================================
			* Total Email Templates Count
			====================================  */
			$email_templates = new EmailTemplates;
			$email_templates_args = array("total"=>true,"school_id"=>$school_id);
			$total_email_templates = $email_templates->get($email_templates_args);

			/** ===================================
			* Total Routes Count
			====================================  */
			$routes = new Routes;
			$routes_args = array("total"=>true,"school_id"=>$school_id);
			$routes_total = $routes->get($routes_args);

			/** ===================================
			* Total Campaigns Count
			====================================  */
			$campaigns = new CampaignsModel;
			$campaigns_args = array("total"=>true,"school_id"=>$school_id);
			$campaigns_total = $campaigns->get($campaigns_args);


			/** ===================================
			* Total Emails Count
			====================================  */
			$emails = new Emails;
			$emails_args = array("total"=>true,"school_id"=>$school_id);
			$emails_total = $emails->get($emails_args);


			/** ===================================
			* Total Uploads Count
			====================================  */
			$uploads = new FilesClass;
			$uploads_args = array("total"=>true,"school_id"=>$school_id);
			$uploads_total = $uploads->get($uploads_args);


			// 			echo '<pre>';
			// print_r($routes_total);
			// echo '</pre>';
			

			// print_r($'entry'=>$entry);
			//exit();

			/** ===================================
			* Total Applicants Count
			====================================  */

			/** ===================================
			* Results
			====================================  */
			$data = array(
				'meta_title'=>'Schools',
				'view_file'=>'schools/details',
				'form'=>$form,
				'entry'=>$entry,
				'total_applicants'=>$total_applicants,
				'total_enquiries'=>$total_enquiries[0]['count'],
				'total_users'=>$total_users,
				'engagement_stats'=>$engagement_stats,
				'total_email_templates'=>$total_email_templates,
				'routes_total'=>$routes_total,
				'campaigns_total'=>$campaigns_total,
				'emails_total'=>$emails_total,
				'uploads_total'=>$uploads_total,
			);
			$this->view($this->layout,$data);

		}else{
			$schools_model = new Schools;
			$filters = new Filter;
			$form_templates = new FormTemplates;
			$fieldClass = new Fields;
			$school_types =  $schools_model->get_school_types();
			// [pageID => masked]
			$pages = [5=>false];

			$school_args =[
				'school_id'=> $this->school_info['id'],
				'paginate'=> !empty($_GET['export']) ? false : true ,
				'search' => $_POST['search']?:'' ,
				'order' => !empty($_GET['order']) ?$_GET['order']:'',
				'list_view' => true,
			];

			$active_columns = $fieldClass->select_fields(['page_id' => $pages]);
			$a_columns=[];
//			foreach($active_columns as $column){
//			  foreach($column as $col) $a_columns[] = $col['db_field_name'];
//			}
//			$field_indices = $a_columns;
			$this->filter_fields = [];
			$this->filter_fields[] = 'school_id';
			foreach($active_columns as $column){
				foreach ($column as $col) {
					$this->filter_fields[] = $col['db_field_name'];
				}
			}

			$school_type_options = array_map(function($item){
				return ['value'=>$item['id'], 'label'=> $item['name']];
			}, $school_types);
			$this->extras['db30'] = [
				'title' => 'School Type',
				'options' => $school_type_options
			];
			$this->extras['school_id'] = [
				'title' => 'School ID',
				'type' => 'text'
			];

			$school_args['filter_sql'] = $filters->description_to_sql($this->filter['description']);
			$setup = $filters->setup_view_filters($this->filter_fields, $this->extras);

			$results = $schools_model->get($school_args);


			$active_fields = $form_templates->get_page_columns(array('page' => 'schools_list', 'filter_id' => $this->filter['id']));

			$fields = array_keys($results[0]);
			$dimensions = $setup[1];
			foreach($dimensions as $k => $group){
				foreach($group as $key => $fieldss){
					foreach ($fieldss as $x => $fd){
						$dimensions[$k][$key][$x]['active'] = in_array($fd['id'], $active_fields) ? 1 : 0;
					}
				}
			}
			$all_fields = $fieldClass->dynamic_data_extract($fields,$active_fields,array_values($active_columns));


			/** ===================================
			 * Export Data
			====================================  */
			if($_GET['export']){
				$this->export_schools($results,$all_fields);
	     	}

			/** ===================================
			* Results
			====================================  */
			$data = array(
				'meta_title'=>'Schools',
				'view_file'=>'schools/index',
				'school_types'=>$school_types,
				'results'=>$results,
				'all_fields' =>$all_fields,
				'filter'=>$this->filter,
				'dimensions' => $dimensions,
				'filter_types' => $setup[0],
				'filter_page_name'=>'schools_list',
				'hide_edit_columns' => false,
			);
			$this->view($this->layout,$data);

		}
		

	}
	public function risky_customers($school_id='')
	{

		if($this->school_info['id']!=1){
			exit();
		}

		if($school_id){

			/** ===================================
			* Get single school info
			====================================  */
			$schools = new Schools;
			$shool_args = array('id'=>$school_id);
			$entry = $schools->get($shool_args);
			$engagement_stats = $schools->get_engagement_stats($shool_args);
			/** ===================================
			* Total Applicants Count
			====================================  */
			$students = new Students;
			$students_args = array("total"=>true,"school_id"=>$school_id);
			$total_applicants = $students->get($students_args);

			/* Total Applicants Count
			====================================  */
			$students = new enquirieModel();
			$students_args = array("usergroup"=>$school_id);
			$total_enquiries = $students->get_total_enquiries($students_args);


			/** ===================================
			* Total Users Count
			====================================  */
			$users = new Users;
			$users_args = array("total"=>true,"school_id"=>$school_id);
			$total_users = $users->get($users_args);

			/** ===================================
			* Total Email Templates Count
			====================================  */
			$email_templates = new EmailTemplates;
			$email_templates_args = array("total"=>true,"school_id"=>$school_id);
			$total_email_templates = $email_templates->get($email_templates_args);

			/** ===================================
			* Total Routes Count
			====================================  */
			$routes = new Routes;
			$routes_args = array("total"=>true,"school_id"=>$school_id);
			$routes_total = $routes->get($routes_args);

			/** ===================================
			* Total Campaigns Count
			====================================  */
			$campaigns = new CampaignsModel;
			$campaigns_args = array("total"=>true,"school_id"=>$school_id);
			$campaigns_total = $campaigns->get($campaigns_args);


			/** ===================================
			* Total Emails Count
			====================================  */
			$emails = new Emails;
			$emails_args = array("total"=>true,"school_id"=>$school_id);
			$emails_total = $emails->get($emails_args);


			/** ===================================
			* Total Uploads Count
			====================================  */
			$uploads = new FilesClass;
			$uploads_args = array("total"=>true,"school_id"=>$school_id);
			$uploads_total = $uploads->get($uploads_args);


			// 			echo '<pre>';
			// print_r($routes_total);
			// echo '</pre>';


			// print_r($'entry'=>$entry);
			//exit();

			/** ===================================
			* Total Applicants Count
			====================================  */

			/** ===================================
			* Results
			====================================  */
			$data = array(
				'meta_title'=>'Schools',
				'view_file'=>'schools/details',
				'form'=>$form,
				'entry'=>$entry,
				'total_applicants'=>$total_applicants,
				'total_enquiries'=>$total_enquiries[0]['count'],
				'total_users'=>$total_users,
				'engagement_stats'=>$engagement_stats,
				'total_email_templates'=>$total_email_templates,
				'routes_total'=>$routes_total,
				'campaigns_total'=>$campaigns_total,
				'emails_total'=>$emails_total,
				'uploads_total'=>$uploads_total,
			);
			$this->view($this->layout,$data);

		}else{

			/** ===================================
			* Get all the Data
			====================================  */
			$schools = new Schools;
			$results = $schools->get_risky_clients();




			/** ===================================
			* Results
			====================================  */
			$data = array(
				'meta_title'=>'Schools',
				'view_file'=>'schools/risky',
				'form'=>$form,
				'results'=>$results
			);
			$this->view($this->layout,$data);

		}


	}

	private function export_schools($results,$all_fields){
		$export_data=[];

		foreach ($all_fields as $all_fieldskey => $all_fieldsvalue) {
			if ($all_fieldsvalue['active']==1) {
				$tittle[$all_fieldsvalue['title']]=$all_fieldsvalue['title'];
				$dbfields[$all_fieldsvalue['db_field_name']]=$all_fieldsvalue['db_field_name'];
				if (isset($all_fieldsvalue['options'])) {
					$field_options[$all_fieldsvalue['db_field_name']]=$all_fieldsvalue['options'];
				}

			}
		}
		$export_data[]=$tittle;
		//echo "<pre>".print_r($tittle,1)."</pre>";
		$final_results = $results;
		foreach ($final_results as $final_resultskey => $final_resultsvalue) {
			foreach ($final_resultsvalue as $final_resultsvaluekey => $final_resultsvaluevalue) {
				if (in_array($final_resultsvaluekey, $dbfields)) {
					if (isset($field_options[$final_resultsvaluekey])) {
						foreach ($field_options[$final_resultsvaluekey] as $optionskey => $optionsvalue) {
							if ($optionsvalue['value']==$final_resultsvaluevalue) {
								$final_results[$final_resultskey][$final_resultsvaluekey]=$optionsvalue['label'];
							}
						}
					}
				}

			}
		}

		foreach ($final_results as $final_resultskey => $final_resultsvalue) {
			$data_row=[];
			foreach ($all_fields as $all_fieldskey => $all_fieldsvalue) {
				if ($all_fieldsvalue['active']==1) {
					$data_row[$all_fieldsvalue['title']]=$final_resultsvalue[$all_fieldsvalue['db_field_name']];

				}
			}
			$export_data[]=$data_row;
		}
		$applicants2 = new Students;
		$applicants2->export_formated2($export_data,'csv','schoolss');
		exit();
	}

}
 ?>
