<?php

/**
 * 
 */
class online_courses extends Controller
{

    public function index($course_id = '', $modules = '', $module_id = '', $units = '', $unit_id = '', $quiz = '')
    {

        $OL = new OnlineLearning;
        $form_templates = new FormTemplates; 

        // attach survey questionaire to course module  unit
        if ($_POST['action'] == "attach_survey") {
            $data_args = array("db45504" => $course_id, 'db45505' => $module_id, 'db45506' => $unit_id, 'db45507' => $_POST['quiz_id']);
            $unit_info = $OL->update_or_insert_survey($data_args);
        }

        // attach survey questionaire to course module  unit
        if ($_POST['action'] == "delete_survey") {
            $data_args = array("id" => $_POST['quiz_id']);
            $OL->delete_survey($data_args);
        }
        $topics = [];
        $data_args = array("db45504" => $course_id, 'db45505' => $module_id, 'db45506' => $unit_id);
        $attached_quiz = $OL->get_attached_quiz($data_args);
        //get an array of all the course units with the survey id 463 attached to them i.e the survey with the 3 repeated questions

        //AFY 5/01/2023 removing this for the time being as there is more than 1 survey
        //$survey_id = 463;

        $survey_id = pull_field("inst_pages", "group_concat(page_id)", "where page_name = '' AND project = ''");
        $all_quiz_units_with_survey = $OL->get_units_with_survey($survey_id, $course_id);
        $all_languages = $OL->get_languages();
        if ($course_id) {

            /** ===================================
             * Add new Form
             * ====================================  */
            if ($_POST['action'] == "add_new_form") {
                $form_args = array(
                    'title' => $_POST['table_name'],
                    'table_name' => 'Unit ' . $unit_id,
                    'category_id' => $_POST['category'],
                    "system_abbrv" => 'inst'
                );


                $form_id = $form_templates->insert_update_form($form_args);

                //update the unit
                $unit_args = array(
                    'id' => $unit_id,
                    'quiz_id' => $form_id,
                    'action' => "update",
                );
                $unit_info = $OL->update_or_insert_unit($unit_args);

                header("Location: " . $this->base_url('online-courses/' . $course_id . '/modules/' . $module_id . '/units/' . $unit_id . '/quiz/' . $form_id));
                exit();
            }

            /** ===================================
             * Get course information
             * ====================================    */
            if ($course_id != "new") {
                $course_args = array("id" => $course_id, 'show_hidden_units' => true, 'raw_html' => true, 'no_default_excerpt' => true, 'school_id' => $this->school_info["id"], "get_core_courses" => true);
                $course_args['with_categories'] = true;
                $course_info = $OL->get_courses($course_args);
                $courses = new Courses;
                $topics = $courses->get_topics(array('school_id' => $this->school_info['id']));
                $recommended_topics = $courses->get_recommended_topics();
                if (!empty($course_info['selected_course'])) $course_info['topics'] = $courses->get_topics(array("course_id" => $course_info['selected_course']));
                $course_info['recommended_topics'] = $recommended_topics;
                $entry_json = json_encode($course_info);


                //Not found
                if (!$course_info['id']) {
                    $data = array(
                        'meta_title' => 'Not found',
                        'view_file' => 'error_pages/404',
                    );
                    $this->view($this->layout, $data);
                    exit();
                }

            } else {
                dev_debug("In NEW Course **$modules**");
                if ($modules) {
                    $language = $modules;
                    $language_info = $OL->get_languages(array('school_id' => $_SESSION['usergroup'], 'code' => $language));
                    $direction = $language_info['direction'];
                } else {
                    $language = 'EN';
                    $direction = 'ltr';
                }

                dev_debug("In NEW Course $language ** $direction");


                $dbh = get_dbh();
                $query = "SELECT id, db232 as title FROM core_courses WHERE usergroup=?";
                $stmt = $dbh->prepare($query);
                $stmt->execute([$_SESSION["usergroup"]]);
                $course_options = $stmt->fetchAll(2);

                $course_info = array('id' => 'new', 'language' => $language_info, 'language_direction' => $direction, 'course_options' => $course_options);
                $entry_json = json_encode($course_info);
                $courses = new Courses;
                $topics = $courses->get_topics(array('school_id' => $this->school_info['id']));
                $module_id = "";
            }
            $english_courses = $OL->english_language_courses(array('school_id' => $this->school_info['id']));
            /** ===================================
             * Get Module information
             * ====================================    */
            if ($module_id != "new") {
                foreach ($course_info['modules'] as $module_info) {
                    if ($module_info['id'] == $module_id) {
                        $module = $module_info;
                    }
                }
                $module_entry_json = json_encode($module);
            } else {
                $entry_info = array();
                $module_entry_json = json_encode($entry_info);
            }

            /** ===================================
             * Get Unit information
             * ====================================    */
            if ($unit_id != "new") {
                foreach ($course_info['modules'] as $module_info) {
                    foreach ($module_info['units'] as $unit_info) {
                        if ($unit_info['id'] == $unit_id) {
                            $unit = $unit_info;
                        }
                    }
                }
                $unit_entry_json = json_encode($unit);
            } else {
                $unit_info = array();
                $unit_entry_json = json_encode($unit_info);
            }

            /** ===================================
             * Get Quiz information
             * ====================================    */
            if ($quiz) {
                $form_templates = new FormTemplates;
                $fields = new Fields;

                $page_args = array("id" => $unit['quiz']['id'], "system_abbrv" => 'inst');
                $form = $form_templates->get($page_args);

                $field_types = $fields->field_types();

                $fields_list = array();
                foreach ($form['fields'] as $field) {
                    $finfo = $field;
                    $finfo['deleted'] = 0;
                    $fields_list[] = $finfo;
                }
                $form_json = json_encode($form);
                $fields_json = json_encode($fields_list);
                $field_types_json = json_encode($field_types);
            }

            /** ===================================
             * Add or Update Course
             * ====================================    */
            if ($_POST['save_course'] || $_POST['add_course']) {
                $this->re_order_courses(['id' => $course_info['id'], 'order' => $_POST['order']]);
                $courses_args = array(
                    'id' => $course_info['id'],
                    'title' => $_POST['title'],
                    'alternative_title' => $_POST['alternative_title'],
                    'description' => $_POST['description'],
                    'excerpt' => $_POST['excerpt'],
                    'colour' => $_POST['colour'],
                    'order' => $_POST['order'],
                    'parent_course' => $_POST['parent_course'],
                    'category' => $_POST['category'],
                    'group' => $_POST['group'],
                    'course_level' => $_POST['course_level'],
                    'next_unit_visibility' => $_POST['next_unit_visibility'],
                    'download_certificate' => $_POST['certificate'],
                    'featured' => $_POST['featured'],
                    'show_support_on_each_unit' => $_POST['show_support_on_each_unit'],
                    'show_feedback_on_each_unit' => $_POST['show_feedback_on_each_unit'],
                    'publish' => $_POST['publish'],
                    'rm_from_library' => $_POST['rm_from_library'],
                    'course_language' => $_POST['course_language'],
                    'course_link' => $_POST['course_link'],
                    'course_external_url'=>$_POST['course_external_url'],
                    'course_slug'=>$_POST['course_slug']
                );

                if ($_POST['save_course']) {
                    $courses_args['action'] = "update";
                } else {
                    $courses_args['action'] = "insert";
                }

                //Update/Add course
                $course = $OL->update_or_insert_course($courses_args);

                //Redirect to course if new
                if ($_POST['add_course']) {
                    echo '<meta http-equiv="refresh" content="0;URL=' . $this->base_url('online-courses/' . $course['id']) . '/?added_course=true">';
                    exit();
                } else {
                    echo '<meta http-equiv="refresh" content="0;URL=' . $this->base_url('online-courses/' . $course_info['id']) . '/?saved_course=true">';
                    exit();
                }
            }

            /** ===================================
             * Update Course Questionnaire settings
             * ====================================    */
            if ($_POST['save_course_questionnaire_settings']) {
                $questionnaire_type = $_POST['questionnaire_type'];
                if ($questionnaire_type == "1") {
                    $score_1_text = $_POST['type_1_score_1'];
                    $score_2_text = $_POST['type_1_score_2'];
                    $score_3_text = $_POST['type_1_score_3'];
                    $score_4_text = $_POST['type_1_score_4'];
                    $score_5_text = '';
                    $score_6_text = '';
                    $score_7_text = '';
                    $score_8_text = '';

                } else if ($questionnaire_type == "4") {
                    $score_1_text = $_POST['type_4_score_1'];
                    $score_2_text = $_POST['type_4_score_2'];
                    $score_3_text = $_POST['type_4_score_3'];
                    $score_4_text = '';
                    $score_5_text = '';
                    $score_6_text = '';
                    $score_7_text = '';
                    $score_8_text = '';
                } else if ($questionnaire_type == "5") {
                    $score_1_text = $_POST['type_5_score_1'];
                    $score_2_text = $_POST['type_5_score_2'];
                    $score_3_text = $_POST['type_5_score_3'];
                    $score_4_text = $_POST['type_5_score_4'];
                    $score_5_text = $_POST['type_5_score_5'];
                    $score_6_text = '';
                    $score_7_text = '';
                    $score_8_text = '';
                    $text_before_questionnaire = $_POST['type_5_before_text'];
                } else if ($questionnaire_type == "6") {
                    $score_1_text = $_POST['type_6_score_1'];
                    $score_2_text = $_POST['type_6_score_2'];
                    $score_3_text = $_POST['type_6_score_3'];
                    $score_4_text = $_POST['type_6_score_4'];
                    $score_5_text = '';
                    $score_6_text = '';
                    $score_7_text = '';
                    $score_8_text = '';
                    $text_before_questionnaire = $_POST['type_6_before_text'];
                } else {
                    $score_1_text = $_POST['conflict_difference_bigger_than_0'];
                    $score_2_text = $_POST['conflict_difference_equal_to_0'];
                    $score_3_text = $_POST['conflict_difference_equal_to_'];
                    $score_4_text = $_POST['conflict_difference_else'];
                    $score_5_text = $_POST['closeness_difference_less_than_0'];
                    $score_6_text = $_POST['closeness_difference_equal_to_0'];
                    $score_7_text = $_POST['closeness_difference_equal_to_1'];
                    $score_8_text = $_POST['closeness_difference_else'];
                }
                if (isset($_POST['closeness_questions'])) {
                    $closeness_questions = implode(',', $_POST['closeness_questions']);
                }
                if (isset($_POST['conflict_questions'])) {
                    $conflict_questions = implode(',', $_POST['conflict_questions']);
                }

                if (isset($_POST['anx_questions'])) {
                    $closeness_questions = implode(',', $_POST['anx_questions']);
                }
                if (isset($_POST['dep_questions'])) {
                    $conflict_questions = implode(',', $_POST['dep_questions']);
                }

                $courses_args = array(
                    'id' => $course_info['id'],
                    "questionnaire_type" => $_POST['questionnaire_type'],
                    "closeness_questions" => $closeness_questions,
                    "conflict_questions" => $conflict_questions,
                    "score_1_text" => $score_1_text,
                    "score_2_text" => $score_2_text,
                    "score_3_text" => $score_3_text,
                    "score_4_text" => $score_4_text,
                    "score_5_text" => $score_5_text,
                    "score_6_text" => $score_6_text,
                    "score_7_text" => $score_7_text,
                    "score_8_text" => $score_8_text,
                    "text_before_questionnaire" => $text_before_questionnaire,
                );


                if ($_POST['save_course_questionnaire_settings']) {
                    $courses_args['action'] = "update";
                }
                //Update/Add course
                $course = $OL->update_or_insert_course($courses_args);

                //Redirect to course if new
                if ($_POST['add_course']) {
                    echo '<meta http-equiv="refresh" content="0;URL=' . $this->base_url('online-courses/pre_post_questions_settings/' . $course['id']) . '/?added_course=true">';
                    exit();
                } else {
                    echo '<meta http-equiv="refresh" content="0;URL=' . $this->base_url('online-courses/pre_post_questions_settings/' . $course_info['id']) . '/?saved_course=true">';
                    exit();
                }
            }

            /** ===================================
             * Delete Course
             * ====================================    */

            if ($_POST['action'] == 'delete_course') {
                $courses_args = array(
                    'id' => $course_info['id'],
                    'school_id' => $this->school_info["id"]
                );
                $OL->delete_online_course($course_args);

                echo '<meta http-equiv="refresh" content="0;URL=' . $this->base_url('online-courses') . '/?deleted=true">';
                exit();
            }

            /** ===================================
             * Copy Course
             * ====================================    */

            if ($_POST['action'] == 'copy_course') {
                $course_args = array(
                    'id' => $course_info['id'],
                    'school_id' => $this->school_info["id"]
                );
                $course = $OL->copy_online_course($course_args);

                //Redirect to course if new
                echo '<meta http-equiv="refresh" content="0;URL=' . $this->base_url('online-courses/' . $course['id']) . '/?added_course=true">';
                exit();
            }

            /** ===================================
             * Add or Update Unit
             * ====================================    */
            if ($_POST['save_unit'] || $_POST['add_unit']) {
                $unit_args = array(
                    'id' => $unit['id'],
                    'trail' => $_POST['trail'],
                    'module_id' => $module['id'],
                    'status' => $_POST['status'],
                    'visibility' => $_POST['visibility'],
                    'publish_date' => $_POST['aa'] . "-" . $_POST['mm'] . "-" . $_POST['jj'],
                    'title' => $_POST['title'],
                    'description' => $_POST['description'],
                    'mp3_file' => $_POST['mp3_file'],
                    'mp3_auto_play' => $_POST['mp3_auto_play'],
                    'quiz_mandetory' => $_POST['quiz_mandetory'],
                    'questionnaire_mandetory' => $_POST['questionnaire_mandetory'],
                    'quiz_answerable_multiple_times' => $_POST['quiz_answerable_multiple_times'],
                    'show_user_answer' => $_POST['show_user_answer'],
                    'progress_certificate' => $_POST['progress_certificate'],
                    'progress_certificate_title' => $_POST['progress_certificate_title'],
                    'unit_slug' => $_POST['unit_slug'],
                );

                if ($_POST['save_unit']) {
                    $unit_args['action'] = "update";
                } else {
                    $unit_args['action'] = "insert";
                }


                //Update/Add unit
                $unit_info = $OL->update_or_insert_unit($unit_args);

                //Redirect to unit if new
                if ($_POST['add_unit']) {
                    echo '<meta http-equiv="refresh" content="0;URL=' . $this->base_url('online-courses/' . $course_info['id'] . '/modules/' . $module['id']) . '/units/' . $unit_info . '?added_entry=true">';
                    exit();
                } else {
                    echo '<meta http-equiv="refresh" content="0;URL=' . $this->base_url('online-courses/' . $course_info['id'] . '/modules/' . $module['id']) . '/units/' . $unit_info . '?saved_entry=true">';
                    exit();
                }
            }

            /** ===================================
             * Delete Unit
             * ====================================    */
            if ($_POST['action'] == 'delete_unit') {
                $unit_args = array(
                    'id' => $_POST['unit_id'],
                    'course_id' => $course_info['id'],
                    'school_id' => $this->school_info['id']
                );

                $OL->delete_unit($unit_args);

                echo '<meta http-equiv="refresh" content="0;URL=' . $this->base_url('online-courses/' . $course_info['id'] . '') . '/?deleted_unit=true">';
                exit();
            }

            /** ===================================
             * Delete Quiz
             * ====================================    */
            if ($_POST['action'] == 'delete_quiz') {
                $unit_args = array('id' => $_POST['quiz_id']);
                $deleted = $OL->delete_quiz($unit_args);

                //Remove the quiz id from the unit
                if ($deleted === true) {
                    $unit_args = array('id' => $unit_id, 'quiz_id' => '');
                    $OL->update_or_insert_unit($unit_args);
                }
                $status = $deleted === true ? 'true' : $deleted;
                echo '<meta http-equiv="refresh" content="0;URL=' . $this->base_url('online-courses/' . $course_info['id'] . '/modules/' . $module_id . '/units/' . $unit_id . '') . '?deleted_quiz=' . $status . '">';
                exit();
            }

            /** ===================================
             * Save the module
             * ====================================    */
            if ($_POST['save_module'] || $_POST['add_module']) {

                $module_args = array(
                    'title' => $_POST['title'],
                    'module_slug' => $_POST['module_slug'],
                    'description' => $_POST['description'],
                    'course_id' => $course_info['id'],
                );

                if ($_POST['save_module']) {
                    $module_args['id'] = $module['id'];
                    $module_args['action'] = "update";
                } else {
                    $module_args['action'] = "insert";
                }


                //Update/Add module
                $module_info = $OL->update_or_insert_module($module_args);

                //Redirect to module if new
                if ($_POST['add_module']) {
                    echo '<meta http-equiv="refresh" content="0;URL=' . $this->base_url('online-courses/' . $course_info['id'] . '/modules/' . $module_info['id']) . '?added_entry=true">';
                    exit();
                } else {
                    echo '<meta http-equiv="refresh" content="0;URL=' . $this->base_url('online-courses/' . $course_info['id'] . '/modules/' . $module_info['id']) . '?saved_entry=true">';
                    exit();
                }
            }

            /** ===================================
             * Delete Module
             * ====================================    */
            if ($_POST['action'] == 'delete_module') {
                $module_args = array(
                    'id' => $_POST['module_id'],
                    'course_id' => $course_info['id'],
                    'school_id' => $school_info["id"]
                );

                $OL->delete_module($module_args);

                echo '<meta http-equiv="refresh" content="0;URL=' . $this->base_url('online-courses/' . $course_info['id'] . '') . '/?deleted_module=true">';
                exit();
            }

            /** ===================================
             * View Unit/Module/Course Details
             * ====================================  */
            if ($quiz) {

                $form_templates = new FormTemplates;
                $fields = new Fields;

                $page_args = array("id" => $unit['quiz']['id'], "system_abbrv" => "inst", "school_id" => $this->school_info['id'], 'db' => $dev_db);
                $form = $form_templates->get($page_args);


                $field_types = $fields->field_types();
                $fields_list = array();
                foreach ($form['fields'] as $field) {
                    $finfo = $field;
                    $finfo['deleted'] = 0;
                    $fields_list[] = $finfo;
                }
                $form_json = json_encode($form);
                $fields_json = json_encode($fields_list);
                $field_types_json = json_encode($field_types);

                $data = array(
                    'meta_title' => $form['title'],
                    'view_file' => 'form_maker/form',
                    'field_types' => $field_types,
                    'form' => $form,
                    'system_abbrv' => "inst",
                    'correct_answers' => true,
                    'answer_hints' => true,
                    'correct_answer_reasons' => true,
                    'incorrect_answer_reasons' => true,
                    'form_json' => $form_json,
                    'back_link' => $this->base_url('online-courses/' . $course_info['id'] . '/modules/' . $module_id . '/units/' . $unit_id),
                    'back_link_title' => $unit['title'],
                    'fields_json' => $fields_json,
                    'field_types_json' => $field_types_json,
                );


            } elseif ($unit_id) {
                $data = array(
                    'meta_title' => $course_info['title'] . ' Units',
                    'view_file' => 'online_courses/unit',
                    'courses_categories' => $OL->get_course_groups(array("school_id" => $_SESSION['usergroup'])),
                    'courses_levels' => $OL->get_courses_levels(array("school_id" => $_SESSION['usergroup'])),
                    'entry_json' => $unit_entry_json,
                    'course_info' => $course_info,
                    'unit' => $unit,
                    'module' => $module,
                    'attached_quiz' => $attached_quiz
                );
            } elseif ($module_id) {
                $data = array(
                    'meta_title' => $course_info['title'] . ' Modules',
                    'view_file' => 'online_courses/module',
                    'courses_categories' => $OL->get_course_groups(array("school_id" => $_SESSION['usergroup'])),
                    'courses_levels' => $OL->get_courses_levels(array("school_id" => $_SESSION['usergroup'])),
                    'entry_json' => $module_entry_json,
                    'course_info' => $course_info,
                    'module' => $module,
                    'attached_quiz' => $attached_quiz
                );
            } else {
                $data = array(
                    'meta_title' => $course_info['title'],
                    'view_file' => 'online_courses/details',
                    'course_groups' => $OL->get_course_groups(array("school_id" => $_SESSION['usergroup'])),
                    'course_categories' => $OL->get_category(array("school_id" => $_SESSION['usergroup'])),
                    'courses_levels' => $OL->get_courses_levels(array("school_id" => $_SESSION['usergroup'])),
                    'all_languages' => $all_languages,
                    'entry_json' => $entry_json,
                    'english_courses' => $english_courses,
                    'course_info' => $course_info,
                    'topics' => $topics,
                    'attached_quiz' => $attached_quiz,
                    'all_survey_attached_modules_units' => $all_quiz_units_with_survey
                );
            }

            $this->view($this->layout, $data);

        } else {
            $filters = new Filter;
            $form_templates = new FormTemplates;
            $fieldClass = new Fields;
            // [pageID => masked]
            $pages = [327 => false];
            $active_columns = $fieldClass->select_fields(['page_id' => $pages]);
            $course_args = array('school_id' => $this->school_info['id'], 'no_quiz_information' => "true", 'raw_html' => true, 'paginate' => true, 'no_modules' => true);

            $field_indices = ["db21909", "db21929", "db25543", "db26246", "db30582", "db31066", "db37364"];
            $this->filter_fields = $field_indices;
            $this->filter_fields[] = 'ols_online_courses.id';
            dev_debug("Filter fields: " . json_encode($this->filter_fields));
            $yes_no_options = [['value' => 1, 'label' => 'Yes'], ['value' => 0, 'label' => 'No']];
            $this->extras['ols_online_courses.id'] = [
                'title' => 'Course ID',
                'type' => 'text'
            ];
            $this->extras['db25543'] = [
                'title' => 'Featured',
                'options' => $yes_no_options
            ];
            $this->extras['db30582'] = [
                'title' => 'Published',
                'options' => $yes_no_options
            ];
            $this->extras['db309757'] = [
                'title' => 'Remove from Course Library',
                'options' => $yes_no_options
            ];
            $this->extras['db26246'] = [
                'title' => 'Questionnaire type',
                'options' => [['value' => 'Type 1', 'label' => 'Type 1'], ['value' => 'Type 2', 'label' => 'Type 2']]
            ];
            $course_args['filter_sql'] = $filters->description_to_sql($this->filter['description']);
            dev_debug($course_args['filter_sql']);
            $setup = $filters->setup_view_filters($this->filter_fields, $this->extras);
            /** ===================================
             * Results
             * ====================================  */
            $language_args = array('school_id' => $this->school_info['id']);
            $languages = $OL->get_languages($language_args);
            $course_args['with_categories'] = true;
            $results = $OL->get_courses_v2($course_args);
            $active_fields = $form_templates->get_page_columns(array('page' => 'online_courses', 'filter_id' => $this->filter['id']));
            dev_debug("Active fields: " . json_encode($active_fields));

            $fields = array_keys($results[0]);
            $dimensions = $setup[1];
            foreach ($dimensions as $k => $group) {
                foreach ($group as $key => $fieldss) {
                    foreach ($fieldss as $x => $fd) {
                        if (!empty($active_fields)) $dimensions[$k][$key][$x]['active'] = in_array($fd['id'], $active_fields) ? 1 : 0;
                    }
                }
            }
            $all_fields = $fieldClass->dynamic_data_extract($fields, $active_fields, array_values($active_columns));

            $data = array(
                'meta_title' => 'Online Courses',
                'view_file' => 'online_courses/index',
                'results' => $results,
                'languages' => $languages,
                'all_fields' => $all_fields,
                'filter' => $this->filter,
                'dimensions' => $dimensions,
                'filter_types' => $setup[0],
                'hide_edit_columns' => true,
                'hide_export_button' => true,
                'filter_page_name' => 'online_courses',
            );
            $this->view($this->layout, $data);

        }


    }

    /**
     * @param $id
     * @return void
     */
    function update_programme_topics($id)
    {
        $OL = new OnlineLearning;
        $args = [
            'programme_id' => $id,
            'topics' => $_REQUEST['topics']
        ];
        $update = $OL->update_course_topics($args);
        echo json_encode([
            'success' => $update,
            'topics' => $_REQUEST['topics']
        ]);
        exit();
    }


    /** ===================================
     * FAQs
     * ====================================  */
    public function faqs($type_id = '')
    {

        $OL = new OnlineLearning;

        $data = array(
            'meta_title' => 'FAQs',
            'view_file' => 'online_courses/faqs',
            'faqs' => $OL->get_faqs(array('school_id' => $this->school_info["id"])),
        );
        $this->view($this->layout, $data);

    }


    /** ===================================
     * Questionnaire
     * ====================================  */
    public function questionnaire()
    {

        // ini_set('display_errors', 1);
        // ini_set('display_startup_errors', 1);
        // error_reporting(E_ALL);

        $OL = new OnlineLearning;
        $form_templates = new FormTemplates;
        $fields = new Fields;


        //Get the course information
        $course_args = array("id" => $this->uri_segement(2), 'raw_html' => true, 'no_default_excerpt' => true, 'show_hidden_units' => true, 'school_id' => $this->school_info["id"]);
        $course_info = $OL->get_courses($course_args);


        //Get the unit info
        foreach ($course_info['modules'] as $module) {
            //echo $module['id']."---".$this->uri_segement(4);
            if ($module['id'] == $this->uri_segement(4)) {

                foreach ($module['units'] as $unit) {
                    //echo $unit['id']."==".$this->uri_segement(6);
                    if ($unit['id'] == $this->uri_segement(6)) {
                        $unit_info = $unit;
                    }
                }
            }
        }


        //Get the form information/Fields
        if ($unit_info['questionnaire_form_id']) {
            $page_args = array("id" => $unit_info['questionnaire_form_id'], "system_abbrv" => "inst", "school_id" => $this->school_info['id']);
            $form = $form_templates->get($page_args);
        } else {
            //Create the form


            $form_args = array('title' => "Unit " . $unit_info['id'] . "", 'category_id' => 8, "system_abbrv" => 'inst');
            $form_id = $form_templates->insert_update_form($form_args);


            //udpate the course with the new form id
            $unit_args = array('id' => $unit_info['id'], 'questionnaire_form_id' => $form_id);
            $course = $OL->update_or_insert_unit($unit_args);

            $page_args = array("id" => $form_id, "system_abbrv" => "inst", "school_id" => $this->school_info['id']);
            $form = $form_templates->get($page_args);
        }


        //Fix the fields variables for angular
        $field_types = $fields->field_types();
        $fields_list = array();
        foreach ($form['fields'] as $field) {
            $finfo = $field;
            $finfo['deleted'] = 0;
            $fields_list[] = $finfo;
        }


        $form_json = json_encode($form);
        $fields_json = json_encode($fields_list);
        $field_types_json = json_encode($field_types);


        $data = array(
            'meta_title' => $unit_info['title'] . " - Pre Questions",
            'view_file' => 'form_maker/form',
            'field_types' => $field_types,
            'form' => $form,
            'system_abbrv' => "inst",
            'form_json' => $form_json,
            'answer_weighting' => true,
            'back_link' => $this->base_url('online-courses/' . $course_info['id'] . '/modules/' . $this->uri_segement(4) . '/units/' . $this->uri_segement(6)),
            'back_link_title' => $unit_info['title'],
            'custom_form_title' => "Questionnaire",
            'fields_json' => $fields_json,
            'field_types_json' => $field_types_json,
        );

        $this->view($this->layout, $data);

    }


    /** ===================================
     * Pre Questions
     * ====================================  */
    public function pre_questions($course_id = '')
    {

        $OL = new OnlineLearning;
        $form_templates = new FormTemplates;
        $fields = new Fields;

        //Get the course information
        $course_args = array("id" => $course_id, 'raw_html' => true, 'no_default_excerpt' => true, 'school_id' => $this->school_info["id"]);
        $course_info = $OL->get_courses($course_args);
        $entry_json = json_encode($course_info);

        //Get the form information/Fields
        if ($course_info['pre_questions_form_id']) {
            $page_args = array("id" => $course_info['pre_questions_form_id'], "system_abbrv" => "inst", "school_id" => $this->school_info['id'], 'db' => $dev_db);
            $form = $form_templates->get($page_args);
        } else {
            //Create the form
            $form_args = array('title' => "Course " . $course_info['id'] . " Pre questions", 'category_id' => 8, "system_abbrv" => 'inst');
            $form_id = $form_templates->insert_update_form($form_args);

            //udpate the course with the new form id
            $courses_args = array('id' => $course_info['id'], 'pre_questions_form_id' => $form_id, 'action' => 'update');
            $course = $OL->update_or_insert_course($courses_args);
        }

        //Fix the fields variables for angular
        $field_types = $fields->field_types();
        $fields_list = array();
        foreach ($form['fields'] as $field) {
            $finfo = $field;
            $finfo['deleted'] = 0;
            $fields_list[] = $finfo;
        }
        $form_json = json_encode($form);
        $fields_json = json_encode($fields_list);
        $field_types_json = json_encode($field_types);

        $data = array(
            'meta_title' => $course_info['title'] . " - Pre Questions",
            'view_file' => 'form_maker/form',
            'field_types' => $field_types,
            'form' => $form,
            'system_abbrv' => "inst",
            'form_json' => $form_json,
            'answer_weighting' => true,
            'back_link' => $this->base_url('online-courses/' . $course_info['id']),
            'back_link_title' => $course_info['title'],
            'custom_form_title' => "Pre Questions",
            'fields_json' => $fields_json,
            'field_types_json' => $field_types_json,
        );
        $this->view($this->layout, $data);

    }

    /** ===================================
     * Post Questions
     * ====================================  */
    public function post_questions($course_id = '')
    {

        $OL = new OnlineLearning;
        $form_templates = new FormTemplates;
        $fields = new Fields;

        //Get the course information
        $course_args = array("id" => $course_id, 'raw_html' => true, 'no_default_excerpt' => true, 'school_id' => $this->school_info["id"]);
        $course_info = $OL->get_courses($course_args);
        $entry_json = json_encode($course_info);

        //Get the form information/Fields
        if ($course_info['post_questions_form_id']) {
            $page_args = array("id" => $course_info['post_questions_form_id'], "system_abbrv" => "inst", "school_id" => $this->school_info['id'], 'db' => $dev_db);
            $form = $form_templates->get($page_args);
        } else {
            //Create the form
            $form_args = array('title' => "Course " . $course_info['id'] . " Post questions", 'category_id' => 9, "system_abbrv" => 'inst');
            $form_id = $form_templates->insert_update_form($form_args);

            //udpate the course with the new form id
            $courses_args = array('id' => $course_info['id'], 'post_questions_form_id' => $form_id, 'action' => 'update');
            $course = $OL->update_or_insert_course($courses_args);
        }

        //Fix the fields variables for angular
        $field_types = $fields->field_types();
        $fields_list = array();
        foreach ($form['fields'] as $field) {
            $finfo = $field;
            $finfo['deleted'] = 0;
            $fields_list[] = $finfo;
        }
        $form_json = json_encode($form);
        $fields_json = json_encode($fields_list);
        $field_types_json = json_encode($field_types);

        $data = array(
            'meta_title' => $course_info['title'] . " - Post Questions",
            'view_file' => 'form_maker/form',
            'field_types' => $field_types,
            'form' => $form,
            'system_abbrv' => "inst",
            'form_json' => $form_json,
            'answer_weighting' => true,
            'back_link' => $this->base_url('online-courses/' . $course_info['id']),
            'back_link_title' => $course_info['title'],
            'custom_form_title' => "Post Questions",
            'fields_json' => $fields_json,
            'field_types_json' => $field_types_json,
        );
        $this->view($this->layout, $data);

    }


    /** ===================================
     * Pre Post Questions Settings
     * ====================================  */
    public function pre_post_questions_settings($course_id = '')
    {

        $OL = new OnlineLearning;

        //Get the course information
        $course_args = array("id" => $course_id, 'raw_html' => true, 'no_default_excerpt' => true, 'school_id' => $this->school_info["id"]);
        $course_info = $OL->get_courses($course_args);

        $data = array(
            'meta_title' => 'Pre & Post Questionnaire Settings',
            'view_file' => 'online_courses/pre_post_questions_settings',
            'course_info' => $course_info,
        );
        $this->view($this->layout, $data);

    }

    /** ===================================
     * Pre Post Questions Settings
     * ====================================  */
    public function pre_post_questionnaire_report($course_id = '')
    {

        $OL = new OnlineLearning;
        //Get the course information
        $course_args = array('raw_html' => true, 'no_default_excerpt' => true, 'school_id' => $this->school_info["id"],'has_post_questionnaire'=>true,'count_modules'=>true, "published_courses"=>true, "transitioned_courses"=>true);
        if ($_SESSION['ulevel']==16) {
            $course_args['sponsor_id']=$_SESSION['sponsor_id'];
        }
        $courses = $OL->get_courses($course_args);
        if ($course_id == '' && empty($_REQUEST['select_course'])) {
            $OL = new OnlineLearning;


            $data = array(
                'meta_title' => 'Pre & Post Questionnaire report',
                'view_file' => 'online_courses/pre_post_questionnaire_report',
                'courses' => $courses
            );
            $this->view($this->layout, $data);
        } else {
            if ($course_id == '') {
                $course_id = $_REQUEST['select_course'];
            }

            //Get the course information
            $course_args = array("id" => $course_id, 'raw_html' => true, 'no_default_excerpt' => true, 'school_id' => $this->school_info["id"], 'count_modules' => true, 'no_quiz_information' => true);

            if (!empty( $_REQUEST['select_sponsor'])) {
                $course_args['sponsor_id']=$_REQUEST['select_sponsor'];
            }
            $course_info = $OL->get_courses($course_args);

            $data = array(
                'meta_title' => 'Pre & Post Questionnaire report',
                'view_file' => 'online_courses/pre_post_questionnaire_report',
                'course_info' => $course_info,
                'courses' => $courses
            );
            $this->view($this->layout, $data);
        }

    }

    /** ===================================
     * Pre Post Questions Settings
     * ====================================  */
    public function learner_feedback($course_id = '')
    {

        $OL = new OnlineLearning;

        //Get the course information
        $course_args = array("course" => $course_id, 'school_id' => $this->school_info["id"]);
        $course_feedback = $OL->unit_feedback($course_args);

        $data = array(
            'meta_title' => 'Learner Feedback report',
            'view_file' => 'online_courses/unit_feedback_report',
            'results' => $course_feedback,
        );
        $this->view($this->layout, $data);

    }

    function course_image_upload($course_id)
    {
        $OL = new OnlineLearning();
        if ($_POST["type"] == "image") {
            if (empty($_FILES)) {
                echo json_encode(["success" => false, "message" => "no file found"]);
                exit();
            }
            $imageFileType = strrchr(basename($_FILES['value']['name']), '.');
            $uploaded_file_name = random() . $imageFileType;
            $file_name = stripslashes($uploaded_file_name);
            $params = [
                'db199' => "Course $course_id Image",
                'db204' => 'ols_course_images/' . $file_name,
                'directory' => 'ols_course_images/',
            ];
            $value = uploadFile($params);
            if (empty($value)) {
                echo json_encode(["success" => false, "message" => "An error occurred"]);
                exit();
            }
        } else {
            $value = $_POST['value'];
        }
        $OL->update_or_insert_course(['id' => $course_id, 'image' => $value, 'action' => 'update']);
        echo json_encode(["success" => true, "message" => "File upload successful","val"=>$value]);
    }

    function course_thumbnail_upload($course_id)
    {
        $OL = new OnlineLearning();
        if ($_POST["type"] == "image" || $_POST["type"] == "thumbnail") {
            if (empty($_FILES)) {
                echo json_encode(["success" => false, "message" => "no file found"]);
                exit();
            }
            $imageFileType = strrchr(basename($_FILES['value']['name']), '.');
            $uploaded_file_name = random() . $imageFileType;
            $file_name = stripslashes($uploaded_file_name);
            $params = [
                'db199' => "Course $course_id Thumbnail",
                'db204' => 'ols_course_thumbnails/' . $file_name,
                'directory' => 'ols_course_thumbnails/',
            ];
            $value = uploadFile($params);
            if (empty($value)) {
                echo json_encode(["success" => false, "message" => "An error occurred"]);
                exit();
            }
        } else {
            $value = $_POST['value'];
        }
        $OL->update_or_insert_course(['id' => $course_id, 'thumbnail' => $value, 'action' => 'update']);
        echo json_encode(["success" => true, "message" => "Thumbnail upload successful"]);
    }

    private function re_order_courses($data)
    {
        $this->updateOrderValues($data['order'], $data['id']);

    }

    private function updateOrderValues($startOrder, $id)
    {
        $sql = "SELECT id, db251258 as order_column FROM ols_online_courses WHERE db251258 = $startOrder ORDER BY db251258";
        $stmt = get_dbh()->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_OBJ);
        if (count($results) > 0) {
            $match = array_filter($results, function ($result) use ($id) {
                return $id == $result->id;
            });
            if (count($match) == 0) {
                $SQL = "UPDATE ols_online_courses SET db251258 = db251258 + 1 WHERE db251258 >= $startOrder";
                $update = get_dbh()->prepare($SQL);
                $update->execute();
            }

        }
    }
    
}