<?php


namespace App\console\commands;


use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class FixStagesForCoreStudents extends Command
{

    protected static $defaultName = 'applicants:fix-stages';

    protected function configure()
    {
        $this->setDescription('Fix applicants stages')
            ->setHelp('This command allows you to fix  applicants  stage');
        $this->addArgument('usergroup', InputArgument::OPTIONAL, 'The usergroup');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $output->writeln([
            'Starting',
            '============',
            '',
        ]);
        $db = dbh();
        $ugval = $input->getArgument('usergroup');
        if ($ugval) {
            $ug = ' core_students.usergroup=' . $ugval . ' AND ';
        } else {
            $ug = '';
        }
        $sql = "select core_students.id,core_students.db30487,dir_stage_tracker.db1142 
            From core_students
            LEFT JOIN dir_stage_tracker ON core_students.id=dir_stage_tracker.rel_id 
            where " . $ug . "  ( core_students.db30487 NOT LIKE '%db%' OR core_students.db30487 IS NULL )
            AND dir_stage_tracker.id=(SELECT id FROM dir_stage_tracker WHERE dir_stage_tracker.rel_id=core_students.id  order by id desc LIMIT 1)
            GROUP BY core_students.id order by dir_stage_tracker.id";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll(\PDO::FETCH_OBJ);
        foreach ($results as $result) {
            $update_sql = "UPDATE core_students SET db30487='" . $result->db1142 . "' WHERE id= '" . $result->id . "'";
            $stmt = $db->prepare($update_sql);
            $stmt->execute();
        }
        $output->writeln([
            'Fixed '.count($results)." records",
            '============',
            '',
        ]);
    }
}