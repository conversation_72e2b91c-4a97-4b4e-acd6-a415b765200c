<?php

namespace App\console\commands;

use App\core\console\Command;
use App\core\support\facades\DB;


class CohortCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cohort:update {--usergroup=1 : The usergroup to update.}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command updates cohorts';


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Updating cohorts');
        $usergroup = $this->option('usergroup');
        DB::table('form_schools')
            ->where('usergroup', $usergroup)
            ->where('db36', '!=', date("Y"))
            ->update(['db36' => date("Y")]);
        $this->info('Cohorts updated');
        return 0;
    }
}