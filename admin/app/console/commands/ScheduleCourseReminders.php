<?php

namespace App\console\commands;

use App\core\console\Command;
use App\core\support\facades\DB;
use App\models\Email;


class ScheduleCourseReminders extends Command
{
    protected $signature = 'reminders:send-schedule-course-reminders';
    protected $description = 'Process bulk enquiry email reminders for scheduled courses';
    public $processed_jobs = 0;
    public $failed_jobs = 0;
    public $pending_jobs = 0;

    protected $debug_mode;


    /**
     */
    public function handle()
    {
        $this->info('Starting scheduled course reminder' . date('m/d/Y h:i:s a', time()));
        //all course bookings with no sessions
        $this->info('Starting with_no_sessions_not1' . date('m/d/Y h:i:s a', time()));
        $query = DB::table('sis_sched_booking_detail')
            ->select([
                'sis_scheduled_booking.id',
                'sis_scheduled_booking.rel_id',
                DB::raw("CONCAT(db48566, ' ', db48568) as full_name"),
                'db48619 as title',
                'db48566 as student_name',
                'db48599 as student_number',
                'db48583 as student_email',
                'db48621 as student_address',
                'db48596 as student_town',
                'db48622 as student_postcode',
                'db48597 as student_phone',
                'db232 as course_title',
                DB::raw("IFNULL(db234, '') as course_summary"),
                'db14947 as course_start_date',
                'db14948 as course_start_time',
                'db14949 as course_end_date',
                'db14950 as course_end_time',
                'db63007 as scheduled_course_information',
                'db14956 as additional_information',
                DB::raw("IFNULL(db14953, '') as course_minimum_attendees"),
                DB::raw("CONCAT_WS(', ', db14963, db14965, db14966) as course_venue"),
                'db14968 as course_venue_information',
                'db14966 as course_postcode',
                DB::raw("IFNULL(db209162, '') as course_whatthreewords"),
                DB::raw("IFNULL(db237356, '') as course_venue_image"),
                'db14970 as booking_status',
                'sis_course_schedule.id as course_schedule',
                'sis_scheduled_booking.usergroup',
                'db19831 as reminder_template',
                'sis_profiles.id as sis_profile_id',
                'sis_profiles.rel_id as sis_profile_rel_id',
                'sis_sched_booking_detail.id as sched_detail_id',
                DB::raw("8 as letter_tag"),
                DB::raw("5 as sms_template_tag"),
            ])
            ->leftJoin('form_schools', 'sis_sched_booking_detail.usergroup', '=', 'form_schools.id')
            ->leftJoin('sis_scheduled_booking', 'sis_sched_booking_detail.db15052', '=', 'sis_scheduled_booking.id')
            ->leftJoin('core_students', 'core_students.id', '=', 'sis_sched_booking_detail.rel_id')
            ->leftJoin('sis_profiles', 'sis_profiles.rel_id', '=', 'core_students.rel_id')
            ->leftJoin('sis_course_schedule', 'sis_course_schedule.id', '=', 'db14977')
            ->leftJoin('sis_booking_status', 'sis_booking_status.id', '=', 'db14983')
            ->leftJoin('core_courses', 'core_courses.id', '=', 'db16136')
            ->leftJoin('sis_course_venues', 'sis_course_venues.id', '=', 'db14954')
            ->where('db30', '12') // school type
            ->where(function ($q) {
                $q->whereNull('sis_scheduled_booking.rec_archive')
                    ->orWhere('sis_scheduled_booking.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_profiles.rec_archive')
                    ->orWhere('sis_profiles.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_course_schedule.rec_archive')
                    ->orWhere('sis_course_schedule.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_sched_booking_detail.rec_archive')
                    ->orWhere('sis_sched_booking_detail.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereIn('db14983', [2, 3])
                    ->orWhereIn('db59978', [2, 3]);
            })
            ->where('db14959', '!=', 5)
            ->where(function ($q) {
                $q->whereNull('db66749')->orWhere('db66749', '!=', 'yes');
            })
            ->whereRaw("(SELECT COUNT(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = db14977 AND (sis_scheduled_sessions.rec_archive IS NULL OR sis_scheduled_sessions.rec_archive = '')) = 0")
            ->whereRaw("
        db14947 > CURDATE() 
        AND db14947 = DATE_ADD(
            CURDATE(), 
            INTERVAL (SELECT db111500 FROM lead_preferences WHERE usergroup = sis_scheduled_booking.usergroup) DAY
        )
    ")
            ->where(function ($q) {
                $q->where('db284645', '=', 'yes')
                    ->orWhereNull('db284645')
                    ->orWhere('db284645', '');
            })
            ->orderBy('sis_scheduled_booking.id');
        $query->chunk(100, function ($results) {
            foreach ($results as $notification_to_be_sent) {
                $this->info('Processing for : ' . $notification_to_be_sent->student_name);
                $this->process_notifications($notification_to_be_sent, 'with_no_sessions_not1', 'course_booking_reminder', 'db111500');
                $this->processed_jobs++;
            }
        });
        $this->info('Ending scheduled course reminder' . date('m/d/Y h:i:s a', time()));
        $this->info('starting with_sessions_not1' . date('m/d/Y h:i:s a', time()));
        //all course bookings(WITH SESSIONS) but session reminders switched off send out as normal course reminders
        $query = DB::table('sis_sched_booking_detail')
            ->select([
                'sis_scheduled_booking.id',
                'sis_scheduled_booking.rel_id',
                DB::raw("CONCAT(db48566, ' ', db48568) AS full_name"),
                'db48619 AS title',
                'db48566 AS student_name',
                'db48599 AS student_number',
                'db48583 AS student_email',
                'db48621 AS student_address',
                'db48596 AS student_town',
                'db48622 AS student_postcode',
                'db48597 AS student_phone',
                'db232 AS course_title',
                DB::raw("IFNULL(db234, '') AS course_summary"),
                'db14947 AS course_start_date',
                'db14948 AS course_start_time',
                'db14949 AS course_end_date',
                'db14950 AS course_end_time',
                'db63007 AS scheduled_course_information',
                'db14956 AS additional_information',
                DB::raw("IFNULL(db14953, '') AS course_minimum_attendees"),
                DB::raw("CONCAT_WS(', ', db14963, db14965, db14966) AS course_venue"),
                'db14968 AS course_venue_information',
                'db14966 AS course_postcode',
                DB::raw("IFNULL(db209162, '') AS course_whatthreewords"),
                DB::raw("IFNULL(db237356, '') AS course_venue_image"),
                'db14970 AS booking_status',
                'sis_course_schedule.id AS course_schedule',
                'sis_scheduled_booking.usergroup',
                'db19831 AS reminder_template',
                'sis_profiles.id AS sis_profile_id',
                'sis_profiles.rel_id AS sis_profile_rel_id',
                'sis_sched_booking_detail.id AS sched_detail_id',
                DB::raw("8 AS letter_tag"),
                DB::raw("5 AS sms_template_tag"),
            ])
            ->leftJoin('form_schools', 'sis_sched_booking_detail.usergroup', '=', 'form_schools.id')
            ->leftJoin('sis_scheduled_booking', 'sis_sched_booking_detail.db15052', '=', 'sis_scheduled_booking.id')
            ->leftJoin('core_students', 'core_students.id', '=', 'sis_sched_booking_detail.rel_id')
            ->leftJoin('sis_profiles', 'sis_profiles.rel_id', '=', 'core_students.rel_id')
            ->leftJoin('sis_course_schedule', 'sis_course_schedule.id', '=', 'db14977')
            ->leftJoin('sis_booking_status', 'sis_booking_status.id', '=', 'db14983')
            ->leftJoin('core_courses', 'core_courses.id', '=', 'db16136')
            ->leftJoin('sis_course_venues', 'sis_course_venues.id', '=', 'db14954')
            ->where('db30', '12') // school type
            ->where(function ($q) {
                $q->whereNull('sis_scheduled_booking.rec_archive')
                    ->orWhere('sis_scheduled_booking.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_course_schedule.rec_archive')
                    ->orWhere('sis_course_schedule.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_profiles.rec_archive')
                    ->orWhere('sis_profiles.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_sched_booking_detail.rec_archive')
                    ->orWhere('sis_sched_booking_detail.rec_archive', '');
            })
            ->where(function ($q) {
                $q->where('db14983', 2)
                    ->orWhere('db14983', 3)
                    ->orWhere('db59978', 2)
                    ->orWhere('db59978', 3);
            })
            ->where('db14959', '!=', 5)
            ->where(function ($q) {
                $q->whereNull('db66749')
                    ->orWhere('db66749', '!=', 'yes');
            })
            ->whereRaw("(SELECT count(*) from sis_scheduled_sessions where sis_scheduled_sessions.rel_id = db14977 and (sis_scheduled_sessions.rec_archive is null or sis_scheduled_sessions.rec_archive = '')) > 0")
            ->whereRaw("db14947 > CURDATE() AND db14947 = DATE_ADD(CURDATE(), INTERVAL (SELECT db111500 FROM lead_preferences WHERE usergroup= sis_scheduled_booking.usergroup) DAY)")
            ->whereRaw("(SELECT db111515 FROM lead_preferences WHERE usergroup= sis_scheduled_booking.usergroup) = 'yes'")
            ->where(function ($q) {
                $q->where('db284645', '=', 'yes')
                    ->orWhereNull('db284645')
                    ->orWhere('db284645', '');
            })
            ->orderBy('sis_scheduled_booking.id');
        $query->chunk(100, function ($results) {
            foreach ($results as $notification_to_be_sent) {
                $this->info('Processing for : ' . $notification_to_be_sent->student_name);
                $this->process_notifications($notification_to_be_sent, 'with_all_sessions_notification1', 'course_booking_reminder', 'db111500');
                $this->processed_jobs++;
            }
        });
        $this->info('ending with_sessions_not1' . date('m/d/Y h:i:s a', time()));
        $this->info('starting with_all_sessions_notification1' . date('m/d/Y h:i:s a', time()));
        //send out session reminder notification 1
        //all sessions booking with pending bookings not sent
        //usergroup has not disabled session reminders
        $query = $query = DB::table('sis_session_bookings')
            ->select([
                'sis_session_bookings.id',
                'sis_scheduled_booking.rel_id',
                DB::raw("CONCAT(db48566, ' ', db48568) AS full_name"),
                'db48619 as title',
                'db48566 AS student_name',
                'db48599 AS student_number',
                'db48583 AS student_email',
                'db48621 AS student_address',
                'db48596 AS student_town',
                'db48622 AS student_postcode',
                'db48597 AS student_phone',
                'db232 AS course_title',
                DB::raw("IFNULL(db234,'') AS course_summary"),
                'db59906 AS course_session_name',
                'db59835 AS course_start_date',
                'db59836 AS course_start_time',
                'db59837 AS course_end_date',
                'db59838 AS course_end_time',
                DB::raw("IFNULL(db14953,'') AS course_minimum_attendees"),
                DB::raw("CONCAT_WS(', ',db14963, db14965, db14966) AS course_venue"),
                'db63007 AS scheduled_course_information',
                'db14956 AS additional_information',
                'db14968 AS course_venue_information',
                'db14966 AS course_postcode',
                DB::raw("IFNULL(db209162,'') AS course_whatthreewords"),
                DB::raw("IFNULL(db237356,'') AS course_venue_image"),
                'db14970 AS booking_status',
                'db63025 AS scheduled_session_information',
                'sis_scheduled_booking.usergroup',
                'db19831 AS reminder_template',
                'sis_profiles.id AS sis_profile_id',
                'sis_profiles.rel_id AS sis_profile_rel_id',
                'sis_sched_booking_detail.id AS sched_detail_id',
                DB::raw("8 as letter_tag"),
                DB::raw("8 as sms_template_tag"),
            ])
            ->leftJoin('form_schools', 'sis_session_bookings.usergroup', '=', 'form_schools.id')
            ->leftJoin('sis_scheduled_booking', 'sis_session_bookings.db59976', '=', 'sis_scheduled_booking.id')
            ->leftJoin('sis_sched_booking_detail', DB::raw('CAST(sis_session_bookings.db59977 AS UNSIGNED)'), '=', 'sis_sched_booking_detail.id')
            ->leftJoin('core_students', 'core_students.id', '=', 'sis_sched_booking_detail.rel_id')
            ->leftJoin('sis_profiles', 'sis_profiles.rel_id', '=', 'core_students.rel_id')
            ->leftJoin('sis_course_schedule', 'sis_course_schedule.id', '=', 'db14977')
            ->leftJoin('sis_booking_status', 'sis_booking_status.id', '=', 'db14983')
            ->leftJoin('core_courses', 'core_courses.id', '=', 'db16136')
            ->leftJoin('sis_scheduled_sessions', 'sis_scheduled_sessions.id', '=', 'db59901')
            ->leftJoin('sis_course_venues', 'sis_course_venues.id', '=', 'db59839')
            ->leftJoin('sis_course_sessions', 'sis_course_sessions.id', '=', 'db59900')
            ->where('db30', '12') // school type
            ->where(function ($q) {
                $q->whereNull('core_students.rec_archive')
                    ->orWhere('core_students.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_scheduled_booking.rec_archive')
                    ->orWhere('sis_scheduled_booking.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_course_schedule.rec_archive')
                    ->orWhere('sis_course_schedule.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_profiles.rec_archive')
                    ->orWhere('sis_profiles.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_scheduled_sessions.rec_archive')
                    ->orWhere('sis_scheduled_sessions.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_session_bookings.rec_archive')
                    ->orWhere('sis_session_bookings.rec_archive', '');
            })
            ->where('db14959', '!=', 5)
            ->where('db59908', '!=', 5)
            ->where(function ($q) {
                $q->where('db14983', 2)
                    ->orWhere('db14983', 3)
                    ->orWhere('db59978', 2)
                    ->orWhere('db59978', 3);
            })
            ->where(function ($q) {
                $q->where('db59902', 2)
                    ->orWhere('db59902', 3);
            })
            ->where(function ($q) {
                $q->whereNull('db59904')
                    ->orWhere('db59904', '!=', 'yes');
            })
            ->where(function ($q) {
                $q->where('db284645', '=', 'yes')
                    ->orWhereNull('db284645')
                    ->orWhere('db284645', '');
            })
            ->whereRaw("db59835 > CURDATE()")
            ->whereRaw("db59835 = DATE_ADD(CURDATE(), INTERVAL (SELECT db111500 FROM lead_preferences WHERE usergroup = sis_scheduled_booking.usergroup) DAY)")
            ->whereRaw("(SELECT db111515 FROM lead_preferences WHERE usergroup = sis_scheduled_booking.usergroup) != 'yes' OR (SELECT db111515 FROM lead_preferences WHERE usergroup = sis_scheduled_booking.usergroup) IS NULL")
            ->orderBy('sis_scheduled_booking.id');
        $query->chunk(100, function ($results) {
            foreach ($results as $notification_to_be_sent) {
                $this->info('Processing for : ' . $notification_to_be_sent->student_name);
                $this->process_notifications($notification_to_be_sent, 'with_all_sessions_notification1', 'course_session_reminder', 'db111500');
                $this->processed_jobs++;
            }
        });
        $this->info('ending with_all_sessions_notification1' . date('m/d/Y h:i:s a', time()));
        //course reminder notification 2
        // bookings with zero sessions
        $this->info('starting with_no_sessions_not2' . date('m/d/Y h:i:s a', time()));
        $query = DB::table('sis_sched_booking_detail')
            ->select([
                'sis_scheduled_booking.id',
                'sis_scheduled_booking.rel_id',
                DB::raw("CONCAT(db48566, ' ', db48568) as full_name"),
                'db48619 as title',
                'db48566 as student_name',
                'db48599 as student_number',
                'db48583 as student_email',
                'db48621 as student_address',
                'db48596 as student_town',
                'db48622 as student_postcode',
                'db48597 as student_phone',
                'db232 as course_title',
                DB::raw("IFNULL(db234, '') as course_summary"),
                'db14947 as course_start_date',
                'db14948 as course_start_time',
                'db14949 as course_end_date',
                'db14950 as course_end_time',
                'db63007 as scheduled_course_information',
                'db14956 as additional_information',
                DB::raw("IFNULL(db14953, '') as course_minimum_attendees"),
                DB::raw("CONCAT_WS(', ', db14963, db14965, db14966) as course_venue"),
                'db14968 as course_venue_information',
                'db14966 as course_postcode',
                DB::raw("IFNULL(db209162, '') as course_whatthreewords"),
                DB::raw("IFNULL(db237356, '') as course_venue_image"),
                'db14970 as booking_status',
                'sis_course_schedule.id as course_schedule',
                'sis_scheduled_booking.usergroup',
                'db19831 as reminder_template',
                'sis_profiles.id as sis_profile_id',
                'sis_profiles.rel_id as sis_profile_rel_id',
                'sis_sched_booking_detail.id as sched_detail_id',
                DB::raw("8 as letter_tag"),
                DB::raw("5 as sms_template_tag"),
            ])
            ->leftJoin('form_schools', 'sis_sched_booking_detail.usergroup', '=', 'form_schools.id')
            ->leftJoin('sis_scheduled_booking', 'db15052', '=', 'sis_scheduled_booking.id')
            ->leftJoin('core_students', 'core_students.id', '=', 'sis_sched_booking_detail.rel_id')
            ->leftJoin('sis_profiles', 'sis_profiles.rel_id', '=', 'core_students.rel_id')
            ->leftJoin('sis_course_schedule', 'sis_course_schedule.id', '=', 'db14977')
            ->leftJoin('sis_booking_status', 'sis_booking_status.id', '=', 'db14983')
            ->leftJoin('core_courses', 'core_courses.id', '=', 'db16136')
            ->leftJoin('sis_course_venues', 'sis_course_venues.id', '=', 'db14954')
            ->where('db30', '12')
            ->where(function ($q) {
                $q->whereNull('sis_scheduled_booking.rec_archive')->orWhere('sis_scheduled_booking.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('core_students.rec_archive')->orWhere('core_students.rec_archive', '');
            })
            ->where('db14959', '!=', 5)
            ->where(function ($q) {
                $q->whereNull('sis_profiles.rec_archive')->orWhere('sis_profiles.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_sched_booking_detail.rec_archive')->orWhere('sis_sched_booking_detail.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_course_schedule.rec_archive')->orWhere('sis_course_schedule.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereIn('db14983', [2, 3])->orWhereIn('db59978', [2, 3]);
            })
            ->where(function ($q) {
                $q->whereNull('db66750')->orWhere('db66750', '!=', 'yes');
            })
            ->where(function ($q) {
                $q->where('db284645', 'yes')->orWhereNull('db284645')->orWhere('db284645', '');
            })
            ->whereRaw("(SELECT COUNT(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = db14977 AND (sis_scheduled_sessions.rec_archive IS NULL OR sis_scheduled_sessions.rec_archive = '')) = 0")
            ->whereRaw("db14947 > CURDATE() AND db14947 = DATE_ADD(CURDATE(), INTERVAL (SELECT db111503 FROM lead_preferences WHERE usergroup = sis_scheduled_booking.usergroup) DAY)")
            ->orderBy('sis_scheduled_booking.id');
        $query->chunk(100, function ($results) {
            foreach ($results as $notification_to_be_sent) {
                $this->info('Processing for : ' . $notification_to_be_sent->student_name);
                $this->process_notifications($notification_to_be_sent, 'with_no_sessions_not2', 'course_booking_reminder', 'db111503');
                $this->processed_jobs++;
            }
        });
        $this->info('ending with_no_sessions_not2' . date('m/d/Y h:i:s a', time()));

        //course reminder 2
        //all bookings with sessions but session reminders switched off
        $this->info('starting with_sessions_not2' . date('m/d/Y h:i:s a', time()));
        $query = DB::table('sis_sched_booking_detail')
            ->select([
                'sis_scheduled_booking.id',
                'sis_scheduled_booking.rel_id',
                DB::raw("CONCAT(db48566, ' ', db48568) AS full_name"),
                'db48619 AS title',
                'db48566 AS student_name',
                'db48599 AS student_number',
                'db48583 AS student_email',
                'db48621 AS student_address',
                'db48596 AS student_town',
                'db48622 AS student_postcode',
                'db48597 AS student_phone',
                'db232 AS course_title',
                DB::raw("IFNULL(db234,'') AS course_summary"),
                'db14947 AS course_start_date',
                'db14948 AS course_start_time',
                'db14949 AS course_end_date',
                'db14950 AS course_end_time',
                'db63007 AS scheduled_course_information',
                'db14956 AS additional_information',
                DB::raw("CONCAT_WS(', ', db14963, db14965, db14966) AS course_venue"),
                'db14968 AS course_venue_information',
                'db14966 AS course_postcode',
                DB::raw("IFNULL(db209162,'') AS course_whatthreewords"),
                DB::raw("IFNULL(db237356,'') AS course_venue_image"),
                'db14970 AS booking_status',
                'sis_course_schedule.id AS course_schedule',
                'sis_scheduled_booking.usergroup',
                'db19831 AS reminder_template',
                'sis_profiles.id AS sis_profile_id',
                'sis_profiles.rel_id AS sis_profile_rel_id',
                'sis_sched_booking_detail.id AS sched_detail_id',
                DB::raw("8 AS letter_tag"),
                DB::raw("5 AS sms_template_tag")
            ])
            ->leftJoin('form_schools', 'sis_sched_booking_detail.usergroup', '=', 'form_schools.id')
            ->leftJoin('sis_scheduled_booking', 'db15052', '=', 'sis_scheduled_booking.id')
            ->leftJoin('core_students', 'core_students.id', '=', 'sis_sched_booking_detail.rel_id')
            ->leftJoin('sis_profiles', 'sis_profiles.rel_id', '=', 'core_students.rel_id')
            ->leftJoin('sis_course_schedule', 'sis_course_schedule.id', '=', 'db14977')
            ->leftJoin('sis_booking_status', 'sis_booking_status.id', '=', 'db14983')
            ->leftJoin('core_courses', 'core_courses.id', '=', 'db16136')
            ->leftJoin('sis_course_venues', 'sis_course_venues.id', '=', 'db14954')
            ->where('db30', '12')
            ->where(function ($q) {
                $q->whereNull('sis_scheduled_booking.rec_archive')
                    ->orWhere('sis_scheduled_booking.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('core_students.rec_archive')
                    ->orWhere('core_students.rec_archive', '');
            })
            ->where('db14959', '!=', 5)
            ->where(function ($q) {
                $q->whereNull('sis_profiles.rec_archive')
                    ->orWhere('sis_profiles.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_sched_booking_detail.rec_archive')
                    ->orWhere('sis_sched_booking_detail.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_course_schedule.rec_archive')
                    ->orWhere('sis_course_schedule.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereIn('db14983', [2, 3])
                    ->orWhereIn('db59978', [2, 3]);
            })
            ->where(function ($q) {
                $q->whereNull('db66750')
                    ->orWhere('db66750', '!=', 'yes');
            })
            ->where(function ($q) {
                $q->where('db284645', '=', 'yes')
                    ->orWhereNull('db284645')
                    ->orWhere('db284645', '');
            })
            ->whereRaw("(SELECT COUNT(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = db14977 AND (sis_scheduled_sessions.rec_archive IS NULL OR sis_scheduled_sessions.rec_archive = '')) > 0")
            ->whereRaw("db14947 > CURDATE() AND db14947 = DATE_ADD(CURDATE(), INTERVAL (SELECT db111503 FROM lead_preferences WHERE usergroup = sis_scheduled_booking.usergroup) DAY)")
            ->whereRaw("(SELECT db111515 FROM lead_preferences WHERE usergroup = sis_scheduled_booking.usergroup) = 'yes'")
            ->orderBy('sis_scheduled_booking.id');
        $query->chunk(100, function ($results) {
            foreach ($results as $notification_to_be_sent) {
                $this->info('Processing for : ' . $notification_to_be_sent->student_name);
                $this->process_notifications($notification_to_be_sent, 'with_sessions_not2', 'course_booking_reminder', 'db111503');
                $this->processed_jobs++;
            }
        });
        $this->info('ending with_sessions_not2' . date('m/d/Y h:i:s a', time()));

        //reminder notification 2
        // all session bookings that are to get session reminder 2
        $this->info('starting with_all_sessions_notification2' . date('m/d/Y h:i:s a', time()));
        $query = DB::table('sis_session_bookings')
            ->select([
                'sis_session_bookings.id',
                'sis_scheduled_booking.rel_id',
                DB::raw("CONCAT(db48566, ' ', db48568) AS full_name"),
                DB::raw('db48619 AS title'),
                DB::raw('db48566 AS student_name'),
                DB::raw('db48599 AS student_number'),
                DB::raw('db48583 AS student_email'),
                DB::raw('db48621 AS student_address'),
                DB::raw('db48596 AS student_town'),
                DB::raw('db48622 AS student_postcode'),
                DB::raw('db48597 AS student_phone'),
                DB::raw('db232 AS course_title'),
                DB::raw("IFNULL(db234,'') AS course_summary"),
                DB::raw('db59906 AS course_session_name'),
                DB::raw('db59835 AS course_start_date'),
                DB::raw('db59836 AS course_start_time'),
                DB::raw('db59837 AS course_end_date'),
                DB::raw('db59838 AS course_end_time'),
                DB::raw("IFNULL(db14953,'') AS course_minimum_attendees"),
                DB::raw("CONCAT_WS(', ', db14963, db14965, db14966) AS course_venue"),
                DB::raw('db14968 AS course_venue_information'),
                DB::raw('db14966 AS course_postcode'),
                DB::raw("IFNULL(db209162,'') AS course_whatthreewords"),
                DB::raw("IFNULL(db237356,'') AS course_venue_image"),
                DB::raw('db14970 AS booking_status'),
                DB::raw('db63025 AS scheduled_session_information'),
                DB::raw('db63007 AS scheduled_course_information'),
                DB::raw('db14956 AS additional_information'),
                'sis_scheduled_booking.usergroup',
                DB::raw('db19831 AS reminder_template'),
                DB::raw('sis_course_schedule.id AS course_schedule'),
                DB::raw('sis_profiles.id AS sis_profile_id'),
                DB::raw('sis_profiles.rel_id AS sis_profile_rel_id'),
                DB::raw('sis_sched_booking_detail.id AS sched_detail_id'),
                DB::raw('8 AS letter_tag'),
                DB::raw('8 AS sms_template_tag'),
            ])
            ->leftJoin('form_schools', 'sis_session_bookings.usergroup', '=', 'form_schools.id')
            ->leftJoin('sis_scheduled_booking', 'sis_session_bookings.db59976', '=', 'sis_scheduled_booking.id')
            ->leftJoin('sis_sched_booking_detail', DB::raw('CAST(sis_session_bookings.db59977 AS UNSIGNED)'), '=', 'sis_sched_booking_detail.id')
            ->leftJoin('core_students', 'core_students.id', '=', 'sis_sched_booking_detail.rel_id')
            ->leftJoin('sis_profiles', 'sis_profiles.rel_id', '=', 'core_students.rel_id')
            ->leftJoin('sis_course_schedule', 'sis_course_schedule.id', '=', 'db14977')
            ->leftJoin('sis_booking_status', 'sis_booking_status.id', '=', 'db59978')
            ->leftJoin('core_courses', 'core_courses.id', '=', 'db16136')
            ->leftJoin('sis_scheduled_sessions', 'sis_scheduled_sessions.id', '=', 'db59901')
            ->leftJoin('sis_course_venues', 'sis_course_venues.id', '=', 'db59839')
            ->leftJoin('sis_course_sessions', 'sis_course_sessions.id', '=', 'db59900')
            ->where('db30', '12')
            ->where(function ($q) {
                $q->whereNull('sis_scheduled_booking.rec_archive')->orWhere('sis_scheduled_booking.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('core_students.rec_archive')->orWhere('core_students.rec_archive', '');
            })
            ->where('db14959', '!=', 5)
            ->where('db59908', '!=', 5)
            ->where(function ($q) {
                $q->where('db284645', 'yes')->orWhereNull('db284645')->orWhere('db284645', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_session_bookings.rec_archive')->orWhere('sis_session_bookings.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_profiles.rec_archive')->orWhere('sis_profiles.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_sched_booking_detail.rec_archive')->orWhere('sis_sched_booking_detail.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_scheduled_sessions.rec_archive')->orWhere('sis_scheduled_sessions.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_course_schedule.rec_archive')->orWhere('sis_course_schedule.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereIn('db14983', [2, 3])->orWhereIn('db59978', [2, 3]);
            })
            ->whereIn('db59902', [2, 3])
            ->where(function ($q) {
                $q->whereNull('db59905')->orWhere('db59905', '!=', 'yes');
            })
            ->whereRaw("db59835 > CURDATE() AND db59835 = DATE_ADD(CURDATE(), INTERVAL (SELECT db111503 FROM lead_preferences WHERE usergroup = sis_scheduled_booking.usergroup) DAY)")
            ->whereRaw("(SELECT db111515 FROM lead_preferences WHERE usergroup = sis_scheduled_booking.usergroup) != 'yes' OR (SELECT db111515 FROM lead_preferences WHERE usergroup = sis_scheduled_booking.usergroup) IS NULL")
            ->orderBy('sis_session_bookings.id');
        $query->chunk(100, function ($results) {
            foreach ($results as $notification_to_be_sent) {
                $this->info('Processing for : ' . $notification_to_be_sent->student_name);
                $this->process_notifications($notification_to_be_sent, 'with_all_sessions_notification2', 'course_session_reminder', 'db111503');
                $this->processed_jobs++;
            }
        });
        $this->info('ending with_all_sessions_notification2' . date('m/d/Y h:i:s a', time()));
        // confirmation notifications
        $this->info('starting confirm_email_notification' . date('m/d/Y h:i:s a', time()));
        $query = DB::table('sis_sched_booking_detail')
            ->select([
                'sis_scheduled_booking.id',
                'sis_scheduled_booking.rel_id',
                DB::raw("CONCAT(db48566, ' ', db48568) AS full_name"),
                'db48619 as title',
                'db48566 as student_name',
                'db48599 as student_number',
                'db48583 as student_email',
                'db48621 as student_address',
                'db48596 as student_town',
                'db48622 as student_postcode',
                'db48597 as student_phone',
                'db232 as course_title',
                DB::raw("IFNULL(db234,'') as course_summary"),
                'db14947 as course_start_date',
                'db14949 as course_end_date',
                'db14948 as course_start_time',
                'db14950 as course_end_time',
                'db63007 as scheduled_course_information',
                'db14956 as additional_information',
                DB::raw("IFNULL(db14953,'') as course_minimum_attendees"),
                DB::raw("CONCAT_WS(', ',db14963, db14965, db14966) as course_venue"),
                'db14968 as course_venue_information',
                'db14966 as course_postcode',
                DB::raw("IFNULL(db209162,'') as course_whatthreewords"),
                DB::raw("IFNULL(db237356,'') as course_venue_image"),
                'db14970 as booking_status',
                'sis_course_schedule.id as course_schedule',
                'sis_scheduled_booking.usergroup',
                'db237314 as reminder_template',
                'sis_profiles.id as sis_profile_id',
                'sis_profiles.rel_id as sis_profile_rel_id',
                'sis_sched_booking_detail.id as sched_detail_id',
                DB::raw('20 as letter_tag'),
                DB::raw('23 as sms_template_tag'),
            ])
            ->leftJoin('form_schools', 'sis_sched_booking_detail.usergroup', '=', 'form_schools.id')
            ->leftJoin('sis_scheduled_booking', 'db15052', '=', 'sis_scheduled_booking.id')
            ->leftJoin('core_students', 'core_students.id', '=', 'sis_sched_booking_detail.rel_id')
            ->leftJoin('sis_profiles', 'sis_profiles.rel_id', '=', 'core_students.rel_id')
            ->leftJoin('sis_course_schedule', 'sis_course_schedule.id', '=', 'db14977')
            ->leftJoin('sis_booking_status', 'sis_booking_status.id', '=', 'db14983')
            ->leftJoin('core_courses', 'core_courses.id', '=', 'db16136')
            ->leftJoin('sis_course_venues', 'sis_course_venues.id', '=', 'db14954')
            ->where('db30', '12')
            ->where(function ($query) {
                $query->whereNull('sis_scheduled_booking.rec_archive')->orWhere('sis_scheduled_booking.rec_archive', '');
            })
            ->where(function ($query) {
                $query->whereNull('sis_profiles.rec_archive')->orWhere('sis_profiles.rec_archive', '');
            })
            ->where(function ($query) {
                $query->whereNull('sis_course_schedule.rec_archive')->orWhere('sis_course_schedule.rec_archive', '');
            })
            ->where(function ($query) {
                $query->whereNull('sis_sched_booking_detail.rec_archive')->orWhere('sis_sched_booking_detail.rec_archive', '');
            })
            ->where(function ($query) {
                $query->where('db14983', 2)->orWhere('db14983', 3)->orWhere('db59978', 2)->orWhere('db59978', 3);
            })
            ->where('db14959', '!=', 5)
            ->where(function ($query) {
                $query->where('db284645', 'yes')->orWhereNull('db284645')->orWhere('db284645', '');
            })
            ->where(function ($query) {
                $query->whereNull('db250781')->orWhere('db250781', '!=', 'yes');
            })
            ->where('db14947', '>', DB::raw('CURDATE()'))
            ->whereRaw("db14947 = DATE_ADD(CURDATE(), INTERVAL (SELECT db237194 FROM lead_preferences WHERE usergroup= sis_scheduled_booking.usergroup) DAY)")
            ->orderBy('sis_scheduled_booking.id');
        $query->chunk(100, function ($results) {
            foreach ($results as $notification_to_be_sent) {
                $this->info('Processing for : ' . $notification_to_be_sent->student_name);
                $this->process_notifications($notification_to_be_sent, 'confirm_email_notification', 'confirm_email_notification', 'db237194');
                $this->processed_jobs++;
            }
        });
        $this->info('ending confirm_email_notification' . date('m/d/Y h:i:s a', time()));


        /// sms course reminder hours before course starts
        $this->info('starting sms_course_reminder_no_sessions' . date('m/d/Y h:i:s a', time()));
        $query = DB::table('sis_sched_booking_detail')
            ->select([
                'sis_scheduled_booking.id',
                'sis_scheduled_booking.rel_id',
                DB::raw("CONCAT(db48566, ' ', db48568) AS full_name"),
                'db48619 as title',
                'db48566 as student_name',
                'db48599 as student_number',
                'db48583 as student_email',
                'db48621 as student_address',
                'db48596 as student_town',
                'db48622 as student_postcode',
                'db48597 as student_phone',
                'db232 as course_title',
                DB::raw("IFNULL(db234,'') AS course_summary"),
                'db14947 as course_start_date',
                'db14948 as course_start_time',
                'db14949 as course_end_date',
                'db14950 as course_end_time',
                'db63007 as scheduled_course_information',
                'db14956 as additional_information',
                DB::raw("IFNULL(db14953,'') as course_minimum_attendees"),
                DB::raw("CONCAT_WS(', ', db14963, db14965, db14966) as course_venue"),
                'db14968 as course_venue_information',
                'db14966 as course_postcode',
                DB::raw("IFNULL(db209162,'') as course_whatthreewords"),
                DB::raw("IFNULL(db237356,'') as course_venue_image"),
                'db14970 as booking_status',
                'sis_course_schedule.id as course_schedule',
                'sis_scheduled_booking.usergroup',
                'db237314 as reminder_template',
                'sis_profiles.id as sis_profile_id',
                'sis_profiles.rel_id as sis_profile_rel_id',
                'sis_sched_booking_detail.id as sched_detail_id',
                DB::raw('5 as sms_template_tag')
            ])
            ->leftJoin('form_schools', 'sis_sched_booking_detail.usergroup', '=', 'form_schools.id')
            ->leftJoin('sis_scheduled_booking', 'db15052', '=', 'sis_scheduled_booking.id')
            ->leftJoin('core_students', 'core_students.id', '=', 'sis_sched_booking_detail.rel_id')
            ->leftJoin('sis_profiles', 'sis_profiles.rel_id', '=', 'core_students.rel_id')
            ->leftJoin('sis_course_schedule', 'sis_course_schedule.id', '=', 'db14977')
            ->leftJoin('sis_booking_status', 'sis_booking_status.id', '=', 'db14983')
            ->leftJoin('core_courses', 'core_courses.id', '=', 'db16136')
            ->leftJoin('sis_course_venues', 'sis_course_venues.id', '=', 'db14954')
            ->leftJoin(DB::raw("(SELECT id, CONCAT(SUBSTRING(db14948, 1, 2), ':', SUBSTRING(db14948, 3, 2), ' 00') as startTime FROM sis_course_schedule) as scs2"), 'sis_course_schedule.id', '=', 'scs2.id')
            ->where('db30', '=', '12')
            ->whereNull('sis_scheduled_booking.rec_archive')
            ->whereNull('sis_profiles.rec_archive')
            ->whereNull('sis_course_schedule.rec_archive')
            ->whereNull('sis_sched_booking_detail.rec_archive')
            ->whereIn('db14983', [2, 3])
            ->orWhereIn('db59978', [2, 3])
            ->where('db14959', '!=', 5)
            ->where(function ($query) {
                $query->where('db284645', '=', 'yes')
                    ->orWhereNull('db284645')
                    ->orWhere('db284645', '=', '');
            })
            ->whereRaw("(SELECT count(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = db14977 AND (sis_scheduled_sessions.rec_archive IS NULL OR sis_scheduled_sessions.rec_archive = '')) = 0")
            ->where(function ($query) {
                $query->whereNull('db262322')->orWhere('db262322', '!=', 'yes');
            })
            ->where('db262277', '=', 'yes')
            ->whereRaw('db14947 = CURDATE()')
            ->whereRaw("TIMEDIFF(scs2.startTime, CURTIME()) <= TIME(CONCAT((SELECT db262367 FROM lead_preferences WHERE usergroup = sis_scheduled_booking.usergroup), '', '0000'))")
            ->whereRaw('scs2.startTime > CURTIME()')
            ->orderBy('sis_scheduled_booking.id');
        $query->chunk(100, function ($results) {
            foreach ($results as $notification_to_be_sent) {
                $this->info('Processing for : ' . $notification_to_be_sent->student_name);
                $this->process_notifications($notification_to_be_sent, 'sms_course_reminder_no_sessions', 'sms_course_reminder_no_sessions', 'db262367', true);
                $this->processed_jobs++;
            }
        });
        $this->info('ending sms_course_reminder_no_sessions' . date('m/d/Y h:i:s a', time()));
        $this->info('starting sms_course_reminder_with_sessions' . date('m/d/Y h:i:s a', time()));
        $query = DB::table('sis_session_bookings')
            ->select([
                'sis_session_bookings.id',
                'sis_scheduled_booking.rel_id',
                DB::raw("CONCAT(db48566, ' ', db48568) AS full_name"),
                'db48619 as title',
                'db48566 as student_name',
                'db48599 as student_number',
                'db48583 as student_email',
                'db48621 as student_address',
                'db48596 as student_town',
                'db48622 as student_postcode',
                'db48597 as student_phone',
                'db232 as course_title',
                DB::raw("IFNULL(db234, '') AS course_summary"),
                'db59906 as course_session_name',
                'db59835 as course_start_date',
                'db59836 as course_start_time',
                'db59837 as course_end_date',
                'db59838 as course_end_time',
                DB::raw("IFNULL(db14953, '') AS course_minimum_attendees"),
                DB::raw("CONCAT_WS(', ', db14963, db14965, db14966) as course_venue"),
                'db14968 as course_venue_information',
                'db14966 as course_postcode',
                DB::raw("IFNULL(db209162, '') AS course_whatthreewords"),
                DB::raw("IFNULL(db237356, '') AS course_venue_image"),
                'db14970 as booking_status',
                'db63025 as scheduled_session_information',
                'db63007 as scheduled_course_information',
                'db14956 as additional_information',
                'sis_scheduled_booking.usergroup',
                'db19831 as reminder_template',
                'sis_course_schedule.id as course_schedule',
                'sis_profiles.id as sis_profile_id',
                'sis_profiles.rel_id as sis_profile_rel_id',
                'sis_sched_booking_detail.id as sched_detail_id',
                DB::raw("8 as letter_tag"),
                DB::raw("8 as sms_template_tag")
            ])
            ->leftJoin('form_schools', 'sis_session_bookings.usergroup', '=', 'form_schools.id')
            ->leftJoin('sis_scheduled_booking', 'sis_session_bookings.db59976', '=', 'sis_scheduled_booking.id')
            ->leftJoin('sis_sched_booking_detail', DB::raw('CAST(sis_session_bookings.db59977 AS UNSIGNED)'), '=', 'sis_sched_booking_detail.id')
            ->leftJoin('core_students', 'core_students.id', '=', 'sis_sched_booking_detail.rel_id')
            ->leftJoin('sis_profiles', 'sis_profiles.rel_id', '=', 'core_students.rel_id')
            ->leftJoin('sis_course_schedule', 'sis_course_schedule.id', '=', 'db14977')
            ->leftJoin('sis_booking_status', 'sis_booking_status.id', '=', 'db59978')
            ->leftJoin('core_courses', 'core_courses.id', '=', 'db16136')
            ->leftJoin('sis_scheduled_sessions', 'sis_scheduled_sessions.id', '=', 'db59901')
            ->leftJoin('sis_course_venues', 'sis_course_venues.id', '=', 'db59839')
            ->leftJoin('sis_course_sessions', 'sis_course_sessions.id', '=', 'db59900')
            ->leftJoin(DB::raw('(SELECT id, CONCAT(SUBSTRING(db59836, 1, 2), ":", SUBSTRING(db59836, 3, 2), " 00") AS startTime FROM sis_scheduled_sessions) AS sss2'), 'sis_scheduled_sessions.id', '=', 'sss2.id')
            ->where('db30', '=', '12')
            ->where(function ($q) {
                $q->whereNull('sis_scheduled_booking.rec_archive')->orWhere('sis_scheduled_booking.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('core_students.rec_archive')->orWhere('core_students.rec_archive', '');
            })
            ->where('db14959', '!=', 5)
            ->where('db59908', '!=', 5)
            ->where(function ($q) {
                $q->whereNull('sis_session_bookings.rec_archive')->orWhere('sis_session_bookings.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_profiles.rec_archive')->orWhere('sis_profiles.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_sched_booking_detail.rec_archive')->orWhere('sis_sched_booking_detail.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_scheduled_sessions.rec_archive')->orWhere('sis_scheduled_sessions.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereNull('sis_course_schedule.rec_archive')->orWhere('sis_course_schedule.rec_archive', '');
            })
            ->where(function ($q) {
                $q->whereIn('db14983', [2, 3])->orWhereIn('db59978', [2, 3]);
            })
            ->whereIn('db59902', [2, 3])
            ->where(function ($q) {
                $q->whereNull('db284645')->orWhere('db284645', '')->orWhere('db284645', '=', 'yes');
            })
            ->where(function ($q) {
                $q->whereNull('db262640')->orWhere('db262640', '!=', 'yes');
            })
            ->where('db262277', '=', 'yes')
            ->where('db59835', '=', DB::raw('CURDATE()'))
            ->whereRaw("TIMEDIFF(sss2.startTime, CURTIME()) <= TIME(CONCAT((SELECT db262367 FROM lead_preferences WHERE usergroup = sis_scheduled_booking.usergroup), '', '0000'))")
            ->whereRaw('sss2.startTime > CURTIME()')
            ->where(function ($q) {
                $q->whereRaw("(SELECT db111515 FROM lead_preferences WHERE usergroup = sis_scheduled_booking.usergroup) != 'yes'")
                    ->orWhereRaw("(SELECT db111515 FROM lead_preferences WHERE usergroup = sis_scheduled_booking.usergroup) IS NULL");
            })
            ->orderBy('sis_session_bookings.id');
        $query->chunk(100, function ($results) {
            foreach ($results as $notification_to_be_sent) {
                $this->info('Processing for : ' . $notification_to_be_sent->student_name);
                $this->process_notifications($notification_to_be_sent, 'sms_course_reminder_with_sessions', 'sms_course_reminder_with_sessions', 'db262367', true);
                $this->processed_jobs++;
            }
        });
        $this->info('ending sms_course_reminder_with_sessions' . date('m/d/Y h:i:s a', time()));

        /// confirmation email on waitling list courses
        // confirmation notifications
        //if there are people on a waiting list send a confirmation email x days to the attendees to confirm they are coming
        $this->info('starting waitinglist_confirm_email_notification' . date('m/d/Y h:i:s a', time()));
        $query = DB::table('sis_sched_booking_detail')
            ->select([
                'sis_scheduled_booking.id',
                'sis_scheduled_booking.rel_id',
                DB::raw("CONCAT(db48566, ' ', db48568) as full_name"),
                'db48619 as title',
                'db48566 as student_name',
                'db48599 as student_number',
                'db48583 as student_email',
                'db48621 as student_address',
                'db48596 as student_town',
                'db48622 as student_postcode',
                'db48597 as student_phone',
                'db232 as course_title',
                DB::raw("IFNULL(db234,'') as course_summary"),
                'db14947 as course_start_date',
                'db14948 as course_start_time',
                'db14949 as course_end_date',
                'db14950 as course_end_time',
                'db63007 as scheduled_course_information',
                'db14956 as additional_information',
                DB::raw("IFNULL(db14953,'') as course_minimum_attendees"),
                DB::raw("CONCAT_WS(', ', db14963, db14965, db14966) as course_venue"),
                'db14968 as course_venue_information',
                'db14966 as course_postcode',
                DB::raw("IFNULL(db209162,'') as course_whatthreewords"),
                DB::raw("IFNULL(db237356,'') as course_venue_image"),
                'db14970 as booking_status',
                'sis_course_schedule.id as course_schedule',
                'sis_scheduled_booking.usergroup',
                'db237314 as reminder_template',
                'sis_profiles.id as sis_profile_id',
                'sis_profiles.rel_id as sis_profile_rel_id',
                'sis_sched_booking_detail.id as sched_detail_id',
                DB::raw("20 as letter_tag"),
                DB::raw("23 as sms_template_tag"),
            ])
            ->leftJoin('form_schools', 'sis_sched_booking_detail.usergroup', '=', 'form_schools.id')
            ->leftJoin('sis_scheduled_booking', 'db15052', '=', 'sis_scheduled_booking.id')
            ->leftJoin('core_students', 'core_students.id', '=', 'sis_sched_booking_detail.rel_id')
            ->leftJoin('sis_profiles', 'sis_profiles.rel_id', '=', 'core_students.rel_id')
            ->leftJoin('sis_course_schedule', 'sis_course_schedule.id', '=', DB::raw('db14977'))
            ->leftJoin('sis_booking_status', 'sis_booking_status.id', '=', DB::raw('db14983'))
            ->leftJoin('core_courses', 'core_courses.id', '=', DB::raw('db16136'))
            ->leftJoin('sis_course_venues', 'sis_course_venues.id', '=', DB::raw('db14954'))
            ->leftJoin(DB::raw("(
        SELECT CAST(db14977 AS UNSIGNED) as sch_course_id, COUNT(sis_sched_booking_detail.id) as cnt
        FROM sis_sched_booking_detail
        LEFT JOIN sis_scheduled_booking ON sis_scheduled_booking.id = db15052
        LEFT JOIN core_students ON core_students.id = db16135
        LEFT JOIN form_schools ON sis_sched_booking_detail.usergroup = form_schools.id
        WHERE db30 = '12'
          AND (sis_scheduled_booking.rec_archive IS NULL OR sis_scheduled_booking.rec_archive = '')
          AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
          AND (core_students.rec_archive IS NULL OR core_students.rec_archive = '')
          AND db59978 = '1'
        GROUP BY db14977
    ) as status_waiting_list"), 'status_waiting_list.sch_course_id', '=', 'sis_course_schedule.id')
            ->where('db30', '12')
            ->where(function ($query) {
                $query->whereNull('sis_scheduled_booking.rec_archive')
                    ->orWhere('sis_scheduled_booking.rec_archive', '');
            })
            ->where(function ($query) {
                $query->whereNull('sis_profiles.rec_archive')
                    ->orWhere('sis_profiles.rec_archive', '');
            })
            ->where(function ($query) {
                $query->whereNull('sis_course_schedule.rec_archive')
                    ->orWhere('sis_course_schedule.rec_archive', '');
            })
            ->where(function ($query) {
                $query->whereNull('sis_sched_booking_detail.rec_archive')
                    ->orWhere('sis_sched_booking_detail.rec_archive', '');
            })
            ->whereIn(DB::raw('db59978'), ['2', '3'])
            ->where('db14959', '!=', '5')
            ->where(function ($query) {
                $query->where('db284645', 'yes')
                    ->orWhereNull('db284645')
                    ->orWhere('db284645', '');
            })
            ->where(DB::raw('status_waiting_list.cnt'), '>', 0)
            ->where(function ($query) {
                $query->whereNull('db261929')
                    ->orWhere('db261929', '!=', 'yes');
            })
            ->where(DB::raw('db14947'), '>', DB::raw('CURDATE()'))
            ->whereRaw("db14947 = DATE_ADD(CURDATE(), INTERVAL (SELECT db261218 FROM lead_preferences WHERE usergroup= sis_scheduled_booking.usergroup) DAY)")
            ->orderBy('sis_scheduled_booking.id');
        $query->chunk(100, function ($results) {
            foreach ($results as $notification_to_be_sent) {
                $this->info('Processing for : ' . $notification_to_be_sent->student_name);
                $this->process_notifications($notification_to_be_sent, 'waitinglist_confirm_email_notification', 'waitinglist_confirm_email_notification', 'db261218');
                $this->processed_jobs++;
            }
        });
        $this->info('ending waitinglist_confirm_email_notification' . date('m/d/Y h:i:s a', time()));
        $this->info('Ending scheduled course reminder' . date('m/d/Y h:i:s a', time()));
    }


    //process the notifications

    /**
     */
    protected function process_notifications($notification_to_be_sent, $type_of_sql = '', $template_tag = '', $interval_db_field = '', $alwaysSend = ''): string
    {

        $shortcoursesModel = new \ShortcoursesModel();
        $usergroup = $notification_to_be_sent->usergroup;
        $texting_allowed = pull_field("lead_preferences", "db111494", "WHERE usergroup='{$usergroup}'");
        $prioritise_email = pull_field("lead_preferences", "db236852", "WHERE usergroup='{$usergroup}'");

        $comms_preference = $this->get_method_of_communication($notification_to_be_sent->sis_profile_id);
        if (empty($notification_to_be_sent->course_session_name)) {
            $notification_to_be_sent->course_session_name = '';
        }
        if (empty($notification_to_be_sent->scheduled_session_information)) {
            $notification_to_be_sent->scheduled_session_information = '';
        }

        list($course_booking_notification_allowed, $start_notification_time, $end_notification_time, $interval) = explode(',', pull_field("lead_preferences", "CONCAT_WS(',',db111497,db111506,db111509,$interval_db_field )", "WHERE usergroup='$notification_to_be_sent->usergroup' LIMIT 1"));
        $between_times = false;
        $reminders_sent = '';
        if ($course_booking_notification_allowed != 'None' && $interval != '0' && $interval != '') {
            if ($start_notification_time && $start_notification_time != '' && $end_notification_time && $end_notification_time != '') {
                date_default_timezone_set('Europe/London');
                if ($this->is_between_times($start_notification_time, $end_notification_time)) {
                    $between_times = true;
                }
            }

            // set this to override reminder settings when sending out reminders hours before the course strta
            if (!empty($alwaysSend)) {
                $between_times = true;
                $comms_preference = ['sms'];
                $texting_allowed = 'yes';
                $prioritise_email = 'no';
            }

            if ($between_times && !empty($comms_preference)) {
                $course_date = date_create($notification_to_be_sent->course_start_date);
                ///format the date according to the preferences else default setting.
                $course_date = format_date_for_notifications($notification_to_be_sent->course_start_date);
                $course_end_date = format_date_for_notifications($notification_to_be_sent->course_end_date);
                $session_info = $shortcoursesModel->get_session_info($notification_to_be_sent->course_schedule);
                $session_info_no_venue = $shortcoursesModel->get_session_info($notification_to_be_sent->course_schedule, '', 'no');
                $session_info_with_webconferencing = $shortcoursesModel->get_session_info($notification_to_be_sent->course_schedule, '', 'no', 'yes');
                $remaining_session_info = $shortcoursesModel->get_session_info($notification_to_be_sent->course_schedule, $notification_to_be_sent->course_start_date);
                $remaining_session_info_no_venue = $shortcoursesModel->get_session_info($notification_to_be_sent->course_schedule, $notification_to_be_sent->course_start_date, 'no');
                $remaining_session_info_with_webconferencing = $shortcoursesModel->get_session_info($notification_to_be_sent->course_schedule, $notification_to_be_sent->course_start_date, 'no', 'yes');
                $has_email = $has_sms = false;
                $course_start_time = substr($notification_to_be_sent->course_start_time, 0, 2) . ':' . substr($notification_to_be_sent->course_start_time, 2, 2);
                $course_end_time = substr($notification_to_be_sent->course_end_time, 0, 2) . ':' . substr($notification_to_be_sent->course_end_time, 2, 2);
                $course_time = $course_start_time . ' - ' . $course_end_time;

                //if coms_pref is email
                if (in_array('email', $comms_preference)) {
                    $has_email = true;
                    $reminder_template = $notification_to_be_sent->reminder_template;
                    if (!empty($reminder_template)) {
                        $template = $reminder_template;
                        $template_data = DB::table('coms_template')
                            ->where('id', $template)
                            ->select('db1085 as template', 'db1086 as subject')
                            ->first();
                        $message_html = $template_data->template;
                        $message_subject = $template_data->subject;

                        $message_html = email_template_replace_values("{{name}}", $notification_to_be_sent->student_name, $message_html);
                        $message_html = email_template_replace_values("{{first_name}}", $notification_to_be_sent->student_name, $message_html);
                        $message_html = email_template_replace_values('{{course_id}}', $notification_to_be_sent->course_schedule, $message_html);
                        $message_html = email_template_replace_values("{{course_name}}", $notification_to_be_sent->course_title, $message_html);
                        $message_html = email_template_replace_values("{{course_summary}}", $notification_to_be_sent->course_summary, $message_html);
                        $message_html = email_template_replace_values("{{course_date}}", $course_date, $message_html);
                        $message_html = email_template_replace_values("{{course_start_date}}", $course_date, $message_html);
                        $message_html = email_template_replace_values("{{course_end_date}}", $course_end_date, $message_html);
                        $message_html = email_template_replace_values("{{course_start_time}}", $course_start_time, $message_html);
                        $message_html = email_template_replace_values("{{course_end_time}}", $course_end_time, $message_html);
                        $message_html = email_template_replace_values("{{course_time}}", $course_time, $message_html);
                        $message_html = email_template_replace_values("{{course_venue}}", $notification_to_be_sent->course_venue, $message_html);
                        $message_html = email_template_replace_values("{{course_venue_information}}", $notification_to_be_sent->course_venue_information, $message_html);
                        $message_html = email_template_replace_values("{{course_postcode}}", $notification_to_be_sent->course_postcode, $message_html);
                        $message_html = email_template_replace_values("{{course_whatthreewords}}", $notification_to_be_sent->course_whatthreewords, $message_html);
                        if (!empty($notification_to_be_sent->course_venue_image)) {
                            $image_path = engine_url("/media/dl.php?a=yes&fl=" . encode($notification_to_be_sent->course_venue_image, 'unsalted'));
                            $venue_image = " <img src=\"$image_path\">";
                        }
                        $message_html = email_template_replace_values('{{course_venue_image}}', $venue_image, $message_html);

                        $message_html = email_template_replace_values("{{full_name}}", $notification_to_be_sent->full_name, $message_html);
                        $message_html = email_template_replace_values("{{title}}", $notification_to_be_sent->title, $message_html);
                        $message_html = email_template_replace_values("{{web_conferencing_information}}", $notification_to_be_sent->scheduled_course_information, $message_html);
                        $message_html = email_template_replace_values("{{additional_information}}", $notification_to_be_sent->additional_information, $message_html);
                        $message_html = email_template_replace_values("{{course_session_name}}", $notification_to_be_sent->course_session_name, $message_html);
                        $message_html = email_template_replace_values("{{session_start_time}}", $course_time, $message_html);
                        $message_html = email_template_replace_values("{{session_start_date}}", $course_date, $message_html);
                        $message_html = email_template_replace_values("{{session_end_date}}", $course_end_date, $message_html);
                        $message_html = email_template_replace_values("{{scheduled_session_information}}", $notification_to_be_sent->scheduled_session_information, $message_html);
                        $message_html = str_replace("{{session_info}}", $session_info, $message_html);
                        $message_html = str_replace("{{session_info_no_venue}}", $session_info_no_venue, $message_html);
                        $message_html = str_replace("{{session_info_with_webconferencing}}", $session_info_with_webconferencing, $message_html);
                        $message_html = str_replace("{{remaining_session_info}}", $remaining_session_info, $message_html);
                        $message_html = str_replace("{{remaining_session_info_no_venue}}", $remaining_session_info_no_venue, $message_html);
                        $message_html = str_replace("{{remaining_session_info_with_webconferencing}}", $remaining_session_info_with_webconferencing, $message_html);
                        $message_html = str_replace("{{course_minimum_attendees}}", $notification_to_be_sent->course_minimum_attendees, $message_html);


                        //mail merge the subject
                        $message_subject = email_template_replace_values("{{name}}", $notification_to_be_sent->student_name, $message_subject);
                        $message_subject = email_template_replace_values("{{first_name}}", $notification_to_be_sent->student_name, $message_subject);
                        $message_subject = email_template_replace_values('{{course_id}}', $notification_to_be_sent->course_schedule, $message_subject);
                        $message_subject = email_template_replace_values("{{course_name}}", $notification_to_be_sent->course_title, $message_subject);
                        $message_subject = email_template_replace_values("{{course_date}}", $course_date, $message_subject);
                        $message_subject = email_template_replace_values("{{course_start_date}}", $course_date, $message_subject);
                        $message_subject = email_template_replace_values("{{course_start_time}}", $course_start_time, $message_subject);
                        $message_subject = email_template_replace_values("{{course_end_date}}", $course_end_date, $message_subject);
                        $message_subject = email_template_replace_values("{{course_end_time}}", $course_end_time, $message_subject);
                        $message_subject = email_template_replace_values("{{course_time}}", $course_time, $message_subject);
                        $message_subject = email_template_replace_values("{{course_venue}}", $notification_to_be_sent->course_venue, $message_subject);
                        $message_subject = email_template_replace_values("{{course_postcode}}", $notification_to_be_sent->course_postcode, $message_subject);
                        $message_subject = email_template_replace_values("{{course_whatthreewords}}", $notification_to_be_sent->course_whatthreewords, $message_subject);
                        $message_subject = email_template_replace_values("{{full_name}}", $notification_to_be_sent->full_name, $message_subject);
                        $message_subject = email_template_replace_values("{{title}}", $notification_to_be_sent->title, $message_subject);
                        $message_subject = email_template_replace_values("{{course_session_name}}", $notification_to_be_sent->course_session_name, $message_subject);
                        $message_subject = email_template_replace_values("{{session_start_time}}", $course_time, $message_subject);
                        $message_subject = email_template_replace_values("{{session_start_date}}", $course_date, $message_subject);
                        $message_subject = str_replace("{{course_minimum_attendees}}", $notification_to_be_sent->course_minimum_attendees, $message_subject);

                        $emails = new Email();
                        $message_subject = $notification_to_be_sent->course_title . " " . $message_subject;


                        $email_args = array(
                            'to' => $notification_to_be_sent->student_email,
                            'subject' => $message_subject,
                            'html' => $message_html,
                            'from' => pull_field("form_schools", "db1117", "WHERE id='$notification_to_be_sent->usergroup'"),
                            'usergroup' => $notification_to_be_sent->usergroup,
                            'rel_id' => $notification_to_be_sent->sis_profile_rel_id,
                            'template_id' => $reminder_template,
                        );
                        $emails->send($email_args);
                        $reminders_sent = 1;
                    }
                }
                $send_sms = true;
                if ($prioritise_email == 'yes' && !empty($has_email)) {
                    $send_sms = false;
                }

                //if coms pref is sms
                if (in_array('sms', $comms_preference) && $texting_allowed == 'yes' && !empty($send_sms)) {
                    $has_sms = true;
                    //get sms template
                    $sms_template_tag = $notification_to_be_sent->sms_template_tag;
                    $sms_template_id = pull_field('coms_sms_template', 'id', "WHERE db159320 = '{$sms_template_tag}' AND usergroup = {$_SESSION['usergroup']} AND (rec_archive is null or rec_archive ='') and db159983 = 'yes'");
                    if (empty($sms_template_id)) {
                        //get default
                        $sms_template_id = pull_field('coms_sms_template', 'id', "WHERE db159320 = '{$sms_template_tag}' AND usergroup = 1 AND (rec_archive is null or rec_archive ='') and db159983='yes'");
                    }
                    if (!empty($sms_template_id)) {
                        $sms_template = DB::table('coms_sms_template')
                            ->where('id', $sms_template_id)
                            ->select('db25661 as subject', 'db25663 as content')
                            ->first();

                        //send out sms
                        $messages = new \CommunicationMessages();
                        $subject = $sms_template->subject;
                        $recipient_number = $notification_to_be_sent->student_phone;
                        $send = 'Send';
                        $student_id = $notification_to_be_sent->sis_profile_rel_id;
                        $sms_text = $sms_template->content;
                        $usergroup = $notification_to_be_sent->usergroup;

                        $sms_text = email_template_replace_values("{{name}}", $notification_to_be_sent->student_name, $sms_text);
                        $sms_text = email_template_replace_values("{{first_name}}", $notification_to_be_sent->student_name, $sms_text);
                        $sms_text = email_template_replace_values("{{course_id}}", $notification_to_be_sent->course_schedule, $sms_text);
                        $sms_text = email_template_replace_values("{{course_name}}", $notification_to_be_sent->course_title, $sms_text);
                        $sms_text = email_template_replace_values("{{course_summary}}", $notification_to_be_sent->course_summary, $sms_text);
                        $sms_text = email_template_replace_values("{{course_date}}", $course_date, $sms_text);
                        $sms_text = email_template_replace_values("{{course_start_date}}", $course_date, $sms_text);
                        $sms_text = email_template_replace_values("{{course_end_date}}", $course_end_date, $sms_text);
                        $sms_text = email_template_replace_values("{{course_start_time}}", $course_start_time, $sms_text);
                        $sms_text = email_template_replace_values("{{course_end_time}}", $course_end_time, $sms_text);
                        $sms_text = email_template_replace_values("{{course_time}}", $course_time, $sms_text);
                        $sms_text = email_template_replace_values("{{course_venue}}", $notification_to_be_sent->course_venue, $sms_text);
                        $sms_text = email_template_replace_values("{{course_venue_information}}", $notification_to_be_sent->course_venue_information, $sms_text);
                        $sms_text = email_template_replace_values("{{course_postcode}}", $notification_to_be_sent->course_postcode, $sms_text);
                        $sms_text = email_template_replace_values("{{full_name}}", $notification_to_be_sent->full_name, $sms_text);
                        $sms_text = email_template_replace_values("{{title}}", $notification_to_be_sent->title, $sms_text);
                        $sms_text = email_template_replace_values("{{web_conferencing_information}}", $notification_to_be_sent->scheduled_course_information, $sms_text);
                        $sms_text = email_template_replace_values("{{additional_information}}", $notification_to_be_sent->additional_information, $sms_text);
                        $sms_text = email_template_replace_values("{{course_session_name}}", $notification_to_be_sent->course_session_name, $sms_text);
                        $sms_text = email_template_replace_values("{{session_start_time}}", $course_time, $sms_text);
                        $sms_text = email_template_replace_values("{{session_start_date}}", $course_date, $sms_text);
                        $sms_text = email_template_replace_values("{{session_end_date}}", $course_end_date, $sms_text);
                        $sms_text = email_template_replace_values("{{scheduled_session_information}}", $notification_to_be_sent->scheduled_session_information, $sms_text);
                        $sms_text = str_replace("{{session_info}}", $session_info, $sms_text);
                        $sms_text = str_replace("{{session_info_no_venue}}", $session_info_no_venue, $sms_text);
                        $sms_text = str_replace("{{session_info_with_webconferencing}}", $session_info_with_webconferencing, $sms_text);
                        $sms_text = str_replace("{{remaining_session_info}}", $remaining_session_info, $sms_text);
                        $sms_text = str_replace("{{remaining_session_info_no_venue}}", $remaining_session_info_no_venue, $sms_text);
                        $sms_text = str_replace("{{remaining_session_info_with_webconferencing}}", $remaining_session_info_with_webconferencing, $sms_text);


                        $sms_text = str_replace(['<br />', '<br>', '<br/>', '<p>', '</p>'], ' ', $sms_text);
                        $sms = $messages->log_sms($template = "", $subject, $sms_text, $recipient_number, $student_id, $usergroup, $send);
                        $reminders_sent = 1;

                    }


                }

                //if coms_pref is letter
                if (in_array('letter', $comms_preference) && empty($has_sms) && empty($has_email)) {
                    $letter_tag = $notification_to_be_sent->letter_tag;

                    //get letter template
                    //if reminder is a a course reminder
                    $letter_template_id = pull_field('core_letter_library', 'id', "WHERE db33619 = '$letter_tag' AND usergroup = $notification_to_be_sent->usergroup AND (rec_archive is null or rec_archive ='') and db1490 = 'yes'");
                    if (empty($letter_template_id)) {
                        //get default
                        $letter_template_id = pull_field('core_letter_library', 'id', "WHERE db33619 = '$letter_tag' AND usergroup = 1 AND (rec_archive is null or rec_archive ='') and db1490 = 'yes'");
                    }
                    $letter_template_data = DB::table('core_letter_library')
                        ->where('id', $letter_template_id)
                        ->select('db1488 as letter_content', 'db1487 as title')
                        ->first();

                    $letter_template = $letter_template_data->letter_content;
                    $letter_title = $notification_to_be_sent->course_title . ' ' . $notification_to_be_sent->course_start_date . ' ' . $letter_template_data->title;


                    $user_address_with_postcode = $notification_to_be_sent->student_address . '<br>' . $notification_to_be_sent->student_town . '<br>' . $notification_to_be_sent->student_postcode;
                    $user_address_no_postcode = $notification_to_be_sent->student_address . '<br>' . $notification_to_be_sent->student_town;

                    $letter_template = str_replace("{{address}}", $user_address_with_postcode, $letter_template);
                    $letter_template = str_replace("{{address_no_postcode}}", $user_address_no_postcode, $letter_template);
                    $letter_template = str_replace("{{postcode}}", $notification_to_be_sent->student_postcode, $letter_template);
                    $letter_template = str_replace("{{current_date}}", format_date_for_notifications(date("Y-m-d")), $letter_template);
                    $letter_template = email_template_replace_values("{{name}}", $notification_to_be_sent->student_name, $letter_template);
                    $letter_template = email_template_replace_values("{{first_name}}", $notification_to_be_sent->student_name, $letter_template);
                    $letter_template = email_template_replace_values("{{course_id}}", $notification_to_be_sent->course_schedule, $letter_template);
                    $letter_template = email_template_replace_values("{{course_name}}", $notification_to_be_sent->course_title, $letter_template);
                    $letter_template = email_template_replace_values("{{course_summary}}", $notification_to_be_sent->course_summary, $letter_template);
                    $letter_template = email_template_replace_values("{{course_date}}", $course_date, $letter_template);
                    $letter_template = email_template_replace_values("{{course_start_date}}", $course_date, $letter_template);
                    $letter_template = email_template_replace_values("{{course_end_date}}", $course_end_date, $letter_template);
                    $letter_template = email_template_replace_values("{{course_start_time}}", $course_start_time, $letter_template);
                    $letter_template = email_template_replace_values("{{course_end_time}}", $course_end_time, $letter_template);
                    $letter_template = email_template_replace_values("{{course_time}}", $course_time, $letter_template);
                    $letter_template = email_template_replace_values("{{course_venue}}", $notification_to_be_sent->course_venue, $letter_template);
                    $letter_template = email_template_replace_values("{{course_venue_information}}", $notification_to_be_sent->course_venue_information, $letter_template);
                    $letter_template = email_template_replace_values("{{course_postcode}}", $notification_to_be_sent->course_postcode, $letter_template);
                    $letter_template = email_template_replace_values("{{full_name}}", $notification_to_be_sent->full_name, $letter_template);
                    $letter_template = email_template_replace_values("{{title}}", $notification_to_be_sent->title, $letter_template);
                    $letter_template = email_template_replace_values("{{web_conferencing_information}}", $notification_to_be_sent->scheduled_course_information, $letter_template);
                    $letter_template = email_template_replace_values("{{additional_information}}", $notification_to_be_sent->additional_information, $letter_template);
                    $letter_template = email_template_replace_values("{{course_session_name}}", $notification_to_be_sent->course_session_name, $letter_template);
                    $letter_template = email_template_replace_values("{{session_start_time}}", $course_time, $letter_template);
                    $letter_template = email_template_replace_values("{{session_start_date}}", $course_date, $letter_template);
                    $letter_template = email_template_replace_values("{{session_end_date}}", $course_end_date, $letter_template);
                    $letter_template = email_template_replace_values("{{scheduled_session_information}}", $notification_to_be_sent->scheduled_session_information, $letter_template);
                    $letter_template = str_replace("{{session_info}}", $session_info, $letter_template);
                    $letter_template = str_replace("{{session_info_no_venue}}", $session_info_no_venue, $letter_template);
                    $letter_template = str_replace("{{session_info_with_webconferencing}}", $session_info_with_webconferencing, $letter_template);
                    $letter_template = str_replace("{{remaining_session_info}}", $remaining_session_info, $letter_template);
                    $letter_template = str_replace("{{remaining_session_info_no_venue}}", $remaining_session_info_no_venue, $letter_template);
                    $letter_template = str_replace("{{remaining_session_info_with_webconferencing}}", $remaining_session_info_with_webconferencing, $letter_template);
                    // insert letter
                    $letter_random = random();
                    $letter_template = addslashes($letter_template);
                    $letter_title = addslashes($letter_title);
                    $db20300 = '';
                    $db69980 = '';
                    $recipient_id = $notification_to_be_sent->sis_profile_rel_id;
                    DB::table('dir_letters_sent')->create([
                        'username_id' => $letter_random,
                        'rec_id' => 1,
                        'usergroup' => $notification_to_be_sent->usergroup,
                        'rel_id' => $recipient_id,
                        'db20300' => $db20300,
                        'db20301' => $letter_title,
                        'db31870' => $letter_template,
                        'db69980' => $db69980,
                    ]);
                    $last_id = DB::getPdo()->lastInsertId();

                    $sub_domain = pull_field('form_schools', 'db985', "WHERE id = $notification_to_be_sent->usergroup");

                    $http_link = env("PROTOCOL") . $sub_domain . '.' . env("APP_URL") . "/engine/modules/inc_letters_sent.php?&rec=$last_id&pdf=1";
                    DB::table('dir_letters_sent')
                        ->where('id', $last_id)
                        ->where('usergroup', $notification_to_be_sent->usergroup)
                        ->update([
                            'db20300' => $http_link,
                        ]);
                    $reminders_sent = 1;
                }

                if (in_array('phone', $comms_preference) & sizeof($comms_preference) == 1) {
                    $emails = new Email;
                    $admin_email = pull_field('form_schools', 'db1118', "WHERE id=$notification_to_be_sent->usergroup");
                    $email_message = "Dear Admin
								$notification_to_be_sent->full_name selected telephone number or home phone or phone call as a preferred method of communication.
								
								May you please get in touch with them and remind them of the booking for the $notification_to_be_sent->course_title starting on $notification_to_be_sent->course_start_date.
								
								The venue of the course is on/at $notification_to_be_sent->course_venue.
								
								May you please get in touch with the student, and remind them of this booking.
							
								Regards
					
							";
                    $coms_template_subject_line = 'Scheduled Course Reminder Request';

                    $email_args = [
                        'to' => $admin_email,
                        'subject' => $coms_template_subject_line,
                        'html' => nl2br($email_message),
                    ];
                    $emails->send($email_args);
                    $reminders_sent = 1;
                }

                if (in_array('mobile', $comms_preference) & sizeof($comms_preference) == 1) {
                    $emails = new Email;
                    $admin_email = pull_field('form_schools', 'db1118', "WHERE id={$usergroup}");
                    $email_message = "Dear Admin
								$notification_to_be_sent->full_name selected mobile number as a preferred method of communication.
								
								May you please get in touch with them and remind them of the booking for the $notification_to_be_sent->course_title starting on $notification_to_be_sent->course_start_date.
								
								The venue of the course is on/at $notification_to_be_sent->course_venue.
								
								May you please get in touch with the student, and remind them of this booking.
							
								Regards
					
							";
                    $coms_template_subject_line = 'Scheduled Course Reminder Request';

                    $email_args = [
                        'to' => $admin_email,
                        'subject' => $coms_template_subject_line,
                        'html' => nl2br($email_message),
                    ];
                    $emails->send($email_args);
                    $reminders_sent = 1;

                }
            }
        }

        if ($between_times && !empty($reminders_sent)) {
            if ($template_tag == 'course_booking_reminder') {
                if ($type_of_sql == 'with_no_sessions_not1') {
                    //update booking to say notification 1 has been sent
                    DB::table('sis_sched_booking_detail')
                        ->where('id', $notification_to_be_sent->sched_detail_id)
                        ->update([
                            'db66749' => 'yes',
                            'rec_lstup' => date('Y-m-d H:i:s'),
                            'rec_lstup_id' => 1,
                        ]);

                } else if ($type_of_sql == 'with_sessions_not1') {
                    //update booking to say notification 1 has been sent
                    DB::table('sis_sched_booking_detail')
                        ->where('id', $notification_to_be_sent->sched_detail_id)
                        ->update([
                            'db66749' => 'yes',
                            'rec_lstup' => date('Y-m-d H:i:s'),
                            'rec_lstup_id' => 1,
                        ]);
                    //mark all sessions that they have received notification 1
                    DB::table('sis_session_bookings')
                        ->where('db59977', $notification_to_be_sent->sched_detail_id)
                        ->update([
                            'db59904' => 'yes',
                            'rec_lstup' => date('Y-m-d H:i:s'),
                            'rec_lstup_id' => 1,
                        ]);

                } else if ($type_of_sql == 'with_no_sessions_not2') {
                    DB::table('sis_sched_booking_detail')
                        ->where('id', $notification_to_be_sent->sched_detail_id)
                        ->update([
                            'db66750' => 'yes',
                            'rec_lstup' => date('Y-m-d H:i:s'),
                            'rec_lstup_id' => 1,
                        ]);
                } else if ($type_of_sql == 'with_sessions_not2') {
                    //update booking to say notification 2 has been sent
                    DB::table('sis_sched_booking_detail')
                        ->where('id', $notification_to_be_sent->sched_detail_id)
                        ->update([
                            'db66750' => 'yes',
                            'rec_lstup' => date('Y-m-d H:i:s'),
                            'rec_lstup_id' => 1,
                        ]);

                    //mark all sessions that they have received notification 2
                    DB::table('sis_session_bookings')
                        ->where('db59977', $notification_to_be_sent->sched_detail_id)
                        ->update([
                            'db59905' => 'yes',
                            'rec_lstup' => date('Y-m-d H:i:s'),
                            'rec_lstup_id' => 1,
                        ]);

                }

            } else if ($template_tag == 'course_session_reminder') {

                if ($type_of_sql == 'with_all_sessions_notification1') {
                    DB::table('sis_session_bookings')
                        ->where('id', $notification_to_be_sent->id)
                        ->update([
                            'db59904' => 'yes',
                            'rec_lstup' => date('Y-m-d H:i:s'),
                            'rec_lstup_id' => 1,
                        ]);

                } else if ($type_of_sql == 'with_all_sessions_notification2') {
                    DB::table('sis_session_bookings')
                        ->where('id', $notification_to_be_sent->id)
                        ->update([
                            'db59905' => 'yes',
                            'rec_lstup' => date('Y-m-d H:i:s'),
                            'rec_lstup_id' => 1,
                        ]);
                }

            } else if ($template_tag == 'confirm_email_notification') {
                //update booking to say confirmation notification  has been sent
                DB::table('sis_sched_booking_detail')
                    ->where('id', $notification_to_be_sent->sched_detail_id)
                    ->update([
                        'db250781' => 'yes',
                        'rec_lstup' => date('Y-m-d H:i:s'),
                        'rec_lstup_id' => 1,
                    ]);
            } elseif ($template_tag == 'waitinglist_confirm_email_notification') {
                //update booking to say confirmation notification  has been sent
                DB::table('sis_sched_booking_detail')
                    ->where('id', $notification_to_be_sent->sched_detail_id)
                    ->update([
                        'db261929' => 'yes',
                        'rec_lstup' => date('Y-m-d H:i:s'),
                        'rec_lstup_id' => 1,
                    ]);
            } elseif ($template_tag == 'sms_course_reminder_no_sessions') {
                //update booking to say confirmation notification  has been sent
                DB::table('sis_sched_booking_detail')
                    ->where('id', $notification_to_be_sent->sched_detail_id)
                    ->update([
                        'db262322' => 'yes',
                        'rec_lstup' => date('Y-m-d H:i:s'),
                        'rec_lstup_id' => 1,
                    ]);
            } else if ($template_tag == 'sms_course_reminder_with_sessions') {
                DB::table('sis_session_bookings')
                    ->where('id', $notification_to_be_sent->id)
                    ->update([
                        'db262640' => 'yes',
                        'rec_lstup' => date('Y-m-d H:i:s'),
                        'rec_lstup_id' => 1,
                    ]);
            }

        }


        return 'Processed';
    }

    //get the preferred method of communication for an eoi
    protected function get_method_of_communication($student_id)
    {

        if (!$student_id || $student_id == '') {
            return 'Email';
        }

        // coms_preference
        $shortcoursesModel = new \ShortcoursesModel();
        return $shortcoursesModel->comms_preference($student_id);
    }

    protected function is_between_times($start = null, $end = null): bool
    {
        if ($start == null) $start = '0000';
        if ($end == null) $end = '2359';
        return ($start <= date('Hi') && date('Hi') <= $end);
    }


}
