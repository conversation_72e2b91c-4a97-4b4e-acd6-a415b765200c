<?php

namespace App\console\commands;

use App\core\console\Command;
use App\core\support\facades\DB;
use App\jobs\SendEmail;

class ProcessEmailBank extends Command
{

    protected $signature = 'emails:send {--id= : The id of the form email log.}
                                                  {--usergroup= : The usergroup to run for.}
                                                  {--bypass_environment_check= : Whether to bypass the environment check.}
                                                  {--limit=1000 : The limit of the rows.}
                                                  {--debug= : Whether to run in debug mode.}';
    protected $description = 'Sends emails saved in form_email_log';


    public function handle()
    {
        $this->info('Processing emails in form_email_log');
        $id = $this->option('id');
        $usergroup = $this->option('usergroup');
        $bypassEnvironmentCheck = $this->option('bypass_environment_check');
        $limit = $this->option('limit');
        $debug = $this->option('debug') ? true : false;
        //execute only on live
        if (env('APP_URL') == 'heiapply.com' || env('APP_URL') == 'hub-apply.com' || $bypassEnvironmentCheck) {
            $query = DB::table('form_email_log')
                ->select('id');
            if ($id) {
                $query->where('id', $id);
            } else {
                $query->where('db1161', 'no')
                    ->where('date', '<', date('Y-m-d H:i:s'))
                    ->where('db1153', 'not in', DB::table('coms_mail_unsubscriptions')->where('db64115', 'on')->where(function ($query) {
                        $query->where('rec_archive', '')
                            ->whereNull('rec_archive');
                    })->pluck('db64116')->toArray());

                if ($usergroup) {
                    $query->where('usergroup', $usergroup);
                }
                if ($limit) {
                    $query->limit($limit);
                }
            }
            $results = $query->get();
            foreach ($results as $result) {
                $this->info('Sending email ' . $result->id);
                SendEmail::dispatch($result->id, $debug);
            }
        } else {
            $this->error('Emails can only be processed on live');
        }
        $this->info('Emails sent in form_email_log');
    }

}