<?php /** @noinspection ALL */

use App\core\support\facades\Log;
use App\jobs\SendEmail;

if( !class_exists( 'Db_helper' ) ) load_helper('db');

	/**
	 * Helper class for sending emails using form_email_log
	 */
	class Email_helper
	{
		private const form_email_log = [ 'id'=>'id', 'rel_id'=>'rel_id', 'rec_id'=>'rec_id', 'usergroup'=>'usergroup', 'date'=>'date', 'to'=>'db1153', 'from'=>'db1154',
			'subject'=>'db1149', 'plain'=>'db1150', 'html'=>'db1151', 'category'=>'db1152', 'cc'=>'db58550', 'reply_to'=>'db1157', 'template_id'=>'db1158',
			'status'=>'db1155', 'processed'=>'db1161', 'user_level'=>'db1159', 'status_message'=>'db1160' ];

		private const lead_preferences = [ 'header_image' => 'db101414', 'footer_image'=>'db101417', 'signature'=>'db101420', 'from_name'=>'db101423', 'reply_to'=>'db101426',
			'append_login'=>'db50673', 'bg_color'=>'db50675', 'login_preference'=> 'db210182', 'allow_login' => 'db66413'];

		private const coms_template = [ 'reply_to'=>'db1090' ];

		private const form_schools = [ 'from'=>'db1117', 'reply_to'=>'db1118' ];

		private const form_users = [ 'id'=>'id', 'user_level'=>'db112', 'email'=>'db119' ];

		private const core_students = [ 'id', 'email'=>'db764' ];

		private const lead_profiles = [ 'id', 'email'=>'db1058' ];

		private const mail_unsubscriptions=[ 'id'=>'id', 'usergroup'=>'usergroup', 'date'=>'date', 'email'=>'db64116', 'checked'=>'db64115','exempt_categories'=>'db171581','exempt_categories_subjects'=>'db171584'];

		private const unsubscriptions_table = "coms_mail_unsubscriptions";
		public const APPLICANT = 4;
		public const LEAD_PROFILE = 1;
		public const ENQUIRER = 1;
		public const TUTOR = 20;
		public const PARENT = 7;
		public const PARTNER = 17;
		public const OTHER = 0;
		public const PRIORITY_URGENT   = "urgent";
		public const PRIORITY_FUTURE   = "future";
		public const PRIORITY_SCHEDULE = "schedule";

		/**
		 * @var String
		 */
		private $to;
		/**
		 * @var String
		 */
		private $from;
		/**
		 * @var String
		 */
		private $subject;
		/**
		 * @var String
		 */
		private $plain_message;
		/**
		 * @var String
		 */
		private $html_message;
		/**
		 * @var String
		 */
		private $category;
		/**
		 * @var Integer
		 */
		private $rel_id;
		/**
		 * @var Integer
		 */
		private $user_level;
		/**
		 * @var Array
		 */
		private $cc = [];
		/**
		 * @var Integer
		 */
		private $usergroup;
		/**
		 * @var \Db_helper
		 */
		private $db;
		/**
		 * @var String
		 */
		private $error;
		/*
		 * @var Array
		 */
		private $errors;
		/**
		 * @var Integer
		 */
		private $template_id;
		/**
		 * @var String
		 */
		private $reply_to;
		/**
		 * @var Integer
		 */
		private $user_id;
		/**
		 * @var String
		 */
		private $login_url;

		/**
		 * @var bool
		 */
		private $sendLater;

		/**
		 * @var DateTime
		 */
		private $date;
		/*
		 * @var bool
		 */
		private $interactive;
		/*
		 * @var Integer
		 */
		private $email_id;

		/*
		 * @var String
		 */
		private $priority = self::PRIORITY_SCHEDULE;
		/**
		 * ###Creates a new Email_helper object
		 *
		 * The instance is not interactive by default which means that if an error is thrown, an email alert will be sent to the sender.
		 * @var bool $interactive interactive is false by default.
		 */
		function __construct($interactive=false)
		{
			$this->db = new Db_helper();
			$this->reset();
			$this->interactive = $interactive;
		}

		/**
		 * @return mixed
		 */
		public function getEmailId()
		{
			return $this->email_id;
		}

		/**
		 * @param mixed $email_id
		 * @return Email_helper
		 */
		public function setEmailId($email_id)
		{
			$this->email_id = $email_id;
			return $this;
		}



		/**
		 * Checks whether the email sending instance is interactive or is a background process
		 *
		 * @return bool
		 */
		public function isInteractive()
		{
			return $this->interactive;
		}

		/**
		 * ###Enables or disables interactive mode.
		 *
		 * If interactive, the email helper will return error messages when an error occurs, otherwise it will trigger an email alert with the details of the error.
		 *
		 * @param bool $interactive
		 * @return Email_helper
		 */
		public function setInteractive($interactive): Email_helper
		{
			$this->interactive = $interactive;
			return $this;
		}



		/**
		 * Resets email parameters
		 *
		 * @return $this
		 */
		public function reset(): Email_helper
		{
			$this->to = $this->from = $this->subject = $this->plain_message = $this->html_message = $this->category = $this->rel_id = $this->template_id = $this->reply_to = $this->login_url = $this->user_level = $this->date = $this->sendLater = null;
			$this->cc = [];
			$this->errors = [];
			$this->usergroup = $_SESSION['usergroup'] ?? 1;
			$this->user_id = $_SESSION['uid']??null;
			$this->category = 'alert';
			return $this;
		}

		/**
		 * ###Schedule your email(s) to be sent later.
		 *
		 * Email will be scheduled for sending in 15 minutes if no DateTime object is provided.
		 *
		 * @param DateTime $date Optional DateTime object of when you want the email to be sent.
		 * @return Email_helper
		 */
		public function sendLater($date=null): Email_helper
		{
			if($date){

				if($date instanceof DateTime) $this->date = $date;
				else throw new Exception("You need to provide a valid DateTime object to schedule an email.");
			}
			else{
				$this->date = new DateTime();
				$minutes = new DateInterval('PT15M'); // send in 15 minutes by default.
				$this->date->add($minutes);
			}
			$this->sendLater = true;
			return $this;
		}

		/**
		 * Sets the email address to send to
		 *
		 * @param String $to Email address of recipient
		 * @return Email_helper
		 */
		public function to($to): Email_helper
		{
			$this->to = $to?:null;
			return $this;
		}

		/**
		 * Sets the email address to send from
		 *
		 * @param String $from
		 * @return Email_helper
		 */
		public function from($from): Email_helper
		{
			$this->from = $from?:null;
			return $this;
		}

		/**
		 * Sets the subject of the email message
		 *
		 * @param String $subject
		 * @return Email_helper
		 */
		public function subject($subject): Email_helper
		{
			$this->subject = $subject;
			return $this;
		}

		/**
		 * Sets the plain text version of the message
		 *
		 * @param String $plain_message Plain text message
		 * @return Email_helper
		 */
		public function plain($plain_message): Email_helper
		{
			$this->plain_message = $plain_message;
			return $this;
		}

		/**
		 * Sets the HTML formatted message
		 *
		 * @param String $html_message The HTML message
		 * @return Email_helper
		 */
		public function html($html_message): Email_helper
		{
			$this->html_message = $html_message;
			return $this;
		}

		/**
		 * Sets the email category
		 *
		 * @param String $category
		 * @return Email_helper
		 */
		public function category($category): Email_helper
		{
			$this->category = $category;
			return $this;
		}

		/**
		 * Sets the ID of the relative subject of the email. This is usually either the Applicant ID or Enquirer ID.
		 *
		 * @param int $rel_id
		 * @return Email_helper
		 */
		public function setRelId($rel_id): Email_helper
		{
			$this->rel_id = $rel_id?:null;
			return $this;
		}

		/**
		 * Sets the Email priority .
		 *
		 * @param String $rel_id
		 * @return Email_helper
		 */
		public function setPriority($priority): Email_helper
		{
			$this->priority = $priority?:self::PRIORITY_FUTURE;
			return $this;
		}

		/**
		 * Sets the Level ID of the relative subject of the email. This is used to distinguish Applicant ID from Enquirer ID etc.
		 *
		 * @param int $user_level
		 * @return Email_helper
		 */
		public function setUserLevel(int $user_level): Email_helper
		{
			$this->user_level = $user_level?:null;
			return $this;
		}

		/**
		 * Sets the ID of the user who is sending the email
		 *
		 * @param int $user_id
		 * @return Email_helper
		 */
		public function setUserId($user_id): Email_helper
		{
			$this->user_id = $user_id?:null;
			return $this;
		}

		/**
		 * Sets a custom login URL to append to the email message
		 *
		 * @param int $user_id
		 * @return Email_helper
		 */
		public function setLoginUrl($url): Email_helper
		{
			$this->login_url = $url?:null;
			return $this;
		}

		/**
		 * An array or comma separated list of email addresses to carbon copy
		 *
		 * @param String|array $cc
		 * @return Email_helper
		 */
		public function cc($cc): Email_helper
		{
			if( is_array($cc) ) $this->cc = array_merge( $this->cc, $cc );
			elseif(!empty($cc)){
				$cc = explode(',',$cc);
				foreach($cc as $k=>$v){
					$e = trim($v);
					if($e) $cc[$k] = $e;
				}
				$this->cc = array_merge( $this->cc, $cc );
			}
			return $this;
		}

		/**
		 * Sets the Usergroup ID to send from
		 *
		 * @param int $usergroup
		 * @return Email_helper
		 */
		public function usergroup($usergroup): Email_helper
		{
			$this->usergroup = $usergroup;
			return $this;
		}

		/**
		 * Sets the ID of the template that was used to generate this email
		 *
		 * @param int $template_id
		 * @return Email_helper
		 */
		public function template($template_id): Email_helper
		{
			$this->template_id = $template_id?:null;
			return $this;
		}

		/**
		 * Sets the emails address to reply to
		 *
		 * @param String $reply_to Email address to reply to
		 * @return \Email_helper
		 */
		public function replyTo($reply_to): Email_helper
		{
			$this->reply_to = $reply_to?:null;
			return $this;
		}

		/**
		 * Gets the last error message
		 *
		 * @return String
		 */
		public function getError(): string
		{
			return $this->error??'';
		}

		/**
		 * @param String $error
		 * @return void
		 */
		private function setError($error): void
		{
			$this->error = $error;
		}

		/**
		 * Gets the email priority
		 * @return String
		 */
		public function getPriority(): string
		{
			return $this->priority;
		}


		/**
		 * ###Sends an email message
		 *
		 * Recommended that you use the email builder functions to set the various parameters for sending an email.
		 *
		 * @param array $args An array with optional parameters like 'to', 'reply_to', 'subject', 'text'/'plain', 'html' etc
		 * @param bool $reset Whether to reset the email parameters after sending. Useful not to reset if you want to resend the same email with minor changes
		 *                    e.g. to a different recipient.
		 * @return booll|string
		 */
		public function send($args=[], $reset=true)//: bool|string
		{
			$params = [ 'status'=>'logged', 'processed'=>$args['processed']??'no' ];
			$sent = true;
			try {

				$params['template_id'] = $args['template_id']??$this->template_id??null;
				$reply_to = $args['reply_to'] ?? $this->reply_to ?? false;
				$unsubscribe_link=$args['unsubscribe_link']??$this->unsubscribe_link??null;
				$reply_to = $reply_to?:$this->getTemplateReplyTo($params['template_id']);
                $params['usergroup'] = $args['usergroup'] ?? $this->usergroup ?? $_SESSION['usergroup'] ?? 1;
                $school = $this->db->get_row(['subdomain' => 'db985', 'org_type' => 'db30'], "form_schools", ['id' => $this->usergroup]);
                $org_types = empty($school['org_type']) ? [] : explode(',', $school['org_type']);
                if($reply_to) {
					$reply_to = $this->trimSafe($reply_to);
					if(!filter_var( $reply_to, FILTER_VALIDATE_EMAIL)) $this->throw_error("The reply to email, '{$reply_to}' is not a valid email address. ");
					$params['reply_to'] = $reply_to;
				}
				$to = $args['to'] ?? $this->to;
				if(empty($to)) {
                    if (in_array(12, $org_types)){
                        if (empty($args['rel_id']) && empty($args['recipient_id'])){
                            $this->throw_error("The recipient id is required. ");
                        }else{
                            $hasAnEmail = '';
                            if (!empty($args['recipient_id'])){
                                $hasAnEmail = pull_field('sis_profiles', 'db211247', "WHERE rel_id='{$args['recipient_id']}'");
                            }
                            if (!empty($args['rel_id'])){
                                $hasAnEmail = pull_field('sis_profiles', 'db211247', "WHERE rel_id='{$args['rel_id']}'");
                            }

                            if ($hasAnEmail == 'no'){
                                return false;
                            }else{
                                $this->throw_error("The recipient email is required. ");
                            }
                        }


                    }else{
                        $this->throw_error("The recipient email is required. ");

                    }
                }
				$to = $this->trimSafe($to);
				if( !filter_var( $to, FILTER_VALIDATE_EMAIL ) ){
					$this->throw_error("'{$to}' is not a valid email address. ");
				}
				$params['to'] = $to;
				$from = $args['from'] ?? $this->from ?? $this->getSchoolFromEmail();
				if(empty($from)) $this->throw_error('Email to send from is not defined for this organisation. ');
				$from = $this->trimSafe($from);
				if(!filter_var( $from, FILTER_VALIDATE_EMAIL)) $this->throw_error("The from email, '{$from}' is not a valid email address. ");
				$params['from'] = $from;
				$subject = $args['subject'] ?? $this->subject ?? $this->getTemplateSubject($params['template_id']);
				if(empty($subject))  $this->throw_error('Email subject is required. ');
				$params['subject'] = $this->sanitise($subject);
				$rel_id = $args['recipient_id'] ?? $args['rel_id'] ?? $this->rel_id ?? $this->findRelId( $to, $reply_to );
				if($rel_id) $params['rel_id'] = $rel_id;
				 $user_level = $args['user_level'] ?? $this->user_level ?? 0;
				 // if rel_is was provided but no level, the findRelId method will try guessing the user level for us
				 if(!$user_level && $rel_id ) $this->findRelId($to, $reply_to);
				 $params['user_level'] = $user_level ?: ($this->user_level ?? 0);

				$plain = $args['plain'] ?? $args['text'] ?? $this->plain_message ?? null;
				$html = $args['html'] ?? $this->html_message ?? (!empty($plain) ? nl2br($plain) : '');
				$plain = $plain ?: strip_tags($html);
				$params['plain'] = strip_tags($this->sanitise($plain));
				if( $params['user_level'] == self::APPLICANT && !empty($params['rel_id']) ) {
					$html = $this->add_login_link($html, $params['rel_id'], $params['user_level']);
				}
				if(in_array(11, $org_types)) $domain = "https://{$school['subdomain']}.hub-apply.com";
				else $domain = "https://{$school['subdomain']}.heiapply.com";
				if (!empty($unsubscribe_link) && !in_array(12, $org_types)) {
					$html = $this->add_unsubscribe_link($html, $params['to'], $domain);
				}
				$params['html'] = $this->sanitise($html);
				if(empty($params['plain']) && empty($params['html']))  $this->throw_error('Message body is required. ');

				$params['category'] = $this->sanitise($args['category']??$this->category??'');

				$queue = $args['queue'] ?? $this->sendLater;
				if($queue){
                    $this->sendLater = true;
					if(!$this->date){
						$this->date = new DateTime();
						$minutes = new DateInterval('PT15M'); // send in 15 minutes by default.
						$this->date->add($minutes);
					}
					$params['date'] = $this->date->format('Y-m-d H:i:s');
				}

				if($this->user_id) $params['rec_id'] = $this->user_id;

				$cc = !empty($args['cc']) ? $args['cc'] : ( !empty($args['ccemails']) ? $args['ccemails'] : [] );
				//if cc is empty check if we have a cc record or not this is for applicants
				if(empty($cc) && !empty($params['rel_id']) && $params['user_level'] == 4){
					$cc = pull_field('form_communication_option', 'db57291', "WHERE rel_id='{$params['rel_id']}'");
				}
                if(!empty($cc)){
					if(is_string($cc)) $cc = explode(',',$cc);
					foreach($cc as $k=>$v) $cc[$k] = trimSafe($v);
					$this->cc = array_merge($this->cc, $cc);
				}
				if( !empty($this->cc) ){
					$cc = array_unique($this->cc);
					foreach($cc as $email){
						$em = $this->trimSafe($email);
						if( !filter_var( $em, FILTER_VALIDATE_EMAIL ) ) $this->throw_error("{$em} in CC is not a valid email address. ");
					}
					$params['cc'] = implode(',',$cc);
				}


                if(!empty($this->errors)){
					$params['status'] = 'rejected';
					$params['processed'] = 'yes';
					$params['status_message'] = implode( "\n", $this->errors );
				}
				$data = $this->db->prepare_data(self::form_email_log, $params);
                // skip inserting if // identical email has beeen triggered within the last 24 hours
				dev_debug('<pre>'.print_r($data,1).'</pre>');
				$queued = $this->db->insert('form_email_log', $data);
				$this->setEmailId($queued);
				$e = $this->db->getError();
				if($e) $this->setError($e);

				if( !empty($this->errors) ){
					$this->reportError( $args, $params, $queued );
					$this->setError(implode("\n",$this->errors));
					$sent = false;
				}
				else $sent = !!$queued;

				if(!empty($args['priority'])){
					$this->setPriority($args['priority']);
				}
                if (!$this->sendLater) {
                    SendEmail::dispatch($queued);
                }
			}
			catch(Exception $e){
				$this->setError($e->getMessage());
				if(!$this->isInteractive()){
					$this->reportError($args, $params);
				}
				$sent = false;
			}
			finally {
				if($this->getPriority() == self::PRIORITY_URGENT){
					if(file_exists(base_path."v2/console.php")){
                        $sent = shell_exec("php ".base_path."v2/console.php emails:send ". $this->getEmailId());
                    }
				}
				if($reset) $this->reset();
				return $sent;
			}
		}


		/**
		 * Throws an exception when in interactive mode, otherwise an error message is logged in emails table
		 *
		 * @param $msg
		 * @throws \Exception
		 */
		private function throw_error($msg){
			if($this->isInteractive()) throw new Exception($msg);
			$this->errors[] = $msg;
		}

		/**
		 * Sends a notification message to an admin if an email failed to send
		 *
		 * @param $args
		 * @param $params
		 */
		private function reportError($args, $params, $email_id=false){
			if(!empty($this->errors)){
				$reason = implode( "\n<br>", $this->errors );
				$template_id = $args['template_id']??$this->template_id??null;
				$to = $params['reply_to']??$args['reply_to']??$this->reply_to??'';
				if(empty($to)){
					$to = $this->getTemplateReplyTo($template_id);
					if(empty($to)) $to = '<EMAIL>';
				}

				$recipient = $args['to'] ?? $params['to'] ?? $this->to;
				$original_subject = $args['subject'] ?? $params['subject'] ?? $this->subject ?? $this->getTemplateSubject($template_id);

				$subject = "Unable to send email message to ".$recipient;
				$message = "Dear $to. \n\n<br><br>We could not process an email message you tried to send. The message details are given below: \n\n<br><br>";
				$message .= "Recipient: {$recipient}\n<br>";
				$message .= "Subject: {$original_subject}\n<br>";
				$message .= "Date: ".date('d M Y, H:i')."\n<br>";
				$message .= "Rejection reason: {$reason}";
				if($email_id){
					$subdomain = $this->db->fetch_field('db985', 'form_schools', ['id'=>$this->usergroup]);
					if($subdomain && !empty($_SERVER['SERVER_NAME']) ) {
						$scheme = !empty($_SERVER['HTTP_X_FORWARDED_PROTO']) ? $_SERVER['HTTP_X_FORWARDED_PROTO']
							: ( !empty($_SERVER['REQUEST_SCHEME']) ? $_SERVER['REQUEST_SCHEME'] : 'https' );
						$domain = str_replace( [$subdomain.'.', 'adminlite.'], '', $_SERVER['SERVER_NAME'] );
						$link = "{$scheme}://{$subdomain}.{$domain}/admin/system/email_logs?eid=$email_id";
						$message .= "\n\n<br><br>You can view the rejected message on <a href=\"$link\" target='_blank'>this link</a>: $link";
					}
				}
				$message .= "\n\n<br><br>Regards,\n<br>HeiApply Support";
				$message .= "\n\n<br><br><small>If you believe you received this message in error, please submit a support request so that we can investigate.</small>";
				$data = [ 'status'=>'logged', 'processed'=>'no',
					'to'=>$to,
					'subject'=>$subject,
					'from'=>'<EMAIL>',
					'plain'=>strip_tags($message),
					'html'=>$message,
					'category'=>'rejected-email-alert'
				];
				$insert = $this->db->prepare_data(self::form_email_log, $data);
				$this->db->insert('form_email_log', $insert);
				$e = $this->db->getError();
				if($e) dev_debug($e);
			}
		}

		private function findRelId($to, $from){
			if( !empty($_SESSION['student_id']) ){
				if(!$this->user_level) $this->setUserLevel(self::APPLICANT);
				return $_SESSION['student_id'];
			}
			if( !empty($_SESSION['application_id']) ){
				if(!$this->user_level) $this->setUserLevel(self::APPLICANT);
				return $_SESSION['application_id'];
			}

			// In practice, we do not expect to ever get here because the rel_id should either be in the session or provided to the email function.
			// check if recipient email is for an applicant or a lead profile
			$rel_id = $to ? $this->getApplicationFromEmail($to) : false;
			if($rel_id) return $rel_id;

			// check if sender email (use reply-to email) is an applicant or lead profile
			$rel_id = $from ? $this->getApplicationFromEmail($from) : false;
			if($rel_id) return $rel_id;

			$rel_id = $to ? $this->getUserFromEmail($to) : false;
			if($rel_id) return $rel_id;

			$rel_id = $from ? $this->getUserFromEmail($from) : false;
			if($rel_id) return $rel_id;

			// when  we fail to find an appropriate rel_id
			$this->setUserLevel(self::OTHER);
			return null;
		}

		private function getApplicationFromEmail($email){
			// first check directly in core_students
			$this->db->where("(rec_archive IS NULL OR rec_archive = '')");  // ensure we don't take some deleted profile
			$this->db->where("(db41 not in ('-1', '-2'))");  // ensure we don't take some archived profile
			//the above are not enough and are bound to cause issues if get yourself here, check if you email is providing the correct student id
			$id = $this->db->fetch_field( 'id', 'core_students', [ self::core_students['email']=>$email, 'usergroup'=>$this->usergroup ] );
			if($id){
				if(!$this->user_level) $this->setUserLevel(self::APPLICANT);
				return $id;
			}

			$user = $this->db->get_row( self::form_users, 'form_users', [ self::form_users['email']=>$email, 'usergroup'=>$this->usergroup ] );
			if($user && in_array( $user['user_level'], [4, 7] ) ){    // user_level 4 is an applicant, 7 is parent
				// find their application. We will just take the latest one because there's no way of telling this message belongs to which application
				$this->db->order_by('id desc');
				$this->db->where("(rec_archive IS NULL OR rec_archive = '')");  // ensure we don't take some deleted profile
				$rel_id = $this->db->fetch_field('id', 'core_students', ['rec_id'=>$user['id'], 'usergroup'=>$this->usergroup]);
				if($rel_id){
					if(!$this->user_level) $this->setUserLevel(self::APPLICANT);
					return $rel_id;
				}
			}

			// check lead_profiles
			$this->db->where("(rec_archive IS NULL OR rec_archive = '')");  // ensure we don't take some deleted profile
			$id = $this->db->fetch_field( 'id', 'lead_profiles', [ self::lead_profiles['email']=>$email, 'usergroup'=>$this->usergroup ] );
			if($id){
				if(!$this->user_level) $this->setUserLevel(self::ENQUIRER);
				return $id;
			}

			return false;
		}

		private function getUserFromEmail($email){
			$user = $this->db->get_row( self::form_users, 'form_users', [ self::form_users['email']=>$email, 'usergroup'=>$this->usergroup ] );
			if($user){
				if(!$this->user_level) $this->setUserLevel($user['user_level']);
				return $user['id'];
			}
			return false;
		}

		private function getTemplateSubject($template_id){
			if(!empty($template_id) && is_numeric($template_id) && $template_id > 0){
				return $this->db->fetch_field('db1086', 'coms_template', ['id'=>$template_id]);
			}
			return '';
		}

		private function getTemplateReplyTo($template_id){
			if(!empty($template_id) && is_numeric($template_id) && $template_id > 0){
				$reply_to = $this->db->fetch_field('db1090', 'coms_template', ['id'=>$template_id]);
				return $reply_to ?: $this->getPreferencesReplyTo();
			}
			return $this->getPreferencesReplyTo();
		}

		private function getPreferencesReplyTo(){
			$reply_to = $this->db->fetch_field( self::lead_preferences['reply_to'], 'lead_preferences', ['usergroup'=>$this->usergroup]);
			return $reply_to ?: $this->getSchoolReplyTo();
		}

		private function getSchoolReplyTo(){
			return $this->db->fetch_field( self::form_schools['reply_to'], 'form_schools', ['id'=>$this->usergroup]);
		}

		private function getSchoolFromEmail(){
			return $this->db->fetch_field( self::form_schools['from'], 'form_schools', ['id'=>$this->usergroup]);
		}


		/**
		 * Appends an HTML login button link to the email body
		 *
		 * @param string $body    The HTML email body
		 * @param int $rel_id     Applicant ID
		 * @param int $user_level Expects a user level of 4. Anything else, the link won't be appended
		 * @return string
		 */
		private function add_login_link($body, $rel_id, $user_level): string
		{
			try {
				if(empty($this->usergroup)) throw new Exception("Usergroup is required");
				if($user_level != self::APPLICANT) throw new Exception("Recipient is not an applicant");
				if(empty($rel_id)) throw new Exception("Student ID is required");

				$preferences = $this->db->get_row(self::lead_preferences, 'lead_preferences', ['usergroup' => $this->usergroup]);
				$append_login = $preferences['append_login'] ?? 'no';
				if($append_login === 'no') throw new Exception("Login link not required");

				$market = $this->db->fetch_field('db510', 'core_students', ['id' => $rel_id, 'usergroup' => $this->usergroup]);

				$lang = empty($market) ? '' : $this->db->fetch_field('db21281', "form_languages", ['db21280' => $market]);
				if(empty($lang)){
					dev_debug("Language for market \"{$market}\" not found, defaulting to English");
					$lang = 1; // default to English if language not found.
				}

				$school = $this->db->get_row(['subdomain' => 'db985', 'org_type' => 'db30'], "form_schools", ['id' => $this->usergroup]);
				if(empty($school['subdomain'])) throw new Exception("School subdomain not found");

				$org_types = empty($school['org_type']) ? [] : explode(',', $school['org_type']);

				//check if the school is a hub school or not
				if(in_array(11, $org_types)) $domain = "https://{$school['subdomain']}.hub-apply.com";
				else $domain = "https://{$school['subdomain']}.heiapply.com";

				if (in_array(12, $org_types)){

					if ($preferences['allow_login'] == 'yes'){
						$login_preference =$preferences['login_preference'] ?? 'student';
						$where =  [
							'LEFT JOIN lead_mrn_stages' => "db48581 = db49850 ",
							'sis_profiles.rel_id' => $rel_id,
							'sis_profiles.usergroup' => $this->usergroup,
						];
						$profile_status =  $this->db->fetch_field('db49851', 'sis_profiles', $where);
						if ($profile_status != 'students'){
							$login_preference.= 's';
							if ($login_preference != $profile_status){
								throw new Exception("Can not add login link user not allowed to login");
							}
						}
					}else{
						throw new Exception("Can not add login link user not allowed to login");
					}
				}

				$path = '';
				if($this->login_url) {
					$first_character = substr($this->login_url, 0, 1);
					if($first_character == "/") {
						$path = ltrim($this->login_url, '/');
					}
				}
				else $path = "application";

				$login_url = "$domain/$path";
				$login_text = translate("Login to your account", $lang);
				$pref_bg_color = empty($preferences['bg_color']) ? '#01b3e3' : $preferences['bg_color'];
				$login = '<table><tr style="background-color:#ffffff"><td style="text-align:left;max-width:600px"><table align="center" style="background-color:#ffffff;text-align:center;max-width:600px"><tbody><tr><td align="center" valign="top" style="padding-top:20px;padding-bottom:40px;margin:0 auto;background-color:#ffffff"><table width="240" border="0" cellspacing="0" cellpadding="0" style="background-color:' . $pref_bg_color . ';border-radius:4px;border-collapse:separate!important"><tbody><tr><td class="m_3513362776129727005hover" width="240" height="46" align="center" valign="middle" style="font-size:16px;font-family:\'roboto\', sans-serif, arial;font-weight:300"><a class="m_3513362776129727005hover" href="' . $login_url . '" title="' . $login_text . '" style="text-decoration:none;color:#ffffff;display:block;border-radius:4px;font-family:\'roboto\', sans-serif, arial;padding-top:10px;border:2px solid ' . $pref_bg_color . ';padding-bottom:10px;font-weight:bold;" target="_blank">' . $login_text . '</a></td></tr></tbody></table></td></tr></tbody></table></td></tr></table>';
				return $body.$login;
			}
			catch(Exception $e){
				dev_debug($e->getMessage());
				return $body;
			}
		}

		/**
		 * Trims, strips slashes and wraps text
		 *
		 * @param string $value
		 * @param int $wordwrap
		 * @return string
		 */
		public function sanitise($value, $wordwrap=70): string
		{
			$value = trim( $value );
			$value = stripslashes( $value );
			//$value = str_replace ('\'','&apos;',$value);
			if($wordwrap) $value = wordwrap($value, $wordwrap, "\r\n");
			return $value;
		}

		/**
		 * Trims text, including removing html encoded spaces
		 *
		 * @param $value
		 * @return string
		 */
		public function trim($value): string
		{
			$string = htmlentities($value, null, 'utf-8');
            $content = str_replace("&nbsp;", "", $string);
			$content = html_entity_decode($content);
			return trim($content);
		}
        public function trimSafe($value): string
        {
            if (!is_string($value)) {
                return '';
            }

            // Convert to UTF-8 safely
            $value = mb_convert_encoding($value, 'UTF-8', 'UTF-8');

            try {
                $string = htmlentities($value, ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8');
                $content = str_replace("&nbsp;", "", $string);
                $content = html_entity_decode($content, ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8');
                return trim($content);
            } catch (\Throwable $e) {
                // Log or handle error to avoid crashing your job
                Log::error("Trim failure: " . $e->getMessage());
                return trim($value); // fallback
            }
        }

	      /**
		 * Appends an HTML unsubscribe link to the email body
		 *
		 * @param string $body    The HTML email body
		 * @param int $rel_id     Applicant ID
		 * @param int $user_level Expects a user level of 4. Anything else, the link won't be appended
		 * @return string
		 */
		private function add_unsubscribe_link($body, $email, $domain): string
		{
			try {
				
				$login = '</br></br></br><table><tr style="background-color:#ffffff"><td style="text-align:center;max-width:600px"><p>
				<a href="'.$domain.'/unsubscribe.php?trigger='.encode($email,'unsalted').'" target="_blank">Unsubscribe from emails</a></p></td></tr></table>';
				return $body.$login;
			}
			catch(Exception $e){
				dev_debug($e->getMessage());
				return $body;
			}
		}

		public function unsubscribe_email($args){
			//if (!empty($args['exempt_categories'])&&is_array($args['exempt_categories'])) {
				$args['exempt_categories']=implode("&*(&",$args['exempt_categories']);
			//}
			//if (!empty($args['exempt_categories_subjects'])&&is_array($args['exempt_categories_subjects'])) {
				$args['exempt_categories_subjects']=implode("&*(&",$args['exempt_categories_subjects']);
			///}

			$existing=$this->db->
			where(self::mail_unsubscriptions['email'],$args['email'])->
			where(self::mail_unsubscriptions['usergroup'],$this->usergroup)->
			where("(rec_archive is null or rec_archive = '')")->
			get_rows(self::unsubscriptions_table);
			$data=$this->db->prepare_data(self::mail_unsubscriptions,$args);
			if (!empty($existing[0])) {
				$this->db->update(self::unsubscriptions_table,$data,['id'=>$existing[0]['id']]);
				$id = $existing[0]['id'];
			}else{
                $this->db->insert(self::unsubscriptions_table,$data);
				
				$insertRecord=$this->db->
				where(self::mail_unsubscriptions['email'],$args['email'])->
				where(self::mail_unsubscriptions['usergroup'],$this->usergroup)->
				where("(rec_archive is null or rec_archive = '')")->
				get_rows(self::unsubscriptions_table);
				
				$id = $insertRecord[0]['id'];
				
			}
			
			return $id;
		}

		public function determine_available_mail_categories($args){
			$tosql="";
			if (!empty($args['to'])) {
				$tosql= " AND ".self::form_email_log['to']."='".$args['to']."'" ;
			}
			$school_type = pull_field('form_schools', 'db30', "WHERE id ='{$_SESSION['usergroup']}'");
			if ($school_type != 12 && pull_field('lead_preferences', 'db266309', "where usergroup = {$_SESSION['usergroup']}") !== 'yes' ){
				$query = "
				select
				left(strings, length(strings) - length( reverse( concat(strings, '1') ) + 0 ) + 1 ) as tb_string ,usergroup,subject
				from
				(select
                SUBSTRING_INDEX(SUBSTRING_INDEX(".self::form_email_log['subject'].",'-',1),':',1) as subject,
				CASE
				when LOCATE('/', ".self::form_email_log['category'].") and LOCATE(':', ".self::form_email_log['category'].") and !LOCATE('Load Time!', ".self::form_email_log['category'].") then REPLACE(REPLACE(".self::form_email_log['category'].",'/',''),':','')
				when LOCATE('Alert', ".self::form_email_log['category'].") AND !LOCATE('_', ".self::form_email_log['category'].") AND !LOCATE('-', ".self::form_email_log['category'].") and ".self::form_email_log['category']."!='alert' then concat(SUBSTRING_INDEX(".self::form_email_log['category'].",'Alert',1),'Alert')
				when LOCATE('Load Time!', ".self::form_email_log['category'].") then concat(SUBSTRING_INDEX(".self::form_email_log['category'].",'Load Time!',1),'Load Time!')
				else SUBSTRING_INDEX(left( ".self::form_email_log['category'].", length(".self::form_email_log['category'].") - length( reverse( concat(".self::form_email_log['category'].", '1') ) + 0 ) + 1 ),'-',1)
				END as strings,id,usergroup from form_email_log  WHERE ".self::form_email_log['usergroup']."=".$this->usergroup." {$tosql} group by ".self::form_email_log['subject']." order by id desc) tb
				group by subject having tb_string  not in ('ticket_status_change','ticket_comment','rejected-email-alert','ticket_priority_change','bounce-email-alert','dropped-email-alert','Support Issue','auto_comms','error_messages','bounce','dropped','error_log','Slow Page Load Time!','rejected')
                order by id desc
				";
			}
			else{
				$exemptions = pull_field('lead_preferences', 'db251525', "where usergroup = {$_SESSION['usergroup']}");
				$exemptions =  ltrim($exemptions, ',');
				if(!empty($exemptions)){
					$exempt_ids = "and id not in ({$exemptions})";
				}
				$query = "
				SELECT db15461 as subject FROM coms_email_tags where find_in_set($school_type, db159284) and db251255 = 'yes' {$exempt_ids};
				";
			}
			return $this->db->query($query);
		}

		public function get_mail_subscriptions($args){
			if (!empty($args['id'])) {
				$this->db->where(self::mail_unsubscriptions['id'],$args['id']);
			}
			if (!empty($args['email'])) {
				$this->db->where(self::mail_unsubscriptions['email'],$args['email']);
			}
			$arr=$this->db->where(self::mail_unsubscriptions['usergroup'],$this->usergroup)
				->where("(" . self::unsubscriptions_table . ".rec_archive IS NULL OR " . self::unsubscriptions_table . ".rec_archive = '' )")
				->get_rows(self::mail_unsubscriptions,self::unsubscriptions_table);
			if (!empty($_GET['debug'])) {
			    echo "<pre>".print_r($arr,1)."</pre>";
			}
			if (!empty($arr)) {
				foreach ($arr as $key => $value) {
					$arr[$key]['exempt_categories']=explode('&*(&', $value['exempt_categories']);
					$arr[$key]['exempt_categories_subjects']=explode('&*(&', $value['exempt_categories_subjects']);
				}
			}

            if (!empty($args['id'])) {
			   return $arr[0];
		    }else{
		    	return $arr;
		    }
		}
	}