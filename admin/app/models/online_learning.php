<?php

/**
 * Online Learning
 */
require_once(ABSPATH . '/app/libs/shortcodes/vendor/autoload.php');

use Thunder\Shortcode\HandlerContainer\HandlerContainer;
use Thunder\Shortcode\Parser\RegularParser;
use Thunder\Shortcode\Processor\Processor;
use Thunder\Shortcode\Shortcode\ShortcodeInterface;


// Compatibility Polyfill ( PHP < 8.0)

if (!function_exists('str_ends_with')) {
    function str_ends_with($haystack, $needle) {
        return substr($haystack, -strlen($needle)) === $needle;
    }
}

class OnlineLearning
{

    /** ===================================
     * Admin Link
     * ====================================*/
    var $admin_course_page = "course_details.php";
    var $admin_courses_page = "courses.php";
    var $admin_unit_page = "unit.php";
    var $admin_quiz_page = "quiz.php";
    var $admin_quizes_page = "quizes.php";
    var $admin_course_user_settings_page = "course_users_settings.php";
    var $admin_modules_page = "modules.php";
    private $is_api_instance = false;
    public $counter = 0;
    public $media_library_folder='';


    function __construct($for_api = false)
    {
        global $domain, $school_info;
        if(!empty($school_info['href'])){
            $this->media_library_folder = $school_info['href'] . "/online-learning/media/" . $_SESSION['usergroup'] . '/';
        }

        if ($for_api) {
            $this->is_api_instance = true;
        }

    }

    /** ===================================
     * Home URL
     * ====================================    */
    function home_url($path = "")
    {
        if ($path) {
            $first_character = substr($path, 0, 1);
            if ($first_character == "/") {
                $path = ltrim($path, '/');
            }
        }

        $domain = $_SERVER['HTTP_HOST'];
        $http = (string)$domain == 'inourplace.heiapplylocal.co.uk' ? 'http' : 'https';
        return $http . '://' . $domain . '/online-learning/' . $path;
    }

    function dev_debug($sql)
    {
        global $debug_switch;
        if ($_SESSION['debug_mode'] == 'yes' || $_GET['debug_mode'] == 'yes') {
            echo "<div><pre class=\"alert_colour\" style=\"text-align:left;\"><strong>DEBUG MSG:</strong>$sql</pre></div>";
        }
    }

    /** ===================================
     * Get Memeber Info
     * ====================================    */
    function get_members($args = array())
    {
        $dbh = get_dbh();

        if ($args['id']) {
            $id_sql = "AND form_users.id='" . $args['id'] . "'";
        }

        if ($args['school_id']) {
            $school_id_sql = "AND core_students.usergroup='" . $args['school_id'] . "'";
        }

        $query = "
			SELECT 
				*
			FROM
				core_students 
				LEFT JOIN form_users ON core_students.db764 = form_users.db119
			WHERE
				1
				$id_sql
				$school_id_sql
			ORDER BY db39 ASC
			";


        $sth = $dbh->prepare($query);
        $sth->execute();

        $results_list = array();
        foreach ($row_result = $sth->fetchAll() as $row) {

            $get_last_worked_on_info = $this->get_last_worked_on_info(array('student_id' => $row['id']));

            if (!$row['db106']) {
                $row['db106'] = $row['db39'];
            }
            if (!$row['db111']) {
                $row['db111'] = $row['db40'];
            }

            $results_list[] = array(
                "id" => $row['id'],
                "first_name" => $row['db106'],
                "last_name" => $row['db111'],
                "email" => $row['db764'],
                "gender" => $row['db44'],
                'last_module_id' => $get_last_worked_on_info['module_id'],
                'last_course_id' => $get_last_worked_on_info['course_id'],
                'last_unit_id' => $get_last_worked_on_info['unit_id'],
            );
        }

        if ($args['id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Get Member Course IDs
     * ====================================    */
    function get_member_course_ids($student_id)
    {
        $dbh = get_dbh();

        if ($student_id) {
            $student_id_sql = "AND ols_user_access_plan.rel_id='" . $student_id . "'";
        }

        /*$query ="
			SELECT
				db21911
			FROM
				ols_user_access_plan
				LEFT JOIN ols_access_plans on ols_access_plans.id = ols_user_access_plan.db22019
				LEFT JOIN ols_access_plan_courses on ols_access_plan_courses.rel_id = CAST(ols_access_plans.id AS CHAR)
			WHERE 1
				$student_id_sql
				AND ols_user_access_plan.db22031 = '2'
				AND (ols_access_plan_courses.rec_archive IS NULL OR ols_access_plan_courses.rec_archive = '')
			GROUP BY db21911
			";*/

        $query = "
			SELECT
				db21911 
			FROM 
				ols_user_access_plan 
				JOIN ols_access_plan_courses on ols_access_plan_courses.rel_id = ols_user_access_plan.db22019
			WHERE 1
				$student_id_sql
				AND ols_user_access_plan.db22031 = '2'
				AND (ols_access_plan_courses.rec_archive IS NULL OR ols_access_plan_courses.rec_archive = '')  
			GROUP BY db21911
			";
        dev_debug($query);


        $sth = $dbh->prepare($query);
        $sth->execute();

        $results_list = "";

        foreach ($row_result = $sth->fetchAll() as $row) {
            if ($row['db21911']) {
                $results_list .= $row['db21911'] . ",";
            }
        }
        //also need to get all courses no longer in the student access plan list but the student has started
        $mysql = "SELECT DISTINCT(db37395) FROM ols_user_progress WHERE ols_user_progress.rel_id= $student_id AND (rec_archive IS NULL or rec_archive = '')";
        dev_debug($mysql);
        $sth = $dbh->prepare($mysql);
        $sth->execute();
        $row_results = $sth->fetchAll();
        if (!empty($row_results)) {
            foreach ($row_results as $row) {
            if (!empty($row['db37395'])) {
                $haystack = ',' . $results_list;
                $needle = ',' . $row['db37395'] . ",";
                if (strpos($haystack, $needle) === false) {
                    //check if this user has transitioned from this course
                    $has_transitioned = pull_field("ols_user_course_transitions", "id", "WHERE rel_id = '$student_id' AND db260402 = '{$row['db37395']}'");

                    //user has not yet transitioned
                    if (empty($has_transitioned)) {
                        $results_list .= $row['db37395'] . ",";
                    }
                }
            }
           }

        }

        dev_debug('Getting to get_member_course_ids :'.$results_list);
        $results_list = rtrim($results_list, ",");
        dev_debug('Getting to get_member_course_ids2 :'.$results_list);
        return $results_list;

    }

    /** ===================================
     * Get Recent Course ID
     * ====================================    */
    function get_recent_course_id($student_id)
    {
        dev_debug('Getting to get_recent_course_id');
        $dbh = get_dbh();
        $student_id_sql="";
        if ($student_id) {
            $student_id_sql = "AND ols_user_access_plan.rel_id='" . $student_id . "'";
        }
        dev_debug('Getting to get_recent_course_id2');

        /*$query ="
			SELECT
				db21911
			FROM
				ols_user_access_plan
				LEFT JOIN ols_access_plans on ols_access_plans.id = ols_user_access_plan.db22019
				LEFT JOIN ols_access_plan_courses on ols_access_plan_courses.rel_id = CAST(ols_access_plans.id AS CHAR)
			WHERE 1
				$student_id_sql
				AND ols_user_access_plan.db22031 = '2'
				AND (ols_access_plan_courses.rec_archive IS NULL OR ols_access_plan_courses.rec_archive = '')
			GROUP BY db21911
			";*/

        $query = "
			SELECT
				db21911 
			FROM 
				ols_user_access_plan 
				JOIN ols_access_plan_courses on ols_access_plan_courses.rel_id = ols_user_access_plan.db22019
			WHERE 1
				$student_id_sql
				AND ols_user_access_plan.db22031 = '2'
				AND (ols_access_plan_courses.rec_archive IS NULL OR ols_access_plan_courses.rec_archive = '')  
			GROUP BY db21911
			";
        dev_debug($query);


        $sth = $dbh->prepare($query);
        $sth->execute();

        $results_list = "";

        foreach ($row_result = $sth->fetchAll() as $row) {
            if ($row['db21911']) {
                $results_list .= $row['db21911'] . ",";
            }
        }
        //also need to get all courses no longer in the student access plan list but the student has started
        $mysql = "SELECT DISTINCT(db37395) FROM ols_user_progress WHERE ols_user_progress.rel_id= $student_id AND (rec_archive IS NULL or rec_archive = '') ORDER BY ID DESC LIMIT 1";
        dev_debug($mysql);
        $sth = $dbh->prepare($mysql);
        $sth->execute();
        $row = $sth->fetch();
        if (!empty($row['db37395'])) {
            $haystack = ',' . $results_list;
            $needle = ',' . $row['db37395'] . ",";
            if (strpos($haystack, $needle) === false) {
                //check if this user has transitioned from this course
                $has_transitioned = pull_field("ols_user_course_transitions", "id", "WHERE rel_id = '$student_id' AND db260402 = '{$row['db37395']}'");

                //user has not yet transitioned
                if (empty($has_transitioned)) {
                    $results_list .= $row['db37395'] . ",";
                }
            }
        }


        $results_list = rtrim($results_list, ",");
        return $results_list;

    }


    /** ===================================
     * Get Member
     * ====================================    */
    function get_last_worked_on_info($args = array())
    {
        $db = new Db_helper();
        //AFY 14/09/21 $member_courses_ids = $this->get_member_course_ids($_SESSION['student_id']);
        //AFY 14/09/21 $member_courses_ids = explode(",", $member_courses_ids);

        if ($args['student_id']) {
            //AFY 14/09/21 $member_courses_ids = $this->get_member_course_ids($_SESSION['student_id']);
            //AFY 14/09/21 $member_courses_ids = explode(",", $member_courses_ids);
            //AFY 14/09/21 $bought_course_id__sql = "AND ols_online_courses.id IN ($member_courses_ids)";

            $user_id_sql = "AND ols_user_progress.rel_id='" . $args['student_id'] . "'";
        }

        $query = "
			SELECT 
				ols_course_units.id as unit_id,
				ols_course_modules.id as module_id,
				ols_online_courses.id as course_id
			FROM
				ols_user_progress 
				LEFT JOIN ols_course_units ON ols_course_units.id = ols_user_progress.db22013
				LEFT JOIN ols_course_modules ON ols_course_modules.id = ols_course_units.rel_id
				LEFT JOIN ols_online_courses ON ols_online_courses.id = ols_course_modules.rel_id
			WHERE
				1
				$user_id_sql
		  
				AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '')
				ORDER BY db22015 DESC LIMIT 1
			";
        $result = $db->query($query);

        foreach ($result as $row) {

            $results_list = array(
                "id" => $row['unit_id'],
                "module_id" => $row['module_id'],
                "course_id" => $row['course_id'],
                "unit_id" => $row['unit_id']
            );
        }

        return $results_list;
    }


    /** ===================================
     * Get Media
     * ====================================    */
    function get_media($args = array())
    {
        $db = new Db_helper();

        if ($args['school_id']) {
            $school_id_sql = "AND ols_media.usergroup='" . $args['school_id'] . "'";
        } else {
            $school_id_sql = "AND ols_media.usergroup='" . $_SESSION['usergroup'] . "'";
        }

        $query = "
			SELECT
				*
			FROM
				ols_media
			WHERE
				1
				$school_id_sql
				ORDER BY id DESC
			";
        $result = $db->query($query);

        foreach ($result as $row) {

            $results_list[] = array(
                "id" => $row['id'],
                "path" => $row['db25615'],
                "type" => $row['db25616'],
            );
        }

        if ($args['id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Insert Media
     * ====================================    */
    function update_or_insert_media($args)
    {
        $db = new Db_helper();

        $entry_info = array();
        if (array_key_exists('path', $args)) {
            $entry_info['db25615'] = $args['path'];
        }
        if (array_key_exists('type', $args)) {
            $entry_info['db25616'] = $args['type'];
        }


        if ($args['id']) {
            $where = array('id' => $args['id']);
            $db->update('ols_media', $entry_info, $where);
            return 1;
        } else {
            $db->system_table_insert_or_update('ols_media', $entry_info);
            $entry_id = $db->lastInsertId();
            return $entry_id;
        }
    }


    /** ===================================
     * Get Courses
     * ====================================    */
    function get_courses($args = array())
    {
        global $user_info, $paginator;
        $db = new Db_helper();
        $has_post_questionnaire_sql = '';
        if (!empty($args['id'])) {
            $id_sql = "AND ols_online_courses.id='" . $args['id'] . "'";
        }
        $filter_sql = "";
        if (!empty($args['filter_sql'])) {
            $filter_sql = $args['filter_sql'];
        }

        if (!empty($args['course_id'])) {
            $id_sql = "AND ols_online_courses.id='" . $args['course_id'] . "'";
        }

        if ($args['title']) {
            $title_sql = "AND ols_online_courses.db232='" . $args['title'] . "'";
        }

        if ($args['category']) {
            $category_id_sql = "AND ols_online_courses.db21929='" . $args['category'] . "'";
        }

        if ($args['course_level']) {
            $course_level_id_sql = "AND ols_online_courses.db37364 IN (" . $args['course_level'] . ")";
        }

        if (!empty($args['search']) || !empty($_POST['search'])) {
            $search_term = empty($_POST['search']) ? $args['search'] : $_POST['search'];
            $db_helper = new db_helper();
            $db_helper->insert("ols_course_search_log", ["db231929" => trim($search_term)]);
            $course_ids = pull_field(
                "core_course_topics_rel LEFT JOIN core_course_topics ON core_course_topics_rel.db32203 = core_course_topics.id",
                "GROUP_CONCAT(core_course_topics_rel.rel_id)", "WHERE core_course_topics.usergroup='{$_SESSION['usergroup']}' AND db32201 like '%{$search_term}%'"
            );
            $search_sql = "AND (ols_online_courses.db21909 LIKE '%" . $search_term . "%'" . (!empty($course_ids) ? " OR db236705 IN ($course_ids)" : '') . ")";
        }

        if ($args['featured']) {
            $featured_sql = "AND ols_online_courses.db25543='1'";
        }

        if ($args['username_id']) {
            $username_id_sql = "AND ols_online_courses.username_id='" . $args['username_id'] . "'";
        }

        if ($args['school_id']) {
            $usergroup_sql = "AND ols_online_courses.usergroup='" . $args['school_id'] . "'";
        }

        if ($args['sponsor_id']) {
            $sponsor_join = "INNER JOIN ols_access_plan_courses ON ols_access_plan_courses.db21911 = ols_online_courses.id INNER JOIN ols_access_plans ON ols_access_plans.id = ols_access_plan_courses.rel_id";
            $sponsor_id_sql = "AND ols_access_plans.db21880='" . $args['sponsor_id'] . "'";
        }

        if (!empty($args['has_post_questionnaire'])) {
            $has_post_questionnaire_sql = "AND (db25551 IS NOT NULL AND db25551 !='')";
        }
        if ($args['active_courses']) {
            $active_courses_ids = $this->get_member_course_ids($args['student_id']);
            if ($active_courses_ids) {
                $active_courses_sql = "AND ols_online_courses.id IN(" . $active_courses_ids . ")";
            } else {
                return array();
            }
        }

        if ($args['published_courses']) {
            $published_courses_sql = "AND (ols_online_courses.db30582 = 'yes' OR ols_online_courses.db30582 = '1')";

        }


        if ($args['library_only']) {
            $library_only_courses_sql = " AND (ols_online_courses.db309757 NOT IN ('yes' ,'1') OR ols_online_courses.db309757 is NULL) ";

        }

        if ($args['count']) {
            $select_sql = "count(distinct ols_online_courses.id) as total_count ";
        } else {
            $select_sql = "*";
        }

        $free_join="";
        $free_where_sql="";
        if ($args['free_only']) {
            $free_join=" join core_courses on db236705=core_courses.id ";
            $free_where_sql=" AND db232 like '%FREE%' ";
        }

        if (empty($args['transitioned_courses'])) {
            $query = "
                    SELECT 
                        $select_sql,
                        ols_online_courses.id as 'course_id',
                        
                                     (SELECT COUNT(ols_course_units.id) FROM ols_course_units LEFT JOIN ols_course_modules on ols_course_units.rel_id=CAST(ols_course_modules.id AS CHAR) WHERE ols_course_units.usergroup={$_SESSION['usergroup']} AND (ols_course_modules.rec_archive is null or ols_course_modules.rec_archive ='') AND (ols_course_units.rec_archive is null or ols_course_units.rec_archive ='') AND ols_course_modules.rel_id=CAST(ols_online_courses.id AS CHAR) AND ols_course_units.db21973 !='hidden' AND db21925 IS NOT NULL AND db21925 !='' ) as audio_count
        
                    FROM
                        ols_online_courses 
                        $sponsor_join 
                        $free_join
                    WHERE
                        1
                        $id_sql
                        $title_sql
                        $school_id_sql
                        $search_sql
                        $filter_sql
                        $username_id_sql
                        $featured_sql
                        $active_courses_sql
                        $published_courses_sql
                        $library_only_courses_sql
                        $category_id_sql
                        $usergroup_sql
                        $course_level_id_sql
                        $sponsor_id_sql
                        $has_post_questionnaire_sql
                        $free_where_sql
                        AND (ols_online_courses.rec_archive is null or ols_online_courses.rec_archive ='')
                    ORDER BY db251258 ASC";
        } else {
            $query = "
                    SELECT 
                        $select_sql,
                        ols_online_courses.id as 'course_id',
                        
                                     (SELECT COUNT(ols_course_units.id) FROM ols_course_units LEFT JOIN ols_course_modules on ols_course_units.rel_id=CAST(ols_course_modules.id AS CHAR) WHERE ols_course_units.usergroup={$_SESSION['usergroup']} AND (ols_course_modules.rec_archive is null or ols_course_modules.rec_archive ='') AND (ols_course_units.rec_archive is null or ols_course_units.rec_archive ='') AND ols_course_modules.rel_id=CAST(ols_online_courses.id AS CHAR) AND ols_course_units.db21973 !='hidden' AND db21925 IS NOT NULL AND db21925 !='' ) as audio_count
        
                    FROM
                        ols_online_courses 
                        $sponsor_join
                        $free_join 
                    WHERE
                        1
                        $id_sql
                        $title_sql
                        $school_id_sql
                        $search_sql
                        $filter_sql
                        $username_id_sql
                        $featured_sql
                        $active_courses_sql
                        $published_courses_sql
                        $library_only_courses_sql
                        $category_id_sql
                        $usergroup_sql
                        $course_level_id_sql
                        $sponsor_id_sql
                        $free_where_sql
                        $has_post_questionnaire_sql
                        AND (ols_online_courses.rec_archive is null or ols_online_courses.rec_archive ='')
                UNION 
                    SELECT 
                        $select_sql,
                        ols_online_courses.id as 'course_id',
                        
                                     (SELECT COUNT(ols_course_units.id) FROM ols_course_units LEFT JOIN ols_course_modules on ols_course_units.rel_id=CAST(ols_course_modules.id AS CHAR) WHERE ols_course_units.usergroup={$_SESSION['usergroup']} AND (ols_course_modules.rec_archive is null or ols_course_modules.rec_archive ='') AND (ols_course_units.rec_archive is null or ols_course_units.rec_archive ='') AND ols_course_modules.rel_id=CAST(ols_online_courses.id AS CHAR) AND ols_course_units.db21973 !='hidden' AND db21925 IS NOT NULL AND db21925 !='' ) as audio_count
        
                    FROM
                        ols_online_courses 
                        $sponsor_join 
                        $free_join
                    WHERE
                        1
                        $id_sql
                        $title_sql
                        $school_id_sql
                        $search_sql
                        $filter_sql
                        $username_id_sql
                        $featured_sql
                        $active_courses_sql
                        $category_id_sql
                        $usergroup_sql
                        $course_level_id_sql
                        $sponsor_id_sql
                        $has_post_questionnaire_sql
                        $free_where_sql
                        AND (ols_online_courses.rec_archive is null or ols_online_courses.rec_archive ='')	
                        AND ols_online_courses.id IN ((SELECT db259964 from ols_course_transition where rec_archive IS NULL OR rec_archive = ''))
                    ORDER BY db251258 ASC";

        }
        dev_debug($query);
        $result = $db->query($query);

        // if($args['featured']){
        // echo '<pre>';
        // echo $query;
        // echo '</pre>';
        // }
        $member_courses_ids = [];
        //Get bought courses IDs
        if ($args['student_id']) {
            $member_courses_ids = $this->get_member_course_ids($args['student_id']);
            $member_courses_ids = explode(",", $member_courses_ids);
            $student_id = $args['student_id'];
        } else {
            if ($_SESSION['student_id']) {
                $member_courses_ids = $this->get_member_course_ids($_SESSION['student_id']);
                $member_courses_ids = explode(",", $member_courses_ids);
                $student_id = $_SESSION['student_id'];
            }
        }
        $results_list = array();
        foreach ($result as $row) {

            if ($args['count']) {
                $results_list = $row['total_count'];
            } else {
                if (empty($args['count_modules'])) {

                    //Get Modules
                    $modules_args = array(
                        'course_id' => $row['id'],
                        'student_id' => $args['student_id'],
                        'no_unit_description' => $args['no_unit_description'],
                        'raw_html' => $args['raw_html'],
                        'show_hidden_units' => $args['show_hidden_units']
                    );

                    if ($args['single_module']) {
                        $modules_args['id'] = $args['single_module'];
                    }

                    if ($args['single_unit_description']) {
                        $modules_args['single_unit_description'] = $args['single_unit_description'];
                    }

                    if (isset($args['no_quiz_information'])) {
                        $modules_args['no_quiz_information'] = $args['no_quiz_information'];
                    }
                    dev_debug("HERE 3".json_encode($modules_args));
                    $modules = [];
                    if (empty($args['no_modules'])) {
                        $modules = $this->get_modules($modules_args);
                    }
                    //Count Total Units
                    $units_count = 0;
                    foreach ($modules as $module) {
                        if (!empty($module['units'])) $units_count += count($module['units']);
                    }

                    //Check which units have been completed
                    $completed_units_count = 0;
                    foreach ($modules as $module) {
                        foreach ($module['units'] as $u_info) {
                            if (!empty($u_info['completed_date'])) {
                                $completed_units_count++;
                            }
                        }
                    }
                } else {
                    $units_count = 0;
                    $completed_units_count = 0;
                    $count_modules = pull_field("ols_course_modules", "count(*)", "WHERE ols_course_modules.rel_id='$row[id]' AND ols_course_modules.rec_archive IS NULL OR ols_course_modules.rec_archive =''");
                }
                //Create a default excerpt if none
                if (!$args['no_default_excerpt']) {
                    if (!$row['db21913']) {
                        $string = strip_tags($row['db21910']);
                        if (strlen($string) > 100) {
                            $stringCut = substr($string, 0, 100);
                            $string = substr($stringCut, 0, strrpos($stringCut, ' ')) . '...';
                        }
                        $row['db21913'] = $string;
                    }
                }

                //Check if course has been bought
                $bought_course = false;
                if (count($member_courses_ids)) {
                    foreach ($member_courses_ids as $ci) {
                        if ($ci == $row['id']) {
                            $bought_course = true;
                            break;
                        }
                    }
                }

                //Check if user has started course
                $started_course = false;
                if ($args['count_modules']) {
                    if ($_SESSION['student_id']) {
                        $started_course = pull_field("ols_user_progress", "COUNT(id)", "WHERE ols_user_progress.rel_id='{$_SESSION['student_id']}' AND db37395 = {$row['id']}") > 0;
                    }
                } else {
                    foreach ($modules as $module) {
                        foreach ($module['units'] as $unit) {
                            if ($unit['last_accessed_date']) {
                                $started_course = true;
                                break;
                            }
                        }
                    }

                }
                //set the resume link to be the last completed unit + 1
                $resume_link = false;
                if (isset($student_id) && $student_id != '' && $bought_course == true) {
                    $completed_units = pull_field("ols_user_progress LEFT JOIN ols_course_modules ON ols_course_modules.id = db37396",
                        "Group_CONCAT(db22013)",
                        "WHERE ols_user_progress.rel_id='$student_id'
                    AND ols_course_modules.rel_id={$row['id']} AND (db22014 IS NOT NULL AND db22014!='')
                    AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '')");
                    if (!empty($completed_units)) {
                        $completed_units_sql = "AND ols_course_units.id NOT IN ($completed_units)";
                    }
                    $query = "SELECT ols_course_units.id as unit_id, ols_course_modules.id as module_id, COALESCE(db21921, ols_course_modules.id) as module_order
                                FROM ols_course_units 
                                LEFT JOIN ols_course_modules ON ols_course_modules.id = ols_course_units.rel_id 
                                WHERE ols_course_modules.usergroup={$_SESSION['usergroup']}
                                AND ols_course_modules.rel_id={$row['id']}
                                AND ols_course_units.usergroup={$_SESSION['usergroup']}
                                $completed_units_sql
                                AND (ols_course_modules.rec_archive IS NULL OR ols_course_modules.rec_archive='') 
                                AND (ols_course_units.rec_archive IS NULL OR ols_course_units.rec_archive='') 
                                AND db21973 !='hidden'
                                ORDER BY CAST(module_order AS SIGNED) ASC ,CAST(db21939 AS SIGNED) ASC LIMIT 1";
                    dev_debug($query);
                    $dbh = get_dbh();
                    $stmt = $dbh->prepare($query);
                    $stmt->execute();
                    $unit_modules = $stmt->fetchAll(2);
                    if (count($unit_modules) > 0) {
                        $mod_unit = $unit_modules[0];
                        $resume_link = $this->home_url("course/") .$this->ensure_slug_matches_title($row['id'], 'course')  . "/" .$this->ensure_slug_matches_title($mod_unit['module_id'], 'module')  . "/" . $this->ensure_slug_matches_title($mod_unit["unit_id"], 'unit')  ;
                    } else {
                        $c_modules = $this->get_modules(['course_id' => $row['id']]);
                        $resume_link = $this->home_url("course/") . $this->ensure_slug_matches_title($row['id'] , 'course') . "/" . $this->ensure_slug_matches_title($c_modules[0]['id'], 'module')  . "/" . $this->ensure_slug_matches_title($c_modules[0]["units"][0]["id"], 'unit') ;
                    }
                }


                $course_trial_units = array();
                // if any unit of any module is a trial unit get the trial units
                foreach ($modules as $course_module) {
                    foreach ($course_module['units'] as $course_unit) {
                        if ($course_unit['trail'] == '1') {
                            $course_trial_units[] = $course_unit['href'];
                        }
                    }
                }
                if ($args['no_quiz_information'] != "true") {
                    //get the pre questionnaires questions
                    if (isset($row['db25550']) && $row['db25550'] != '') {
                        $form_templates = new FormTemplates;
                        //Get the Form Info
                        $form_args = array("id" => $row['db25550'], "system_abbrv" => 'inst');
                        $form = $form_templates->get($form_args);
                        $questionnaire_questions = $form['fields'];

                    }
                }

                //language
                $language_info = array();
                $language_direction = 'ltr';
                $language_meta = null;
                $language_has_icon = false;
                $language_icon = null;
                $course_image = null;

                if (!empty($row['db253409'])) {
                    $course_image = $this->point_to_prod(engine_url("/media/dl.php?a=yes&fl=" . encode($row['db253409'], "unsalted")));
                }

                $course_thumbnail = null;

                if (!empty($row['db254762'])) {
                    $course_thumbnail = $this->point_to_prod(engine_url("/media/dl.php?a=yes&fl=" . encode($row['db254762'], "unsalted")));
                }

                if (isset($row['db31066']) && $row['db31066'] != '') {
                    $language_model = new languages_model;
                    $language_info = $this->get_languages(array('school_id' => $_SESSION['usergroup'], 'id' => $row['db31066']));
                    $language_direction = $language_info['direction'];
                    $language_meta = $language_model->get_language_meta($language_info['id'],true);
                    $language_meta = count($language_meta) > 0 ? $language_meta : [];
                    $index = array_search("icon", array_column($language_meta, "meta_key"));
                    if (is_numeric($index)) {
                        $server_domain_name = env('APP_URL');
                        $target_path = "/var/www/vhosts/$server_domain_name/private/media/";
                        $str = str_replace($target_path, "", $language_meta[$index]["meta_value"]);
                        $language_meta[$index]["meta_value"] = $this->point_to_prod(engine_url("/media/dl.php?a=yes&fl=" . encode($str, "unsalted")));
                        $language_has_icon = true;
                        $language_icon = $this->point_to_prod(engine_url("/media/dl.php?a=yes&fl=" . encode($str, "unsalted")));
                    }
                }
                $last_completed = pull_field("ols_user_progress", "CONCAT_WS(',',db37396,db22013)", "WHERE rel_id='{$_SESSION['student_id']}' AND db37395 = '{$row['id']}' ORDER by id DESC LIMIT 1");
                if ($last_completed) {
                    list ($last_completed_module_id, $last_completed_unit_id) = explode(",", $last_completed);
                } else {
                    $last_completed_module_id = $modules[0]["id"];
                }
                $course_options = []; 
                if (!empty($args["get_core_courses"])) {
                    $dbh = get_dbh();
                    $query = "SELECT id, db232 as title FROM core_courses WHERE usergroup=?";
                    $stmt = $dbh->prepare($query);
                    $stmt->execute([$_SESSION["usergroup"]]);
                    $course_options = $stmt->fetchAll(2);
                }

                $group_name = "";
                if (!empty($row['db21929'])) {
                    $group_name = pull_field('ols_course_groups', 'db21927', "WHERE id='" . $row['db21929'] . "' LIMIT 1");
                }

                //Result
                $course_info = array(
                    "id" => $row['id'],
                    'course_id' => $row['course_id'],
                    'course_slug' => $this->generate_unique_slug($row['db21909'], 'ols_online_courses', 'db361083', $row['course_id'] ?? null),
                    "selected_course" => $row['db236705'],
                    "course_external_url"=>$row['db358213'],
                    "has_audio" => $row['audio_count'] > 0,
                    "course_options" => $course_options,
                    "buy_course_text" => text_translate(terminology("Get Started", $_SESSION['url'], 'Online learning buy course', true), $_SESSION['lang']),
                    "started_course" => $started_course,
                    "resume_link" => $resume_link,
                    "image" => $course_image, 
                    "thumbnail" => $course_thumbnail,
                    "last_module_id" => intval($last_completed_module_id),
                    "last_unit_id" => intval($last_completed_unit_id),
                    "last_unit_order" => intval($last_completed_unit_order),
                    "bought" => $bought_course,
                    "username_id" => $row['username_id'],
                    "title" => $row['db21909'],
                    "alternative_title" => $row['db260924'],
                    "excerpt" => $row['db21913'],
                    "colour" => $row['db21914'],
                    "next_unit_visibility" => $row['db21915'],
                    "subject" => $row['db21917'],
                    "group" => $row['db21929'],
                    "group_name" => $group_name,
                    "course_level" => $row['db37364'],
                    "course_language" => $row['db31066'],
                    "course completion_text" => $row['db21931'],
                    "unit_complete_button_color" => $row['db21932'],
                    "unit_complete_button_text" => $row['db21931'],
                    "module_complete_button_color" => $row['db21935'],
                    "module_complete_button_text" => $row['db21934'],
                    "enable_feedback" => $row['db21937'],
                    "course_feedback_form" => $row['db21938'],
                    "enable_download_certificate" => $row['db21940'],
                    "course_viewable_after_completion" => $row['db21941'],
                    "featured" => $row['db25543'],
                    "pre_questions_form_id" => $row['db25550'],
                    "post_questions_form_id" => $row['db25551'],
                    "show_support_on_each_unit" => $row['db21950'],
                    "show_feedback_on_each_unit" => $row['db66669'],
                    "language" => $language_info,
                    "language_direction" => $language_direction,
                    "publish" => $row['db30582'],
                    "rm_from_library" => $row['db309757'],
                    "href" => $this->home_url("course/") . $this->ensure_slug_matches_title($row['id'], 'course') ,
                    "slug_href"=>"",
                    "units_count" => $units_count,
                    "completed_units_count" => $completed_units_count,
                    "completion_percentage" => $units_count == 0 ? 0 : number_format(($completed_units_count / $units_count) * 100, 0),
                    "modules" => $modules,
                    "trial_units" => $course_trial_units,
                    "questionnaire_type" => $row['db26246'],
                    "closeness_questions" => $row['db26247'],
                    "conflict_questions" => $row['db26248'],
                    "all_questionnaire_questions" => $questionnaire_questions,
                    "score_1_text" => $row['db26238'],
                    "score_2_text" => $row['db26239'],
                    "score_3_text" => $row['db26240'],
                    "score_4_text" => $row['db26241'],
                    "score_5_text" => $row['db26242'],
                    "score_6_text" => $row['db26243'],
                    "score_7_text" => $row['db26244'],
                    "score_8_text" => $row['db26245'],
                    "text_before_questionnaire" => $row['db250853'],
                    "language_meta" => $language_meta,
                    "language_has_icon" => $language_has_icon,
                    "language_icon" => $language_icon,
                    "parent_course" => $row['db253412'],
                    "order" => $row['db251258']
                );

                if (!empty($course_info['course_slug'])) {
                    $course_info['slug_href']= $this->home_url("course-v4/" . $course_info['course_slug']);
                }

                if (!empty($args['friendly_reminder_emails'])) {
                    $unsub_mails=explode(',',  pull_field("dir_core_students_meta", "db40613", "WHERE rel_id = '".$args['student_id']."'"));
                    if (in_array($row['id'], $unsub_mails)) {
                        $course_info['reminder_active']=true;
                    }else{
                        $course_info['reminder_active']=false;
                    }
                }

                 if (!empty($args['show_other_languages'])) {
                    if (empty($row['db253412'])) {
                        $row['db253412']=$row['id'];
                    }
                    $course_info['other_languages']=$this->get_related_courses($row['db253412'],$row['id'],$row['db31066']);
                }

                if ($args['raw_html']) {
                    $course_info['description'] = $row['db21910'];
                } else if (!empty($args['process_shortcodes'])) {
                    $course_info['description'] = $this->proccess_short_codes($row['db21910']);
                } else {
                    if (ACTIVE_COURSE == $row['id']) {
                        if ($row['db21910']) {
                            $course_info['description'] = $this->proccess_short_codes($row['db21910']);
                        }
                        // /$course_info['description'] = $row['db21910'];
                    } else {
                        $course_info['description'] = $row['db21910'];
                    }
                }
                if (!empty($args['count_modules'])) {
                    $course_info['count_modules'] = $count_modules;

                }

                if (!empty($args['with_plans'])) {
                    $plan_args = array(
                        'course' => $row['username_id'],
                        'school_id' => $_SESSION['usergroup'],
                        'plans_only' => '1',
                        'hide_coupons' => true
                    );

                    $course_info['plans'] = $this->get_plans($plan_args);
                }

                if (!empty($args['with_languages'])) {
                    dev_debug("get_courses_v2 6");
                    $course_info['languages'] = $this->get_courses_v2(['language_link' => $row['db253412'], 'no_modules' => true]);
                }

                if (!empty($args['with_categories'])) {
                    $cat_args = [
                        'id' => $row['db253415'],
                        'usergroup' => $_SESSION['school_id']
                    ];
                    $course_info['category_new'] = !empty($row['db253415']) ? $this->get_category($cat_args) : null;
                }


                $results_list[] = $course_info;

            }

        }

        // echo '<pre>';
        // print_r($results_list);
        // exit();

        if ($args['id'] || $args['title'] || $args['username_id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

  
      /** ===================================
     * Get Courses
     * ====================================    */
    function get_courses_v2($args = array())
    {
        global $user_info;
        $db = new Db_helper();
        $transition_course = array();
        $has_transitioned = NULL;

        if ($args['id']) {
            $id_sql = "AND ols_online_courses.id=" . $args['id'];
        }

        if (!empty($args['ids'])) {
            $id_sql = "AND ols_online_courses.id IN({$args['ids']})";
        }

        if (isset($args['student_id']) || isset($_SESSION['student_id'])) {
            $student_id = isset($args['student_id']) ? $args['student_id'] : $_SESSION['student_id'];

        }

        if ($args['title']) {
            $title_sql = "AND ols_online_courses.db232='" . $args['title'] . "'";
        }

        if ($args['category']) {
            $category_id_sql = "AND ols_online_courses.db21929='" . $args['category'] . "'";
        }

        if ($args['course_level']) {
            $course_level_id_sql = "AND ols_online_courses.db37364 IN (" . $args['course_level'] . ")";
        }

        if (!empty($args['search']) || !empty($_POST['search']) || (!empty($_SESSION['personalization']) && empty($args['featured']))) {
            $search_term = empty($_POST['search']) ? (empty($args['search']) ? $_SESSION['personalization']['interest'] : $args['search']) : $_POST['search'];
            $words = explode(" ", str_replace("_", " ", $search_term));
            $searchQuery = implode(" OR ", array_map(function ($term) {
                return "db32201 LIKE '%" . $term . "%'";
            }, $words));
            if (!empty($search_term)) {
                $db_helper = new db_helper();
                $db_helper->insert("ols_course_search_log", ["db231929" => trim($search_term)]);
                $course_ids = pull_field(
                    "core_course_topics_rel LEFT JOIN core_course_topics ON core_course_topics_rel.db32203 = CAST(core_course_topics.id AS CHAR)",
                    "GROUP_CONCAT(CONCAT(\"'\",core_course_topics_rel.rel_id,\"'\"))", "WHERE core_course_topics.usergroup='{$_SESSION['usergroup']}' AND ({$searchQuery})"
                );
                $search_sql = "AND (ols_online_courses.db21909 LIKE '%" . $search_term . "%'" . (!empty($course_ids) ? " OR db236705 IN ($course_ids)" : '') . ")";
            }
        }

        if ($args['featured']) {
            $featured_sql = "AND ols_online_courses.db25543='1'";
        }

        if ($args['username_id']) {
            $username_id_sql = "AND ols_online_courses.username_id='" . $args['username_id'] . "'";
        }

        if ($args['school_id']) {
            $usergroup_sql = "AND ols_online_courses.usergroup='" . $args['school_id'] . "'";
        }

        if ($args['sponsor_id']) {
            $sponsor_join = "INNER JOIN ols_access_plan_courses ON ols_access_plan_courses.db21911 = CAST(ols_online_courses.id AS CHAR) INNER JOIN ols_access_plans ON ols_access_plans.id = CAST(ols_access_plan_courses.rel_id AS UNSIGNED)";
            $sponsor_id_sql = "AND ols_access_plans.db21880='" . $args['sponsor_id'] . "'";
        }

        if ($args['published_courses']) {
            $published_courses_sql = "AND (ols_online_courses.db30582 = 'yes' OR ols_online_courses.db30582 = '1')";

        }
        if ($args['library_only']) {
            $library_only_courses_sql = " AND (ols_online_courses.db309757 NOT IN ('yes' ,'1') OR ols_online_courses.db309757 is NULL) ";

        }
        if ($args['active_courses']) {
            $active_courses_ids = $this->get_member_course_ids($args['student_id']);
            if ($active_courses_ids) {
                $active_courses_sql = "AND ols_online_courses.id IN(" . $active_courses_ids . ")";
                $published_courses_sql = '';
            } else {
                return array();
            }
        }
        $filter_sql = "";
        if (!empty($args['filter_sql'])) {
            $filter_sql = $args['filter_sql'];
        }
        $module_units_count_join="";
        $completed_join="";
        if ($args['count']) {
            $select_sql = "count(distinct ols_online_courses.id) as total_count ";
        } else {
            $completed_count = "";
            if (!empty($student_id)) {
                // $completed_count = ",(SELECT COUNT(ols_user_progress .id) FROM ols_user_progress LEFT JOIN  ols_course_units ON ols_course_units.id = CAST(db22013 AS SIGNED) LEFT JOIN  ols_course_modules ON ols_course_modules.id = CAST(db37396 AS SIGNED) WHERE ols_course_units.usergroup='{$_SESSION['usergroup']}' AND (ols_course_modules.rec_archive IS NULL OR ols_course_modules.rec_archive='') AND (ols_course_units.rec_archive IS NULL OR ols_course_units.rec_archive='') AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive='') AND ols_course_units.db21973 !='hidden' AND db37395=CAST(ols_online_courses.id AS CHAR) AND ols_user_progress.rel_id = {$student_id} AND (db22014 IS NOT NULL AND db22014 !='')  GROUP BY db37395, ols_user_progress.rel_id) as completed_units";
                 $completed_count = ",COUNT(DISTINCT CASE 
                    WHEN up.id IS NOT NULL AND up.db22014 IS NOT NULL AND up.db22014 != '' THEN up.id 
                  END) AS completed_units";
                $completed_join="LEFT JOIN 
                    ols_user_progress up
                    ON up.db37395 = CAST(ols_online_courses.id AS CHAR)
                    AND up.rel_id = '{$student_id}'
                    AND (up.rec_archive IS NULL OR up.rec_archive = '')
                    AND (up.db22014 IS NOT NULL AND up.db22014 != '')";
            }
            // $select_sql = "ols_online_courses.*,
            //  (SELECT COUNT(id) FROM ols_course_modules WHERE usergroup='{$_SESSION['usergroup']}' AND rel_id=CAST(ols_online_courses.id AS CHAR) AND (rec_archive is null or rec_archive ='')) as module_count,
            //  (SELECT COUNT(ols_course_units.id) FROM ols_course_units LEFT JOIN ols_course_modules on ols_course_units.rel_id=CAST(ols_course_modules.id AS CHAR) WHERE ols_course_units.usergroup='{$_SESSION['usergroup']}' AND (ols_course_modules.rec_archive is null or ols_course_modules.rec_archive ='') AND (ols_course_units.rec_archive is null or ols_course_units.rec_archive ='') AND ols_course_modules.rel_id=CAST(ols_online_courses.id AS CHAR) AND ols_course_units.db21973 !='hidden') as unit_count,
            //  (SELECT COUNT(ols_course_units.id) FROM ols_course_units LEFT JOIN ols_course_modules on ols_course_units.rel_id=CAST(ols_course_modules.id AS CHAR) WHERE ols_course_units.usergroup='{$_SESSION['usergroup']}' AND (ols_course_modules.rec_archive is null or ols_course_modules.rec_archive ='') AND (ols_course_units.rec_archive is null or ols_course_units.rec_archive ='') AND ols_course_modules.rel_id=CAST(ols_online_courses.id AS CHAR) AND ols_course_units.db21973 !='hidden' AND db21925 IS NOT NULL AND db21925 !='' ) as audio_count
            //  $completed_count";

            $select_sql = "ols_online_courses.*,
                COUNT(DISTINCT cm.id) AS module_count,
                COUNT(DISTINCT cu.id) AS unit_count,
                COUNT(DISTINCT CASE 
                    WHEN cu.db21925 IS NOT NULL AND cu.db21925 != '' THEN cu.id 
                  END) AS audio_count
             $completed_count";


              $module_units_count_join="
                LEFT JOIN 
                    ols_course_modules cm 
                    ON cm.rel_id = CAST(ols_online_courses.id AS CHAR)
                    AND cm.usergroup = '$_SESSION[usergroup]'
                    AND (cm.rec_archive IS NULL OR cm.rec_archive = '')
                LEFT JOIN 
                    ols_course_units cu 
                    ON cu.rel_id = CAST(cm.id as CHAR)
                    AND cu.usergroup = '$_SESSION[usergroup]'
                    AND (cu.rec_archive IS NULL OR cu.rec_archive = '')
                    AND cu.db21973 != 'hidden'";

        }

        $language_link_sql = "";
        if (!empty($args['language_link'])) {
            $language_link_sql = "AND db253412='" . $args['language_link'] . "' AND (db253412 IS NOT NULL OR db253412 != CAST(ols_online_courses.id AS CHAR))";
        }

        $main_language_courses_sql = "";
        if (!empty($args['main_language_courses'])) {
            $main_language_courses_sql = "AND (db253412='' OR db253412 IS NULL OR db253412=ols_online_courses.id) AND db31066='1'";
        }
        
        $with_langs_joins="";
        if (!empty($args['with_languages'])) {
            //$select_sql .= ",(SELECT group_concat(oc.id) FROM ols_online_courses oc WHERE oc.db253412=ols_online_courses.id AND ols_online_courses.usergroup='" . $_SESSION['usergroup'] . "') as languages";
            $select_sql .=",GROUP_CONCAT(DISTINCT oclang.id) AS languages";
            $with_langs_joins="LEFT JOIN 
            ols_online_courses oclang 
            ON oclang.db253412 = ols_online_courses.id
            AND oclang.usergroup = '" . $_SESSION['usergroup'] . "'";
        }

        $free_join="";
        $free_where_sql="";
        if (!empty($args['free_only'])) {
            $free_join=" join core_courses on db236705=core_courses.id ";
            $free_where_sql=" AND db232 like '%FREE%' ";
        }
        $order_sql = "ORDER BY db251258 ASC";
        if (!empty($args['order'])) {
            $order_sql = $args['order'];
        }


        $query = "
            SELECT 
             $select_sql
            FROM
            ols_online_courses
            $module_units_count_join
            $completed_join
            $with_langs_joins
            $sponsor_join
            $free_join 
            WHERE
                 ols_online_courses.usergroup='$_SESSION[usergroup]' 
                 $id_sql
                $title_sql
                $search_sql
                $username_id_sql
                $featured_sql
                $active_courses_sql
                $published_courses_sql
                $library_only_courses_sql
                $category_id_sql
                $usergroup_sql
                $course_level_id_sql
                $sponsor_id_sql
                {$filter_sql}
              {$language_link_sql}
              {$main_language_courses_sql}
              {$free_where_sql}
                AND (ols_online_courses.rec_archive is null or ols_online_courses.rec_archive ='')
                GROUP BY ols_online_courses.id
            {$order_sql}";
        dev_debug("get_courses_v2 " . $query);
        $result = $db->query($query);

        // if($args['featured']){
        // echo '<pre>';
        // echo $query;
        // echo '</pre>';
        // }

        //Get bought courses IDs
        $member_courses_ids = [];
        if (isset($args['student_id']) || isset($_SESSION['student_id'])) {
            $member_courses_ids = $this->get_member_course_ids($student_id);
            $member_courses_ids = explode(",", $member_courses_ids);
            $student_id = $args['student_id'];
        }
        $results_list = array();
        foreach ($result as $row) {

            //Get Modules
            $modules_args = array(
                'course_id' => $row['id'],
                'student_id' => $args['student_id'],
                'no_unit_description' => $args['no_unit_description'],
                'raw_html' => $args['raw_html'],
                'show_hidden_units' => $args['show_hidden_units']
            );

            if ($args['single_module']) {
                $modules_args['id'] = $args['single_module'];
            }

            if ($args['single_unit_description']) {
                $modules_args['single_unit_description'] = $args['single_unit_description'];
            }

            if (!empty($args['hide_units'])) {
                $modules_args['hide_units'] = $args['hide_units'];
            }

            if (isset($args['no_quiz_information'])) {
                $modules_args['no_quiz_information'] = $args['no_quiz_information'];
            }
            dev_debug("HERE 1".json_encode($modules_args));
            $modules = [];
            if (empty($args['no_modules'])) {
                $modules = $this->get_modules($modules_args);
            }

            if ($args['count']) {
                $results_list = $row['total_count'];
            } else {
                //Create a default excerpt if none
                if (!$args['no_default_excerpt']) {
                    if (!$row['db21913']) {
                        $string = strip_tags($row['db21910']);
                        if (strlen($string) > 100) {
                            $stringCut = substr($string, 0, 100);
                            $string = substr($stringCut, 0, strrpos($stringCut, ' ')) . '...';
                        }
                        $row['db21913'] = $string;
                    }
                }

                //Check if course has been bought
                $bought_course = false;
                if (count($member_courses_ids) && in_array($row['id'], $member_courses_ids)) {
                    $bought_course = true;
                }

                //Check if user has started course
                $started_course = (isset($row['completed_units']) && $row['completed_units'] > 0);


                //set the resume link to be the last completed unit + 1
                $resume_link = false;
                if (isset($student_id) && $student_id != '' && $bought_course == true) {
                    $has_transitioned = pull_field("ols_user_course_transitions", "id", "WHERE rel_id = '$student_id' AND db260402 = '{$row['id']}'");

                    if (empty($has_transitioned)) {
                        //check to see if this course has a new version
                        list($transition_course_to, $transition_before_module, $transition_before_unit) = explode("|||", pull_field("ols_course_transition", "CONCAT(db259967, '|||', db259973,'|||',db260399)", "WHERE db259964 = '{$row['id']}' AND (rec_archive IS NULL OR rec_archive='')"));

                        $completed_units = pull_field("ols_user_progress LEFT JOIN ols_course_modules ON ols_course_modules.id = CAST(db37396 AS SIGNED)",
                            "Group_CONCAT(db22013)",
                            "WHERE ols_user_progress.rel_id='$student_id'
                        AND ols_course_modules.rel_id={$row['id']} AND (db22014 IS NOT NULL AND db22014!='')
                        AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '')");
                        if (!empty($completed_units)) {
                            $completed_units_sql = "AND ols_course_units.id NOT IN ($completed_units)";
                        }
                        $query = "SELECT ols_course_units.id as unit_id, ols_course_modules.id as module_id, COALESCE(db21921, ols_course_modules.id) as module_order,COALESCE(db21939, ols_course_units.id) as unit_order
                                    FROM ols_course_units 
                                    LEFT JOIN ols_course_modules ON ols_course_modules.id = ols_course_units.rel_id 
                                    WHERE ols_course_modules.usergroup={$_SESSION['usergroup']}
                                    AND ols_course_modules.rel_id={$row['id']}
                                    AND ols_course_units.usergroup={$_SESSION['usergroup']}
                                    $completed_units_sql
                                    AND (ols_course_modules.rec_archive IS NULL OR ols_course_modules.rec_archive='') 
                                    AND (ols_course_units.rec_archive IS NULL OR ols_course_units.rec_archive='') 
                                    AND db21973 !='hidden'
                                    ORDER BY CAST(module_order AS SIGNED) ASC ,CAST(db21939 AS SIGNED) ASC LIMIT 1";
                        dev_debug($query);
                        $dbh = get_dbh();
                        $stmt = $dbh->prepare($query);
                        $stmt->execute();
                        $unit_modules = $stmt->fetchAll(2);
                        if (count($unit_modules) > 0) {
                            $mod_unit = $unit_modules[0];
                            $resume_link = $this->home_url("course/") . $this->ensure_slug_matches_title($row['id'], 'course')  . "/" . $this->ensure_slug_matches_title($mod_unit['module_id'], 'module')  . "/" . $this->ensure_slug_matches_title($mod_unit["unit_id"], 'unit') ;
                            if (!empty ($transition_course_to)) {
                                $transition_course_to_name = pull_field("ols_online_courses", "db21909", "WHERE id = $transition_course_to");
                                //check if the order of the next unit is before or after or equal to the unit for automatic transition
                                $resume_module_order = $mod_unit['module_order'];
                                $resume_unit_order = $mod_unit['unit_order'];
                                $transition_module_order = pull_field("ols_course_modules", "COALESCE(db21921, ols_course_modules.id)", "WHERE id = {$transition_before_module}");
                                $transition_unit_order = pull_field("ols_course_units", "COALESCE(db21939, ols_course_units.id)", "WHERE id = {$transition_before_unit}");
                                if ($transition_module_order > $resume_module_order) {
                                    $automatic_transition = true;
                                } else if ($transition_module_order == $resume_module_order) {
                                    if ($transition_unit_order > $resume_unit_order) {
                                        $automatic_transition = true; //Force this user to transition
                                    } else {
                                        $automatic_transition = false; //Do not force this user to transition
                                    }
                                } else {
                                    $automatic_transition = false;
                                }
                                $transition_course = array(
                                    'id' => $transition_course_to,
                                    'name' => $transition_course_to_name,
                                    'automatic_transition' => $automatic_transition,
                                    'student_id' => $student_id
                                );


                            }
                        } else {
                            $c_modules = $this->get_modules(['course_id' => $row['id']]);
                            $resume_link = $this->home_url("course/") . $this->ensure_slug_matches_title($row['id'], 'course')  . "/" . $this->ensure_slug_matches_title($c_modules[0]['id'], 'module')  . "/" . $this->ensure_slug_matches_title($c_modules[0]["units"][0]["id"], 'unit') ;
                            if (!empty ($transition_course_to)) {
                                $transition_course_to_name = pull_field("ols_online_courses", "db21909", "WHERE id = $transition_course_to");
                                $automatic_transition = true; //Force this user to transition
                                $transition_course = array(
                                    'id' => $transition_course_to,
                                    'name' => $transition_course_to_name,
                                    'automatic_transition' => $automatic_transition,
                                    'student_id' => $student_id
                                );
                            }
                        }

                    } else {
                        //student has already transitioned, no access to ols course is allowed
                        $bought_course = false;
                        $resume_link = '';
                    }
                }

                $course_trial_units = array();

                if ($args['no_quiz_information'] != "true") {
                    //get the pre questionnaires questions
                    if (isset($row['db25550']) && $row['db25550'] != '') {
                        $form_templates = new FormTemplates;
                        //Get the Form Info
                        $form_args = array("id" => $row['db25550'], "system_abbrv" => 'inst');
                        $form = $form_templates->get($form_args);
                        $questionnaire_questions = $form['fields'];

                    }
                }

                //language
                $language_info = array();
                $language_direction = 'ltr';
                $language_meta = null;
                $language_has_icon = false;
                $language_icon = null;

                if (isset($row['db31066']) && $row['db31066'] != '') {
                    $language_model = new languages_model;
                    $language_info = $this->get_languages(array('school_id' => $_SESSION['usergroup'], 'id' => $row['db31066']));
                    $language_direction = $language_info['direction'];
                    $language_meta = $language_model->get_language_meta($language_info['id'],true);
                    $language_meta = count($language_meta) > 0 ? $language_meta : [];
                    $index = array_search("icon", array_column($language_meta, "meta_key"));
                    if (is_numeric($index)) {
                        $server_domain_name = env('APP_URL');
                        $target_path = "/var/www/vhosts/$server_domain_name/private/media/";
                        $str = str_replace($target_path, "", $language_meta[$index]["meta_value"]);
                        $language_meta[$index]["meta_value"] = $this->point_to_prod(engine_url("/media/dl.php?a=yes&fl=" . encode($str, "unsalted")));
                        $language_has_icon = true;
                        $language_icon = $this->point_to_prod(engine_url("/media/dl.php?a=yes&fl=" . encode($str, "unsalted")));
                    }
                }
                $last_completed = pull_field("ols_user_progress", "CONCAT_WS(',',db37396,db22013)", "WHERE rel_id='{$_SESSION['student_id']}' AND db37395 = '{$row['id']}' ORDER by id DESC LIMIT 1");
                if ($last_completed) {
                    list ($last_completed_module_id, $last_completed_unit_id) = explode(",", $last_completed);
                } else {
                    $last_completed_module_id = $modules[0]["id"];
                }

                $course_options = [];
                if (!empty($args["get_core_courses"])) {
                    $dbh = get_dbh();
                    $query = "SELECT id, db232 as title FROM core_courses WHERE usergroup=?";
                    $stmt = $dbh->prepare($query);
                    $stmt->execute([$_SESSION["usergroup"]]);
                    $course_options = $stmt->fetchAll(2);
                }

                $course_image = null;

                if (!empty($row['db253409'])) {
                    $course_image = $this->point_to_prod(engine_url("/media/dl.php?a=yes&fl=" . encode($row['db253409'], "unsalted")));
                }

                $course_thumbnail = null;

                if (!empty($row['db254762'])) {
                    $course_thumbnail = $this->point_to_prod(engine_url("/media/dl.php?a=yes&fl=" . encode($row['db254762'], "unsalted")));
                }

                $category_name = "";
                if (!empty($row['db21929'])) {
                    $category_name = pull_field('ols_course_groups', 'db21927', "WHERE id='" . $row['db21929'] . "' LIMIT 1");
                }

                //Result
                $course_info = array(
                    "id" => $row['id'],
                    'course_slug' => $this->generate_unique_slug($row['db21909'], 'ols_online_courses', 'db361083', $row['id'] ?? null),
                    "selected_course" => $row['db236705'],
                    "course_external_url"=>$row['db358213'],
                    "has_audio" => $row['audio_count'] > 0,
                    "buy_course_text" => text_translate(terminology("Buy Course", $_SESSION['url'], 'Online learning buy course', true), $course['language']['id']),
                    "started_course" => $started_course,
                    "resume_link" => $resume_link,
                    "image" => $course_image,
                    "thumbnail" => $course_thumbnail,
                    "course_options" => $course_options,
                    "last_module_id" => intval($last_completed_module_id),
                    "last_unit_id" => intval($last_completed_unit_id),
                    "last_unit_order" => intval($last_completed_unit_order),
                    "bought" => $bought_course,
                    "username_id" => $row['username_id'],
                    "title" => $row['db21909'],
                    "alternative_title" => $row['db260924'],
                    "excerpt" => $row['db21913'],
                    "colour" => $row['db21914'],
                    "next_unit_visibility" => $row['db21915'],
                    "subject" => $row['db21917'],
                    "group" => $row['db21929'],
                    "category_name" => $category_name,
                    "course_level" => $row['db37364'],
                    "course_language" => $row['db31066'],
                    "course completion_text" => $row['db21931'],
                    "unit_complete_button_color" => $row['db21932'],
                    "unit_complete_button_text" => $row['db21931'],
                    "module_complete_button_color" => $row['db21935'],
                    "module_complete_button_text" => $row['db21934'],
                    "enable_feedback" => $row['db21937'],
                    "course_feedback_form" => $row['db21938'],
                    "enable_download_certificate" => $row['db21940'],
                    "course_viewable_after_completion" => $row['db21941'],
                    "featured" => $row['db25543'],
                    "pre_questions_form_id" => $row['db25550'],
                    "post_questions_form_id" => $row['db25551'],
                    "show_support_on_each_unit" => $row['db21950'],
                    "show_feedback_on_each_unit" => $row['db66669'],
                    "language" => $language_info,
                    "language_direction" => $language_direction,
                    "publish" => $row['db30582'],
                    "rm_from_library" => $row['db309757'],
                    "href" => $this->home_url("course/") . $this->ensure_slug_matches_title($row['id'], 'course') ,
                    "slug_href"=> "",
                    "completion_percentage" => $row['unit_count'] == 0 ? 0 : number_format(($row['completed_units'] / $row['unit_count']) * 100, 0),
                    "module_count" => $row['module_count'],
                    "modules" => $modules,
                    "trial_units" => $course_trial_units,
                    "questionnaire_type" => $row['db26246'],
                    "closeness_questions" => $row['db26247'],
                    "conflict_questions" => $row['db26248'],
                    "all_questionnaire_questions" => $questionnaire_questions,
                    "score_1_text" => $row['db26238'],
                    "score_2_text" => $row['db26239'],
                    "score_3_text" => $row['db26240'],
                    "score_4_text" => $row['db26241'],
                    "score_5_text" => $row['db26242'],
                    "score_6_text" => $row['db26243'],
                    "score_7_text" => $row['db26244'],
                    "score_8_text" => $row['db26245'],
                    "language_meta" => $language_meta,
                    "language_has_icon" => $language_has_icon,
                    "language_icon" => $language_icon,
                    "order" => $row['db251258'],
                    "parent_course" => $row['db253412'],
                    "transition_course" => $transition_course,

                );

                if (!empty($course_info['course_slug'])) {
                    $course_info['slug_href']= $this->home_url("course-v4/" . $course_info['course_slug']);
                }


                if (!empty($args['show_other_languages'])) {
                    if (empty($row['db253412'])) {
                        $row['db253412']=$row['id'];
                    }
                    $course_info['other_languages']=$this->get_related_courses($row['db253412'],$row['id'],$row['db31066']);
                }



                if (empty($args['minimal_info'])) {
                    if ($args['raw_html']) {
                        $course_info['description'] = $row['db21910'];
                    } else if (!empty($args['process_shortcodes'])) {
                        $course_info['description'] = $this->proccess_short_codes($row['db21910']);
                    } else {
                        if (defined('ACTIVE_COURSE')) {
                            if (ACTIVE_COURSE == $row['id']) {
                                if ($row['db21910']) {
                                    $course_info['description'] = $this->proccess_short_codes($row['db21910']);
                                }
                                // /$course_info['description'] = $row['db21910'];
                            }
                        } else {
                            $course_info['description'] = $row['db21910'];
                        }
                    }
                }

                if (!empty($args['count_modules'])) {
                    $course_info['count_modules'] = $count_modules;
                }

                if (!empty($args['with_plans'])) {
                    $plan_args = array(
                        'course' => $row['username_id'],
                        'school_id' => $_SESSION['usergroup'],
                        'plans_only' => '1',
                        'hide_coupons' => true,
                    );

                    $course_info['plans'] = $this->get_plans($plan_args);
                }

                if (!empty($row['languages'])) {
                    dev_debug("get_courses_v2 5");
                    $course_info['languages'] = $this->get_courses_v2(['ids' => $row['languages'], 'no_modules' => true]);
                }

                if (!empty($args['with_categories'])) {
                    $cat_args = [
                        'id' => $row['db253415'],
                        'usergroup' => $_SESSION['school_id']
                    ];
                    $course_info['category_new'] = !empty($row['db253415']) ? $this->get_category($cat_args) : null;
                }

                if (!empty($args['minimal_info'])) {
                    unset($course_info['selected_course']);
                    unset($course_info['buy_course_text']);
                    unset($course_info['started_course']);
                    unset($course_info['resume_link']);
                    unset($course_info['last_module_id']);
                    unset($course_info['last_unit_id']);
                    unset($course_info['last_unit_order']);
                    unset($course_info['bought']);
                    unset($course_info['username_id']);
                    unset($course_info['alternative_title']);
                    //unset($course_info['image']); // Keep 'thumbnail' instead for performance
                    unset($course_info['next_unit_visibility']);
                    unset($course_info['subject']);
                    unset($course_info['group']);
                    unset($course_info['course_level']);
                    unset($course_info['course_completion_text']);
                    unset($course_info['unit_complete_button_color']);
                    unset($course_info['unit_complete_button_text']);
                    unset($course_info['module_complete_button_color']);
                    unset($course_info['module_complete_button_text']);
                    unset($course_info['enable_feedback']);
                    unset($course_info['course_feedback_form']);
                    unset($course_info['enable_download_certificate']);
                    unset($course_info['course_viewable_after_completion']);
                    unset($course_info['featured']);
                    unset($course_info['pre_questions_form_id']);
                    unset($course_info['post_questions_form_id']);
                    unset($course_info['show_support_on_each_unit']);
                    unset($course_info['show_feedback_on_each_unit']);
                    unset($course_info['publish']);
                    unset($course_info['completion_percentage']);
                    unset($course_info['modules']);
                    unset($course_info['trial_units']);
                    unset($course_info['questionnaire_type']);
                    unset($course_info['closeness_questions']);
                    unset($course_info['conflict_questions']);
                    unset($course_info['all_questionnaire_questions']);
                    unset($course_info['score_1_text']);
                    unset($course_info['score_2_text']);
                    unset($course_info['score_3_text']);
                    unset($course_info['score_4_text']);
                    unset($course_info['score_5_text']);
                    unset($course_info['score_6_text']);
                    unset($course_info['score_7_text']);
                    unset($course_info['score_8_text']);
                    unset($course_info['language_meta']);
                    unset($course_info['parent_course']);
                    unset($course_info['transition_course']);
                    // Optionally, unset 'description' if it's not needed in minimal info mode
                    // unset($course_info['description']);
                }


                // echo '<pre>';
                // print_r($course_info);
                // echo '</pre>';
                // exit();
                if (empty($has_transitioned)) {
                    $results_list[] = $course_info;
                }

            }
            $transition_course = array();
        }

        // echo '<pre>';
        // print_r($results_list);
        // exit();

        if ($args['id'] || $args['title'] || $args['username_id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /**
     * @param $args
     * @return array|bool
     */
    public function get_category($args = array())
    {
        $params = [];
        $where = "";
        if (!empty($args['id'])) {
            $where .= " AND id = :id";
            $params['id'] = $args['id'];
        }

        if (!empty($args['usergroup'])) {
            $where .= "AND usergroup=:usergroup";
            $params['usergroup'] = $args['usergroup'];
        }

        $query = get_dbh()->prepare("SELECT id, db231806 as name, db231926 as tag FROM ols_course_categories WHERE 1 $where ORDER BY db231806 ASC");
        $query->execute($params);

        if (!empty($args['id'])) {
            return $query->fetch(PDO::FETCH_ASSOC);
        }
        return $query->fetchAll(PDO::FETCH_ASSOC);
    }


    /** ===================================
     * Update Course
     * ====================================    */
    function update_or_insert_course($args)
    {
        $db = new Db_helper();

        $course_info = array();

        if ($args['title']) {
            $course_info['db21909'] = $args['title'];
        }
        if (array_key_exists('alternative_title', $args)) {
            $course_info['db260924'] = $args['alternative_title'];
        }
        if (array_key_exists('course_link', $args)) {
            $course_info['db236705'] = $args['course_link'];
        }
        if (array_key_exists('colour', $args)) {
            $course_info['db21914'] = $args['colour'];
        }
        if (array_key_exists('pre_questions_form_id', $args)) {
            $course_info['db25550'] = $args['pre_questions_form_id'];
        }
        if (array_key_exists('post_questions_form_id', $args)) {
            $course_info['db25551'] = $args['post_questions_form_id'];
        }
        if (array_key_exists('group', $args)) {
            $course_info['db21929'] = $args['group'];
        }
        if (array_key_exists('category', $args)) {
            $course_info['db253415'] = $args['category'];
        }

        if (array_key_exists('course_level', $args)) {
            $course_info['db37364'] = $args['course_level'];
        }
        if (array_key_exists('excerpt', $args)) {
            $course_info['db21913'] = $args['excerpt'];
        }
        if (array_key_exists('next_unit_visibility', $args)) {
            $course_info['db21915'] = $args['next_unit_visibility'];
        }
        if (array_key_exists('description', $args)) {
            $course_info['db21910'] = $args['description'];
        }
        if (array_key_exists('featured', $args)) {
            $course_info['db25543'] = $args['featured'];
        }
        if (array_key_exists('show_support_on_each_unit', $args)) {
            $course_info['db21950'] = $args['show_support_on_each_unit'];
        }
        if (array_key_exists('show_feedback_on_each_unit', $args)) {
            $course_info['db66669'] = $args['show_feedback_on_each_unit'];
        }
        if (array_key_exists('publish', $args)) {
            $course_info['db30582'] = $args['publish'];
        }
        if (array_key_exists('rm_from_library', $args)) {
            $course_info['db309757'] = $args['rm_from_library'];
        }
        if (array_key_exists('course_language', $args)) {
            $course_info['db31066'] = $args['course_language'];
        }
        if (array_key_exists('order', $args)) {
            $course_info['db251258'] = $args['order'];
        }
        if (array_key_exists('parent_course', $args)) {
            $course_info['db253412'] = $args['parent_course'];
        }
        if (array_key_exists('download_certificate', $args)) {
            $course_info['db21940'] = $args['download_certificate'];
        }
        if (array_key_exists('questionnaire_type', $args)) {
            $course_info['db26246'] = $args['questionnaire_type'];
        }
        if (array_key_exists('closeness_questions', $args)) {
            $course_info['db26247'] = $args['closeness_questions'];
        }
        if (array_key_exists('conflict_questions', $args)) {
            $course_info['db26248'] = $args['conflict_questions'];
        }
        if (array_key_exists('score_1_text', $args)) {
            $course_info['db26238'] = $args['score_1_text'];
        }
        if (array_key_exists('score_2_text', $args)) {
            $course_info['db26239'] = $args['score_2_text'];
        }
        if (array_key_exists('score_3_text', $args)) {
            $course_info['db26240'] = $args['score_3_text'];
        }
        if (array_key_exists('score_4_text', $args)) {
            $course_info['db26241'] = $args['score_4_text'];
        }
        if (array_key_exists('score_5_text', $args)) {
            $course_info['db26242'] = $args['score_5_text'];
        }
        if (array_key_exists('score_6_text', $args)) {
            $course_info['db26243'] = $args['score_6_text'];
        }
        if (array_key_exists('score_7_text', $args)) {
            $course_info['db26244'] = $args['score_7_text'];
        }
        if (array_key_exists('score_8_text', $args)) {
            $course_info['db26245'] = $args['score_8_text'];
        }
        if (array_key_exists('text_before_questionnaire', $args)) {
            $course_info['db250853'] = $args['text_before_questionnaire'];
        }

        if (!empty($args['image'])) {
            $course_info['db253409'] = $args['image'];
        }

        if (!empty($args['thumbnail'])) {
            $course_info['db254762'] = $args['thumbnail'];
        }

        if (!empty($args['course_external_url'])) {
            $course_info['db358213']= $args['course_external_url'];
        }

        if ($args['id']) {
            $where = array('id' => $args['id']);
        } elseif ($args['username_id']) {
            $where = array('username_id' => $args['username_id']);
        }
        if ($args['action'] == "update") {
            $db->update('ols_online_courses', $course_info, $where);
            $entry_id = $args['id'];
        } else {
            $db->system_table_insert_or_update('ols_online_courses', $course_info);
            $entry_id = $db->lastInsertId();
            
        }


          //update  slug ensureing it ends with "-$entry_id" if "-$entry_id" already exists in slug do not add it
        if (!empty($args['course_slug']) && $entry_id) {
            $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $args['course_slug']), '-')) ;
            $slug = iconv('UTF-8', 'ASCII//TRANSLIT//IGNORE', $slug);
            if (!str_ends_with($slug, "-$entry_id")) {
                $slug .= "-$entry_id";
            }

            $dbh = get_dbh();
            $stmt = $dbh->prepare("UPDATE ols_online_courses SET db361083 = :slug WHERE id = :id");
            $stmt->execute([':slug' => $slug, ':id' => $entry_id]);
        }





         return $this->get_courses(array('id' => $entry_id));
    }


    /** ===================================
     * Update Plan
     * ====================================    */
    function update_or_insert_plan($args)
    {
        $db = new Db_helper();

        $plan_info = array();

        if (array_key_exists('title', $args)) {
            $plan_info['db21876'] = $args['title'];
        }
        if (array_key_exists('status', $args)) {
            $plan_info['db21877'] = $args['status'];
        }
        if (array_key_exists('publish', $args)) {
            $plan_info['db21879'] = $args['publish'];
        }
        if (array_key_exists('sponsor', $args)) {
            $plan_info['db21880'] = $args['sponsor'];
        }
        if (array_key_exists('sponsor_logo_on_certificate', $args)) {
            $plan_info['db25910'] = $args['sponsor_logo_on_certificate'];
        }
        if (array_key_exists('sponsor_logo', $args)) {
            $plan_info['db25911'] = $args['sponsor_logo'];
        }
        if (array_key_exists('access_code', $args)) {
            $plan_info['db21881'] = $args['access_code'];
        }
        if (array_key_exists('sponsor_to_verify_access', $args)) {
            $plan_info['db21882'] = $args['sponsor_to_verify_access'];
        }
        if (array_key_exists('notice', $args)) {
            $plan_info['db21883'] = $args['notice'];
        }
        if (array_key_exists('payment_plan', $args)) {
            $plan_info['db21884'] = $args['payment_plan'];
        }
        if (array_key_exists('members_allowed', $args)) {
            $plan_info['db21885'] = $args['members_allowed'];
        }
        if (array_key_exists('price', $args)) {
            $plan_info['db21886'] = $args['price'];
        }
        if (array_key_exists('include_vat', $args)) {
            $plan_info['db21887'] = $args['include_vat'];
        }
        if (array_key_exists('price_including_vat', $args)) {
            $plan_info['db21888'] = $args['price_including_vat'];
        }
        if (array_key_exists('bulk_discounts_available', $args)) {
            $plan_info['db21889'] = $args['bulk_discounts_available'];
        }
        if (array_key_exists('membership_number_prefix', $args)) {
            $plan_info['db21890'] = $args['membership_number_prefix'];
        }
        if (array_key_exists('permit_access_to_login', $args)) {
            $plan_info['db21891'] = $args['permit_access_to_login'];
        }
        if (array_key_exists('event_number_of_members', $args)) {
            $plan_info['db21892'] = $args['event_number_of_members'];
        }
        if (array_key_exists('admin_only_type', $args)) {
            $plan_info['db21893'] = $args['admin_only_type'];
        }
        if (array_key_exists('description', $args)) {
            $plan_info['db21904'] = $args['description'];
        }
        if (array_key_exists('access_plan_type', $args)) {
            $plan_info['db26817'] = $args['access_plan_type'];
        }


        // echo '<pre>';
        // print_r($args);ß
        // echo '</pre>';

        // echo '<pre>';
        // print_r($course_info);
        // echo '</pre>';
        // exit();

        if (array_key_exists('id', $args)) {
            $where['id'] = $args['id'];
            $db->update('ols_access_plans', $plan_info, $where);
            return $args['id'];
        } else {
            $db->system_table_insert_or_update('ols_access_plans', $plan_info);
            $new_course_id = $db->lastInsertId();
            return $new_course_id;
        }
    }


    /** ===================================
     * Record Unit view
     * ====================================    */
    function record_unit_view($args)
    {
        $db = new Db_helper();
        $dbh = get_dbh();
        $unit_id = $args['unit_id'];
        $student_id = $args['student_id'];
        $last_accessed = pull_field("ols_user_progress", "db22015", "WHERE rel_id = '$student_id' AND db22013='$unit_id'  AND (rec_archive is NULL OR rec_archive ='')");

        if ($last_accessed || $last_accessed != '') {
            //update
            $sql = "UPDATE ols_user_progress SET db22015 = '" . custom_date_and_time() . "' WHERE rel_id=? AND db22013=? AND (rec_archive is NULL OR rec_archive ='')"; //query
            $sth = $dbh->prepare($sql);
            $sth->execute(array($student_id, $unit_id));
        } else {
            //insert

            //Get the Course
            $course_id = $args['course_id'];
            //get the Module
            $module_id = $args['module_id'];
            //Get the access plan
            $access_plan_id = pull_field("ols_user_access_plan JOIN ols_access_plan_courses ON db22019 = ols_access_plan_courses.rel_id  AND db21911 ='$course_id'", "ols_access_plan_courses.rel_id", "WHERE ols_user_access_plan. rel_id  = '$student_id'");

            $sql = "INSERT INTO ols_user_progress (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id, db22013,db22015,db37394,db37395,db37396) VALUES ('" . random() . "', '" . session_info("uid") . "', '" . session_info("usergroup") . "', '" . $student_id . "','" . custom_date_and_time() . "', '" . session_info("uid") . "', '" . $unit_id . "', '" . custom_date_and_time() . "', '" . $access_plan_id . "', '" . $course_id . "', '" . $module_id . "')";
            $sth = $dbh->prepare($sql);
            $sth->execute();//query

        }
    }


    /** ===================================
     * Get Unit progress
     * ====================================    */
    function get_unit_progress($args = array())
    {

        $dbh = get_dbh();

        if (isset($args['unit_id'])) {
            $module_id_sql = "AND ols_user_progress.db22013='" . $args['unit_id'] . "'";
        }

        if (isset($args['student_id'])) {
            $user_id_sql = "AND ols_user_progress.rel_id='" . $args['student_id'] . "' AND ols_user_progress.rel_id!=''";
        }

        $query = "
			SELECT 
				*,
				ols_user_progress.rec_id as progress_user_id
			FROM
				ols_user_progress 
			WHERE
				1
				$module_id_sql
				$user_id_sql
				AND (rec_archive is null or rec_archive='')
			ORDER BY id ASC";


        $sth = $dbh->prepare($query);
        $sth->execute();
        $results_list = array();
        while ($row = $sth->fetch(PDO::FETCH_ASSOC)) {

            $entry = array(
                "unit_id" => $row['db22013'],
                "completed_date" => $row['db22014'],
                "last_accessed_date" => $row['db22015'],
            );
            $results_list[] = $entry;
        }

        return $results_list;

    }

    public function preferences($usergroup)
    {
        $dbh = get_dbh();
        $query="select * from ols_preferences WHERE usergroup='{$usergroup}' LIMIT 1";
        $sth=$dbh->prepare($query);
        dev_debug($query);
        $sth->execute();
        return $sth->fetch(PDO::FETCH_OBJ);
    }


    function proccess_short_codes($text)
    {
         //convert all &quot; and &nbsp;....
        $this->counter++;
        $escape_questionnaires = $this->is_api_instance;
        $text = str_replace('&quot;', '"', $text);
        $text = str_replace('&nbsp;', ' ', $text);
        $file = ABSPATH . 'app/libs/shortcodes/index.php';
        if (file_exists($file)) {
            require($file);
        }
        return $text;
    }


    /** ===================================
     * Get Units
     * ====================================    */
    function get_units($args = array())
    {
        $db = new Db_helper();


        if ($args['id']) {
            $id_sql = "AND ols_course_units.id='" . $args['id'] . "'";
        }

        if ($args['module_id']) {
            $module_id_sql = "AND ols_course_units.rel_id='" . $args['module_id'] . "'";
        }

        if ($args['school_id']) {
            $school_id_sql = "AND ols_course_units.usergroup='" . $args['school_id'] . "'";
        }

        if ($args['username_id']) {
            $username_id_sql = "AND ols_course_units.username_id='" . $args['username_id'] . "'";
        }

        if ($args['show_hidden_units']) {
            $hide_sql = "";
        } else {
            $hide_sql = "AND ols_course_units.db21973 !='hidden'";
        }
        if ($args['trial']) {
            $trial_sql = "AND ols_course_units.db21959='1'";
        }

        $query = "
			SELECT 
				*,
				ols_course_units.id as unit_id,
				ols_course_units.rel_id as module_id,
				ols_course_units.username_id as unit_username_id
				$progress_fields
			FROM
				ols_course_units 
				$progress_join
			WHERE
				1
				$id_sql
				$module_id_sql
				$school_id_sql
				$search_sql
				$user_id_sql
				$username_id_sql
				$hide_sql
				$trial_sql
				AND (rec_archive is null or rec_archive='')
			ORDER BY cast(db21939 as unsigned), ols_course_units.id ASC";

        $result = $db->query($query);

        //get the first unit module id
        $first_module_id = $result[0]['module_id'];
        if ($first_module_id) {
            $query2 = "
					SELECT
						ols_online_courses.id as course_id,
                        ols_online_courses.db21909,
						ols_course_modules.id as module_id,
                        ols_course_modules.db21919
					FROM
						ols_course_modules 
					LEFT JOIN ols_online_courses ON ols_course_modules.rel_id = ols_online_courses.id
					WHERE
						ols_course_modules.id='" . $first_module_id . "'
					LIMIT 1";
            $main_info = $db->query($query2);
            $main_info = $main_info[0];
        }


        //Get Module Progress
        if ($args['student_id']) {
            $progress_args = array('module_id' => $args['module_id'], 'student_id' => $args['student_id']);
            $progress = $this->get_unit_progress($progress_args);
        } elseif ($_SESSION['student_id']) {
            $progress_args = array('module_id' => $args['module_id'], 'student_id' => $_SESSION['student_id']);
            $progress = $this->get_unit_progress($progress_args);
        }
        $i = 0;
        foreach ($result as $row) {

            //Status Title
            if ($row['db21972'] == "publish") {
                $status_title = "publish";
            } else if ($row['db21972'] == "pending") {
                $status_title = "Pending Review";
            } else if ($row['db21972'] == "draft") {
                $status_title = "Draft";
            }

            //get unit progress
            $progress_info = array();
            foreach ($progress as $pg) {
                if ($pg['unit_id'] == $row['unit_id']) {
                    $progress_info = $pg;
                }
            }
            $quiz = "";
            $responses_list = "";
            $correct_response_count = 0;
            if ($args['no_quiz_information'] != "true") {
                if ($row['db24588']) {
                    $quiz_form = $row['db24588'];
                    $form_templates = new FormTemplates;
                    $page_args = array("id" => $quiz_form, "system_abbrv" => 'inst');
                    $form = $form_templates->get($page_args);

                    if ($form['table_name']) {
                        $quiz = array(
                            'id' => $row['db24588'],
                            'title' => $form['title'],
                            'questions' => $form['fields'],
                        );
                    }
                    if ($args['student_id'] && $form['table_name']) {
                        $table_exists = pull_field($form['table_name'], "1");
                        if ($table_exists) {
                            $answers = $form_templates->answers(array('form_name' => $form['table_name'], 'rel_id' => $args['student_id'], 'single_answer' => true));
                        } else {
                            $answers = [];
                        }
                        $responses_list = array();
                        if (count($answers)) {
                            foreach ($form['fields'] as $field) {
                                if (str_replace(' ', '_', $field['answer']) == str_replace(' ', '_', $answers[$field['db_field_name']])) {
                                    $correct_response = true;
                                    $correct_response_count++;
                                } else {
                                    $correct_response = false;
                                }
                                $responses_list[] = array(
                                    'question' => $field['title'],
                                    'answer' => str_replace('_', ' ', $field['answer'])/*$field['answer']*/,
                                    'answer_hint' => $field['answer_hint'],
                                    'answer_reason' => $field['answer_reason'],
                                    'incorrect_reason' => $field['incorrect_reason'],
                                    'response' => str_replace('_', ' ', $answers[$field['db_field_name']])/*$answers[$field['db_field_name']]*/,
                                    'correct' => $correct_response
                                );
                            }
                        }
                    }
                }

                $quiz_results = "";
                if ($responses_list != "") {
                    $total_questions = count($responses_list);
                    $total_correct_percentage = number_format(($correct_response_count / $total_questions) * 100, 0);
                    $quiz_results = array(
                        'total_questions' => $total_questions,
                        'correct_responses' => $correct_response_count,
                        'percentage' => $total_correct_percentage
                    );
                }
            }

            $unit_info = array(
                "id" => $row['unit_id'],
                'unit_slug' => $this->generate_unique_slug($row['db21923'], 'ols_course_units', 'db361093', $row['unit_id'] ?? null),
                "module_id" => intval($row['module_id']),
                'module_slug' => $this->generate_unique_slug($main_info['db21919'], 'ols_online_courses', 'db360683', $main_info['module_id'] ?? null),
                "course_id" => intval($main_info['course_id']),
                'course_slug' => $this->generate_unique_slug($main_info['db21909'], 'ols_online_courses', 'db361083', $main_info['course_id'] ?? null),
                "quiz" => $quiz,
                'form' => $form,
                "quiz_response" => $responses_list,
                "quiz_results" => $quiz_results,
                "quiz_mandetory" => $row['db25513'],
                "questionnaire_form_id" => $row['db25552'],
                "questionnaire_mandetory" => $row['db25553'],
                "quiz_answerable_multiple_times" => $row['db41795'],
                "show_user_answer" => $row['db41796'],
                "username_id" => $row['unit_username_id'],
                "completed_date" => $progress_info['completed_date'],
                "last_accessed_date" => $progress_info['last_accessed_date'],
                "title" => $row['db21923'],
                "trail" => $row['db21959'],
                "mp3_file" => $row['db21925'],
                "mp3_auto_play" => $row['db21942'],
                "status" => $row['db21972'],
                "status_title" => $status_title,
                "visibility" => empty($row['db21973']) ?'publish':$row['db21973'],
                "publish_date" => $row['db21974'],
                "order" => intval(empty($row['db21939']) ? $i : $row['db21939']),
                "progress_certificate" => $row['db66584'],
                "progress_certificate_title" => $row['db66585'],

                "href" => $this->home_url("course/") . $this->ensure_slug_matches_title($main_info['course_id'], 'course')  . "/" . $this->ensure_slug_matches_title($main_info['module_id'], 'module')  . "/" . $this->ensure_slug_matches_title($row['unit_id'], 'unit') ,
                "slug_href"=>""
            );

            if (!empty($unit_info['unit_slug'])&&!empty($unit_info['module_slug'])&&!empty($unit_info['course_slug'])) {
                $unit_info["slug_href"]=$this->home_url("course-v4/" . $unit_info['course_slug'] . "/" . $unit_info['module_slug'] . "/" . $unit_info['unit_slug']);
            }




                        $i++;
            if (empty($args['no_unit_description'])) {
                if ($args['raw_html']) {
                    $unit_info['description'] = $row['db21924'];
                } else {
                    if (defined("ACTIVE_UNIT") && ACTIVE_UNIT == $row['unit_id']) {
                        $unit_info['description'] = $this->proccess_short_codes($row['db21924']);
                        //$unit_info['description'] = $row['db21924'];
                    } else {
                        $unit_info['description'] = $row['db21924'];
                    }
                }
            }

            if (!empty($args['info_for_notes'])) {
                $unit_info=[
                   "id" => $row['unit_id'],
                   "title" => $row['db21923'],
                   "href" => $this->home_url("course/") . $this->ensure_slug_matches_title($main_info['course_id'] , 'course') . "/" . $this->ensure_slug_matches_title($main_info['module_id'] , 'module') . "/" . $this->ensure_slug_matches_title($row['unit_id'], 'unit') ,
                ];
            }

            $results_list[] = $unit_info;

        }

        if ($args['id'] || $args['title'] || $args['username_id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    function get_pre_questions_answers($args = array())
    {
        $form_templates = new FormTemplates;

        $answers = $form_templates->answers(array('form_name' => 'course_1_pre_questions', 'rel_id' => $args['student_id'], 'single_answer' => true));
        $responses_list = array();
        if (count($answers)) {
            foreach ($form['fields'] as $field) {
                if ($field['answer'] == $answers[$field['db_field_name']]) {
                    $correct_response = true;
                    $correct_response_count++;
                } else {
                    $correct_response = false;
                }
                $responses_list[] = array(
                    'question' => $field['title'],
                    'answer' => $field['answer'],
                    'response' => $answers[$field['db_field_name']],
                    'correct' => $correct_response
                );
            }
        }
    }
    /** ===================================
     * Get Learner supplementary answers
     * ====================================    */
    /*	function get_access_plan_learners_supp_answers ($args=array()){
		$db = new Db_helper();
		$results_list = array();
		if($args['access_plan']){
			$access_plan_id_sql = "AND ols_access_plans.id='".$args['access_plan']."'";
		}
		$access_plan_id = $args['access_plan'];
		if ($access_plan_id) {
			$query = "SELECT
				ols_user_access_plan.rel_id
				FROM ols_user_access_plan INNER JOIN core_students ON core_students.id = ols_user_access_plan.rel_id WHERE $access_plan_id = db22019 AND db22031='2'  AND (core_students.rec_archive is null or core_students.rec_archive= '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND ols_user_access_plan.usergroup=$_SESSION[usergroup]";

			dev_debug($query);
			$result = $db->query($query);
			foreach ($result as $row) {

				$supplementary_questions_args = array('access_plan_id'=>$access_plan_id, 'student_id'=>$row['rel_id']);
				$supplementary_questions = $this->get_supplementary_question($supplementary_questions_args);
				$results_list[] =  array(
					"access_plan_id"=>$access_plan_id,
					"learner_id"=>$row['id'],
					"supplementary_questions"=>$supplementary_questions,
				);
			}

		}
		if($args['id'] || $args['title'] || $args['username_id']){
			return $results_list[0];
		}else{
			return $results_list;
		}

	}
*/
    /** ===================================
     * Get Learner supplementary answers
     * ====================================    */
    function get_access_plan_learners_supp_answers($args = array())
    {
        $db = new Db_helper();
        $dbh = get_dbh();
        $results_list = array();
        if ($args['access_plan']) {
            $access_plan_id_sql = "AND ols_access_plans.id='" . $args['access_plan'] . "'";
        }
        $access_plan_id = $args['access_plan'];
        if ($access_plan_id) {


            $query_questions = "
			SELECT 
				*,
				ols_supplementary_questions.id as question_id
			FROM
				ols_supplementary_questions 
			WHERE
				1
				AND ols_supplementary_questions.rel_id='$access_plan_id'
		  
				AND (ols_supplementary_questions.rec_archive is null or ols_supplementary_questions.rec_archive='')
			ORDER BY db21897 ASC";
            dev_debug($query_questions);
            $result_questions = $db->query($query_questions);
            //$sth_questions = $dbh->prepare($query_questions);
            //$sth_questions->execute();
            //$results_list = array();
            foreach ($result_questions as $entry) {

                $supplementary_questions = array();
                $query = "SELECT
				ols_user_access_plan.rel_id, (SELECT db22026 FROM ols_user_supp_answers WHERE rel_id=ols_user_access_plan.rel_id AND db22027='$access_plan_id' AND db22025='$entry[question_id]' ORDER BY id DESC LIMIT 1) as 'user_answer'
				FROM ols_user_access_plan INNER JOIN core_students ON core_students.id = ols_user_access_plan.rel_id WHERE '$access_plan_id' = db22019 AND db22031='2'  AND (core_students.rec_archive is null or core_students.rec_archive= '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND ols_user_access_plan.usergroup='$_SESSION[usergroup]'";

                dev_debug($query);
                $result = $db->query($query);
                foreach ($result as $row) {

                    //$user_answer = pull_field("ols_user_supp_answers", "db22026", "WHERE rel_id='$row[rel_id]' AND db22027='$access_plan_id' AND db22025='$entry[id]'");

                    $user_answer = trim($row['user_answer']);
                    $my_entry = array(
                        'id' => $entry['question_id'],
                        'title' => $entry['db21895'],
                        'type' => $entry['db21896'],
                        'order' => $entry['db21897'],
                        'required' => $entry['db22024'],
                        'answers' => $entry['db25608'],
                        'user_answer' => $user_answer,
                    );
                    //dev_debug ("ADDING $entry[db21895] ** $user_answer **FOR** $access_plan_id User $row[id]");
                    $supplementary_questions[] = $my_entry;

                }
                $results_list[] = array(
                    "access_plan_id" => $access_plan_id,
                    "learner_id" => $row['rel_id'],
                    "supplementary_questions" => $supplementary_questions,
                );

            }

        }
        if ($args['id'] || $args['title'] || $args['username_id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }

    }

    /** ===================================
     * Get Plans
     * ====================================    */
    function get_plans($args = array())
    {
        $db = new Db_helper();
        $scheduled_courses = [];
        $challenge_questions = [];
        // echo "22";

        $vat_percent = pull_field("lead_preferences", "db15030", "WHERE usergroup = '" . $_SESSION['usergroup'] . "'");

        if (!isset($vat_percent)) {
            $vat_percent = 0;
        }
        if ($args['id']) {
            $id_sql = "AND ols_access_plans.id='" . $args['id'] . "'";
        }
        if ($args['ids']) {
            $id_sql = "AND ols_access_plans.id IN (" . $args['ids'] . ")";
        }
        if ($args['free']) {
            $free_sql = "AND (ols_access_plans.db21886 IS NULL OR ols_access_plans.db21886='' OR ols_access_plans.db21886='0' OR ols_access_plans.db21886='0.00')";
        }
        if ($args['sponsor_id']) {
            $sponsor_id_sql = "AND ols_access_plans.db21880='" . $args['sponsor_id'] . "'";
        }

        if ($args['module_id']) {
            $module_id_sql = "AND ols_access_plans.rel_id='" . $args['module_id'] . "'";
        }

        if ($args['school_id']) {
            $school_id_sql = "AND ols_access_plans.usergroup='" . $args['school_id'] . "'";
        }

        if ($args['count']) {
            $select_sql = "count(*) as total_count ";
        } else {
            $select_sql = "*, ols_access_plans.id as access_plan_id";
        }

        if ($args['username_id']) {
            $username_id_sql = "AND ols_access_plans.username_id='" . $args['username_id'] . "'";
        }

        if ($args['access_code']) {
            //$access_code_sql = "AND ols_access_plans.db21881='".$args['access_code']."'";
            $access_code_sql = "AND FIND_IN_SET ('$args[access_code]',ols_access_plans.db21881)";
        }

        if ($args['student_id']) {
            $this_student_id = $args['student_id'];
        }

        if ($args['dont_show_hidden_plans']) {
            $hidden_plans_sql = "AND ols_access_plans.db21881=''";
        }
        if ($args['access_plan_level']) {
            $access_plan_level_id_sql = "AND ols_access_plans.db26817 IN (" . $args['access_plan_level'] . ")";
        }
        if ($args['student_id']) {
            if (isset($args['username_id']) && $args['username_id'] != '') {
                //student could be logged in but not access this plan yet so show him new screen
                $access_plan_id = pull_field("ols_access_plans", "id", "WHERE username_id = '" . $args['username_id'] . "'");
                $query_check = pull_field("ols_user_access_plan", "COUNT(*)", "WHERE (rec_archive IS NULL OR rec_archive ='') AND rel_id = '$args[student_id]' AND db22019 = '$access_plan_id'");

                if ($query_check > 0) {
                    $query =
                        "SELECT
							Distinct ols_access_plans.* 
							, ols_access_plans.id as access_plan_id
							, ols_user_access_plan.id as user_access_plan_id
						FROM
							ols_access_plans 
							LEFT JOIN ols_user_access_plan ON ols_user_access_plan.db22019 = ols_access_plans.id 
							LEFT JOIN ols_access_plan_status ON ols_access_plans.db21877 = ols_access_plan_status.id
						WHERE
							1
							AND ols_user_access_plan.rel_id = '$args[student_id]'
							AND (ols_access_plans.rec_archive IS NULL OR ols_access_plans.rec_archive = '')
							AND (ols_user_access_plan.rec_archive IS NULL OR ols_user_access_plan.rec_archive ='')
							$id_sql
							$module_id_sql
							$free_sql
							$school_id_sql
							$sponsor_id_sql
							$search_sql
							$access_code_sql
							$username_id_sql
							$hidden_plans_sql
							$access_plan_level_id_sql
							
							ORDER BY db21876,ols_access_plans.id ASC";
                } else {
                    $query = "
						SELECT 
							$select_sql
							,ols_access_plans.username_id as username_id
						FROM
							ols_access_plans
							LEFT JOIN ols_access_plan_status ON ols_access_plans.db21877 = ols_access_plan_status.id
							LEFT JOIN ols_sponsors ON ols_access_plans.db21880 = ols_sponsors.id
						WHERE
							1
							$id_sql
							$module_id_sql
							$free_sql
							$school_id_sql
							$sponsor_id_sql
							$search_sql
							$access_code_sql
							$username_id_sql
							$hidden_plans_sql
							$access_plan_level_id_sql
							AND (ols_access_plans.rec_archive IS NULL OR ols_access_plans.rec_archive = '')
							AND (db21877 = '2')
						ORDER BY db21876,ols_access_plans.id ASC";
                }
            } else {
                $query =
                    "SELECT
							Distinct ols_access_plans.* 
							, ols_access_plans.id as access_plan_id
							, ols_user_access_plan.id as user_access_plan_id
						FROM
							ols_access_plans 
							LEFT JOIN ols_user_access_plan ON ols_user_access_plan.db22019 = ols_access_plans.id 
							LEFT JOIN ols_access_plan_status ON ols_access_plans.db21877 = ols_access_plan_status.id
						WHERE
							1
							AND ols_user_access_plan.rel_id = '$args[student_id]'
							AND (ols_access_plans.rec_archive IS NULL OR ols_access_plans.rec_archive = '')
							AND (ols_user_access_plan.rec_archive IS NULL OR ols_user_access_plan.rec_archive ='')
							AND db22031 ='2'
							$id_sql
							$module_id_sql
							$free_sql
							$school_id_sql
							$sponsor_id_sql
							$search_sql
							$access_code_sql
							$username_id_sql
							$hidden_plans_sql
							$access_plan_level_id_sql
							ORDER BY db21876,ols_access_plans.id ASC";

            }
        } else if ($args['course']) {
            $query = "
				SELECT
					Distinct ols_access_plans.* 
					, ols_access_plans.id as access_plan_id
				FROM 
					ols_access_plans 
					LEFT JOIN ols_access_plan_courses ON ols_access_plan_courses.rel_id = ols_access_plans.id 
					LEFT JOIN ols_access_plan_status ON ols_access_plans.db21877 = ols_access_plan_status.id
                    INNER JOIN ols_online_courses ON ols_online_courses.id=db21911 and ols_online_courses.username_id='$args[course]'
				WHERE 
					/*'$args[course]' IN (SELECT username_id from ols_online_courses where id = db21911)*/
				    (db21881 is null or db21881 = '')
					AND db21879='yes'
					AND (ols_access_plans.rec_archive IS NULL OR ols_access_plans.rec_archive = '')
					AND (ols_access_plan_courses.rec_archive IS NULL OR ols_access_plan_courses.rec_archive ='')
					AND (db21877 = '2')
					$access_plan_level_id_sql
					$free_sql
				ORDER BY db21876,ols_access_plans.id ASC";
        } else if ($args['course_id']) {
            $query = "
				SELECT
					Distinct ols_access_plans.* 
					, ols_access_plans.id as access_plan_id
				FROM 
					ols_access_plans 
				WHERE 
					$args[course_id] IN (SELECT group_concat(db21911) from ols_access_plan_courses where ols_access_plan_courses.rel_id = ols_access_plans.id)
					AND (ols_access_plans.rec_archive IS NULL OR ols_access_plans.rec_archive = '')
					AND (db21877 = '2')
					$access_plan_level_id_sql
					$free_sql
					$sponsor_id_sql
				ORDER BY db21876,ols_access_plans.id ASC";
        } else {
            $query = "
			SELECT 
				$select_sql
				,ols_access_plans.username_id as username_id
			FROM
				ols_access_plans
				LEFT JOIN ols_access_plan_status ON ols_access_plans.db21877 = ols_access_plan_status.id
				LEFT JOIN ols_sponsors ON ols_access_plans.db21880 = ols_sponsors.id
			WHERE
				1
				$id_sql
				$module_id_sql
				$free_sql
				$school_id_sql
				$sponsor_id_sql
				$search_sql
				$access_code_sql
				$username_id_sql
				$hidden_plans_sql
				$access_plan_level_id_sql
				AND (ols_access_plans.rec_archive IS NULL OR ols_access_plans.rec_archive = '')
				AND (db21877 = '2')
			ORDER BY db21876,ols_access_plans.id ASC";

        }
        // echo '<pre>';
        // print_r($query);
        // echo '</pre>';
        // exit();
// comment
        dev_debug("Query to select access plan ids: " . $query);
        //error_log("Query to select access plan ids: " . $query);
        $result = $db->query($query);
        $sql_query=$query;
        foreach ($result as $row) {

            if ($args['count']) {
                $results_list = $row['total_count'];
            } else {
                if ($args['completion_stage_required']) {
                    $access_plan_courses_args = array('access_plan' => $row['access_plan_id'], 'no_unit_description' => $args['no_unit_description'], 'raw_html' => $args['raw_html'], 'course_stats_required' => $args['course_stats_required'], "completion_stage_required" => $args['completion_stage_required']);
                } else if ($args['summary_stage_required']) {
                    $access_plan_courses_args = array('access_plan' => $row['access_plan_id'], 'no_unit_description' => $args['no_unit_description'], 'raw_html' => $args['raw_html'], 'course_stats_required' => $args['course_stats_required'], "summary_stage_required" => $args['summary_stage_required']);
                } else if (!($args['plans_only'])) {
                    $access_plan_courses_args = array('access_plan' => $row['access_plan_id'], 'no_unit_description' => $args['no_unit_description'], 'raw_html' => $args['raw_html'], 'course_stats_required' => $args['course_stats_required'], 'cumulative_learners_required' => $args['cumulative_learners_required']);
                }
                if (!empty($args["summary_only"])) {
                    $access_plan_courses_args['summary_only'] = true;
                }
                if (array_key_exists('transitioned_courses', $args)) {
                    $access_plan_courses_args['transitioned_courses'] = "true";
                }
                dev_debug("ACCESS PLAN ID " . $row['access_plan_id']);
                if (!($args['plans_only'])) {
                    $access_plan_courses = $this->get_access_plan_courses($access_plan_courses_args);
                    if ($row['db26817'] == 1) {
                        $scheduled_courses = $this->get_access_plan_scheduled_courses(['access_plan' => $row['access_plan_id']]);
                    }
                }
                if ($args["supplementary_questions_only"]) {
                    if ($args['student_id']) {
                        $supplementary_questions_args = array('access_plan_id' => $row['access_plan_id'], 'student_id' => $this_student_id);
                    } else {
                        $supplementary_questions_args = array('access_plan_id' => $row['access_plan_id']);
                    }
                    $supplementary_questions = $this->get_supplementary_question($supplementary_questions_args);
                    if ($args['sponsor_id']) {

                        $access_plan_learner_supp_questions_args = array('access_plan' => $row['access_plan_id']);
                        $access_plan_learner_supp_questions = $this->get_access_plan_learners_supp_answers($access_plan_learner_supp_questions_args);
                    }
                }
                if (!$args["summary_only"]) {
                    if ($args['student_id']) {
                        $supplementary_questions_args = array('access_plan_id' => $row['access_plan_id'], 'student_id' => $this_student_id);
                    } else {
                        $supplementary_questions_args = array('access_plan_id' => $row['access_plan_id']);
                    }
                    $supplementary_questions = $this->get_supplementary_question($supplementary_questions_args);

                    if (isset($args['student_id'])) {
                        $challenge_questions_args = array('access_plan_id' => $row['access_plan_id'], 'student_id' => $this_student_id);

                    } else {
                        $challenge_questions_args = array('access_plan_id' => $row['access_plan_id']);
                    }

                    $challenge_questions = $this->get_challenge_questions($challenge_questions_args);
                    $user_access_plan_status = '';
                    $user_access_plan_id = '';
                    if (isset($this_student_id) && isset($row['user_access_plan_id']) && $row['user_access_plan_id'] != '') {
                        $user_access_plan_status = pull_field("ols_user_access_plan", "db22031", "WHERE id =$row[user_access_plan_id]");
                        $user_access_plan_id = $row['user_access_plan_id'];
                        $user_supplementary_answers = $this->get_supplementary_answers(array("student_id" => $this_student_id, "access_plan_id" => $row['access_plan_id']));
                        $user_challenge_answers = $this->get_user_challenge_answers(array("student_id" => $this_student_id, "access_plan_id" => $row['access_plan_id']));
                    }
                    $coupons = [];
                    if (empty($args['hide_coupons'])) {
                        $coupons = $this->get_coupons(array('plan_id' => $row['access_plan_id']));
                    }
                    $gross_price = $row['db21886'];
                    if ($row['db21887'] == 'yes') {
                        $gross_price = round($gross_price + ($gross_price * $vat_percent) / 100, 2);
                    }
                    $supp_ans_valid = "";
                    if (isset($row['user_access_plan_id'])) {
                        $supp_ans_valid = pull_field("ols_user_access_plan", "db22056", "WHERE id =$row[user_access_plan_id]");
                    }

                    $chal_ans_valid = "";
                    if (isset($row['user_access_plan_id'])) {
                        $chal_ans_valid = pull_field("ols_user_access_plan", "db22057", "WHERE id =$row[user_access_plan_id]");
                    }

                    $sponsor_validity = "";
                    if (isset($row['user_access_plan_id'])) {
                        $sponsor_validity = pull_field("ols_user_access_plan", "db22058", "WHERE id =$row[user_access_plan_id]");
                    }
                    if ($args['sponsor_id']) {

                        $access_plan_learner_supp_questions_args = array('access_plan' => $row['access_plan_id']);
                        $access_plan_learner_supp_questions = $this->get_access_plan_learners_supp_answers($access_plan_learner_supp_questions_args);
                    }

                }
                $number_of_registered_users = pull_field("ols_user_access_plan INNER JOIN core_students ON core_students.id = CAST(ols_user_access_plan.rel_id AS UNSIGNED)", "count(distinct(ols_user_access_plan.rel_id))", "WHERE db22019 ='$row[access_plan_id]' AND db22031='2' AND (core_students.rec_archive is null or core_students.rec_archive= '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '')");

                if ($args['cumulative_learners_required']) {
                    if (!empty($args['active_learners_only'])) {
                        $active_learners_by_month = $this->get_monthly_active_learners(array('access_plan_id' => $row['access_plan_id'], 'school_id' => $args['school_id']));
                    } else {
                        $active_learners_by_month = $this->get_monthly_active_learners(array('access_plan_id' => $row['access_plan_id'], 'school_id' => $args['school_id']));
                        $registered_learners_by_month = $this->get_monthly_registered_learners(array('access_plan_id' => $row['access_plan_id'], 'school_id' => $args['school_id']));
                    }
                } else {
                    $registered_learners_by_month = array();
                }
                $rno = rand(1000, 99999);
                $results_list[] = array(
                    "id" => $row['access_plan_id'],
                    "module_id" => $row['rel_id'],
                    "username_id" => $row['username_id'],
                    "title" => $row['db21876'],
                    "status" => $this->get_statuses(array('id' => $row['db21877'])),
                    "publish" => $row['db21879'],
                    "captcha" => ["rno" => $rno, "url" => website_url . "/reg_inc_pngimg.php?rno=" . $rno, "verify" => md5($rno)],
                    "sponsor" => array(
                        "id" => $row['db21880'],
                        "company_name" => $row['db21861'],
                    ),
                    "access_code" => $row['db21881'],
                    "access_plan_language" => $row['db41053'],
                    "sponsor_to_verify_access" => $row['db21882'],
                    "sponsor_logo_on_certificate" => $row['db21882'],
                    "notice" => $row['db21883'],
                    "payment_plan" => $row['db21884'],
                    "members_allowed" => $row['db21885'],
                    "price" => $row['db21886'],
                    "coupons" => $coupons,
                    "include_vat" => $row['db21887'],
                    "price_including_vat" => $gross_price,
                    "vat_percent" => $vat_percent,
                    "bulk_discounts_available" => $row['db21889'],
                    "membership_number_prefix" => $row['db21890'],
                    "permit_access_to_login" => $row['db21891'],
                    "event_number_of_members" => $row['db21892'],
                    "admin_only_type" => $row['db21893'],
                    "description" => $row['db21904'],
                    "access_plan_type" => $row['db26817'],
                    "courses" => $access_plan_courses,
                    "scheduled_courses" => $scheduled_courses,
                    "supplementary_questions" => $supplementary_questions,
                    "user_supplementary_answers" => $user_supplementary_answers,
                    "challenge_questions" => $challenge_questions,
                    "user_challenge_answers" => $user_challenge_answers,
                    "user_supplementary_validity" => $supp_ans_valid,
                    "user_challenge_validity" => $chal_ans_valid,
                    "user_sponsor_validity" => $sponsor_validity,
                    "user_access_plan_status" => $user_access_plan_status,
                    "user_access_plan_id" => $user_access_plan_id,
                    "access_plan_learner_supp_answers" => $access_plan_learner_supp_questions,
                    "monthly_registered_learners" => $registered_learners_by_month,
                    "monthly_active_learners" => $active_learners_by_month,
                    "no_of_registered_users" => $number_of_registered_users,
                    //"sql_query"=>$sql_query

                );
            }
        }

        if (($args['id'] && !($args['plan_id_set'])) || $args['title'] || $args['username_id'] || $args['access_code']) {
            return $results_list[0];
        } else {
            return $results_list;
        }

    }

    /** ===================================
     * Get Access Plan Courses
     * ====================================    */
    function get_access_plan_courses($args = array())
    {
        $db = new Db_helper();
        $dbh = get_dbh();
        $id_sql = '';
        $access_plan_sql = '';
        $school_id_sql = '';
        $username_id_sql = '';
        $transitioned_courses_sql = '';

        if (!empty($args['id'])) {
            $id_sql = "AND ols_access_plan_courses.id='" . $args['id'] . "'";
        }

        if (!array_key_exists('transitioned_courses', $args)) {
            $transitioned_courses_sql = "AND (ols_access_plan_courses.db262370 IS NULL OR ols_access_plan_courses.db262370 ='active')";
        }


        if (!empty($args['access_plan'])) {
            $access_plan_sql = "AND ols_access_plan_courses.rel_id='" . $args['access_plan'] . "'";
        }

        if (!empty($args['school_id'])) {
            $school_id_sql = "AND ols_access_plan_courses.usergroup='" . $args['school_id'] . "'";
        } else {
            if (isset($_SESSION['usergroup'])) {
                $school_id_sql = "AND ols_access_plan_courses.usergroup='" . $_SESSION['usergroup'] . "'";
            }
        }

        if (!empty($args['username_id'])) {
            $username_id_sql = "AND ols_access_plan_courses.username_id='" . $args['username_id'] . "'";
        }

        $query = "
			SELECT 
				*,ols_access_plan_courses.rel_id as 'apc_rel_id'
			FROM
				ols_access_plan_courses 
				LEFT JOIN ols_online_courses ON ols_online_courses.id=CAST(ols_access_plan_courses.db21911 AS UNSIGNED)
			WHERE
				1
				$id_sql
				$access_plan_sql
				$school_id_sql
				$username_id_sql
				$transitioned_courses_sql
				AND (ols_access_plan_courses.rec_archive is null or ols_access_plan_courses.rec_archive='')
			ORDER BY db251258 ASC";
        // $_GET['debug_mode']="yes";
        dev_debug($query);
        // $_GET['debug_mode']="no";
        // echo '<pre>';
        // print_r($query);
        // echo '</pre>';
        // exit();

        //$sql_topass=$query;
        $sth_courses = $dbh->prepare($query);
        $sth_courses->execute();
        $results_list = array();
        $courses_count = $sth_courses->rowCount();
        dev_debug("COUNT of Courses " . $courses_count);

        if (!empty($args['course_stats_required'])) {
            //get all post questionnaire responses on courses linked to a plan
            $all_post_questionnaire_responses = $this->get_post_ques_responses_by_course($args);
        }
        if (!empty($args['completion_stage_required']) || !empty($args['summary_stage_required'])) {
        $no_of_registered_users = pull_field("ols_user_access_plan JOIN core_students ON core_students.id = ols_user_access_plan.rel_id", "count(DISTINCT(ols_user_access_plan.rel_id))", "WHERE (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '') AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND db22019 = '$row[apc_rel_id]' AND db22031='2'");
        $no_of_registered_users_last_month = pull_field("ols_user_access_plan INNER JOIN core_students ON core_students.id = ols_user_access_plan.rel_id", "count(distinct(ols_user_access_plan.rel_id))", "WHERE db22019 = '$row[apc_rel_id]' AND db22031='2' AND (core_students.rec_archive is null or core_students.rec_archive= '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND ols_user_access_plan.date >='$date_search_start' and ols_user_access_plan.date <'$date_search_end' ");
        }


        while ($row = $sth_courses->fetch(PDO::FETCH_ASSOC)) {
            //$no_of_registered_users = 0;
            //$no_of_registered_users_last_month = 0;
            $no_learners_started = 0;
            $avg_units_completed_per_learner = 0;
            $number_units_in_course = 0;
            $no_units_completed_greater_than_70 = 0;
            $no_units_completed_between_51_and_69 = 0;
            $no_units_completed_between_10_and_50 = 0;
            $no_units_completed_less_than_10 = 0;

            $total_post_questionnaire = 0;
            $yes_post_questionnaire = 0;
            $found_course_helpful_post_questionnaire = 0;
            dev_debug("course_stats_required" . $args['course_stats_required']);
            if ($args['course_stats_required']) {
                //AFY 4/12/24 same for all courses $no_of_registered_users = pull_field("ols_user_access_plan JOIN core_students ON core_students.id = ols_user_access_plan.rel_id", "count(DISTINCT(ols_user_access_plan.rel_id))", "WHERE (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '') AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND db22019 = '$row[apc_rel_id]' AND db22031='2'");
                dev_debug("SELECT count(DISTINCT(ols_user_access_plan.rel_id)) FROM ols_user_access_plan JOIN core_students ON core_students.id = ols_user_access_plan.rel_id WHERE (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '') AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND db22019 = '$row[apc_rel_id]' AND db22031='2'");

                list($no_learners_started, $avg_units_completed_per_learner) = explode(',', pull_field("ols_user_progress INNER JOIN core_students ON core_students.id = ols_user_progress.rel_id", "CONCAT(count(DISTINCT(ols_user_progress.rel_id)),',',count(*) DIV count(DISTINCT(ols_user_progress.rel_id)))", "WHERE ols_user_progress.db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND ols_user_progress.db37394 ='$row[apc_rel_id]' AND (db37395 = '$row[db21911]')"));

                //dev_debug("SELECT CONCAT(count(DISTINCT(ols_user_progress.rel_id)),',',count(*) DIV count(DISTINCT(ols_user_progress.rel_id))) FROM ols_user_progress INNER JOIN core_students ON core_students.id = ols_user_progress.rel_id LEFT JOIN ols_course_units on ols_user_progress.db22013 = ols_course_units.id LEFT JOIN ols_course_modules on ols_course_units.rel_id = ols_course_modules.id WHERE ols_user_progress.db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND ols_user_progress.db37394 =$row[apc_rel_id] AND ols_user_progress.db37394 IN (SELECT ols_user_access_plan.db22019  FROM ols_user_access_plan WHERE ols_user_access_plan.db22031='2' AND (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '')) AND (ols_course_modules.rel_id = '$row[db21911]')");

                //$avg_units_completed_per_learner = pull_field("ols_user_progress JOIN core_students ON core_students.id = ols_user_progress.rel_id LEFT JOIN ols_user_access_plan on ols_user_progress.rel_id = ols_user_access_plan.rel_id LEFT JOIN ols_course_units on ols_user_progress.db22013 = ols_course_units.id LEFT JOIN ols_course_modules on ols_course_units.rel_id = ols_course_modules.id", "count(*) DIV count(DISTINCT(ols_user_progress.rel_id))", "WHERE db22019 = $row[apc_rel_id] AND db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (ols_user_access_plan.rec_archive IS NULL OR ols_user_access_plan.rec_archive = '')  AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND db22031='2' AND (ols_course_modules.rel_id = $row[db21911])");
                if (!$no_learners_started || $no_learners_started == '' || $no_learners_started == '0') {
                    $no_learners_started = 0;
                }
                if (!$avg_units_completed_per_learner || $avg_units_completed_per_learner == '' || $avg_units_completed_per_learner == '0') {
                    $avg_units_completed_per_learner = 0;
                }
                $unit_modules = pull_field("ols_course_modules", "group_concat(id)", "where rel_id = '$row[db21911]' and (rec_archive is null or rec_archive = '')");
                //dev_debug("SELECT group_concat(id) FROM ols_course_modules WHERE rel_id = '$row[db21911]' and (rec_archive is null or rec_archive = '')");
                $number_units_in_course = pull_field("ols_course_units", "count(*)", "where rel_id IN ($unit_modules) and (rec_archive is null or rec_archive = '')");
                //dev_debug("SELECT count(*) FROM ols_course_units WHERE where rel_id IN ($unit_modules) and (rec_archive is null or rec_archive = '')");

                //remove found enjoyable for the time being
                if ($row['db21911'] == 4) { //understanding pregnancy
                    $query = "SELECT
								count(if(db562 = 'Yes' OR db562 = 'No' OR db564 = 'Yes' OR db564 = 'No' ,1, NULL)) as total_last_module_questionnaire_responses,
								count(if(db564 = 'Yes' ,1, NULL)) as would_reccommend_total,
								count(if(db562 = 'Yes' ,1, NULL)) as found_helpful_total
								FROM postq_course_4_post_questions
								LEFT JOIN ols_user_access_plan ON postq_course_4_post_questions.rel_id = ols_user_access_plan.rel_id
								WHERE
								(ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '')
								AND (postq_course_4_post_questions.rec_archive is NULL or postq_course_4_post_questions.rec_archive = '')
								AND db22019 = '$row[apc_rel_id]'
								AND db22031='2'";
                    $sth = $dbh->prepare($query);
                    $sth->execute();
                    $stats = $sth->fetch();

                    $total_post_questionnaire = $stats['total_last_module_questionnaire_responses'];
                    $found_course_helpful_post_questionnaire = $stats['found_helpful_total'];
                    $would_recommend_post_questionnaire = $stats['would_reccommend_total'];
                    //$found_enjoyable_post_questionnaire =      pull_field("ols_user_access_plan INNER JOIN postq_course_4_post_questions ON postq_course_4_post_questions.rel_id = ols_user_access_plan.rel_id","count(*)","WHERE (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '') AND (postq_course_4_post_questions.rec_archive is NULL or postq_course_4_post_questions.rec_archive = '') AND db22019 = '$row[apc_rel_id]' AND db22031='2' AND db569 = 'Yes'");
                } else if ($row['db21911'] == 5) { //understanding your baby
                    $query = "SELECT
								count(if(db899 = 'Yes' OR db899 = 'No' OR db900 = 'Yes' OR db900 = 'No' ,1, NULL)) as total_last_module_questionnaire_responses,
								count(if(db900 = 'Yes' ,1, NULL)) as would_reccommend_total,
								count(if(db899 = 'Yes',1, NULL)) as found_helpful_total
								FROM postq_course_5_post_questions
								LEFT JOIN ols_user_access_plan ON postq_course_5_post_questions.rel_id = ols_user_access_plan.rel_id
								WHERE
								(ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '')
								AND (postq_course_5_post_questions.rec_archive is NULL or postq_course_5_post_questions.rec_archive = '')
								AND db22019 = '$row[apc_rel_id]'
								AND db22031='2'";
                    $sth = $dbh->prepare($query);
                    $sth->execute();
                    $stats = $sth->fetch();

                    $total_post_questionnaire = $stats['total_last_module_questionnaire_responses'];
                    $found_course_helpful_post_questionnaire = $stats['found_helpful_total'];
                    $would_recommend_post_questionnaire = $stats['would_reccommend_total'];
                } else if ($row['db21911'] == 6) { //understanding your child
                    $query = "SELECT
								count(if(db995 = 'Yes' OR db995 = 'No' OR db999 = 'Yes' OR db999 = 'No' ,1, NULL)) as total_last_module_questionnaire_responses,
								count(if(db999 = 'Yes' ,1, NULL)) as would_reccommend_total,
								count(if(db995 = 'Yes',1, NULL)) as found_helpful_total
								FROM postq_course_6_post_questions
								LEFT JOIN ols_user_access_plan ON postq_course_6_post_questions.rel_id = ols_user_access_plan.rel_id
								WHERE
								(ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '')
								AND (postq_course_6_post_questions.rec_archive is NULL or postq_course_6_post_questions.rec_archive = '')
								AND db22019 = '$row[apc_rel_id]'
								AND db22031='2'";
                    $sth = $dbh->prepare($query);
                    $sth->execute();
                    $stats = $sth->fetch();

                    $total_post_questionnaire = $stats['total_last_module_questionnaire_responses'];
                    $found_course_helpful_post_questionnaire = $stats['found_helpful_total'];
                    $would_recommend_post_questionnaire = $stats['would_reccommend_total'];
                } else if ($row['db21911'] == 33) { //Understanding pregnancy, labour, birth and your baby: for women couples
                    $query = "SELECT
								count(if(db1680 = 'Yes' OR db1680 = 'No' OR db1681 = 'Yes' OR db1681 = 'No' ,1, NULL)) as total_last_module_questionnaire_responses,
								count(if(db1681 = 'Yes' ,1, NULL)) as would_reccommend_total,
								count(if(db1680 = 'Yes',1, NULL)) as found_helpful_total
								FROM postq_course_33_post_questions_1
								LEFT JOIN ols_user_access_plan ON postq_course_33_post_questions_1.rel_id = ols_user_access_plan.rel_id
								WHERE
								(ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '')
								AND (postq_course_33_post_questions_1.rec_archive is NULL or postq_course_33_post_questions_1.rec_archive = '')
								AND db22019 = '$row[apc_rel_id]'
								AND db22031='2'";
                    $sth = $dbh->prepare($query);
                    $sth->execute();
                    $stats = $sth->fetch();

                    $total_post_questionnaire = $stats['total_last_module_questionnaire_responses'];
                    $found_course_helpful_post_questionnaire = $stats['found_helpful_total'];
                    $would_recommend_post_questionnaire = $stats['would_reccommend_total'];
                } else if ($row['db21911'] == 35) { //4. Understanding your teenager's brain
                    $total_post_questionnaire = pull_field("ols_user_access_plan INNER JOIN postq_course_35_post_questions_1 ON postq_course_35_post_questions_1.rel_id = ols_user_access_plan.rel_id", "count(*)", "WHERE (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '') AND (postq_course_35_post_questions_1.rec_archive is NULL or postq_course_35_post_questions_1.rec_archive = '') AND db22019 = '$row[apc_rel_id]' AND db22031='2' AND (db1669 = 'Yes' OR db1669 = 'No' OR db1670 = 'Yes' OR db1670 = 'No')");
                    //$yes_post_questionnaire = pull_field("ols_user_access_plan INNER JOIN postq_course_6_post_questions ON postq_course_6_post_questions.rel_id = ols_user_access_plan.rel_id","count(*)","WHERE (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '') AND (postq_course_6_post_questions.rec_archive is NULL or postq_course_6_post_questions.rec_archive = '') AND db22019 = '$row[apc_rel_id]' AND db22031='2' AND db999 = 'Definitely applies'");
                    $found_course_helpful_post_questionnaire = pull_field("ols_user_access_plan INNER JOIN postq_course_35_post_questions_1 ON postq_course_35_post_questions_1.rel_id = ols_user_access_plan.rel_id", "count(*)", "WHERE (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '') AND (postq_course_35_post_questions_1.rec_archive is NULL or postq_course_35_post_questions_1.rec_archive = '') AND db22019 = '$row[apc_rel_id]' AND db22031='2' AND db1669 = 'Yes'");
                    $would_recommend_post_questionnaire = pull_field("ols_user_access_plan INNER JOIN postq_course_35_post_questions_1 ON postq_course_35_post_questions_1.rel_id = ols_user_access_plan.rel_id", "count(*)", "WHERE (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '') AND (postq_course_35_post_questions_1.rec_archive is NULL or postq_course_35_post_questions_1.rec_archive = '') AND db22019 = '$row[apc_rel_id]' AND db22031='2' AND db1670 = 'Yes'");
                    //$found_enjoyable_post_questionnaire =      pull_field("ols_user_access_plan INNER JOIN postq_course_35_post_questions_1 ON postq_course_35_post_questions_1.rel_id = ols_user_access_plan.rel_id","count(*)","WHERE (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '') AND (postq_course_35_post_questions_1.rec_archive is NULL or postq_course_35_post_questions_1.rec_archive = '') AND db22019 = '$row[apc_rel_id]' AND db22031='2' AND db1671 = 'Yes'");
                } else {
                    $course = $row['db21911'];
                    $total_post_questionnaire = $all_post_questionnaire_responses[$course]['total_last_module_questionnaire_responses'];
                    $found_course_helpful_post_questionnaire = $all_post_questionnaire_responses[$course]['found_helpful_total'];
                    $would_recommend_post_questionnaire = $all_post_questionnaire_responses[$course]['would_reccommend_total'];
                }

            }
            dev_debug("summary_stage_required" . $args['summary_stage_required']);
            if ($args['summary_stage_required']) {
                //$no_of_registered_users = pull_field("ols_user_access_plan", "count(*)", "WHERE (rec_archive is NULL or rec_archive = '') AND db22019 = '$row[apc_rel_id]' AND db22031='2'");
                //$no_of_registered_users = pull_field("ols_user_access_plan INNER JOIN core_students ON core_students.id = ols_user_access_plan.rel_id", "count(distinct(ols_user_access_plan.rel_id))", "WHERE db22031='2' AND db22019 = '$row[apc_rel_id]' AND (core_students.rec_archive is null or core_students.rec_archive= '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '')");
                if (!$no_of_registered_users || $no_of_registered_users == '') {
                    $no_of_registered_users = 0;
                }
                list($no_learners_started, $avg_units_completed_per_learner) = explode(',', pull_field("ols_user_progress INNER JOIN core_students ON core_students.id = ols_user_progress.rel_id", "CONCAT(count(DISTINCT(ols_user_progress.rel_id)),',',count(*) DIV count(DISTINCT(ols_user_progress.rel_id)))", "WHERE ols_user_progress.db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND ols_user_progress.db37394 ='$row[apc_rel_id]' AND (db37395 = '$row[db21911]')"));

                //$no_learners_started = pull_field("ols_user_progress INNER JOIN core_students ON core_students.id = ols_user_progress.rel_id", "count(DISTINCT(ols_user_progress.rel_id))", "WHERE ols_user_progress.db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND ols_user_progress.db37394 ='$row[apc_rel_id]' AND (db37395 = '$row[db21911]')");
                if (!$no_learners_started || $no_learners_started == '') {
                    $no_learners_started = 0;
                }
                if (!$avg_units_completed_per_learner || $avg_units_completed_per_learner == '' || $avg_units_completed_per_learner == '0') {
                    $avg_units_completed_per_learner = 0;
                }
                $date_search = date('Y-m-01');
                $date_search_start = date('Y-m-d', strtotime(date($date_search) . ' -1 MONTH'));
                $date_search_end = date('Y-m-d', strtotime(date($date_search_start) . ' +1 MONTH'));

                //AFY 4/12/24 same for all courses $no_of_registered_users_last_month = pull_field("ols_user_access_plan INNER JOIN core_students ON core_students.id = ols_user_access_plan.rel_id", "count(distinct(ols_user_access_plan.rel_id))", "WHERE db22019 = '$row[apc_rel_id]' AND db22031='2' AND (core_students.rec_archive is null or core_students.rec_archive= '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND ols_user_access_plan.date >='$date_search_start' and ols_user_access_plan.date <'$date_search_end' ");
            }

            dev_debug("completion_stage_required" . $args['completion_stage_required']);
            //dev_debug("$args[summary_stage_required] ** $no_of_registered_users ** $no_learners_started ** $no_of_registered_users_last_month ** $row[apc_rel_id]");
            if ($args['completion_stage_required']) {
                //$no_of_registered_users = pull_field("ols_user_access_plan", "count(*)", "WHERE (rec_archive is NULL or rec_archive = '') AND db22019 = '$row[apc_rel_id]' AND db22031='2'");
                //AFY 4/12/24 SAME FOR ALL COURSES$no_of_registered_users = pull_field("ols_user_access_plan INNER JOIN core_students ON core_students.id = ols_user_access_plan.rel_id", "count(distinct(ols_user_access_plan.rel_id))", "WHERE db22031='2' AND db22019 = '$row[apc_rel_id]' AND (core_students.rec_archive is null or core_students.rec_archive= '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '')");
                if (!$no_of_registered_users || $no_of_registered_users == '') {
                    $no_of_registered_users = 0;
                }

                $no_learners_started = pull_field("ols_user_progress INNER JOIN core_students ON core_students.id = ols_user_progress.rel_id", "count(DISTINCT(ols_user_progress.rel_id))", "WHERE ols_user_progress.db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND ols_user_progress.db37394 ='$row[apc_rel_id]' AND (db37395 = '$row[db21911]')");
                if (!$no_learners_started || $no_learners_started == '') {
                    $no_learners_started = 0;
                }
                $date_search = date('Y-m-01');
                $date_search_start = date('Y-m-d', strtotime(date($date_search) . ' -1 MONTH'));
                $date_search_end = date('Y-m-d', strtotime(date($date_search_start) . ' +1 MONTH'));

                //AFY 4/12/24 SAME FOR ALL COURSES $no_of_registered_users_last_month = pull_field("ols_user_access_plan INNER JOIN core_students ON core_students.id = ols_user_access_plan.rel_id", "count(distinct(ols_user_access_plan.rel_id))", "WHERE db22019 = '$row[apc_rel_id]' AND db22031='2' AND (core_students.rec_archive is null or core_students.rec_archive= '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND ols_user_access_plan.date >='$date_search_start' and ols_user_access_plan.date <'$date_search_end' ");

                $unit_modules = pull_field("ols_course_modules", "group_concat(id)", "where rel_id = '$row[db21911]' and (rec_archive is null or rec_archive = '')");
                $number_units_in_course = pull_field("ols_course_units", "count(*)", "where rel_id IN ($unit_modules) and (rec_archive is null or rec_archive = '')");
                //$no_learners_started =0;
                if ($no_learners_started > 0) {

                    $average_number_of_units_completed_query = "SELECT round(count(*) / $number_units_in_course *100) as avger FROM ols_user_progress JOIN core_students ON core_students.id = ols_user_progress.rel_id LEFT JOIN ols_user_access_plan on ols_user_progress.rel_id = ols_user_access_plan.rel_id LEFT JOIN ols_course_units on ols_user_progress.db22013 = ols_course_units.id LEFT JOIN ols_course_modules on ols_course_units.rel_id = ols_course_modules.id WHERE db22019 = '$row[apc_rel_id]' AND db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive IS NULL OR core_students.rec_archive = '') AND (ols_user_access_plan.rec_archive IS NULL OR ols_user_access_plan.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND db22031='2' AND (ols_course_modules.rel_id = '$row[db21911]') GROUP by ols_user_access_plan.rel_id";
                    dev_debug($average_number_of_units_completed_query);
                    $no_units_completed_greater_than_70 = 0;
                    $no_units_completed_between_51_and_69 = 0;
                    $no_units_completed_between_10_and_50 = 0;
                    $no_units_completed_less_than_10 = 0;
                    $average_number_of_units_completed = $db->query($average_number_of_units_completed_query);
                    foreach ($average_number_of_units_completed as $avger) {
                        if ($avger['avger'] >= 70) {
                            $no_units_completed_greater_than_70++;
                        } else if ($avger['avger'] >= 51 & $avger['avger'] <= 69) {
                            $no_units_completed_between_51_and_69++;
                        } else if ($avger['avger'] >= 10 & $avger['avger'] <= 50) {
                            $no_units_completed_between_10_and_50++;
                        } else if ($avger['avger'] < 10) {
                            $no_units_completed_less_than_10++;
                        }
                    }

                    /*$no_units_completed_greater_than_70_query = "SELECT round(count(*) / $number_units_in_course *100) as avger FROM ols_user_progress JOIN core_students ON core_students.id = ols_user_progress.rel_id LEFT JOIN ols_user_access_plan on ols_user_progress.rel_id = ols_user_access_plan.rel_id LEFT JOIN ols_course_units on ols_user_progress.db22013 = ols_course_units.id LEFT JOIN ols_course_modules on ols_course_units.rel_id = ols_course_modules.id WHERE db22019 = $row[apc_rel_id] AND db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive IS NULL OR core_students.rec_archive = '') AND (ols_user_access_plan.rec_archive IS NULL OR ols_user_access_plan.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND db22031='2' AND (ols_course_modules.rel_id = $row[db21911]) GROUP by ols_user_access_plan.rel_id HAVING avger >= 70";
					$no_units_completed_greater_than_70 = count($db->get_rows($no_units_completed_greater_than_70_query));
					$no_units_completed_between_51_and_69_query = "SELECT round(count(*) / $number_units_in_course *100) as avger FROM ols_user_progress JOIN core_students ON core_students.id = ols_user_progress.rel_id  LEFT JOIN ols_user_access_plan on ols_user_progress.rel_id = ols_user_access_plan.rel_id LEFT JOIN ols_course_units on ols_user_progress.db22013 = ols_course_units.id LEFT JOIN ols_course_modules on ols_course_units.rel_id = ols_course_modules.id WHERE db22019 = $row[apc_rel_id] AND db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive IS NULL OR core_students.rec_archive = '') AND (ols_user_access_plan.rec_archive IS NULL OR ols_user_access_plan.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND db22031='2' AND (ols_course_modules.rel_id = $row[db21911]) GROUP by ols_user_access_plan.rel_id HAVING avger between 51 AND 69" ;
					$no_units_completed_between_51_and_69 = count($db->get_rows($no_units_completed_between_51_and_69_query));
					$no_units_completed_between_10_and_50_query = "SELECT round(count(*) / $number_units_in_course *100) as avger FROM ols_user_progress JOIN core_students ON core_students.id = ols_user_progress.rel_id  LEFT JOIN ols_user_access_plan on ols_user_progress.rel_id = ols_user_access_plan.rel_id LEFT JOIN ols_course_units on ols_user_progress.db22013 = ols_course_units.id LEFT JOIN ols_course_modules on ols_course_units.rel_id = ols_course_modules.id WHERE db22019 = $row[apc_rel_id] AND db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive IS NULL OR core_students.rec_archive = '') AND (ols_user_access_plan.rec_archive IS NULL OR ols_user_access_plan.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND db22031='2' AND (ols_course_modules.rel_id = $row[db21911]) GROUP by ols_user_access_plan.rel_id HAVING avger between 10 AND 50" ;
					$no_units_completed_between_10_and_50 = count($db->get_rows($no_units_completed_between_10_and_50_query));
					$no_units_completed_less_than_10_query = "SELECT round(count(*) / $number_units_in_course *100) as avger FROM ols_user_progress JOIN core_students ON core_students.id = ols_user_progress.rel_id  LEFT JOIN ols_user_access_plan on ols_user_progress.rel_id = ols_user_access_plan.rel_id LEFT JOIN ols_course_units on ols_user_progress.db22013 = ols_course_units.id LEFT JOIN ols_course_modules on ols_course_units.rel_id = ols_course_modules.id WHERE db22019 = $row[apc_rel_id] AND db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive IS NULL OR core_students.rec_archive = '') AND (ols_user_access_plan.rec_archive IS NULL OR ols_user_access_plan.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND db22031='2' AND (ols_course_modules.rel_id = $row[db21911]) GROUP by ols_user_access_plan.rel_id HAVING avger < 10" ;
					$no_units_completed_less_than_10 = count($db->get_rows($no_units_completed_less_than_10_query));*/
                } else {
                    $no_units_completed_greater_than_70 = 0;
                    $no_units_completed_between_51_and_69 = 0;
                    $no_units_completed_between_10_and_50 = 0;
                    $no_units_completed_less_than_10 = 0;

                }
            }
            //Get\\
            // $course_args = array('id'=>$row['db21911'],'no_unit_description'=>$args['no_unit_description'],'raw_html'=>$args['raw_html']);
            // $course = $this->get_courses($course_args);
            //Result

            //find the cumulative monthly actual learners for each course
            if ($args['group_id']) {
                $actual_learners_by_month = null;
            }
            dev_debug("cumulative_learners_required" . $args['cumulative_learners_required']);
            if ($args['cumulative_learners_required']) {
                $actual_learners_by_month = $this->get_monthly_actual_learners_by_course(array('course_id' => $row['db21911'], 'access_plan_id' => $row['apc_rel_id'], 'school_id' => $args['school_id']));
            }
            $course = array(
                'id' => $row['db21911'],
                'title' => $row['db21909'],
                'alternative_title' => $row['db260924'],
                'lang'=>$row['db31066'],
                "href" => $this->home_url("course/") . $this->ensure_slug_matches_title($row['db21911'], 'course') ,
                "no_registered_users" => $no_of_registered_users,
                "no_of_registered_users_last_month" => $no_of_registered_users_last_month,
                "no_started_learners" => $no_learners_started,
                "avg_units_completed_per_learner" => $avg_units_completed_per_learner,
                "no_of_units_in_course" => $number_units_in_course,
                "count_learners_no_units_completed_greater_than_70" => $no_units_completed_greater_than_70,
                "count_learners_no_units_completed_between_51_and_69" => $no_units_completed_between_51_and_69,
                "count_learners_no_units_completed_between_10_and_50" => $no_units_completed_between_10_and_50,
                "count_learners_no_units_completed_less_than_10" => $no_units_completed_less_than_10,
                "monthly_actual_learners" => $actual_learners_by_month,
                'count_found_course_helpful' => $found_course_helpful_post_questionnaire,
                'count_recommend_course' => $would_recommend_post_questionnaire,
                /*'count_found_course_enjoyable' => $found_enjoyable_post_questionnaire,*/
                'count_completed_post_questionnaire' => $total_post_questionnaire,
                //'sql_topass'=>$sql_topass
            );
            //dev_debug("ANITA $args[summary_stage_required] ** $no_of_registered_users ** $no_learners_started ** $no_of_registered_users_last_month ** $row[apc_rel_id]");

            $results_list[] = $course;
        }

        if ($args['id'] || $args['username_id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /** ===================================
     * Get Access Plan Scheduled Courses
     * ====================================    */
    function get_access_plan_scheduled_courses($args = array())
    {

        $bm = new Booking_model();

        $db = new Db_helper();
        $dbh = get_dbh();

        if ($args['id']) {
            $id_sql = "AND ols_access_plan_sched_courses.id='" . $args['id'] . "'";
            $args['access_plan_sched_courses_id'] = $args['id'];
        }

        if ($args['access_plan']) {
            $access_plan_sql = "AND ols_access_plan_sched_courses.rel_id='" . $args['access_plan'] . "'";
        }

        if ($args['school_id']) {
            $school_id_sql = "AND ols_access_plan_sched_courses.usergroup='" . $args['school_id'] . "'";
        } else {
            if (isset($_SESSION['usergroup'])) {
                $school_id_sql = "AND ols_access_plan_sched_courses.usergroup='" . $_SESSION['usergroup'] . "'";
            }
        }

        if ($args['username_id']) {
            $username_id_sql = "AND ols_access_plan_sched_courses.username_id='" . $args['username_id'] . "'";
            $args['access_plan_sched_courses_username_id'] = $args['username_id'];
        }

        $data = $bm->get_available_courses($args);
        if ((isset($args['id']) && $args['id']) || (isset($args['username_id']) && $args['username_id'])) {
            return isset($data[0]) ? $data[0] : false;
        } else return $data;

        /*$query ="
			SELECT
				*,ols_access_plan_sched_courses.rel_id as 'apsc_rel_id'
			FROM
				ols_access_plan_sched_courses
				LEFT JOIN sis_course_schedule ON sis_course_schedule.id=ols_access_plan_sched_courses.db67086
			WHERE
				1
				$id_sql
				$access_plan_sql
				$school_id_sql
				$username_id_sql
				AND (ols_access_plan_sched_courses.rec_archive is null or ols_access_plan_sched_courses.rec_archive='')
			ORDER BY ols_access_plan_sched_courses.id ASC";
		// dev_debug($query);
		// echo '<pre>';
		// print_r($query);
		// echo '</pre>';
		// exit();
		$sth = $dbh->prepare($query);
		$sth->execute();
		$results_list = array();

		while ($row = $sth->fetch(PDO::FETCH_ASSOC)) {

			$course = array(
				'id'=>$row['db67086'],
			);
			$results_list[] =  $course;
		}

		if($args['id'] || $args['username_id']){
			return $results_list[0];
		}else{
			return $results_list;
		}*/
    }

    function get_support_priorities($args = array())
    {
        $dbh = get_dbh();

        $support_priorities = array();
        $query = "SELECT id,db101 FROM form_support_priority where (rec_archive IS NULL or rec_archive = '') ORDER by db102";
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $num = count($results);
        foreach ($results as $row) {
            $support_priorities[] = array(
                "id" => $row['id'],
                "title" => $row['db101']
            );
        }
        return $support_priorities;

    }

    /** ===================================
     * Get Support
     * ====================================    */
    function get_support($args = array())
    {

        global $paginator;
        $dbh = get_dbh();
        $users = new Users;

        if (isset($args['student_id'])) {
            $id_sql = "AND ols_support.rel_id='" . $args['student_id'] . "'";
        }

        if ($args['count']) {
            $select_sql = "count(*) as total_count ";
        } else {
            $select_sql = "form_users.id as ticket_user_id,form_users.db106,form_users.db111,form_users.db119,ols_support.*
      
      ";
        }

        if (isset($args['id'])) {
            $id_sql = "AND ols_support.id='" . $args['id'] . "'";
        }

        if (isset($args['status'])) {
            if ($args['status'] == 'awaiting_admin_reply') {
                $last_reply_rel_id_where = "AND ols_support.rec_id=(select rec_lstup_id from ols_support_comments WHERE db25514 = ols_support.id ORDER BY id DESC LIMIT 1)";
                $status_sql = "AND ols_support.db22078='Open'";
            } else {
                $status_sql = "AND ols_support.db22078='" . $args['status'] . "'";
            }
        }

        if (isset($args['priority'])) {
            $priority_sql = "AND ols_support.db22074='" . $args['priority'] . "'";
        }

        if (isset($args['search'])) {
            $search_sql = "AND (ols_support.db22071 LIKE '%" . $args['search'] . "%' OR ols_support.id='" . $args['search'] . "')";
        }


        if (isset($args['username_id'])) {
            $username_id_sql = "AND ols_support.username_id='" . $args['username_id'] . "'";
        }

        if ($args['school_id']) {
            $school_id_sql = "AND ols_support.usergroup='" . $args['school_id'] . "'";
        }

        if (isset($args['user_id'])) {
            $user_id_sql = "AND ols_support.rec_id='" . $args['user_id'] . "'";
        }

        $query = "
			SELECT 
        $select_sql
        ,(select date from ols_support_comments WHERE db25514 = ols_support.id ORDER BY id DESC LIMIT 1) AS 'last_reply_date',
        db58502 as priority_colour,
        db101 as priority_name
			FROM
        ols_support 
        LEFT JOIN form_users ON form_users.id=ols_support.rec_id
        LEFT JOIN form_support_priority ON form_support_priority.id=ols_support.db22074
			WHERE
				1
        $id_sql
        $priority_sql
				$status_sql
				$school_id_sql
        $category_sql
        $search_sql
        $user_id_sql
        $username_id_sql
        $last_reply_rel_id_where
				AND (ols_support.rec_archive is null or ols_support.rec_archive='')
			ORDER BY ols_support.id DESC";


        // echo '<pre>';
        // print_r($query);
        // exit();
        if (array_key_exists('paginate', $args)) {
            if ($args['paginate']) {
                $limit_sql = $paginator->limit_sql();
                $paginator->calculate_total_entries($query);
            }
        }

        $query = $query . $limit_sql;

        dev_debug($query);
        $sth = $dbh->prepare($query);
        $sth->execute();
        $results_list = array();

        $db = new Db_helper();

        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {

            if ($args['count']) {
                $results_list = $entry['total_count'];
            } else {
                $comments_args = array(
                    'entry_id' => $entry['id'],
                    'order_asc' => true
                );

                if ($args['include_internal_comments']) {
                    $comments_args['include_internal_comments'] = 1;
                }

                if ($args['comments_order']) {
                    unset($comments_args['order_asc']);
                }

                // echo '<pre>';
                // print_r($comments_args);
                // exit();

                $users_args = array("id" => $entry['rec_id']);
                $user_info = $users->get($users_args);
                if (!isset($entry['priority_colour']) || $entry['priority_colour'] == "" || $entry['priority_colour'] == 'not specified') {
                    $color = "grey";
                } else {
                    $color = $entry['priority_colour'];
                }

                /*if($entry['db22074']=="High"){ $color = "red"; }
        if($entry['db22074']=="Medium"){ $color = "orange"; }
        if($entry['db22074']=="Normal"){ $color = "grey"; }*/

                $comments = $this->get_support_comments($comments_args);
                $entry_info = array(
                    'id' => $entry['id'],
                    'title' => $entry['db22071'],
                    'date_added' => $entry['date'],
                    'student' => array('id' => $entry['rel_id']),
                    'username_id' => $entry['username_id'],
                    'user' => $user_info,
                    'description' => $entry['db22072'],
                    'category' => $entry['db22073'],
                    'priority' => $entry['db22074'],
                    'priority_name' => $entry['priority_name'],
                    'date' => $entry['date'],
                    'status' => $entry['db22078'],
                    'comments' => $comments,
                    'color' => $color,
                    'closing_note' => $entry['db56610'],
                    'last_reply_date' => $entry['last_reply_date'],
                    "user" => array(
                        "id" => $entry['ticket_user_id'],
                        "first_name" => $entry['db106'],
                        "last_name" => $entry['db111'],
                        "email" => $entry['db119'],
                    )
                );

                if ($entry['db56606']) {
                    //get the resposnsible person
                    $query = "SELECT  * FROM form_users WHERE id='" . $entry['db56606'] . "' AND (rec_archive is null or rec_archive='') ORDER BY id ASC LIMIT 1";
                    // echo $query;
                    // exit();
                    $results = $db->query($query);
                    foreach ($results as $user) {
                        $entry_info['person_responsible'] = array(
                            "first_name" => $user['db106'],
                            "last_name" => $user['db111'],
                            "email" => $user['db119'],
                        );
                    }


                }


                $results_list[] = $entry_info;
            }

        }

        if ($args['id'] || $args['username_id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Complete multiple tickets from array
     * ====================================    */
    function complete_tickets($tickets)
    {

        $dbh = get_dbh();
        global $school_info;
        $db = new Db_helper();

        foreach ($tickets as $ticket) {
            $sql = "UPDATE ols_support SET db22078='Completed' WHERE id='$ticket'";
            $sth = $dbh->prepare($sql);
            $sth->execute();

            //Get the ticket info
            $query = "SELECT ols_support.*, form_users.db106, form_users.db111,db112,db119 FROM ols_support
       LEFT JOIN form_users ON form_users.id = ols_support.rec_id
       WHERE 1 AND ols_support.id='" . $ticket . "' AND (ols_support.rec_archive is null or ols_support.rec_archive='') LIMIT 1";
            $stmt = $dbh->prepare($query);
            $stmt->execute();
            $ticket_info = $stmt->fetch(PDO::FETCH_ASSOC);
            //  echo $query;
            //  exit();

            //If Admin replys then an email student
            if (($ticket_info['rec_id'] != $_SESSION['uid']) && ($ticket_info['db112'] == 4)) {
                $subject = "Support ticket completed";
                $ticket_link = $school_info["href"] . "/online-learning/support/" . $ticket_info['username_id'];
                $message = "Dear " . $ticket_info['db106'] . " " . $ticket_info['db111'] . " <br/> <br/>
 
 This is an automatically generated email to let you know that your support ticket has now been marked as complete</br>
 ----------------------------------------------------</br>
 To view the ticket, please log in to your account at</br>
 <a href='https://" . $ticket_link . "'>" . $ticket_link . "</a></br></br>
     
 Kind regards</br>
 " . $school_info['title'] . "</br>
 </br></br></br>
 ";
                $email_args = array(
                    'to' => $ticket_info['db119'],
                    'subject' => $subject,
                    'text' => $message,
                    'html' => $message,
                    'category' => "Support Issue",
                    // 'recipient_id'=> random()
                );
                $emails = new Emails;
                $emails->send($email_args);
            }
        }
    }

    /** ===================================
     * Get Support Comments
     * ====================================    */
    function get_support_comments($args = array())
    {


        $dbh = get_dbh();
        $users = new Users;

        if (isset($args['id'])) {
            $id_sql = "AND ols_support_comments.id='" . $args['id'] . "'";
        }

        if (isset($args['student_id'])) {
            $student_id_sql = "AND ols_support_comments.rel_id='" . $args['student_id'] . "'";
        }


        if (isset($args['entry_id'])) {
            $entry_id_sql = "AND ols_support_comments.db25514='" . $args['entry_id'] . "'";
        }

        if (isset($args['per_unit'])) {
            $per_unit_sql = " GROUP BY ols_support_comments.rec_id,ols_course_units.id";
        }

        if ($args['no_replies']) {
            $no_replies_sql = "AND form_users.db112 ='4'";
        }

        if (isset($args['order_asc'])) {
            $order_sql = " ORDER BY ols_support_comments.id asc";
        } else {
            $order_sql = " ORDER BY ols_support_comments.id DESC";
        }

        if ($args['include_internal_comments']) {

        } else {
            $internal_comments_sql = "AND (db57187!='yes' AND db57187!='on' or db57187 is null) ";
        }

        if (isset($args['category'])) {
            $category_sql = "AND ols_support_comments.db22083='" . $args['category'] . "'";
            $units_joins = "
				LEFT JOIN ols_course_units ON ols_course_units.id = ols_support_comments.db25514
				LEFT JOIN ols_online_courses ON ols_online_courses.id = ols_course_units.rel_id
			";
            $units_fields = ",ols_course_units.id as unit_id,
				ols_online_courses.id as course_id
			";
        }

        $query = "
			SELECT 
				*,
				ols_support_comments.rec_id as user_id ,
				ols_support_comments.rel_id as student_id ,
        ols_support_comments.id as comment_id,
        ols_support_comments.date as comment_date
				$units_fields
			FROM
				ols_support_comments 
				LEFT JOIN core_students ON core_students.id = ols_support_comments.rec_id
				LEFT JOIN form_users ON form_users.id=core_students.rec_id
				$units_joins
			WHERE
        1
        $user_id_sql
				$entry_id_sql
				$category_sql
				$student_id_sql
        $no_replies_sql
        $internal_comments_sql 
				$id_sql
				AND (ols_support_comments.rec_archive is null or ols_support_comments.rec_archive='')
			$per_unit_sql
				$order_sql
			 
       ";


// echo '<pre>';
// 			print_r($query);
// 			echo '</pre>';


        $sth = $dbh->prepare($query);
        $sth->execute();
        $results_list = array();
        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {
            $tagged_users = array_map(function ($usr) {
                return pull_field("form_users", "CONCAT('@', db106, ' ', db111)", "WHERE id=$usr");
            }, explode(",", $entry['db63996']));
            $users_args = array("id" => $entry['user_id']);
            //print_r($users_args);
            $user_info = $users->get($users_args);


            $entry = array(
                'id' => $entry['comment_id'],
                'user' => $user_info,
                'tagged_users' => $tagged_users,
                'student' => array('id' => $entry['student_id']),
                'date' => $entry['comment_date'],
                'description' => $entry['db22082'],
                'category' => $entry['db22083'],
                'internal' => $entry['db57187'],
                'unit' => array(
                    'id' => $entry['unit_id'],
                    'title' => $entry['db21923']
                ),
                'course' => array(
                    'id' => $entry['id'],
                    'title' => $entry['db21909']
                ),

            );

            // echo '<pre>';
            // print_r($entry);
            // echo '</pre>';

            $results_list[] = $entry;
        }

        if ($args['id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Get Supplementary Question
     * ====================================    */
    function get_supplementary_question($args = array())
    {
        $db = new Db_helper();
        $dbh = get_dbh();
        $users = new Users;

        if (isset($args['access_plan_id'])) {
            $plan_id_sql = "AND ols_supplementary_questions.rel_id='" . $args['access_plan_id'] . "'";
        }

        $query = "
			SELECT 
				*,
				ols_supplementary_questions.id as question_id
			FROM
				ols_supplementary_questions 
			WHERE
				1
				$plan_id_sql
				$id_sql
		  
				AND (ols_supplementary_questions.rec_archive is null or ols_supplementary_questions.rec_archive='')
			ORDER BY db21897 ASC";
        dev_debug($query);
        $sth = $dbh->prepare($query);
        $sth->execute();
        $results_list = array();
        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {
            if (isset($args['student_id'])) {
                $user_answer = trim(pull_field("ols_user_supp_answers", "db22026", "WHERE rel_id='$args[student_id]' AND db22027='$args[access_plan_id]' AND db22025='$entry[id]'"));
            }

            $entry = array(
                'id' => $entry['question_id'],
                'title' => $entry['db21895'],
                'type' => $entry['db21896'],
                'order' => $entry['db21897'],
                'required' => $entry['db22024'],
                'answers' => $entry['db25608'],
                'user_answer' => $user_answer,
                'query' => "SELECT db22026 FROM ols_user_supp_answers WHERE rel_id='$args[student_id]' AND db22027='$args[access_plan_id]' AND db22025='$entry[id]'",
            );
            $results_list[] = $entry;
        }

        if ($args['id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Get Supplementary Question
     * ====================================    */
    function get_supplementary_answers($args = array())
    {

        $dbh = get_dbh();
        $users = new Users;

        if (!empty($args['id'])) {
            $id_sql = "AND ols_user_supp_answers.id='" . $args['id'] . "'";
        }

        if (!empty($args['student_id'])) {
            $student_id_sql = "AND ols_user_supp_answers.rel_id='" . $args['student_id'] . "'";
        }
        if (!empty($args['access_plan_id'])) {
            $plan_id_sql = "AND ols_user_supp_answers.db22027='" . $args['access_plan_id'] . "'";
        }

        if (!empty($args['access_plan_ids']) && is_array($args['access_plan_ids'])) {
            $ids = implode(",", array_map('intval', $args['access_plan_ids']));
            $plans_id_sql = "AND ols_user_supp_answers.db22027 IN ($ids)";
        }

        $query = "
			SELECT 
				*,
				ols_user_supp_answers.date as 'answer_date'
			FROM
				ols_user_supp_answers 
				LEFT JOIN ols_supplementary_questions ON ols_supplementary_questions.id = ols_user_supp_answers.db22025
				LEFT JOIN ols_access_plans ON ols_access_plans.id = ols_supplementary_questions.rel_id
			WHERE
				1
				$student_id_sql
				$plan_id_sql
                $plans_id_sql
				$id_sql
				AND (ols_user_supp_answers.rec_archive is null or ols_user_supp_answers.rec_archive='')
			ORDER BY ols_user_supp_answers.id ASC";

        // echo '<pre>';
        // print_r($query);
        // echo '</pre>';
        dev_debug($query);
        $sth = $dbh->prepare($query);
        $sth->execute();

        $results_list = array();
        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {

            $entry = array(
                'id' => $entry['id'],
                'question' => $entry['db21895'],
                'answer' => $entry['db22026'],
                'date' => $entry['answer_date'],
                'plan' => array(
                    'title' => $entry['db21876'],
                    'plan_id'=>$entry['db22027']
                ),
            );
            $results_list[] = $entry;
        }

        if ($args['id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Get Challenge Question
     * ====================================    */
    function get_challenge_questions($args = array())
    {
        $db = new Db_helper();

        $dbh = get_dbh();
        $users = new Users;

        if (isset($args['access_plan_id'])) {
            $plan_id_sql = "AND ols_challenge_questions.rel_id='" . $args['access_plan_id'] . "'";
        }

        $query = "
			SELECT 
				*
			FROM
				ols_challenge_questions 
			WHERE
				1
				$plan_id_sql
				$id_sql
				AND (rec_archive is null or rec_archive='')
			ORDER BY id ASC";
        dev_debug($query);
        $sth = $dbh->prepare($query);
        $sth->execute();
        $results_list = array();
        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {

            $answer = $this->get_challenge_answers(array('question_id' => $entry['id']));
            if (isset($args['student_id'])) {
                $user_answer = pull_field("ols_user_chal_answers", "db22030", "WHERE rel_id='$args[student_id]' AND db22028='$entry[rel_id]' AND db22029='$entry[id]'");
                $user_answer_status = pull_field("ols_user_chal_answers", "db22032", "WHERE rel_id='$args[student_id]' AND db22028='$entry[rel_id]' AND db22029='$entry[id]'");
            }
            $entry = array(
                'id' => $entry['id'],
                'title' => $entry['db21899'],
                'type' => $entry['db21900'],
                'answer' => $answer['answer'],
                'group_answer' => $answer['group_answer'],
                'user_answer' => $user_answer,
                'user_answer_status' => $user_answer_status
            );
            $results_list[] = $entry;
        }

        if ($args['id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Get Challenge Question Answers
     * ====================================    */
    function get_challenge_answers($args = array())
    {

        $dbh = get_dbh();
        $users = new Users;

        if (isset($args['question_id'])) {
            $question_id_sql = "AND db21902='" . $args['question_id'] . "'";
        }

        $query = "
			SELECT 
				*,GROUP_CONCAT(db21903) as group_db21903
			FROM
				ols_challenge_answers 
			WHERE
				1
				$question_id_sql
				$id_sql
				AND (rec_archive is null or rec_archive='')
			ORDER BY id ASC";
        dev_debug($query);
        $sth = $dbh->prepare($query);
        $sth->execute();
        $results_list = array();
        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {
            $entry = array(
                'id' => $entry['id'],
                'question_id' => $entry['db21902'],
                'answer' => $entry['db21903'],
                'group_answer' => $entry['group_db21903']
            );
            $results_list[] = $entry;
        }

        if ($args['question_id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /** ===================================
     * Get Supplementary Question
     * ====================================    */
    function get_user_challenge_answers($args = array())
    {

        $dbh = get_dbh();

        if (isset($args['id'])) {
            $id_sql = "AND ols_user_chal_answers.id='" . $args['id'] . "'";
        }

        if (isset($args['student_id'])) {
            $student_id_sql = "AND ols_user_chal_answers.rel_id='" . $args['student_id'] . "'";
        }
        if (isset($args['access_plan_id'])) {
            $plan_id_sql = "AND ols_user_chal_answers.db22028='" . $args['access_plan_id'] . "'";
        }

        if (!empty($args['access_plan_ids']) && is_array($args['access_plan_ids'])) {
            $ids = implode(",", array_map('intval', $args['access_plan_ids']));
            $plans_id_sql = "AND ols_user_chal_answers.db22028 IN ($ids)";
        }

        $query = "
			SELECT 
				*,
				ols_user_chal_answers.date as 'answer_date'
			FROM
				ols_user_chal_answers 
				LEFT JOIN ols_challenge_questions ON ols_challenge_questions.id = ols_user_chal_answers.db22029
				LEFT JOIN ols_access_plans ON ols_access_plans.id = ols_challenge_questions.rel_id
			WHERE
				1
				$student_id_sql
				$plan_id_sql
                $plans_id_sql
				$id_sql
				AND (ols_user_chal_answers.rec_archive is null or ols_user_chal_answers.rec_archive='')
			ORDER BY ols_user_chal_answers.id ASC";

        // echo '<pre>';
        // print_r($query);
        // echo '</pre>';

        $sth = $dbh->prepare($query);
        $sth->execute();

        $results_list = array();
        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {

            $entry = array(
                'id' => $entry['id'],
                'question' => $entry['db21899'],
                'answer' => $entry['db22030'],
                'date' => $entry['answer_date'],
                'plan' => array(
                    'title' => $entry['db21876'],
                    'plan_id'=>$entry['db22028']
                ),
            );
            $results_list[] = $entry;
        }

        if ($args['id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /** ===================================
     * Unit Notes
     * ====================================    */
    function unit_notes($args = array())
    {

        $db = new Db_helper();

        $id_sql = isset($args['id']) ? "AND ols_unit_notes.id='" . $args['id'] . "'" : "";
        $unit_id_sql = isset($args['unit_id']) ? "AND ols_unit_notes.db25556='" . $args['unit_id'] . "'" : "";
        $student_id_sql = isset($args['student_id']) ? "AND ols_unit_notes.rel_id='" . $args['student_id'] . "'" : "";
        
        $units_ids_sql = isset($args['units_ids']) ? "AND ols_unit_notes.db25556 IN (" . implode(',', $args['units_ids'])  . ")" : "";

        // Pagination parameters
        $limit = isset($args['limit']) ? intval($args['limit']) : 10;
        $offset = isset($args['offset']) ? intval($args['offset']) : 0;

        $limit_ordering="ORDER BY id ASC
            LIMIT $limit OFFSET $offset";

        if (!empty($args['get_submited_note'])) {
            $limit_ordering="ORDER BY id DESC
            LIMIT 1";
        }

        $query = "
            SELECT 
                *
            FROM
                ols_unit_notes 
            WHERE
                1
                $id_sql
                $student_id_sql
                $unit_id_sql
                $units_ids_sql
                AND (rec_archive is null or rec_archive='')
                $limit_ordering
            ";

        $results = $db->query($query);
        foreach ($results as $note) {

            $unit_info = $this->get_units(array('id' => $note['db25556'],"info_for_notes"=>true));

            $note = array(
                'id' => $note['id'],
                'description' => $note['db25557'],
                'unit' => $unit_info,
                'query'=>$query
            );
            $results_list[] = $note;
        } 

        if ($args['id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Insert or Update Note
     * ====================================    */
    function insert_update_note($args)
    {
        $db = new Db_helper();

        if (!$args['description']) {
            return false;
        }

        $info = array();
        if ($args['student_id']) {
            $info['rel_id'] = $args['student_id'];
        }
        if ($args['unit_id']) {
            $info['db25556'] = $args['unit_id'];
        }
        if ($args['description']) {
            $info['db25557'] = $args['description'];
        }

        if ($args['id']) {
            $where = array('id' => $args['id']);
            $db->update('ols_unit_notes', $info, $where);
            return 1;
        } else {
            $db->system_table_insert_or_update('ols_unit_notes', $info);
            $new_entry_id = $db->lastInsertId();
            return $new_entry_id;
        }
    }

    /** ===================================
     * Delete note
     * ====================================    */
    function delete_note($args)
    {
        $db = new Db_helper();

        if ($args['id']) {
            $where = array('id' => $args['id']);

            if ($args['student_id']) {
                $where['rel_id'] = $args['student_id'];
            }
            if ($args['school_id']) {
                $where['usergroup'] = $args['school_id'];
            }
            $db->archive('ols_unit_notes', $where);
        }
        return $args['id'];

    }


    /** ===================================
     * Unit Feedback
     * ====================================    */
    function unit_feedback($args = array())
    {

        $dbh = get_dbh();
        $execution_params = array();

        if (isset($args['id'])) {
            $id_sql = "AND ols_unit_feedback.id=:id";
            $execution_params['id'] = $args['id'];
        }

        if (isset($args['unit_id'])) {
            $unit_id_sql = "AND ols_unit_feedback.db66644=:unit_id']";
            $execution_params['unit_id'] = $args['unit_id'];
        }

        if (isset($args['student_id'])) {
            $student_id_sql = "AND ols_unit_feedback.rel_id=:student_id";
            $execution_params['student_id'] = $args['student_id'];
        }

        if (isset($args['course'])) {
            $course_id_sql = "AND db66644 IN (SELECT id from ols_course_units WHERE (ols_course_units.rec_archive IS NULL OR ols_course_units.rec_archive ='') AND ols_course_units.rel_id IN (SELECT id from ols_course_modules WHERE (ols_course_modules.rec_archive IS NULL OR ols_course_modules.rec_archive = '') AND ols_course_modules.rel_id = :course_id))";
            $execution_params['course_id'] = $args['course'];
        }

        if ($args['school_id']) {
            $school_id_sql = " AND ols_unit_feedback.usergroup = :school_id";
            $execution_params['school_id'] = $args['school_id'];
        }

        $query = "
			SELECT 
				ols_unit_feedback.id as 'feedback_id',
				DATE_FORMAT(ols_unit_feedback.date,'%d/%m/%Y') as feedback_date,
				db66665 as 'feedback',
				ols_course_units.id  as 'unit_id',
				ols_course_units.db21923 as 'unit_name',
				core_students.db764 as 'learner'
				
			FROM
				ols_unit_feedback 
				JOIN ols_course_units ON ols_course_units.id = db66644
				JOIN core_students ON core_students.id = ols_unit_feedback.rel_id
			WHERE
				1
				$id_sql
				$unit_id_sql
				$student_id_sql
				$course_id_sql
				$school_id_sql
				AND (ols_unit_feedback.rec_archive IS NULL OR ols_unit_feedback.rec_archive='')
				
			ORDER BY ols_unit_feedback.id ASC";
        dev_debug("$query**$args[course]**$args[school_id]");
        $stmt = $dbh->prepare($query);
        $success = $stmt->execute($execution_params);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($results as $feedback_row) {

            $feedback = array(
                'id' => $feedback_row['feedback_id'],
                'feedback' => $feedback_row['feedback'],
                'feedback_date' => $feedback_row['feedback_date'],
                'learner' => $feedback_row['learner'],
                'unit' => array(
                    'title' => $feedback_row['unit_name'],
                    'id' => $feedback_row['unit_id']
                )
            );
            $results_list[] = $feedback;
        }

        if ($args['id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /** ===================================
     * Insert or Update Feedback
     * ====================================    */
    function insert_update_feedback($args)
    {
        $db = new Db_helper();

        if (!$args['description']) {
            return false;
        }

        $info = array();
        if ($args['student_id']) {
            $info['rel_id'] = $args['student_id'];
        }
        if ($args['unit_id']) {
            $info['db66644'] = $args['unit_id'];
        }
        if ($args['description']) {
            $info['db66665'] = $args['description'];
        }

        if ($args['id']) {
            $where = array('id' => $args['id']);
            $db->update('ols_unit_feedback', $info, $where);
            return 1;
        } else {
            $db->system_table_insert_or_update('ols_unit_feedback', $info);
            $new_entry_id = $db->lastInsertId();
            return $new_entry_id;
        }
    }

    /** ===================================
     * Delete note
     * ====================================    */
    function delete_feedback($args)
    {
        $db = new Db_helper();

        if ($args['id']) {
            $where = array('id' => $args['id']);

            if ($args['student_id']) {
                $where['rel_id'] = $args['student_id'];
            }
            if ($args['school_id']) {
                $where['usergroup'] = $args['school_id'];
            }
            $db->archive('ols_unit_feedback', $where);
        }
        return $args['id'];

    }


    /** ===================================
     * Delete Media
     * ====================================    */
    function delete_media($args)
    {
        $db = new Db_helper();

        if ($args['id']) {


            $media = $this->get_media(array('id' => $args['id']));

            //Delete the file
            $htdoc = str_replace("admin/", "", ABSPATH);
            $path = $htdoc . "online-learning/media/" . $_SESSION['usergroup'] . "/images/" . $media['path'];
            unlink($path);

            //Delete the record
            $where = array('id' => $args['id']);
            if ($args['school_id']) {
                $where['usergroup'] = $args['school_id'];
            } else {
                $where['usergroup'] = $_SESSION['usergroup'];
            }
            $db->delete('ols_media', $where);
        }
    }


    /** ===================================
     * Get Course Categories
     * ====================================    */
    function get_course_groups($args = array())
    {

        $dbh = get_dbh();
        $users = new Users;
        if ($args['school_id']) {
            $school_id_sql = "AND ols_course_groups.usergroup='" . $args['school_id'] . "'";
        }

        $query = "
			SELECT 
				*
			FROM
				ols_course_groups 
			WHERE
				1
				$school_id_sql
			ORDER BY id ASC";
        dev_debug($query);
        $sth = $dbh->prepare($query);
        $sth->execute();
        $results_list = array();
        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {

            $entry = array(
                'id' => $entry['id'],
                'title' => $entry['db21927']
            );
            $results_list[] = $entry;
        }

        if ($args['id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /** ===================================
     * Get Course Levels
     * ====================================    */
    function get_courses_levels($args = array())
    {

        $dbh = get_dbh();

        if ($args['school_id']) {
            $school_id_sql = "AND core_course_level.usergroup='" . $args['school_id'] . "'";
        }

        $query = "
			SELECT 
				*
			FROM
				core_course_level 
			WHERE
				1
				$school_id_sql
			ORDER BY id ASC";
        dev_debug($query);
        $sth = $dbh->prepare($query);
        $sth->execute();
        $results_list = array();
        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {

            $entry = array(
                'id' => $entry['id'],
                'title' => $entry['db343']
            );
            $results_list[] = $entry;
        }

        if ($args['id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Get Sponsors
     * ====================================    */
    function get_sponsors($args = array())
    {

        $dbh = get_dbh();
        $users = new Users;
        $select_sql = " * ";
        if ($args['count']) {
            $select_sql = " COUNT(*) as total_count ";
        }

        if ($args['school_id']) {
            $school_id_sql = " AND ols_sponsors.usergroup ='" . $args['school_id'] . "'";
        } else {
            $school_id_sql = " AND ols_sponsors.usergroup ='" . $_SESSION['usergroup'] . "'";
        }

        if ($args['sponsor_id'] || $args['id']) {
            $id_sql = " AND id='" . ($args['sponsor_id'] ?: $args['id']) . "'";
        }
        $query = "
			SELECT 
				$select_sql
			FROM
				ols_sponsors 
			WHERE
				1
				$school_id_sql
				$id_sql
			ORDER BY id ASC";

        $sth = $dbh->prepare($query);
        $sth->execute();
        dev_debug(__METHOD__ . ' - ' . $query);
        $results_list = array();
        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {
            if ($args['count']) {
                $results_list[] = $entry['total_count'];
            } else {
                $entry = array(
                    'id' => $entry['id'],
                    'company_name' => $entry['db21861'],
                    'address' => $entry['db21862'],
                    'post_code' => $entry['db21863'],
                    'telephone' => $entry['db21864'],
                    'vat_number' => $entry['db21865'],
                    'first_name' => $entry['db21867'],
                    'surname' => $entry['db21868'],
                    'email' => $entry['db21869'],
                    'telephone' => $entry['db21870'],
                    'mobile' => $entry['db21871'],
                );

                $results_list[] = $entry;
            }
        }

        if ($args['id'] || $args['count']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Get Access Types
     * ====================================    */
    function get_access_plan_types($args = array())
    {

        $dbh = get_dbh();
        $users = new Users;

        if ($args['id']) {
            $id_sql = " AND ols_access_plan_types.id='" . $args['id'] . "'";
        }

        $query = "
			SELECT 
				*
			FROM
				ols_access_plan_types 
			WHERE
				1
				$id_sql
			ORDER BY id ASC";


        $sth = $dbh->prepare($query);
        $sth->execute();
        $results_list = array();
        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {

            $entry = array(
                'id' => $entry['id'],
                'title' => $entry['db21857'],
                'description' => $entry['db21858'],
            );
            $results_list[] = $entry;
        }

        if ($args['id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /** ===================================
     * Get Statuses
     * ====================================    */
    function get_statuses($args = array())
    {

        $dbh = get_dbh();
        $users = new Users;

        if ($args['id']) {
            $id_sql = " AND ols_access_plan_status.id='" . $args['id'] . "'";
        }

        $query = "
			SELECT 
				*
			FROM
				ols_access_plan_status 
			WHERE
				1
				$id_sql
			ORDER BY id ASC";


        $sth = $dbh->prepare($query);
        $sth->execute();
        $results_list = array();
        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {

            $entry = array(
                'id' => $entry['id'],
                'title' => $entry['db25914'],
                'description' => $entry['db25915'],
            );
            $results_list[] = $entry;
        }

        if ($args['id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Get Quiz
     * ====================================    */
    function get_quiz($args = array())
    {

        $dbh = get_dbh();

        if (isset($args['id'])) {
            $id_sql = "AND ols_quizzes.id='" . $args['id'] . "'";
        }

        $query = "
			SELECT 
				*
			FROM
				ols_quizzes 
			WHERE
				1
				$id_sql
				AND (rec_archive is null or rec_archive='')
			ORDER BY id ASC";
        dev_debug($query);
        $sth = $dbh->prepare($query);
        $sth->execute();
        $results_list = array();
        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {

            $entry = array(
                'id' => $entry['id'],
                'title' => $entry['db22084'],
                'description' => $entry['db22085']
            );
            $results_list[] = $entry;
        }

        if ($args['id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Get Coupons
     * ====================================    */
    function get_coupons($args = array())
    {

        $dbh = get_dbh();

        if (isset($args['id'])) {
            $id_sql = "AND ols_coupons.id='" . $args['id'] . "'";
        }

        if (isset($args['code'])) {
            $code_sql = "AND db21962='" . $args['code'] . "'";
        }

        if (isset($args['plan_id'])) {
            //$plan_id_sql = "AND db21971='".$args['plan_id']."'";
            $plan_id_sql = "AND ols_access_plan_coupons.rel_id='" . $args['plan_id'] . "'";
            //$group_sql = "GROUP BY ols_access_plan_coupons.db21971";
        }


        $query = "
			SELECT 
			 *,ols_coupons.id as coupon_id
			FROM 
				ols_coupons
				LEFT JOIN ols_access_plan_coupons ON ols_access_plan_coupons.db21971 = ols_coupons.id
			WHERE 
				1
				$id_sql
				$code_sql
				$plan_id_sql
				$group_sql 
				";

        dev_debug($query);

        $sth = $dbh->prepare($query);
        $sth->execute();
        $results_list = array();
        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {

            $entry = array(
                'id' => $entry['coupon_id'],
                'title' => $entry['db21961'],
                'code' => $entry['db21962'],
                'description' => $entry['db21963'],
                'discount_type' => $entry['db21964'],
                'discount' => $entry['db21965'],
                'start_date' => $entry['db21966'],
                'expiry_date' => $entry['db21967'],
                'max_use' => $entry['db21968'],
                'use_count' => $entry['db21969'],
            );
            $results_list[] = $entry;
        }

        if ($args['id'] || $args['code']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /** ===================================
     * Validate Coupons
     * ====================================    */
    function validate_coupons($args = array())
    {
        $db = new Db_helper();

        $dbh = get_dbh();

        if (isset($args['id'])) {
            $id_sql = "AND ols_coupons.id='" . $args['id'] . "'";
        }

        if (isset($args['code'])) {
            $code_sql = "AND db21962='" . $args['code'] . "'";
        }

        if (isset($args['plan_id'])) {
            //$plan_id_sql = "AND db21971='".$args['plan_id']."'";
            $plan_id_sql = "AND ols_access_plan_coupons.rel_id='" . $args['plan_id'] . "'";
            //$group_sql = "GROUP BY ols_access_plan_coupons.db21971";
        }


        $query = "
			SELECT 
			 *,ols_coupons.id as coupon_id
			FROM 
				ols_coupons
				LEFT JOIN ols_access_plan_coupons ON ols_access_plan_coupons.db21971 = ols_coupons.id
			WHERE 
				1
				AND (ols_access_plan_coupons.rec_archive is null or ols_access_plan_coupons.rec_archive = '')
				AND (ols_coupons.rec_archive is null or ols_coupons.rec_archive = '')
				AND ols_access_plan_coupons.usergroup = $_SESSION[usergroup]
				AND ols_coupons.usergroup = $_SESSION[usergroup]
				$id_sql
				$code_sql
				$plan_id_sql
				$group_sql 
				";

        dev_debug($query);
        $sth = $dbh->prepare($query);
        $sth->execute();
        $results_list = array();
        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {
            $coupon_valid = "Valid";
            $coupon_invalid_text = '';
            $today = date("Y-m-d");
            //validate start date
            if (isset($entry['db22068']) && $entry['db22068'] != '' && $entry['db22068'] != '0000-00-00') {


                if ($entry['db22068'] > $today) {
                    //problem
                    $coupon_valid = "Invalid";
                    $coupon_invalid_text .= "$args[code] Coupon has not started yet. ";
                }
            }
            //validate end date
            //check to see if there is an expiry date
            if (isset($entry['db22069']) && $entry['db22069'] != '' && $entry['db22069'] != '0000-00-00') {
                //validate expiry date
                if ($entry['db22069'] < $today) {
                    $coupon_valid = "Invalid";
                    $coupon_invalid_text .= "$args[code] Coupon has expired. ";
                }
            }

            //see how many times the coupon has been used
            if (isset($entry['db22070']) && $entry['db22070'] != '') {
                $use_count = pull_field("ols_user_access_plan", "count(*)", "WHERE (rec_archive is null or rec_archive ='') AND usergroup = '$_SESSION[usergroup]' AND db22031='2' AND db22019='$args[plan_id]' AND db22049='$entry[coupon_id]'");
                if ($use_count >= $entry['db22070']) {
                    $coupon_valid = "Invalid";
                    $coupon_invalid_text .= "Maximum number of uses for $args[code] has been reached. ";
                }
            }

            $entry = array(
                'id' => $entry['coupon_id'],
                'title' => $entry['db21961'],
                'code' => $entry['db21962'],
                'description' => $entry['db21963'],
                'discount_type' => $entry['db21964'],
                'discount' => $entry['db21965'],
                'start_date' => $entry['db21966'],
                'expiry_date' => $entry['db21967'],
                'max_use' => $entry['db21968'],
                'use_count' => $entry['db21969'],
                'valid_coupon' => $coupon_valid,
                'invalid_coupon_text' => $coupon_invalid_text,

            );
            $results_list[] = $entry;
        }

        if ($args['id'] || $args['code']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /** ===================================
     * Get FAQs
     * ====================================    */
    function get_faqs($args = array())
    {

        $dbh = get_dbh();

        if (isset($args['id'])) {
            $id_sql = "AND ols_faqs.id='" . $args['id'] . "'";
        }

        if ($args['school_id']) {
            $school_id_sql = "AND ols_faqs.usergroup='" . $args['school_id'] . "'";
        }

        $query = "
			SELECT 
				*
			FROM
				ols_faqs 
			WHERE
				1
				$id_sql
				$school_id_sql
				AND (rec_archive is null or rec_archive='')
			ORDER BY db22076 ASC";

        dev_debug($query);
        $sth = $dbh->prepare($query);
        $sth->execute();
        $results_list = array();
        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {

            $entry = array(
                'id' => $entry['id'],
                'title' => $entry['db22076'],
                'description' => $entry['db22077']
            );
            $results_list[] = $entry;
        }

        if ($args['id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Insert or Update Comment
     * ====================================    */
    function insert_update_comment($args)
    {
        global $school_info;
        $db = new Db_helper();
        $dbh = get_dbh();
        if (!$args['description'] || !$args['entry_id']) {
            return false;
        }

        $info = array();
        if ($args['student_id']) {
            $info['rel_id'] = $args['student_id'];
        }
        //if($args['student_id']){ $info['rec_id'] = $args['student_id']; }
        if ($args['entry_id']) {
            $info['db25514'] = $args['entry_id'];
        }
        if ($args['description']) {
            $info['db22082'] = $args['description'];
        }
        if ($args['category']) {
            $info['db22083'] = $args['category'];
        }
        if ($args['internal_comments']) {
            $info['db57187'] = $args['internal_comments'];
        }

        if ($args['id']) {
            $where = array('rel_id' => $args['id']);
            $db->update('ols_support_comments', $info, $where);
            return 1;
        } else {

            //Get the ticket info
            $query = "SELECT ols_support.*, form_users.db106, form_users.db111,db112,db119 FROM ols_support
	      LEFT JOIN form_users ON form_users.id = ols_support.rec_id
	      WHERE 1 AND ols_support.id='" . $args['entry_id'] . "' AND (ols_support.rec_archive is null or ols_support.rec_archive='') LIMIT 1";
            $stmt = $dbh->prepare($query);
            $stmt->execute();
            $ticket_info = $stmt->fetch(PDO::FETCH_ASSOC);

            //Check if it was active
            if ($ticket_info['db22078'] == "Completed") {
                $dbh = get_dbh();
                $sql = "UPDATE ols_support SET db22078 = 'Open' WHERE id = '" . $args['entry_id'] . "'";
                $sth = $dbh->prepare($sql);
                $sth->execute();
            }

            //If Admin replys then an email student
            if ($_SESSION['ulevel'] == "2") {
                $subject = "Alert: A new support ticket reply";
                if ($school_info['id'] == "42") {
                    $ticket_link = $school_info["href"] . "/online-learning/support/" . $ticket_info['username_id'];
                } else {
                    $ticket_link = $school_info["href"] . "/new/support_tickets/info/" . $ticket_info['username_id'];
                }
                $message = "Dear " . $ticket_info['db106'] . " " . $ticket_info['db111'] . ' <br/><br/>

	      	This is an automatically generated email to let you know that we have replied to your ticket</br>
	      	----------------------------------------------------</br>
	      	To view the ticket, please log in to your account at</br>
	      	<a href="https://' . $ticket_link . '">' . $ticket_link . "</a></br></br>

	      	Kind regards</br>
	      	" . $school_info['title'] . "</br>
	      	</br></br></br>
	      	";

                $email_args = array(
                    'from' => $school_info['sending_email_address'],
                    'to' => $ticket_info['db119'],
                    'subject' => $subject,
                    'text' => $message,
                    'html' => $message,
                    'category' => "Support Issue",
                    // 'recipient_id'=> random()
                );

                $emails = new Emails;
                $emails->send($email_args);
            }

            //If student replies - send email to admin
            if ($ticket_info['rec_id'] == $_SESSION['uid']) {
                $subject = "Alert: A new support ticket reply";
                $ticket_link = $school_info["href"] . "/admin/online_courses_support/" . $ticket_info['id'];
                $message = 'Dear Admin <br/><br/>

	     		This is an automatically generated email to let you know there is a new reply on a ticket</br>
	     		----------------------------------------------------</br>
	     		To view the ticket, please log in to your account at</br>
	     		<a href="' . $ticket_link . '">' . $ticket_link . "</a></br></br>

	     		Kind regards</br>
	     		" . $school_info['title'] . "</br>
	     		</br></br></br>
	     		";
                $email_args = array(
                    'from' => $school_info['sending_email_address'],
                    'to' => $school_info['receiving_email_address'] . ",<EMAIL>",
                    'subject' => $subject,
                    'text' => $message,
                    'html' => $message,
                    'category' => "Support Issue",
                );

                $emails = new Emails;
                $emails->send($email_args);
            }


            $info['rel_id'] = $args['student_id'];
            $info['db59914'] = '';
            $db->system_table_insert_or_update('ols_support_comments', $info);
            $new_entry_id = $db->lastInsertId();
            return $new_entry_id;
        }
    }


    /** ===================================
     * Insert or Update support
     * ====================================    */
    function insert_update_ticket($args)
    {
        $db = new Db_helper();

        $info = array();
        if (!empty($args['student_id'])) {
            $info['rel_id'] = $args['student_id'];
        }
        if (!empty($args['title'])) {
            $info['db22071'] = $args['title'];
        }
        if (!empty($args['description'])) {
            $info['db22072'] = $args['description'];
        }
        if (!empty($args['category'])) {
            $info['db22073'] = $args['category'];
        }
        if (!empty($args['priority'])) {
            $info['db22074'] = $args['priority'];
        }
        if (!empty($args['status'])) {
            $info['db22078'] = $args['status'];
        }
        if (!empty($args['rec_id'])) {
            $info['rec_id'] = $args['rec_id'];
        }

        if (!empty($args['id'])) {
            $where = array('id' => $args['id']);
            $db->update('ols_support', $info, $where);
            $return = $args['id'];

        } else {
            $db->system_table_insert_or_update('ols_support', $info);
            $new_entry_id = $db->lastInsertId();
            $return = $new_entry_id;
            $full_name = $email = '';
            if (!empty($args['student_id'])) {
                list($name, $surname, $email) = explode(',', pull_field("core_students", "concat_ws(',',db39, db40, db764)", "WHERE id =$args[student_id]"));
                $full_name = $name . " " . $surname;
            }

            if (!empty($args['partner_ticket'])) {
                list($name, $surname, $email) = explode(',', pull_field("form_users", "concat_ws(',',db106, db111, db119)", "WHERE id =" . $_SESSION['uid']));
                $full_name = $name . " " . $surname;
            }

            $title = $args['title'];
            $enquiry = $args['description'];
            $priority = $args['priority'];
            if (is_int($priority)) {
                $priority = pull_field("form_support_priority", "db101", "WHERE id = $priority");
            }

            list($ticket_owner_subdomain, $domain_email_address) = explode(',', pull_field("form_schools", "concat_ws(',',db985,db1117)", "WHERE id=$_SESSION[usergroup]"));
            $members = pull_field("form_internal_teams", "db50639", "WHERE db50622=23 AND usergroup={$_SESSION['usergroup']}");
            $svn = explode(".", $_SERVER['SERVER_NAME']);
            $server_domain_name = $svn[1] . '.' . $svn[2] . '.' . $svn[3];
            $server_domain_name = rtrim($server_domain_name, ".");
            $subject = "Alert: A new support ticket has been submitted on https://$ticket_owner_subdomain.$server_domain_name";

            $dbh = get_dbh();
            $sql = "SELECT db119 as `email` FROM form_users WHERE id IN ($members)";
            $sth = $dbh->prepare($sql);
            $sth->execute();
            $persons = $sth->fetchAll(2);
            array_unshift($persons, ['email' => $domain_email_address]);
            $this->dev_debug("HERE 2".json_encode($persons));
            foreach ($persons as $person) {
                // The message
                $message = "Dear Admin<br/></br>
		
                    This is an automatically generated email to let you know the following</br>
                    ----------------------------------------------------</br>
                    Name = $full_name</br>
                    Email = $email</br>
                    Subject = $title</br>
                    Description = $enquiry</br>
                    Priority = $priority</br></br>
                    
                    ----------------------------------------------------</br>
                    To view the ticket, please log in to AppliCatalyst at</br>
                    https://$ticket_owner_subdomain.$server_domain_name/admin/online_courses_support/$return</br></br>
                            
                    Kind regards</br>
                    Automatic Email Generator</br>
                    --------------------------------------</br></br></br>
                ";


                $email_args = array(
                    'to' => $person['email'],
                    'subject' => $subject,
                    'text' => $message,
                    'html' => nl2br($message),
                    'category' => "Support Issue",
                    // 'recipient_id'=> random()
                );

                $emails = new Emails;
                $emails->send($email_args);

            }
        }

        return $return;

    }


    /** ===================================
     * Insert or Update FAQs
     * ====================================    */
    function insert_update_faqs($args)
    {
        $db = new Db_helper();

        $info = array();
        if ($args['title']) {
            $info['db22076'] = $args['title'];
        }
        if ($args['description']) {
            $info['db22077'] = $args['description'];
        }

        if ($args['student_id']) {
            $where = array('rel_id' => $args['student_id']);
            $db->update('ols_faqs', $info, $where);
            return 1;
        } else {
            $db->system_table_insert_or_update('ols_faqs', $info);
            $new_entry_id = $db->lastInsertId();
            return $new_entry_id;
        }
    }


    /** ===================================
     * Update or Insert Unit
     * ====================================    */
    function update_or_insert_unit($args)
    {
        $db = new Db_helper();

        $unit_info = array();
        if ($args['module_id']) {
            $unit_info['rel_id'] = $args['module_id'];
        }
        if (array_key_exists('title', $args)) {
            $unit_info['db21923'] = $args['title'];
        }
        if (array_key_exists('description', $args)) {
            $unit_info['db21924'] = $args['description'];
        }
        if (array_key_exists('mp3_file', $args)) {
            $unit_info['db21925'] = $args['mp3_file'];
        }
        if (array_key_exists('publish_date', $args)) {
            $unit_info['db21974'] = $args['publish_date'];
        }
        if (array_key_exists('visibility', $args)) {
            $unit_info['db21973'] = $args['visibility'];
        }
        if (array_key_exists('status', $args)) {
            $unit_info['db21972'] = $args['status'];
        }
        if (array_key_exists('quiz_id', $args)) {
            $unit_info['db24588'] = $args['quiz_id'];
        }
        if (array_key_exists('quiz_mandetory', $args)) {
            $unit_info['db25513'] = $args['quiz_mandetory'];
        }

        if (array_key_exists('questionnaire_form_id', $args)) {
            $unit_info['db25552'] = $args['questionnaire_form_id'];
        }
        if (array_key_exists('questionnaire_mandetory', $args)) {
            $unit_info['db25553'] = $args['questionnaire_mandetory'];
        }
        if (array_key_exists('quiz_answerable_multiple_times', $args)) {
            $unit_info['db41795'] = $args['quiz_answerable_multiple_times'];
        }
        if (array_key_exists('show_user_answer', $args)) {
            $unit_info['db41796'] = $args['show_user_answer'];
        }

        if (array_key_exists('progress_certificate', $args)) {
            if ($args['progress_certificate']) {
                $unit_info['db66584'] = $args['progress_certificate'];
            } else {
                $unit_info['db66584'] = "";
            }
        }
        if (array_key_exists('progress_certificate_title', $args)) {
            $unit_info['db66585'] = $args['progress_certificate_title'];
        }


        if (array_key_exists('trail', $args)) {

            if ($args['trail']) {
                $unit_info['db21959'] = $args['trail'];
            } else {
                $unit_info['db21959'] = "";
            }
        }

        if (array_key_exists('mp3_auto_play', $args)) {
            if ($args['mp3_auto_play']) {
                $unit_info['db21942'] = $args['mp3_auto_play'];
            } else {
                $unit_info['db21942'] = "";
            }
        }

        if ($args['id'] || $args['username_id']) {

            if ($args['id']) {
                $where = array('id' => $args['id']);
            } elseif ($args['username_id']) {
                $where = array('username_id' => $args['username_id']);
            }

            $db->update('ols_course_units', $unit_info, $where);
            $entry_id = $args['id'];
        } else {
            $unit_info['db21939'] = pull_field("ols_course_units", "db21939", "WHERE rel_id='{$args['module_id']}' ORDER BY IFNULL(CAST(db21939 AS SIGNED),ols_course_units.id) DESC LIMIT 1") + 1;
            $db->system_table_insert_or_update('ols_course_units', $unit_info);
            $entry_id = $db->lastInsertId();
            //return $this->get_units(array('id'=>$entry_id,'show_hidden_units'=>1));
            //return $entry_id;
        }


        //update  slug ensureing it ends with "-$entry_id" if "-$entry_id" already exists in slug do not add it
        if (!empty($args['module_slug']) && $entry_id) {
            $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $args['module_slug']), '-')) ;
            $slug = iconv('UTF-8', 'ASCII//TRANSLIT//IGNORE', $slug);
            if (!str_ends_with($slug, "-$entry_id")) {
                $slug .= "-$entry_id";
            }

            $dbh = get_dbh();
            $stmt = $dbh->prepare("UPDATE ols_course_modules SET db360683 = :slug WHERE id = :id");
            $stmt->execute([':slug' => $slug, ':id' => $entry_id]);
        }






          return $entry_id;

    }


    /** ===================================
     * Update or Insert Unit
     * ====================================    */
    function update_or_insert_user_access_plan($args)
    {
        $db = new Db_helper();

        $entry_info = array();
        if (array_key_exists('student_id', $args)) {
            $entry_info['rel_id'] = $args['student_id'];
        }
        if (array_key_exists('plan_id', $args)) {
            $entry_info['db22019'] = $args['plan_id'];
            $entry_info['db272531'] = pull_field("ols_access_plans", "db21880", "WHERE id =$args[plan_id]");
        }
        if (array_key_exists('coupon_id', $args)) {
            $entry_info['db22049'] = $args['coupon_id'];
        }
        if (array_key_exists('status', $args)) {
            $entry_info['db22031'] = $args['status'];
        }
        if (array_key_exists('invoice_id', $args)) {
            $entry_info['db22059'] = $args['invoice_id'];
        }
        if (array_key_exists('sponsor_validation_status', $args)) {
            $entry_info['db22058'] = $args['sponsor_validation_status'];
        }
        if (array_key_exists('supplementary_question_status', $args)) {
            $entry_info['db22056'] = $args['supplementary_question_status'];
        }
        if (array_key_exists('challenge_question_status', $args)) {
            $entry_info['db22057'] = $args['challenge_question_status'];
        }
        if (array_key_exists('challenge_group_id', $args)) {
            $entry_info['db32191'] = $args['challenge_group_id'];
        }


//set the challenge answer group


        if ($args['action'] == "update") {
            if ($args['id']) {
                $where = array('id' => $args['id']);
                $mywhere = "WHERE id = $args[id]";
            } elseif ($args['username_id']) {
                $where = array('username_id' => $args['username_id']);
                $mywhere = "WHERE username_id = '$args[username_id]'";
            } elseif ($args['student_id']) {
                $where = array('rel_id' => $args['student_id'], 'db22019' => $args['plan_id']);
                $mywhere = "WHERE rel_id='$args[student_id]' AND db22019='$args[plan_id]'";
            }

            if (array_key_exists('status', $args)) {
                //check if old status is same as new if not update the tracker

                $old_status = pull_field("ols_user_access_plan", "db22031", $mywhere);
                if ($old_status != $args['status']) {
                    //change the tracker
                    $db->system_table_insert_or_update('ols_user_ap_status_track', array('rel_id' => pull_field("ols_user_access_plan", "id", $mywhere), 'db25614' => '$args[status]'));
                }
            }
            $db->update('ols_user_access_plan', $entry_info, $where);
            return 1;
        } else {
            $db->system_table_insert_or_update('ols_user_access_plan', $entry_info);
            $entry_id = $db->lastInsertId();
            $db->system_table_insert_or_update('ols_user_ap_status_track', array('rel_id' => $entry_id, 'db25614' => '1'));


            return $this->get_units(array('id' => $entry_id));
        }
    }


    /** ===================================
     * Create Student Account
     * ====================================    */
    function create_online_student_account($args = array())
    {
        $db = new Db_helper();

        $users = new Users;

        $user_info = array(
            'school_id' => $args['school_id'],
            'first_name' => $args['first_name'],
            'middle_name' => null,
            'last_name' => $args['last_name'],
            'email' => $args['email'],
            'type_id' => 4,
            'password' => $args['password'],
            'institution' => null,
            'application_stage' => null,
            'gender' => null,
            'level_of_entry' => null,
            'date_of_birth' => null,
            'source' => 'Direct',
            'course_id' => null,
            'country' => null,
            'telephone' => null,
            'internal_reference' => null,
            'cohort' => null,
            'cohort_intake' => null, #double check
            'ucas' => null,
            'application_route' => null,
            'send_ucas_letter' => null,
            'create_student_account' => true,
        );

        $new_user_info = $users->create($user_info);
        if ($args['send_welcome_email']) {
            $email_args = array('to' => $args['email'], 'first_name' => $user_info['first_name']);
            $this->send_student_welcome_email($email_args);
        }
        // login

        // ALL IS WELL PROCEED

        // LOAD SESSION WITH USER VARIABLES
        //$_SESSION['loggedin']= "yes";
        //$_SESSION['user']= $user_email;
        $_SESSION['uid'] = $new_user_info['user_id'];
        $_SESSION['access'] = $args['school_id'];
        $_SESSION['ulevel'] = 4;
        $_SESSION['usergroup'] = $args['school_id'];
        $_SESSION['activation'] = '1';
        $_SESSION['salt'] = md5(random());// generate a random salt for this session
        $_SESSION['system'] = 'lite';

        // GET THE ACCESS MODULE AND COLLECT CURRENT USERS SETTINGS

        // LOAD SESSION MODULE ACCESS VARIABLES
        $_SESSION['loggedin'] = "yes";
        $_SESSION['user'] = $args['email'];
        $_SESSION['name'] = "$args[first_name]";
        $_SESSION['surname'] = "$args[last_name]";
        $_SESSION['fullname'] = "$args[first_name] $args[last_name]";

        // update last logged in timestamp
        $date_today = date('Y-m-d H:i:s');
        $track_page_url = curPageURL();
        $check_last_login = pull_field("form_last_login", "count(*)", "WHERE rec_id='$_SESSION[uid]'");

        if ($check_last_login > "0") {
            $form_last_login_id = pull_field("form_last_login", "id", "WHERE rec_id='$_SESSION[uid]' ORDER BY id DESC LIMIT 1");
            $fll_info = array('id' => $form_last_login_id, 'rel_id' => $new_user_info['student_id'], 'db1276' => $date_today, 'db1275' => $track_page_url);
        } else {
            $fll_info = array('rel_id' => $new_user_info['student_id'], 'db1276' => $date_today, 'db1275' => $track_page_url);
        }
        $db->system_table_insert_or_update('form_last_login', $fll_info);
        // end

        return $new_user_info;
    }

    /** ===================================
     * Get Modules
     * ====================================    */
    function get_modules($args = array())
    {
        $dbh = get_dbh();
        $db = new Db_helper();

        if ($args['id']) {
            $id_sql = "AND ols_course_modules.id='" . $args['id'] . "'";
        }

        if ($args['course_id']) {
            $course_id_sql = "AND ols_course_modules.rel_id='" . $args['course_id'] . "'";
        }

        if ($args['school_id']) {
            $school_id_sql = "AND ols_course_modules.usergroup='" . $args['school_id'] . "'";
        }

        if ($args['username_id']) {
            $username_id_sql = "AND ols_course_modules.username_id='" . $args['username_id'] . "'";
        }

        $query = "
			SELECT 
				*
			FROM
				ols_course_modules 
			WHERE
				1
				$id_sql
				$course_id_sql
				$school_id_sql
				$username_id_sql
				AND (rec_archive is null or rec_archive='')
			ORDER BY cast(db21921 as unsigned) ASC";

        $results = $db->query($query);
        $i = 0;
        foreach ($results as $row) {


            //Get Units
            $units_args = array('module_id' => $row['id'], 'student_id' => $args['student_id'], 'no_unit_description' => $args['no_unit_description'], 'raw_html' => $args['raw_html'], 'show_hidden_units' => $args['show_hidden_units']);

            if (empty($args['hide_units'])) {
                if (isset($args['no_quiz_information'])) {
                    $units_args['no_quiz_information'] = $args['no_quiz_information'];
                }
                // if($args['single_unit_description']){
                // 	$units_args['id'] = $args['single_unit_description'];
                // }
                $units = $this->get_units($units_args);
            } else {
                $units = [];
            }


            //Check if module is completed
            $units_completed_count = 0;
            foreach ($units as $unit) {
                if ($unit['completed_date']) {
                    $units_completed_count++;
                }
            }

            if (!empty($units) && ($units_completed_count == count($units)) && (count($units))) {
                $completed_module = 1;
            } else {
                $completed_module = 0;
            }

            //Calculate correct answers in unit
            $correct_questions = 0;
            $total_questions = 0;
            foreach ($units as $unit) {
                if (!empty($unit['quiz_results'])) $total_questions = $total_questions + $unit['quiz_results']['total_questions'];
                if (!empty($unit['quiz_results'])) $correct_questions = $correct_questions + $unit['quiz_results']['correct_responses'];
            }

            $quiz_results = array(
                'total_questions' => $total_questions,
                'correct_responses' => $correct_questions,
                'percentage' => $total_questions == 0 ? 0 : number_format(($correct_questions / $total_questions) * 100, 0)
            );

            $results_list[] = array(
                "id" => $row['id'],
                "username_id" => $row['username_id'],
                "course_id" => $row['rel_id'],
                "title" => $row['db21919'],
                "description" => $row['db21920'],
                "order" => intval((isset($row['db21921']) && !empty($row['db21921'])) ? $row['db21921'] : $i),
                "completed_module" => $completed_module,
                "completion_percentage" => empty($units) ? 0 : number_format(($units_completed_count / count($units)) * 100, 0),
                "quiz_results" => $quiz_results,
                "units" => $units,
                "href" => $this->home_url("course/") . $this->ensure_slug_matches_title($row['rel_id'] , 'course') . "/" . $this->ensure_slug_matches_title($row['id'], 'module')  . "/" . $this->ensure_slug_matches_title($units[0]['id'], 'unit') ,
            );
            $i++;
        }

        if ($args['id'] || $args['title'] || $args['username_id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /** ===================================
     * Update or Insert Module
     * ====================================    */
    function update_or_insert_module($args)
    {
        $db = new Db_helper();

        $module_info = array();
        if ($args['module_id']) {
            $module_info['rel_id'] = $args['module_id'];
        }
       
        if (array_key_exists('title', $args)) {
            $module_info['db21919'] = $args['title'];
        }
        if (array_key_exists('course_id', $args)) {
            $module_info['rel_id'] = $args['course_id'];
        }
        if (array_key_exists('description', $args)) {
            $module_info['db21920'] = $args['description'];
        }

        if ($args['action'] == "update") {

            if ($args['id']) {
                $where = array('id' => $args['id']);
            } elseif ($args['username_id']) {
                $where = array('username_id' => $args['username_id']);
            }

            $db->update('ols_course_modules', $module_info, $where);
           $entry_id = $args['id'];
        } else {
            $db->system_table_insert_or_update('ols_course_modules', $module_info);
            $entry_id = $db->lastInsertId();
           
        }


        //update  slug ensureing it ends with "-$entry_id" if "-$entry_id" already exists in slug do not add it
        if (!empty($args['module_slug']) && $entry_id) {
            $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $args['module_slug']), '-')) ;
            $slug = iconv('UTF-8', 'ASCII//TRANSLIT//IGNORE', $slug);
            if (!str_ends_with($slug, "-$entry_id")) {
                $slug .= "-$entry_id";
            }

            $dbh = get_dbh();
            $stmt = $dbh->prepare("UPDATE ols_course_modules SET db360683 = :slug WHERE id = :id");
            $stmt->execute([':slug' => $slug, ':id' => $entry_id]);
        }




         return $this->get_modules(array('id' => $entry_id));
    }


    /** ===================================
     * Delete Online Course
     * ====================================    */
    function delete_online_course($args)
    {
        $db = new Db_helper();

        if ($args['id']) {
            $where = array('id' => $args['id']);
            if ($args['school_id']) {
                $where['usergroup'] = $args['school_id'];
            }
            $db->archive('ols_online_courses', $where);
        }

        if ($args['username_id']) {
            $where = array('id' => $args['username_id']);
            if ($args['school_id']) {
                $where['usergroup'] = $args['school_id'];
            }
            $db->archive('ols_online_courses', $where);
        }
    }


    /** ===================================
     * Copy Online Course
     * ====================================    */
    function copy_online_course($args)
    {
        $dbh = get_dbh();

        $form_templates = new FormTemplates;
        $query = "select * from ols_online_courses where id = $args[id]";
        dev_debug($query);
        $sth = $dbh->prepare($query);
        $sth->execute();
        $course_info = $sth->fetch(PDO::FETCH_ASSOC);

        $course_copy_args = array();

        $course_copy_args['title'] = $course_info['db21909']."_COPY"; //name of course
        $course_copy_args['alternative_title'] = $course_info['db260924']."_COPY"; // alternative name of course

        if (!empty($course_info['db236705'])) {
            $course_copy_args['course_link'] = $course_info['db236705'] ;
        }
        if (!empty($course_info['db21914'])) {
            $course_copy_args['colour'] = $course_info['db21914'] ;
        }
        if (!empty($course_info['db21914'])) {
            $course_copy_args['colour'] = $course_info['db21914'] ;
        }
        if (!empty($course_info['db25550'])) {
            $course_copy_args['pre_questions_form_id'] = $course_info['db25550'] ;
        }
        if (!empty($course_info['db25551'])) {
            $course_copy_args['post_questions_form_id'] = $course_info['db25551'] ;
        }
        if (!empty($course_info['db21929'])) {
            $course_copy_args['group'] = $course_info['db21929'] ;
        }
        if (!empty($course_info['db253415'])) {
            $course_copy_args['category'] = $course_info['db253415'] ;
        }
        if (!empty($course_info['db37364'])) {
            $course_copy_args['course_level'] = $course_info['db37364'] ;
        }
        if (!empty($course_info['db21913'])) {
            $course_copy_args['excerpt'] = $course_info['db21913'] ;
        }
        if (!empty($course_info['db21915'])) {
            $course_copy_args['next_unit_visibility'] = $course_info['db21915'] ;
        }
        if (!empty($course_info['db21910'])) {
            $course_copy_args['description'] = $course_info['db21910'] ;
        }
        if (!empty($course_info['db25543'])) {
            $course_copy_args['featured'] = $course_info['db25543'] ;
        }
        if (!empty($course_info['db21950'])) {
            $course_copy_args['show_support_on_each_unit'] = $course_info['db21950'] ;
        }
        if (!empty($course_info['db66669'])) {
            $course_copy_args['show_feedback_on_each_unit'] = $course_info['db66669'] ;
        }
        if (!empty($course_info['db30582'])) {
            $course_copy_args['publish'] = $course_info['db30582'] ;
        }
        if (!empty($course_info['db309757'])) {
            $course_copy_args['rm_from_library'] = $course_info['db309757'] ;
        }
        if (!empty($course_info['db31066'])) {
            $course_copy_args['course_language'] = $course_info['db31066'] ;
        }
        if (!empty($course_info['db251258'])) {
            $course_copy_args['order'] = $course_info['db251258'] ;
        }
        if (!empty($course_info['db253412'])) {
            $course_copy_args['parent_course'] = $course_info['db253412'] ;
        }
        if (!empty($course_info['db21940'])) {
            $course_copy_args['download_certificate'] = $course_info['db21940'] ;
        }
        if (!empty($course_info['db26246'])) {
            $course_copy_args['questionnaire_type'] = $course_info['db26246'] ;
        }
        if (!empty($course_info['db26247'])) {
            $course_copy_args['closeness_questions'] = $course_info['db26247'] ;
        }
        if (!empty($course_info['db26248'])) {
            $course_copy_args['conflict_questions'] = $course_info['db26248'] ;
        }
        if (!empty($course_info['db26238'])) {
            $course_copy_args['score_1_text'] = $course_info['db26238'] ;
        }
        if (!empty($course_info['db26239'])) {
            $course_copy_args['score_2_text'] = $course_info['db26239'] ;
        }
        if (!empty($course_info['db26240'])) {
            $course_copy_args['score_3_text'] = $course_info['db26240'] ;
        }
        if (!empty($course_info['db26241'])) {
            $course_copy_args['score_4_text'] = $course_info['db26241'] ;
        }
        if (!empty($course_info['db26242'])) {
            $course_copy_args['score_5_text'] = $course_info['db26242'] ;
        }
        if (!empty($course_info['db26243'])) {
            $course_copy_args['score_6_text'] = $course_info['db26243'] ;
        }
        if (!empty($course_info['db26244'])) {
            $course_copy_args['score_7_text'] = $course_info['db26244'] ;
        }
        if (!empty($course_info['db26245'])) {
            $course_copy_args['score_8_text'] = $course_info['db26245'] ;
        }
        if (!empty($course_info['db250853'])) {
            $course_copy_args['text_before_questionnaire'] = $course_info['db250853'] ;
        }
        if (!empty($course_info['db253409'])) {
            $course_copy_args['image'] = $course_info['db253409'] ;
        }

        if (!empty($course_info['db254762'])) {
            $course_copy_args['thumbnail'] = $course_info['db254762'] ;
        }

        $course_copy_args['action'] = "insert";
        $new_course =  $this->update_or_insert_course($course_copy_args);
        $new_course_id = $new_course['id'];
        //db25550 pre questionnaire
        //db25551 post questionnaire
        if (!empty($course_info['db25550'])) {
            $page_args = array("id" => $course_info['db25550'], "system_abbrv" => "inst", "school_id" => $_SESSION['usergroup']);
            $pre_questionnaire = $form_templates->get($page_args);

            //Create the form
            $form_args = array('title' => "Course " . $new_course_id . " Pre questions", 'category_id' => 8, "system_abbrv" => 'inst');
            $new_pre_questionnaire_id = $form_templates->insert_update_form($form_args);

            //udpate the course with the new form id
            $courses_args = array('id' => $new_course_id, 'pre_questions_form_id' => $new_pre_questionnaire_id, 'action' => 'update');
            $this->update_or_insert_course($courses_args);

            $questions  = $pre_questionnaire['fields'];
            foreach ($questions as $field) {
                $field_info = array(
                    'title' => $field['title'],
                    'type' => $field['type'],
                    'box_size' => $field['box_size'],
                    'required' => $field['required'],
                    'read_only' => $field['read_only'],
                    'disabled' => $field['disabled'],
                    'read_only_for_applicants' => $field['read_only_for_applicants'],
                    'disabled_when_editing' => $field['disabled_when_editing'],
                    'hide_when_adding' => $field['hide_when_adding'],
                    'hide_when_editing' => $field['hide_when_editing'],
                    'hide_when_editing_for_applicants' => $field['hide_when_editing_for_applicants'],
                    'hide_when_record_equal_user_id' => $field['hide_when_record_equal_user_id'],
                    'default' => $field['default'],
                    'form_id' => $new_pre_questionnaire_id,
                    'order' => $field['order'],
                    'delete' => $field['deleted'],
                    'locked' => $field['locked'],
                    /*'options' => $field['options'],*/
                    'conditional' => $field['conditional'],
                    'description' => $field['description'],
                    'review_order' => $field['review_order'],
                    'decision' => $field['decision'],
                    "system_abbrv" => 'inst',
                    'extra_options' => $field['extra_options'],
                    'default_state' => $field['default_state'],

                    'archived' => $field['archived']
                );


                $field_info['answer'] = $field['answer'];
                $field_info['answer_hint'] = $field['answer_hint'];
                $field_info['answer_reason'] = $field['answer_reason'];
                $field_info['incorrect_reason'] = $field['incorrect_reason'];
                $field_info['weighting'] = $field['weighting'];
                $field_info['weighting_single'] = $field['weighting'];
                $field_info['usergroup'] = $field['usergroup'];

                $fields = new Fields;
                $fields->insert_update_field($field_info);
            }

            $course_copy_args['db25550'] = $new_pre_questionnaire_id;
        }
        if (!empty($course_info['db25551'])) {
            $page_args = array("id" => $course_info['db25551'], "system_abbrv" => "inst", "school_id" => $_SESSION['usergroup']);
            $post_questionnaire = $form_templates->get($page_args);

            //Create the form
            $form_args = array('title' => "Course " . $new_course_id . " Post questions", 'category_id' => 9, "system_abbrv" => 'inst');
            $new_post_questionnaire_id = $form_templates->insert_update_form($form_args);

            //udpate the course with the new form id
            $courses_args = array('id' => $new_course_id, 'post_questions_form_id' => $new_post_questionnaire_id, 'action' => 'update');
            $this->update_or_insert_course($courses_args);

            $questions  = $post_questionnaire['fields'];
            foreach ($questions as $field) {
                $field_info = array(
                    'title' => $field['title'],
                    'type' => $field['type'],
                    'box_size' => $field['box_size'],
                    'required' => $field['required'],
                    'read_only' => $field['read_only'],
                    'disabled' => $field['disabled'],
                    'read_only_for_applicants' => $field['read_only_for_applicants'],
                    'disabled_when_editing' => $field['disabled_when_editing'],
                    'hide_when_adding' => $field['hide_when_adding'],
                    'hide_when_editing' => $field['hide_when_editing'],
                    'hide_when_editing_for_applicants' => $field['hide_when_editing_for_applicants'],
                    'hide_when_record_equal_user_id' => $field['hide_when_record_equal_user_id'],
                    'default' => $field['default'],
                    'form_id' => $new_post_questionnaire_id,
                    'order' => $field['order'],
                    'delete' => $field['deleted'],
                    'locked' => $field['locked'],
                    /*'options' => $field['options'],*/
                    'conditional' => $field['conditional'],
                    'description' => $field['description'],
                    'review_order' => $field['review_order'],
                    'decision' => $field['decision'],
                    "system_abbrv" => 'inst',
                    'extra_options' => $field['extra_options'],
                    'default_state' => $field['default_state'],

                    'archived' => $field['archived']
                );


                $field_info['answer'] = $field['answer'];
                $field_info['answer_hint'] = $field['answer_hint'];
                $field_info['answer_reason'] = $field['answer_reason'];
                $field_info['incorrect_reason'] = $field['incorrect_reason'];
                $field_info['weighting'] = $field['weighting'];
                $field_info['weighting_single'] = $field['weighting'];
                $field_info['usergroup'] = $field['usergroup'];

                $fields = new Fields;
                $fields->insert_update_field($field_info);
            }

            $course_copy_args['db25551'] = $new_post_questionnaire_id;
        }



        //next copy the modules and units
        $course_modules = $this->get_modules(['course_id' => $course_info['id']]);
        foreach ($course_modules as $course_module) {
            //copy the module
            $module_copy_args = array();

            $module_copy_args['title'] = $course_module['title'] ;
            $module_copy_args['course_id'] = $new_course_id;
            $module_copy_args['description'] = $course_module['description'] ;
            $module_copy_args['action'] = "insert";
            $new_module =  $this->update_or_insert_module($module_copy_args);
            $new_module_id = $new_module['id'];
            $module_units = $course_module['units'];
            if (!empty($module_units)) {
                foreach ($module_units AS $module_unit) {
                    $unit_copy_args = array();
                    $unit_copy_args['module_id'] = $new_module_id;


                    if (!empty($module_unit['title'])) {
                        $unit_copy_args['title'] = $module_unit['title'];
                    }
                    if (!empty($module_unit['description'])) {
                        $unit_copy_args['description'] = $module_unit['description'];
                    }
                    if (!empty($module_unit['mp3_file'])) {
                        $unit_copy_args['mp3_file'] = $module_unit['mp3_file'];
                    }
                    if (!empty($module_unit['publish_date'])) {
                        $unit_copy_args['publish_date'] = $module_unit['publish_date'];
                    }
                    if (!empty($module_unit['visibility'])) {
                        $unit_copy_args['visibility'] = $module_unit['visibility'];
                    }
                    if (!empty($module_unit['status'])) {
                        $unit_copy_args['status'] = $module_unit['status'];
                    }
                    if (!empty($module_unit['quiz_mandetory'])) {
                        $unit_copy_args['quiz_mandetory'] = $module_unit['quiz_mandetory'];
                    }
                    if (!empty($module_unit['questionnaire_mandetory'])) {
                        $unit_copy_args['questionnaire_mandetory'] = $module_unit['questionnaire_mandetory'];
                    }
                    if (!empty($module_unit['quiz_answerable_multiple_times'])) {
                        $unit_copy_args['quiz_answerable_multiple_times'] = $module_unit['quiz_answerable_multiple_times'];
                    }
                    if (!empty($module_unit['show_user_answer'])) {
                        $unit_copy_args['show_user_answer'] = $module_unit['show_user_answer'];
                    }
                    if (!empty($module_unit['progress_certificate'])) {
                        $unit_copy_args['progress_certificate'] = $module_unit['progress_certificate'];
                    }
                    if (!empty($module_unit['trail'])) {
                        $unit_copy_args['trail'] = $module_unit['trail'];
                    }
                    if (!empty($module_unit['mp3_auto_play'])) {
                        $unit_copy_args['mp3_auto_play'] = $module_unit['mp3_auto_play'];
                    }

                    $new_unit_id =  $this->update_or_insert_unit($unit_copy_args);
                    if (!empty($module_unit['quiz'])) {

                        $form_args = array(
                            'table_name' => 'quiz_unit_'.$new_unit_id,
                            'title' => $module_unit['quiz']['title'],

                            "system_abbrv" => 'inst',
                            'category_id'=> '7'
                        );
                        $new_quiz_id = $form_templates->insert_update_form($form_args);

                        //update the unit
                        $unit_args = array(
                            'id' => $new_unit_id,
                            'quiz_id' => $new_quiz_id,
                            'action' => "update",
                        );
                        $this->update_or_insert_unit($unit_args);

                        $questions  = $module_unit['quiz']['questions'];
                        foreach ($questions as $field) {
                            $field_info = array(
                                'title' => $field['title'],
                                'type' => $field['type'],
                                'box_size' => $field['box_size'],
                                'required' => $field['required'],
                                'read_only' => $field['read_only'],
                                'disabled' => $field['disabled'],
                                'read_only_for_applicants' => $field['read_only_for_applicants'],
                                'disabled_when_editing' => $field['disabled_when_editing'],
                                'hide_when_adding' => $field['hide_when_adding'],
                                'hide_when_editing' => $field['hide_when_editing'],
                                'hide_when_editing_for_applicants' => $field['hide_when_editing_for_applicants'],
                                'hide_when_record_equal_user_id' => $field['hide_when_record_equal_user_id'],
                                'default' => $field['default'],
                                'form_id' => $new_quiz_id,
                                'order' => $field['order'],
                                'delete' => $field['deleted'],
                                'locked' => $field['locked'],

                                'conditional' => $field['conditional'],
                                'description' => $field['description'],
                                'review_order' => $field['review_order'],
                                'decision' => $field['decision'],
                                "system_abbrv" => 'inst',
                                'extra_options' => $field['extra_options'],
                                'default_state' => $field['default_state'],

                                'archived' => $field['archived']
                            );


                            $field_info['answer'] = $field['answer'];
                            $field_info['answer_hint'] = $field['answer_hint'];
                            $field_info['answer_reason'] = $field['answer_reason'];
                            $field_info['incorrect_reason'] = $field['incorrect_reason'];
                            $field_info['weighting'] = $field['weighting'];
                            $field_info['weighting_single'] = $field['weighting'];
                            $field_info['usergroup'] = $field['usergroup'];

                            $fields = new Fields;
                            $fields->insert_update_field($field_info);
                        }

                        $unit_copy_args['quiz_id'] = $new_quiz_id;
                    }
                    if (!empty($module_unit['questionnaire_form_id'])) {
                        $page_args = array("id" => $module_unit['questionnaire_form_id'], "system_abbrv" => "inst", "school_id" => $_SESSION['usergroup']);
                        $form = $form_templates->get($page_args);
                        $form_args = array(
                            'table_name' => 'preq_unit_'.$new_unit_id ,
                            'title' => "Unit " . $new_unit_id . "",
                            'category_id' => 8,
                            "system_abbrv" => 'inst');
                        $new_questionnaire_form_id = $form_templates->insert_update_form($form_args);

                        //udpate the course with the new form id
                        $unit_args = array(
                            'id' => $new_unit_id,
                            'questionnaire_form_id' => $new_questionnaire_form_id,
                            'action' => "update",);
                        $this->update_or_insert_unit($unit_args);

                        $questions  = $form['fields'];
                        foreach ($questions as $field) {
                            $field_info = array(
                                'title' => $field['title'],
                                'type' => $field['type'],
                                'box_size' => $field['box_size'],
                                'required' => $field['required'],
                                'read_only' => $field['read_only'],
                                'disabled' => $field['disabled'],
                                'read_only_for_applicants' => $field['read_only_for_applicants'],
                                'disabled_when_editing' => $field['disabled_when_editing'],
                                'hide_when_adding' => $field['hide_when_adding'],
                                'hide_when_editing' => $field['hide_when_editing'],
                                'hide_when_editing_for_applicants' => $field['hide_when_editing_for_applicants'],
                                'hide_when_record_equal_user_id' => $field['hide_when_record_equal_user_id'],
                                'default' => $field['default'],
                                'form_id' => $new_questionnaire_form_id,
                                'order' => $field['order'],
                                'delete' => $field['deleted'],
                                'locked' => $field['locked'],

                                'conditional' => $field['conditional'],
                                'description' => $field['description'],
                                'review_order' => $field['review_order'],
                                'decision' => $field['decision'],
                                "system_abbrv" => 'inst',
                                'extra_options' => $field['extra_options'],
                                'default_state' => $field['default_state'],

                                'archived' => $field['archived']
                            );


                            $field_info['answer'] = $field['answer'];
                            $field_info['answer_hint'] = $field['answer_hint'];
                            $field_info['answer_reason'] = $field['answer_reason'];
                            $field_info['incorrect_reason'] = $field['incorrect_reason'];
                            $field_info['weighting'] = $field['weighting'];
                            $field_info['weighting_single'] = $field['weighting'];
                            $field_info['usergroup'] = $field['usergroup'];

                            $fields = new Fields;
                            $fields->insert_update_field($field_info);
                        }

                        $unit_copy_args['questionnaire_form_id'] = $new_questionnaire_form_id;
                    }

                    //check for quiz allocations
                    $query = "SELECT * FROM ols_quiz_allocation WHERE db45504 = ? AND db45505 = ? AND db45506 = ? AND usergroup = ? AND (rec_archive is null or rec_archive = '')";
                    $execution_params= array($course_info['id'],$course_module['id'], $module_unit['id'], $_SESSION['usergroup']);

                    $stmt = $dbh->prepare($query);
                    $success = $stmt->execute($execution_params);
                    $quiz_allocations = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    foreach ($quiz_allocations as $quiz_allocation) {
                        // attach survey questionnaire to course module  unit
                        $data_args = array("db45504" => $new_course_id, 'db45505' => $new_module_id, 'db45506' => $new_unit_id, 'db45507' => $quiz_allocation['db45507']);
                        $this->update_or_insert_survey($data_args);

                    }

                }
            }

        }
        return $this->get_courses(array('id' => $new_course_id));


    }


    /** ===================================
     * Delete Online Course
     * ====================================    */
    function delete_access_plan($args)
    {
        $db = new Db_helper();

        if ($args['id']) {
            $where = array('id' => $args['id']);
            if ($args['school_id']) {
                $where['usergroup'] = $args['school_id'];
            }
            $db->archive('ols_access_plans', $where);
        }
    }

    /** ===================================
     * Delete Online Course
     * ====================================    */
    function delete_module($args)
    {
        $db = new Db_helper();

        if ($args['id']) {
            $where = array('id' => $args['id']);
            if ($args['school_id']) {
                $where['usergroup'] = $args['school_id'];
            }
            if ($args['course_id']) {
                $where['rel_id'] = $args['course_id'];
            }
            $db->archive('ols_course_modules', $where);
        }
    }


    /** ===================================
     * Delete Unit
     * ====================================    */
    function delete_unit($args)
    {
        $db = new Db_helper();


        if ($args['id']) {
            $where = array('id' => $args['id']);
            if ($args['school_id']) {
                $where['usergroup'] = $args['school_id'];
            }
            $db->archive('ols_course_units', $where);
        }
    }


    /** ===================================
     * Delete Quiz
     * ====================================    */
    function delete_quiz($args = array())
    {
        $form_templates = new FormTemplates;

        $form_args = array('id' => $args['id'], "system_abbrv" => "inst", 'drop_table' => true);

        return $form_templates->delete_form($form_args);
    }


    /** ===================================
     * Send Student Welcome Email
     * ====================================    */

    function send_student_welcome_email($args)
    {
        $db = new Db_helper();

        //Send Welcome email
        $school_email = pull_field("form_schools", "db1117", "WHERE id='$_SESSION[usergroup]'");//Main International Email Address
        $emailFrom = $school_email;
        $emailTo = $args['to'];
        $subject = "Your account for our Online Learning Portal has been activated";

        $message_html = '	<table border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" style="background:#edeff0;font-family:Helvetica,sans-serif;height:100%!important;margin:0;padding:0;width:100%!important;font-size:14px;color:#9ba6b0">
<tbody>
	<tr>
  	<td align="center" style="vertical-align:top;padding-bottom:15px;border-collapse:collapse">
      <table border="0" cellpadding="0" cellspacing="0" style="width:90%;max-width:600px;margin-right:5%;margin-left:5%;display:block">
        <tbody>
        	<tr>
            <td align="center" style="vertical-align:top;border-collapse:collapse">
              <table border="0" cellpadding="0" cellspacing="0" style="width:100%">
                <tbody>
                	<tr>
                    <td align="left" width="192" style="width:192px;vertical-align:top;padding-top:21px;padding-bottom:21px;border-collapse:collapse">
                      <a href="#"><img src="assets/images/logo.png"></a>
                    </td>
                    <td>
                      &nbsp;&nbsp;
                    </td>
                	</tr>
              	</tbody>
              </table>
            </td>
       	</tr>

        <tr>
				  <td align="center" style="vertical-align:top;border-collapse:collapse">
				    <table border="0" cellpadding="0" cellspacing="0" style="background-color:#fff;border-top-left-radius:5px;border-top-right-radius:5px;">
				      <tbody>
				      	<tr>
				        <td align="center" style="vertical-align:top;border-collapse:collapse;padding-top:3%;padding-right:7%;padding-left:7%;padding-bottom:20px">
				          <table border="0" cellpadding="0" cellspacing="0">
				            <tbody>
				            	<tr>
				              	<td style="vertical-align:top;font-size:18px;font-weight:bold;line-height:100%;text-align:left;border-collapse:collapse">
					                <h1 style="font-family:Helvetica,sans-serif;color:#384047;display:block;font-size:24px;font-weight:bold;line-height:130%;letter-spacing:normal;margin-right:0;margin-top:15px;margin-bottom:15px;margin-left:0;text-align:left">
					                  Dear {{ first_name }}
					                </h1>
					                <p style="font-family:Helvetica,sans-serif;color:#8d9aa5;display:block;font-size:18px;font-weight:normal;line-height:150%;letter-spacing:normal;margin-right:0;margin-top:15px;margin-bottom:10px;margin-left:0;text-align:left">
					                  Welcome to the online learning portal with fascinating courses for everyone who wants to be an even better mum, dad, grandparent or carer.
					                  <br>
					                  <a  style="font-family:Helvetica,sans-serif;margin-top:15px;margin-bottom:20px;font-weight:bold;padding-top:13px;padding-right:22px;padding-bottom:12px;padding-left:22px;background:#53bbb4;color:white!important;font-size:16px;text-decoration:none;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:-moz-inline-stack;display:inline-block;vertical-align:middle;border-top-left-radius:5px;border-top-right-radius:5px;border-bottom-left-radius:5px;border-bottom-right-radius:5px" href="{{ login_link }}" target="_blank">
						                  Login <img width="12px" style="width:12px;height:12px;border:0;line-height:100%;outline:none;text-decoration:none;display:inline;margin-left:5px" src="https://ci5.googleusercontent.com/proxy/zW2RVJVT028OJr34HRIK4VjUqjIEHzHU3ppS-W0IRmEH4PF_0b-S7hjclbGzfhklQ8op9rbSD5yY2rmaQ11CSnJZ8FxNgzmGiylYllL73b1Js1HVGxD3k5TBUdjOSs0A=s0-d-e1-ft#https://email-images.teamtreehouse.com/global/icon_right_arrow_white2x.png">
						                </a>
					                  </p>
					                  <p style="font-family:Helvetica,sans-serif;color:#8d9aa5;display:block;font-size:18px;font-weight:normal;line-height:150%;letter-spacing:normal;margin-right:0;margin-top:15px;margin-bottom:10px;margin-left:0;text-align:left">If you have any questions about how to use the online learning portal, please log in to the portal, click on support desk and send us a message from there.&nbsp;
										<br/>
										<br/>
					                  
					                  Regards,<br>
					                  InOurPlace Support
					                  ' . $school_info['title'] . '
					                </p>
					              </td>
					            </tr>
				          	</tbody>
				        	</table>
				        </td>
				      </tr>
				    </tbody>
				  </table>
				    
				  </td>
				</tr>

        <tr>
          <td align="left" style="vertical-align:top;border-collapse:collapse">
            <table border="0" cellpadding="0" cellspacing="0" style="width:100%">
              <tbody>
              	<tr>
                	<td style="text-align:center;vertical-align:top;padding-top:25px;border-collapse:collapse">
                    <p style="font-family:Helvetica,sans-serif;line-height:160%;font-family:Helvetica,sans-serif;color:#b7c0c7;font-size:12px;margin-top:0;margin-bottom:0">
                    <span>Powered by HEIapply</span>
                    </p>
                	</td>
              	</tr>
            	</tbody>
            </table>
          </td>
        </tr>
      </tbody>
     </table>
    
  </td>
</tr>
</tbody>
</table>';
        $message_html = str_replace("{{ first_name }}", $args['first_name'], $message_html);
        $message_html = str_replace("{{ login_link }}", $this->home_url("/login"), $message_html);
        $message_plain_txt = $message_html;
        log_email($emailTo, $subject, $message_plain_txt, $message_html, $emailFrom, "online_learning_welcome");

    }

    /** ===================================
     * Get Invoices
     * ====================================    */
    function get_invoices($args = array())
    {

        $dbh = get_dbh();
        $users = new Users;

        if (isset($args['student_id'])) {
            $student_id_sql = "AND lead_invoice_settings.rel_id='" . $args['student_id'] . "'";
        }

        if ($args['count']) {
            $select_sql = "count(*) as total_count ";
        } else {
            $select_sql = "*,lead_invoice_settings.username_id as invoice_username_id";
        }

        if (isset($args['id'])) {
            $id_sql = "AND lead_invoice_settings.id='" . $args['id'] . "'";
        }

        if (isset($args['status'])) {
            $status_sql = "AND lead_invoice_settings.db15005='" . $args['status'] . "'";
        }


        if (isset($args['username_id'])) {
            $username_id_sql = "AND lead_invoice_settings.username_id='" . $args['username_id'] . "'";
        }

        if ($args['school_id']) {
            $school_id_sql = "AND lead_invoice_settings.usergroup='" . $args['school_id'] . "'";
        }
        else {
            $school_id_sql = "AND lead_invoice_settings.usergroup='" . $_SESSION['usergroup'] . "'";
        }

        $query = "
			SELECT 
				$select_sql
			FROM
				lead_invoice_settings
				JOIN lead_invoice_items ON lead_invoice_items.rel_id = lead_invoice_settings.id
			WHERE
				1
				$student_id_sql
				$id_sql
				$status_sql
				$school_id_sql
				$username_id_sql
				AND (lead_invoice_settings.rec_archive is null or lead_invoice_settings.rec_archive='')
			ORDER BY lead_invoice_settings.id DESC";

        dev_debug($query);
        $sth = $dbh->prepare($query);
        $sth->execute();
        $results_list = array();

        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {

            if ($args['count']) {
                $results_list = $entry['total_count'];
            } else {
                $entry = array(
                    'id' => $entry['id'],
                    'student' => array('id' => $entry['rel_id']),
                    'username_id' => $entry['invoice_username_id'],
                    'currency_id' => $entry['db14987'],
                    'issue_date' => $entry['db14988'],
                    'due_date' => $entry['db14989'],
                    'reminder_date' => $entry['db14990'],
                    'date' => $entry['date'],
                    'office_id' => $entry['db14991'],
                    'recipient_type' => $entry['db14992'],
                    'recipient_id' => $entry['db14993'],
                    'payment_option' => $entry['db14995'],
                    'number_of_installments_allowed' => $entry['db14996'],
                    'delivery_options' => $entry['db14997'],
                    'include_vat' => $entry['db14998'],
                    'description' => $entry['db15016'],
                    'discount_amount' => $entry['db14999'],
                    'comments' => $entry['db15000'],
                    'deposit' => $entry['db15002'],
                    'value' => $entry['db15003'],
                    'due_date_1' => $entry['db15004'],
                    'status' => $entry['db15005'],
                    'display_total' => $entry['db15006'],
                    'recipient_address' => $entry['db19355'],
                    'invoice_number' => $entry['db25617']


                );


                $results_list[] = $entry;
            }

        }

        if ($args['id'] || $args['username_id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    function get_responses_list($form, $answers)
    {
        $responses_list = array();
        if (count($answers)) {
            foreach ($form['fields'] as $field) {

                $weight = 0;
                if ($field['answer'] == $answers[$field['db_field_name']]) {
                    $correct_response = true;
                    $correct_response_count++;
                } else {
                    $correct_response = false;
                }

                if (in_array($field['type'], array('radio_fromlist', 'dropdown', 'grid'))) {
                    $oi = 0;
                    foreach ($field['options'] as $option) {
                        dev_debug("OPTION ".json_encode($option));
                        dev_debug("MY ANSWERS **". $field['db_field_name']. "**".$answers[$field['db_field_name']]);
                        if ($option['value'] == $answers[$field['db_field_name']]) {
                            $weight = $option['weighting'];
                            dev_debug("MY WEIGHT **". $option['title']. "**".$option['weighting']);
                        }
                        $oi++;
                    }
                } elseif ($field['type'] == "radio_yes_no") {
                    if ("yes" == $answers[$field['db_field_name']]) {
                        $weight = $field['weighting'][0];
                    }
                    if ("no" == $answers[$field['db_field_name']]) {
                        $weight = $field['weighting'][1];
                    }
                } else {
                    if ($answers[$field['db_field_name']]) {
                        $weight = $field['weighting'][0];
                    }
                }

                $responses_list[] = array(
                    'answer_id' => $answers['id'],
                    'question' => $field['title'],
                    'type' => $field['type'],
                    'weight' => $weight,
                    'response' => $answers[$field['db_field_name']],
                );
            }
        }

        return $responses_list;
    }

    /** ===================================
     * Get Courses
     * ====================================    */
    function get_course_info_for_questionnaires($args = array())
    {
        global $user_info;
        $db = new Db_helper();

        if ($args['id']) {
            $id_sql = "AND ols_online_courses.id='" . $args['id'] . "'";
        }

        if ($args['username_id']) {
            $username_id_sql = "AND ols_online_courses.username_id='" . $args['username_id'] . "'";
        }

        $query = "
			SELECT 
				*
			FROM
				ols_online_courses 
			WHERE
				1
				$id_sql
				$username_id_sql
				
			ORDER BY db21909 ASC";
        $result = $db->query($query);


        $results_list = array();
        foreach ($result as $row) {

            //get the pre questionnaires questions
            if (isset($row['db25550']) && $row['db25550'] != '') {
                $form_templates = new FormTemplates;
                //Get the Form Info
                $form_args = array("id" => $row['db25550'], "system_abbrv" => 'inst');
                $form = $form_templates->get($form_args);
                $questionnaire_questions = $form['fields'];

            }

            //language
            $language_info = array();
            $language_direction = 'ltr';
            if (isset($row['db31066']) && $row['db31066'] != '') {
                $language_info = $this->get_languages(array('school_id' => $_SESSION['usergroup'], 'id' => $row['db31066']));
                $language_direction = $language_info['direction'];

            }
            //Result
            $course_info = array(
                "id" => $row['id'],
                "username_id" => $row['username_id'],
                "title" => $row['db21909'],
                "alternative_title" => $row['db260924'],
                "excerpt" => $row['db21913'],
                "colour" => $row['db21914'],
                "next_unit_visibility" => $row['db21915'],
                "subject" => $row['db21917'],
                "category" => $row['db21929'],
                "course_level" => $row['db37364'],
                "course completion_text" => $row['db21931'],
                "unit_complete_button_color" => $row['db21932'],
                "unit_complete_button_text" => $row['db21931'],
                "module_complete_button_color" => $row['db21935'],
                "module_complete_button_text" => $row['db21934'],
                "enable_feedback" => $row['db21937'],
                "course_feedback_form" => $row['db21938'],
                "enable_download_certificate" => $row['db21940'],
                "course_viewable_after_completion" => $row['db21941'],
                "featured" => $row['db25543'],
                "pre_questions_form_id" => $row['db25550'],
                "post_questions_form_id" => $row['db25551'],
                "show_support_on_each_unit" => $row['db21950'],
                "show_feedback_on_each_unit" => $row['db66669'],
                "publish" => $row['db30582'],
                "href" => $this->home_url("course/") . $this->ensure_slug_matches_title($row['id'], 'course') ,
                "questionnaire_type" => $row['db26246'],
                "closeness_questions" => $row['db26247'],
                "conflict_questions" => $row['db26248'],
                "all_questionnaire_questions" => $questionnaire_questions,
                "score_1_text" => $row['db26238'],
                "score_2_text" => $row['db26239'],
                "score_3_text" => $row['db26240'],
                "score_4_text" => $row['db26241'],
                "score_5_text" => $row['db26242'],
                "score_6_text" => $row['db26243'],
                "score_7_text" => $row['db26244'],
                "score_8_text" => $row['db26245'],
                "language" => $language_info,
                "text_before_questionnaire" => $row['db250853'],

            );


            $results_list[] = $course_info;


        }

        // echo '<pre>';
        // print_r($results_list);
        // exit();

        if ($args['id'] || $args['username_id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /** ===================================
     * Get Sponsor Groups
     * ====================================    */
    function get_sponsor_groups($args = array())
    {
        global $user_info;
        $db = new Db_helper();
        if ($args['id']) {
            $id_sql = "AND ols_general_groups.id='" . $args['id'] . "'";
        }

        if ($args['sponsor_id']) {
            $title_sql = "AND ols_general_groups.db30486='" . $args['sponsor_id'] . "'";
        }

        if ($args['school_id']) {
            $school_id_sql = "AND ols_general_groups.usergroup='" . $args['school_id'] . "'";
        }

        if ($args['name']) {
            $name_sql = "AND ols_general_groups.db27435='" . $args['name'] . "'";
        }

        if ($args['search']) {
            $search_sql = "AND ols_general_groups.db27435 LIKE '%" . $args['db27435'] . "%'";
        }

        if ($args['username_id']) {
            $username_id_sql = "AND ols_general_groups.username_id='" . $args['username_id'] . "'";
        }
        if ($args['school_id']) {
            $usergroup_sql = "AND ols_general_groups.usergroup='" . $args['school_id'] . "'";
        }


        if ($args['count']) {
            $select_sql = "count(*) as total_count ";
        } else {
            $select_sql = "*";
        }


        $query = "
			SELECT 
				$select_sql
			FROM
				ols_general_groups 
			WHERE
				1
				$id_sql
				$title_sql
				$school_id_sql
				$search_sql
				$username_id_sql
				
				$name_sql
				$usergroup_sql
			ORDER BY db27435 ASC";
        $result = $db->query($query);


        $results_list = array();
        //for each course in access plan
        //1. Find number of learners for this group
        //2. find number of learners this month
        //3. find number of learners completed
        //4. find average number of modules completed
        $sponsor_access_plans = $args['sponsor_access_plans'];
        foreach ($sponsor_access_plans as $sponsor_access_plan) {
            foreach ($sponsor_access_plan['courses'] as $sponsor_access_plan_course) {

                $course_group_info = array();
                foreach ($result as $row) {

                    if ($args['count']) {
                        $results_list[] = $row['total_count'];
                    } else {
                        //Result
                        $no_of_registered_users = pull_field("ols_user_access_plan INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id", "count(*)", "WHERE db22031='2' AND db21880 = '$args[sponsor_id]' and (ols_access_plans.rec_archive is null or ols_access_plans.rec_archive = '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND db22019='$sponsor_access_plan[id]'");

                        $no_learners_started = pull_field("ols_user_progress INNER JOIN core_students ON core_students.id = ols_user_progress.rel_id INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_progress.db37394 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = core_students.id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id", "count(DISTINCT(ols_user_progress.rel_id))", "WHERE ols_user_progress.db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND ols_access_plans.db21880 = '$args[sponsor_id]' AND ols_user_progress.db37394='$sponsor_access_plan[id]'");
                        //$avg_modules_completed_per_learner = pull_field("ols_user_progress LEFT JOIN ols_user_access_plan on ols_user_progress.rel_id = ols_user_access_plan.rel_id LEFT JOIN ols_course_units on ols_user_progress.db22013 = ols_course_units.id LEFT JOIN ols_course_modules on ols_course_units.rel_id = ols_course_modules.id", "count(*) DIV count(DISTINCT(ols_user_progress.rel_id))", "WHERE db22019 = $row[apc_rel_id] AND db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (ols_user_access_plan.rec_archive IS NULL OR ols_user_access_plan.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND db22031='2' AND (ols_course_modules.rel_id = $row[db21911])");
                        $new_learners_this_month = pull_field("ols_user_access_plan INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id", "count(*)", "WHERE db22031='2' AND db21880 = '$args[sponsor_id]' and (ols_access_plans.rec_archive is null or ols_access_plans.rec_archive = '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND UNIX_TIMESTAMP(ols_user_access_plan.date) >= UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY - INTERVAL 1 MONTH) AND UNIX_TIMESTAMP(ols_user_access_plan.date) <  UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY) AND db22019='$sponsor_access_plan[id]'");
                        $new_learners_last_month = pull_field("ols_user_access_plan INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id", "count(*)", "WHERE db22031='2' AND db21880 = '$args[sponsor_id]' and (ols_access_plans.rec_archive is null or ols_access_plans.rec_archive = '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND UNIX_TIMESTAMP(ols_user_access_plan.date) >= UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY - INTERVAL 2 MONTH) AND UNIX_TIMESTAMP(ols_user_access_plan.date) <  UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY - INTERVAL 1 MONTH) AND db22019='$sponsor_access_plan[id]'");
                        $sponsor_course_stats = array();

                        if ($args["course_stats_required"]) {

                            $course_no_of_registered_users = pull_field("ols_user_access_plan INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id", "count(*)", "WHERE db22019 = '$sponsor_access_plan[id]' AND db22031='2' AND db21880 = '$args[sponsor_id]' and (ols_access_plans.rec_archive is null or ols_access_plans.rec_archive = '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND db27436 = '$row[id]'");
                            $course_no_learners_started = pull_field("ols_user_progress INNER JOIN ols_user_access_plan on ols_user_progress.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id", "count(DISTINCT(ols_user_progress.rel_id))", "WHERE db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (ols_user_access_plan.rec_archive IS NULL OR ols_user_access_plan.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND db22031='2' AND db21880 = '$args[sponsor_id]' AND db27436 = '$row[id]' AND db22019 = '$sponsor_access_plan[id]'");

                            $course_new_learners_this_month = pull_field("ols_user_access_plan INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id", "count(*)", "WHERE db22019 = '$sponsor_access_plan[id]' AND db22031='2' AND db21880 = '$args[sponsor_id]' and (ols_access_plans.rec_archive is null or ols_access_plans.rec_archive = '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND db27436 = '$row[id]' AND UNIX_TIMESTAMP(ols_user_access_plan.date) >= UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY - INTERVAL 1 MONTH) AND UNIX_TIMESTAMP(ols_user_access_plan.date) <  UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY)");
                            $course_new_learners_last_month = pull_field("ols_user_access_plan INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id", "count(*)", "WHERE db22019 = '$sponsor_access_plan[id]' AND db22031='2' AND db21880 = '$args[sponsor_id]' and (ols_access_plans.rec_archive is null or ols_access_plans.rec_archive = '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND db27436 = '$row[id]' AND UNIX_TIMESTAMP(ols_user_access_plan.date) >= UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY - INTERVAL 2 MONTH) AND UNIX_TIMESTAMP(ols_user_access_plan.date) <  UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY - INTERVAL 1 MONTH)");

                            $course_no_units_completed = pull_field("ols_user_progress INNER JOIN ols_user_access_plan on ols_user_progress.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id INNER JOIN ols_course_units on ols_user_progress.db22013 = ols_course_units.id INNER JOIN ols_course_modules on ols_course_units.rel_id = ols_course_modules.id", "count(*)", "WHERE db22019 = '$sponsor_access_plan[id]' AND db22031='2' AND db21880 = '$args[sponsor_id]' AND db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (ols_user_access_plan.rec_archive IS NULL OR ols_user_access_plan.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND db22031='2' AND (ols_course_modules.rel_id = '$sponsor_access_plan_course[id]')");


                            $course_number_of_units = pull_field("ols_course_units INNER JOIN ols_course_modules on ols_course_units.rel_id = ols_course_modules.id", "count(*)", "WHERE ols_course_modules.rel_id = '$sponsor_access_plan_course[id]' and (ols_course_units.rec_archive is NULL OR ols_course_units.rec_archive = '') AND (ols_course_modules.rec_archive is NULL or ols_course_modules.rec_archive = '')");
                            $group_course_info = array(
                                "access_plan" => $sponsor_access_plan['id'],
                                "access_plan_title" => $sponsor_access_plan['title'],
                                "access_code" => $sponsor_access_plan['access_code'],
                                "course" => $sponsor_access_plan_course['id'],
                                "course_title" => $sponsor_access_plan_course['title'],
                                "alternative_title" => $sponsor_access_plan_course['alternative_title'],
                                "course_no_of_registered_users" => $course_no_of_registered_users,
                                "course_no_learners_started" => $course_no_learners_started,
                                "course_new_learners_this_month" => $course_new_learners_this_month,
                                "course_new_learners_last_month" => $course_new_learners_last_month,
                                "course_avg_units_completed" => $course_no_units_completed . '**' . $course_no_learners_started . '**' . $course_number_of_units . '**' . round((($course_no_units_completed / $course_no_learners_started) / $course_number_of_units) * 100), ((($course_no_units_completed / $course_no_learners_started) / $course_number_of_units) * 100),


                            );
                            $course_group_stats = array(
                                "group_id" => $row['id'],
                                "group_name" => $row['db27435'],
                                "sponsor_id" => $row['db30486'],
                                "total_registered" => $no_of_registered_users,
                                "total_learners" => $no_learners_started,
                                "new_learners_this_month" => $new_learners_this_month,
                                "new_learners_last_month" => $new_learners_last_month,
                                "course_group_no_of_registered_users" => $course_no_of_registered_users,
                                "course_group_no_learners_started" => $course_no_learners_started,
                                "course_group_new_learners_this_month" => $course_new_learners_this_month,
                                "course_group_new_learners_last_month" => $course_new_learners_last_month,
                                "course_group_avg_units_completed" => $course_no_units_completed . '**' . $course_no_learners_started . '**' . $course_number_of_units . '**' . round((($course_no_units_completed / $course_no_learners_started) / $course_number_of_units) * 100), ((($course_no_units_completed / $course_no_learners_started) / $course_number_of_units) * 100),

                            );
                            $sponsor_course_stats[] = $group_course_info;
                            $course_group_info[] = $course_group_stats;

                            $general_group_info = array(
                                "id" => $row['id'],
                                "name" => $row['db27435'],
                                "sponsor_id" => $row['db30486'],
                                "total_registered" => $no_of_registered_users,
                                "total_learners" => $no_learners_started,
                                "new_learners_this_month" => $new_learners_this_month,
                                "new_learners_last_month" => $new_learners_last_month,
                                "course_stats" => $sponsor_course_stats,

                            );
                        }
                    }
                }

                $course_groups_info = array(
                    "access_plan" => $sponsor_access_plan['id'],
                    "access_plan_title" => $sponsor_access_plan['title'],
                    "access_plan_unlock_code" => $sponsor_access_plan['access_code'],
                    "course" => $sponsor_access_plan_course['id'],
                    "course_title" => $sponsor_access_plan_course['title'],
                    "alternative_title" => $sponsor_access_plan_course['alternative_title'],
                    "course_group_info" => $course_group_info,
                    "group_course_info" => $group_course_info
                );
                $results_list[] = $course_groups_info;

            }

        }

        // echo '<pre>';
        // print_r($results_list);
        // exit();

        if ($args['id'] || $args['name'] || $args['count'] || $args['username_id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /** ===================================
     * Get Languages
     * ====================================    */
    function get_languages($args = array())
    {
        global $user_info;
        $db = new Db_helper();
        $dbh = get_dbh();
        if (!empty($args['id'])) {
            $id_sql = "AND form_languages.id='" . $args['id'] . "'";
        }

        if (!empty($args['code'])) {
            $code_sql = "AND form_languages.db21281='" . $args['code'] . "'";
        }

        $ug_specific_sql = "";

        if (!empty($args['ug_specific'])) {
            //$ug_specific_sql = " AND form_languages.usergroup IN(1, " . $_SESSION['usergroup'] . ") AND form_languages.id IN ( select group_concat(db31066) from ols_online_courses WHERE usergroup='" . $_SESSION['usergroup'] . "' GROUP BY db31066)";
            $for_published_only="";
            if (!empty($args['for_published_only'])) {
                $for_published_only.="AND (ols_online_courses.db30582 = 'yes' OR ols_online_courses.db30582 = '1')";
            }

            if ($args['for_library_only']) {
                $for_published_only .= " AND (ols_online_courses.db309757 NOT IN ('yes' ,'1') OR ols_online_courses.db309757 is NULL) ";
            }

            $ug_specific_sql = " AND form_languages.usergroup IN(1, " . $_SESSION['usergroup'] . ") 
             AND form_languages.id IN (
                SELECT db31066 
                FROM ols_online_courses 
                WHERE usergroup = '" . $_SESSION['usergroup'] . "'
                $for_published_only
                AND db31066 IS NOT NULL
            )";
        }

        if (!empty($args['school_id'])) {
            $school_id_sql = "AND form_languages.usergroup='" . $args['school_id'] . "'";
        }

        $query = "
			SELECT 
				*
			FROM
				form_languages 
			WHERE
				1
				$id_sql
				$code_sql
			   $ug_specific_sql
			ORDER BY id ASC";

        dev_debug($query);
        $sth = $dbh->prepare($query);
        $sth->execute();
        $results_list = array();
        while ($entry = $sth->fetch(PDO::FETCH_ASSOC)) {

            $entry = array(
                'id' => $entry['id'],
                'name' => $entry['db21280'],
                'code' => $entry['db21281'],
                'direction' => $entry['db31065'],
                'native_name'=>$entry['db337233']
            );
            $results_list[] = $entry;
        }

        if (!empty($args['id']) || !empty($args['code'])) {
            dev_debug('returning ' . $results_list[0]['direction'] . '**' . $results_list[0]['name']);
            return $results_list[0];
        } else {
            return $results_list;
        }

    }

    /** ===================================
     * Get new users
     * returns the number of registered users from last month
     * ====================================    */
    function get_ols_new_users($args = array())
    {
        $dbh = get_dbh();
        $execution_params = array();

        if ($args['school_id'] == '' || $args['school_id'] == null) {
            $school_id_sql = "AND ols_user_access_plan.usergroup = '" . $_SESSION['usergroup'] . "'";
        } else {
            $school_id_sql = "AND ols_user_access_plan.usergroup = ? ";
            $execution_params[] = $args['school_id'];
        }

        if ($args['sponsor_id'] == '' || $args['sponsor_id'] == null)
            $sponsor_id_sql = "";
        else {
            $sponsor_id_sql = "AND ols_access_plans.db21880 = ?";
            $execution_params[] = $args['sponsor_id'];
        }

        if ($args['access_plan'] == '' || $args['access_plan'] == null)
            $access_plan_sql = "";
        else {
            $access_plan_sql = "AND ols_access_plans.id = ?";
            $execution_params[] = $args['access_plan'];
        }

        if ($args['course'] == '' || $args['course'] == null)
            $course_sql = "";
        else {
            $course_sql = "AND ols_user_access_plan.db22019 IN (SELECT ols_access_plan_courses.rel_id  FROM ols_access_plan_courses WHERE ols_access_plan_courses.db21911 = ? AND (ols_access_plan_courses.rec_archive is NULL or ols_access_plan_courses.rec_archive = ''))";
            $execution_params[] = $args['course'];
        }

        if ($args['group'] == '' || $args['group'] == null)
            $group_sql = "";
        else {
            $group_sql = "AND ols_access_plans.db21880  IN (SELECT ols_general_groups.db30486  FROM ols_general_groups WHERE ols_general_groups.id = ? AND (ols_general_groups.rec_archive is NULL or ols_general_groups.rec_archive = ''))";
            $execution_params[] = $args['group'];
        }

        $new_users_query = "SELECT count(ols_user_access_plan.id) FROM ols_user_access_plan
		INNER JOIN ols_access_plans ON ols_user_access_plan.db22019 = ols_access_plans.id
		INNER JOIN core_students ON core_students.id = ols_user_access_plan.rel_id 
		WHERE DATEDIFF(current_date(),ols_user_access_plan.date) < 31 
		AND ols_user_access_plan.db22031='2'
		$school_id_sql
		$sponsor_id_sql 
		$access_plan_sql
		$course_sql
		$group_sql
		AND (ols_user_access_plan.rec_archive IS NULL OR ols_user_access_plan.rec_archive = '')
		AND (core_students.rec_archive is NULL or core_students.rec_archive = '') 
		AND (ols_access_plans.rec_archive is NULL or ols_access_plans.rec_archive = '')";

        dev_debug($new_users_query);

        $stmt = $dbh->prepare($new_users_query);
        $stmt->execute($execution_params);
        $new_users = $stmt->fetchColumn();

        dev_debug("New users from last month: " . $new_users);
        return $new_users;
    }

    /** ===================================
     * Get total registered learners
     * returns the total number of registered users
     * ====================================    */
    function get_ols_total_learners($args = array())
    {
        $dbh = get_dbh();
        $execution_params = array();

        if ($args['school_id'] == '' || $args['school_id'] == null) {
            $school_id_sql = "AND ols_user_access_plan.usergroup = '" . $_SESSION['usergroup'] . "'";
        } else {
            $school_id_sql = "AND ols_user_access_plan.usergroup = ? ";
            $execution_params[] = $args['school_id'];
        }

        if ($args['sponsor_id'] == '' || $args['sponsor_id'] == null)
            $sponsor_id_sql = "";
        else {
            $sponsor_id_sql = "AND ols_access_plans.db21880 = ?";
            $execution_params[] = $args['sponsor_id'];
        }

        if ($args['access_plan'] == '' || $args['access_plan'] == null)
            $access_plan_sql = "";
        else {
            $access_plan_sql = "AND ols_access_plans.id = ?";
            $execution_params[] = $args['access_plan'];
        }

        if ($args['course'] == '' || $args['course'] == null)
            $course_sql = "";
        else {
            $course_sql = "AND ols_user_access_plan.db22019 IN (SELECT ols_access_plan_courses.rel_id  FROM ols_access_plan_courses WHERE ols_access_plan_courses.db21911 = ? AND (ols_access_plan_courses.rec_archive is NULL or ols_access_plan_courses.rec_archive = ''))";
            $execution_params[] = $args['course'];
        }

        if ($args['group'] == '' || $args['group'] == null)
            $group_sql = "";
        else {
            $group_sql = "AND ols_access_plans.db21880  IN (SELECT ols_general_groups.db30486  FROM ols_general_groups WHERE ols_general_groups.id = ? AND (ols_general_groups.rec_archive is NULL or ols_general_groups.rec_archive = ''))";
            $execution_params[] = $args['group'];
        }

        $total_users_query = "SELECT count(ols_user_access_plan.id) FROM ols_user_access_plan
		INNER JOIN ols_access_plans ON ols_user_access_plan.db22019 = ols_access_plans.id
		INNER JOIN core_students ON core_students.id = ols_user_access_plan.rel_id 
		WHERE ols_user_access_plan.db22031='2' 
		$school_id_sql
		$sponsor_id_sql 
		$access_plan_sql
		$course_sql
		$group_sql
		AND (ols_user_access_plan.rec_archive IS NULL OR ols_user_access_plan.rec_archive = '')
		AND (core_students.rec_archive is NULL or core_students.rec_archive = '') 
		AND (ols_access_plans.rec_archive is NULL or ols_access_plans.rec_archive = '')";

        dev_debug($total_users_query);

        $stmt = $dbh->prepare($total_users_query);
        $stmt->execute($execution_params);
        $total_users = $stmt->fetchColumn();

        dev_debug("Total users : " . $total_users);
        return $total_users;
    }

    /** ===================================
     * Get actual learners
     * actual learners are those who have started of those registered
     * ====================================    */
    function get_ols_actual_learners($args = array())
    {
        $dbh = get_dbh();
        $execution_params = array();

        if ($args['school_id'] == '' || $args['school_id'] == null) {
            $school_id_sql = "AND ols_user_progress.usergroup = '" . $_SESSION['usergroup'] . "'";
        } else {
            $school_id_sql = "AND ols_user_progress.usergroup = ? ";
            $execution_params[] = $args['school_id'];
        }

        if ($args['sponsor_id'] == '' || $args['sponsor_id'] == null)
            $sponsor_id_sql = "";
        else {
            $sponsor_id_sql = "AND ols_access_plans.db21880 = ?";
            $execution_params[] = $args['sponsor_id'];
        }

        if ($args['access_plan'] == '' || $args['access_plan'] == null)
            $access_plan_sql = "";
        else {
            $access_plan_sql = "AND ols_user_progress.db37394 = ?";
            $execution_params[] = $args['access_plan'];
        }

        if ($args['course'] == '' || $args['course'] == null)
            $course_sql = "";
        else {
            //$course_sql = "AND ols_user_access_plan.db22019 IN (SELECT ols_access_plan_courses.rel_id  FROM ols_access_plan_courses WHERE ols_access_plan_courses.db21911 = ? AND (ols_access_plan_courses.rec_archive is NULL or ols_access_plan_courses.rec_archive = ''))";
            $course_sql = "AND ols_user_progress.db37395 = ?";
            $execution_params[] = $args['course'];
        }

        if ($args['group'] == '' || $args['group'] == null)
            $group_sql = "";
        else {
            $group_sql = "AND ols_access_plans.db21880  IN (SELECT ols_general_groups.db30486  FROM ols_general_groups WHERE ols_general_groups.id = ? AND (ols_general_groups.rec_archive is NULL or ols_general_groups.rec_archive = ''))";
            $execution_params[] = $args['group'];
        }

        $user_access_plan_status_sql = "AND ols_user_progress.db37394  IN (SELECT ols_user_access_plan.db22019  FROM ols_user_access_plan WHERE ols_user_access_plan.db22031='2' AND (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = ''))";

        $actual_users_query = "SELECT count(DISTINCT ols_user_progress.rel_id) FROM ols_user_progress
		INNER JOIN core_students ON core_students.id = ols_user_progress.rel_id
		INNER JOIN ols_access_plans ON ols_user_progress.db37394 = ols_access_plans.id 
		WHERE ols_user_progress.db22014 IS NOT NULL
		$school_id_sql
		$sponsor_id_sql 
		$access_plan_sql
		$course_sql
		$group_sql
		$user_access_plan_status_sql
		AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '')
		AND (core_students.rec_archive is NULL or core_students.rec_archive = '') 
		AND (ols_access_plans.rec_archive is NULL or ols_access_plans.rec_archive = '')";

        dev_debug($actual_users_query);

        $stmt = $dbh->prepare($actual_users_query);
        $stmt->execute($execution_params);
        $actual_users = $stmt->fetchColumn();
        dev_debug("Actual users: " . $actual_users);
        return $actual_users;
    }

    public function get_sponsor_groups_plain($args = array())
    {
        $dbh = get_dbh();

        if ($args['username_id']) {
            $username_id_sql = "AND ols_challenge_answers.username_id='" . $args['username_id'] . "'";
        }
        if ($args['school_id']) {
            $usergroup_sql = "AND ols_challenge_answers.usergroup='" . $args['school_id'] . "'";
        }

        $all_plans_groups = array();

        $sponsor_access_plans = $args['sponsor_access_plans'];
        //changed from reporting by answer to reporting by group
        foreach ($sponsor_access_plans as $sponsor_access_plan) {
            $plan_group = array();
            $plan_id = $sponsor_access_plan['id'];
            /*
			$query ="SELECT
						(SELECT db27435 from ols_general_groups WHERE id = db27436 ) as 'Group',
						(SELECT db21899 from ols_challenge_questions WHERE id = db21902) as 'Question',
						ols_challenge_answers.db21903 as 'Answer',
						ols_challenge_answers.id as 'answer_id',
						ols_challenge_answers.db21902 as 'question_id',
						ols_challenge_answers.db27436 as 'group_id'
					FROM ols_challenge_answers
					WHERE  (ols_challenge_answers.rel_id = $plan_id)
					$username_id_sql
					$usergroup_sql
					AND (ols_challenge_answers.rec_archive IS NULL OR ols_challenge_answers.rec_archive = '')
					GROUP BY Answer
					ORDER BY Answer";
			*/
            $query = "SELECT
						(SELECT db27435 from ols_general_groups WHERE id = db27436 ) as 'Group',
						(SELECT db21899 from ols_challenge_questions WHERE id = db21902) as 'Question',
						ols_challenge_answers.db21902 as 'question_id',
						ols_challenge_answers.db27436 as 'group_id',
						status_2.cnt as 'group_count'
						
					FROM ols_challenge_answers
					LEFT JOIN (SELECT db56592 as group_id, count(DISTINCT(ols_user_access_plan.rel_id)) as cnt FROM ols_user_access_plan JOIN core_students ON core_students.id = ols_user_access_plan.rel_id JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_access_plan.rel_id 
					            WHERE db22019 = '$plan_id' AND db22031='2' AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') group by db56592) as status_2 ON status_2.group_id=ols_challenge_answers.db27436 
					WHERE  (ols_challenge_answers.rel_id = '$plan_id')  
					$username_id_sql
					$usergroup_sql
					AND (ols_challenge_answers.rec_archive IS NULL OR ols_challenge_answers.rec_archive = '') 
					GROUP BY group_id
					ORDER BY group_id";

            dev_debug($query);
            $sth = $dbh->prepare($query);
            $sth->execute();
            $group_summary_results = $sth->fetchAll(PDO::FETCH_ASSOC);

            $plan_group['plan_access_code'] = $sponsor_access_plan['access_code'];
            $plan_group['plan_groups'] = $group_summary_results;

            //loop through each of the groups and compile the number of registered learners
            $plan_group['plan_groups_details'] = array();

            foreach ($group_summary_results as $key => $value) {
                $group_dets = array();
                $group_dets['group_title'] = $value['Group'];
                $group_dets['group_id'] = $value['group_id'];
                $group_dets['group_courses'] = $this->get_group_stats(array('plan_id' => $plan_id, 'group_id' => $value['group_id'], 'plan_title' => $plan_group['plan_access_code'], 'group_count' => $value['group_count'], 'transitioned_courses' => "true"));
                $plan_group['plan_groups_details'][] = $group_dets;
            }

            $all_plans_groups[$plan_id] = $plan_group;
        }

        return $all_plans_groups;
    }

    public function get_question_answer_totals($args = array())
    {
        /*		$where_sql = "";
		$params = array();
		# code...'SELECT age, COUNT(*) AS freq FROM table GROUP BY age'
		# db22027='242' AND db22025='314

		if(isset($args['access_plan_id']) && $args['access_plan_id'] !=''){
			$where_sql .= " AND db22027 =:access_plan_id ";
			$params['access_plan_id'] = $args['access_plan_id'];
		}

		if(isset($args['question_id']) && $args['question_id'] !=''){
			$where_sql .=" AND db22025 =:question_id ";
			$params['question_id'] = $args['question_id'];
		}

		if(isset($args['group_id']) && $args['group_id'] !=''){
			$group_id_sql = "AND ols_user_supp_answers.rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db27436 = :group_id )";
			$params['group_id'] = $args['group_id'];
		}

		if(isset($args['answer_id']) && $args['answer_id'] !=''){
			$answer_id_sql = "AND ols_user_supp_answers.rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db21903 = :answer_id )";
			$params['answer_id'] = $args['answer_id'];
		}

*/


        $dbh = get_dbh();

        if (isset($args['access_plan_id']) && $args['access_plan_id'] != '') {
            $params['access_plan_id'] = $args['access_plan_id'];
        }

        if (isset($args['question_id']) && $args['question_id'] != '') {
            $params['question_id'] = $args['question_id'];
        }

        if (isset($args['group_id']) && $args['group_id'] != '') {
            $group_id_sql = "AND db32191 = :group_id";
            $params['group_id'] = $args['group_id'];
        }

        if (isset($args['answer_id']) && $args['answer_id'] != '') {
            $answer_id_sql = "AND ols_user_supp_answers.rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db21903 = :answer_id )";
            $params['answer_id'] = $args['answer_id'];
        }

        $learner_supplementary_answers_query = "
SELECT ols_user_access_plan.rel_id, 
(SELECT db22026 FROM ols_user_supp_answers WHERE rel_id=ols_user_access_plan.rel_id AND db22027=:access_plan_id AND db22025=:question_id ORDER BY id DESC LIMIT 1) as 'user_answer' 
FROM ols_user_access_plan  
INNER JOIN core_students ON core_students.id = ols_user_access_plan.rel_id 
WHERE db22031='2' 
AND db22019 = :access_plan_id  
AND (core_students.rec_archive is null or core_students.rec_archive= '')  
AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') 
$group_id_sql";
        //dev_debug("ANITA SUPP QUERY" + $learner_supplementary_answers_query);
        $learner_supplementary_answers_sth = $dbh->prepare($learner_supplementary_answers_query);
        $learner_supplementary_answers_sth->execute($params);
        $learner_supplementary_answers = array();
        while ($entry = $learner_supplementary_answers_sth->fetch(PDO::FETCH_ASSOC)) {

            $learner_supplementary_questions_answers = array();


            //$user_answer = pull_field("ols_user_supp_answers", "db22026", "WHERE rel_id=$entry[rel_id] AND db22027='$access_plan[id]' AND db22025='$supplementary_question[id]'");
            $user_answer = trim($entry['user_answer']);
            $answer_entry = array(
                'id' => $args['question_id'],
                'user_answer' => $user_answer,

            );
            $learner_supplementary_questions_answers[] = $answer_entry;


            $learner_supplementary_answers[] = array(
                "access_plan_id" => $args['access_plan_id'],
                "learner_id" => $entry['rel_id'],
                "supplementary_questions" => $learner_supplementary_questions_answers,
            );


        }

        $supp_questions_answers = array();

        $access_plan_learner_supp_answers = $learner_supplementary_answers;
        foreach ($access_plan_learner_supp_answers as $access_plan_learner_supp_answer) {
            foreach ($access_plan_learner_supp_answer['supplementary_questions'] as $o_supplementary_question) {

                $answer = $o_supplementary_question['user_answer'];
                $answer_array = explode(',', $answer);
                foreach ($answer_array as $single_answer) {

                    dev_debug("Question:" . $args['question_id'] . ' Answer:' . $single_answer);
                    $supp_questions_answers[trim($single_answer)]++;
                }

            }
        }

        $final = array();
        //dev_debug(__METHOD__.' - '. $sql. ' - '.json_encode($params));
        foreach ($supp_questions_answers as $key => $value) {

            $point_name = "Not Answered";
            if ($key && !in_array(strtolower($key), array("select"))) {
                $point_name = $key;
            }
            $point = array(
                'name' => $point_name,
                'y' => (int)$value,
            );

            if ($key == 0) {
                $point['sliced'] = true;
                $point['selected'] = true;
            }
            $final[] = $point;
        }
        return $final;


        /*		$dbh = get_dbh();
		$query = "SELECT db22026 as value, COUNT(*) AS freq FROM ols_user_supp_answers
		WHERE 1
		$where_sql
		$group_id_sql
		$answer_id_sql
		GROUP BY db22026 ORDER BY freq DESC";
		dev_debug(__METHOD__.' - '. $query. ' - '.json_encode($params));
		$sth = $dbh->prepare($query);
		$sth->execute($params);
		$results = $sth->fetchAll(PDO::FETCH_ASSOC);

		$final = array();
		//dev_debug(__METHOD__.' - '. $sql. ' - '.json_encode($params));
		foreach ($results as $key => $value) {
			$point_name = "Not Answered";
			if($value['value'] && ! in_array(strtolower($value['value']), array("select"))){
				$point_name = $value['value'];
			}
			$point = array(
				'name' => $point_name,
				'y' => (int) $value['freq'],
			);

			if($key == 0){
				$point['sliced'] = true;
				$point['selected'] = true;
			}
			$final[] = $point;
		}
		return $final;*/
    }

    public function get_user_progress_totals($args = array())
    {
        $where_sql = "";
        $params = array();

        if (isset($args['school_id']) && $args['school_id'] != '') {
            $usergroup_sql .= " AND up.usergroup=:school_id ";
            $params['school_id'] = $args['school_id'];
        }

        if (isset($args['access_plan_id']) && $args['access_plan_id'] != '') {
            $where_sql .= " AND up.db37394=:access_plan_id ";
            $params['access_plan_id'] = $args['access_plan_id'];
        }

        if (isset($args['course_id']) && $args['course_id'] != '') {
            $where_sql .= " AND up.db37395  =:course_id ";
            $params['course_id'] = $args['course_id'];
        }

        if (isset($args['sponsor_id']) && $args['sponsor_id'] != '') {
            $where_sql .= " AND db21880 =:sponsor_id ";
            $params['sponsor_id'] = $args['sponsor_id'];
        }

        if (isset($args['group_id']) && $args['group_id'] != '') {
            $group_id_sql = "AND up.rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db27436 = :group_id )";
            $params['group_id'] = $args['group_id'];
        }

        if (isset($args['answer_id']) && $args['answer_id'] != '') {
            $answer_id_sql = "AND up.rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db21903 = :answer_id )";
            $params['answer_id'] = $args['answer_id'];
        }

        if (isset($args['activity'])) {
            if ($args['activity'] != '_') {
                list($params['from_sql'], $params['to_sql']) = explode("_", $args['activity']);
                $where_sql .= " AND db22014 <= NOW() - INTERVAL :from_sql MONTH
					       AND db22014 >= NOW() - INTERVAL :to_sql MONTH ";
            }
        }

        if (isset($args['with_drilldown'])) {
            $drilldown_sql = " AND db37396 = :module_id ";
        }
        $dbh = get_dbh();
        $query = "SELECT db21876 as access_plan , db21909 as course, db21919 as module, db21923 as unit , COUNT(distinct up.rel_id) as freq, cm.id as module_id, db22014 as date_completed 
			from ols_user_progress up 
			LEFT JOIN ols_access_plans ap ON ap.id=up.db37394 
			LEFT JOIN ols_online_courses oc ON oc.id = up.db37395 
			LEFT JOIN ols_course_modules cm ON cm.id = up.db37396 
			LEFT JOIN ols_course_units cu on cu.id=up.db22013 
			WHERE (db22014 IS NOT NULL OR db22014 !='') 
			$where_sql
			$group_id_sql
			$answer_id_sql
			$usergroup_sql
			and db21973 not like '%hidden%'
			AND (up.rec_archive='' or up.rec_archive is null)
			AND (oc.rec_archive='' or oc.rec_archive is null)
			AND (cm.rec_archive='' or cm.rec_archive is null)
			AND (cu.rec_archive='' or cu.rec_archive is null)
			GROUP BY oc.id,cm.id
			ORDER BY oc.id, cast(db21921 as SIGNED), cm.id, cast(db21939 as signed) desc, cast(db22013 as signed) desc";
        dev_debug(__METHOD__ . ' - ' . $query . ' - ' . json_encode($params));
        //error_log(__METHOD__ . ' - ' . $query . ' - ' . json_encode($params));
        $sth = $dbh->prepare($query);
        $sth->execute($params);
        $results = $sth->fetchAll(PDO::FETCH_ASSOC);
        $final = array();

        foreach ($results as $key => $value) {
            $point = array(
                'name' => $value['module'],
                'type' => 'column',
                'y' => (int)$value['freq'],
                'drilldown' => 'point_' . $key
            );
            $drilldown_items = array(
                'name' => $value['module'],
                'id' => 'point_' . $key,
                'data' => array()
            );
            if ($args['with_drilldown']) {
                $params['module_id'] = $value['module_id'];
                $query2 = "SELECT db21923 as unit , COUNT(distinct up.rel_id) as freq, db22014 as date_completed 
					from ols_user_progress up 
					LEFT JOIN ols_access_plans ap ON ap.id=up.db37394 
					LEFT JOIN ols_course_units cu on cu.id=up.db22013 
					WHERE (db22014 IS NOT NULL OR db22014 !='') 
					$where_sql
					$drilldown_sql
					$group_id_sql
					$answer_id_sql
					$usergroup_sql
					and db21973 not like '%hidden%'
					AND (up.rec_archive='' or up.rec_archive is null)
					AND (ap.rec_archive='' or ap.rec_archive is null)
					AND (cu.rec_archive='' or cu.rec_archive is null)
					GROUP BY db37395,db37396,db22013
					ORDER BY cast(db21939 as signed) asc, cast(db22013 as signed) asc
					";
                dev_debug("query2 $query2");
                //error_log("query2 $query2 - " . json_encode($params));
                $sth2 = $dbh->prepare($query2);
                $sth2->execute($params);
                $results2 = $sth2->fetchAll(PDO::FETCH_ASSOC);
                foreach ($results2 as $k => $v) {
                    if ($k == 0) {
                        $point['y'] = (int)$v['freq'];
                    }
                    $drilldown_items['data'][] = array($v['unit'], (int)$v['freq']);
                }
                $drills[] = $drilldown_items;
            }
            $final[] = $point;
        }
        return array($final, $drills, $params);
    }

    public function get_plan_title_access_code($args = array())
    {
        $dbh = get_dbh();
        $plan_and_access_code_query = "SELECT ols_access_plans.db21876 as 'title', ols_access_plans.db21881 as 'access_code'
		FROM ols_access_plans
		WHERE ols_access_plans.id = ?
		AND (ols_access_plans.rec_archive is NULL or ols_access_plans.rec_archive = '')";

        dev_debug($plan_and_access_code_query);

        $stmt = $dbh->prepare($plan_and_access_code_query);
        $stmt->execute(array($args['id']));
        $plan_and_access_code = $stmt->fetchAll(PDO::FETCH_ASSOC);
        dev_debug("plan title: " . $plan_and_access_code[0]['title'] . " & plan access code: " . $plan_and_access_code[0]['access_code']);
        return array('title' => $plan_and_access_code[0]['title'], 'access_code' => $plan_and_access_code[0]['access_code']);
    }

    public function get_course_title($args = array())
    {
        $dbh = get_dbh();
        $course_title_query = "SELECT ols_online_courses.db21909 as 'title'
		FROM ols_online_courses
		WHERE ols_online_courses.id = ?
		AND (ols_online_courses.rec_archive is NULL or ols_online_courses.rec_archive = '')";

        dev_debug($course_title_query);

        $stmt = $dbh->prepare($course_title_query);
        $stmt->execute(array($args['id']));
        $course_title = $stmt->fetchAll(PDO::FETCH_ASSOC);
        dev_debug("course title: " . $course_title[0]['title']);
        return array('title' => $course_title[0]['title']);
    }

    public function get_group_title($args = array())
    {
        $dbh = get_dbh();
        $group_title_query = "SELECT ols_general_groups.db27435 as 'title'
		FROM ols_general_groups
		WHERE ols_general_groups.id = ?
		AND (ols_general_groups.rec_archive is NULL or ols_general_groups.rec_archive = '')";

        dev_debug($group_title_query);

        $stmt = $dbh->prepare($group_title_query);
        $stmt->execute(array($args['id']));
        $group_title = $stmt->fetchAll(PDO::FETCH_ASSOC);
        dev_debug("group title: " . $group_title[0]['title']);
        return array('title' => $group_title[0]['title']);
    }

    public function get_inactivity_stats($args = array(), $extras = array())
    {
        $dbh = get_dbh();
        $args = array_merge($args, $extras);
        if ($args['from']) {
            $params['from_sql'] = (int)$args['from'];
        } else {
            $params['from_sql'] = 3;
        }

        if ($args['to']) {
            $params['to_sql'] = (int)$args['to'];
        } else {
            $params['to_sql'] = 6;
        }

        if ($args['sponsor_id']) {
            $params['sponsor_id'] = (int)$args['sponsor_id'];
            $sponsor_id_sql = " AND db21880 = :sponsor_id";
        }

        $params['course_id'] = (int)$args['course_id'];

        $query = "SELECT 
					    db21909 AS course,
					    oc.id,
					    up.rel_id,
					    db22014 AS date_completed
					FROM
					    ols_user_progress up
					        LEFT JOIN
					    ols_online_courses oc ON oc.id = up.db37395
					    	LEFT JOIN
					    ols_access_plans ap ON ap.id = up.db37394
					WHERE
					    	(db22014 IS NOT NULL OR db22014 != '')
					        AND db22014 <= NOW() - INTERVAL :from_sql MONTH
					        AND db22014 >= NOW() - INTERVAL :to_sql MONTH
					        AND oc.id = :course_id
					        $sponsor_id_sql
					GROUP BY up.rel_id , up.db37395
					ORDER BY up.date DESC , db37395 DESC
					";
        $stmt = $dbh->prepare($query);
        $stmt->execute($params);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        dev_debug(__METHOD__ . " - $query - " . $params);
        return count($results);
    }

    public function get_monthly_registered_learners($args = array())
    {
        $dbh = get_dbh();

        $monthly_registered_learners = array();
        $execution_params = array();

        $access_plan_sql = "AND ols_user_access_plan.db22019 = ?";
        $execution_params[] = $args['access_plan_id'];

        if ($args['school_id'] && $args['school_id'] != null) {
            $school_id_sql = "AND ols_user_access_plan.usergroup = ?";
            $execution_params[] = $args['school_id'];
        } else {
            $school_id_sql = "";
        }

        $query = "SELECT YEAR(ols_user_access_plan.date) as year, 
					MONTH(ols_user_access_plan.date) as month, 
					COUNT( ols_user_access_plan.rel_id) as num_learners
				FROM ols_user_access_plan
					INNER JOIN ols_access_plans ON ols_user_access_plan.db22019 = ols_access_plans.id
					INNER JOIN core_students ON core_students.id = ols_user_access_plan.rel_id 
				WHERE ols_user_access_plan.db22031='2' 
				$access_plan_sql
				$school_id_sql
				AND (ols_user_access_plan.rec_archive IS NULL OR ols_user_access_plan.rec_archive = '')
				AND (core_students.rec_archive is NULL or core_students.rec_archive = '') 
				AND (ols_access_plans.rec_archive is NULL or ols_access_plans.rec_archive = '')
				GROUP BY YEAR(ols_user_access_plan.date), MONTH(ols_user_access_plan.date)";
        dev_debug($query . "AND ols_access_plans.id = " . $args['access_plan_id']);

        $stmt = $dbh->prepare($query);
        $stmt->execute($execution_params);

        $monthly_registered_learners = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $count_records = count($monthly_registered_learners);
        $previous_value = 0;
        if ($count_records > 0) {
            $my_monthly_registered_learners = [];
            $start_year = $monthly_registered_learners[0]['year'];
            $start_month = $monthly_registered_learners[0]['month'];
            $end_year = date("Y");//$monthly_registered_learners[$count_records - 1]['year'];
            $end_month = date("n");//$monthly_registered_learners[$count_records - 1]['month'];
            for ($year = $start_year; $year <= $end_year; $year++) {
                for ($month = 1; $month <= 12; $month++) {
                    foreach ($monthly_registered_learners as $monthly_registered_learner) {
                        if ($monthly_registered_learner['year'] == $year && $monthly_registered_learner['month'] == $month) {
                            $found = true;
                            $my_monthly_registered_learners[] = $monthly_registered_learner;
                            break;
                        }
                    }
                    if ($found == false) {
                        $my_monthly_registered_learners[] = array('year' => $year, 'month' => $month, 'num_learners' => 0);
                    }
                    $found = false;
                    //dev_debug("Mymonthly_registered_learners: BEGIN **$year**$end_year**$month**$end_month**" );
                    if ($year == $end_year && $month == $end_month) {
                        break;
                    }

                }
            }
            $monthly_registered_learners = $my_monthly_registered_learners;

        }
        dev_debug("monthly_registered_learners: BEGIN **$end_year**$end_month**");
        dev_debug(print_r($monthly_registered_learners, true));
        dev_debug("monthly_registered_learners: END");
        return $monthly_registered_learners;
    }

    public function get_monthly_active_learners($args = array())
    {
        $dbh = get_dbh();

        $monthly_registered_learners = array();
        $execution_params = array();

        $access_plan_sql = "AND db37394 = ?";
        $execution_params[] = $args['access_plan_id'];

        if ($args['school_id'] && $args['school_id'] != null) {
            $school_id_sql = "AND ols_user_progress.usergroup = ?";
            $execution_params[] = $args['school_id'];
        } else {
            $school_id_sql = "";
        }

        $query = "SELECT YEAR(ols_user_progress.date) as year, 
					MONTH(ols_user_progress.date) as month, 
					COUNT( DISTINCT(ols_user_progress.rel_id)) as num_learners
				FROM ols_user_progress
				LEFT JOIN ols_user_access_plan on CAST(ols_user_progress.rel_id AS CHAR) =  ols_user_access_plan.rel_id
				WHERE ols_user_access_plan.db22031='2' 
				$access_plan_sql
				$school_id_sql
				AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '')
				GROUP BY YEAR(ols_user_progress.date), MONTH(ols_user_progress.date)";
        dev_debug($query);

        $stmt = $dbh->prepare($query);
        $stmt->execute($execution_params);

        $monthly_actual_learners = $stmt->fetchAll(PDO::FETCH_ASSOC);

        dev_debug("monthly_actual_learners: " . $monthly_actual_learners);
        return $monthly_actual_learners;
    }

    public function get_monthly_actual_learners_by_course($args = array())
    {
        $dbh = get_dbh();

        $monthly_actual_learners = array();
        $execution_params = array();

        $course_id_sql = "AND db37395 = ?";
        $execution_params[] = "{$args['course_id']}";
        $my_access_plan_sql = "AND db37394 IN (SELECT ols_user_access_plan.db22019  FROM ols_user_access_plan WHERE ols_user_access_plan.db22031='2' AND (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = ''))";
        if ($args['access_plan_id']) {
            $access_plan_sql = "AND db37394 = ?";
            $execution_params[] = $args['access_plan_id'];
        } else {
            $my_access_plan_sql = '';
        }

        if ($args['school_id'] && $args['school_id'] != null) {
            $school_id_sql = "AND ols_user_progress.usergroup = ?";
            $execution_params[] = "{$args['school_id']}";
        } else {
            $school_id_sql = "";
        }
        if ($args['group_id'] && $args['group_id'] != '') {
            $group_join_sql = "JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_progress.rel_id";
            $group_id_sql = "AND ols_user_chal_answers.db56592 = ?";
            //$group_id_sql = "AND rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db27436 = ? )";
            $execution_params[] = $args['group_id'];
        } else {
            $group_id_sql = "";
            $group_join_sql = '';
        }

        if ($args['answer_id'] && $args['answer_id'] != '') {
            $answer_id_sql = "AND rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db21903 = ? )";
            $execution_params[] = $args['answer_id'];
        } else {
            $answer_id_sql = "";
        }

        if ($args['access_plan_list'] && $args['access_plan_list'] != '') {

            $access_plans_list = "'" . implode("','", explode(",", $args['access_plan_list'])) . "'";

            $access_plans_list_sql = "AND db37394 IN (" . $access_plans_list . ")";
        } else {
            $my_access_plan_sql = '';
        }

        $query = "SELECT YEAR(unique_user_course_records.date) as year, 
					MONTH(unique_user_course_records.date) as month, 
					COUNT(DISTINCT unique_user_course_records.rel_id) as actual_learners
				FROM 
				   (SELECT ols_user_progress.usergroup, ols_user_progress.rel_id, ols_user_progress.date, ols_user_progress.rec_archive, db22014, db37394, db37395 FROM ols_user_progress $group_join_sql
				       WHERE db22014 IS NOT NULL
				        $course_id_sql 
				        $access_plan_sql 
				        $school_id_sql
				        $group_id_sql
				        $answer_id_sql
				        $access_plans_list_sql
				        $my_access_plan_sql
				       AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '')
				       /*AND db37394 IN (SELECT ols_user_access_plan.db22019  FROM ols_user_access_plan WHERE ols_user_access_plan.db22031='2' AND (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = ''))*/

				    GROUP BY ols_user_progress.rel_id, ols_user_progress.db37395) as unique_user_course_records
					INNER JOIN core_students ON core_students.id = CAST(unique_user_course_records.rel_id as unsigned)
				WHERE 1=1
			
				AND (core_students.rec_archive is NULL or core_students.rec_archive = '') 
				GROUP BY YEAR(unique_user_course_records.date), MONTH(unique_user_course_records.date)";

        dev_debug("$query db37395(courseid)**$args[course_id]**db37394(accessplanlist)**$access_plans_list**");

        $stmt = $dbh->prepare($query);
        $stmt->execute($execution_params);

        $monthly_actual_learners = $stmt->fetchAll(PDO::FETCH_ASSOC);

        dev_debug("monthly_actual_learners: " . $monthly_actual_learners);
        return $monthly_actual_learners;
    }

    public function get_monthly_actual_learners($args = array())
    {
        $dbh = get_dbh();

        $monthly_actual_learners = array();
        $execution_params = array();
        $course_id_sql = "";
        if (!empty($args['course_id']) && $args['course_id'] != 0) {
            $course_id_sql = "AND db37395 = ?";
            $execution_params[] = $args['course_id'];
        }
        $my_access_plan_sql = "AND db37394 IN (SELECT ols_user_access_plan.db22019  FROM ols_user_access_plan WHERE ols_user_access_plan.db22031='2' AND (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = ''))";
        if ($args['access_plan_id']) {
            $access_plan_sql = "AND db37394 = ?";
            $execution_params[] = $args['access_plan_id'];
        } else {
            $my_access_plan_sql = '';
        }

        if ($args['school_id'] && $args['school_id'] != null) {
            $school_id_sql = "AND ols_user_progress.usergroup = ?";
            $execution_params[] = $args['school_id'];
        } else {
            $school_id_sql = "";
        }
        if ($args['group_id'] && $args['group_id'] != '') {
            $group_join_sql = "JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = CAST(ols_user_progress.rel_id as CHAR)";
            $group_id_sql = "AND ols_user_chal_answers.db56592 = ?";
            //$group_id_sql = "AND rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db27436 = ? )";
            $execution_params[] = $args['group_id'];
        } else {
            $group_id_sql = "";
            $group_join_sql = '';
        }

        if ($args['answer_id'] && $args['answer_id'] != '') {
            $answer_id_sql = "AND rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db21903 = ? )";
            $execution_params[] = $args['answer_id'];
        } else {
            $answer_id_sql = "";
        }

        if ($args['access_plan_list'] && $args['access_plan_list'] != '') {
            $access_plans_list = $args['access_plan_list'];
            $access_plans_list_sql = "AND db37394 IN (" . $access_plans_list . ")";
        } else {
            $my_access_plan_sql = '';
        }

        $query = "SELECT YEAR(unique_user_course_records.date) as year, 
					MONTH(unique_user_course_records.date) as month, 
					COUNT(DISTINCT unique_user_course_records.rel_id) as actual_learners
				FROM 
				   (SELECT ols_user_progress.usergroup, ols_user_progress.rel_id, ols_user_progress.date, ols_user_progress.rec_archive, db22014, db37394, db37395 FROM ols_user_progress $group_join_sql
				       WHERE db22014 IS NOT NULL
				        $course_id_sql 
				        $access_plan_sql 
				        $school_id_sql
				        $group_id_sql
				        $answer_id_sql
				        $access_plans_list_sql
				        $my_access_plan_sql
				       AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '')
				       /*AND db37394 IN (SELECT ols_user_access_plan.db22019  FROM ols_user_access_plan WHERE ols_user_access_plan.db22031='2' AND (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = ''))*/

				    GROUP BY ols_user_progress.rel_id, ols_user_progress.db37395) as unique_user_course_records
					INNER JOIN core_students ON core_students.id = CAST(unique_user_course_records.rel_id as unsigned)
				WHERE 1=1
			
				AND (core_students.rec_archive is NULL or core_students.rec_archive = '') 
				GROUP BY YEAR(unique_user_course_records.date), MONTH(unique_user_course_records.date)";
        $stmt = $dbh->prepare($query);
        $stmt->execute($execution_params);
        $monthly_actual_learners = $stmt->fetchAll(PDO::FETCH_ASSOC);

        dev_debug("monthly_actual_learners: " . $monthly_actual_learners);
        return $monthly_actual_learners;
    }

    /** ===================================
     * Get Group Reports
     * ====================================    */
    function get_group_reports($args = array())
    {
        $dbh = get_dbh();
        $db = new Db_helper();

        $monthly_actual_learners = array();
        $execution_params = array();

        if ($args['group_id']) {
            $group_id_sql = "AND ols_general_groups.id = ?";
            $execution_params[] = $args['group_id'];
        }

        if ($args['sponsor_id']) {
            $sponsor_sql = "AND ols_general_groups.db30486= ?";
            $execution_params[] = $args['sponsor_id'];
        }

        if ($args['school_id'] && $args['school_id'] != null) {
            $school_id_sql = "AND ols_general_groups.usergroup = ?";
            $execution_params[] = $args['school_id'];
        }

        $query = "
			SELECT 
				*
			FROM
				ols_general_groups 
			WHERE
				1
				$group_id_sql
				$sponsor_sql
				$school_id_sql
			ORDER BY db27435 ASC";

        dev_debug($query);
//        error_log("Group Reports: $query");
        $stmt = $dbh->prepare($query);
        $stmt->execute($execution_params);
        $group_info = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $results_list = array();
        $results_list["group_id"] = $group_info[0]['id'];
        $results_list["group_name"] = $group_info[0]['db27435'];
        $results_list["group_sponsor_id"] = $group_info[0]['db30486'];
        //get courses on the access plan
        //$access_plan_courses_args = array('access_plan' => $args['plan_id'], 'group_id' => $args['group_id'], 'cumulative_learners_required' => $args['cumulative_learners_required'], 'transitioned_courses' => "true");
        $access_plan_courses_args = array('access_plan' => $args['plan_id'], 'group_id' => $args['group_id'], 'transitioned_courses' => "true");
        $access_plan_courses = $this->get_access_plan_courses($access_plan_courses_args);

        //------------------------------------------------
        foreach ($access_plan_courses as $access_plan_course) {
            /*
			$no_of_registered_users = pull_field("ols_user_access_plan INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id", "count(*)", "WHERE db22031='2' AND db21880 = '$args[sponsor_id]' and (ols_access_plans.rec_archive is null or ols_access_plans.rec_archive = '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND db22019='$sponsor_access_plan[id]'");
			$no_learners_started = pull_field("ols_user_progress INNER JOIN core_students ON core_students.id = ols_user_progress.rel_id INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_progress.db37394 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = core_students.id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id", "count(DISTINCT(ols_user_progress.rel_id))", "WHERE ols_user_progress.db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND ols_user_progress.db37394 IN (SELECT ols_user_access_plan.db22019  FROM ols_user_access_plan WHERE ols_user_access_plan.db22031='2' AND (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '')) AND ols_access_plans.db21880 = '$args[sponsor_id]' AND ols_user_progress.db37394='$sponsor_access_plan[id]'");
			//$avg_modules_completed_per_learner = pull_field("ols_user_progress LEFT JOIN ols_user_access_plan on ols_user_progress.rel_id = ols_user_access_plan.rel_id LEFT JOIN ols_course_units on ols_user_progress.db22013 = ols_course_units.id LEFT JOIN ols_course_modules on ols_course_units.rel_id = ols_course_modules.id", "count(*) DIV count(DISTINCT(ols_user_progress.rel_id))", "WHERE db22019 = $row[apc_rel_id] AND db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (ols_user_access_plan.rec_archive IS NULL OR ols_user_access_plan.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND db22031='2' AND (ols_course_modules.rel_id = $row[db21911])");
			$new_learners_this_month = pull_field("ols_user_access_plan INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id", "count(*)", "WHERE db22031='2' AND db21880 = '$args[sponsor_id]' and (ols_access_plans.rec_archive is null or ols_access_plans.rec_archive = '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND UNIX_TIMESTAMP(ols_user_access_plan.date) >= UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY - INTERVAL 1 MONTH) AND UNIX_TIMESTAMP(ols_user_access_plan.date) <  UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY) AND db22019='$sponsor_access_plan[id]'");
			$new_learners_last_month = pull_field("ols_user_access_plan INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id", "count(*)", "WHERE db22031='2' AND db21880 = '$args[sponsor_id]' and (ols_access_plans.rec_archive is null or ols_access_plans.rec_archive = '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND UNIX_TIMESTAMP(ols_user_access_plan.date) >= UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY - INTERVAL 2 MONTH) AND UNIX_TIMESTAMP(ols_user_access_plan.date) <  UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY - INTERVAL 1 MONTH) AND db22019='$sponsor_access_plan[id]'");
			*/
            //$course_no_of_registered_users = pull_field("ols_user_access_plan INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id", "count(*)", "WHERE db22019 = $sponsor_access_plan[id] AND db22031='2' AND db21880 = '$args[sponsor_id]' and (ols_access_plans.rec_archive is null or ols_access_plans.rec_archive = '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND db27436 = '$row[id]'");
            //AFY changed this on 14/04/20201 $course_no_learners_started = pull_field("ols_user_progress INNER JOIN core_students ON core_students.id = ols_user_progress.rel_id", "count(DISTINCT(ols_user_progress.rel_id))", "WHERE ols_user_progress.db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND ols_user_progress.db37394 = '$args[plan_id]' AND ols_user_progress.db37394 IN (SELECT ols_user_access_plan.db22019  FROM ols_user_access_plan WHERE ols_user_access_plan.db22031='2' AND (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '')) AND ols_user_progress.db37395 = '$access_plan_course[id]' AND ols_user_progress.rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db27436 = '$args[group_id]' )");

            $course_no_learners_started = pull_field("ols_user_progress INNER JOIN core_students ON core_students.id = ols_user_progress.rel_id JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_progress.rel_id", "count(DISTINCT(ols_user_progress.rel_id))", "WHERE ols_user_progress.db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND ols_user_progress.db37394 = '$args[plan_id]' AND ols_user_progress.db37395 = '$access_plan_course[id]' AND ols_user_chal_answers.db56592= '$args[group_id]'");
            //old way of finding course_no_learners_started
            //$course_no_learners_started = pull_field("ols_user_progress INNER JOIN core_students ON core_students.id = ols_user_progress.rel_id INNER JOIN ols_user_access_plan on ols_user_progress.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id", "count(DISTINCT(ols_user_progress.rel_id))", "WHERE db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (ols_user_access_plan.rec_archive IS NULL OR ols_user_access_plan.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND db22031='2' AND db21880 = $args[sponsor_id] AND db27436 = $args[group_id] AND db22019 = $args[plan_id]");

            //$course_new_learners_this_month = pull_field("ols_user_access_plan INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id", "count(*)", "WHERE db22019 = $sponsor_access_plan[id] AND db22031='2' AND db21880 = '$args[sponsor_id]' and (ols_access_plans.rec_archive is null or ols_access_plans.rec_archive = '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND db27436 = '$row[id]' AND UNIX_TIMESTAMP(ols_user_access_plan.date) >= UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY - INTERVAL 1 MONTH) AND UNIX_TIMESTAMP(ols_user_access_plan.date) <  UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY)");
            //$course_new_learners_last_month = pull_field("ols_user_access_plan INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id", "count(*)", "WHERE db22019 = $sponsor_access_plan[id] AND db22031='2' AND db21880 = '$args[sponsor_id]' and (ols_access_plans.rec_archive is null or ols_access_plans.rec_archive = '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND db27436 = '$row[id]' AND UNIX_TIMESTAMP(ols_user_access_plan.date) >= UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY - INTERVAL 2 MONTH) AND UNIX_TIMESTAMP(ols_user_access_plan.date) <  UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY - INTERVAL 1 MONTH)");
            $actual_learners_by_month = $this->get_monthly_actual_learners_by_course(array('course_id' => $access_plan_course['id'], 'access_plan_id' => $args['plan_id'], 'school_id' => $args['school_id'], 'group_id' => $args['group_id']));

            //$course_no_units_completed = pull_field("ols_user_progress INNER JOIN ols_user_access_plan on ols_user_progress.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 INNER JOIN ols_user_chal_answers ON ols_user_chal_answers.rel_id = ols_user_access_plan.rel_id INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id INNER JOIN ols_course_units on ols_user_progress.db22013 = ols_course_units.id INNER JOIN ols_course_modules on ols_course_units.rel_id = ols_course_modules.id", "count(*)", "WHERE db22019 = $sponsor_access_plan[id] AND db22031='2' AND db21880 = '$args[sponsor_id]' AND db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (ols_user_access_plan.rec_archive IS NULL OR ols_user_access_plan.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND db22031='2' AND (ols_course_modules.rel_id = $access_plan_course[id])");
            //$course_number_of_units = pull_field("ols_course_units INNER JOIN ols_course_modules on ols_course_units.rel_id = ols_course_modules.id", "count(*)", "WHERE ols_course_modules.rel_id = $access_plan_course[id] and (ols_course_units.rec_archive is NULL OR ols_course_units.rec_archive = '') AND (ols_course_modules.rec_archive is NULL or ols_course_modules.rec_archive = '')");
            $group_course_info = array(
                "access_plan" => $args['plan_id'],
                "course_id" => $access_plan_course['id'],
                "course_title" => $access_plan_course['title'],
                "alternative_title" => $access_plan_course['alternative_title'],
                "course_no_of_registered_users" => $course_no_of_registered_users,
                "course_no_learners_started" => $course_no_learners_started,
                "course_new_learners_this_month" => $course_new_learners_this_month,
                "course_new_learners_last_month" => $course_new_learners_last_month,
                'monthly_actual_learners' => $actual_learners_by_month,
                //"course_avg_units_completed" => $course_no_units_completed.'**'.$course_no_learners_started.'**'.$course_number_of_units.'**'.round((($course_no_units_completed /$course_no_learners_started)/$course_number_of_units) * 100),((($course_no_units_completed / $course_no_learners_started) / $course_number_of_units) * 100),
            );

            $results_list['answer_group_courses'][] = $group_course_info;
        }
        //---------------------------------------------------------------
        return $results_list;
    }

    /** ===================================
     * Get Answer Reports
     * ====================================    */
    function get_answer_reports($args = array())
    {
        $dbh = get_dbh();
        $db = new Db_helper();

        $monthly_actual_learners = array();

        $results_list = array();
        //get courses on the access plan
        $access_plan_courses_args = array('access_plan' => $args['plan_id'], 'answer_id' => $args['answer_id'], 'cumulative_learners_required' => $args['cumulative_learners_required'], 'transitioned_courses' => "true");
        $access_plan_courses = $this->get_access_plan_courses($access_plan_courses_args);

        //------------------------------------------------
        foreach ($access_plan_courses as $access_plan_course) {
            dev_debug("DEBUG 2 SELECT count(DISTINCT(ols_user_progress.rel_id)) FROM ols_user_progress INNER JOIN core_students ON core_students.id = ols_user_progress.rel_id WHERE ols_user_progress.db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND ols_user_progress.db37394 = '$args[plan_id]' AND ols_user_progress.db37394 IN (SELECT ols_user_access_plan.db22019  FROM ols_user_access_plan WHERE ols_user_access_plan.db22031='2' AND (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '')) AND ols_user_progress.db37395 = '$access_plan_course[id]' AND ols_user_progress.rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db27436 = '$args[answer_id]'");
            $course_no_learners_started = pull_field("ols_user_progress INNER JOIN core_students ON core_students.id = ols_user_progress.rel_id", "count(DISTINCT(ols_user_progress.rel_id))", "WHERE ols_user_progress.db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND ols_user_progress.db37394 = '$args[plan_id]' AND ols_user_progress.db37395 = '$access_plan_course[id]' AND ols_user_progress.rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db27436 = '$args[answer_id]' )");
            $actual_learners_by_month = $this->get_monthly_actual_learners_by_course(array('course_id' => $access_plan_course['id'], 'access_plan_id' => $args['plan_id'], 'school_id' => $args['school_id'], 'answer_id' => $args['answer_id']));
            $group_course_info = array(
                "access_plan" => $args['plan_id'],
                "course_id" => $access_plan_course['id'],
                "course_title" => $access_plan_course['title'],
                "alternative_title" => $access_plan_course['alternative_title'],
                "course_no_learners_started" => $course_no_learners_started,
                'monthly_actual_learners' => $actual_learners_by_month,
            );

            $results_list['answer_group_courses'][] = $group_course_info;
        }
        //---------------------------------------------------------------
        return $results_list;
    }


    /** ===================================
     * Get Sponsor Groups (Waves) and Sponsors in each wave
     * <AUTHOR>
     * ====================================    */
    function get_waves($args = array())
    {
        //get all waves belonging to usergroup
        $dbh = get_dbh();
        $execution_params = array();

        if ($args['school_id']) {
            $school_id_sql = " AND ols_sponsor_groups.usergroup = :school_id";
            $execution_params['school_id'] = $args['school_id'];
        } else {
            $school_id_sql = " AND ols_sponsor_groups.usergroup = :school_id";
            $execution_params['school_id'] = $_SESSION['usergroup'];
        }

        $query = "SELECT id, db42888 as 'name' FROM ols_sponsor_groups WHERE 1 $school_id_sql ORDER BY db42888 ASC";
        $stmt = $dbh->prepare($query);
        $stmt->execute($execution_params);
        $sponsor_groups = $stmt->fetchAll(PDO::FETCH_ASSOC);

        dev_debug(__METHOD__ . ' - ' . $query);
        return $sponsor_groups;
    }

    /** ===================================
     * Get Sponsor Types and Sponsors in each type
     * <AUTHOR>
     * ====================================    */
    function get_sponsor_types($args = array())
    {
        //get all types belonging to usergroup
        $dbh = get_dbh();
        $execution_params = array();

        if ($args['school_id']) {
            $school_id_sql = " AND ols_sponsor_types.usergroup = :school_id";
            $execution_params['school_id'] = $args['school_id'];
        } else {
            $school_id_sql = " AND ols_sponsor_types.usergroup = :school_id";
            $execution_params['school_id'] = $_SESSION['usergroup'];
        }

        $query = "SELECT id, db30485 as 'name' FROM ols_sponsor_types WHERE 1 $school_id_sql ORDER BY db30485 ASC";
        $stmt = $dbh->prepare($query);
        $stmt->execute($execution_params);
        $sponsor_types = $stmt->fetchAll(PDO::FETCH_ASSOC);

        dev_debug(__METHOD__ . ' - ' . $query);
        return $sponsor_types;
    }

    /** ===================================
     * Get Sponsor Groups (Waves) and Sponsors in each wave
     * <AUTHOR>
     * ====================================    */
    function get_wave_sponsors($args = array())
    {
        //get all sponsors belonging to wave
        $dbh = get_dbh();
        $execution_params = array();

        if ($args['school_id']) {
            $school_id_sql = " AND ols_sponsors.usergroup = :school_id";
            $execution_params['school_id'] = $args['school_id'];
        } else {
            $school_id_sql = " AND ols_sponsors.usergroup = :school_id";
            $execution_params['school_id'] = $_SESSION['usergroup'];
        }

        if ($args['sponsor_group_id']) {
            $sponsor_group_id_sql = " AND ols_sponsors.db42891 IN ({$args['sponsor_group_id']})";
        }

        if ($args['sponsor_type_id']) {
            $sponsor_type_id_sql = " AND ols_sponsors.db33633 IN ({$args['sponsor_type_id']})";
        }

        $sponsors_query = "SELECT id,db21861 as sponsor_name FROM ols_sponsors WHERE 1 $school_id_sql {$sponsor_group_id_sql} {$sponsor_type_id_sql} ORDER BY db21861 ASC";
        $stmt = $dbh->prepare($sponsors_query);
        $stmt->execute($execution_params);
        dev_debug(__METHOD__ . ' - ' . $sponsors_query);

        $sponsors = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return $sponsors;
    }


    /** ===================================
     * Get the reports for each of the sponsors in a wave
     * <AUTHOR>
     * ====================================
     */
    function get_wave_reports($args = array())
    {
        $plans = $args['sponsor_access_plans'];

        //get courses on the access plans
        $sponsor_courses_args = array('course_id' => $args['course_id'], 'access_plan_list' => $plans, 'cumulative_learners_required' => $args['cumulative_learners_required'], 'school_id' => $args['school_id']);
        $sponsor_courses = $this->get_sponsor_course_reports($sponsor_courses_args);

        return $sponsor_courses;
    }

    /** ===================================
     * Get all reports by course for each sponsor
     * ====================================
     **/
    function get_sponsor_course_reports($args = array())
    {
        //get cumulative learners for each course across all the access plans that the course is part of
        if ($args['cumulative_learners_required']) {
            $actual_learners_by_month = $this->get_monthly_actual_learners(array('course_id' => $args['course_id'], 'access_plan_list' => $args['access_plan_list'], 'school_id' => $args['school_id']));
        }
        $course = array(
            'id' => $args['course_id'],
            'monthly_actual_learners' => $actual_learners_by_month
        );

        return $course;
    }

    /** ===================================
     * Get all plans belonging to a sponsor
     * ====================================
     **/
    function get_sponsor_plans($args = array())
    {
        $dbh = get_dbh();
        $execution_params = array();

        if ($args['school_id']) {
            $school_id_sql = " AND ols_access_plans.usergroup = :school_id";
            $execution_params['school_id'] = $args['school_id'];
        } else {
            $school_id_sql = " AND ols_access_plans.usergroup = :school_id";
            $execution_params['school_id'] = $_SESSION['usergroup'];
        }

        if ($args['sponsor_id']) {
            $sponsor_id_sql = " AND ols_access_plans.db21880 = :sponsor_id";
            $execution_params['sponsor_id'] = $args['sponsor_id'];
        }
        $fields = "ols_access_plans.id";
        if (!empty($args["plan_ids_only"])) {
            $fields = "GROUP_CONCAT(CONCAT(\"'\",ols_access_plans.id,\"'\"))";
        }

        $query = "SELECT $fields FROM ols_access_plans WHERE 1 $school_id_sql $sponsor_id_sql AND (ols_access_plans.rec_archive is null or ols_access_plans.rec_archive='')";

        $stmt = $dbh->prepare($query);
        $stmt->execute($execution_params);
        $plans = $stmt->fetchAll(PDO::FETCH_COLUMN);

        return $plans;
    }

    /** ===================================
     * Get all unique courses belonging to a sponsor
     * ====================================
     **/
    function get_sponsor_courses($args = array())
    {
        $transitioned_courses_sql = '';
        $dbh = get_dbh();
        if (!array_key_exists('transitioned_courses', $args)) {
            $transitioned_courses_sql = "AND (ols_access_plan_courses.db262370 IS NULL OR ols_access_plan_courses.db262370 ='active')";
        }

        if (!empty($args['all_access_plans'])) {
            $access_plans_list = $args['all_access_plans'];
            $access_plans_list_sql = " AND ols_access_plan_courses.rel_id IN (" . $access_plans_list . ")";
        }

        if (!empty($args['school_id'])) {
            $school_id_sql = " AND ols_access_plan_courses.usergroup ='" . $args['school_id'] . "'";
        } else {
            $school_id_sql = " AND ols_access_plan_courses.usergroup = '" . $_SESSION['usergroup'] . "'";
        }

        //select unique course ids across all the sponsor's access plans
        $query = "
			SELECT 
				distinct ols_access_plan_courses.db21911 as id, ols_online_courses.db21909 as title, ols_online_courses.db260924 as alternative_title
			FROM
				ols_access_plan_courses 
				LEFT JOIN ols_online_courses ON ols_online_courses.id=ols_access_plan_courses.db21911
			WHERE
				1
				$school_id_sql
				$access_plans_list_sql
				$transitioned_courses_sql
				AND (ols_access_plan_courses.rec_archive is null or ols_access_plan_courses.rec_archive='')
			ORDER BY ols_access_plan_courses.id ASC";

        dev_debug("Get Sponsor Courses $query");
        $sth = $dbh->prepare($query);
        $sth->execute();
        $course = array();
        $all_courses = array();
        $results = $sth->fetchAll(PDO::FETCH_ASSOC);
        foreach ($results as $result_course) {

            $course['id'] = $result_course['id'];
            $course['title'] = $result_course['title'];
            $course['alternative_title'] = $result_course['alternative_title'];

            if (!empty($args['no_started_learners'])) {
                $no_learners_started = pull_field("ols_user_progress INNER JOIN core_students ON core_students.id = ols_user_progress.rel_id", "count(DISTINCT(ols_user_progress.rel_id))", "WHERE ols_user_progress.db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND (ols_user_progress.rel_id IS NOT NULL AND ols_user_progress.rel_id !='') AND (db37395 = '$result_course[id]')");
                if (!$no_learners_started || $no_learners_started == '') {
                    $no_learners_started = 0;
                }
                $course['no_started_learners'] = $no_learners_started;
            }
            $all_courses[] = $course;
        }
        return $all_courses;
    }

    /** ===================================
     * Get stats beloning to a group i.e geographical group
     * ====================================
     **/
    function get_group_stats($args = array())
    {
        $db = new Db_helper();

        //get courses on the access plan
        $access_plan_courses_args = array('access_plan' => $args['plan_id'], 'group_id' => $args['group_id'], 'transitioned_courses' => "true");
        $access_plan_courses = $this->get_access_plan_courses($access_plan_courses_args);
        if (isset($args['group_count'])) {
            $course_no_of_registered_users = $args['group_count'];
        } else {
            if (($args['group_id']) && ($args['plan_id'])) {
                $course_no_of_registered_users = pull_field("ols_user_access_plan JOIN core_students ON core_students.id = ols_user_access_plan.rel_id", "count(DISTINCT(ols_user_access_plan.rel_id))", "WHERE db22019 = '$args[plan_id]' AND db22031='2' AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND ols_user_access_plan.rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db27436 = '$args[group_id]' )");
            }
        }
        //------------------------------------------------
        foreach ($access_plan_courses as $access_plan_course) {
            if (($args['group_id']) && ($args['plan_id'])) {
                //$course_no_of_registered_users = pull_field("ols_user_access_plan JOIN core_students ON core_students.id = ols_user_access_plan.rel_id", "count(DISTINCT(ols_user_access_plan.rel_id))", "WHERE db22019 = '$args[plan_id]' AND db22031='2' AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND (ols_user_access_plan.rec_archive is null or ols_user_access_plan.rec_archive = '') AND ols_user_access_plan.rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db27436 = '$args[group_id]' )");

                //$course_no_learners_started = pull_field("ols_user_progress INNER JOIN core_students ON core_students.id = ols_user_progress.rel_id", "count(DISTINCT(ols_user_progress.rel_id))", "WHERE ols_user_progress.db22014 IS NOT NULL AND (ols_user_progress.rec_archive IS NULL OR ols_user_progress.rec_archive = '') AND (core_students.rec_archive is NULL or core_students.rec_archive = '') AND ols_user_progress.db37394 = $args[plan_id] AND ols_user_progress.db37394 IN (SELECT ols_user_access_plan.db22019  FROM ols_user_access_plan WHERE ols_user_access_plan.db22031='2' AND (ols_user_access_plan.rec_archive is NULL or ols_user_access_plan.rec_archive = '')) AND ols_user_progress.db37395 = $access_plan_course[id] AND ols_user_progress.rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db27436 = $args[group_id] )");

                $group_course_info = array(
                    "access_plan" => $args['plan_id'],
                    "course_id" => $access_plan_course['id'],
                    //"title" => $access_plan_course['title'],
                    "title" => $args['plan_title'],
                    "no_registered_users" => $course_no_of_registered_users
                    //"no_started_learners" => $course_no_learners_started,
                );

                $results_list['group_courses'][] = $group_course_info;
            } else {
                $group_course_info = array(
                    "access_plan" => $args['plan_id'],
                    "course_id" => $access_plan_course['id'],
                    "title" => $args['plan_title'],
                    "no_registered_users" => 0
                );
                $results_list['group_courses'][] = $group_course_info;
            }
        }
        //---------------------------------------------------------------
        return $results_list;

    }


    function existing_questionaires()
    {

        $dbh = get_dbh();
        $query = "SELECT
			inst_pages.page_id as quiz_id,
			inst_pages.title 
			FROM
			 inst_pages
			 left join inst_table on inst_pages.page_id=inst_table.pg_id
			where  inst_pages.page_name = '' and  inst_pages.project='' and 
			inst_table.usergroup='" . $_SESSION['usergroup'] . "' group by page_id";
        dev_debug($query);


        $sth = $dbh->prepare($query);
        $sth->execute();

        $results_list = [];

        foreach ($row_result = $sth->fetchAll() as $row) {
            array_push($results_list, $row);
        }

        return $results_list;
    }

    function update_or_insert_survey($data)
    {
        $db = new Db_helper();
        $db->system_table_insert_or_update('ols_quiz_allocation', $data);
        return $db->lastInsertId();
    }


    function delete_survey($data)
    {
        $db = new Db_helper();
        $db->delete('ols_quiz_allocation', $data);
    }

    function get_attached_quiz($data)
    {
        $db = new Db_helper();
        $db->join("inst_pages", "inst_pages.page_id=ols_quiz_allocation.db45507", "LEFT");
        foreach ($data as $key => $value) {
            $db->where($key, $value);
        }
        return $db->get_rows('ols_quiz_allocation');
    }

    /**
     * <AUTHOR>
     * the method below gets the page ids and names of the tables with the pre and post questionnaire answes for the course given in the search string
     */
    function get_course_questionnaire_pages($course_id)
    {
        $dbh = get_dbh();

        //select page id and names for the search string
        $query = "
			SELECT 
				page_id,sys_cat_abv,concat(sys_cat_abv,'_',page_name) as questionnaire_page
			FROM
				inst_pages 
				INNER JOIN inst_cat ON inst_pages.project = inst_cat.sys_cat_id
			WHERE
				inst_pages.page_name LIKE 'course_" . $course_id . "_p%_questions%'";

        $sth = $dbh->prepare($query);
        $success = $sth->execute();
        $results = $sth->fetchAll(PDO::FETCH_ASSOC);

        return $results;
    }


    /**
     * <AUTHOR>
     * the method below gets the questions that are common to both the pre and post questionnaire for a course along with the answers to those questions
     */
    function get_common_pre_post_questions($args = array())
    {
        $dbh = get_dbh();
        $execution_params = array();

        if ($args['school_id']) {
            $school_id_sql = " AND inst_table.usergroup = :school_id";
            $execution_params['school_id'] = $args['school_id'];
        } else {
            $school_id_sql = " AND inst_table.usergroup = :school_id";
            $execution_params['school_id'] = $_SESSION['usergroup'];
        }

        if ($args['questionnaire_pages']) {
            //get the array of page ids
            $page_ids = array_column($args['questionnaire_pages'], "page_id", "sys_cat_abv");
            $page_ids_str = implode(",", $page_ids);
            $page_ids_sql = " AND inst_table.pg_id IN ($page_ids_str)";
        }

        //select the questions that make up the questionnaire pages
        $query = "
			SELECT 
				inst_table.name, count(inst_table.name) as count
			FROM
				inst_table 
			WHERE
				1
				$school_id_sql
				$page_ids_sql
			GROUP BY inst_table.name";

        $sth = $dbh->prepare($query);
        $success = $sth->execute($execution_params);
        $questions = $sth->fetchAll(PDO::FETCH_ASSOC);

        $args['questions'] = array();

        //only get the responses for those questions that are in both the pre and post questionnaires i.e if count is 2 bcz then the question is in both questionnaires
        foreach ($questions as $question) {
            if ($question['count'] == 2) {
                $args['questions'][] = $question['name'];
            }
        }

        $responses = $this->get_pre_post_questionnaire_answers($args);

        return $responses;
    }

    /**
     * <AUTHOR>
     * the method below gets the answers to the pre and post questionnaires questions for a given course
     */

    function get_pre_post_questionnaire_answers($args = array())
    {
        $dbh = get_dbh();
        $execution_params = array();
        $all_responses = array();

        if ($args['questionnaire_pages']) {
            //get the array of page names
            $page_names = array_column($args['questionnaire_pages'], "questionnaire_page", "sys_cat_abv");
            $page_ids = array_column($args['questionnaire_pages'], "page_id", "sys_cat_abv");
            $pre_course_ques_page = $page_names['preq'];
            $post_course_ques_page = $page_names['postq'];
            $pre_course_ques_page_id = $page_ids['preq'];
            $post_course_ques_page_id = $page_ids['postq'];
        }

        if ($args['sponsor_id']) {
            $sponsor_id_join = "INNER JOIN ols_user_access_plan ON ols_user_access_plan.rel_id = $pre_course_ques_page.rel_id INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 ";
            $sponsor_id_sql = " AND ols_access_plans.db21880 = :sponsor_id";
            $execution_params['sponsor_id'] = $args['sponsor_id'];
        }

        if ($args['school_id']) {
            $preq_school_id_sql = " AND $pre_course_ques_page.usergroup = :school_id";
            $postq_school_id_sql = " AND $post_course_ques_page.usergroup = :school_id";
            $execution_params['school_id'] = $args['school_id'];
        } else {
            $preq_school_id_sql = " AND $pre_course_ques_page.usergroup = :school_id";
            $postq_school_id_sql = " AND $post_course_ques_page.usergroup = :school_id";
            $execution_params['school_id'] = $_SESSION['usergroup'];
        }

        if ($args['group_id']) {
            //$group_id_join = "INNER JOIN ols_general_groups ON ols_general_groups.db30486 = ols_access_plans.db21880";
            //$group_id_sql = " AND ols_general_groups.id = :group";
            $pre_group_id_sql = "AND $pre_course_ques_page.rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db27436 = :group )";
            $post_group_id_sql = "AND $post_course_ques_page.rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db27436 = :group )";
            $execution_params['group'] = $args['group_id'];
        }

        if ($args['plan_id']) {
            $access_plan_sql = " AND ols_access_plans.id = :plan_id";
            $execution_params['plan_id'] = $args['plan_id'];
        }


        //loop through the questions and for each questions get its db_field_names for the pre an dpost questionnaires
        //once you have these, get the answers from each page
        foreach ($args['questions'] as $question) {
            $trimmed_ques = trim($question, "0123456789. ?");
            //get all answers to this questionnaire question from the relevant table

            //get the db field for each pre questionnaire question
            $query = "SELECT inst_table.db_field_name FROM inst_table WHERE inst_table.name LIKE '%$trimmed_ques%' AND inst_table.pg_id=$pre_course_ques_page_id";
            $sth = $dbh->prepare($query);
            $success = $sth->execute();
            $ques_db_field_name = $sth->fetchColumn();

            //get the responses for each pre questionnnaire questions
            $query = "SELECT 
						$ques_db_field_name as response,count(distinct $pre_course_ques_page.rel_id) as num_responses
					FROM 
						$pre_course_ques_page 
						INNER JOIN 
						$post_course_ques_page 
						ON
						$pre_course_ques_page.rel_id = $post_course_ques_page.rel_id
						$sponsor_id_join
					WHERE 
						1
						$preq_school_id_sql
						$postq_school_id_sql
						$sponsor_id_sql
						$pre_group_id_sql
						$access_plan_sql
					GROUP BY 
						$ques_db_field_name
						";
            $sth = $dbh->prepare($query);
            $success = $sth->execute($execution_params);
            $responses = $sth->fetchAll(PDO::FETCH_ASSOC);
            $preq_dets = array('ques_title' => $trimmed_ques, 'preq_stats' => array('ques_db_field' => $ques_db_field_name, 'responses' => $responses));

            //get the db field for each post questionnaire question
            $query = "SELECT inst_table.db_field_name FROM inst_table WHERE inst_table.name LIKE '%$trimmed_ques%' AND inst_table.pg_id=$post_course_ques_page_id";
            $sth = $dbh->prepare($query);
            $success = $sth->execute();
            $ques_db_field_name = $sth->fetchColumn();

            //get the responses for each post questionnnaire questions
            $query = "SELECT 
						$ques_db_field_name as response,count(distinct $post_course_ques_page.rel_id) as num_responses
					FROM 
						$post_course_ques_page 
						INNER JOIN
						$pre_course_ques_page  
						ON
						$pre_course_ques_page.rel_id = $post_course_ques_page.rel_id
						$sponsor_id_join
					WHERE 
						1
						$postq_school_id_sql
						$preq_school_id_sql
						$sponsor_id_sql
						$post_group_id_sql
						$access_plan_sql
					GROUP BY 
						$ques_db_field_name
						";
            $sth = $dbh->prepare($query);
            $success = $sth->execute($execution_params);
            $responses = $sth->fetchAll(PDO::FETCH_ASSOC);
            $postq_dets = array('postq_stats' => array('ques_db_field' => $ques_db_field_name, 'responses' => $responses));

            $all_responses[$trimmed_ques] = array_merge($preq_dets, $postq_dets);
        }

        return $all_responses;
    }


    /** ===================================
     * Get Member Acees Plan
     * ====================================    */
    function get_member_access_plan($student_id, $course_id)
    {
        $dbh = get_dbh();

        if ($student_id) {
            $student_id_sql = "AND ols_user_access_plan.rel_id='" . $student_id . "'";
        }

        if ($course_id) {
            $course_id_sql = "AND db21911='" . $course_id . "'";
        }

        $query = "
			SELECT
				db21911,
				ols_access_plans.*
			FROM 
				ols_user_access_plan 
				LEFT JOIN ols_access_plans on ols_access_plans.id = ols_user_access_plan.db22019
				LEFT JOIN ols_access_plan_courses on ols_access_plan_courses.rel_id = ols_access_plans.id
			WHERE 1
				$student_id_sql
				$course_id_sql
				AND ols_user_access_plan.db22031 = '2'
				AND (ols_access_plan_courses.rec_archive IS NULL OR ols_access_plan_courses.rec_archive = '')  
			GROUP BY db21911
			";
        dev_debug($query);


        $sth = $dbh->prepare($query);
        $sth->execute();

        $results_list = [];

        foreach ($row_result = $sth->fetchAll() as $row) {
            array_push($results_list, $row);
        }
        //$results_list = rtrim($results_list,",");
        return $results_list;

    }

    /**
     * <AUTHOR>
     * the method below gets the answers to the 3 post questionnaires questions for a given course
     * 1. Did you find this course helpful?
     * 2. Did you find this course enjoyable?
     * 3. Would you recommend this course to others?
     */

    function get_post_questionnaire_answers_only($args)
    {
        $dbh = get_dbh();
        $execution_params = array();
        $all_responses = array();

        $sponsors_access_plans = $this->get_sponsor_plans(array('sponsor_id' => $args['sponsor_id'], 'school_id' => $args['school_id']));

        $sponsor_plans = '(';

        foreach ($sponsors_access_plans as $access_plan) {
            if (isset($access_plan) && $access_plan !== "") {
                $sponsor_plans = $sponsor_plans . "'".$access_plan . "',";
            }
        }
        $sponsor_plans = rtrim($sponsor_plans, ",");
        $sponsor_plans = $sponsor_plans . ')';


        if ($args['course_id']) {
            $course_id_sql = " AND course_id = :course_id";
            $execution_params['course_id'] = $args['course_id'];
        }

        if ($args['sponsor_id']) {
            $sponsor_id_join = "INNER JOIN ols_user_access_plan ON ols_user_access_plan.rec_id = inst_responses.rec_id INNER JOIN ols_access_plans ON ols_access_plans.id = CAST(ols_user_access_plan.db22019 AS SIGNED) ";
            //$sponsor_id_sql = " AND ols_access_plans.db21880 = :sponsor_id";
            $sponsor_id_sql = " AND inst_r.access_plan_id in $sponsor_plans";
            $execution_params['sponsor_id'] = $args['sponsor_id'];
        }

        if ($args['school_id']) {
            $school_id_sql = " AND inst_r.usergroup = :school_id";
            $subquery_school_id_sql = " AND inst_table.usergroup = :school_id";
            $execution_params['school_id'] = $args['school_id'];
        } else {
            $school_id_sql = " AND inst_r.usergroup = :school_id";
            $subquery_school_id_sql = " AND inst_table.usergroup = :school_id";
            $execution_params['school_id'] = $_SESSION['usergroup'];
        }

        if ($args['group_id']) {
            $group_id_sql = "AND inst_r.rec_id IN (SELECT ols_user_chal_answers.rec_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = CAST(ols_challenge_answers.id AS CHAR) WHERE ols_challenge_answers.db27436 = :group )";
            $execution_params['group'] = $args['group_id'];
        }

       // error_log ("ANITA get_post_questionnaire_answers_only" );

        $db = new Db_helper();
        $survey_id = pull_field("inst_pages", "group_concat(page_id)", "where page_name = '' AND project = ''");
        //$modules = $this->get_only_modules_with_survey(463, $args['course_id']);
        $modules = $this->get_only_modules_with_survey($survey_id, $args['course_id']);

        $surveyed_modules = "(";

        foreach ($modules as $module) {
            if (isset($module) && $module !== "") {
                $surveyed_modules = $surveyed_modules . "'".$module . "',";
            }
        }
        $surveyed_modules = rtrim($surveyed_modules, ",");
        $surveyed_modules = $surveyed_modules . ")";

        $query = "SELECT 
					round(count(if(response = 'yes',1, NULL))/count(response)*100, 0) as affirmative_percentage,
					count(if(response = 'yes',1, NULL)) as affirmative_total,
					round(count(if(response = 'no',1, NULL))/count(response)*100, 0) as non_affirmative_percentage,
					count(if(response = 'no',1, NULL)) as non_affirmative_total,
					round(count(if(response = 'a little bit',1, NULL))/count(response)*100, 0) as somewhat_affirmative_percentage,
					count(if(response = 'a little bit',1, NULL)) as somewhat_affirmative_total,
					round(count(if(response = '',1, NULL))/count(response)*100, 0) as no_response_percentage,
					count(if(response = '',1, NULL)) as no_response_total,
					 (SELECT name FROM inst_table WHERE inst_table.db_field_name =inst_r.db_field_name $subquery_school_id_sql) as question,
					db_field_name, module_id, unit_id 
				FROM 
					 (select distinct rec_id,response, db_field_name, course_id, access_plan_id, module_id, unit_id, usergroup from inst_responses where usergroup = :school_id) as inst_r
					 LEFT JOIN ols_course_modules ON ols_course_modules.id = inst_r.module_id
				WHERE 
					1
					/*db_field_name IN ('db1770','db1771','db1773')*/
					AND module_id IN $surveyed_modules
					$sponsor_id_sql
					$course_id_sql
					$group_id_sql
					$school_id_sql
					and unit_id!='complete'
				GROUP BY 
					db_field_name,course_id, module_id, unit_id
				ORDER BY CAST(db21921 as UNSIGNED ) asc, ols_course_modules.id asc,unit_id ASC
					";

        dev_debug($query);
        //error_log ("ANITA get_post_questionnaire_answers_only QUERY $query" );
        $stmt = $dbh->prepare($query);
        $success = $stmt->execute($execution_params);
        $responses = $stmt->fetchAll(PDO::FETCH_ASSOC);

        /*
		//the following code collects data from the older tables for backward compatibility
		//The 3 hardcoded questions are:
		$would_you_recommend_this_course = "would you recommend this course";
		$did_you_find_this_course_helpful = "did you find this course helpful";
		$did_you_find_this_course_enjoyable = "did you find this course enjoyable";

		$execution_params = array();
		if($args['school_id']){
			$school_id_sql = " AND inst_table.usergroup = :school_id";
			$execution_params['school_id'] = $args['school_id'];
		}else{
			$school_id_sql = " AND inst_table.usergroup = :school_id";
			$execution_params['school_id'] = $_SESSION['usergroup'];
		}

		$query_for_questions_from_old_tables = "
				SELECT db_field_name, name, concat(sys_cat_abv, '_',inst_pages.page_name) as page_name, inst_table.pg_id
				FROM
					inst_table
						INNER JOIN inst_pages ON inst_pages.page_id = inst_table.pg_id
						INNER JOIN inst_cat ON inst_cat.sys_cat_id = inst_pages.project
				WHERE
					page_name LIKE '%course_".$course_id."_postq_questions%'
					AND sys_cat_abv='postq'
					AND (name LIKE '%$would_you_recommend_this_course%' OR name LIKE '%$did_you_find_this_course_helpful%' OR name LIKE '%$did_you_find_this_course_enjoyable%')
					$school_id_sql
		";

		$stmt = $dbh->prepare($query_for_questions_from_old_tables);
		$success = $stmt -> execute($execution_params);
		$backward_compatible_questions = $stmt -> fetchAll(PDO::FETCH_ASSOC);

		if($backward_compatible_questions){
			$backward_compatible_page = $backward_compatible_questions[0]['page_name'];

			if($backward_compatible_questions[0] && $backward_compatible_questions[0]['name'] && $backward_compatible_questions[0]['db_field_name'] && $backward_compatible_questions[0]['page_name']){
				$backward_compatible_page = $backward_compatible_questions[0]['page_name'];
				$sql_ready_backward_compatible_1st_ques = str_replace(' ','_',trim($backward_compatible_questions[0]['name'],"0123456789. ?"));
				$affirmative_perc_calc_for_1st_ques = "round(count(if($backward_compatible_questions[0]['db_field_name'] = 'yes',1, NULL))/count($backward_compatible_questions[0]['db_field_name'])*100, 2) as $sql_ready_backward_compatible_1st_ques";
			}

			if($backward_compatible_questions[1] && $backward_compatible_questions[1]['name'] && $backward_compatible_questions[1]['db_field_name'] && $backward_compatible_questions[1]['page_name']){
				$backward_compatible_page = $backward_compatible_questions[1]['page_name'];
				$sql_ready_backward_compatible_2nd_ques = str_replace(' ','_',trim($backward_compatible_questions[1]['name'],"0123456789. ?"));
				$affirmative_perc_calc_for_2nd_ques = "round(count(if($backward_compatible_questions[1]['db_field_name'] = 'yes',1, NULL))/count($backward_compatible_questions[1]['db_field_name'])*100, 2) as $sql_ready_backward_compatible_2nd_ques";
			}

			if($backward_compatible_questions[2] && $backward_compatible_questions[2]['name'] && $backward_compatible_questions[2]['db_field_name'] && $backward_compatible_questions[2]['page_name']){
				$backward_compatible_page = $backward_compatible_questions[2]['page_name'];
				$sql_ready_backward_compatible_3rd_ques = str_replace(' ','_',trim($backward_compatible_questions[2]['name'],"0123456789. ?"));
				$affirmative_perc_calc_for_3rd_ques = "round(count(if($backward_compatible_questions[2]['db_field_name'] = 'yes',1, NULL))/count($backward_compatible_questions[2]['db_field_name'])*100, 2) as $sql_ready_backward_compatible_3rd_ques";
			}

			//calculate only the affirmative responses to the 3 questions
			$execution_params = array();

			if($args['sponsor_id']){
				$sponsor_id_join = "INNER JOIN ols_user_access_plan ON ols_user_access_plan.rel_id = $backward_compatible_page.rel_id INNER JOIN ols_access_plans ON ols_access_plans.id = ols_user_access_plan.db22019 ";
				$sponsor_id_sql = " AND ols_access_plans.db21880 = :sponsor_id";
				$execution_params['sponsor_id'] = $args['sponsor_id'];
			}

			if($args['school_id']){
				$school_id_sql = " AND $backward_compatible_page.usergroup = :school_id";
				$execution_params['school_id'] = $args['school_id'];
			}else{
				$school_id_sql = " AND $backward_compatible_page.usergroup = :school_id";
				$execution_params['school_id'] = $_SESSION['usergroup'];
			}

			if($args['group_id']){
				$group_id_sql = "AND $backward_compatible_page.rel_id IN (SELECT ols_user_chal_answers.rel_id FROM ols_user_chal_answers INNER JOIN ols_challenge_answers ON ols_user_chal_answers.db26816 = ols_challenge_answers.id WHERE ols_challenge_answers.db27436 = :group )";
				$execution_params['group'] = $args['group_id'];
			}

			$all_backward_compatible_responses_query = "
					SELECT
						$affirmative_perc_calc_for_1st_ques
						$affirmative_perc_calc_for_2nd_ques
						$affirmative_perc_calc_for_3rd_ques
						max(ols_course_modules.id) as module_id
						'complete' as unit_id
					FROM
						$backward_compatible_page
						INNER JOIN
							ols_course_modules ON ols_course_modules.rel_id = $course_id
						$sponsor_id_join
					WHERE
						1
						$sponsor_id_sql
						$group_id_sql
						$school_id_sql
			";

			$stmt = $dbh->prepare($all_backward_compatible_responses_query);
			$success = $stmt -> execute($execution_params);
			$backward_compatible_responses = $stmt -> fetchAll(PDO::FETCH_ASSOC);

			//loop through reponses and add the percantages from the backward compatible responses
			foreach($responses as $response){
				$question = $response['question'];
				$module_id = $response['module_id'];
				$unit_id = $response['unit_id'];
				$backward_compatible_question_1st = trim($backward_compatible_questions[0]['name'],"0123456789. ");
				$backward_compatible_question_2nd = trim($backward_compatible_questions[1]['name'],"0123456789. ");
				$backward_compatible_question_3rd = trim($backward_compatible_questions[2]['name'],"0123456789. ");
				$backward_compatible_module_id = $backward_compatible_responses[0]['module_id'];

				if($question == $backward_compatible_question_1st || $question == $backward_compatible_question_2nd || $question == $backward_compatible_question_3rd){
					if($backward_compatible_responses[0][$sql_ready_backward_compatible_1st_ques] && $module_id == $backward_compatible_module_id && $unit_id=='complete'){
						$response['affirmative_percentage'] = ($response['affirmative_percentage'] +  $backward_compatible_responses[0][$sql_ready_backward_compatible_1st_ques])/2;
					}
					else if($backward_compatible_responses[0][$sql_ready_backward_compatible_2nd_ques] && $module_id == $backward_compatible_module_id && $unit_id=='complete'){
						$response['affirmative_percentage'] = ($response['affirmative_percentage'] +  $backward_compatible_responses[0][$sql_ready_backward_compatible_2nd_ques])/2;
					}
					else if($backward_compatible_responses[0][$sql_ready_backward_compatible_3rd_ques] && $module_id == $backward_compatible_module_id && $unit_id=='complete'){
						$response['affirmative_percentage'] = ($response['affirmative_percentage'] +  $backward_compatible_responses[0][$sql_ready_backward_compatible_3rd_ques])/2;
					}
				}
			}
		}*/

        return $responses;
    }

    /**
     * <AUTHOR>
     * the method below gets an array of the course id,module id, unit id with the survey attached to it
     * the survey we are interested in is the one with 3 questions below:
     * 1. Did you find this course helpful?
     * 2. Did you find this course enjoyable?
     * 3. Would you recommend this course to others?
     */

    function get_units_with_survey($survey_id, $course_id)
    {
        $dbh = get_dbh();
        $execution_params = array();
        $all_units = array();

        if ($survey_id) {
            $survey_id_sql = " AND db45507 IN ( $survey_id)";
            //$execution_params['survey_id'] = $survey_id;
        }

        if ($course_id) {
            $course_id_sql = " AND db45504 = :course_id";
            $execution_params['course_id'] = $course_id;
        }

        if ($args['school_id']) {
            $school_id_sql = " AND ols_quiz_allocation.usergroup = :school_id";
            $execution_params['school_id'] = $args['school_id'];
        } else {
            $school_id_sql = " AND ols_quiz_allocation.usergroup = :school_id";
            $execution_params['school_id'] = $_SESSION['usergroup'];
        }

        $query = "SELECT 
						GROUP_CONCAT(CONCAT(db45505 ,'_', db45506 )) as module_unit_pairs
					FROM 
						ols_quiz_allocation 
					WHERE 
						1
						$survey_id_sql
						$course_id_sql
						$school_id_sql
						";

        $stmt = $dbh->prepare($query);
        $success = $stmt->execute($execution_params);
        $all_units = $stmt->fetchAll(PDO::FETCH_COLUMN);
        return $all_units;
    }

    /**
     * <AUTHOR>
     * the method below gets the responses by a student to the survey questions
     */
    function survey_answers($args = array())
    {
        $dbh = get_dbh();
        $execution_params = array();

        dev_debug("all args  = " . json_encode($args));

        if ($args['unit_id']) {
            $unit_id_sql = " AND unit_id = :unit_id ";
            $execution_params['unit_id'] = $args['unit_id'];
        }

        if ($args['id']) {
            $id_sql = " AND id = :id ";
            $execution_params['id'] = $args['id'];
        }

        if ($args['rel_id']) {
            $rel_id_sql = " AND rel_id = :rel_id ";
            $execution_params['rel_id'] = $args['rel_id'];
        }

        if ($args['rec_id']) {
            $rec_id_sql = " AND rec_id = :rec_id ";
            $execution_params['rec_id'] = $args['rec_id'];
        }

        if ($args['school_id']) {
            $school_id_sql = " AND usergroup = :school_id ";
            $execution_params['school_id'] = $args['school_id'];
        } else {
            $school_id_sql = " AND usergroup = :school_id ";
            $execution_params['school_id'] = $_SESSION['usergroup'];
        }

        if ($args['attached_quiz']) {
            $fields_list_sql = "AND db_field_name in (" . pull_field("inst_table", "CONCAT('\"',GROUP_CONCAT(db_field_name SEPARATOR '\",\"'),'\"')", " WHERE pg_id ='$args[attached_quiz]'") . ")";
        } else {
            $fields_list_sql = "AND db_field_name in ('db1770','db1771','db1773')";
        }

        $query = "
					SELECT 
						*
					FROM
						inst_responses
					WHERE
					1
						$fields_list_sql
						$unit_id_sql
						$id_sql
						$rel_id_sql
						$rec_id_sql
						$school_id_sql
						GROUP BY course_id,unit_id,db_field_name
				";
        dev_debug("query  = " . json_encode($query));
        $stmt = $dbh->prepare($query);
        $success = $stmt->execute($execution_params);
        $answers = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return $answers;

    }

    /**
     * <AUTHOR>
     * the method below gets a summary of the details from all modules belonging to a course
     */

    function get_course_modules_summary_only($args)
    {
        $dbh = get_dbh();
        $execution_params = array();

        if ($args['course_id']) {
            $course_id_sql = " AND ols_course_modules.rel_id = :course_id ";
            $execution_params['course_id'] = $args['course_id'];
        }

        if ($args['school_id']) {
            $school_id_sql = " AND ols_course_modules.usergroup = :school_id";
            $execution_params['school_id'] = $args['school_id'];
        } else {
            $school_id_sql = " AND ols_course_modules.usergroup = :school_id";
            $execution_params['school_id'] = $_SESSION['usergroup'];
        }

        $query = "
			SELECT 
				id as module_id,
				@rownum := @rownum + 1 AS module_relative_position,
				db21919 as module_name
			FROM
				ols_course_modules,
				(SELECT @rownum := 0) as module_relative_position
			WHERE
				1
				$school_id_sql
				$course_id_sql
				AND (rec_archive is null or rec_archive='')
				ORDER BY CAST(db21921 as UNSIGNED ) asc, id asc
		";
        dev_debug("query  = " . json_encode($query));
        $stmt = $dbh->prepare($query);
        $success = $stmt->execute($execution_params);
        $all_course_modules = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return $all_course_modules;
    }

    /**
     * <AUTHOR>
     * the method counts the number of groups that belong to a sponsor
     */

    function get_groups($args)
    {
        $dbh = get_dbh();
        $execution_params = array();

        if ($args['count']) {
            $select_sql = " count(distinct id) as groups";
        }

        if ($args['school_id']) {
            $school_id_sql = " AND ols_general_groups.usergroup = :school_id";
            $execution_params['school_id'] = $args['school_id'];
        } else {
            $school_id_sql = " AND ols_general_groups.usergroup = :school_id";
            $execution_params['school_id'] = $_SESSION['usergroup'];
        }

        if ($args['sponsor_id']) {
            $sponsor_id_sql = " AND ols_general_groups.db30486 = :sponsor_id";
            $execution_params['sponsor_id'] = $args['sponsor_id'];
        }

        $query = "
			SELECT 
				$select_sql
			FROM
				ols_general_groups
			WHERE
				1
				$school_id_sql
				$sponsor_id_sql
				AND (ols_general_groups.rec_archive is null or ols_general_groups.rec_archive='')
		";
        dev_debug("query  = " . json_encode($query));
        $stmt = $dbh->prepare($query);
        $success = $stmt->execute($execution_params);
        $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return $groups;
    }

    function generate_temp_emails_bulk($args)
    {
        $db = new Db_helper();
        //echo "<pre>".print_r($args,1)."</pre>";exit();
        $dbh = get_dbh();

        if (in_array($args["pg_id"], [323])) {
            #access plan page rows will be student ids
            $query = "SELECT id,rel_id,(SELECT  CONCAT(db39,' ',db40) FROM core_students WHERE id = ols_user_access_plan .rel_id AND usergroup=ols_user_access_plan.usergroup) as 'name',
				'applicant' as type,
				(SELECT  db764 FROM core_students WHERE id = ols_user_access_plan.rel_id AND usergroup=ols_user_access_plan.usergroup) as 'email'
				FROM ols_user_access_plan WHERE db22019 = " . $args["ref_id"] . " AND id IN (" . implode(",", $args["rows"]) . ") and usergroup='" . $_SESSION['usergroup'] . "'";
            $features = pull_field('form_schools', 'db1261', "WHERE id = {$_SESSION['usergroup']}");
            $result = $db->query($query);
        }


        $content = "<h4 style = 'font-size: 18px;margin-top: 10px;margin-bottom: 10px;'>Below is a preview of some of the emails to be sent out</h4>
		<h5 style = 'margin-top: 10px;margin-bottom: 10px;font-size: 14px;'>Showing " . $args["selected"] . " Emails of  " . $args["selected"] . "</h5>
		";
        $email_rows[] = $content;
        $style = "style=\"background-color: #4CAF50; border: none; color: white; padding: 8px 40px; text-align: center; text-decoration: none; 
			 	display: inline-block; font-size: 16px; margin: 4px 2px; cursor: pointer;\"";

        $count = 1;
        foreach ($result as $row) {
            $message = $args["message"];

            if ($row["type"] == "applicant") {
                $profile_info = pull_field("core_students", "CONCAT(rec_id,'|',db39,'|',db40,'|',db764)", "WHERE id='" . $row["rel_id"] . "'");
                $profile_info = explode("|", $profile_info);
                $profile_id = $profile_info[0];
                $name = $profile_info[1];
                $surname = $profile_info[2];
                $email = $profile_info[3];
            }


            //REPLACE TAGS HERE


            $message = str_replace("{{first_name}}", $name, $message);
            $message = str_replace("{{name}}", $name, $message);
            $message = str_replace("{{surname}}", $surname, $message);
            $div = '<div class="email">
	        <h5 class="right margin-0">Email number ' . $count . '</h5>
	        <h5>To: ' . $email . '</h5>
	        <h5>Subject: ' . $args["subject"] . '</h5>
	        <div>Message<br>
	        ' . nl2br($message) . '
	        </div>
	        </div>
	        ';
            $count++;
            $email_rows[] = $div;
        }

        return $email_rows;
    }

    function bulk_email($args)
    {
        $db = new Db_helper();
        $dbh = get_dbh();
        $emails = new Emails;
        if (in_array($args["pg_id"], [323])) {
            #access plan page rows will be student ids
            $query = "SELECT id,rel_id,(SELECT  CONCAT(db39,' ',db40) FROM core_students WHERE id = ols_user_access_plan .rel_id AND usergroup=ols_user_access_plan.usergroup) as 'name',
				'applicant' as type,
				(SELECT  db764 FROM core_students WHERE id = ols_user_access_plan.rel_id AND usergroup=ols_user_access_plan.usergroup) as 'email'
				FROM ols_user_access_plan WHERE db22019 = " . $args["ref_id"] . " AND id IN (" . implode(",", $args["rows"]) . ") and usergroup='" . $_SESSION['usergroup'] . "'";
            $features = pull_field('form_schools', 'db1261', "WHERE id = {$_SESSION['usergroup']}");
            $result = $db->query($query);
        }

        $count = 0;
        foreach ($result as $row) {
            $message = $args["message"];

            if ($row["type"] == "applicant") {
                $profile_info = pull_field("core_students", "CONCAT(rec_id,'|',db39,'|',db40,'|',db764)", "WHERE id='" . $row["rel_id"] . "'");
                $profile_info = explode("|", $profile_info);
                $profile_id = $profile_info[0];
                $name = $profile_info[1];
                $surname = $profile_info[2];
                $email = $profile_info[3];
            }

            //REPLACE TAGS HERE


            $message = str_replace("{{first_name}}", $name, $message);
            $message = str_replace("{{name}}", $name, $message);
            $message = str_replace("{{surname}}", $surname, $message);
            $email_args = array(
                'to' => $email,
                'subject' => $args["subject"],
                'text' => $message,
                'html' => nl2br($message)
            );

            $email_args["queue"] = true;
            //chech disabled mails
            $surveyoptin = pull_field("dir_core_students_meta", "db49864", "WHERE rel_id = '" . $row["rel_id"] . "'");

            if ($surveyoptin != "no") {
                $emails->send($email_args);
            }

            $count++;

        }

        $s = ($count == 1) ? 'email' : 'emails';
        $t = ($count == 1) ? 'It' : 'They';
        $url = "<p><br><br><br><a href = '" . base_url("system/email_logs/scheduled") . "' class = 'btn btn-primary thickbox' target = '_blank'>Click here to see the queued emails</a><br><br><br>Emails will be sent out automatically in 15 minutes</p>";
        $success = "<h4>Congratulations!</h4><p>You have {$count} {$s} in the send queue. {$t} will be sent out shortly.</p>" . $url;
        $error = "<h4>Error!</h4><p>No messages were sent.</p>";
        return ($count > 0) ? $success : $error;
    }


    /**
     * <AUTHOR>
     * the method below gets an array of the module ids with the survey attached to them
     * the survey we are interested in is the one with 3 questions below:
     * 1. Did you find this course helpful?
     * 2. Did you find this course enjoyable?
     * 3. Would you recommend this course to others?
     */

    function get_only_modules_with_survey($survey_id, $course_id)
    {
        $dbh = get_dbh();
        $execution_params = array();

        if ($survey_id) {
            $survey_id_sql = " AND db45507 IN ($survey_id)";
            //$execution_params['survey_id'] = $survey_id;
        }

        if ($course_id) {
            $course_id_sql = " AND db45504 = :course_id";
            $execution_params['course_id'] = $course_id;
        }

        if ($args['school_id']) {
            $school_id_sql = " AND ols_quiz_allocation.usergroup = :school_id";
            $execution_params['school_id'] = $args['school_id'];
        } else {
            $school_id_sql = " AND ols_quiz_allocation.usergroup = :school_id";
            $execution_params['school_id'] = $_SESSION['usergroup'];
        }

        $query = "SELECT db45505 FROM ols_quiz_allocation WHERE 
						1
						$survey_id_sql
						$course_id_sql
						$school_id_sql
						";

        $stmt = $dbh->prepare($query);
        $success = $stmt->execute($execution_params);
        $modules = $stmt->fetchAll(PDO::FETCH_COLUMN, 0);

        return $modules;
    }

    public function process_questionnaires($text)
    {
        $html = $text;
        $hasForm = false;
        if (strpos($text, 'post_questionnaire') !== false) {
            $handler_type = "post_questionnaire";
            $form_id_type = "post_questions_form_id";
            $hasForm = true;
            $unitData = $this->handle_questionnaire_shortcodes($text, $handler_type, $form_id_type);
        }
        if (strpos($text, 'pre_questionnaire') !== false) {
            $handler_type = "pre_questionnaire";
            $form_id_type = "pre_questions_form_id";
            $hasForm = true; 
            dev_debug(" process_questionnaires 1  **$form_id_type** handler_type **$handler_type**");
            $unitData = $this->handle_questionnaire_shortcodes($text, $handler_type, $form_id_type);
        }

        $response = ["has_form" => $hasForm];
        if (!$hasForm) {
            $response["html"] = $this->proccess_short_codes($html);
        } else {
            $response["html"] = $this->proccess_short_codes($unitData->text);
            $response["form_data"] = $unitData->form_data;
            //$response["form_responses_html"] = $unitData->text;
        }
        return (object)$response;
    }

    private function handle_questionnaire_shortcodes($text, $handler_type, $form_id_type)
    {
        $handlers = new HandlerContainer();
        $su_course_id = null;
        if (!empty($handler_type)) {
            $handlers->add($handler_type, function (ShortcodeInterface $s) use (&$su_course_id,$handler_type) {
                $content = $s->getContent();
                $atts = $s->getParameters();
                $atts['course'] = str_replace('"', "", html_entity_decode($atts['course']));
                $su_course_id = $atts['course'];
                if ('post_questionnaire'==$handler_type) {
                   $form_id_type = "post_questions_form_id";
                }

                if ('pre_questionnaire'==$handler_type) {
                   $form_id_type = "pre_questions_form_id";
                }
                //$OL=$this;
                //$html = handle_questionnaire($atts,$html,$form_id_type);
                include($_SERVER['DOCUMENT_ROOT'] . '/online-learning/views/su_questionnaire.php');
                return $html;
            });
        }
        /*      its important for the html to be processed here so
                that we get the @param $su_course_id */
        $processor = new Processor(new RegularParser(), $handlers);
        $html = $processor->process($text); 
        $args = [
            "form_id_type" => $form_id_type,
            "su_course_id" => $su_course_id,
        ];
        $form_data = $this->get_questionnaire_fields($args);
        return (object)["text" => $html, "success" => true, "form_data" => $form_data];
    }

    function get_questionnaire_fields($args)
    {
        $fieldModel = new \Fields();
        $questionnaire_statuses = (object)["filled" => "filled", "new" => "new"];
        $course_args = array('id' => $args['su_course_id']);
        $course = $this->get_course_info_for_questionnaires($course_args);
        $form_templates = new FormTemplates;
        $page_args = array("id" => $course[$args['form_id_type']], "system_abbrv" => "inst", "school_id" => $_SESSION['usergroup']);
        $form = $form_templates->get($page_args);
        $table_exists = pull_field($form['table_name'], "1");
        if ($table_exists) {
            $answers = $form_templates->answers(array('form_name' => $form['table_name'], 'rel_id' => $_SESSION['student_id'], 'single_answer' => true));
            if ($answers['id']) {
                $latest_max = 0;
                if (count($form['fields'])) {
                    foreach ($form['fields'] as $fld) {
                        $latest_max = $latest_max + max($fld['weighting']);
                    }
                }
                $responses = $this->get_responses_list($form, $answers);
                //Total score
                $latest_total_score = 0;
                if (count($responses)) {
                    foreach ($responses as $ans) {

                        $latest_total_score = $latest_total_score + $ans['weight'];
                    }
                }
            }
        } else {
            $answers = [];
        }
        $field_order_count = 0;
        $fields = [];
        foreach ($form['fields'] as $question) {
            $question["field_order_count"] = $field_order_count;
            $question['json'] = true;
            array_push($fields, $fieldModel->json($question));
        }
        if (false && $answers['id'] && $args['form_id_type'] == "pre_questions_form_id") {
            $success = true;
            $form_available = false;
            $form_data = null;
            $filled = $questionnaire_statuses->filled;
            $message = text_translate('You completed this pre questionnaire on  ', $course['language']['id']) . ' ' . date('d/m/Y', strtotime($answers['date']));
        } else if (false && $answers['id'] && $args['form_id_type'] == "post_questions_form_id") {
            $success = true;

            $form_available = false;
            $form_data = null;
            $filled = $questionnaire_statuses->filled;
            $message = text_translate(' Thank you for submitting this post questionnaire on  ', $course['language']['id']) . ' ' . date('d/m/Y', strtotime($answers['date']));
            $questionnaire_type = $course['questionnaire_type'];
            if (isset($questionnaire_type) && $questionnaire_type != '') {
                if ($questionnaire_type == "1" || $questionnaire_type == "4" || $questionnaire_type == "5") {
                    //pre and post score questionnaire
                    //1. add all the pre scores
                    //2. Add all the post scores
                    //3. Post - Pre = score

                    //1. Pre answers
                    $page_args = array("id" => $course['pre_questions_form_id'], "system_abbrv" => "inst", "school_id" => $_SESSION['usergroup']);
                    $pre_form = $form_templates->get($page_args);
                    $pre_answers = $form_templates->answers(array('form_name' => $pre_form['table_name'], 'rel_id' => $_SESSION['student_id'], 'single_answer' => true));
                    $pre_total_score = 0;
                    if ($pre_answers['id']) {
                        $responses = $this->get_responses_list($pre_form, $pre_answers);

                        //Total score
                        if (count($responses)) {
                            foreach ($responses as $ans) {
                                $pre_total_score += $ans['weight'];
                            }
                        }
                    }

                    //2. Post answers
                    $page_args = array("id" => $course['post_questions_form_id'], "system_abbrv" => "inst", "school_id" => $_SESSION['usergroup']);
                    $post_form = $form_templates->get($page_args);
                    $post_answers = $form_templates->answers(array('form_name' => $post_form['table_name'], 'rel_id' => $_SESSION['student_id'], 'single_answer' => true));
                    $post_total_score = 0;
                    if ($post_answers['id']) {
                        $responses = $this->get_responses_list($post_form, $post_answers);
                        //Total score
                        if (count($responses)) {
                            foreach ($responses as $ans) {
                                $post_total_score += $ans['weight'];
                            }
                        }
                    }
                    //3. Post - Pre = score
                    $total_score = $post_total_score - $pre_total_score;
                    if ($questionnaire_type == "1") {
                        //4. Show the result
                        if ($total_score > 5) {
                            $score_text = $course['score_1_text'];
                        } else if ($total_score >= 0 && $total_score <= 5) {
                            $score_text = $course['score_2_text'];
                        } else if ($total_score < 0 && $total_score > -5) {
                            $score_text = $course['score_3_text'];;
                        } else {
                            $score_text = $course['score_4_text'];;
                        }
                        $chart = text_translate('Confidence', $course['language']['id']);
                    } elseif ($questionnaire_type == "4") {
                        //4. Show the result
                        if ($total_score < 0) {
                            $score_text = $course['score_1_text'];
                        } else if ($total_score == 0) {
                            $score_text = $course['score_2_text'];
                        } else if ($total_score > 0) {
                            $score_text = $course['score_3_text'];;
                        }
                        $chart = text_translate('Score', $course['language']['id']);
                    } else {
                        //4. Show the result
                        if ($total_score > 6) {
                            $score_text = $course['score_1_text'];
                        } else if ($total_score >= 1 && $total_score <= 6) {
                            $score_text = $course['score_2_text'];
                        } else if ($total_score == 0 && $total_score == -1) {
                            $score_text = $course['score_3_text'];;
                        } else if ($total_score <= -1 && $total_score >= -6) {
                            $score_text = $course['score_4_text'];;
                        } else {
                            $score_text = $course['score_5_text'];;
                        }
                        $chart = text_translate('Score', $course['language']['id']);
                    }

                    $form_data['score_data'] = [
                        "pre" => text_translate('Pre', $course['language']['id']),
                        "post" => text_translate('Post', $course['language']['id']),
                        "diff" => text_translate('Diff', $course['language']['id']),
                        "chart" => $chart,
                        "questionnaire_type" => $questionnaire_type,
                        "pre_total_score" => $pre_total_score,
                        "post_total_score" => $post_total_score,
                        "total_score" => $total_score,
                        "score_text" => $score_text
                    ];
                } else {
                    //questionnaire is conflict/closeness questionnaire

                    //pre and post score questionnaire
                    //1. Add all the pre conflict and pre closeness scores
                    //2. Add all the post conflict and post closeness scores
                    //3  conflict score = post conflict - pre conflict
                    //4 closeness score post closeness - pre closeness


                    //1. Pre answers
                    $closeness_questions = explode(',', $course['closeness_questions']);
                    $conflict_questions = explode(',', $course['conflict_questions']);
                    $page_args = array("id" => $course['pre_questions_form_id'], "system_abbrv" => "inst", "school_id" => $_SESSION['usergroup']);
                    $pre_form = $form_templates->get($page_args);
                    $pre_answers = $form_templates->answers(array('form_name' => $pre_form['table_name'], 'rel_id' => $_SESSION['student_id'], 'single_answer' => true));
                    $pre_closeness_score = 0;
                    $pre_conflict_score = 0;
                    if ($pre_answers['id']) {
                        $responses = $this->get_responses_list($pre_form, $pre_answers);
                        //Total score
                        if (count($responses)) {

                            foreach ($responses as $ans) {

                                if (in_array($ans['question'], $closeness_questions)) {
                                    $pre_closeness_score += $ans['weight'];
                                }
                                if (in_array($ans['question'], $conflict_questions)) {
                                    $pre_conflict_score += $ans['weight'];
                                }
                            }
                        }
                    }

                    //2. Post answers
                    $page_args = array("id" => $course['post_questions_form_id'], "system_abbrv" => "inst", "school_id" => $_SESSION['usergroup']);
                    $post_form = $form_templates->get($page_args);
                    $post_answers = $form_templates->answers(array('form_name' => $post_form['table_name'], 'rel_id' => $_SESSION['student_id'], 'single_answer' => true));
                    $post_closeness_score = 0;
                    $post_conflict_score = 0;

                    if ($post_answers['id']) {
                        $responses = $this->get_responses_list($post_form, $post_answers);
                        //Total score
                        if (count($responses)) {
                            foreach ($responses as $ans) {
                                if (in_array($ans['question'], $closeness_questions)) {
                                    $post_closeness_score += $ans['weight'];
                                }
                                if (in_array($ans['question'], $conflict_questions)) {
                                    $post_conflict_score += $ans['weight'];
                                }
                            }
                        }
                    }
                    //3. Post - Pre = score
                    $total_conflict_score = $post_conflict_score - $pre_conflict_score;
                    $total_closeness_score = $post_closeness_score - $pre_closeness_score;

                    //4. Show the result
                    if ($total_conflict_score < 0) {
                        $conflict_score_text = $course['score_1_text'];
                    } else if ($total_conflict_score == 0) {
                        $conflict_score_text = $course['score_2_text'];
                    } else if ($total_conflict_score == 1) {
                        $conflict_score_text = $course['score_3_text'];;
                    } else {
                        $conflict_score_text = $course['score_4_text'];;
                    }
                    //4. Show the result
                    if ($total_closeness_score > 0) {
                        $closeness_score_text = $course['score_5_text'];
                    } else if ($total_closeness_score == 0) {
                        $closeness_score_text = $course['score_6_text'];
                    } else if ($total_closeness_score == -1) {
                        $closeness_score_text = $course['score_7_text'];;
                    } else {
                        $closeness_score_text = $course['score_8_text'];;
                    }
                    $form_data['score_data'] = [
                        "pre" => text_translate('Pre', $course['language']['id']),
                        "post" => text_translate('Post', $course['language']['id']),
                        "diff" => text_translate('Diff', $course['language']['id']),
                        "questionnaire_type" => $questionnaire_type,
                        "pre_closeness_score" => $pre_closeness_score,
                        "post_closeness_score" => $post_closeness_score,
                        "total_closeness_score" => $total_closeness_score,
                        "pre_conflict_score" => $pre_conflict_score,
                        "post_conflict_score" => $post_conflict_score,
                        "total_conflict_score" => $total_conflict_score,
                        "closeness_score_text" => $closeness_score_text,
                        "conflict_score_text" => $conflict_score_text,
                    ];
                }
            }
        } else {
            $success = true;
            $form_available = true;
            $form_data = $form;
            $has_fields = (count($form['fields']) > 0);
            $filled = $questionnaire_statuses->new;
            $message = "Please fill in questionnaire";
            $form_data['score_data'] = [
                "pre" => text_translate('Pre', $course['language']['id']),
                "post" => text_translate('Post', $course['language']['id']),
                "diff" => text_translate('Diff', $course['language']['id']),
                "has_fields" => $has_fields
            ];
        }
        return [
            "success" => false,
            "form" => $form_data,
            "form_available" => $form_available,
            "status" => $filled,
            "type" => $args['form_id_type'],
            "message" => $message
        ];
    }

    function verifyChallengeQuestions($plan = false, $student_id = false)
    {
        $db = new Db_helper();
        $OL = new OnlineLearning();
        if (!$plan) {
            $user_access_plan_challenge_questions_status = false;
            if ($_SESSION['uid']) {
                $plan = $OL->get_plans(array('username_id' => noHTML($_REQUEST['plan']), 'student_id' => $_SESSION['student_id']));
            } else {
                $plan = $OL->get_plans(array('username_id' => noHTML($_REQUEST['plan'])));
            }
        }
        foreach ($plan['challenge_questions'] as $question) {
            $question_type = $question['type'];
            $challenge_answer = noHTML($_REQUEST['code']) ?: noHTML($_REQUEST["challenge_{$question["id"]}"]);
            $valid = false;
            //strip out and spaces (Postcode)
            $challenge_answer = str_replace(' ', '', $challenge_answer);
            if ($question_type == 'like') {
                list($maybe_answer, $maybe_answer_group) = explode('??', pull_field("ols_challenge_answers", "CONCAT(id,'??',db27436)", "WHERE rel_id = '$plan[id]' AND (rec_archive IS NULL OR rec_archive = '') AND db21902 = '$question[id]' AND '$challenge_answer' LIKE CONCAT('%',db62992,'%')"));

                if ($maybe_answer and $maybe_answer != '') {
                    $valid = true;
                    $db26816_valid_answer_id = $maybe_answer;
                    $db56592_valid_answer_group = $maybe_answer_group;
                }
            } else {
                //check if this is an event access plan type... if it is validate differently
                $access_plan_type = pull_field("ols_access_plans", "db26817", "WHERE id = $plan[id] ");
                if ($access_plan_type != 3) {
                    list($maybe_answer, $maybe_answer_group) = explode('??', pull_field("ols_challenge_answers", "CONCAT(id,'??',db27436)", "WHERE rel_id = '$plan[id]' AND (rec_archive IS NULL OR rec_archive = '') AND db21902 = '$question[id]' AND db62992 = '$challenge_answer'"));
                } else {
                    //$comparison_challenge_answer = preg_replace("/[^A-Za-z]/", "", getUKPostcodeFirstPart($challenge_answer));
                    $comparison_challenge_answer = preg_replace("/(.*?)\d(.*)/", "$1", getUKPostcodeFirstPart($challenge_answer));
                    list($maybe_answer, $maybe_answer_group) = explode('??', pull_field("ols_challenge_answers", "CONCAT(id,'??',db27436)", "WHERE rel_id = '$plan[id]' AND (rec_archive IS NULL OR rec_archive = '') AND db21902 = '$question[id]' AND db62992 = '$comparison_challenge_answer'"));
                }

                if ($maybe_answer and $maybe_answer != '') {
                    $valid = true;
                    $db26816_valid_answer_id = $maybe_answer;
                    $db56592_valid_answer_group = $maybe_answer_group;
                }
            }

            $user_access_plan_challenge_questions_status = ($user_access_plan_challenge_questions_status && $valid);

            if ($valid == true) {
                $result = ["success" => true, "message" => "Post Code is valid.", "answer" => $maybe_answer];
            } else {
                $result = ["success" => false, "message" => "Post Code is not valid . " . getUKPostcodeFirstPart($challenge_answer), "answer" => $comparison_challenge_answer];
            }
            $challenge_info = array('db22032' => $valid ? "Valid" : "Invalid", 'db22028' => $plan['id'], 'db22029' => $question['id'], 'db22030' => $challenge_answer, 'db26816' => $db26816_valid_answer_id, 'db56592' => $db56592_valid_answer_group);
            $challenge_info['rel_id'] = $student_id;
            $db->system_table_insert_or_update('ols_user_chal_answers', $challenge_info);
            return (object)$result;
        }

        if (count($plan['challenge_questions']) < 1) {
            $result = ["success" => true, "message" => "No Challenge Questions Found", "answer" => ""];
        } else {
            $result = ["success" => false, "message" => "Failed to verify challenge question.", "answer" => ""];
        }
        return (object)$result;
    }


    function get_post_ques_responses_by_course($args)
    {
        $dbh = get_dbh();
        $execution_params = array();
        $responses_by_course = array();
        $execution_params['usergroup'] = $_SESSION['usergroup'];

        if ($args['access_plan']) {
            $access_plan_id_sql = "AND inst_r.access_plan_id= :access_plan_id";
            $execution_params['access_plan_id'] = $args['access_plan'];
        }

        $query = "SELECT
				inst_r.course_id, inst_r.access_plan_id,inst_r.module_id,
				count(distinct inst_r.rec_id) as total_last_module_questionnaire_responses,
				count(if(inst_r.response = 'yes' and inst_r.db_field_name='db1771' ,1, NULL)) as db1771_would_reccommend_total,
				count(if(inst_r.response = 'yes' and inst_r.db_field_name='db1770' ,1, NULL)) as db1770_found_helpful_total
				FROM (select distinct rec_id,response, db_field_name, course_id, access_plan_id, module_id, unit_id, usergroup from inst_responses where usergroup = :usergroup) as inst_r
				LEFT JOIN ols_course_modules ON ols_course_modules.id = inst_r.module_id 
				WHERE inst_r.usergroup = :usergroup
				AND inst_r.db_field_name IN ('db1770','db1771')
				$access_plan_id_sql
				group by inst_r.module_id, inst_r.course_id, inst_r.access_plan_id
				order by inst_r.course_id, CAST(db21921 as UNSIGNED ) asc, ols_course_modules.id asc
				";

        dev_debug($query);
        $sth = $dbh->prepare($query);
        $sth->execute($execution_params);
        $post_questionnaire_responses = $sth->fetchAll();

        foreach ($post_questionnaire_responses as $module_response) {
            $course = $module_response['course_id'];
            //only append a course to the array responses_by_course just once
            if (!array_key_exists($course, $responses_by_course)) {
                $responses_by_course[$course] = array(
                    'course_id' => $module_response['course_id'],
                    'access_plan_id' => $module_response['access_plan_id'],
                    'module_id' => $module_response['module_id'],
                    'total_last_module_questionnaire_responses' => $module_response['total_last_module_questionnaire_responses'],
                    'would_reccommend_total' => $module_response['db1771_would_reccommend_total'],
                    'found_helpful_total' => $module_response['db1770_found_helpful_total']
                );
            }
        }

        return $responses_by_course;
    }


    function update_course_topics($args)
    {
        $db = new Db_helper();
        if (array_key_exists('topics', $args)) {
            if ($args['programme_id']) {

                //Clear current topics
                $where = array('rel_id' => $args['programme_id']);
                $db->delete('core_course_topics_rel', $where);
            }
            //Add the new topics
            foreach ($args['topics'] as $topic) {
                if (is_string($topic)) {
                    $topic_args = array(
                        'db32201' => $topic
                    );
                    $topic = $db->system_table_insert_or_update('core_course_topics', $topic_args);
                } else if (array_key_exists('id', $topic)) {
                    $topic = $topic['id'];
                }
                $topic_args = array(
                    'rel_id' => $args['programme_id'],
                    'db32203' => $topic
                );


                $result = $db->system_table_insert_or_update('core_course_topics_rel', $topic_args);
            }
            return $result;
        }
        return false;
    }

    function verify_postcode($args)
    {

        $dbh = get_dbh();
        $comparison_challenge_answer = preg_replace("/(.*?)\d(.*)/", "$1", getUKPostcodeFirstPart($args['postcode']));
        if ($args['course_id']) {
            $query = "SELECT ols_challenge_answers.rel_id FROM ols_challenge_answers 
LEFT JOIN ols_access_plans ON ols_access_plans.id = ols_challenge_answers.rel_id 
LEFT JOIN ols_access_plan_courses ON ols_access_plan_courses.rel_id = CAST(ols_challenge_answers.rel_id AS CHAR) 
WHERE db21877 = '2'/*access plan status is active*/ 
AND db62992 = REPLACE('{$args['postcode']}', ' ', '') /*typed_in_postcode*/ 
AND db21911 = '{$args['course_id']}' /*id of course we are searching for */
AND db26817 = '4' /* Learner courses only */
AND (ols_challenge_answers.rec_archive IS NULL OR ols_challenge_answers.rec_archive = '') 
AND (ols_access_plans.rec_archive IS NULL OR ols_access_plans.rec_archive = '') 
AND (ols_access_plan_courses.rec_archive IS NULL OR ols_access_plan_courses.rec_archive = '')";
        } else {
            $query = "SELECT ols_challenge_answers.rel_id FROM ols_challenge_answers 
LEFT JOIN ols_access_plans ON ols_access_plans.id = ols_challenge_answers.rel_id 
#LEFT JOIN ols_access_plan_courses ON ols_access_plan_courses.rel_id = CAST(ols_challenge_answers.rel_id AS CHAR) 
WHERE db21877 = '2'/*access plan status is active*/ 
AND db62992 = REPLACE('{$args['postcode']}', ' ', '') /*typed_in_postcode*/ 
AND db26817 = '4' /* Learner courses only */
AND (ols_challenge_answers.rec_archive IS NULL OR ols_challenge_answers.rec_archive = '') 
AND (ols_access_plans.rec_archive IS NULL OR ols_access_plans.rec_archive = '') 
#AND (ols_access_plan_courses.rec_archive IS NULL OR ols_access_plan_courses.rec_archive = '')";
        }
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(2);

        $access_code_ids = array_map(function ($result) {
            return $result['rel_id'];
        }, $results);
        if (empty($access_code_ids)) {
            return [];
        }
        $plans = $this->get_plans(["ids" => implode(",", $access_code_ids)]);
        return $plans;
    }

    public function english_language_courses($array = array())
    {
        $query = get_dbh()->prepare("SELECT id, db21909 as title FROM ols_online_courses WHERE usergroup=:school_id AND db31066=:language AND (rec_archive IS NULL OR rec_archive ='')");
        $query->execute(['school_id' => $array['usergroup'] ?? $_SESSION['usergroup'], 'language' => '1']);
        return $query->fetchAll(PDO::FETCH_ASSOC);
    }

    /** ===================================
     * Update Plan
     * ====================================    */
    function transition_student_to_updated_version_of_course($args)
    {
        $db = new Db_helper();

        $transition_info = array();

        if (array_key_exists('course_from', $args)) {
            $transition_info['db260402'] = $args['course_from'];
        }
        if (array_key_exists('course_to', $args)) {
            $transition_info['db260405'] = $args['course_to'];
        }

        if (!empty($args['student_id'])) {
            $transition_info['rel_id'] = $args['student_id'];
        }

        $db->system_table_insert_or_update('ols_user_course_transitions', $transition_info);
        return $db->lastInsertId();

    }

    /**
     * @param $args
     * @return array|false|mixed[]
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function get_personalized_courses($args = [])
    {
        $topics_matched_ids = $this->find_topics($args['interest']);
        if (count(explode(',', $topics_matched_ids))) {
            $course_finder_sql = "SELECT 
        DISTINCT core_courses.id,
        core_courses.username_id,
        core_courses.db232 as title
    FROM core_courses
            LEFT JOIN core_course_topics_rel ON core_courses.id=core_course_topics_rel.rel_id 
            WHERE core_courses.usergroup=:usergroup
            AND core_course_topics_rel.db32203 IN ({$topics_matched_ids})";
            dev_debug("FINDER: " . $course_finder_sql);
            $course_sth = get_dbh()->prepare($course_finder_sql);
            $course_sth->execute(['usergroup' => $_SESSION['usergroup']]);
            return $course_sth->fetchAll(2);
        }

        return [];
    }


    /**
     * @param $interest
     * @return array|false|mixed[]
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function find_topics($interest = '')
    {
        if (!empty($interest)) {
            $words = explode(" ", str_replace("_", " ", $interest));
            $searchQuery = implode(" OR ", array_map(function ($term) {
                return "db32201 LIKE '%" . $term . "%'";
            }, $words));
            $SQL = "SELECT GROUP_CONCAT(id) FROM core_course_topics WHERE usergroup=:usergroup AND ({$searchQuery})";
            dev_debug("TOPICS: " . $SQL);
            $sth = get_dbh()->prepare($SQL);
            $sth->execute(['usergroup' => $_SESSION['usergroup']]);
            return $sth->fetchColumn();
        }
        return [];
    }

    public function save_personalization($personalization)
    {
        $db = new Db_helper();
        $preferences = $this->user_preferences() ?? [];
        $preferences['db271913'] = json_encode($personalization);
        return $db->system_table_insert_or_update('ols_user_preferences', $preferences);
    }

    public function get_personalization()
    {
        $raw_data = $this->user_preferences();
        return !empty($raw_data) ? json_decode($raw_data['db271913'], true) : [];
    }

    private function user_preferences()
    {
        $SQL = "SELECT * FROM ols_user_preferences WHERE usergroup=:usergroup AND rec_id=:user";
        dev_debug("User Preferences: " . $SQL);
        $sth = get_dbh()->prepare($SQL);
        $sth->execute(['usergroup' => $_SESSION['usergroup'], 'user' => $_SESSION['uid']]);
        return $sth->fetch(PDO::FETCH_ASSOC);
    }

    public function get_related_courses($parent_course_id,$course_id,$current_course_lang=0)
    {
        $db = new Db_helper();
        $results_list = [];
        $dbh = get_dbh();
        $current_course_lang_sql="";
        if (!empty($current_course_lang)) {
            $current_course_lang_sql=" AND db31066<>'".$current_course_lang."'";
        }

        // SQL query to find related courses based on db253412 and join with language information
        $query = "
            SELECT 
                ols_online_courses.id AS course_id,
                ols_online_courses.db21909 as title,
                form_languages.id AS language_id,
                form_languages.db21280 AS language_name,
                form_languages.db21281 AS language_code,
                form_languages.db31065 AS direction,
                language_meta.meta_value AS language_icon
            FROM 
                ols_online_courses 
            LEFT JOIN 
                form_languages ON ols_online_courses.db31066 = form_languages.id
            LEFT JOIN 
                (SELECT rel_id as language_id, db236756 as meta_value 
                 FROM form_languages_meta 
                 WHERE db236753 = 'icon') AS language_meta
                 ON form_languages.id = language_meta.language_id
            WHERE 
                ols_online_courses.db253412 = '$parent_course_id' 
                AND ols_online_courses.id <> '$course_id' 
                {$current_course_lang_sql}
                AND (ols_online_courses.rec_archive IS NULL OR ols_online_courses.rec_archive = '') 
            ORDER BY 
                ols_online_courses.db251258 ASC
        ";

        // Execute the query with prepared statement
        //$_GET['debug_mode']="yes";
        dev_debug($query);
        $stmt = $dbh->prepare($query);
        $stmt->execute();

        // Process results and format them as required
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            // Construct the language icon URL if available
            $language_icon_url = null;
            if ($row['language_icon']) {
                $encoded_icon_path = encode($row['language_icon'], "unsalted");
                $language_icon_url = $this->point_to_prod(engine_url("/media/dl.php?a=yes&fl=" . $encoded_icon_path));
            }

            // Append each related course with language details to the results list
            $results_list[] = [
                'course_id' => $row['course_id'],
                'title' => $row['title'],
                 'href'=>$this->home_url("course/") .$this->ensure_slug_matches_title($row['course_id'], 'course') ,
                'language_id' => $row['language_id'],
                'language_name' => $row['language_name'],
                'language_icon' => str_replace("heiapply.co.uk", "heiapply.com", $language_icon_url) ,
            ];
        }

        return $results_list;
    }

    function point_to_prod($url){
        $url= str_replace("local.co.uk", ".com", $url);
        return str_replace("heiapply.co.uk", "heiapply.com", $url );
    }

    function get_user_info(){
        $uid = $_GET['uid'] ?? null;
        if (!$uid) {
            echo json_encode(['success' => false, 'message' => 'No uid provided']);
            exit;
        }

        // Create and/or load your necessary classes
        $users = new Users();
        $user_info = $users->get(['id' => $uid]);

        if (!$user_info) {
            echo json_encode(['success' => false, 'message' => "User not found"]);
            exit;
        }

        // Example “pull_field” calls
        $telephone = pull_field("core_students", "db765", "WHERE rec_id = $uid");
        $course_audio = pull_field("core_students", "db25618", "WHERE rec_id = $uid");

        // Return JSON
        return [
                'id' => $user_info['id'],
                'first_name' => $user_info['first_name'] ?? '',
                'last_name'  => $user_info['last_name']  ?? '',
                'email'      => $user_info['email']      ?? '',
                'about'      => $user_info['about']      ?? '',
                'gender'     => $user_info['gender']     ?? '',
                'telephone'  => $telephone               ?? '',
                'course_audio' => $course_audio          ?? 'off',
            ];



    }


    function update_profile(){

        $input = file_get_contents('php://input');
        $data  = json_decode($input, true);
        if (!$data) {
            echo json_encode(['success' => false, 'message' => 'No JSON body found']);
            exit;
        }

        // Basic error check
        if (empty($data['uid'])) {
            echo json_encode(['success' => false, 'message' => 'No user ID provided']);
            exit;
        }

        // Prepare the data
        $profile_info = [
            'id'         => $data['uid'],
            'first_name' => $data['first_name'] ?? '',
            'last_name'  => $data['last_name']  ?? '',
            'gender'     => $data['gender']     ?? '',
            'telephone'  => $data['telephone']  ?? '',
            'course_audio' => $data['course_audio'] ?? 'off'
        ];

        // Update user table
        $users = new Users();
        $users->update($profile_info);

        // Update “core_students” table
        $dbhelp = new Db_helper();
        $dbhelp->where('rec_id', $data['uid']);
        $dbhelp->update('core_students', [
            'db765'    => $profile_info['telephone'],
            'db25618'  => $profile_info['course_audio']
        ]);



    }


     function deactivate_reminder_emails($args)
    {
        $current_meta_id = pull_field("dir_core_students_meta", "id", "WHERE rel_id = '".$args['student_id']."'");
        $db = new Db_helper();
        $meta_info['db40613'] = $args['course_ids'];
        $meta_info['rel_id'] = $args['student_id'];
        if ($current_meta_id) {
            $where['id'] = $current_meta_id;
            $db->update('dir_core_students_meta', $meta_info, $where);
            return ["success" => true, "course_ids" => $args['course_ids'], "current_meta_id" => $current_meta_id,'updated'=>true];
        } else {
            $db->system_table_insert_or_update('dir_core_students_meta', $meta_info);
            $current_meta_id = $db->lastInsertId();
            return ["success" => true, "course_ids" => $args['course_ids'], "current_meta_id" => $current_meta_id,'inserted'=>true];
        }
       
    }

    function update_survey_optin($args){

        //if ($_REQUEST['action'] == "set_ols_survey_reminders") {
        $current_meta_id = pull_field("dir_core_students_meta", "id", "WHERE rel_id = '".$args['student_id']."'");
        $db = new Db_helper();
        $meta_info['db49864'] = $args['val'];
        $meta_info['rel_id'] = $args['student_id'];
        if ($current_meta_id) {
            $where['id'] = $current_meta_id;
            $db->update('dir_core_students_meta', $meta_info, $where);
        } else {
            $db->system_table_insert_or_update('dir_core_students_meta', $meta_info);
            $current_meta_id = $db->lastInsertId();
        }
        return array("success" => true, "val" => $args['val'], "current_meta_id" => $current_meta_id);

    }

    function updatePassword($args){
        $args['password'] = trim( stripslashes( str_replace ('\'','\'\'',$args['newPassword'])));
        $args['password'] = md5($args['password']);
        $db = new Db_helper();
        $where['usergroup'] = $_SESSION['usergroup'];
        $where['db119'] = $args['email'];
        $db->update('form_users', ['db222'=>$args['password']], $where);
        return ["success" => true, "info" => $args,'updated'=>true];


    }



    /**
     * Get all Unit IDs given a course_id
     *
     * @param int $course_id
     * @return array List of Unit IDs
     */
    function get_unit_ids_for_course($course_id)
    {
        // First, get all modules belonging to this course
        $modules = $this->get_modules([
            'course_id' => $course_id,
            // optionally set show_hidden_units => true if you need hidden units too
        ]);

        $all_unit_ids = [];
        foreach ($modules as $module) {
            // Now get all units under this module
            $units = $this->get_units([
                'module_id' => $module['id'],
                'show_hidden_units' => true, // set to false if you only want visible units
            ]);

            // Collect their IDs
            foreach ($units as $u) {
                $all_unit_ids[] = $u['id'];
            }
        }

        return $all_unit_ids;
    }


    function ensure_slug_matches_title($id, $type) {
        $dbh = get_dbh();

        // Map type to table, title field, and slug field
       $map = [
            'course' => ['table' => 'ols_online_courses', 'title_field' => 'db21909', 'slug_field' => 'db361083'],
            'module' => ['table' => 'ols_course_modules', 'title_field' => 'db21919', 'slug_field' => 'db360683'],
            'unit'   => ['table' => 'ols_course_units',   'title_field' => 'db21923', 'slug_field' => 'db361093'],
        ];


        if (!isset($map[$type])) {
            throw new InvalidArgumentException("Invalid type: $type");
        }

        $table = $map[$type]['table'];
        $title_field = $map[$type]['title_field'];
        $slug_field = $map[$type]['slug_field'];

        // Fetch the current title
        $sql = "SELECT $title_field FROM $table WHERE id = :id LIMIT 1";
        $stmt = $dbh->prepare($sql);
        $stmt->execute([':id' => $id]);
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        

        if (!$row) {
            throw new RuntimeException("Record not found for $type with ID $id.");
        }

        $title = $row[$title_field];

        // Call your existing slug generator (it will auto-update if needed)
        return $this->generate_unique_slug($title, $table, $slug_field, $id);
    }




    function generate_unique_slug($title, $table, $slug_field, $existing_id = null) {
        $dbh=get_dbh();
        // Step 1: Generate basic slug

        //$slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title))).'-'.$existing_id;
        //$slug = strtolower(trim(str_replace(" ", "-", $title))).'-'.$existing_id;
         // Multilingual-friendly slug: keep Unicode letters/numbers, replace everything else with dashes
        // $title = iconv('UTF-8', 'ASCII//TRANSLIT//IGNORE', $title);
        // $slug = mb_strtolower(trim(preg_replace('/[^\p{L}\p{N}]+/u', '-', $title)), 'UTF-8').'-'.$existing_id;


        $transliterated = iconv('UTF-8', 'ASCII//TRANSLIT//IGNORE', $title).'-'.$existing_id;
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $transliterated), '-'));
        $slug = trim($slug, '-');
        $original_slug = $slug;
        $original_slug = $slug;
        $i = 1;

        // Step 2: Ensure uniqueness
        while (true) {
            $sql = "SELECT id FROM $table WHERE $slug_field = :slug AND (rec_archive IS NULL OR rec_archive = '')";
            $params = [':slug' => $slug];
            if ($existing_id !== null) {
                $sql .= " AND id != :id";
                $params[':id'] = $existing_id;
            }

            $stmt = $dbh->prepare($sql);
            $stmt->execute($params);

            if ($stmt->rowCount() === 0) break;

            $slug = $original_slug . '-' . $i++;
        }

        // Step 3: Update record if needed
        if ($existing_id !== null) {
            $check_sql = "SELECT $slug_field FROM $table WHERE id = :id LIMIT 1";
            $check_stmt = $dbh->prepare($check_sql);
            $check_stmt->execute([':id' => $existing_id]);
            $existing_row = $check_stmt->fetch(PDO::FETCH_ASSOC);

            if ($existing_id==$slug) {
                $slug=$existing_row[$slug_field];
            }

            if (!$existing_row) {
                $update_sql = "UPDATE $table SET $slug_field = :slug WHERE id = :id";
                $update_stmt = $dbh->prepare($update_sql);
                $update_stmt->execute([':slug' => $slug, ':id' => $existing_id]);
            }
        }

        return $slug;
    }




}
