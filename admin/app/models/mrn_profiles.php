<?php

use App\core\support\facades\DB;

/**
 * Enquiries
 */
class mrnProfilesModel
{

    /** ===================================
     * Pop Message
     * ====================================    */
    function popmsg($message, $abv = "", $color = ' style="color:#000"')
    {
        $message = str_replace("'", "\'", $message);
        $msg = "'" . $message . "'";
        return '<span class="hotspot" onmouseover="tooltip.show(' . translate($msg, $_SESSION['lang']) . ');" onmouseout="tooltip.hide();"' . $color . '>' . $abv . '</span> ';
    }

    function get_enq($args = array())
    {
        global $paginator;
        $dbh = get_dbh();
        $active_fields = $this->get_enq_columns();
        $active_columns = array_filter(array_column($active_fields, 'db_field_name'));
        $more_sql = '';
        $search_sql = '';
        $extra_columns_sql = '';

        //preferred scheduled courses
        $preferred_scheduled_course_1 = in_array('db48649', $active_columns);
        $preferred_scheduled_course_2 = in_array('db48650', $active_columns);
        $preferred_scheduled_course_3 = in_array('db48651', $active_columns);


        $active_columns = array_filter($active_columns, function ($c) {
            $exceptions = array('id', 'username_id', 'date', 'db48566', 'db48568', 'db48581', 'db48583', 'db48601', 'db48649', 'db48650', 'db48651', 'db48593', 'db48594', 'db48592');
            return !in_array($c, $exceptions);
        });
        //'db48593',
        $all_columns_sql = '';
        if (count($active_columns) > 0) {
            $active_columns = $this->fields_dimensions($active_columns);

            $all_columns_sql = "," . implode(",", $active_columns);
            // change some field
        }
        $filter_sql_lite ='';
        if(!empty($args['filter_sql_lite'])){
            $filter_sql_lite = $args['filter_sql_lite'];
        }

        if (!empty($args['id'])) {
            $more_sql .= " AND sis_profiles.id = '" . $args['id'] . "' ";
        }
        if (!empty($args['ids'])) {
            $more_sql .= " AND sis_profiles.id IN (" . $args['ids'] . ") ";
        }
        if (!empty($args['parent_email'])) {
            $more_sql .= " AND sis_profiles.db48583 = '" . $args['parent_email'] . "' ";
        }

        if (!empty($args['status_id'])) {
            $more_sql .= " AND sis_profiles.db48581 = '" . $args['status_id'] . "' ";
        }

        if (!empty($args['parents_email'])) {
            foreach ($args['parents_email'] as $parents) {
                $parent[] = $parents;
            }
            $more_sql .= " AND sis_profiles.db48583 IN ('" . implode("','", $parent) . "')";
        }

        if (!empty($args['email_address'])) {
            $more_sql = " AND sis_profiles.db48583 ='" . $args['email_address'] . "' ";
        }

        if (!empty($args['has_email'])) {
            $more_sql .= " AND sis_profiles.db48583 != '' ";
        }

        if (!empty($args['first_name'])) {
            $more_sql .= " AND sis_profiles.db48566 ='" . $args['first_name'] . "' ";
        }

        if (!empty($args['status']) && $args['status'] != 'archived_profiles') {
            $more_sql .= " AND sis_profiles.db48581 ='" . $args['status'] . "' ";
        }


        if (!empty($args['surname'])) {
            $more_sql .= " AND sis_profiles.db48568 ='" . $args['surname'] . "' ";
        }

        if (!empty($args['middle_name'])) {
            $more_sql .= " AND sis_profiles.db48567 ='" . $args['middle_name'] . "' ";
        }

        if (!empty($args['gender'])) {
            $more_sql .= " AND sis_profiles.db48573 ='" . $args['gender'] . "' ";
        }

        if (!empty($args['city'])) {
            $more_sql .= " AND sis_profiles.db48596 ='" . $args['city'] . "' ";
        }

        if (!empty($args['course_id'])) {
            $more_sql .= " AND sis_profiles.db48593 ='" . $args['course_id'] . "' ";
        }

        if (!empty($args['ids_in']) && strlen(trim($args['ids_in'])) > 0) {
            $more_sql .= " AND sis_profiles.id IN (" . $args['ids_in'] . ")";
        }

        if (!empty($args['profile_email'])) {
            $more_sql .= " AND sis_profiles.db48583 LIKE '%" . $args['profile_email'] . "%' ";
        }

        if (!empty($args['profile_name'])) {
            $more_sql .= " AND sis_profiles.db48566 LIKE '%" . $args['profile_name'] . "%' ";
        }

        if (!empty($args['profile_surname'])) {
            $more_sql .= " AND sis_profiles.db48568 LIKE '%" . $args['profile_surname'] . "%' ";
        }
        $profile_early_leaver_data = '';
        if (!empty($args['applicant_or_student']) && $args['applicant_or_student'] != 'archived_profiles') {
            $applicant_or_students = pull_field("lead_mrn_stages", "GROUP_CONCAT(CONCAT(\"'\",db49850,\"'\"))","WHERE db166247='".$args['applicant_or_student']."'");
            $applicant_or_students_without_quote = pull_field("lead_mrn_stages", "GROUP_CONCAT(db49850)","WHERE db166247='".$args['applicant_or_student']."'");

            $more_sql .= " AND db48581 in ($applicant_or_students)";

            if ($applicant_or_students_without_quote == '11' || $applicant_or_students_without_quote == '12') { //early leaver or graduated
                $profile_early_leaver_data .= "(SELECT CONCAT(db106, ' ', db111) FROM form_users where form_users.id =  (SELECT rec_id FROM sis_profile_history WHERE rel_id = sis_profiles.id AND db61492 = '$applicant_or_students_without_quote' AND  db61494 = '1' ORDER BY id DESC LIMIT 1))  as 'status_changed_by', (SELECT DATE_FORMAT(date,'%d/%m/%Y %H:%i') FROM sis_profile_history WHERE rel_id = sis_profiles.id AND db61492 = '$applicant_or_students_without_quote' AND  db61494 = '1' ORDER BY id DESC LIMIT 1) as '$args[applicant_or_student] date',";
            }
        }
        $profile_archive_data = '';
        $profile_rec_check = "AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '')";
        if (!empty($args['status']) &&$args['status'] == 'archived_profiles' || $args['applicant_or_student'] == 'archived_profiles') {
            $profile_rec_check = "AND (sis_profiles.rec_archive IS NOT NULL AND sis_profiles.rec_archive != '')";
            $profile_archive_data = "if (sis_profiles.rec_archive = 1,'Super Admin',(SELECT CONCAT(db106, ' ', db111) FROM form_users where form_users.id = sis_profiles.rec_archive)) as 'archived_by',
                                    DATE_FORMAT(sis_profiles.rec_lstup, '%d/%m/%Y') as 'archived_date',";
        }


        if (!empty($args['order'])) {
            $o = explode(" ", $args['order']);
            if ($o[0] == "manage") {
                $column = "sis_profiles.id";
            }
            if ($o[0] == "date_created") {
                $column = "sis_profiles.date";
            }
            if ($o[0] == "date/time_created") {
                $column = "sis_profiles.date";
            }
            if ($o[0] == "notes") {
                $column = "sis_profiles.date";
            }
            if ($o[0] == "surname") {
                $column = "sis_profiles.db48568";
            }
            if ($o[0] == "first_name") {
                $column = "sis_profiles.db48566";
            }
            if ($o[0] == "course") {
                $column = "core_courses.db232";
            }
            if ($o[0] == "status") {
                $column = "sis_profiles.db48581";
            }
            if (strpos("__" . $o[0], "db") == 2) {
                $column = $o[0];
            }
            $order = ($column ?: "sis_profiles.id") . " " . ($o[1] == "asc" ? "asc" : "desc");
            $order_sql = " ORDER BY " . $order;

        } else {
            $order_sql = " ORDER BY sis_profiles.date DESC";
        }
        if (!empty($args['search'])) {
            $term = trim(addslashes($args['search']));
            $search_sql = " AND ( 
            sis_profiles.db48568 LIKE '%" . $term . "%' 
            OR sis_profiles.db48566 LIKE '%" . $term . "%'
            OR CONCAT(sis_profiles.db48566,db48568) LIKE '%" . str_replace(" ", "", $term) . "%'
            OR sis_profiles.id LIKE '%" . $term . "%'				
            OR sis_profiles.db48583 LIKE '%" . $term . "%'
            OR sis_profiles.db48593 LIKE '%" . $term . "%'
            OR sis_profiles.db48597 LIKE '%" . $term . "%'
            OR sis_profiles.db48599 LIKE '%" . $term . "%'
            OR sis_profiles.id LIKE '%" . $term . "%'
            OR sis_profiles.date LIKE '" . date_search_term_time($term) . "'";
            //cannot use these as it breaks the sql
            //foreach ($active_columns as $key => $field) {
            //    $search_sql .= "OR {$field} LIKE '%{$term}%' ";
            //}

            $search_sql .= ")";
        }
        $preferred_scheduled_course_1_sql ='';
        if ($preferred_scheduled_course_1) {
            $preferred_scheduled_course_1_sql = "IF ((db48649 IS NULL OR db48649 = '' OR db48649 ='not specified'),'', (SELECT CONCAT(DATE_FORMAT(db14947,'%d/%m/%Y'),' ', CONCAT(SUBSTRING(db14948,1,2),':',SUBSTRING(db14948,3,2))) from sis_course_schedule WHERE sis_course_schedule.id = db48649)) as  db48649,";
        }
        $preferred_scheduled_course_2_sql ='';
        if ($preferred_scheduled_course_2) {
            $preferred_scheduled_course_2_sql = "IF ((db48650 IS NULL OR db48650 = '' OR db48650 ='not specified'),'', (SELECT CONCAT(DATE_FORMAT(db14947,'%d/%m/%Y'),' ', CONCAT(SUBSTRING(db14948,1,2),':',SUBSTRING(db14948,3,2))) from sis_course_schedule WHERE sis_course_schedule.id = db48650)) as  db48650,";
        }
        $preferred_scheduled_course_3_sql ='';
        if ($preferred_scheduled_course_3) {
            $preferred_scheduled_course_3_sql = "IF ((db48651 IS NULL OR db48651 = '' OR db48651 ='not specified'),'', (SELECT CONCAT(DATE_FORMAT(db14947,'%d/%m/%Y'),' ', CONCAT(SUBSTRING(db14948,1,2),':',SUBSTRING(db14948,3,2))) from sis_course_schedule WHERE sis_course_schedule.id = db48651)) as  db48651,";
        }

        if (!empty($args['first_pass'])) {
            $extra_columns_sql .= "
            '' as unsubscribed,
            '' as 'no_days_since_last_attended',
			'' as 'date_last_attended',
			'' as 'date_initial_ilp',
			'' as 'date_last_ilp_review',
			'' as 'has_initial_ilp',
			'' as 'has_ilp_review',
	        '' as 'date_of_course_last_booked',
            '' as 'attended_a_course_with_start_date',
            '' as 'booked_a_course_session_with_start_date',
            '' as 'has_attended',
            '' as 'has_booked',
            '' as 'has_not_booked',
			'' as 'no_current_bookings',
		    '' as 'no_of_courses_attended',
			'' as 'no_of_courses_cancelled_by_learner_during',
			'' as 'no_of_courses_cancelled_by_learner_prior',
			'' as 'no_of_courses_cancelled_by_college',
			'' as 'no_of_courses_not_attended',
			'' as  'no_of_courses_partially_attended'
            ";
        } else {
            $extra_columns_sql .= "
            if((SELECT count(id)
				FROM coms_mail_unsubscriptions WHERE usergroup='{$_SESSION['usergroup']}'
				AND (rec_archive IS NULL OR rec_archive ='' )
				AND (db64115='on' or db171581 is not null or db171581<> '' or db171584 is not null or db171584<> '' )
				and db64116 =db48583 )> 0, 'Yes', 'No') as unsubscribed,
				(SELECT DATE_FORMAT(db61500,'%d/%m/%Y') FROM sis_ind_learner_plan 
                                    WHERE sis_ind_learner_plan.rel_id =convert(cast(sis_profiles.rel_id as CHAR) using utf8) 
                                    AND (sis_ind_learner_plan.rec_archive IS NULL OR sis_ind_learner_plan.rec_archive = '')
                                    AND (db61498 = 'inital_ILP')
                                    ORDER BY id DESC LIMIT 1
                                ) as 'date_initial_ilp',
            (SELECT DATE_FORMAT(db61500,'%d/%m/%Y') FROM sis_ind_learner_plan 
                                    WHERE sis_ind_learner_plan.rel_id = convert(cast(sis_profiles.rel_id as CHAR) using utf8) 
                                    AND (sis_ind_learner_plan.rec_archive IS NULL OR sis_ind_learner_plan.rec_archive = '')
                                    AND (db61498 != 'inital_ILP')
                                    ORDER BY db61500 DESC LIMIT 1
                                ) as 'date_last_ilp_review',
			IF ((SELECT id FROM sis_ind_learner_plan 
                                    WHERE sis_ind_learner_plan.rel_id = convert(cast(sis_profiles.rel_id as CHAR) using utf8) 
                                    AND (sis_ind_learner_plan.rec_archive IS NULL OR sis_ind_learner_plan.rec_archive = '')
                                    AND (db61498 = 'inital_ILP')
                                    ORDER BY db61500 DESC LIMIT 1
                                ) IS NOT NULL,'yes','no') as 'has_initial_ilp',
			IF ((SELECT id FROM sis_ind_learner_plan 
                                    WHERE sis_ind_learner_plan.rel_id = convert(cast(sis_profiles.rel_id as CHAR) using utf8) 
                                    AND (sis_ind_learner_plan.rec_archive IS NULL OR sis_ind_learner_plan.rec_archive = '')
                                    AND (db61498 = 'in_learning_review')
                                    ORDER BY db61500 DESC LIMIT 1
                                ) IS NOT NULL,'yes','no') as 'has_ilp_review',
			 (SELECT datediff(date(now()), db14949) FROM core_students
			JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
			JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
			JOIN sis_course_schedule ON sis_course_schedule.id = db14977
			WHERE core_students.rel_id = sis_profiles.rel_id
			 AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
			 AND (db64664 = 1 OR db64664 = 2)
			ORDER BY db14949 DESC LIMIT 1) as 'no_days_since_last_attended',
			
			(SELECT DATE_FORMAT(db14949,'%d/%m/%Y') FROM core_students
	            JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
	            JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
	            JOIN sis_course_schedule ON sis_course_schedule.id = db14977
	            WHERE core_students.rel_id = sis_profiles.rel_id
	            AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
	            AND (db64664 = '1' OR db64664 = '2') AND sis_course_schedule.db14949<=DATE(now())
	            ORDER BY db14949 DESC LIMIT 1
            ) as 'date_last_attended',
	           
	        (SELECT DATE_FORMAT(db14947,'%d/%m/%Y') FROM core_students
	            JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
	            JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
	            JOIN sis_course_schedule ON sis_course_schedule.id = db14977
	            WHERE core_students.rel_id = sis_profiles.rel_id
	            AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
	            and (db59978 = '2' or db59978= '3')
	            ORDER BY sis_sched_booking_detail.id DESC LIMIT 1
            ) as 'date_of_course_last_booked',
                                        
	        (SELECT db14978 FROM core_students
				JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
				JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
				JOIN sis_course_schedule ON sis_course_schedule.id = db14977 WHERE core_students.rel_id = sis_profiles.rel_id
				AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
				AND (db64664 = '1' OR db64664 = '2') order by sis_scheduled_booking.id asc limit 1
			) as 'attended_a_course_with_start_date',
			
			IFNULL((SELECT db14947 FROM core_students
            JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
            JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
            JOIN sis_course_schedule ON sis_course_schedule.id = db14977 
            WHERE core_students.rel_id = sis_profiles.rel_id
            AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
            AND (db59978 = '1' OR db59978 = '2' OR db59978 = '14') order by sis_scheduled_booking.id asc limit 1), 
            (SELECT db59835 FROM core_students
            JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
            JOIN sis_session_bookings ON sis_session_bookings.rel_id = convert(cast(core_students.id as CHAR) using utf8)
            JOIN sis_scheduled_sessions ON sis_scheduled_sessions.id = CAST(sis_session_bookings.db59901 AS SIGNED)
            WHERE core_students.rel_id = sis_profiles.rel_id
            AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
            AND (sis_session_bookings.rec_archive IS NULL OR sis_session_bookings.rec_archive = '')
            AND (db59902 = '1' OR db59902 = '2' OR db59902 = '14') order by sis_session_bookings.id asc limit 1) 
            ) as 'booked_a_course_session_with_start_date',

	        (SELECT db16136 FROM core_students
				JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
				JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
				JOIN sis_course_schedule ON sis_course_schedule.id = db14977 WHERE core_students.rel_id = sis_profiles.rel_id
				AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
				AND (db64664 = '1' OR db64664 = '2') order by sis_scheduled_booking.id asc limit 1
			) as 'has_attended',
				    
	        (SELECT db16136 FROM core_students
				JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
				JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
				JOIN sis_course_schedule ON sis_course_schedule.id = db14977 WHERE core_students.rel_id = sis_profiles.rel_id
				AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
				 order by sis_scheduled_booking.id asc limit 1
			) as 'has_booked',
			
			(SELECT count(sis_sched_booking_detail.id) FROM core_students
			JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
			JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
			JOIN sis_course_schedule ON sis_course_schedule.id = db14977
			WHERE core_students.rel_id = sis_profiles.rel_id AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
			AND (db59978 = 2 OR db59978 = 3) AND db14978 >= date(now())) as 'no_current_bookings',
			
			(SELECT count(*) FROM core_students
			JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
			JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
			JOIN sis_course_schedule ON sis_course_schedule.id = db14977 WHERE core_students.rel_id = sis_profiles.rel_id
			AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (db64664 = 1 OR db64664 = 2)) as 'no_of_courses_attended',
			
			(SELECT count(core_students.id) FROM core_students
			JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
			JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
			JOIN sis_course_schedule ON sis_course_schedule.id = db14977 WHERE core_students.rel_id = sis_profiles.rel_id
			AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
			AND (db64664 = 8)) as 'no_of_courses_cancelled_by_learner_during',
			
			(SELECT count(core_students.id) FROM core_students
			JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
			JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
			JOIN sis_course_schedule ON sis_course_schedule.id = db14977 WHERE core_students.rel_id = sis_profiles.rel_id
			AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
			AND (db64664 = 4)) as 'no_of_courses_cancelled_by_learner_prior',
			
			(SELECT count(core_students.id) FROM core_students
			JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
			JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
			JOIN sis_course_schedule ON sis_course_schedule.id = db14977 WHERE core_students.rel_id = sis_profiles.rel_id
			AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
			AND (db64664 = 5)) as 'no_of_courses_cancelled_by_college',
			
			(SELECT count(core_students.id) FROM core_students
			JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
			JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
			JOIN sis_course_schedule ON sis_course_schedule.id = db14977 WHERE core_students.rel_id = sis_profiles.rel_id
			AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
			AND (db64664 = 3)) as 'no_of_courses_not_attended',
			
			
			(SELECT count(core_students.id) FROM core_students
			JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
			JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
			JOIN sis_course_schedule ON sis_course_schedule.id = db14977 WHERE core_students.rel_id = sis_profiles.rel_id
			AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
			AND (db64664 = 2)) as  'no_of_courses_partially_attended'
            ";
        }

        $query = "SELECT
            sis_profiles.username_id,
            sis_profiles.id as 'profile_id',
            sis_profiles.rel_id as 'profile_core_student_id',
            sis_profiles.id as 'id',
            sis_profiles.`date` AS 'date_created',
            sis_profiles.rec_archive,
            $profile_archive_data
            $profile_early_leaver_data
            db48566 AS 'first_name',
            db48568 AS 'surname',
            db48572 as dob,
            (YEAR(NOW()) - YEAR(db48572) - (DATE_FORMAT(NOW(), '%m%d') < DATE_FORMAT(db48572, '%m%d'))) as age,
            (SELECT db49848 FROM lead_mrn_stages WHERE id = db48581) AS 'status',
            /*core_courses.db232 AS 'course',*/
            db48596 as 'city',
            db48583 as 'email_address',
            db48597 as mobile,
            IF ((db48593 IS NULL OR db48593 = '' OR db48593 ='not specified'),'', (SELECT db232 from core_courses where id = db48593)) as  db48593,
            $preferred_scheduled_course_1_sql
            IF ((db48594 IS NULL OR db48594 = '' OR db48594 ='not specified'),'', (SELECT db232 from core_courses where id = db48594)) as  db48594,
            $preferred_scheduled_course_2_sql
            IF (( db48592 IS NULL OR db48592 = '' OR db48592 ='not specified'),'', (SELECT db232 from core_courses where id = db48592)) as  db48592,
            $preferred_scheduled_course_3_sql
            IFNULL(db233984,'') as 'admin_notes',
            IF((SELECT db48826 FROM sis_course_geog_coverage WHERE id = db48601 and (rec_archive IS NULL OR rec_archive = '')) IS NULL,NULL,db48601) AS 'db48601',
            $extra_columns_sql

            $all_columns_sql
            FROM `sis_profiles`
            /*LEFT JOIN core_courses on core_courses.id = sis_profiles.db48593*/
            LEFT join lead_mrn_stages on lead_mrn_stages.id= sis_profiles.db48581
            WHERE sis_profiles.usergroup='{$_SESSION['usergroup']}'
            {$profile_rec_check}
            $more_sql
            $filter_sql_lite
            $search_sql
        ";

        $query = $query . $order_sql;
        $limit_sql = " ";


        if (!empty($args['paginate'])) {
            $total_query = "SELECT sis_profiles.id FROM `sis_profiles` 
            LEFT JOIN core_courses on core_courses.id = sis_profiles.db48593
            LEFT join lead_mrn_stages on lead_mrn_stages.id= sis_profiles.db48581
            WHERE sis_profiles.usergroup='{$_SESSION['usergroup']}' 
            {$profile_rec_check}
            $more_sql
            $filter_sql_lite 
            $search_sql
        ";
            $limit_sql = $paginator->limit_sql();
            $paginator->calculate_total_entries($total_query);
        }

        $query1 = $query;
        $query = $query . $limit_sql;
        //echo $query;

        dev_debug("GET EOIs - " . $query . " Json= " . json_encode($args));
        $list = array();
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        //DEBUG HERE
        $results_list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $timezone_name = get_timezone_name();
        $preferred_date_format = get_preferred_date_format();
        $preferred_date_time_format = $preferred_date_format . ' H:i';
        if (!(empty('check_duplicates'))) {
            $args_check_duplicates = array();
            list ($args_check_duplicates ['search_non_active'], $args_check_duplicates ['search_dob'], $args_check_duplicates ['search_graduates'])
                =  explode('?|?',pull_field("lead_preferences", "CONCAT(db248882,'?|?',db231539,'?|?',db298181)", "WHERE usergroup = $_SESSION[usergroup] and (rec_archive is null or rec_archive = '')"));

        }
        foreach ($results_list as $entry) {
            $datetime = convert_datetime_to_timezone($entry ['date_created'], $timezone_name, $preferred_date_time_format);
            $entry ['date_created'] = $datetime;
            $dob_date = date_create($entry['dob']);
            $entry ['dob'] = date_format($dob_date, $preferred_date_format);
            if (!(empty('check_duplicates'))) {
                $profile_data = [
                    'email'=> $entry['email_address'],
                    'mobile' => $entry['mobile'],
                    'id'=> $entry['id'],
                    'first_name' => $entry['first_name'],
                    'surname' => $entry['surname'],
                    'dob' => $entry['dob']
                ];
                $duplicate_check = $this->check_duplicates($profile_data, $args_check_duplicates);
                $entry['duplicate_check'] = $duplicate_check;
            }

            $list[] = $entry;
        }


        if (array_key_exists('sql', $args)) {
            die(json_encode($list));
        }


        if (!empty($args['return_sql'])) {
            return $query1;
        } elseif (!empty($args['id'])) {
            return $list[0];
        } else {
            return $list;
        }

        return $list;
    }

    function save_merge_job($args = array())
    {
        global $db;
        //CREATE MERGE JOB
        $fields = "id";
        //SAVE MERGE JOB DETAILS

        //UPDATE ROWS TO MERGED

        //INSERT CORE NOTE INTO RECORDS
        //print_r($args["fields"]);
        foreach ($args["fields"] as $value) {
            //echo $value["field"]." ".$value["value"]."<br>";
            if (($value["field"] != 'id') || ($value["field"] != 'username_id')) {
                $query = "SELECT db_field_name  FROM system_table WHERE pg_id = 97 AND usergroup = " . $_SESSION["usergroup"] . " AND (LOWER(REPLACE(`name`, ' ', '_')) = '" . $value["field"] . "' OR db_field_name='" . $value["field"] . "')";
                //echo $query;
                $column_name = $db->rawQuery($query);
                if ($column_name) {
                    $pullname = $column_name[0]["db_field_name"];
                    $fvalue = pull_field("lead_profiles", $pullname, "WHERE id='" . $value["value"] . "'");
                    //UPDATE MASTER
                    $enq_info = array(
                        'id' => $args["master"],
                        $pullname => $fvalue,
                    );
                    $fields .= "," . $pullname;
                    $db->system_table_insert_or_update('lead_profiles', $enq_info);
                }

            }
        }

        //SAVE RULE
        $rule_info = array(
            'db41785' => 'Maunal Merge',
            'db41786' => 'User executed maunal merge',
            /*'db42858'=>$args["query"],*/
        );
        $fields .= "," . $pullname;
        $db->system_table_insert_or_update('form_merge_rules', $rule_info);
        $rule_id = $db->lastInsertId();


        //SAVE JOB PULL ID
        $job_info = array(
            'db41780' => 'Maunal Merge',
            'db41781' => 'User executed maunal merge on' . date("Y-m-d HH:mm:ss"),
            'db41782' => $rule_id,
            'db41783' => "Manual",
            'db43094' => 'yes',
        );
        $db->system_table_insert_or_update('form_merge_jobs', $job_info);
        $job_id = $db->lastInsertId();

        //SAVE MASTER RECORD FOR JOB
        $master_detail = array(
            'db43089' => $args["master"],
            'db43090' => 'yes',
            'db43091' => $job_id,
            'rel_id' => $job_id,
            'db43092' => $fields,
            'db43093' => 'lead_profiles',
        );
        $db->system_table_insert_or_update('form_merge_jobs_details', $master_detail);
        //SAVE MERGED RECORDS

        foreach ($args["records"] as $value) {
            if ($value != $args["master"]) {
                $enq_info = array(
                    'id' => $value,
                    'db1056' => 13,
                );
                $db->system_table_insert_or_update('lead_profiles', $enq_info);
                $username_id = pull_field("lead_profiles", "username_id", "WHERE id='" . $args["master"] . "'");
                $url = engine_url("direct/proc?pg=97&vw=") . $username_id . "&ref=" . $args["master"];
                $link = "<a href = '" . $url . "'> View </a>";
                $msg = 'Enquiry Merged into ' . $link;
                //INSERT INTO lead_interactions
                $lead_info = array(
                    'rel_id' => $value,
                    'db1405' => date("Y-m-d"),
                    'db1403' => 6,
                    'db1404' => $msg,
                );
                $db->system_table_insert_or_update('lead_interactions', $lead_info);
                $merge_detail = array(
                    'db43089' => $value,
                    'db43090' => 'no',
                    'db43091' => $job_id,
                    'rel_id' => $job_id,
                    'db43092' => $fields,
                    'db43093' => 'lead_profiles',
                );
                $db->system_table_insert_or_update('form_merge_jobs_details', $merge_detail);
            }
            //echo $value."<br>";
            //db1056
        }
        return "Merge Successfull";

    }

    function generate_form($args = array())
    {
        $table = '<button class="btn btn-default pull-right" title="Scroll Right" style="margin-top: 10px;margin-right: 10px;" onclick="ScrollByLeftModal()"><span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span></button>';
        $table .= '<button class="btn btn-default pull-right" style="margin-top: 10px;" title="Scroll Left" onclick="ScrollByRightModal()"><span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span></button>';
        $table .= '<table class="table tmodal"><input type="hidden" id="master_rec_id" name="master_rec_id" value="' . $args["rows"][0] . '">';//master_rec
        $count = count($args["rows"]);
        $ids = implode(',', $args["rows"]);

        $enq_args = array(
            'ids' => $ids,
        );
        $all_results = $this->get_enq($enq_args);
        //print_r($results);
        $enq_args = array(
            'ids' => $ids,
            'return_sql' => true,
        );
        $return_sql = $this->get_enq($enq_args);
        $table .= '<input type="hidden" id="query" name="query" value="' . $return_sql . '">';
        $num = 1;
        $table .= '<tr><th></th><th><div id="master_id" name="master_id">MASTER RECORD:RECORD ' . $num . '</div></th>';
        $checked = "checked";
        //onclick="select_as_master('.$one.')
        foreach ($args["rows"] as $one) {
            //$table.= '<th>RECORD '.$num.'</th>';
            $table .= '<th><div class="custom-control custom-radio custom-control-inline">
				  <input type="radio" id="master_rec_' . $one . '" name="master_rec" class="master custom-control-input rec_' . $one . '" ' . $checked . ' " value="' . $one . '" data-count = "' . $num . '">
				  <label class="custom-control-label" for="master_rec_' . $one . '">RECORD ' . $num . '</label>
				</div></th>';
            $checked = "";
            $num++;
        }
        $table .= '</tr>';
        foreach ($results as $k => $v) {
            $resulto[$k] = array_count_values($v);
            arsort($resulto[$k]);
        }
        //print_r($resulto);
        //print_r($args["columns"]);
        foreach ($args["columns"] as $value) {
            if ($value['active'] != 2) {
                $resultArray = array();


                //print_r(array_filter(array_map((function ($v) { return $v > 1 ? $v : 0; }), array_count_values($results[$value['db_field_name']]))));
                //print_r($all_results);
                $subArr = array_column($all_results, $value['db_field_name']);
                $countArr = array_count_values($subArr);//array with all count
                $filteredArr = array_filter($countArr, function ($val) {
                    return $val > 1;
                });//filter that is having duplicates
                $cnt = array_sum($filteredArr);
                if ($count == $cnt) {
                    $color = 'style="background: #CDF5CD;"';
                    $disabled = 'disabled';
                } elseif (($value['db_field_name'] == "id") || ($value['db_field_name'] == "username_id")) {
                    $disabled = 'disabled';
                } else {
                    $color = "";
                    $disabled = '';
                }
                //$count = count(array_keys($resulto, $values['label'], true));$value['db_field_name']
                $table .= '<tr ' . $color . '><input type="hidden" id="' . $value['db_field_name'] . '" name="' . $value['db_field_name'] . '" value="' . $args["rows"][0] . '"><td>' . ucwords(str_replace("_", " ", $value['title'])) . '</td>';
                //GET FIRST ONE
                $enq_args = array(
                    'id' => $args["rows"][0],
                );
                $result = $this->get_enq($enq_args);

                $item = $result[$value['db_field_name']];
                if ($value['options']) {
                    foreach ($value['options'] as $key => $values) {
                        if ($values['value'] == $item) {
                            $item = $values['label'];
                            break;
                        }

                    }
                }
                $table .= '<td><div id = "' . $value['db_field_name'] . '" name = "' . $value['db_field_name'] . '">' . $item . '</td><div>';
                //EOF
                $checked = "checked";
                foreach ($args["rows"] as $one) {
                    $enq_args = array(
                        'id' => $one,
                    );
                    $result = $this->get_enq($enq_args);

                    $item = $result[$value['db_field_name']];
                    if ($value['options']) {
                        foreach ($value['options'] as $key => $values) {
                            if ($values['value'] == $item) {
                                $item = $values['label'];
                                break;
                            }

                        }
                    }
                    $table .= '<td><div class="custom-control custom-radio custom-control-inline" ' . $disabled . '>
						  <input type="radio" id="' . $value['db_field_name'] . '_' . $one . '" name="' . $value['db_field_name'] . '_" class="field custom-control-input rec_' . $one . ' ' . $value['db_field_name'] . '" ' . $checked . ' data-field = "' . $value['db_field_name'] . '" value="' . $item . '" ' . $disabled . ' data-fvalue = "' . $one . '">
						  <label class="custom-control-label" for="' . $value['db_field_name'] . '_' . $one . '" ' . $disabled . '>' . $item . '</label>
						</div></td>';

                    //$table.= '<td><input type="hidden" id="'.$value['db_field_name'].'" name="'.$value['db_field_name'].'" value="'.$value.'">'.$item.'</td>';
                    $mycolumns .= $value['db_field_name'] . ',';
                    $checked = "";
                }

                $table .= '</tr>';
            }
        }
        $f = substr(trim($mycolumns), 0, -1);
        $myArray = explode(',', $f);
        $myArray = array_unique($myArray);
        $mycolumns = implode(',', $myArray);
        $myrows = implode(',', $args["rows"]);
        $table .= '<input type="hidden" id="all_fields" name="all_fields" value="' . $mycolumns . '">';
        $table .= '<input type="hidden" id="rows" name="rows" value="' . $myrows . '">';
        $table .= '<input type="hidden" id="filter" name="filter" value="' . $args['filter'] . '">';
        $table .= '</table>';
        return $table;
    }

    /** ===================================
     * Process Bulk Actions
     * ====================================    */
    function process_bulk_action($args = array())
    {
        if ($_POST['bulk_action']) {
            $selected_rows = [];
            $bulk_action_list_list = [];

            if (empty($_POST['select_all_entries'])) {
                foreach ($_POST as $key => $value) {
                    if (strpos($key, 'table_row_') !== false) {
                        $row_id = str_replace("table_row_", "", $key);
                        $selected_rows[] = $row_id;
                    }
                }
            }

            foreach ($args['results'] as $entry_info) {
                if (in_array($entry_info['id'], $selected_rows)) {
                    $bulk_action_list_list[] = $entry_info;
                }
            }


            if ($_POST['select_all_entries']) {
                $bulk_action_list_list = $args['results'];
                if (!empty($_POST["un_ticked_ids"])) {
                    $un_ticked_ids = explode(',', $_POST["un_ticked_ids"]);
                    foreach ($bulk_action_list_list as $key => $entry) {
                        if (in_array($entry["id"], $un_ticked_ids)) {
                            unset($bulk_action_list_list[$key]);
                        }
                    }
                }
            }


            /** ===================================
             * Bulk Update Status
             * ====================================    */

            if ($_POST['bulk_action'] == "change_status" && $_POST['bulk_status_id']) {
                foreach ($bulk_action_list_list as $entry) {
                    $stage_args = array(
                        'student_id' => $entry['id'],
                        'status_id' => $_POST['bulk_status_id'],
                    );
                    $this->update_stage($stage_args);
                }

                return "The status has now been updated successfully.";
            }


            /** ===================================
             * Bulk ReQoL
             * ====================================    */


            if ($_POST['bulk_action'] == "reqol_request") {
                foreach ($bulk_action_list_list as $entry) {

                    $this->reqol_request($entry);
                }

                return "ReQoL Request Sent.";
            }


            /** ===================================
             * Bulk Convert Profiles
             * ====================================    */

            if ($_POST['bulk_action'] == "convert_student") {
                //can user log in
                $login_pref = pull_field("lead_preferences", "db66413", "WHERE usergroup=" . $_SESSION['usergroup']);
                foreach ($bulk_action_list_list as $entry) {
                    $args = $entry['id'];
                    //for each entry get the core students id
                    list($form_user_id, $core_student_id) = explode('&?&?&?', pull_field("sis_profiles", "CONCAT(rec_id, '&?&?&?', rel_id)", "WHERE id=$args AND usergroup =" . $_SESSION['usergroup']));
                    $this->convert_student($args, $_POST['bulk_status_id'], $core_student_id, $form_user_id, $login_pref, true);
                }

                return "Profiles Converted Successfully.";
            }

            if ($_POST['bulk_action'] == "convert_student_no_send") {
                //can user log in
                $login_pref = pull_field("lead_preferences", "db66413", "WHERE usergroup=" . $_SESSION['usergroup']);
                foreach ($bulk_action_list_list as $entry) {
                    $args = $entry['id'];
                    //for each entry get the core students id
                    list($form_user_id, $core_student_id) = explode('&?&?&?', pull_field("sis_profiles", "CONCAT(rec_id, '&?&?&?', rel_id)", "WHERE id=$args AND usergroup =" . $_SESSION['usergroup']));
                    $this->convert_student($args, $_POST['bulk_status_id'], $core_student_id, $form_user_id, $login_pref, false);
                }

                return "Profiles Converted Successfully.";
            }

            /** ===================================
             * Bulk Archive
             * ====================================    */

            if ($_POST['bulk_action'] == "archive") {
                $ids = array_map(function ($item) {
                    return $item['id'];
                }, $bulk_action_list_list);
                $n = count($ids);
                $ids = implode(',', $ids);
                $dbh = get_dbh();

                // add to tracker
                $ids_array = explode(',', $ids);
                foreach ($ids_array as $id) {
                    $insert_sql = "INSERT INTO sis_profile_convert (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id, db63026)
									VALUES (?,?,?,?,?,?,?)";
                    $sth = $dbh->prepare($insert_sql);
                    $core_student_id = pull_field('sis_profiles', 'rel_id', "WHERE id={$id}");
                    $sth->execute(array(random(), session_info("uid"), session_info("usergroup"), $core_student_id, custom_date_and_time(), session_info("uid"), '99'));

                    $final_args = [
                        'id' => $id,
                        'action' => 'archive'
                    ];
                    $this->archive_or_unarchive($final_args);

                }
                $sql = "UPDATE sis_profiles SET rec_archive = :user, rec_lstup = CURRENT_TIMESTAMP, rec_lstup_id = :user WHERE id IN ({$ids}) and usergroup={$_SESSION['usergroup']}";
                $st = $dbh->prepare($sql);
                $st->bindParam(':user', $_SESSION['uid'], PDO::PARAM_INT);
                $st->execute();
                return "{$n} EOIs were archived successfully.";
            }

            /** ===================================
             * Bulk Unarchive
             * ====================================    */

            if ($_POST['bulk_action'] == "unarchive") {
                $ids = array_map(function ($item) {
                    return $item['id'];
                }, $bulk_action_list_list);
                $n = count($ids);
                $ids = implode(',', $ids);
                $this->unarchive_record($ids);
                return "{$n} EOIs were unarchived successfully.";
            }


            /** ===================================
             * Bulk Email
             * ====================================    */
            if ($_POST['bulk_action'] == "bulk_email" && $_POST['bulk_email_message']) {


                //$messages = new CommunicationMessages;
                $template_tag = pull_field("coms_template", "db1147", "WHERE id = {$_POST['template_id']}");
                if (!empty($bulk_action_list_list)) {
                    foreach ($bulk_action_list_list as $entry) {

                        if (!empty($_POST['waiting_list'])) {
                            $student_id = pull_field('core_waitinglist', 'rel_id', "WHERE id ={$entry['id']}");
                            $entry['id'] = pull_field("sis_profiles", "id", "WHERE rel_id={$student_id}");
                        }
                        if (!empty($_POST['register_interest_list'])) {
                            $student_id = pull_field('core_course_reg_interest', 'rel_id', "WHERE id ={$entry['id']}");
                            $entry['id'] = pull_field("sis_profiles", "id", "WHERE rel_id={$student_id}");
                        }
                        $msg_args = array(
                            'message' => $_POST['bulk_email_message'],
                            'enq_id' => $entry['id'],
                            'template_name' => $_POST['bulk_email_template'],
                            'template_tag' => $template_tag,
                            'template_id' => $_POST['template_id'],
                            'subject' => $_POST['bulk_email_template_subject'],
                        );
                        $this->send_mail_to_enq($msg_args);
                        //echo $entry['id']."<br>";

                    }
                    return "The message has been sent successfully.";

                } elseif (!empty($selected_rows)) {
                    foreach ($selected_rows as $entry) {
                        if (!empty($_POST['waiting_list'])) {
                            $student_id = pull_field('core_waitinglist', 'rel_id', "WHERE id ={$entry}");
                            $entry = pull_field("sis_profiles", "id", "WHERE rel_id={$student_id}");
                        }
                        if (!empty($_POST['register_interest_list'])) {
                            $student_id = pull_field('core_course_reg_interest', 'rel_id', "WHERE id ={$entry}");
                            $entry = pull_field("sis_profiles", "id", "WHERE rel_id={$student_id}");
                        }
                        $msg_args = array(
                            'message' => $_POST['bulk_email_message'],
                            'enq_id' => $entry,
                            'template_name' => $_POST['bulk_email_template'],
                            'template_tag' => $template_tag,
                            'template_id' => $_POST['template_id'],
                            'subject' => $_POST['bulk_email_template_subject'],
                        );
                        $this->send_mail_to_enq($msg_args);
                        //echo $entry."<br>";

                    }

                    return "The message has been sent successfully.";

                } else {
                    return 'An error has occurred, emails not sent';
                }

            }


            /** ===================================
             * Bulk Text
             * ====================================    */
            if ($_POST['bulk_action'] == "send_sms" && $_POST['bulk_text_message']) {
                $messages = new CommunicationMessages;
                $subject = $_POST['db25666'];
                $usergroup = $_SESSION['usergroup'];
                $send = $_POST['db25671'];


                $response = [];

                //$messages = new CommunicationMessages;
                if (!empty($bulk_action_list_list)) {
                    foreach ($bulk_action_list_list as $entry) {
                        $sms_text = $_POST['bulk_text_message'];
                        if (!empty($_POST['waiting_list'])) {
                            $student_id = pull_field('core_waitinglist', 'rel_id', "WHERE id ={$entry['id']}");
                            $entry['id'] = pull_field("sis_profiles", "id", "WHERE rel_id={$student_id}");
                        }
                        if (!empty($_POST['register_interest_list'])) {
                            $student_id = pull_field('core_course_reg_interest', 'rel_id', "WHERE id ={$entry['id']}");
                            $entry['id'] = pull_field("sis_profiles", "id", "WHERE rel_id={$student_id}");
                        }
                        $recipient_number = pull_field('sis_profiles', 'db48597', "WHERE id = " . $entry['id']);
                        $student_id = pull_field('sis_profiles', 'rel_id', "WHERE id = " . $entry['id']);
                        $student_name = pull_field('core_students', "db39", "WHERE id = {$student_id}");
                        $sms_text = email_template_replace_values("{{first_name}}", $student_name, $sms_text);
                        $sms_text = email_template_replace_values("{{name}}", $student_name, $sms_text);
                        $sms_text = str_replace("{{student}}", $student_id, $sms_text);
                        $sms_text = str_replace("{{domain_name}}", web_url, $sms_text);
                        $sms = $messages->log_sms($template = "", $subject, $sms_text, $recipient_number, $student_id, $usergroup, $send);
                        $response[] = $sms;
                    }
                } elseif (!empty($selected_rows)) {
                    foreach ($selected_rows as $entry) {
                        $sms_text = $_POST['bulk_text_message'];
                        if (empty($sms_text)) {
                            $sms_text = $_POST['db25668'];
                        }
                        if (empty($sms_text) || $sms_text =='undefined') {
                            $sms_text = 'No message to send';
                        }
                        if (!empty($_POST['waiting_list'])) {
                            $course_id = pull_field('core_waitinglist', 'db16146', "WHERE id ={$entry}");
                            $student_ids = pull_field('core_waitinglist', 'rel_id', "WHERE id ={$entry}");
                            $entry = pull_field("sis_profiles", "id", "WHERE rel_id={$student_ids}");
                            $course_name = pull_field("core_courses","db232", "WHERE id =$course_id");
                        }
                        if (!empty($_POST['register_interest_list'])) {
                            list($course_id,$scheduled_course_id) = explode('???', pull_field('core_course_reg_interest', "CONCAT(db207761,'???',db207764)", "WHERE id ={$entry}"));
                            $student_id = pull_field('core_course_reg_interest', 'rel_id', "WHERE id ={$entry}");
                            $entry = pull_field("sis_profiles", "id", "WHERE rel_id={$student_id}");

                            $course_name = pull_field("core_courses","db232", "WHERE id =$course_id");
                            $scheduled_course_info = pull_field("sis_course_schedule", "CONCAT(IFNULL(sis_course_schedule.id, ''),'|||',(SELECT db232 from core_courses where id = db14946),'|||',(SELECT IFNULL(db234,'') from core_courses where id = db14946),'|||',
				    IFNULL(DATE_FORMAT(db14947,'%d %M %Y'), ''),'|||',IFNULL(CONCAT(SUBSTRING(db14948,1,2),':',SUBSTRING(db14948,3,2)), ''),'|||',
				    IFNULL(DATE_FORMAT(db14949,'%d %M %Y'),''),'|||',IFNULL(CONCAT(SUBSTRING(db14950,1,2),':',SUBSTRING(db14950,3,2)), ''),'|||',
				    IFNULL(db63007,''),'|||',IFNULL(db14956,''),'|||',IFNULL((SELECT db14963 from sis_course_venues where id = db14954),''),'|||',
				    IFNULL(db14946, ''),'|||',IFNULL(db15433, ''),'|||',ifnull((SELECT db14966 from sis_course_venues where id = db14954), ''),'|||',
				    ifnull((SELECT db14965 from sis_course_venues where id = db14954), ''),'|||',IFNULL((SELECT db209162 from sis_course_venues where id = db14954), ''),'|||',
				    IFNULL(db14953,''),'|||',IFNULL((SELECT db14968 from sis_course_venues where id = db14954), ''),'|||',
				    IFNULL((SELECT db237356 from sis_course_venues where id = db14954), ''),'|||',
				    IFNULL((select db48826 from sis_course_geog_coverage where id = db66793),''),'|||',IFNULL((SELECT db233 from core_courses where id = db14946),''))",
                                "WHERE id=$scheduled_course_id");
                            list($scheduled_course_id, $scheduled_course_name, $course_summary, $scheduled_course_start_date, $scheduled_course_start_time, $scheduled_course_end_date,
                                $scheduled_course_end_time, $scheduled_course_web_conferencing_info, $scheduled_course_additional_info, $scheduled_course_venue_name, $course_id,
                                $scheduled_course_evaluation_link, $course_postcode, $venue_address, $venue_whatthreewords, $course_minimum_attendees,
                                $venue_access_information, $venue_image, $geo_coverage, $course_abbrev) = explode('|||', $scheduled_course_info);
                            if (!empty($venue_address)) {
                                $course_venue = $scheduled_course_venue_name . '<br>' . $venue_address . ' ' . $course_postcode;
                            } else {
                                $course_venue = $scheduled_course_venue_name;
                            }
                        }
                        $recipient_number = pull_field('sis_profiles', 'db48597', "WHERE id = " . $entry);
                        $student_id = pull_field('sis_profiles', 'rel_id', "WHERE id = " . $entry);
                        $student_name = pull_field('core_students', "db39", "WHERE id = {$student_id}");
                        $sms_text = email_template_replace_values("{{first_name}}", $student_name, $sms_text);
                        $sms_text = email_template_replace_values("{{name}}", $student_name, $sms_text);
                        $sms_text = str_replace("{{student}}", $student_id, $sms_text);
                        $sms_text = str_replace("{{domain_name}}", web_url, $sms_text);
                        $sms_text = str_replace("{{course_name}}", $course_name, $sms_text);
                        $sms_text = str_replace("{{course_start_date}}", $scheduled_course_start_date, $sms_text);
                        $sms_text = str_replace("{{course_venue}}", $course_venue, $sms_text);

                        $sms = $messages->log_sms($template = "", $subject, $sms_text, $recipient_number, $student_id, $usergroup, $send);
                        $response[] = $sms;
                    }
                }
                // get responses and check that are there errors or not
                $statuses = array_column($response, 'status');

                //if there are any errors
                if (in_array('error', $statuses)) {

                    //email the user that some messages failed to send they may visit the sms logs for further information
                    $message = "Some SMSes were not sent out, please visit the sms logs";

                } else {
                    // send an email that all smses have been sent successfully.
                    $message = "SMSes sent out successfully.";

                }
                return $message;
            }


            /** ===================================
             * Bulk Letter
             * ====================================    */

            if ($_POST['bulk_action'] == "bulk_letter") {
                foreach ($bulk_action_list_list as $entry) {
                    $args = array(
                        'student_id' => pull_field('sis_profiles', 'rel_id', "WHERE id =" . $entry['id']),
                        'letter_id' => $_POST['bulk_letter_id'],
                    );
                    $this->send_letter($args);
                }

                return "Letters Sent Successfully.";
            }

        }
    }


    public function get_enq_columns($inactive = '')
    {
        $dbh = get_dbh();
        if (empty($inactive)) {
            $locked_sql = "and t.locked != '9' ";
        } else {
            $locked_sql = "and t.locked = '9' ";
        }
        $query = "SELECT * 
			FROM system_table t 
			WHERE t.pg_id = '597' 
			and t.usergroup = '{$_SESSION['usergroup']}' 
			{$locked_sql}
			and t.db_field_name IS NOT NULL 
			AND t.db_field_name !='' 
			AND t.type!='textonly' 
			AND t.type!='subtitle' 
			AND t.type!='title'
		";
        dev_debug($query);
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function get_enq_stages($args = [])
    {
        $status = $args['status'];
        dev_debug("MRN STATUS1" . $status);
//        echo "<pre>"."Debug  Message inside model!"."</pre>";
        if (empty($status)) {
            $status = "1";
        }
        $dbh = get_dbh();
        $query = "SELECT id, db49848 as stage FROM lead_mrn_stages WHERE (rec_archive is null OR rec_archive = '') AND db49851 ='" . $status . "'  ORDER BY id ASC";
        dev_debug($query);
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function generate_temp_emails($args)
    {
        $features = pull_field('form_schools', 'db1261', "WHERE id = {$_SESSION['usergroup']}");
        $schools = new Schools;
        $email_rows = array();
        $rows = explode("&", $args["rows"]);
        $all_html = "";
        $count = 1;
        $total = 0;
        if (isset($args['limit'])) $limit = $args['limit']; else $limit = FALSE;

        foreach ($rows as $key) {
            if (strpos($key, 'table_row_') !== false) {
                $row_id = str_replace("table_row_", "", $key);
                $row_id = substr($row_id, 0, -2);
                if (is_numeric($row_id)) {
                    $total++;
                }
            }
        }

        if ($limit && $limit > $total) $limit = $total;
        $l = $limit ? $limit : $total;

        $content = "<h4>Below is a preview of some of the emails to be sent out</h4>
        <h5>Showing " . $l . " Emails of  " . $args["selected"] . "</h5>
        ";
        $email_rows[] = $content;
        $style = "style=\"background-color: #4CAF50; border: none; color: white; padding: 8px 40px; text-align: center; text-decoration: none;
        display: inline-block; font-size: 16px; margin: 4px 2px; cursor: pointer;\"";
        foreach ($rows as $key) {
            if (strpos($key, 'table_row_') !== false) {
                $row_id = str_replace("table_row_", "", $key);
                $row_id = substr($row_id, 0, -2);
                if (is_numeric($row_id)) {
                    if (!empty($args['waiting_list'])) {
                        $student_id = pull_field('core_waitinglist', 'rel_id', "WHERE id ={$row_id}");
                        //overwrite row id
                        $row_id = pull_field("sis_profiles", "id", "WHERE rel_id={$student_id}");
                    }
                    if (!empty($_POST['register_interest_list'])) {
                        $student_id = pull_field('core_course_reg_interest', 'rel_id', "WHERE id ={$row_id}");
                        $row_id = pull_field("sis_profiles", "id", "WHERE rel_id={$student_id}");
                    }
                    $profile_info = pull_field("sis_profiles", "CONCAT(db48566,'|',db48568,'|',db48583,'|',username_id)", "WHERE id='" . $row_id . "'");

                    $profile_info = explode("|", $profile_info);
                    $school_info = $schools->get(array('id' => $_SESSION['usergroup']));

                    $name = $profile_info[0];
                    $surname = $profile_info[1];
                    $email = $profile_info[2];
                    $username_id = $profile_info[3];
                    $message = $args["message"];
                    $message = email_template_replace_values("{{first_name}}", $name, $message);
                    $message = email_template_replace_values("{{name}}", $name, $message);
                    $message = email_template_replace_values("{{surname}}", $surname, $message);
                    $message = email_template_replace_values("{{email}}", $email, $message);

                    $message = $this->email_template_replace_values_from_db($message, $row_id, "sis_profiles");
                    $coms_template_subject_line = $args["subject"];
                    $coms_template_html_version = $message . $reply_html;


                    $div = '<div class="email">
                            <h5 class="right margin-0">Email number ' . $count . '</h5>
                            <h5>To: ' . $email . '</h5>
                            <h5>Subject: ' . $args["subject"] . '</h5>
                            <div>Message<br>
                            ' . $coms_template_html_version . '
                            </div>
                            </div>
                            ';
                    $count++;
                    $email_rows[] = $div;
                }
            }
            if ($limit && $count > $limit) break;
        }

        //print_r($all_html);
        return $email_rows;
    }


    /** ===================================
     * Bulk general Message
     * ====================================    */
    public function send_mail_to_enq($args)
    {
        $schools = new Schools;
        $usergroup=$args['usergroup']??$_SESSION['usergroup'];
        $queue=$args['queue']??false;
        $dbh = get_dbh();
        $profile_info = pull_field("sis_profiles", "CONCAT(IFNULL(db48566,''),'|',IFNULL(db48568,''),'|',IFNULL(db48583,''),'|',username_id,'|',rel_id,'|',rec_id )", "WHERE id=" . $args["enq_id"] . " AND usergroup = $_SESSION[usergroup]");
        $profile_info = explode("|", $profile_info);
        $school_info = $schools->get(array('id' => $_SESSION['usergroup']));

        $name = $profile_info[0];
        $surname = $profile_info[1];
        $email = $profile_info[2];

        $username_id = $profile_info[3];
        $core_student_profile_id = $profile_info[4];
        $form_users_id = $profile_info[5];
        $message = $args["message"];
        $subdomain = pull_field("form_schools", "db985", "WHERE id = $usergroup");
        $url = env('PROTOCOL') . $subdomain . "." . env('APP_URL');
        // Send password reset email link if requested
        if (stripos($message, "[password_reset_link]") !== false) {
            $db1134 = random();
            $db1135 = sanitise($email);// email address
            $db1136 = $_SERVER['REMOTE_ADDR'];///ip address$post_email

            $usr_code = pull_field("form_users", "username_id", "WHERE id ='$form_users_id' and (rec_archive is null or rec_archive= '')");

            // ADD RESET PASSWORD TO TRACKER
            $sth_pass_reset = $dbh->prepare("INSERT INTO form_password_reset (username_id, rec_id, usergroup, rel_id, db1134, db1135, db1136) VALUES (?, ?, ?,?, ?, ?, ?)");
            $sth_pass_reset->execute(array(random(), session_info("uid"), session_info("usergroup"), $form_users_id, $db1134, $db1135, $db1135));
            $reset_password_link = $url . "/resetpassword?code=$db1134&usr=$usr_code";
            $message = email_template_replace_values("[password_reset_link]", "<a href=\"$reset_password_link\">$reset_password_link</a>", $message);
        }

        if (!empty($args['template_tag']) ) {
            $template_tag_array = explode(',', $args['template_tag']);

            if (in_array('53', $template_tag_array) || strpos($message,"submit_reqol.php") !== false) {
                //REQOL

                //rel id has to be the sis_profile
                $profile_core_student_id = pull_field("core_students", "id", "WHERE id = $core_student_profile_id AND usergroup = $_SESSION[usergroup]");
                $reqol_request_args = [
                    'rec_id' => $_SESSION['uid'],
                    'rel_id' => $profile_core_student_id,
                    'username_id' => random(),
                    'usergroup' => $_SESSION['usergroup'],
                    'db205541' => '',
                    'rec_lstup_id'=>$_SESSION['uid'],

                ];
                $stmt=$dbh->prepare("insert into sis_reqol_requests(rec_id,rel_id,username_id,usergroup,rec_lstup_id,rec_lstup,date,db205541) values (:rec_id,:rel_id,:username_id,:usergroup,:rec_lstup_id,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,:db205541)");
                $stmt->execute($reqol_request_args);
                $lastid = $dbh->lastInsertId();
                //get reqol request username id
                $reqol_request_id = pull_field('sis_reqol_requests', 'username_id', "WHERE id =" . $lastid . " AND usergroup='$_SESSION[usergroup]' AND ( rec_archive IS NULL OR rec_archive = '')");

                //REPLACE VALUES
                $message = str_replace("{{FirstName}}", $name, $message);
                $message = str_replace("{{first_name}}", $name, $message);

                $message = str_replace("{{name}}", $name, $message);
                $message = str_replace("{{ref_id}}", $profile_core_student_id, $message);
                $message = str_replace("{{domain_name}}", web_url, $message);
                $message = str_replace("{{view_name}}", $username_id, $message);
                $message = str_replace("{{reqol_request_id}}", $reqol_request_id, $message);
            }

            if (in_array('155', $template_tag_array)) {//Equality and Diversity

                //REPLACE VALUES
                $message = str_replace("{{FirstName}}", $name, $message);
                $message = str_replace("{{name}}", $name, $message);
                $message = str_replace("{{profile}}", $username_id, $message);
                $message = str_replace("{{domain_name}}", web_url, $message);
            }
        }


        $message = str_replace("{{profile}}", $username_id, $message);
        $message = str_replace("{{student}}", $core_student_profile_id, $message);
        $message = str_replace("{{domain_name}}", $url, $message);

        $message = email_template_replace_values("{{first_name}}", $name, $message);
        $message = email_template_replace_values("{{name}}", $name, $message);
        $message = email_template_replace_values("{{surname}}", $surname, $message);
        $message = email_template_replace_values("{{email}}", $email, $message);

        $subject = $args["subject"];
        $subject = email_template_replace_values("{{first_name}}", $name, $subject);
        $subject = email_template_replace_values("{{name}}", $name, $subject);
        $subject = email_template_replace_values("{{surname}}", $surname, $subject);
        $subject = email_template_replace_values("{{email}}", $email, $subject);

        if (!empty($args["template_name"])) {
            $message = $this->email_template_replace_values_from_db($message, $args["enq_id"], "sis_profiles");
        }
        $coms_template_subject_line = $subject;
        $coms_template_plain_text_version = $message;
        $coms_template_html_version = $message;

        $emails = new Emails;

        $email_args = array(
            'to' => $email,
            'subject' => $coms_template_subject_line,
            'text' => $coms_template_plain_text_version,
            'html' => $coms_template_html_version,
            'rel_id' => $profile_info[4],
            'template_id' => $args['template_id'],
            'queue' => $args['queue']
        );

        $emails->send($email_args);

    }

    /*------------------------------
// EMAIL TEMPLATE REPLACEMENT PROCESSORS FROM DB
---------------------------------*/
    function email_template_replace_values_from_db($content, $contact_id = 570, $contact_type = 'sis_profiles', $record_id = '')
    {


        //DEAL WITH RECORD ID
        // explode it to see how it to use what you need
        $record_id = explode("|", $record_id);

        //EXTRACT ALL THE CUSTOM FIELDS IN MESSAGE BODY
        preg_match_all('/\{{(.*?)\}}/', $content, $matches);

        $data = [];
        $arr = $matches[0] ?? [];
        foreach ($arr as $v) {
            $data[] = is_numeric($v) ? $v : "'{$v}'";
        }
        $field_list = implode(',', $data);

        $usergroups = empty($_SESSION['usergroup']) ? "1" : "1, " . $_SESSION['usergroup'];

        //PULL THE CUSTOM FIELDS VALUES FROM DB
        $listed_custom_fields = $field_list ? pull_field("coms_custom_fields", "group_concat(db1318)", "WHERE db1316 IN($field_list) AND usergroup IN ($usergroups)") : '';
        //echo $field_list.'---'.$listed_custom_fields;
        dev_debug($field_list);

        // GET THE REPLACEMENT FIELDS FROM THE DB
        $dbh = get_dbh();
        $sql = "SELECT
			db_field_name,
			type,
			figures,
			db1316 as custom_field,
			db_field_name,
			concat(sys_cat_abv,'_',page_name) as db_table
			FROM system_cat a,system_pages b,system_table c,coms_custom_fields d
			WHERE b.page_id=c.pg_id
			AND a.sys_cat_id = b.project
			AND c.form_id = d.db1318
			AND form_id IN($listed_custom_fields)";
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        dev_debug($sql);
        $usergroup = $_SESSION['usergroup'] ?? 1;
        foreach ($results as $row) {
            $query_contact_id = $contact_id;
            $form_id = $row['form_id'];    //form id
            $table = $row['db_table'];    //table id
            $db_field_name = $row['db_field_name'];    //field name
            $custom_field = $row['custom_field'];    //field name

            $field_type = $row['type'];
            $external_table_data = $row['figures'];

            //REALTIONAL CONNECTION
            //work out which relational info to use

            $relational_field = "id";// $contact_type is a contact

            //END REALTIONAL CONNECTION

            // GET THE CUSTOM VALUE
            //check if there is a unique reference on offer table
            if ($table == "$record_id[0]") {
                $unique_record_id = "AND id='$record_id[1]'";
            } else {
                $unique_record_id = '';// reset
            }
            $where_rel = 'id';

            $session_usergroup = " usergroup = " . $_SESSION["usergroup"] . " AND ";
            //if($_SESSION['uid']=="332"){
            $v .= "table = $table<br/>";
            $v .= "id = $record_id[1]<br/>";
            $v .= "uid = $unique_record_id<br/>";
            $v .= "$table $db_field_name WHERE $where_rel = '$query_contact_id' $unique_record_id<br/><hr/>";
            dev_debug("search replace values=" . $v);

            //}
            //echo $v;

            $custom_field_value = pull_field("$table", "$db_field_name", "WHERE $session_usergroup $where_rel = '$query_contact_id' $unique_record_id");

            // Lookout for foreign based fields and process them differently
            if ($field_type == 'dynamic_list_group') {
                list($default_tables, $default_field) = explode(",", $external_table_data);
                list($default_table) = explode(" ", $default_tables);
                $custom_field_value = pull_field($default_table, $default_field, "WHERE id='$custom_field_value' LIMIT 1");
            }

            if ($field_type == 'date_field') {
                $custom_field_value = format_date("j F Y", $custom_field_value);
            }

            $custom_field = str_replace(array("{{", "}}"), "", $custom_field);// remove curley brackets
            $data[$custom_field] = str_replace("_", " ", $custom_field_value);//clean the tag

        }

        dev_debug(implode("/", $data));

        //SEARCH & REPLACE ALL THE FIELDS
        $pattern = '/{{(.*?)[\|\|.*?]?}}/';

        $replace = preg_replace_callback($pattern, function ($match) use ($data) {
            $match = explode('||', $match[1]);
            return isset($data[$match[0]]) ? $data[$match[0]] : $data[$match[1]];
        }, $content);
        return $replace;
    }


    function get_sis_profile($args)
    {
        $leads_data = array();
        global $db;
        $sql = "SELECT CONCAT(db48566, ' ', db48568) AS 'fullname',
                    id as 'lead_id',
                    username_id,
                    '' as 'coursename'
                    FROM sis_profiles WHERE db48583 LIKE '%$args[email]%' AND usergroup='$_SESSION[usergroup]' AND (rec_archive IS NULL OR rec_archive = '')";
        $leads_data = $db->query($sql);
        return $leads_data;
    }

    function get_enq_pref()
    {
        $enq_pref = pull_field("lead_preferences", "db40625", "WHERE usergroup = $_SESSION[usergroup]");

        return $enq_pref;
    }

    function get_profile_data($profile_id, $vw)
    {
        $dbh = get_dbh();
        /***now get profiles data***/
        /*(SELECT db49848 FROM lead_mrn_stages WHERE id = db48581) AS 'status',	*/
        $studentquery = "SELECT db48583 as 'email',
            db48566 as 'first_name',
            db48567 as 'middle_name',
            db48568 as 'surname',
            db48571 as 'telephone',
            db48597 as 'mobile',
            IFNULL(db233984,'') as 'admin_notes',
            db27 as 'usergroup_name',
            sis_profiles.id as 'id',
            sis_profiles.rel_id as 'core_student_id',
            sis_profiles.username_id,
			sis_profiles.`date` AS 'date_created',
			db48617 as 'potential_als',
			db48612 as 'als_status',						
			db48581  AS 'status',
			(select db49848 from lead_mrn_stages where id = db48581 ) as 'status_name',
			sis_profiles.rec_archive,					
			core_courses.db232 AS 'course_interested',
            db211247 as has_email,
            db252134 as 'enroled_on_behalf',
            db48572 as dob,
            db48599 as 'ug_specific_student_id',
            db48609 as 'primary role',
            IF(db48609 IS NULL or db48609 = '', '', (SELECT db48829 FROM sis_primary_groups  WHERE id = db48609)) as 'primary_role_name'
            FROM sis_profiles
            LEFT JOIN core_courses on core_courses.id = sis_profiles.db48593
            LEFT JOIN form_schools on form_schools.id = sis_profiles.usergroup                        
            WHERE sis_profiles.id = '{$profile_id}'  
            AND sis_profiles.username_id='{$vw}' 
            AND sis_profiles.usergroup= '{$_SESSION['usergroup']}'
        ";
        dev_debug($studentquery);
//        $studentdata=$db->query($studentquery);
        $stmt = $dbh->prepare($studentquery);
        $stmt->execute();
        $studentdata = $stmt->fetchObject();

        $date_created = $studentdata->date_created;
        $timezone_name = get_timezone_name();
        $preferred_date_format = get_preferred_date_format();
        $preferred_date_time_format = $preferred_date_format . ' H:i';
        $date_created = convert_datetime_to_timezone($date_created, $timezone_name, $preferred_date_time_format);
        $studentdata->date_created = $date_created;
        $dob_date = date_create($studentdata->dob);
        $studentdata->dob = date_format($dob_date, $preferred_date_format);
//        print_r($studentdata);
//        echo "<pre>".$studentdata->email."</pre>";
//        exit();
        return $studentdata;


    }

    function get_statuses_in_type_for_status($status_id)
    {
        $dbh = get_dbh();

        $sql = "SELECT id, db49848 as 'title' from lead_mrn_stages WHERE (rec_archive IS NULL OR rec_archive = '') AND db49851 = (SELECT db49851 FROM lead_mrn_stages WHERE id = $status_id )";
        dev_debug($sql);
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(2);

    }

    function get_profile_answers($profile_id, $school_id)
    {
        $dbh = get_dbh();
        $form_cms = new FrontendPages;
        $pages_args = array("school_id" => $school_id);
        $pages = $form_cms->get($pages_args);
        $form_templates = new FormTemplates;
        $form_answers = array();
        ///now get the profile answers
        $profilequery = "SELECT * from sis_profiles where id=" . $profile_id;
        $stmt = $dbh->prepare($profilequery);
        $stmt->execute();
        $profiledata = $stmt->fetchAll();
        foreach ($profiledata as $row) {
//            if($row['db48574']){
//                $source_of_referral = $row['db48574'];
//                $row['db48574'] = pull_field("core_source_of_referrals","db48821", "WHERE id = $source_of_referral");
//            }
            if ($row['db48593']) {
                $course_id1 = $row['db48593'];
                $row['db48593'] = pull_field("core_courses", "db232", "WHERE id = $course_id1");
            }
            if ($row['db48594']) {
                $course_id2 = $row['db48594'];
                $row['db48594'] = pull_field("core_courses", "db232", "WHERE id = $course_id2");
            }
            if ($row['db48592']) {
                $course_id3 = $row['db48592'];
                $row['db48592'] = pull_field("core_courses", "db232", "WHERE id = $course_id3");
            }
            if ($row['db66730']) {
                $course_id4 = $row['db66730'];
                $row['db66730'] = pull_field("core_courses", "db232", "WHERE id = $course_id4");
            }
            if ($row['db66732']) {
                $course_id5 = $row['db66732'];
                $row['db66732'] = pull_field("core_courses", "db232", "WHERE id = $course_id5");
            }
            if ($row['db66734']) {
                $course_id6 = $row['db66734'];
                $row['db66734'] = pull_field("core_courses", "db232", "WHERE id = $course_id6");
            }

            if ($row['db48649']) {
                $scourse_id1 = $row['db48649'];
                $row['db48649'] = pull_field("sis_course_schedule", "CONCAT(DATE_FORMAT(db14947,'%d/%m/%Y'),' ', CONCAT(SUBSTRING(db14948,1,2),':',SUBSTRING(db14948,3,2)))", "WHERE id = $scourse_id1");
            }
            if ($row['db48650']) {
                $scourse_id2 = $row['db48650'];
                $row['db48650'] = pull_field("sis_course_schedule", "CONCAT(DATE_FORMAT(db14947,'%d/%m/%Y'),' ', CONCAT(SUBSTRING(db14948,1,2),':',SUBSTRING(db14948,3,2)))", "WHERE id = $scourse_id2");
            }
            if ($row['db48651']) {
                $scourse_id3 = $row['db48651'];
                $row['db48651'] = pull_field("sis_course_schedule", "CONCAT(DATE_FORMAT(db14947,'%d/%m/%Y'),' ', CONCAT(SUBSTRING(db14948,1,2),':',SUBSTRING(db14948,3,2)))", "WHERE id = $scourse_id3");
            }
            if ($row['db66731']) {
                $scourse_id4 = $row['db66731'];
                $row['db66731'] = pull_field("sis_course_schedule", "CONCAT(DATE_FORMAT(db14947,'%d/%m/%Y'),' ', CONCAT(SUBSTRING(db14948,1,2),':',SUBSTRING(db14948,3,2)))", "WHERE id = $scourse_id4");
            }
            if ($row['db66733']) {
                $scourse_id5 = $row['db66733'];
                $row['db66733'] = pull_field("sis_course_schedule", "CONCAT(DATE_FORMAT(db14947,'%d/%m/%Y'),' ', CONCAT(SUBSTRING(db14948,1,2),':',SUBSTRING(db14948,3,2)))", "WHERE id = $scourse_id5");
            }
            if ($row['db66735']) {
                $scourse_id6 = $row['db66735'];
                $row['db66735'] = pull_field("sis_course_schedule", "CONCAT(DATE_FORMAT(db14947,'%d/%m/%Y'),' ', CONCAT(SUBSTRING(db14948,1,2),':',SUBSTRING(db14948,3,2)))", "WHERE id = $scourse_id6");
            }


            if (!empty($row['db48609'])) {
                $row['db48609'] = pull_field('sis_primary_groups', 'db48829', " WHERE id = {$row['db48609']}");
            }

            if (!empty($row['db48610'])) {
                $row['db48610'] = pull_field('sis_student_groups', 'db66789', " WHERE id in ({$row['db48610']})");
            }

            if (!empty($row['db48601'])) {
                $row['db48601'] = pull_field('sis_course_geog_coverage', 'db48826', " WHERE id in ({$row['db48601']})");
            }

            if (!empty($row['db48600'])) {
                $row['db48600'] = pull_field('sis_course_geog_coverage', 'db48826', " WHERE id in ({$row['db48600']})");
            }

            if (!empty($row['db236771'])) {
                $row['db236771'] = pull_field('sis_course_geog_area', 'db48828', " WHERE id in ({$row['db236771']})");
            }

            if (!empty($row['db48643'])) {
                if ($row['db48643'] == 'yes' || $row['db48643'] == 'no' || $row['db48643'] == 'prefer_not_to_say') {
                    // do nothing
                } else {
                    $row['db48643'] = pull_field('core_disabilities', 'db16998', " WHERE id in ({$row['db48643']})");
                }
            }
            if (!empty($row['db48591'])) {
                $row['db48591'] = pull_field('core_faith_affiliations', 'db48824', " WHERE id in ({$row['db48591']})");
            }

            if (!empty($row['db48604']) && is_numeric($row['db48604'])) {
                $row['db48604'] = pull_field('core_ethnicity', 'db17001', " WHERE id in ({$row['db48604']})");
            }
            if (!empty($row['db48653'])) {
                $row['db48653'] = pull_field('sis_employment_status', 'db48833', " WHERE id in ({$row['db48653']})");
            }

            if (!empty(is_numeric($row['db48574']))) {
                $row['db48574'] = pull_field('core_hear_about_us', 'db16997', " WHERE id in ({$row['db48574']})");
            }

            //dob
            $preferred_date_format = get_preferred_date_format();
            $dob_date = date_create($row['db48572']);
            $row['db48572'] = date_format($dob_date, $preferred_date_format);

            foreach ($pages as $cms_page) {
                if ($cms_page['new_form']) {

                    //Get the custom form
                    $fields_args = array(
                        "form_id" => $cms_page['new_form'],
                        "school_id" => $school_id,
                    );
                    $fields_list = $form_templates->get_custom_form_fields($fields_args);
                    foreach ($fields_list as $field) {
                        if ($field['db_field_name']) {
                            $form_answers[$field['db_field_name']] = $row[$field['db_field_name']];
                        }
                    }
                }
            }
        }
//        echo "answers are=";
//        print_r($form_answers);
//        exit();
        return $form_answers;
    }

    function getOptions($pg_id, $db_field)
    {
        $dbh = get_dbh();
        $field = !empty(pull_field("system_table", "CONCAT(figures, '||||', type)", " WHERE pg_id='{$pg_id}' AND db_field_name='{$db_field}' AND usergroup={$_SESSION['usergroup']}")) ?
            pull_field("system_table", "CONCAT(figures, '||||', type)", " WHERE pg_id='{$pg_id}' AND db_field_name='{$db_field}' AND usergroup={$_SESSION['usergroup']}") :
            pull_field("system_table", "CONCAT(figures, '||||', type)", " WHERE pg_id='{$pg_id}' AND db_field_name='{$db_field}'");
        list($sql, $field_type) = explode("||||", $field);

        if (preg_match_search($sql, '[SESSION]')) {
            $sql = session_floating($sql);
        }
        list($from_sql, $field_list) = explode(",", $sql);
        if (preg_match_search($field_list, "+")) {
            $fields = str_replace('+', ', ', $field_list);
        } else {
            $fields = "id, " . $field_list;
        }

        if ($field_type === "dynamic_list_group" || $field_type === "linked_field_ug") {
            if (preg_match_search($from_sql, "WHERE")) {
                $order_pos = stripos($from_sql, 'ORDER');
                if ($order_pos !== false) {
                    $order_sql = substr($from_sql, $order_pos);
                    $from_sql = substr($from_sql, 0, $order_pos - 1);
                    $from_sql .= " AND usergroup = {$_SESSION['usergroup']} $order_sql";
                } else {
                    $from_sql .= " AND usergroup = {$_SESSION['usergroup']}";
                }
            } else {
                $from_sql .= " WHERE usergroup = {$_SESSION['usergroup']}";
            }
        }

        $sql = " SELECT $fields as title FROM $from_sql";
        dev_debug($sql);
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(2);
    }

    function getSections($parent_page, $section = 'tabs', $group_category = false)
    {
        global $school_info;
        $modules = $school_info['module_ids'];
        $dbh = get_dbh();
        if ($group_category) {
            $group_category_list = "AND db18717 = '$group_category' ";
        }
        if ($_SESSION['ulevel'] == 9) {
            $sth = $dbh->prepare("SELECT form_create_view.id as idss,db40638,db,db1,db193,db190,db4,db5,db82,db282,db284,db298,form_submodules.id,form_submodules.db766
                    FROM form_create_view LEFT JOIN form_submodules
                    ON form_create_view.id=form_submodules.db327
                    WHERE db193=?
                    AND db82='yes'
                    AND form_submodules.id IN ($modules)
                    $group_category_list
                    order by db259 ASC");
            $sth->execute(array($parent_page));
        } else {
            $order_by = pull_field("lead_preferences", "db250658", "WHERE usergroup=" . $_SESSION['usergroup']);
            if (!empty($order_by)) {
                $order_by_sql = "FIELD(form_create_view.id,$order_by)";
            } else {
                $order_by_sql = "CAST(db259 AS SIGNED)";
            }
            $sth = $dbh->prepare("SELECT form_create_view.id as idss,db40638,db,db1,db193,db190,db4,db5,db82,db282,db284,db298,form_submodules.id,form_submodules.db766
                    FROM form_create_view LEFT JOIN form_submodules
                    ON form_create_view.id=form_submodules.db327
                    WHERE db193=?
                    AND db82='yes'
                    AND form_submodules.id IN ($modules)
                    $group_category_list
                    AND (?<=form_submodules.db766 OR FIND_IN_SET(?,db278000))
                    order by $order_by_sql ASC");
            $sth->execute(array($parent_page, $_SESSION['ulevel'], $_SESSION['ulevel']));
        }
        return $sth->fetchAll(2);
    }

    function convert_student($profile_id, $conversion_status, $core_student_id, $form_user_id = "", $login_pref = "", $send_invite = true)
    {
        $dbh = get_dbh();

        if (empty ($login_pref)) {
            $login_pref = pull_field("lead_preferences", "db66413", "WHERE usergroup=" . $_SESSION['usergroup']);
        }
        if (empty($login_pref)) {
            $login_pref = 'no';
        }

        $invite_time_pref = pull_field("lead_preferences", "db210182", "WHERE usergroup=" . $_SESSION['usergroup']);
        if (empty($invite_time_pref)) {
            $invite_time_pref = 'student';
        }
        $supporting_person = pull_field("sis_profiles", "db252134", "WHERE id=$profile_id AND usergroup =" . $_SESSION['usergroup']);
        if (empty($supporting_person)) {
            $enroled_on_behalf = 'no';
        } else {
            $enroled_on_behalf = $supporting_person;
        }
        ###first of all add info the conversion tracker, sis_profile_convert
        $sth = $dbh->prepare("INSERT INTO sis_profile_convert (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id, db63026) VALUES (?,?,?,?,?,?,?)");
        if ($sth->execute(array(random(), session_info("uid"), session_info("usergroup"), $core_student_id, custom_date_and_time(), session_info("uid"), $conversion_status))) {
            #####second update the profile
            $sql = "UPDATE sis_profiles set db48581='$conversion_status' WHERE id='$profile_id'";
            dev_debug($sql);
            $stmt = $dbh->prepare($sql);
            if ($stmt->execute()) {
                if ($login_pref == 'yes' && $enroled_on_behalf == 'no') {
                    if (($conversion_status == 10 && $invite_time_pref == 'student') || ($conversion_status == 5 && $invite_time_pref == 'applicant')) {
                        if (empty($form_users_id)) {
                            $form_user_id = pull_field("sis_profiles", "rec_id", "WHERE id=$profile_id AND usergroup =" . $_SESSION['usergroup']);
                        }
                        $user_level = pull_field("form_users","db112","WHERE id = $form_user_id AND usergroup=$_SESSION[usergroup]");
                        if ($user_level == '2' || $user_level == '3') {
                            $email_address = pull_field("sis_profiles","db48583","WHERE id = $profile_id AND usergroup=$_SESSION[usergroup] and (rec_archive IS NULL OR rec_archive = '')");
                            //this is admin, need to get the student user record
                            $form_user_id = pull_field("form_users", "id", "WHERE db119='$email_address' AND usergroup=$_SESSION[usergroup] and  (rec_archive IS NULL OR rec_archive = '')");
                        }

                        $access_to_online_learning_resource = pull_field("lead_preferences","db359733","WHERE usergroup = $_SESSION[usergroup] AND (rec_archive is null OR rec_archive = '')");
                        if (($access_to_online_learning_resource) == 'yes') {
                            //add an entry to the online learning access plan
                            $access_plan_id = pull_field ("ols_access_plans","id","WHERE usergroup=$_SESSION[usergroup] AND (rec_archive is null OR rec_archive = '') AND db21877 = '2'");
                            if (!(empty($access_plan_id))) {
                                $sth = $dbh->prepare("INSERT INTO ols_user_access_plan (username_id, rec_id, usergroup, rel_id, db22019, db22031, db22049, db22056, db22057, db22058) VALUES (?,?,?,?,?,?,?,?,?,?)");
                                $sth->execute(array(random(), $form_user_id, session_info("usergroup"), $core_student_id, $access_plan_id,'2','','Valid','Valid','Valid'));

                            }
                        }

                        
                        #####third id convert to student and allow login
                        #####update suspend on form_user and send out welcome email
                        if (!empty($form_user_id)) {
                            $sql = "UPDATE form_users SET db307 = 'no' WHERE id = $form_user_id AND usergroup = $_SESSION[usergroup]";
                            dev_debug($sql);
                            $stmt = $dbh->prepare($sql);
                            if ($stmt->execute()) {
                                if ($send_invite) {
                                    $this->send_welcome_and_password_reset_emails($profile_id, $core_student_id, $form_user_id);
                                }
                                return true;
                            } else {
                                return false;
                            }
                        }
                    }
                }
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    function send_welcome_and_password_reset_emails($profile_id, $core_student_id, $form_users_id)
    {
        global $db;
        $dbh = get_dbh();
        global $server_domain_name;
        global $front_website_url;

        $email_add = pull_field("form_schools", "db1117", "WHERE id='$_SESSION[usergroup]'");//Main International Email Address
        if (!$email_add) {
            $master_email = master_email;
            $schools_institution_name = "$email_add";
        } else {
            $schools_institution_name = "$_SESSION[school_name]";
            $master_email = master_email; // use support email address TEMPORARY OVERRIDE
        }


        $chck_sql = "SELECT * FROM sis_profiles WHERE id = {$profile_id}";
        dev_debug($chck_sql);
        $sth = $dbh->prepare($chck_sql);
        $sth->execute();
        $lead_profile = $sth->fetch();
        if (empty($lead_profile)) {
            die('<div class="alert alert-danger">SIS profile with this does not exist. This should not happen.</div>');
        }


        // Send password reset email
        $db1134 = random();
        $db1135 = sanitise($lead_profile['db48583']); // email address
        $db1136 = $_SERVER['REMOTE_ADDR']; ///ip address$post_email


        // ADD RESET PASSWORD TO TRACKER
        $sth_pass_reset = $dbh->prepare("INSERT INTO form_password_reset (username_id, rec_id, usergroup, rel_id, db1134, db1135, db1136) VALUES (?, ?, ?,?, ?, ?, ?)");
        $sth_pass_reset->execute(array(random(), session_info("uid"), session_info("usergroup"), $core_student_id, $db1134, $db1135, $db1135));

        //send welcome email
        $select_email_template = pull_field("coms_template", "id", "WHERE usergroup='" . session_info('usergroup') . "' AND (db1320='invite_from_eoi' OR db1147='11') ");
        //select default
        if (!$select_email_template) {
            $select_email_template = pull_field("coms_template", "id", "WHERE (db1320='invite_from_eoi' OR db1147='11') AND usergroup='1'");
        }

        $usr_code = pull_field("form_users", "username_id", "WHERE id ={$form_users_id}");

        // FUNCTION TO GET_COMS_TEMPLATE
        list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template($select_email_template);


        /********************************** SETTINGS *******************************/
        $domain = $_SERVER['HTTP_HOST'];
        $reset_password_link = (env('PROTOCOL') ?? "https://") . $domain . "/resetpassword?code=$db1134&usr=$usr_code";
        $host_url = $front_website_url . '/portal';

        /********************************** SETTINGS END *******************************/
        // IF INVITE IS GIFT.. RUN THIS
        $coms_template_subject_line = email_template_replace_values("{{institution}}", "$schools_institution_name", $coms_template_subject_line);
        $coms_template_html_version = email_template_replace_values("{{name}}", "{$lead_profile['db48566']}", $coms_template_html_version);
        $coms_template_html_version = email_template_replace_values("{{first_name}}", "{$lead_profile['db48566']}", $coms_template_html_version);
        $coms_template_plain_text_version = email_template_replace_values("{{name}}", "{$lead_profile['db48566']}", $coms_template_plain_text_version);
        $coms_template_plain_text_version = email_template_replace_values("{{first_name}}", "{$lead_profile['db48566']}", $coms_template_plain_text_version);
        $coms_template_html_version = email_template_replace_values("{{organisation_name}}", "$schools_institution_name", $coms_template_html_version);
        $coms_template_plain_text_version = email_template_replace_values("{{organisation_name}}", "$schools_institution_name", $coms_template_plain_text_version);
        $coms_template_html_version = email_template_replace_values("[password_reset_link]", "<a href=\"$reset_password_link\">$reset_password_link</a>", $coms_template_html_version);
        $coms_template_plain_text_version = email_template_replace_values("[password_reset_link]", "<a href=\"$reset_password_link\">$reset_password_link</a>", $coms_template_plain_text_version);
        $coms_template_html_version = email_template_replace_values("[domain]", "$_SESSION[domain]", $coms_template_html_version);
        $coms_template_html_version = email_template_replace_values("{{domain}}", "$_SESSION[domain]", $coms_template_html_version);
        $coms_template_plain_text_version = email_template_replace_values("[domain]", "$_SESSION[domain]", $coms_template_plain_text_version);
        $coms_template_html_version = email_template_replace_values("[subdomain]", "$_SESSION[subdomain]", $coms_template_html_version);
        $coms_template_plain_text_version = email_template_replace_values("[subdomain]", "$_SESSION[subdomain]", $coms_template_plain_text_version);
        $coms_template_html_version = email_template_replace_values("{{organisation_email}}", "$master_email", $coms_template_html_version);
        $coms_template_plain_text_version = email_template_replace_values("{{organisation_email}}", "$master_email", $coms_template_plain_text_version);
        $coms_template_html_version = email_template_replace_values("{{student_portal}}", "<a href=\"$host_url\">student portal</a>", $coms_template_html_version);
        $coms_template_plain_text_version = email_template_replace_values("{{student_portal}}", "<a href=\"$host_url\">student portal</a>", $coms_template_plain_text_version);

        //check if there is also a reqol request
        if (stripos($coms_template_html_version,"submit_reqol.php") !== false ) {

//rel id has to be the sis_profile
            $reqol_request_args = [
                'rec_id' => $_SESSION['uid'],
                'rel_id' => $core_student_id,
                'username_id' => random(),
                'usergroup' => $_SESSION['usergroup'],
                'db205541' => ''

            ];
            $db->insert('sis_reqol_requests', $reqol_request_args);
            $lastid = $db->lastInsertId();
            //get reqol request username id
            $reqol_request_id = pull_field('sis_reqol_requests', 'username_id', "WHERE id =" . $lastid . " AND usergroup='$_SESSION[usergroup]' AND ( rec_archive IS NULL OR rec_archive = '')");

            //REPLACE VALUES
            $coms_template_html_version = email_template_replace_values("{{domain_name}}", "$_SESSION[domain]", $coms_template_html_version);
            $coms_template_plain_text_version = email_template_replace_values("{{domain_name}}", "$_SESSION[domain]", $coms_template_plain_text_version);
            $coms_template_html_version = email_template_replace_values("{{ref_id}}", "$core_student_id", $coms_template_html_version);
            $coms_template_plain_text_version = email_template_replace_values("{{ref_id}}", "$core_student_id", $coms_template_plain_text_version);
            $coms_template_html_version = email_template_replace_values("{{view_name}}", "$lead_profile[username_id]", $coms_template_html_version);
            $coms_template_plain_text_version = email_template_replace_values("{{view_name}}", "$lead_profile[username_id]", $coms_template_plain_text_version);
            $coms_template_html_version = email_template_replace_values("{{reqol_request_id}}", "$reqol_request_id", $coms_template_html_version);
            $coms_template_plain_text_version = email_template_replace_values("{{reqol_request_id}}", "$reqol_request_id", $coms_template_plain_text_version);

        }
        

        //Align content
        $message_bodytxtonly = $coms_template_plain_text_version;
        $message_body_html = $coms_template_html_version;

        $db1077 = $db1135;
        $db1079 = '';
        $db1080 = '';
        $db1081 = $message_bodytxtonly;
        $db1089 = 'eoi';
        $db1091 = '';
        $sth_tracker = $dbh->prepare("INSERT INTO form_invite_tracker (username_id, rec_id, usergroup, rel_id, db1077, db1078, db1079, db1080, db1081, db1089, db1091) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?)");
        if (!$sth_tracker->execute(array(random(), session_info("uid"), $_SESSION['usergroup'], $core_student_id, $db1077, date('Y-m-d H:i:s'), $db1079, $db1080, $db1081, $db1089, $db1091))) {
            //print_r($sth_tracker->errorInfo());
            die('<div class="alert alert-warning">Sorry  tracking failed. Email as not sent</div>');
        }

        $email_add = $lead_profile['db48583'];
        $emailTo = $email_add;
        $emailFrom = master_email;
        $subject = addslashes("$coms_template_subject_line");

        $message_html = $message_plain = '';
        $message_html .= "$message_body_html";

        $message_plain .= "
            $message_bodytxtonly";

        $message_plain = email_template_replace_values_from_db($message_plain, $core_student_id, "applicant");
        $message_html = email_template_replace_values_from_db($message_html, $core_student_id, "applicant");

        $email_args = array(
            'to' => $email_add,
            'subject' => $coms_template_subject_line,
            'text' => $coms_template_plain_text_version,
            'html' => $coms_template_html_version,
            'category' => "coms_msg_alert",
            'recipient_id' => $core_student_id,
            'template_id' => $select_email_template,
        );


        $emails = new Emails;
        $emails->send($email_args);

        //log_email($emailTo, $subject, $message_plain, $message_html, $emailFrom, "Password Reset From EOI");
    }

    function get_profile_summary($profile_status, $profile_id)
    {
        $dbh = get_dbh();
        $usergroup = $_SESSION['usergroup'];
        //if ($profile_status >= 10) {
        /*Latest ILP score
        Latest WB Category
        Number of days since last ILP
        Number of days since end of last attended course
        Number of Course Bookings (total)
        Number of Attended Courses*/
        //latest ILP score
        $latest_ilp_score = pull_field("sis_ind_learner_plan", "((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61539) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61540) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61543) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61545) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61547) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61548) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61549) ) ", "WHERE rel_id = $profile_id AND (rec_archive IS NULL OR rec_archive = '') ORDER BY db61500 DESC LIMIT 1");

        //latest_WB_category
        $latest_WB_category = pull_field("sis_ind_learner_plan", "CASE WHEN (
            CASE WHEN (
                (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61539 IS NOT NULL AND db61539 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61540 IS NOT NULL AND db61540 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61543 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61537 IS NOT NULL AND db61543 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61545 IS NOT NULL AND db61545 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61547 IS NOT NULL AND db61547 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61548 IS NOT NULL AND db61548 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61549 IS NOT NULL AND db61549 !='not specified') THEN 1 ELSE 0 END) = 14) THEN
            
            ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61539) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61540) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61543) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61545) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61547) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61548) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61549) ) ELSE
            FORMAT(
                ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61539) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61540) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61543) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61545) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61547) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61548) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61549) ) /
            (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61539 IS NOT NULL AND db61539 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61540 IS NOT NULL AND db61540 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61543 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61537 IS NOT NULL AND db61543 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61545 IS NOT NULL AND db61545 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61547 IS NOT NULL AND db61547 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61548 IS NOT NULL AND db61548 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61549 IS NOT NULL AND db61549 !='not specified') THEN 1 ELSE 0 END)*14 ,0) END
            >59) THEN 'High' ELSE (CASE WHEN (
            case WHEN (
                (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61539 IS NOT NULL AND db61539 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61540 IS NOT NULL AND db61540 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61543 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61537 IS NOT NULL AND db61543 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61545 IS NOT NULL AND db61545 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61547 IS NOT NULL AND db61547 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61548 IS NOT NULL AND db61548 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61549 IS NOT NULL AND db61549 !='not specified') THEN 1 ELSE 0 END) = 14) THEN
            
            ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61539) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61540) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61543) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61545) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61547) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61548) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61549) ) ELSE
            FORMAT(
                ((SELECT db63243 FROM sis_ilp_scoring where db63242 = db61536) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61537) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61538) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61539) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61540) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61541) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61542) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61543) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61544) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61545) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61546) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61547) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61548) +
            (SELECT db63243 FROM sis_ilp_scoring where db63242 = db61549) ) /
            (CASE WHEN (db61536 IS NOT NULL AND db61536 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61537 IS NOT NULL AND db61537 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61538 IS NOT NULL AND db61538 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61539 IS NOT NULL AND db61539 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61540 IS NOT NULL AND db61540 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61541 IS NOT NULL AND db61541 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61543 IS NOT NULL AND db61542 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61537 IS NOT NULL AND db61543 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61544 IS NOT NULL AND db61544 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61545 IS NOT NULL AND db61545 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61546 IS NOT NULL AND db61546 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61547 IS NOT NULL AND db61547 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61548 IS NOT NULL AND db61548 !='not specified') THEN 1 ELSE 0 END +
            CASE WHEN (db61549 IS NOT NULL AND db61549 !='not specified') THEN 1 ELSE 0 END)*14 ,0) END
            <40) THEN 'Low' ELSE 'Average' END) END", "WHERE rel_id = $profile_id AND (rec_archive IS NULL OR rec_archive = '') ORDER BY db61500 DESC LIMIT 1");

        //Number of days since last ILP
        $no_days_since_last_ilp = pull_field("sis_ind_learner_plan", "datediff(now(), db61500)", "WHERE rel_id = $profile_id AND (rec_archive IS NULL OR rec_archive = '') ORDER BY db61500 DESC LIMIT 1");

        /* }else{
             $no_days_since_last_ilp = 0;
             $latest_WB_category = 0;
             $latest_ilp_score =0;
         }*/

        $no_days_since_end_of_last_attended_course = pull_field("core_students 
JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
JOIN sis_course_schedule ON sis_course_schedule.id = db14977", "datediff(date(now()), db14949)",
            "WHERE core_students.rel_id = $profile_id
        AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') 
 AND (db64664 = 1 OR db64664 = 2) 
ORDER BY db14949 DESC LIMIT 1");

        $no_course_bookings = pull_field("core_students 
JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
JOIN sis_course_schedule ON sis_course_schedule.id = db14977", "count(sis_sched_booking_detail.id)",
            "WHERE core_students.rel_id = $profile_id AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (db59978 = 2 OR db59978 = 3) AND db14978 >= date(now())");

        $no_attended_courses = pull_field("core_students 
JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
JOIN sis_course_schedule ON sis_course_schedule.id = db14977", "count(*)", "WHERE core_students.rel_id = $profile_id AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (db64664 = 1 OR db64664 = 2)");

        $query = "SELECT if((SELECT count(*) from sis_ind_learner_plan WHERE sis_ind_learner_plan.rel_id = lead_profiles.id)>0,'yes','no') as 'initial_ILP_complete' FROM lead_profiles WHERE id=$candidate_id";
        $sth = $dbh->prepare($query);
        $sth->execute();

        $initial_ILP_complete = pull_field("sis_ind_learner_plan", "if(count(*)>0,'yes','no')", "WHERE sis_ind_learner_plan.rel_id = $profile_id AND (sis_ind_learner_plan.rec_archive IS NULL OR sis_ind_learner_plan.rec_archive = '')");


        $attended_induction_course = pull_field("core_students 
JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
JOIN sis_course_schedule ON sis_course_schedule.id = db14977 
JOIN core_courses ON core_courses.id = CAST(db14946 AS UNSIGNED)", "IF(count(sis_sched_booking_detail.id) > 0,'yes','no')",
            "WHERE core_students.rel_id = $profile_id AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (db64664 = 1 OR db64664 = 2) AND db66775 = 'yes'");

        return array(
            'no_days_since_last_ilp' => $no_days_since_last_ilp,
            'latest_WB_category' => $latest_WB_category,
            'latest_ilp_score' => $latest_ilp_score,
            'no_days_since_end_of_last_attended_course' => $no_days_since_end_of_last_attended_course,
            'no_course_bookings' => $no_course_bookings,
            'no_attended_courses' => $no_attended_courses,
            'initial_ILP_complete' => $initial_ILP_complete,
            'attended_induction_course' => $attended_induction_course,
        );
    }

    public function update_stage($args)
    {


        $this->update_profile_status($args['student_id'], $args['status_id']);

        $sql = "UPDATE sis_profiles SET db48581= :db48581, rec_lstup=CURRENT_TIMESTAMP,rec_lstup_id = {$_SESSION['uid']} WHERE id = :id AND usergroup = :usergroup ";
        $dbh = get_dbh();
        $st = $dbh->prepare($sql);
        $st->execute(
            [
                ':db48581' => $args['status_id'],
                ':id' => $args['student_id'],
                ':usergroup' => $_SESSION['usergroup']
            ]
        );


    }

    public function reqol_request($args)
    {
        $emails = new Emails;
        global $db;

        $reqol_request_args = [
            'rec_id' => $_SESSION['uid'],
            'rel_id' => $args['profile_core_student_id'],
            'username_id' => random(),
            'usergroup' => $_SESSION['usergroup']

        ];

        $id = $db->insert('sis_reqol_requests', $reqol_request_args);
        $lastid = $db->lastInsertId();

        //send email 
        $select_email_template = pull_field("coms_template", "id", "WHERE db1147='53' AND usergroup='$_SESSION[usergroup]' AND (rec_archive is NULL or rec_archive = '')");

        //default template if not added
        if (empty($select_email_template)) {
            $select_email_template = pull_field("coms_template", "id", "WHERE db1147='53' AND usergroup='1' AND (rec_archive is NULL or rec_archive = '')");
        }

        //get template
        $qry = "SELECT db1085 as template, db1086 as subject FROM coms_template WHERE id='$select_email_template'";
        $email_template = $db->rawQueryOne($qry);


        //get reqol request username id
        $reqol_request_id = pull_field('sis_reqol_requests', 'username_id', "WHERE id =" . $lastid . " AND usergroup='$_SESSION[usergroup]' AND ( rec_archive IS NULL OR rec_archive = '')");

        //REPLACE VALUES
        $email_template['template'] = email_template_replace_values("{{FirstName}}", $args['first_name'], $email_template['template']);
        $email_template['template'] = email_template_replace_values("{{first_name}}", $args['first_name'], $email_template['template']);

        $email_template['template'] = email_template_replace_values("{{name}}", $args['first_name'], $email_template['template']);
        $email_template['template'] = email_template_replace_values("{{ref_id}}", $args['profile_core_student_id'], $email_template['template']);
        $email_template['template'] = email_template_replace_values("{{domain_name}}", web_url, $email_template['template']);
        $email_template['template'] = email_template_replace_values("{{view_name}}", $args['username_id'], $email_template['template']);
        $email_template['template'] = email_template_replace_values("{{reqol_request_id}}", $reqol_request_id, $email_template['template']);

        $email_args = array(
            'to' => $args['email_address'],
            'subject' => $email_template['subject'],
            'text' => $email_template['template'],
            'html' => $email_template['template'],
            'recipient_id' => $args['profile_core_student_id'],
            'template_id' => $select_email_template,
        );

        $emails->send($email_args);

    }


    public function send_letter($args)
    {
        $dbh=get_dbh();
        $usergroup=$args['usergroup']??$_SESSION['usergroup'];
        $letter_templates = new LetterTemplates;
        //get letter 
        $template_args = [
            'id' => $args['letter_id']
        ];
        $letter = $letter_templates->get_templates($template_args);

        $letter_args = [
            'rec_id' => $_SESSION['uid'],
            'rel_id' => $args['student_id'],
            'username_id' => random(),
            'usergroup' => $_SESSION['usergroup'],
            'db31870' => $letter['content'],
            'db20301' => $letter['title'],
            'date'=>date('Y-m-d H:i:s'),
            'rec_lstup'=>date('Y-m-d H:i:s'),
            'rec_lstup_id'=> $_SESSION['uid'],
        ];
        $stmt = $dbh->prepare("insert into dir_letters_sent(rec_id,rel_id,username_id,usergroup,date,rec_lstup_id,rec_lstup,db31870,db20301) values (:rec_id,:rel_id,:username_id,:usergroup,:date,:rec_lstup_id,:rec_lstup,:db31870,:db20301) ");
        $stmt->execute($letter_args);
        $lastid = $dbh->lastInsertId();
        $subdomain = pull_field("form_schools", "db985", "WHERE id = $usergroup");
        $url = env('PROTOCOL') . $subdomain . "." . env('APP_URL');
        /// after insert update link
        $http_link = $url."/engine/modules/inc_letters_sent.php?&rec=$lastid&pdf=1";

        $sql = "UPDATE dir_letters_sent SET db20300= :db20300 WHERE id=:id AND usergroup=:usergroup ";
        dev_debug($sql);
        $dbh = get_dbh();
        $st = $dbh->prepare($sql);
        $st->execute(
            [
                ':db20300' => $http_link,
                ':id' => $lastid,
                ':usergroup' => $_SESSION['usergroup']
            ]
        );

    }

    public function unarchive_record($args)
    {
        $dbh = get_dbh();
        $ids = explode(',', $args);
        foreach ($ids as $id) {
            $final_args = [
                'id' => $id,
                'action', 'unarchive'
            ];
            $this->archive_or_unarchive($final_args);

            $insert_sql = "INSERT INTO sis_profile_convert (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id, db63026)
									VALUES (?,?,?,?,?,?,?)";
            $sth = $dbh->prepare($insert_sql);
            $core_student_id = pull_field('sis_profiles', 'rel_id', "WHERE id={$id}");
            $sth->execute(array(random(), session_info("uid"), session_info("usergroup"), $core_student_id, custom_date_and_time(), session_info("uid"), '98'));

        }
        $sql = "UPDATE sis_profiles SET rec_archive ='', rec_lstup = CURRENT_TIMESTAMP, rec_lstup_id = :user WHERE id IN ({$args})";
        $st = $dbh->prepare($sql);
        $st->bindParam(':user', $_SESSION['uid'], PDO::PARAM_INT);
        $st->execute();
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    public function get_booking_portal($args): array
    {
        global $paginator;
        $id = $args['id'];
        $usergroup = $_SESSION['usergroup'];
        global $db;
        $dbh = get_dbh();
        $search_term = '';
        $tutorsModel = new CourseTutors;
        if (!empty($args['search_term'])) {
            $search_term = "AND ((SELECT db232 from core_courses where id = db16136) like '%{$args['search_term']}%' or
			 (select db14973 from sis_attendance_status where id=db14981) like '%{$args['search_term']}%' or
			 (select db14970 from sis_booking_status where sis_booking_status.id = db14983) like '%{$args['search_term']}%' or
			 db14963 like '%{$args['search_term']}%'
			  )";
        }
        if (empty($args['order'])) {
            $order = " ORDER BY db14978 desc";
        } else {
            $order = "ORDER BY {$args['order']} desc";
        }
        $limit_sql = '';


        $bookings_sql = "SELECT
    		sis_profiles.id as profile_id,
    		sis_profiles.rel_id as id,
    		(select db14970 from sis_booking_status where sis_booking_status.id = db14983) as booking_status,
            (select db14973 from sis_attendance_status where id=db14981)  as attendance_status,
            (SELECT db232 from core_courses where id = db16136) as course_title,
            db14977 as scheduled_course_id,
            db14947 as `start_date`,
            db14949 as `end_date`,
            db19985 as sched_course_id,
            db16136 as course_id,
            sis_scheduled_booking.date as booked_date,
            db14963 as venue_name,
            (select db14941 from sis_course_schedule_stages where sis_course_schedule_stages.id =db14959) as course_status,
            (select username_id from core_students where id=sis_profiles.rel_id) as username_id,
            sis_scheduled_booking.id as booking_id,
            IF(CURDATE()>db14949, 'past', 'not_past') AS 'due',
			(select username_id from core_courses where id=db16136) as course_username_id,
    		db63007 as web_link,
    		db14950 as end_time,
			db14948 as start_time,
			(select count(id) from core_course_materials
			    where rel_id=db16136
			    and usergroup ='{$_SESSION['usergroup']}'
			    and (rec_archive is null or rec_archive= '')
			    and db63982 = 'active'
			) as course_materials,
            db14954 as 'course_venue_id'
    		
            FROM sis_scheduled_booking
            LEFT JOIN sis_profiles ON sis_profiles.rec_id = {$id}
            LEFT JOIN sis_sched_booking_detail on sis_sched_booking_detail.db15052=sis_scheduled_booking.id
            LEFT JOIN sis_course_schedule ON sis_course_schedule.id= db14977
            LEFT JOIN sis_course_venues ON sis_course_schedule.db14954 = sis_course_venues.id
            WHERE sis_scheduled_booking.rel_id in (select id from core_students where usergroup={$usergroup} and rel_id=sis_profiles.rel_id)
            AND (sis_scheduled_booking.rec_archive is NULL OR sis_scheduled_booking.rec_archive = '') 
            AND (sis_course_schedule.rec_archive is NULL OR sis_course_schedule.rec_archive = '')             
            AND (sis_profiles.rec_archive is NULL OR sis_profiles.rec_archive = '')
            AND (sis_sched_booking_detail.rec_archive is NULL OR sis_sched_booking_detail.rec_archive = '')
            AND db48581 IN (select id from lead_mrn_stages where db166247 IN ('eois','applicants', 'students'))
            {$search_term}
            {$order}
        ";
        dev_debug($bookings_sql);
        if (!empty($args['paginate'])) {
            $limit_sql = $paginator->limit_sql();
            $paginator->calculate_total_entries($bookings_sql);
        }
        $bookings_sql .= ' ' . $limit_sql;
        $bookings = $db->query($bookings_sql);
        if (!empty($bookings)) {
            #get sessions
            foreach ($bookings as $booking_key => $booking) {
                if (!empty($booking['scheduled_course_id'])) {
                    $sessions_info = array();
                    ////now get the data for all the session
                    $session_info_sql = "SELECT
                        db59906 as 'session_name',
                        db14963 as 'session_venue_name',
                        db14941 as 'session_status',
                        db59835 as 'start_date',
                        db59836 as 'start_time',
                        db59837 as 'end_date',
                        db59838 as 'end_time',
						db63025 as web_conferencing_link
                        FROM sis_scheduled_sessions 
                        LEFT JOIN sis_course_venues on sis_course_venues.id=sis_scheduled_sessions.db59839
                        LEFT JOIN sis_course_schedule_stages on sis_course_schedule_stages.id=sis_scheduled_sessions.db59908                                            
                        WHERE sis_scheduled_sessions.rel_id=:scheduled_course_id 
                        AND sis_scheduled_sessions.usergroup=:usergroup 
                        AND (sis_scheduled_sessions.rec_archive is NULL or sis_scheduled_sessions.rec_archive = '')
                    ";
                    $session_info_stmt = $dbh->prepare($session_info_sql);
                    dev_debug($session_info_sql);
                    dev_debug('scheduled_course_id - ' . $booking['scheduled_course_id'] . '</br>' . 'usergroup - ' . $_SESSION['usergroup']);
                    $session_info_stmt->execute(
                        array(
                            'scheduled_course_id' => $booking['scheduled_course_id'],
                            'usergroup' => $_SESSION['usergroup']
                        )
                    );
                    $sessions_info = $session_info_stmt->fetchAll(PDO::FETCH_ASSOC);
                    $bookings[$booking_key]['sessions'] = $sessions_info;

                    // get the shared resources for this course if any
                    $shared_args = array(
                        'programme' => $booking['course_id'],
                    );
                    $shared_resources = get_shared_resources($shared_args);
                    $course_resources = [];
                    // only be left with shared resources where db34640 = 'programme'
                    foreach ($shared_resources as $resource) {
                        if ($resource['db34640'] == 'programme') {
                            $course_resources[] = $resource;
                        }
                    }
                    $bookings[$booking_key]['resources'] = $course_resources;

                    //get tutor name and photo
                    $tutorArgs = [
                        'core_course_id' => $booking['course_id'],
                        'scheduled_course_id' => $booking['scheduled_course_id']
                    ];
                    $tutorDetails = $tutorsModel->get_tutor_details($tutorArgs);
                    $bookings[$booking_key]['tutorsData'] = $tutorDetails;

                }
            }
        }
        $applications = [];
        $core_student_id = pull_field('core_students', 'id', "WHERE rec_id='{$id}' AND (rec_archive IS NULL or rec_archive = '') AND usergroup = $_SESSION[usergroup] AND ((SELECT db48581  from sis_profiles where sis_profiles.rel_id = core_students.id) IN (select id from lead_mrn_stages where db166247 IN ('eois','applicants', 'students')))");
        $applications['id'] = $core_student_id;
        $applications['username_id'] = pull_field('core_students', 'username_id', "WHERE rec_id='{$id}' AND (rec_archive IS NULL or rec_archive = '') AND usergroup = $_SESSION[usergroup] AND ((SELECT db48581  from sis_profiles where sis_profiles.rel_id = core_students.id) IN (select id from lead_mrn_stages where db166247 IN ('eois','applicants', 'students')))");
        $applications['status'] = pull_field('lead_mrn_stages', 'db49848', " WHERE id = (select db48581 from sis_profiles where sis_profiles.rec_id = {$id} AND (rec_archive IS NULL or rec_archive = '') AND usergroup = $_SESSION[usergroup] AND db48581 IN (select id from lead_mrn_stages where db166247 IN ('eois','applicants', 'students')) order by id desc limit 1)");
        $attended_induction_course = pull_field("core_students 
JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
JOIN sis_course_schedule ON sis_course_schedule.id = db14977 
JOIN core_courses ON core_courses.id = CAST(db14946 AS UNSIGNED)", "IF(count(sis_sched_booking_detail.id) > 0,'yes','no')",
            "WHERE core_students.rel_id = {$core_student_id} AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (db64664 = '1' OR db64664 = '2') AND db66775 = 'yes'");

        $applications['attended_induction_course'] = $attended_induction_course;

        $no_of_courses_attended = pull_field("sis_profiles","(SELECT count(*) FROM core_students
			JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
			JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
			JOIN sis_course_schedule ON sis_course_schedule.id = db14977 WHERE core_students.rel_id = sis_profiles.rel_id
			AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (db64664 = '1' OR db64664 = '2'))","WHERE sis_profiles.rec_id = {$id}");

        return [
            'applications' => $applications,
            'booked_courses' => $bookings,
            'no_of_courses_attended' => $no_of_courses_attended
        ];
    }

    public function fields_dimensions($active_columns)
    {

        $fields_to_use = [
            'db48643' => "(SELECT GROUP_CONCAT(db16998) FROM core_disabilities WHERE usergroup = '$_SESSION[usergroup]' AND  FIND_IN_SET(id,db48643)) as db48643",
            'db48609' => '(select db48829 from sis_primary_groups where id=db48609) as db48609',
            'db48594' => '(select db232 from core_courses where id=db48594) as db48594',
            'db48592' => '(select db232 from core_courses where id=db48592) as db48592',
            'db48574' => '(select db16997 from core_hear_about_us where id=db48574) as db48574',
            'db48610' => "(select group_concat(db66789) from sis_student_groups where usergroup = '$_SESSION[usergroup]' AND FIND_IN_SET(id, db48610)) as db48610"
        ];

        foreach ($active_columns as $c_key => $c_value) {
            if (array_key_exists($c_value, $fields_to_use)) {

                $active_columns[$c_key] = $fields_to_use[$c_value];
            }
        }


        return $active_columns;
    }

    public function get_unsubscribe_status($args)
    {
        $mail = new Emails();
        $email = addslashes(trim($args));
        return $mail->get_mail_subscriptions(['email' => $email]);
    }

    public function subscribe_user($user)
    {
        global $db;
        $dbh = get_dbh();
        $email_add = addslashes(trim(pull_field('sis_profiles', 'db48583', "WHERE id='{$user}'")));
        $query = "SELECT id
		FROM coms_mail_unsubscriptions WHERE usergroup='{$_SESSION['usergroup']}'
		AND (rec_archive IS NULL OR rec_archive ='' )
		AND (db64115='on' or db171581 is not null or db171581<> '' or db171584 is not null or db171584<> '' )
		and db64116 ='{$email_add}'";
        $results = $db->query($query);

        foreach ($results as $result) {
            $update_sql = "UPDATE coms_mail_unsubscriptions SET rec_archive= '{$_SESSION['uid']}' WHERE id={$result['id']}";
            $sth = $dbh->prepare($update_sql);
            $sth->execute();
        }
    }

    /**
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function check_duplicates($profile_data, $args_check_duplicates= array()): bool
    {
        $dbh=get_dbh();
        $condition = $search = '';

        if (!empty($profile_data['email'])) {
            $email = trim(addslashes($profile_data['email']));
            $email_search = "db48583 = '{$email}'";
        } else {
            $email_search = '';
        }

        if (!empty($profile_data['mobile'])) {
            if ($email_search != '') $condition = ' OR ';
            $contact_search = "db48597 = \"{$profile_data['mobile']}\"";
        } else {
            $contact_search = '';
        }
        $non_active_search = '';
        if (!empty($args_check_duplicates['search_non_active'])) {
            $search_non_active = $args_check_duplicates['search_non_active'];
        }
        else {
            $search_non_active = pull_field("lead_preferences", "db248882", "WHERE usergroup = $_SESSION[usergroup] and (rec_archive is null or rec_archive = '')");
        }
        if ($search_non_active == 'no') {
            $non_active_search .= " AND (sis_profiles.rec_archive IS NULL OR sis_profiles.rec_archive = '') ";
            $non_active_search .= " AND (SELECT db166247 FROM lead_mrn_stages WHERE id = db48581) IN ('eois','applicants','students')";
        }

        $dob_check = "";
        if (!empty($args_check_duplicates['search_dob'])) {
            $search_dob = $args_check_duplicates['search_dob'];
        }
        else {
            $search_dob = pull_field("lead_preferences", "db231539", "where usergroup = {$_SESSION['usergroup']} and (rec_archive is null or rec_archive = '')");
        }
        if ( $search_dob == 'yes') {
            $second_condition = "";
            if (!empty($contact_search) || !empty($email_search)) {
                $second_condition = " or ";
            }
            $first_name = trim(addslashes($profile_data['first_name']));
            $surname = trim(addslashes($profile_data['surname']));
            $dob_check = " {$second_condition} (db48572 = '{$profile_data['dob']}' AND db48566= '{$first_name}' AND db48568= '{$surname}')";
        }

        if (!empty($contact_search) || !empty($email_search) || !empty($dob_check)) {
            $search = "$email_search $condition $contact_search $dob_check";
        }

        //include graduate to duplicate check
        $graduateCheck = '';
        if (!empty($args_check_duplicates['search_dob'])&&!empty($args_check_duplicates['search_graduate'])) {
            $search_graduate = $args_check_duplicates['search_graduate'];
        }
        else {
            $search_graduate = pull_field("lead_preferences", "db298181", "where usergroup = {$_SESSION['usergroup']} and (rec_archive is null or rec_archive = '')");
        }
        if ( $search_graduate == 'no') {
            $graduateCheck = " AND db48581  not in (select db49850 from lead_mrn_stages where db166247='graduated')";
        }

        $sql = "SELECT * from sis_profiles
         where ({$search})
         and id != {$profile_data['id']}
         and (rec_archive is null or rec_archive = '')
		 and usergroup = '{$_SESSION['usergroup']}'
		     {$graduateCheck}
		 $non_active_search
         ";

        if (!empty($contact_search) || !empty($email_search) || !empty($dob_check)) {
            $stmt=$dbh->prepare($sql);
            $stmt->execute();
            $duplicates = $stmt->fetchAll(2);
        }

        if (empty($contact_search) && empty($email_search) && empty($dob_check)) {
            return false;
        } elseif (!empty($duplicates)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function reqol_checks($user_id)
    {
        $dbh = get_dbh();
        $student_ids_list = pull_field('sis_profiles', 'rel_id', " WHERE rec_id = {$user_id} and (rec_archive is null or rec_archive  ='') AND (SELECT db166247 FROM lead_mrn_stages WHERE id = db48581) IN ('applicants','students')");

        $reqols_sql = "
                SELECT sis_reqol_requests.username_id as reqol_request_username, 
                sis_reqol_requests.rel_id as student_id, 
                sis_profiles.username_id as profile_username_id
                FROM sis_reqol_requests
                LEFT JOIN sis_reqol ON sis_reqol.db63857 = sis_reqol_requests.username_id  AND (sis_reqol.rec_archive IS NULL OR sis_reqol.rec_archive = '') 
                LEFT JOIN sis_profiles ON sis_profiles.rel_id = sis_reqol_requests.rel_id
                WHERE sis_reqol_requests.rel_id in ({$student_ids_list})
                AND (sis_reqol_requests.rec_archive IS NULL OR sis_reqol_requests.rec_archive = '')
                AND sis_reqol_requests.usergroup = {$_SESSION['usergroup']}
                AND sis_reqol.id IS NULL                
                ORDER BY sis_reqol_requests.id ASC
            ";
        dev_debug($reqols_sql);

        $sth5 = $dbh->prepare($reqols_sql);
        $sth5->execute();
        return $sth5->fetchAll(PDO::FETCH_ASSOC);
    }

    /** ===================================
     * Student groups
     * ====================================    */
    function get_student_groups($args)
    {
        global $db;
        if ($args['school_id']) {
            $school_id_sql = "AND sis_student_groups.usergroup='" . $args['school_id'] . "'";
        }

        if ($args['title']) {
            $id_sql = "AND sis_student_groups.db66789='" . $args['title'] . "'";
        }

        if (!empty($args['active'])) {
            $active_sql = " AND (sis_student_groups.rel_id IS NULL OR sis_student_groups.rel_id ='') 
            AND (sis_student_groups.rec_archive IS NULL OR sis_student_groups.rec_archive ='') 
            AND (sis_student_groups.db66789 !='' OR sis_student_groups.db66789 IS NOT NULL ) ";
        }

        $query = "SELECT * FROM sis_student_groups WHERE 1 $school_id_sql $id_sql $active_sql ORDER BY db66789";
        $results = $db->query($query);
        dev_debug($query);
        $results_list = array();
        foreach ($results as $entry) {

            $results_list[] = array(
                'id' => $entry['id'],
                'title' => $entry['db66789'],
            );

        }

        if ($args['id'] || $args['title']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /** ===================================
     * Geographical Areas
     * ====================================    */
    function get_geographical_areas($args)
    {
        global $db;
        if ($args['school_id']) {
            $school_id_sql = "AND sis_course_geog_area.usergroup='" . $args['school_id'] . "'";
        }

        if ($args['title']) {
            $id_sql = "AND sis_course_geog_area.db48828='" . $args['title'] . "'";
        }

        if (!empty($args['active'])) {
            $active_sql = " AND (sis_course_geog_area.rel_id IS NULL OR sis_course_geog_area.rel_id ='') AND (sis_course_geog_area.rec_archive IS NULL OR sis_course_geog_area.rec_archive ='') AND (sis_course_geog_area.db48828 !='' OR sis_course_geog_area.db48828 IS NOT NULL ) ";
        }

        $query = "SELECT * FROM sis_course_geog_area WHERE 1 $school_id_sql $id_sql $active_sql ORDER BY db48828";
        $results = $db->query($query);
        dev_debug($query);
        $results_list = array();
        foreach ($results as $entry) {

            $results_list[] = array(
                'id' => $entry['id'],
                'title' => $entry['db48828'],
            );

        }

        if ($args['id'] || $args['title']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /** ===================================
     * Geographical Coverage
     * ====================================    */
    function get_geographical_coverage($args)
    {
        global $db;
	    $school_id_sql = $id_sql = $active_sql = '';
        if($args['school_id']){
            $school_id_sql = "AND sis_course_geog_coverage.usergroup='".$args['school_id']."'";
        }

        if ($args['title']) {
            $id_sql = "AND sis_course_geog_coverage.db48826='" . $args['title'] . "'";
        }

        if(! empty($args['active'])){
            $active_sql = " AND (sis_course_geog_coverage.rel_id IS NULL OR sis_course_geog_coverage.rel_id ='')
             AND (sis_course_geog_coverage.rec_archive IS NULL OR sis_course_geog_coverage.rec_archive ='')
             AND (sis_course_geog_coverage.db48826 !='' OR sis_course_geog_coverage.db48826 IS NOT NULL )
             ";
        }

        $query = "SELECT * FROM sis_course_geog_coverage WHERE 1 $school_id_sql $id_sql $active_sql ORDER BY db48826";
        $results = $db->query($query);
        dev_debug($query);
        $results_list = array();
        foreach ($results as $entry) {

            $results_list[] = array(
                'id'=>$entry['id'],
                'title'=>$entry['db48826'],
	            'value' => $entry['id'],
	            'label' => $entry['db48826'],
            );

        }

        if ($args['id'] || $args['title']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    public function archive_or_unarchive($args)
    {
        $dbh = get_dbh();
        $profile_id = $args['id'];
        $user_id = NULL;
        $action = 'no';
        $form_users_id = pull_field('sis_profiles', 'rec_id', "where id ={$profile_id}");
        $check = ['1'];

        if ($args['action'] == 'archive') {
            $user_id = $_SESSION['uid'];
            $action = 'yes';

            /// ON ARCHIVE ONLY
            /// suspend the user so that they can not login if they only have one sis_profile
            $check_sql = "SELECT id from sis_profiles where (rec_archive is null or rec_archive= '') and rec_id = '{$form_users_id}'";
            $sth8 = $dbh->prepare($check_sql);
            $sth8->execute();
            $check = $sth8->fetchAll(PDO::FETCH_ASSOC);
        }

        $core_student_id = pull_field("sis_profiles", 'rel_id', "WHERE usergroup='$_SESSION[usergroup]' AND id = $profile_id");

        $upsql = "UPDATE core_students SET rec_archive = :uid, rec_lstup = CURRENT_TIMESTAMP WHERE id=:core_student_id";
        $upsql = $dbh->prepare($upsql);
        $upsql->execute(
            array(
                'uid' => $user_id,
                'core_student_id' => $core_student_id
            )
        );

        if (count($check) == 1) {
            $user_update = "UPDATE form_users set db307 ='{$action}' WHERE id = {$form_users_id} and usergroup ={$_SESSION['usergroup']}";
            $sth6 = $dbh->prepare($user_update);
            $sth6->execute();

        }

        $tables_to_effect = array(
            'form_sms_log', 'form_email_log',
            'dir_internal_notes', 'core_letter_generator', 'dir_letters_sent',
            'core_tasks', 'sis_ind_learner_goals',
            'sis_ind_learner_plan', 'enrol_induction_checklist',
            'sis_student_contact', 'sis_reqol', 'core_notes', 'sis_reqol_requests', 'sis_next_of_kin', 'lead_interactions'
        );


        foreach ($tables_to_effect as $table) {
            $up1sql = "UPDATE $table SET rec_archive = :uid, rec_lstup = CURRENT_TIMESTAMP WHERE rel_id=:core_student_id and usergroup=:usergroup";
            $up1sql = $dbh->prepare($up1sql);
            $up1sql->execute(
                array(
                    'uid' => $user_id,
                    'usergroup' => $_SESSION['usergroup'],
                    'core_student_id' => $core_student_id
                )
            );
        }
        ### 3 delete all the records related to sis_profiles.id=sis_scheduled_booking.rel_id and usergroup = MRN group
        $up2sql = "UPDATE sis_scheduled_booking SET rec_archive = :uid, rec_lstup = CURRENT_TIMESTAMP WHERE rel_id in (select id from core_students where usergroup=:usergroup and rel_id=:core_student_id) and usergroup=:usergroup";
        $up2sql = $dbh->prepare($up2sql);
        $up2sql->execute(
            array(
                'uid' => $user_id,
                'usergroup' => $_SESSION['usergroup'],
                'core_student_id' => $core_student_id
            )
        );

        ### 4 delete all the records related to sis_profiles.id=sis_session_bookings.rel_id and usergroup = MRN group
        $up3sql = "UPDATE sis_session_bookings SET rec_archive = :uid, rec_lstup = CURRENT_TIMESTAMP WHERE rel_id in (select id from core_students where usergroup=:usergroup and rel_id=:core_student_id) and usergroup=:usergroup";
        $up3sql = $dbh->prepare($up3sql);
        $up3sql->execute(
            array(
                'uid' => $user_id,
                'usergroup' => $_SESSION['usergroup'],
                'core_student_id' => $core_student_id
            )
        );

        ### 5 delete all the records related to sis_profiles.id=core_students.rel_id and usergroup = MRN group
        $up4sql = "UPDATE core_students SET rec_archive = :uid, rec_lstup = CURRENT_TIMESTAMP WHERE rel_id=:core_student_id and usergroup=:usergroup";
        $up4sql = $dbh->prepare($up4sql);
        $up4sql->execute(
            array(
                'uid' => $user_id,
                'usergroup' => $_SESSION['usergroup'],
                'core_student_id' => $core_student_id
            )
        );

        ### 7 delete all in sis_sis_sched_booking_detail
        $up6sql = "UPDATE sis_sched_booking_detail SET rec_archive = :uid, rec_lstup = CURRENT_TIMESTAMP  where rel_id in (select id from sis_scheduled_booking where rel_id in (select id from core_students where usergroup=:usergroup and rel_id=:core_student_id))";
        $up6sql = $dbh->prepare($up6sql);
        $up6sql->execute(
            array(
                'uid' => $user_id,
                'usergroup' => $_SESSION['usergroup'],
                'core_student_id' => $core_student_id
            )
        );
    }

    public function update_profile_status($profile_id, $chosen_status)
    {

        $old_status = pull_field("sis_profiles", "db48581", "WHERE id=$profile_id and usergroup = $_SESSION[usergroup]");
        //db61492, db61493, db61494
        $status_hist_info = array(
            'db61492' => $chosen_status,
            'db61493' => $old_status,
            'db61494' => 1,
            'rel_id' => $profile_id
        );
       DB::system_table_insert_or_update('sis_profile_history', $status_hist_info);

        return DB::getPdo()->lastInsertId();
    }

    public function all_duplicates(array $args)
    {
        $dbh=get_dbh();
        global $paginator, $db;
        $limit_sql = $id_sql = $search_sql = $filter_sql = $outlined = $dob_check = '';

        if (!empty($args['search'])) {
            $term = trim($args['search']);
            $searchable_fields = 'db48568,db48583,db48566,db48597,db48571';
            $gedified_hack = explode(" ", $term);
            foreach ($gedified_hack as $piece) {
                $piece = str_replace("'", "\'", $piece);
                $searchable_fields = str_replace(",", ",' ',", $searchable_fields);
                $outlined .= "AND CONCAT($searchable_fields) LIKE '%$piece%' ";
            }

            //added to allow for when data is missing infields
            $outlined = str_replace("db48568", "COALESCE(p1.db48568,'')", $outlined);
            $outlined = str_replace("db48583", "COALESCE(p1.db48583,'')", $outlined);
            $outlined = str_replace("db48566", "COALESCE(p1.db48566,'')", $outlined);
            $outlined = str_replace("db48597", "COALESCE(p1.db48597,'')", $outlined);
            $outlined = str_replace("db48571", "COALESCE(p1.db48571,'')", $outlined);
            dev_debug($outlined);
            $search_sql = $outlined;
        }

        if (!empty($args['id'])) {
            $id_sql = "AND p1.id='{$args['id']}'";
        }

        if (!empty($args['order'])) {
            $order_by_sql = "ORDER BY {$args['order']}";
        } else {
            $order_by_sql = "ORDER BY p1.id desc";
        }

        if (!empty($args['filter_sql'])) {
            $filter_sql = $args['filter_sql'];
            $filter_sql = str_replace('archived', "if(p1.rec_archive, 'yes', 'no')", $filter_sql);
        }

        if (pull_field("lead_preferences", "db231539", "where usergroup = {$_SESSION['usergroup']}") == 'yes') {
            $dob_check = " or (p1.db48572 = p2.db48572 AND p1.db48566=p2.db48566 AND p1.db48568=p2.db48568)";
        }
        $non_active_search_p2 = '';
        $non_active_search_p1 = '';
        $search_non_active = pull_field("lead_preferences", "db248882", "WHERE usergroup = $_SESSION[usergroup] and (rec_archive is null or rec_archive = '')");
        if ($search_non_active == 'no') {
            $non_active_search_p1 .= " AND (p1.rec_archive IS NULL OR p1.rec_archive = '') ";
            $non_active_search_p1 .= " AND (SELECT db166247 FROM lead_mrn_stages WHERE id = p1.db48581) IN ('eois','applicants','students')";
            $non_active_search_p2 .= " AND (p2.rec_archive IS NULL OR p2.rec_archive = '') ";
            $non_active_search_p2 .= " AND (SELECT db166247 FROM lead_mrn_stages WHERE id = p2.db48581) IN ('eois','applicants','students')";
        }

        $query = "
			SELECT p1.id,
			DATE_FORMAT(p1.date, '%d/%m/%Y') as date_created,
			p1.rel_id as student_id,
			p1.username_id,
			p1.db48566 as first_name,
			p1.db48568 as surname,
			p1.db48572 as date_of_birth,
			p1.db48583,
			p1.db48597,
			if(p1.rec_archive, 'yes', 'no') as archived
			FROM sis_profiles p1
			INNER JOIN sis_profiles p2 ON (
			    ( (p1.db48583 = p2.db48583 and p1.db48583 REGEXP '^[^@]+@[^@]+\.[^@]{2,}$')
				or ( p2.db48597 = p1.db48597 and (p1.db48597 !='' and p1.db48597 is not null))
			        {$dob_check}
				)
			    AND p1.id <> p2.id
			    
			    {$non_active_search_p2}
			)
			
			WHERE cast(p1.usergroup as unsigned) = {$_SESSION['usergroup']} and cast(p2.usergroup as unsigned) = {$_SESSION['usergroup']}
			
			$id_sql
			$search_sql
			$filter_sql
			$non_active_search_p1
			group by p1.id
			$order_by_sql
		";

        if (!empty($args['paginate'])) {
            $limit_sql = $paginator->limit_sql();
            $paginator->calculate_total_entries($query);
        }

        $query = $query . $limit_sql;
        dev_debug(__METHOD__ . ": " . $query);
        $stmt=$dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(2);

        return $results;
    }

    public function radio_yes_no(): array
    {
        return [
            [
                'label' => 'Yes',
                'value' => 'yes',
            ],
            [
                'label' => 'No',
                'value' => 'no',
            ]
        ];
    }

    /**
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function get_primary_groups()
    {
        global $db;
        $sql = "SELECT id as value , db48829 as label 
			FROM sis_primary_groups 
			where (rec_archive is null or rec_archive = '') 
			and (db105425 is null or db105425 != 'no') 
			and usergroup = '{$_SESSION['usergroup']}'
		";

        if (empty($db->query($sql))) {
            $sql = "SELECT id as value , db48829 as label 
				FROM sis_primary_groups 
				where (rec_archive is null or rec_archive = '') 
				and (db105425 is null or db105425 != 'no') 
				and usergroup = '1'
			";
        }
        return $db->query($sql);
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    public function get_languages()
    {
        $dbh = get_dbh();
        $supported_languages = pull_field('lead_preferences', 'db50794', "WHERE usergroup = {$_SESSION['usergroup']}");
        $supported_languages_sql = "";
        if (!empty($supported_languages)) {
            $supported_languages_sql = " and id in ({$supported_languages})";
        }
        $sql = "Select id,db21280, db21281,db253286 from form_languages where (usergroup= '1' or usergroup = '{$_SESSION['usergroup']}') {$supported_languages_sql}";
        dev_debug($sql);
        $sth = $dbh->prepare($sql);
        $sth->execute();
        return $sth->fetchAll(PDO::FETCH_OBJ);
    }
}
