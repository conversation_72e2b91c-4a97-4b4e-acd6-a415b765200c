<?php
use App\core\support\Collection;
	/**
	* Hooks
	*/
	class Hooks{

		Public $hooks_path = "/Applications/XAMPP/xamppfiles/heiapply/engine/models/custom/";

		/** ===================================
		 * Get Event Hooks
		====================================	*/
		function get_event_hooks($args=array()){
			$dbh = get_dbh();

			if($args['id']){
				$id_sql = "AND core_automated_event.id='".$args['id']."'";
      }

      if($args['type']){
				$type_sql = "AND core_automated_event.db22035='".$args['type']."'";
			}

			if($args['school_id']){
				$school_id_sql = "AND core_automated_event.usergroup='".$args['school_id']."'";
			}else{
				$school_id_sql = "AND core_automated_event.usergroup='".$_SESSION['usergroup']."'";
			}

			if($args['field_id']){
				$field_like = '"field_id":"'.$args['field_id'].'"';
				$field_like2 = '"field_id":'.$args['field_id'].'';
				$field_id_sql = "AND (db22036 LIKE '%".$field_like."%' or db22036 LIKE '%".$field_like2."%')";
			}


			$query="
			SELECT 
				*
			FROM
				core_automated_event 
			WHERE
				1
	        $id_sql
	        $type_sql
			$school_id_sql
			$field_id_sql
			AND (core_automated_event.rec_archive = '' OR core_automated_event.rec_archive IS NULL)
	      	ORDER BY db22034 ASC";

			$sth = $dbh->prepare($query);
			$sth->execute();

			$num=0;

			$hooks_list = array();
			while($row_result = $sth->fetchAll()) {
				foreach($row_result as $row){

				$actions = $this->get_actions(array('hook_id'=>$row['id']));

				$hook_info = array(
					"id"=>$row['id'],
					"title"=>$row['db22034'],
					"event_type"=>$row['db22035'],
					"details"=>$row['db22036'],
					"actions"=>$actions,
				);

				$details = json_decode($row['db22036']);
				if($row['db22035']=="time_related"){
					$hook_info['time_type'] = $details->time_type;
					if($details->date){
						$hook_info['date'] = date("Y-m-d",strtotime($details->date));
					}
					$hook_info['time_period'] = $details->time_period;
				}

				if($row['db22035']=="location"){
					$hook_info['location'] = $details->location;
				}

				if($row['db22035']=="data_change"){
					$hook_info['conditions'] = $details;
				}

				if(!$details->table){
					//$details->table = $args['form_id'];
					//$details->table_fix = true;
				}
				// echo '<pre>';
				// 	print_r($details);
				// 	echo '</pre>';
				// 	exit();

				$hooks_list[] = $hook_info;
			}

			if($args['id']){
				return $hooks_list[0];
			}else{
				return $hooks_list;
			}

			}//end for each
		}//end while

		/** ===================================
		 * Delete Event Hook
		====================================	*/
		function delete_event_hooks($args){
			global $db;

			if($args['id']){
				$where = array('db22048'=>$args['id']);
				$db->delete('core_automated_event_action', $where);

				$where = array('id'=>$args['id']);
				$db->delete('core_automated_event', $where);
			}
		}


		/** ===================================
		 * Delete Action
		====================================	*/
		function delete_action($args){
			global $db;

			if($args['id']){
				$where = array('id'=>$args['id']);
				$db->delete('core_automated_event_action', $where);
			}
		}

		/** ===================================
		 * Get Actions
		====================================	*/
		function get_actions($args=array()){
			$dbh = get_dbh();
            //get school languages
            $sth=$dbh->prepare("SELECT db50794 from lead_preferences WHERE usergroup='".$_SESSION['usergroup']."'");
            $sth->execute();
            $languages=explode(',',$sth->fetchColumn(0));
			if($args['hook_id']){
				$id_sql = "AND core_automated_event_action.db22048='".$args['hook_id']."'";
			}

			$query="
			SELECT 
				*
			FROM
				core_automated_event_action 
			WHERE
				1
				$id_sql
			ORDER BY id ASC";

			//// echo $query;
			//exit();

			$sth = $dbh->prepare($query);
			$sth->execute();

			$actions = array();
			while($row_result = $sth->fetchAll()) {
				foreach($row_result as $row){


					if($row['db22051']=="null"){
						$row['db22051'] = "[]";
					}
					$FJson = json_decode($row['db22051']);
                    $translations = $this->get_action_multi_languages(['rel_id' => $row['id']]);
                    if (count($translations) == 0) {
                        //just put english as the default
                        $message = '';
                        switch ($row['db22043']) {
                            case 'letter':
                                $message = $row['db22047'];
                                break;
                            case 'notification':
                                $message = $row['db22046'];
                                break;
                            case 'resources':
                                $message = $row['db32611'];
                                break;
                            case 'email':
                                $message = $row['db22044'];
                                break;
                            case 'sms':
                                $message = $row['db22045'];
                                break;
                            case 'task':
                                $message = $row['db32611'];
                                break;
                        }
                        $translations[] = [
                            'id' => '',
                            'username_id' => '',
                            'rec_id' => '',
                            'db51136' => $row['db22042'],
                            'db51137' => $row['db22055'],
                            'db51138' => $message,
                            'db51139' => 1,
                        ];
                    }
                    $translations = \App\core\support\Collection::unwrap(\App\core\support\Collection::make($translations)->keyBy('db51139'));
//					echo json_encode($translations);
                    foreach ($languages as $key) {
                        if (!array_key_exists($key, $translations)) {
                            //put the langauage in the array
                            $translations[$key] = [
                                'id' => '',
                                'username_id' => '',
                                'rec_id' => '',
                                'db51136' => '',
                                'db51137' => '',
                                'db51138' => '',
                                'db51139' => $key,
                            ];
                        }
                    }
                    //initial task options
                    $task_options=[
                        [
                            'title'=>'Hide Upload Button',
                            'value'=>'yes'
                        ]
                    ];
                    if(!empty($row['db52954'])){
                        foreach (json_decode($row['db52954'], true) as $key){
                            foreach ($task_options as $k=>$v){
                                if($v['title']==$key['title']){
                                    $task_options[$k]['value']=$key['value'];
                                    break;
                                }

                            }
                        }

                    }

                    $action_info = array(
                        "id" => $row['id'],
                        "title" => $row['db22055'],
                        "type" => $row['db22038'],
                        "email_to_type" => $row['db22040'],
                        "email_from" => $row['db22039'],
                        "email_to" => $row['db22052'],
                        "language" => $row['db22041'],
                        "subject" => $row['db22042'],
                        "message_type" => $row['db22043'],
                        "template" => $row['db22050'],
                        "fields_list" => $FJson,
                        "email" => $row['db22044'],
                        "sms" => $row['db22045'],
                        "notification" => $row['db22046'],
                        "letter" => $row['db22047'],
                        "hook_id" => $row['db22048'],
                        "publish_days" => $row['db37736'],

                        "task_id" => $row['db32505'],
                        "task_due_date" => $row['db32609'],
                        "task_notify_applicant" => $row['db74312'],
                        "task_status" => $row['db32610'],
                        "task_description" => $row['db32611'],
                        "resources" => json_decode($row['db35942']),
                        "description" => $row['db32611'],
                        "url" => $row['db251930'],
                        "translations" => $translations,
                        "task_options" => $task_options,
                    );


					$actions[] = $action_info;

				}//end for each
			}//end while

			return $actions;
		}
        public function get_action_multi_languages($args)
        {
            $dbh = get_dbh();

            if (!empty($args['rel_id'])) {
                $rel_id_sql = " AND rel_id='" . $args['rel_id'] . "'";
            } else {
                $rel_id_sql = '';
            }
            $query = "SELECT id,db51136,db51137,db51138,db51139 FROM form_event_translations WHERE 1 " . $rel_id_sql;
            $sth = $dbh->prepare($query);
            $sth->execute();
            return $sth->fetchAll(PDO::FETCH_ASSOC);
        }
		/** ===================================
		 * Get Tasks
		====================================	*/
		function get_tasks($args=array()){
			global $paginator;
			global $school_info;
			$dbh = get_dbh();

			if($args['id']){
				$id_sql = "AND core_tasks.id='".$args['id']."'";
			}

			if(!$args['applicant_tasks']){
				$form_notification_logs_join = "LEFT JOIN form_notification_logs ON form_notification_logs.rel_id = core_tasks.id ";
			}

			if($args['student_ids']){
				$student_id_sql = "AND core_tasks.rel_id IN(".implode(",", $args['student_ids']).")";
			}

			if($args['assigned_to_me']){
				$assigned_to_sql = " AND db1745 ='".$args['assigned_to_me']."' ";
    //         $locate_sql = " ,locate(concat(',', " . $args['assigned_to_me'] . ", ','), concat(',', db39484, ',')) as locate_reader,
				// locate(concat(',', ".$args['assigned_to_me'].", ','), concat(',', db1745, ',')) as locate_reviewer
				// ";

				// //reviewer active asignemnt
				// $assigned_to_reviewer="AND (core_assignations.db69836 = '".$args['assigned_to_me']."'  and ((core_assignations.db69743 is null or core_assignations.db69743 ='') and core_tasks.date>core_assignations.date or (core_tasks.date between core_assignations.date and core_assignations.db69743 )) AND (core_assignations.rec_archive IS NULL OR core_assignations.rec_archive = ''))";
				// $core_assignations_left_join= "LEFT JOIN core_assignations ON core_assignations.rel_id = core_students.id";
			}


			if($args['assigned_to_me']){
				$assigned_to_sql = " AND db1745 ='".$args['assigned_to_me']."' ";
    //         $locate_sql = " ,locate(concat(',', " . $args['assigned_to_me'] . ", ','), concat(',', db39484, ',')) as locate_reader,
				// locate(concat(',', ".$args['assigned_to_me'].", ','), concat(',', db1745, ',')) as locate_reviewer
				// ";

				// //reviewer active asignemnt
				// $assigned_to_reviewer="AND (core_assignations.db69836 = '".$args['assigned_to_me']."'  and ((core_assignations.db69743 is null or core_assignations.db69743 ='') and core_tasks.date>core_assignations.date or (core_tasks.date between core_assignations.date and core_assignations.db69743 )) AND (core_assignations.rec_archive IS NULL OR core_assignations.rec_archive = ''))";
				// $core_assignations_left_join= "LEFT JOIN core_assignations ON core_assignations.rel_id = core_students.id";
			}


			if($args['for_applicants_assigned_to_me']){
				$applicants_only = " AND db22444 ='Applicant' ";
            

				//reviewer active asignemnt
				if (in_array('reviews', $school_info['support_lead'])) {
				// $assigned_to_reviewer="AND (SELECT count(core_assignations.id) FROM  core_assignations where core_assignations.rel_id=core_students.id AND db69884='2' AND (core_assignations.db69836 = '".$args['for_applicants_assigned_to_me']."'  and ((core_assignations.db69743 is null or core_assignations.db69743 ='') and core_tasks.date>core_assignations.date or (core_tasks.date between core_assignations.date and core_assignations.db69743 )) AND (core_assignations.rec_archive IS NULL OR core_assignations.rec_archive = '')))>0";
				$assigned_to_reviewer="AND EXISTS (
						    SELECT 1
						    FROM core_assignations
						    WHERE core_assignations.rel_id = core_students.id
						      AND core_assignations.db69884 = '2'
						      AND core_assignations.db69836 = '".$args['for_applicants_assigned_to_me']."'
						      AND (
						          (core_assignations.db69743 IS NULL OR core_assignations.db69743 = '')
						          AND core_tasks.date > core_assignations.date
						          OR core_tasks.date BETWEEN core_assignations.date AND core_assignations.db69743
						      )
						      AND (core_assignations.rec_archive IS NULL OR core_assignations.rec_archive = '')
						)";
				}
				//$core_assignations_left_join= "LEFT JOIN core_assignations ON core_assignations.rel_id = core_students.id";
			}


			if($args['due_by'] =='today'){
            $duebytoday_sql = " AND (db1744 <= CURDATE() OR db40386 LIKE '%priority%')  AND db1744 NOT IN ('1970-01-01', '0000-00-00')";
			}

			if($args['due_by'] =='over_due'){
            $duebytoday_sql = " AND (db1744 < CURDATE()) AND db1744 NOT IN ('1970-01-01', '0000-00-00')";
			}

			if($args['due_by'] =='this_day'){
            $duebytoday_sql = " AND (db1744 = CURDATE() OR db40386 LIKE '%priority%') AND db1744 NOT IN ('1970-01-01', '0000-00-00')";
			}
        if (!$args['student_ids']) {
        	
			if($args['unread']){
                //$unread_sql = "AND (locate(concat(',', " .$_SESSION['uid'] . ", ','), concat(',', coalesce(db39484, '0'), ','))) = 0";
                $unread_sql = "AND FIND_IN_SET('" .$_SESSION['uid'] . "', db39484) = 0";
			}else{
                //$unread_sql = " AND (locate(concat(',', " . $_SESSION['uid']. ", ','), concat(',', db39484, ','))) > 0";
                 $unread_sql = "AND FIND_IN_SET('" .$_SESSION['uid'] . "', db39484) > 0";
            }
        }

			if($args['due_by'] =='soon'){//due in 2 days
				$duebytoday_sql = "AND db1744 >= CURDATE() + INTERVAL 1 DAY
						AND db1744 <= CURDATE() + INTERVAL 2 DAY
						AND db1744 NOT IN ('1970-01-01', '0000-00-00')";
			}

        if ($args['order']) {
        	$args['order']=str_replace('date', "db1744", $args['order']);
            $order_sql = "ORDER BY core_tasks." . $args['order'] . ", db40386 DESC";
        } else {
            $order_sql = "ORDER BY db40386 DESC, core_tasks.id ";
        }

        if($args['notifications'] == true || $args['messages_tab'] ==true){
            $date_limit_sql = " AND core_tasks.date >= '2019-10-01'";
            $notify_table = " AND form_notification_logs.db39395 = 'core_tasks' AND (form_notification_logs.rec_archive IS NULL OR form_notification_logs.rec_archive = '')";
        }

        if ($args['active_tasks']) {
        	$active_tasks_sql="AND db1746='active'";
        }


        $filter_sql="";
	    if (isset($args['filter_sql'])) {
	    	$filter_sql=$args['filter_sql'];
	    }

	    if (isset($args['assigned_to_me'])) {
	    	$locate_sql = " ,locate(concat(',', " . $args['assigned_to_me'] . ", ','), concat(',', db39484, ',')) as locate_reader,
				locate(concat(',', ".$args['assigned_to_me'].", ','), concat(',', db1745, ',')) as locate_reviewer
				";
	    }

	    if (isset($args['for_applicants_assigned_to_me'])) {
	    	$locate_sql = " ,locate(concat(',', " . $args['for_applicants_assigned_to_me'] . ", ','), concat(',', db39484, ',')) as locate_reader,
				locate(concat(',', ".$args['for_applicants_assigned_to_me'].", ','), concat(',', db1745, ',')) as locate_reviewer
				";
	    }

   
           $school_cyclesql="";


			if (!empty($args['school_cycle'])||! empty($_GET['cohort'])) {
				 if (!empty($args['school_cycle'])) {
				 	 $school_cyclesql=" AND db890='".$args['school_cycle']."'";
				 }

				 if (!empty($_GET['cohort'])&& $_GET['cohort'] != 'all_cohorts') {
				 	 $school_cyclesql=" AND db890='".$_GET['cohort']."'";
				 }
				
			}else{
                $school_cyclesql=" AND db890='".$_SESSION['school_cycle']."'";
			}



	    
				

			if($args['count']){
				$count_sql = " COUNT(core_tasks.id) as count ";
			}else{
				$count_sql = " 
				*,
				core_tasks.id as task_id,
				core_tasks.rec_id as user_id,
                core_tasks.db47669 as task_type,
				core_tasks.db1744 as task_date,
				core_tasks.date as created_at,
				core_tasks.rel_id as student_id,
		    	core_students.username_id AS student_username_id
		    	$locate_sql
		    	
		    	";
			}

			$query="
			SELECT
				$count_sql
			FROM
				core_tasks 
				LEFT JOIN form_users ON form_users.id = core_tasks.rec_id
				
				LEFT JOIN core_students ON core_students.id = core_tasks.rel_id
				LEFT JOIN dir_cohorts ON dir_cohorts.id = core_students.db1682
				$form_notification_logs_join
			WHERE
				1
				AND core_tasks.usergroup=".$_SESSION['usergroup']." 
				AND (core_tasks.rec_archive IS NULL OR core_tasks.rec_archive = '')
				$filter_sql
                $date_limit_sql
                $notify_table
				$id_sql
				$assigned_to_sql
				$unread_sql
				$student_id_sql
				$assigned_to_reviewer
				$duebytoday_sql
				$active_tasks_sql
				$school_cyclesql
				$applicants_only
				$order_sql
			";
			if(array_key_exists('paginate', $args) && $args['paginate']){
				$limit_sql = $paginator->limit_sql();
				$paginator->calculate_total_entries($query);
				$query = $query.$limit_sql;
			}
			// echo $query;
			// exit();
			dev_debug(__FUNCTION__." - ".$query);
			$sth = $dbh->prepare($query);
			$sth->execute();

			$tasks = array();
			if($args['count']){
				return  $sth->fetchAll(PDO::FETCH_ASSOC);
			}

			while($row_result = $sth->fetchAll()) {
				foreach($row_result as $row){
					if (isset($args['notifications_display'])) {
						$description="<strong>".$row['db1741']."</strong><br> </br>".$row['db1743']." <br> </br>Task Created On:".$row['created_at'];
					}else{
                        $description=$row['db1743'];
					}
					$tasks[] =  array(
						"id"=>$row['task_id'],
                    "user_id" => $row['user_id'],
						"date"=>$row['task_date'],
						"title"=>$row['db1741'],
						"student_id"=>$row['student_id'],
						"student_username_id"=>$row['student_username_id'],
						"email" => $row['db764'],
						"student_firstname"=>$row['db39'] ?: $row['db22444'],
						"student_lastname"=>$row['db40'],
						"task_description"=>$row['db1743'],
                    "task_type_custom"=> $row["task_type"],
						"description"=>$description,
						"due_by"=>$row['db1744'],
						"assigned_to"=>$row['db1745'],
						"status"=>$row['db1746'],
						"completion_date"=>$row['db1747'],
						"related_module"=>$row['db1749'],
						"closing_notes"=>$row['db1748'],
						"assign_to_applicant"=>$row['db22441'],
						"person_normally_responsible"=>$row['db22444'],
						"notify_applicant"=>$row['db22445'],
						"type"=>$row['db22580'],
						"parent_task"=>$row['db23146'],
						"task_type"=>$row['db23147'],
						"template_task_id"=>$row['db23148'],
						"automation"=>$row['db23149'],
						"confirmed_by_admin"=>$row['db24375'],
						"order"=>$row['db24376'],
						"tags"=>$row['db40386'],
						"link" => engine_url("/direct/proc?pg=4&vw=".$row['student_username_id']."&ref=".$row['student_id'].""),
						'manage_task_link'=>engine_url("/controller.php?width=700&height=500&vw=".$row['student_username_id']."&pick_page=148&ref=".$row['student_id']."&rec=".$row['task_id']."&jqmRefresh=true"),
						'mark_task_as_complete'=>true,
						'task_notes_downloads'=>engine_url("/tools/tasks/detail.php?id=".$row['task_id']."&ref=".$row['task_id']."&amp;width=850&amp;height=600&amp;jqmRefresh=false"),
						'task_delete_link'=>engine_url("/controller.php?pg=148&amp;rec=".$row['task_id']."&amp;del=".$row['task_id']."&amp;width=700&amp;height=500&amp;jqmRefresh=true"),
                    "read" => $row['locate_reader'] == 1 ? 'yes' : 'no',
                    "sent_by" => array(
                        'pic' => $pic,
                        'id' => $row['sent_by_id'],
                        'first_name' => $row['db106'],
                        'last_name' => $row['db111'],

                    ),
                    'intake'=>$row['db1681'],
                );
            }//end for each
        }//end while

        // if (isset($args['notifications_display'])) {
        //   echo "<pre>".print_r($tasks,1)."</pre>";
        // }
        return $tasks;
    }



    /** ===================================
     * Get Tasks Notes
     * ====================================    */
    function get_task_notes($args = array())
    {
        global $paginator;
        global $school_info;
        $dbh = get_dbh();

        if ($args['id']) {
            $id_sql = "AND core_task_notes.id='" . $args['id'] . "'";
        }

        if($args['school_id']){
			$school_id_sql = "AND core_task_notes.usergroup='".$args['school_id']."'";
		}else{
			$school_id_sql = "AND core_task_notes.usergroup='".$_SESSION['usergroup']."'";
		}

		if ($args['ulevel']) {
			if ('applicant'==$args['ulevel']) {
				$ulevelsql=" AND  form_users.db112=4 ";
			}else{
               $ulevelsql=" AND  form_users.db112!=4 ";
			}
		}

        if ($args['student_ids']) {
            $student_id_sql = "AND core_task_notes.rel_id IN(" . implode(",", $args['student_ids']) . ")";
        }

        if ($args['assigned_to_me']&&!isset($args['tasks_assigned_to']) ) {
        	if (in_array('reviews', $school_info['support_lead'])) {
            $assigned_to_sql = "AND (SELECT count(core_assignations.id) FROM  core_assignations where core_assignations.rel_id=core_students.id AND db69884='2' AND (core_assignations.db69836 = '".$args['assigned_to_me']."' and ((core_assignations.db69743 is null or core_assignations.db69743 ='') and core_task_notes.date>core_assignations.date  or (core_task_notes.date between core_assignations.date and core_assignations.db69743 )) AND (core_assignations.rec_archive IS NULL OR core_assignations.rec_archive = '')))>0";
            }
            $locate_sql = " ,locate(concat(',', " . $args['assigned_to_me'] . ", ','), concat(',', db39484, ',')) as locate_reader,
                (SELECT count(core_assignations.id) FROM  core_assignations where core_assignations.rel_id=core_students.id AND db69884='2' AND (core_assignations.db69836 = '".$args['assigned_to_me']."' and ((core_assignations.db69743 is null or core_assignations.db69743 ='') and core_task_notes.date>core_assignations.date  or (core_task_notes.date between core_assignations.date and core_assignations.db69743 )) AND (core_assignations.rec_archive IS NULL OR core_assignations.rec_archive = ''))) as locate_reviewer
                ";
        }



        if (!$args['student_ids']) {
            if ($args['unread']) {
                //$unread_sql = "AND (locate(concat(',', " . $_SESSION['uid']. ", ','), concat(',', coalesce(db39484,'0'), ',')) = 0)";
                $unread_sql = "AND FIND_IN_SET('" .$_SESSION['uid'] . "', db39484) = 0";
            } else {
                //$unread_sql = " AND (locate(concat(',', " .$_SESSION['uid'] . ", ','), concat(',', db39484, ','))) > 0";
                $unread_sql = "AND FIND_IN_SET('" .$_SESSION['uid'] . "', db39484) > 0";
            }
        }

        if($args['messages_tab'] == true){
            $date_limit_sql = " AND core_task_notes.date >= '2019-10-01'";
        }
        $tasks_assigned_to_sql ="";
        if ($args['tasks_assigned_to']) {
            $tasks_assigned_to_sql = "AND core_tasks.db1745='" . $args['tasks_assigned_to'] . "'";
        }
        if (!isset($args['return_sql'])) {
	        if ($args['order']) {
	            $order_sql = "ORDER BY core_task_notes.id " . $args['order'];
	        } else {
	            $order_sql = "ORDER BY  core_task_notes.id DESC";
	        }
	    }

         $filter_sql="";
	    if (isset($args['filter_sql'])) {
	    	$filter_sql=$args['filter_sql'];
	    }

        $school_cyclesql="";
		
		if($_GET['cohort'] != 'all_cohorts'){
			if (!empty($args['school_cycle'])||! empty($_GET['cohort'])) {
				if (!empty($args['school_cycle'])) {
					$school_cyclesql=" AND db890='".$args['school_cycle']."'";
				}
				
				if (!empty($_GET['cohort'])) {
					$school_cyclesql=" AND db890='".$_GET['cohort']."'";
				}
				
			}else{
				$school_cyclesql=" AND db890='".$_SESSION['school_cycle']."'";
			}
		}

        if ($args['count']) {
            $count_sql = " COUNT(core_task_notes.id) as count ";
        } elseif(isset($args['return_sql'])){
           $count_sql="
				core_tasks.id as task_id,
                form_users.db106,
				form_users.db111,
				'' as db139,
        		form_users.db1730,
        		'' as db54073,
				core_task_notes.id AS 'ticket_id',
				form_users.id AS 'sent_by_id',
				core_students.id AS 'student_id',
				core_students.username_id AS 'student_username_id',
				'9' as type_id,
				'' as entry_info,
				'' as type_name,
				'' as partner_id,
				'' as read_by,
				'' as 'subject', 
	    	db22727 as 'details', 
	    	core_task_notes.date AS date,
	    	UNIX_TIMESTAMP(core_task_notes.date) AS dateago,
	    	(SELECT concat(core_students.id,'|',core_students.username_id,'|',db232) FROM core_students,core_courses WHERE core_students.db889 = core_courses.id and core_students.id = core_tasks.rel_id) as core_students_data,
		'' as locate_reader,
		'' as locate_reviewer";
	    	 $order_sql="";
        } else {
            $count_sql = " 
                *,
                core_task_notes.id as conv_id,
                core_tasks.id as task_id,
                core_task_notes.rec_id as user_id,
                core_task_notes.date as task_date,
                core_students.id as student_id,
                core_students.username_id AS student_username_id
                $locate_sql
                ";
        }

        $query = "
            SELECT
                $count_sql
            FROM
                core_task_notes

                LEFT JOIN core_tasks on core_tasks.id= core_task_notes.rel_id
                 
                LEFT JOIN form_users ON form_users.id = core_task_notes.rec_id
                
                LEFT JOIN core_students ON core_students.id = core_tasks.rel_id
                LEFT JOIN dir_cohorts ON dir_cohorts.id = core_students.db1682


                LEFT JOIN form_notification_logs ON form_notification_logs.rel_id = core_task_notes.id 
                
            WHERE
                1
                AND core_task_notes.usergroup=".$_SESSION['usergroup']." 
                AND form_notification_logs.db39395 = 'core_task_notes' AND (form_notification_logs.rec_archive IS NULL OR form_notification_logs.rec_archive = '')
	    		AND (core_students.rec_archive IS NULL OR core_students.rec_archive ='')
	    		AND core_task_notes.rec_id <> '".$_SESSION['uid']."' 
	    		$ulevelsql
	    		$filter_sql
                $date_limit_sql
                $id_sql
                $tasks_assigned_to_sql
                $school_id_sql
                $assigned_to_sql
                $unread_sql
                $school_cyclesql
                $student_id_sql
                $order_sql
            ";
        if (isset($args['return_sql'])) {
        	return $query;
        }
        if (array_key_exists('paginate', $args) && $args['paginate']) {
            $limit_sql = $paginator->limit_sql();
            $paginator->calculate_total_entries($query);
            $query = $query . $limit_sql;
        }
        // echo $query;
        // exit();
        dev_debug(__FUNCTION__ . " - " . $query);
        $sth = $dbh->prepare($query);
        $sth->execute();

        $tasks = array();
        if ($args['count']) {
            return $sth->fetchAll(PDO::FETCH_ASSOC);
        }

        while ($row_result = $sth->fetchAll()) {
            foreach ($row_result as $row) {
                $tasks[] = array(
                    "id" => $row['conv_id'],
                    "user_id" => $row['user_id'],
                    "date" => $row['task_date'],
                    "task_id" => $row['task_id'],
                    "m_type" => 'task_notes',
                    "student_id" => $row['student_id'],
                    "student_username_id" => $row['student_username_id'],
                    "student_firstname" => $row['db39'],
                    "student_lastname" => $row['db40'],
                    "description" => $row['db22727'],
                    "link" => engine_url("/direct/proc?pg=4&vw=" . $row['student_username_id'] . "&ref=" . $row['student_id'] . ""),
                    "read" => $row['locate_reader'] == 1 ? 'yes' : 'no',
						"sent_by"=>array(
							'pic'=>$pic,
							'id'=>$row['sent_by_id'],
							'first_name'=>$row['db106'],
							'last_name'=>$row['db111'],
						),
					'intake'=>$row['db1681'],
					);
				}//end for each
			}//end while

			return $tasks;
		}


		/** ===================================
		 * Get Automated Events Files
     * ====================================    */
    function get_event_hooks_files($args = array())
    {
			$dbh = get_dbh();

			if($args['id']){
				$id_sql = "AND core_automated_event_files.id='".$args['id']."'";
			}

			$query="
			SELECT 
				*
			FROM
				core_automated_event_files 
			WHERE
				1
				$id_sql
			ORDER BY db22054 ASC";

			$sth = $dbh->prepare($query);
			$sth->execute();

			$actions = array();
			while($row_result = $sth->fetchAll()) {
				foreach($row_result as $row){

				$actions[] =  array(
					"id"=>$row['id'],
					"file_name"=>$row['db22054'],
				);

				}//end for each
			}//end while
			return $actions;
		}


		/** ===================================
		 * Get Automated Events categories
		====================================	*/
		function get_event_hooks_categories($args=array()){
			$dbh = get_dbh();

			if($args['id']){
				$id_sql = "AND core_automated_event_files.id='".$args['id']."'";
			}

			$query="
			SELECT 
				*
			FROM
				core_automated_event_files 
			WHERE
				1
				$id_sql
			ORDER BY db22054 ASC";

			$sth = $dbh->prepare($query);
			$sth->execute();

			$categories = array();
			while($row_result = $sth->fetchAll()) {
				foreach($row_result as $row){

				$categories[] =  array(
					"value"=>$row['id'],
					"lable"=>$row['db22054'],
				);

				}//end for each
			}//end while
			return $categories;
		}


		/** ===================================
		 * Get Normal Hooks
		====================================	*/
		function get($args=array()){
			$dbh = get_dbh();

			if($args['page_id']){
				$page_id_sql = "AND system_model_hooks.model_id='".$args['page_id']."'";
			}

			$query="
			SELECT 
				*
			FROM
				system_model_hooks 
				LEFT JOIN system_pages ON system_pages.page_id = system_model_hooks.id
			WHERE
				1
				$page_id_sql

			ORDER BY function_name ASC";

			$sth = $dbh->prepare($query);
			$sth->execute();

			$hooks_list = array();
			while($row_result = $sth->fetchAll()) {
				foreach($row_result as $row){
					$hook_type = '';
				if($row['hook_type']==0){
					$hook_type = '<span class="label label-primary">PRE</span> <span class="label label-warning">ANY</span>';
				}elseif($row['hook_type']==1){
					$hook_type = '<span class="label label-default">POST</span> <span class="label label-warning">ANY</span>';
				}elseif($row['hook_type']==2){
					$hook_type = '<span class="label label-primary">PRE</span> <span class="label label-success">INSERT</span>';
				}elseif($row['hook_type']==3){
					$hook_type = '<span class="label label-default">POST</span> <span class="label label-success">INSERT</span>';
				}elseif($row['hook_type']==4){
					$hook_type = '<span class="label label-primary">PRE</span> <span class="label label-info">UPDATE</span>';
				}elseif($row['hook_type']==5){
					$hook_type = '<span class="label label-default">POST</span> <span class="label label-info">UPDATE</span>';
				}elseif($row['hook_type']==6){
					$hook_type = '<span class="label label-primary">PRE</span> <span class="label label-danger">DELETE</span>';
				}elseif($row['hook_type']==7){
					$hook_type = '<span class="label label-default">POST</span> <span class="label label-danger">DELETE</span>';
				}

				#get hook code
				$file_path = $this->hooks_path."".$row['script_path'];
				//$code = file_get_contents($file_path);

				//echo ABSPATH;
				// echo $code;
				// exit();

				$hooks_list[] =  array(
					"id"=>$row['id'],
					"title"=>$row['function_name'],
					"script_path"=>$row['script_path'],
					"namespace"=>$row['namespace'],
					"order"=>$row['exec_order'],
					"model_id"=>$row['model_id'],
					"page_name"=>$row['page_name'],
					"type"=>$hook_type,
					//"code"=>$code,
				);
				}//end for each
			}//end while

			return $hooks_list;
		}


		/** ===================================
		 * Insert or Update Hooks
		====================================	*/
		function update_or_insert_event_hooks($args=array()){
			global $db;

			if($args->event_type=="time_related"){
				$event_details = array('time_type'=>$args->time_type,'date'=>$args->date,'time_period'=>$args->time_period,'time_period'=>$args->time_period);
				$args->details = json_encode($event_details);
			}

			if($args->event_type=="location"){
				$event_details = array('location'=>$args->location);
				$args->details = json_encode($event_details);
			}

			if($args->event_type=="data_change"){

				if($args->conditions_json){
					$args->conditions_json = str_replace("'", "", $args->conditions_json);
					$args->details = $args->conditions_json;
				}else{
					$list = $args->conditions;
					$args->details = json_encode($list);
				}
			}

			$hooks_info =  array(
				"db22034"=>$args->title,
				"db22035"=>$args->event_type,
				"db22036"=>$args->details
			);


			// print_r($hooks_info);


			// if($args->category=="New"){
			// 	//Create new category
			// 	$category_info = array('title'=>$args->new_category);
			// 	$db->insert('AAAAAAAAAAAAA',$category_info);
			// 	$args->category = $db->lastInsertId();
			// }

			if($args->id=="new" || $args->id==""){
				$db->system_table_insert_or_update('core_automated_event',$hooks_info);
				$hook_id = $db->lastInsertId();
			}else{
				$awhere = array("id"=>$args->id);
				$db->update('core_automated_event',$hooks_info,$awhere);
				$hook_id = $args->id;
			}
			if (!empty($args->conditions)) {
				foreach ($args->conditions as $condition) {
					$static = '{"ug":"' . $_SESSION['usergroup'] . '"}';
					$result = $db->query("select *from system_model_hooks where model_id=" . $condition->table . " and script_path='automated_hooks.php' and static='$static'");
					if (count($result) === 0) {
						$dbh = get_dbh();
						$stmt = $dbh->prepare("insert into system_model_hooks (model_id,hook_type, exec_order, script_path, namespace, function_name, static, mappings) values (?,?,?,?,?,?,?,?)");
						$stmt->execute([
							$condition->table,
							'0',
							'1',
							'automated_hooks.php',
							'automated_hooks',
							'automated_hooks',
							$static,
							'',
						]);
					}
				}
			}
			if (!empty($args->actions)) {
				//Delete Current Actions
				$where = array('db22048' => $args->id);
				$db->delete('core_automated_event_action', $where);

				//Insert Actions
				foreach ($args->actions as $action) {

					$action->fields_list = json_encode($action->fields_list);

					$action_info = array(
						"db22055" => $action->title,
						"db22038" => $action->type,
						"db22039" => $action->email_from,
						"db22040" => $action->email_to_type,
						"db22052" => $action->email_to,
						"db22041" => $action->language,
						"db22050" => $action->template,
						"db22051" => $action->fields_list,
						"db22042" => $action->subject,
						"db22043" => $action->message_type,
						"db22044" => $action->email,
						"db22045" => $action->sms,
						"db22046" => $action->notification,
						"db22047" => $action->letter,

						"db32505" => $action->task_id,
						"db32609" => $action->task_due_date,
						"db32610" => $action->task_status,
						"db32611" => $action->task_description,

						"db22048" => $hook_id
					);

					$awhere = array("id" => $action->id);
					$db->system_table_insert_or_update('core_automated_event_action', $action_info);
					$action_id = $db->lastInsertId();
				}
			}


			return $hook_id;
		}


		/** ===================================
		 * Save action
		====================================	*/
		function update_or_insert_task($args=array()){
			load_helper('db');
			$db = new Db_helper();
			$applicant_check = false;
            dev_debug("update_or_insert_task".print_r($args,1));
			$taks_args = array();
			if(array_key_exists('title', $args)){ $taks_args['db1741'] = $args['title'];}
			if(array_key_exists('task_description', $args)){ $taks_args['db1743'] = $args['task_description'];}
			if(array_key_exists('due_by', $args)){ $taks_args['db1744'] = $args['due_by'];}
			if(array_key_exists('assigned_to', $args)){ $taks_args['db1745'] = $args['assigned_to'];}
			if(array_key_exists('status', $args)){ $taks_args['db1746'] = $args['status'];}
			if(array_key_exists('completion_date', $args)){ $taks_args['db1747'] = $args['completion_date'];}
			if(array_key_exists('related_module', $args)){ $taks_args['db1749'] = $args['related_module'];}
			if(array_key_exists('closing_notes', $args)){ $taks_args['db1748'] = $args['closing_notes'];}
			if(array_key_exists('assign_to_applicant', $args)){ $taks_args['db22441'] = $args['assign_to_applicant'];}
			if(array_key_exists('person_normally_responsible', $args)){ $taks_args['db22444'] = $args['person_normally_responsible'];}
			if(array_key_exists('notify_applicant', $args)){ $taks_args['db22445'] = $args['notify_applicant'];}
			if(array_key_exists('type', $args)){ $taks_args['db22580'] = $args['type'];}
			if(array_key_exists('parent_task', $args)){ $taks_args['db23146'] = $args['parent_task'];}
			if(array_key_exists('task_type', $args)){ $taks_args['db23147'] = $args['task_type'];}
			if(array_key_exists('template_task_id', $args)){ $taks_args['db23148'] = $args['template_task_id'];}
			if(array_key_exists('automation', $args)){ $taks_args['db23149'] = $args['automation'];}
			if(array_key_exists('confirmed_by_admin', $args)){ $taks_args['db24375'] = $args['confirmed_by_admin'];}
		  if(array_key_exists('order', $args)){ $taks_args['db24376'] = $args['order'];}
		  if(array_key_exists('release_date', $args)){ $taks_args['db37730'] = $args['release_date'];}
		  if(array_key_exists('task_options', $args)){
		      if(!empty($args['task_options'])){
                  $task_options=\App\core\support\Collection::wrap(json_decode($args['task_options'],true));
                  $hide_upload_button=$task_options->where('title','Hide Upload Button')->first();
                  if(!empty($hide_upload_button)){
                      $taks_args['db52933'] = $hide_upload_button['value'];
                  }
              }
		  }


			if(array_key_exists('student_id', $args)){ $taks_args['rel_id'] = $args['student_id'];}




			if(array_key_exists('id', $args)){
				$taks_args['id'] =$args['id'];
				$task_id = $taks_args['id'];
			}else{

				$users = new Users;
				$af = new Students;
				//Get the applicant details
			  $applicant_args = array('id'=>$args['student_id']);
			  $applicant = $af->get($applicant_args); 
			  $market=pull_field("core_students","db510","WHERE id='".$args['student_id']."'");
              $lang = pull_field("form_languages","id","WHERE db21280='$market'");
				//Send the task email
				if($args['person_normally_responsible']=="Admin"){
					//Get the admin email address
					$users_args = array("id"=>$args['assigned_to'],'school_id'=>$_SESSION['usergroup']);
					$user_info = $users->get($users_args);
					$email_to = $user_info['email'];
                    
                   $select_email_template = pull_field("coms_template","id","WHERE usergroup='".session_info('usergroup')."' AND FIND_IN_SET('47',db1147) > 0 AND db47591='$lang'");
					//Get the EMAIL Template
					if(!$select_email_template){$select_email_template = pull_field("coms_template","id","WHERE usergroup='$_SESSION[usergroup]' AND FIND_IN_SET('47',db1147) > 0 Order By id ASC LIMIT 1");}
					if(!$select_email_template){$select_email_template = pull_field("coms_template","id","WHERE db1147=47 AND usergroup='1'"); }// select default
						
					//get template
					list($coms_template_id,$coms_template_rec_id,$coms_template_usergroup,$coms_template_rel_id,$coms_template_template_name,$coms_template_subject_line,$coms_template_plain_text_version,$coms_template_html_version,$coms_template_email_address_to_send_from)=get_coms_template($select_email_template);
					if (""!=$coms_template_html_version) {
						 $email_template=$coms_template_html_version;
					}else{
						 $email_template=terminology("Dear {{ user.first_name }}

					We have just added a task for you to complete in relation to an applicant called {{ applicant.first_name }} {{ applicant.last_name }}.
					Please follow the following link ({{ applicant.admin_profile_page_url }}) to view this applicant profile.

					Regards
					Admissions Office", curPageURL(), "Admin Task Notification",true);
						 $coms_template_subject_line="New task assigned to you";
					}
					//$email_template = "";
				}elseif($args['person_normally_responsible']=="Reviewer"){

					//Get the admin email address
					$users_args = array("id"=>$args['assigned_to'],'school_id'=>$_SESSION['usergroup']);
					$user_info = $users->get($users_args);
					$email_to = $user_info['email'];

					//Get the applicant email address
					//Get the EMAIL Template
					$select_email_template = pull_field("coms_template","id","WHERE usergroup='".session_info('usergroup')."' AND FIND_IN_SET('80',db1147) > 0 AND db47591='$lang'");
                    if(!$select_email_template){$select_email_template = pull_field("coms_template","id","WHERE usergroup='$_SESSION[usergroup]' AND FIND_IN_SET('80',db1147) > 0 Order By id ASC LIMIT 1");}
					if(!$select_email_template){$select_email_template = pull_field("coms_template","id","WHERE db1147=80 AND usergroup='1'"); }// select default
						
					//get template
					list($coms_template_id,$coms_template_rec_id,$coms_template_usergroup,$coms_template_rel_id,$coms_template_template_name,$coms_template_subject_line,$coms_template_plain_text_version,$coms_template_html_version,$coms_template_email_address_to_send_from)=get_coms_template($select_email_template);
					if (""!=$coms_template_html_version) {
						 $email_template=$coms_template_html_version;
					}else{
						 $email_template=terminology("Dear {{ user.first_name }}

						We have just added a task for you to complete in relation to an applicant called {{ applicant.first_name }} {{ applicant.last_name }}.
						Please follow the following link ({{ applicant.admin_profile_page_url }}) to view this applicant profile.

						Regards
						Admissions Office", curPageURL(), "Reviewer Task Notification",true);
						 $coms_template_subject_line="New task assigned to you";
					}
					//$email_template = "";
				}elseif($args['person_normally_responsible']=="Applicant"){
					$email_to = $applicant['email_address'];
					if ($email_to=="") {
						$email_to=pull_field("core_students", "db764", "WHERE id = $args[student_id]");
					}
					//Get the EMAIL Template
					$select_email_template = pull_field("coms_template","id","WHERE usergroup='".session_info('usergroup')."' AND FIND_IN_SET('77',db1147) > 0 AND db47591='$lang'");
                    if(!$select_email_template){  $select_email_template = pull_field("coms_template","id","WHERE usergroup='$_SESSION[usergroup]' AND FIND_IN_SET('77',db1147) > 0 Order By id ASC LIMIT 1"); }
					if(!$select_email_template){$select_email_template = pull_field("coms_template","id","WHERE db1147=77 AND usergroup='1'"); }// select default
						
					//get template
					list($coms_template_id,$coms_template_rec_id,$coms_template_usergroup,$coms_template_rel_id,$coms_template_template_name,$coms_template_subject_line,$coms_template_plain_text_version,$coms_template_html_version,$coms_template_email_address_to_send_from)=get_coms_template($select_email_template);
					if (""!=$coms_template_html_version) {
						 $email_template=$coms_template_html_version;
					}else{
						 $email_template=terminology("Dear {{ applicant.first_name }} 
						We have just added a task for you to complete in relation to your {{ applicant.course.title }} application
						Please follow the following link ({{ applicant_login_page_url }}) to login to your application portal.

						Regards
						Admissions Office", curPageURL(), "Applicant Task Notification",true);
						 $coms_template_subject_line="New task assigned to you";
					}

	               
                $applicant_check = true;
                /*$email_template = "Dear {{ applicant.first_name }}

We have just added a task for you to complete in relation to your {{ applicant.course.title }} application
Please follow the following link ({{ applicant_login_page_url }}) to login to your application portal.

Regards
Admissions Office";*/
				}


				//Apply the email variables
        $template_args = array(
          "email_html"=>$email_template,
          "student_id"=>$args["student_id"],
          "including_defaults"=>true
        );

        if(in_array($args['person_normally_responsible'], array('Reviewer','Admin'))){
        	$template_args['user_id'] = $args['assigned_to'];
        }

        $email_html = process_email_variables($template_args);

		//Check if the task has already been set
		$allow_new_task = false;
		$dbh= get_dbh();
        $tag = 'automated_task-'.$args['action_id'].'-'.$args['student_id'];
		$task_title = $taks_args['db1741'];
		$duplicates_task_check_sql = "SELECT * FROM core_tasks
		WHERE db1741 = '{$task_title}' AND rel_id= '{$args['student_id']}' and db1745 = '{$args['assigned_to']}'
		";
		dev_debug($duplicates_task_check_sql);
		$stmt = $dbh->prepare($duplicates_task_check_sql);
		$stmt->execute();
		$duplicates_task_check = $stmt->fetchAll(PDO::FETCH_ASSOC);
		
		$duplicates_email_check_sql = "
		SELECT * FROM form_email_log
		WHERE db1153 = '{$email_to}' and db1152 ='{$tag}' and rel_id= '{$args['student_id']}'
		";
		dev_debug($duplicates_email_check_sql);
		$stmt2 = $dbh->prepare($duplicates_email_check_sql);
		$stmt2->execute();
		$duplicates_email_check= $stmt2->fetchAll(PDO::FETCH_ASSOC);
//		$duplicates_task_check = $db->where("db1741",$task_title,"=")->where("rel_id",$args['student_id'])->where('db1745', $args['assigned_to'])->get('core_tasks');
        //$duplicates_email_check= $db->where("db1153",$email_to,"=")->where("db1152",$tag,"=")->where("rel_id",$args['student_id'],"=")->get("form_email_log");

        dev_debug("duplicates_email_check -".count($duplicates_email_check). " duplicates_task_check -".count($duplicates_task_check));
        if(empty($duplicates_email_check) && empty($duplicates_task_check)){

        	$allow_new_task = true;
					$emails = new Emails;
	        $email_args = array(
	            'to'=> $email_to,
	            // 'from'=>$from,
	            'subject'=> $coms_template_subject_line,
	            'text'=> $email_html,
	            'html'=> text_to_html($email_html),
	            'category'=> $tag,
	            'recipient_id'=> $args['student_id']
          );
	        dev_debug("email_args".print_r($email_args,1));
          if(date('Y-m-d')==$args['release_date']){
          	//CHECK IF TASK IS ASSIGNED TO APPLICANT AND IF NOTIFY IS ON
          	if($applicant_check==true){
          		if($args['task_notify_applicant']=='yes'){
          			$emails->send($email_args);
          		}
          	}
          	else{
          		$emails->send($email_args);		
          	}
          }
	     }
      }


			if($args['action_id'] && $allow_new_task){

				$db->system_table_insert_or_update('core_tasks',$taks_args);
				if(!array_key_exists('id', $args)){
					$task_id = $db->lastInsertId();
				}
			}

			return $task_id;
		}

		public function get_template($student_id, $tag_name, $usergroup=null){
			$usergroup = $usergroup??$_SESSION['usergroup']??1;

			$market = pull_field("core_students","db510","WHERE id='".$student_id."'");
			$lang = $market ? pull_field("form_languages","id","WHERE db21280='$market'") : false;
			$ra = "AND (rec_archive IS NULL OR rec_archive = '')";
			// check if the school has a template. if it does then use it otherwise select the default get languagd
			$template = $lang ? pull_field("coms_template","db1085","WHERE usergroup = {$usergroup} {$ra} AND db1320 = '{$tag_name}' AND db47591 = '$lang'") : false;

			//get english version instead
			if(!$template){
				$template = pull_field("coms_template","db1085","WHERE usergroup = {$usergroup} {$ra} AND db1320 = '{$tag_name}' AND db47591 = 1");
			}

			//just get a template (older setup compatibility)
			if(!$template){
				$template = pull_field("coms_template","db1085","WHERE usergroup = {$usergroup} {$ra} AND db1320 = '{$tag_name}' ");
			}

			if(!$template && $usergroup!=1 ){
				$template = pull_field("coms_template","db1085","WHERE db1320 = '{$tag_name}' {$ra} AND usergroup = 1");
			}
			return $template;
		}

		/** ===================================
		 * Save action
		====================================	*/
		function update_or_insert_action($args=array()){
			global $db;

			$action_info = array();
            //prepare translations
            //echo " args <pre>".print_r($args,1)."<pre>";

			if(array_key_exists('fields_list', $args)){ $action_info['db22051'] = json_encode($args['fields_list']);}
			if(array_key_exists('title', $args)){ $action_info['db22055'] = $args['title'];}
			if(array_key_exists('type', $args)){ $action_info['db22038'] = $args['type'];}
			if(array_key_exists('email_from', $args)){ $action_info['db22039'] = $args['email_from'];}
			if(array_key_exists('email_to_type', $args)){ $action_info['db22040'] = $args['email_to_type'];}
			if(array_key_exists('email_to', $args)){ $action_info['db22052'] = $args['email_to'];}
			if(array_key_exists('language', $args)){ $action_info['db22041'] = $args['language'];}
			if(array_key_exists('template', $args)){ $action_info['db22050'] = $args['template'];}
            if (array_key_exists('subject', $args)) {
                $action_info['db22042'] = !(empty($args['translations'][1])) ? $args['translations'][1]['db51136'] : $args['subject'];
            }
			if(array_key_exists('message_type', $args)){ $action_info['db22043'] = $args['message_type'];}
            if (array_key_exists('email', $args)) {
                $action_info['db22044'] = !(empty($args['translations'][1])) ? $args['translations'][1]['db51138'] : $args['email'];
            }
            if (array_key_exists('sms', $args)) {
                $action_info['db22045'] = !(empty($args['translations'][1])) ? $args['translations'][1]['db51138'] : $args['sms'];
            }
            if (array_key_exists('notification', $args)) {
                $action_info['db22046'] = !(empty($args['translations'][1])) ? $args['translations'][1]['db51138'] : $args['notification'];
            }
            if (array_key_exists('letter', $args)) {
                $action_info['db22047'] = !(empty($args['translations'][1])) ? $args['translations'][1]['db51138'] : $args['letter'];
            }
			if(array_key_exists('event_id', $args)){ $action_info['db22048'] = $args['event_id'];}

			if(array_key_exists('task_id', $args)){ $action_info['db32505'] = $args['task_id'];}
			if(array_key_exists('task_due_date', $args)){ $action_info['db32609'] = $args['task_due_date'];}
			if(array_key_exists('task_notify_applicant', $args)){ $action_info['db74312'] = $args['task_notify_applicant'];}
      if(array_key_exists('task_status', $args)){ $action_info['db32610'] = $args['task_status'];}
      if(array_key_exists('publish_days', $args)){ $action_info['db37736'] = $args['publish_days'];}
            if(array_key_exists('task_options', $args)){ $action_info['db52954'] = json_encode($args['task_options']);}
            if($action_info['db22043']=="task"){
				if(array_key_exists('task_description', $args)){
				    $action_info['db32611'] = $args['task_description'];
				}
			}
			else{
				if(array_key_exists('description', $args)){
				    $action_info['db32611'] = !(empty($args['translations'][1]))?$args['translations'][1]['db51138']:$args['description'];
				}
			}
			if(array_key_exists('resources', $args)){ $action_info['db35942'] = json_encode($args['resources']);}
			if(array_key_exists('resource_title', $args)){ $action_info['db22042'] = $args['resource_title'];}
			if($args['message_type'] == 'resources'){
				$action_info['db22038'] = $args['message_type'];
			}


			if(array_key_exists('id', $args)){
				$action_info['id'] =$args['id'];
				$action_id = $action_info['id'];
			}
			if(array_key_exists('url', $args)){ $action_info['db251930'] = $args['url'];}
			//print_r($action_info);
			//echo " action_info <pre>".print_r($action_info,1)."<pre>";
			$db->system_table_insert_or_update('core_automated_event_action',$action_info);
			//print_r($db);
			if(!array_key_exists('id', $args)){
				$action_id = $db->lastInsertId();
			}
            foreach ($args['translations'] as $key) {
                $key['rel_id'] = $action_id;
                $db->system_table_insert_or_update('form_event_translations', $key);
            }

			return $action_id;
		}

    /**
     * Grabs the include and calls a hook
     * @param $hook
     * @param $args
     * @return array
     */
    function call_hook($hook, $args){
			global $letter;
        error_log('Running hook function ' . $hook['namespace'] . '\\' . $hook['function_name']);
        require_once(dirname(__DIR__, 3) . '/engine/models/custom/' . $hook['script_path']);

        // Grab and process static and mapping arguments
        $static = json_decode($hook['static'], true) or array();

        // Manipulation of mappings
        $col_mappings = json_decode($hook['mappings'], true) or array();

        $mappings = array();
        foreach ($col_mappings as $key => $col_name)
            $mappings[$key] = $args[$col_name];


        // Call the hook function
        list($args, $mappings) = call_user_func(
            $hook['namespace'] . '\\' . $hook['function_name'],
            $args, $static, $mappings);

        // Put whatever changed in mappings back to $args and return
        foreach ($col_mappings as $key => $col_name)
            $args[$col_name] = $mappings[$key];

        return $args;
    }

    /**
     * Function to return pall hooks linked to a page
     * @param $page_id
     * @return array
     */
    function get_model_hooks($page_id)
    {
        $dbh = get_dbh();
        $sth = $dbh->prepare("SELECT * FROM system_model_hooks WHERE model_id=? ORDER BY exec_order");
        $sth->execute(array($page_id));
        return $sth->fetchAll();
    }

    function filter_hooks($hooks, $type)
    {
        return array_filter($hooks, function ($hook) use ($type) {
            return ($hook['hook_type'] == $type);
        });
    }

    /**
     * @param $model_hooks
     * @param $hook_id
     * @param $args
     * @param $msg_response
     * @return mixed
     */
    function call_hooks($model_hooks, $hook_id, $args, &$error)
    {
				global $letter;
        // echo '<pre>';
        // print_r($model_hooks);
        // echo '</pre>';
        // echo '***';
        foreach ($this->filter_hooks($model_hooks, $hook_id) as $hook) {
            $args = $this->call_hook($hook, $args);
            // msg is just simple message, nothing to worry about
            if (isset($args['msg'])) {
                $error .= $args['msg'] . "\n";
                unset($args['msg']);
            }
            // 'error' means finish hooks, but do not process further
            if (isset($args['error'])) {
                $error .= $args['error_msg'] . "\n";
                unset($args['error_msg']);
            }
            // 'stop' means critical error, do not process any further
            if (isset($args['stop'])) {
                $error .= $args['stop_msg'] . "\n";
                unset($args['stop_msg']);
                break;
            }
        }
        return $args;
    }


    
    function get_filter(){
    	$filter_sql="";
    	if(isset($_GET['intake'])&&$_GET['intake']!='all'){
           $filter_sql.=" AND db1682=".$_GET['intake'];
    	}

    	return $filter_sql;
    }


	}

?>
