<?php

/**
 * Courses
 */
class Courses
{

    private $studentsModel;

    public function __construct()
    {
        $this->studentsModel = new Students();
    }


    /**
     * This creates the course into the core_course table
     *
     */
    function insert_update_course($args)
    {
        global $db;

        if (!$args['state']) {
            $args['state'] = "on";
        }
        if (!$args['publish']) {
            $args['publish'] = "public";
        }


        $entry_info = array();


        if (!empty($args['fee'])) {
            $entry_info['db712'] = $args['fee'];
        } else {
            $entry_info['db712'] = '';
        }
        if (!empty($args['two_year_route'])) {
            $entry_info['db711'] = $args['two_year_route'];
        } else {
            $entry_info['db711'] = '';
        }


        if (!empty($args['course_level'])) {
            $entry_info['db341'] = $args['course_level'];
        } else {
            $school_type = pull_field('core_schools_type left join form_schools on core_schools_type.id = form_schools.db30', 'db277', "WHERE form_schools.id ='$_SESSION[usergroup]'");
            if ($school_type == 'MRN') {
                //select the default route for this school
                $entry_info['db341'] = pull_field('core_course_level', 'id', "WHERE usergroup ='$_SESSION[usergroup]' AND (rec_archive IS NULL OR rec_archive ='') ORDER BY id DESC LIMIT 1");
            } else {
                $entry_info['db341'] = '';
            }
        }
        if (!empty($args['state'])) {
            $entry_info['db340'] = $args['state'];
        } else {
            $entry_info['db340'] = '';
        }
        if (!empty($args['publish'])) {
            $entry_info['db235'] = $args['publish'];
        } else {
            $entry_info['db235'] = '';
        }

        if (!empty($args['abbreviation'])) {
            $entry_info['db233'] = $args['abbreviation'];
        } else {
            $entry_info['db233'] = '';
        }
        if (!empty($args['title'])) {
            $entry_info['db232'] = $args['title'];
        } else {
            $entry_info['db232'] = '';
        }


        //do not put any else here causes issues
        if (!empty($args['image'])) {
            $entry_info['db32206'] = $args['image'];
        }


        if (!empty($args['deadline_date'])) {
            $entry_info['db710'] = $args['deadline_date'];
        } else {
            $entry_info['db710'] = '';
        }
        if (!empty($args['references_deadline_date'])) {
            $entry_info['db237680'] = $args['references_deadline_date'];
        } else {
            $entry_info['db237680'] = '';
        }
		
		if (!empty($args['reference_deadline_time'])){
			$args['reference_deadline_time'] = str_replace(':', '', $args['reference_deadline_time']);
			$entry_info['db333796'] = $args['reference_deadline_time'];
		}
        if (!empty($args['age_group'])) {
            $entry_info['db22001'] = $args['age_group'];
        } else {
            $entry_info['db22001'] = '';
        }
        if (!empty($args['summary'])) {
            $entry_info['db234'] = $args['summary'];
        } else {
            $entry_info['db234'] = '';
        }
        if (!empty($args['start_date'])) {
            $entry_info['db29048'] = $args['start_date'];
        } else {
            $entry_info['db29048'] = '';
        }
        if (!empty($args['duration'])) {
            $entry_info['db29045'] = $args['duration'];
        } else {
            $entry_info['db29045'] = '';
        }
        if (!empty($args['participation_mode'])) {
            $entry_info['db29047'] = $args['participation_mode'];
        } else {
            $entry_info['db29047'] = '';
        }
        if (!empty($args['study_mode'])) {
            $entry_info['db29046'] = $args['study_mode'];
        } else {
            $entry_info['db29046'] = '';
        }
        if (!empty($args['internal_code'])) {
            $entry_info['db231'] = $args['internal_code'];
        } else {
            $entry_info['db231'] = '';
        }
        if (!empty($args['route'])) {
            $entry_info['db22005'] = $args['route'];
        } else {
            $school_type = pull_field('core_schools_type left join form_schools on core_schools_type.id = form_schools.db30', 'db277', "WHERE form_schools.id ='$_SESSION[usergroup]'");
            if ($school_type == 'MRN') {
                //select the default route for this school
                $entry_info['db22005'] = pull_field('dir_route_rules', 'id', "WHERE usergroup ='$_SESSION[usergroup]' AND (rec_archive IS NULL OR rec_archive ='') ORDER BY id DESC LIMIT 1");
            } else {
                $entry_info['db22005'] = '';
            }
        }
        if (!empty($args['department'])) {
            $entry_info['db25610'] = $args['department'];
        } else {
            $entry_info['db25610'] = '';
        }
        if (!empty($args['tutor_id'])) {
            $entry_info['db592'] = $args['tutor_id'];
        } else {
            $entry_info['db592'] = '';
        }
        if (!empty($args['youtube'])) {
            $entry_info['db32194'] = $args['youtube'];
        } else {
            $entry_info['db32194'] = '';
        }
        if (!empty($args['career_opportunities'])) {
            $entry_info['db32196'] = $args['career_opportunities'];
        } else {
            $entry_info['db32196'] = '';
        }
        if (!empty($args['entry_requirements'])) {
            $entry_info['db32195'] = $args['entry_requirements'];
        } else {
            $entry_info['db32195'] = '';
        }
        if (!empty($args['delivery_and_assesment'])) {
            $entry_info['db32197'] = $args['delivery_and_assesment'];
        } else {
            $entry_info['db32197'] = '';
        }
        if (!empty($args['testimonials'])) {
            $entry_info['db32504'] = $args['testimonials'];
        } else {
            $entry_info['db32504'] = '';
        }
        if (!empty($args['language'])) {
            $entry_info['db32199'] = $args['language'];
        } else {
            $entry_info['db32199'] = '';
        }
        if (!empty($args['program_inclusions'])) {
            $entry_info['db32198'] = $args['program_inclusions'];
        } else {
            $entry_info['db32198'] = '';
        }
        if (!empty($args['description'])) {
            $entry_info['db29050'] = $args['description'];
        } else {
            $entry_info['db29050'] = '';
        }
        if (!empty($args['hide_apply_button'])) {
            $entry_info['db33218'] = $args['hide_apply_button'];
        } else {
            $entry_info['db33218'] = '';
        }
        if (!empty($args['no_confirmed_tutors'])) {
            $entry_info['db67076'] = $args['no_confirmed_tutors'];
        } else {
            $entry_info['db67076'] = '';
        }
        if (!empty($args['uses_eoi_or_enquiries'])) {
            $entry_info['db86300'] = $args['uses_eoi_or_enquiries'];
        } else {
            $entry_info['db86300'] = '';
        }
        if (!empty($args['enquiry_form'])) {
            $entry_info['db86303'] = $args['enquiry_form'];
        } else {
            $entry_info['db86303'] = '';
        }
        if (!empty($args['url_name'])) {
            $entry_info['db90866'] = $args['url_name'];
        } else {
            $entry_info['db90866'] = '';
        }
        if (!empty($args['course_status'])) {
            $entry_info['db66752'] = $args['course_status'];
        } else {
            $entry_info['db66752'] = '';
        }
        if (!empty($args['course_qa_status'])) {
            $entry_info['db107036'] = $args['course_qa_status'];
        } else {
            $entry_info['db107036'] = '';
        }
        if (!empty($args['eval_form'])) {
            $entry_info['db111752'] = $args['eval_form'];
        } else {
            $entry_info['db111752'] = '';
        }
        if (!empty($args['credits'])) {
            $entry_info['db121283'] = $args['credits'];
        } else {
            $entry_info['db121283'] = '';
        }

        if (!empty($args['course_groups'])) {
            $entry_info['db66774'] = $args['course_groups'];
        } else {
            $entry_info['db66774'] = '';
        }

        if (!empty($args['core_course_type'])) {
            $entry_info['db66751'] = $args['core_course_type'];
        } else {
            $entry_info['db66751'] = '';
        }

        if (!empty($args['email_template_id'])) {
            $entry_info['db206927'] = $args['email_template_id'];
        } else {
            $entry_info['db206927'] = '';
        }

        if (!empty($args['enquiry_form_url'])) {
            $entry_info['db333799'] = $args['enquiry_form_url'];
        } else {
            $entry_info['db333799'] = '';
        }

        

        // echo '<pre>';
        // print_r($args);
        // exit();

        //leave as array key exist cause of times like 0000
        if (array_key_exists('deadline_time', $args)) {
            $args['deadline_time'] = str_replace(':', '', $args['deadline_time']);
            $entry_info['db709'] = $args['deadline_time'];
        }

        if (!empty($args['mandatory_course'])) {
            $entry_info['db66775'] = $args['mandatory_course'];
        } else {
            $entry_info['db66775'] = 'no';
        }

        if ($args['id']) {
            $where = array('id' => $args['id']);
            //before update check if status, publish and course status have changed or not
            $this->course_history($args);
            $db->update('core_courses', $entry_info, $where);
            $programme_id = $args['id'];
        } else {

            $db->system_table_insert_or_update('core_courses', $entry_info);
            $programme_id = $db->lastInsertId();
        }


        if (!array_key_exists('locations', $args)) {
            $where = array('rel_id' => $args['id']);
            $db->delete('core_course_locations_rel', $where);
        }
        if (array_key_exists('locations', $args)) {

            if ($args['id']) {
                //Clear current Locations
                $where = array('rel_id' => $args['id']);
                $db->delete('core_course_locations_rel', $where);
            }

            //Add the new locations
            foreach ($args['locations'] as $location) {
                if (array_key_exists('id', $location)) {
                    $location = $location['id'];
                }
                $location_args = array(
                    'rel_id' => $programme_id,
                    'db32202' => $location
                );

                $db->system_table_insert_or_update('core_course_locations_rel', $location_args);
            }
        }

        if (!array_key_exists('topics', $args)) {
            $where = array('rel_id' => $args['id']);
            $db->delete('core_course_topics_rel', $where);
        }

        if (!array_key_exists('requirements', $args)) {
            $where = array('rel_id' => $args['id']);
            $db->delete('app_course_assignment_rel', $where);
        }

        if (array_key_exists('topics', $args)) {
            if ($args['id']) {
                //Clear current Locations
                $where = array('rel_id' => $args['id']);
                $db->delete('core_course_topics_rel', $where);
            }

            //Add the new topics
            foreach ($args['topics'] as $topic) {
                if (array_key_exists('id', $topic)) {
                    $topic = $topic['id'];
                }
                $topic_args = array(
                    'rel_id' => $programme_id,
                    'db32203' => $topic
                );


                $db->system_table_insert_or_update('core_course_topics_rel', $topic_args);
            }
        }


        if (array_key_exists('requirements', $args)) {
            if ($args['id']) {
                //Clear current Locations
                $where = array('rel_id' => $args['id']);
                $db->delete('app_course_assignment_rel', $where);
            }

            //Add the new topics
            foreach ($args['requirements'] as $requirement) {
                if (array_key_exists('id', $requirement)) {
                    $requirement = $requirement['id'];
                }
                $requirement_args = array(
                    'rel_id' => $programme_id,
                    'db73250' => $requirement
                );


                $db->system_table_insert_or_update('app_course_assignment_rel', $requirement_args);
            }
        }


        if (array_key_exists('intakes', $args)) {

            if ($args['id']) {
                //Clear current intakes
                $where = array('db18719' => $args['id']);
                $db->delete('dir_cohorts', $where);
            }

            //Add the new intakes
            $students = new Students;
            foreach ($args['intakes'] as $intake) {
                $intake_args = $intake;
                $intake_args["course_id"] = $programme_id;
                $intake_id = $students->insert_update_cohort($intake_args);
            }
        }

        if (array_key_exists('course_sessions', $args)) {

            if ($args['id']) {
                //Clear current intakes
                $where = array('rel_id' => $args['id']);
                $db->delete('sis_course_sessions', $where);
            }

            //Add the new course_sessions
            $students = new Students;
            foreach ($args['course_sessions'] as $course_session) {
                if (array_key_exists('name', $course_session)) {
                    $entry_info['db59829'] = $course_session['name'];
                }
                if (array_key_exists('description', $course_session)) {
                    $entry_info['db59830'] = $course_session['description'];
                }
                if (array_key_exists('order', $course_session)) {
                    $entry_info['db59831'] = $course_session['order'];
                }
                $entry_info["rel_id"] = $programme_id;
                $course_session_id = $db->system_table_insert_or_update('sis_course_sessions', $entry_info);
            }
        }

        if (array_key_exists('course_materials', $args)) {

            if ($args['id']) {
                //Clear current materials
                $where = array('rel_id' => $args['id']);
                $db->delete('core_course_materials', $where);
            }

            //Add the new course_materials
            $students = new Students;
            foreach ($args['course_materials'] as $course_material) {
                if (array_key_exists('name', $course_material)) {
                    $entry_info['db63983'] = $course_material['name'];
                }
                if (array_key_exists('type', $course_material)) {
                    $entry_info['db63979'] = $course_material['type'];
                }
                if (array_key_exists('link', $course_material)) {
                    $entry_info['db63980'] = $course_material['link'];
                }
                if (array_key_exists('password', $course_material)) {
                    $entry_info['db63981'] = $course_material['password'];
                }
                if (array_key_exists('status', $course_material)) {
                    $entry_info['db63982'] = $course_material['status'];
                }
                $entry_info["rel_id"] = $programme_id;
                $course_material_id = $db->system_table_insert_or_update('core_course_materials', $entry_info);
            }
        }

        if (pull_field('form_schools', 'db30', "WHERE id ={$_SESSION['usergroup']}") == 12) {

            if ($args['id']) {
                //Clear current materials
                $where = array('rel_id' => $args['id']);
                $db->delete('core_course_meta', $where);
            }

            $courseManagementArgs = [
                'db1419' => $args['bookingPortalCondition'],
                'db1420' => $args['courseCondition'],
                'rel_id' => $programme_id
            ];

            if (!empty($args['bookingPortalCondition']) || !empty($args['courseCondition'])) {
                $db->system_table_insert_or_update('core_course_meta', $courseManagementArgs);
            }
        }

        return $programme_id;
    }


    /** ===================================
     * Departments
     * ====================================    */
    function get_departments($args)
    {
        global $db;
        $dbh = get_dbh();
        $cohort_sql = '';

        if ($args['school_id']) {
            $school_id_sql = "AND core_departments.usergroup='" . $args['school_id'] . "'";
        }

        if ($args['hod_user_id']) {
            $hod_id_sql = "AND core_departments.db29342 LIKE '%" . $args['hod_user_id'] . "%'";
        }


        if (array_key_exists('order', $args)) {

            $order = explode(" ", $args['order']);
            $order_field = $order[0];
            $order_type = $order[1];

            if (!$order_type) {
                $order_type = "ASC";
            }

            $order_sql = "ORDER BY " . $order_field . " " . $order_type;
        } else {
            $order_sql = "ORDER BY core_departments.id DESC";
        }

        if ($args['cohort']) {
            if ($args['cohort'] != "all") {
                $cohort_sql = "AND core_students.db890 = '" . $args['cohort'] . "'";
            }
        }

        if ($args['include_profiles_created']) {
            $profiles_created_sql = ",(
					SELECT count(DISTINCT core_students.id) as total_applicants
					FROM core_students
					LEFT JOIN form_schools ON form_schools.id = core_students.usergroup
					LEFT JOIN core_courses ON core_courses.id = core_students.db889
					LEFT JOIN core_departments ON core_departments.id = core_courses.db25610
					WHERE(core_students.rec_archive IS NULL OR core_students.rec_archive = '')
					AND(core_students.db135 != 'yes' OR core_students.db135 IS NULL)
					AND core_students.usergroup =  '" . $args['school_id'] . "'
					$cohort_sql
					AND core_departments.id = department_id
					) as total_applicants";
        }


        if ($args['include_total_submitted']) {
            $total_submitted_sql = ",(
					SELECT count(DISTINCT core_students.id) as total_applicants
					FROM core_students
					LEFT JOIN dir_appli_stages ON dir_appli_stages.id = core_students.db41
					LEFT JOIN dir_stage_tracker ON dir_stage_tracker.rel_id = core_students.id
					LEFT JOIN form_schools ON form_schools.id = core_students.usergroup
					LEFT JOIN core_courses ON core_courses.id = core_students.db889
					LEFT JOIN core_departments ON core_departments.id = core_courses.db25610
					WHERE(core_students.rec_archive IS NULL OR core_students.rec_archive = '')
					AND(core_students.db135 != 'yes' OR core_students.db135 IS NULL)
					AND core_students.usergroup =  '" . $args['school_id'] . "'
					$cohort_sql
					AND core_departments.id = department_id
					AND (dir_stage_tracker.rec_archive IS NULL OR dir_stage_tracker.rec_archive = '')
					AND dir_stage_tracker.db1142 = '12'
					) as total_submitted";
        }


        if ($args['all']) {
            $query = "
				SELECT *,
					core_departments.id as department_id,
					core_departments.usergroup as department_usergroup,
					core_departments.id as department_id
				$profiles_created_sql
				$total_submitted_sql
				FROM core_departments
				WHERE 1
				   	AND (core_departments.rec_archive IS NULL OR core_departments.rec_archive = '')
					$school_id_sql
					$hod_id_sql
					$id_sql
					GROUP BY core_departments.id
					$order_sql
				";
        } else {
            $query = "
				SELECT db25609,
				core_departments.id as department_id,
					core_departments.usergroup as department_usergroup,
					 core_departments.id as department_id FROM core_courses
				LEFT JOIN `core_departments` ON core_departments.id = core_courses.db25610
				WHERE 1
				 AND (core_departments.rec_archive IS NULL OR core_departments.rec_archive = '')
				  $school_id_sql $id_sql $hod_id_sql
				GROUP BY core_departments.id
				$order_sql";
        }

        // echo '<pre>';
        // 	echo $query;
        // 	exit();

        $applicants = new Students;

        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $results_list = array();
        foreach ($results as $row) {

            $department_info = array(
                'id' => $row['department_id'],
                'title' => $row['db25609']
            );

            if ($args['include_profiles_created']) {
                $department_info['total_applicants'] = $row['total_applicants'];
            }

            if ($args['include_total_submitted']) {
                $department_info['total_submitted'] = $row['total_submitted'];
            }


            if ($args['include_offers_made']) {
                $got_offer_count = $applicants->total_offers_count(array('cohort' => $args['cohort'], 'school_id' => $args['school_id'], 'department' => $row['department_id']));
                $department_info['total_offers'] = $got_offer_count;
            }

            if ($args['include_offers_accepted']) {
                $got_offer_count = $applicants->total_offers_count(array('cohort' => $args['cohort'], 'school_id' => $args['school_id'], 'department' => $row['department_id'], 'accepted' => 'yes'));
                $department_info['total_offers_accepted'] = $got_offer_count;
            }

            if ($args['include_enrolled']) {
                $enrolled_offer_count = $applicants->enrolled_count(array('cohort' => $args['cohort'], 'stage_in' => '8,9', 'school_id' => $args['school_id'], 'department' => $row['department_id']));
                $department_info['total_enrolled'] = $got_offer_count;
            }


            $results_list[] = $department_info;
        }

        if ($args['id'] || $args['title']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Delete Online Course
     * ====================================    */
    function delete_course($args)
    {
        global $db;

        if ($args['id']) {
            $where = array('id' => $args['id'], 'usergroup' => $_SESSION['usergroup']);
            $db->archive('core_courses', $where);
        }
    }

    /** ===================================
     * Venue
     * ====================================    */
    function get_venues($args)
    {
        global $db;
        $dbh = get_dbh();
        if ($args['school_id']) {
            $school_id_sql = "AND sis_course_venues.usergroup='" . $args['school_id'] . "'";
        }

        $query = "
				SELECT * FROM sis_course_venues
				WHERE 1  $school_id_sql $id_sql
				ORDER BY sis_course_venues.db14963 DESC";

        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $results_list = array();
        foreach ($results as $entry) {

            $results_list[] = array(
                'id' => $entry['id'],
                'title' => $entry['db14963'],
                'ref_number' => $entry['db14964'],
                'address' => $entry['db14965'],
                'post_code' => $entry['db14966'],
                'telephone' => $entry['db14967'],
                'instructions' => $entry['db14968'],
                'facilities' => $entry['db14969'],

            );
        }

        if ($args['id'] || $args['title']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Get Course Levels
     * ====================================    */
    function get_course_levels($args)
    {
        global $db;
        $dbh = get_dbh();
        if ($args['id']) {
            $id_sql = "AND core_course_level.id='" . $args['id'] . "'";
        }

        if ($args['title']) {
            $title_sql = "AND core_course_level.db343='" . $args['title'] . "'";
        }


        if ($args['filter_sql_lite']) {
            $filter_sql_lite = $args['filter_sql_lite'];
        }

        if ($args['school_id']) {
            $school_id_sql = "AND core_course_level.usergroup='" . $args['school_id'] . "'";
        }

        if ($args['limit']) {
            $limit_sql = " LIMIT " . $args['limit'] . "";
        }

        if ($args['cohort']) {
            if ($args['cohort'] != "all") {
                $cohort_sql = "AND core_students.db890 = '" . $args['cohort'] . "'";
            }
        }

        if ($args['search']) {
            $search_sql = "AND core_course_level.db343 LIKE '%" . $args['search'] . "%'";
        }

        if (array_key_exists('partner_id', $args)) {
            $unique_course_levels_sql = "SELECT * FROM core_courses LEFT JOIN core_partner_programmes ON core_partner_programmes.db33566 = core_courses.id AND (core_partner_programmes.rec_archive IS NULL OR core_partner_programmes.rec_archive = '')
				WHERE
					core_partner_programmes.db33565='" . $args['partner_id'] . "'
				";

            //For Student Count Querires
            $partner_programmes_left_join = "LEFT JOIN core_partner_programmes ON core_partner_programmes.db33566 = core_students.db889 AND (core_partner_programmes.rec_archive IS NULL OR core_partner_programmes.rec_archive = '')";
            $partner_id_sql = "AND core_partner_programmes.db33565='" . $args['partner_id'] . "'";
        }

        if (array_key_exists('show_based_on_partner_programmes', $args)) {
            $course_left_join = "LEFT JOIN core_courses ON core_courses.db341 = core_course_level.id";

            $partner_programmes_left_join_specific = "LEFT JOIN core_partner_programmes ON core_partner_programmes.db33566 = core_courses.id AND (core_partner_programmes.rec_archive IS NULL OR core_partner_programmes.rec_archive = '')";
            $partner_id_sql_specific = "AND core_partner_programmes.db33565='" . $args['show_based_on_partner_programmes'] . "'";
        }

        if (array_key_exists('order', $args)) {

            $order = explode(" ", $args['order']);
            $order_field = $order[0];
            $order_type = $order[1];

            if (!$order_type) {
                $order_type = "ASC";
            }

            if ($order_field == "level_name") {
                $order_field = "db343";
            }
            if ($order_field == "created") {
                $order_field = "total_applicants";
            }
            if ($order_field == "submitted") {
                $order_field = "total_submitted";
            }

            $order_sql = "ORDER BY " . $order_field . " " . $order_type;
        } else {
            $order_sql = "ORDER BY db343 ASC";
        }

        if ($args['include_profiles_created']) {

            $profiles_created_sql = ",(
					SELECT count(DISTINCT core_students.id) as total_applicants
					FROM core_students
					LEFT JOIN dir_appli_stages ON dir_appli_stages.id = core_students.db41
					LEFT JOIN dir_stage_tracker ON dir_stage_tracker.rel_id = core_students.id
					LEFT JOIN form_schools ON form_schools.id = core_students.usergroup
					LEFT JOIN core_courses ON core_courses.id = core_students.db889
					LEFT JOIN core_course_level ON core_course_level.id = core_courses.db341
					$partner_programmes_left_join
					WHERE(core_students.rec_archive IS NULL OR core_students.rec_archive = '')
					AND(core_students.db135 != 'yes' OR core_students.db135 IS NULL)
					AND core_students.usergroup =  '" . $args['school_id'] . "'
					$partner_id_sql
					$cohort_sql
					AND core_course_level.id = level_id
					) as total_applicants";

            $profiles_created_fv_sql = ",(
					SELECT count(DISTINCT core_students.id) as total_applicants_fv
					FROM core_students
					LEFT JOIN core_courses ON core_courses.id = core_students.db889
					LEFT JOIN core_course_level ON core_course_level.id = core_courses.db341
					$partner_programmes_left_join
					WHERE(core_students.rec_archive IS NULL OR core_students.rec_archive = '')
					AND(core_students.db135 != 'yes' OR core_students.db135 IS NULL)
					AND core_students.usergroup =  '" . $args['school_id'] . "'
					$cohort_sql
					$partner_id_sql
					AND core_course_level.id = level_id
					AND core_students.db41  = '0'
					) as total_applicants_fv";
        }

        if ($args['include_total_submitted']) {
            $total_submitted_sql = ",(
					SELECT count(DISTINCT core_students.id) as total_applicants
					FROM core_students
					LEFT JOIN dir_appli_stages ON dir_appli_stages.id = core_students.db41
					LEFT JOIN dir_stage_tracker ON dir_stage_tracker.rel_id = core_students.id
					LEFT JOIN form_schools ON form_schools.id = core_students.usergroup
					LEFT JOIN core_courses ON core_courses.id = core_students.db889
					LEFT JOIN core_course_level ON core_course_level.id = core_courses.db341
					$partner_programmes_left_join
					WHERE(core_students.rec_archive IS NULL OR core_students.rec_archive = '')
					AND(core_students.db135 != 'yes' OR core_students.db135 IS NULL)
					AND core_students.usergroup =  '" . $args['school_id'] . "'
					$cohort_sql
					$partner_id_sql
					AND core_course_level.id = level_id
					AND dir_stage_tracker.db1142 = '12'
					) as total_submitted";

            $total_submitted_fv_sql = ",(
					SELECT count(DISTINCT core_students.id) as total_applicants_fv
					FROM core_students
					LEFT JOIN core_courses ON core_courses.id = core_students.db889
					LEFT JOIN core_course_level ON core_course_level.id = core_courses.db341
					$partner_programmes_left_join
					WHERE(core_students.rec_archive IS NULL OR core_students.rec_archive = '')
					AND(core_students.db135 != 'yes' OR core_students.db135 IS NULL)
					AND core_students.usergroup =  '" . $args['school_id'] . "'
					$cohort_sql
					$partner_id_sql
					AND core_course_level.id = level_id
					AND core_students.db41 = '12'
					) as total_submitted_fv";
        }

        if ($args['include_total_rejected']) {
            $total_rejected_sql = ",(
					SELECT count(DISTINCT core_students.id) as total_applicants
					FROM core_students
					LEFT JOIN dir_appli_stages ON dir_appli_stages.id = core_students.db41
					LEFT JOIN dir_stage_tracker ON dir_stage_tracker.rel_id = core_students.id
					LEFT JOIN form_schools ON form_schools.id = core_students.usergroup
					LEFT JOIN core_courses ON core_courses.id = core_students.db889
					LEFT JOIN core_course_level ON core_course_level.id = core_courses.db341
					$partner_programmes_left_join
					WHERE(core_students.rec_archive IS NULL OR core_students.rec_archive = '')
					AND(core_students.db135 != 'yes' OR core_students.db135 IS NULL)
					AND core_students.usergroup =  '" . $args['school_id'] . "'
					$cohort_sql
					$partner_id_sql
					AND core_course_level.id = level_id
					AND dir_stage_tracker.db1142 = '-2'
					) as total_rejected";

            $total_rejected_fv_sql = ",(
					SELECT count(DISTINCT core_students.id) as total_applicants_fv
					FROM core_students
					LEFT JOIN core_courses ON core_courses.id = core_students.db889
					LEFT JOIN core_course_level ON core_course_level.id = core_courses.db341
					$partner_programmes_left_join
					WHERE(core_students.rec_archive IS NULL OR core_students.rec_archive = '')
					AND(core_students.db135 != 'yes' OR core_students.db135 IS NULL)
					AND core_students.usergroup =  '" . $args['school_id'] . "'
					$cohort_sql
					$partner_id_sql
					AND core_course_level.id = level_id
					AND core_students.db41 = '-2'
					) as total_rejected_fv";
        }

        if ($args['include_total_withdrawn']) {
            $total_withdrawn_sql = ",(
					SELECT count(DISTINCT core_students.id) as total_applicants
					FROM core_students
					LEFT JOIN core_courses ON core_courses.id = core_students.db889
					LEFT JOIN core_course_level ON core_course_level.id = core_courses.db341
					$partner_programmes_left_join
					WHERE(core_students.rec_archive IS NULL OR core_students.rec_archive = '')
					AND(core_students.db135 != 'yes' OR core_students.db135 IS NULL)
					AND core_students.usergroup =  '" . $args['school_id'] . "'
					$cohort_sql
					$partner_id_sql
					AND core_course_level.id = level_id
					AND core_students.db41 = '-1'
					) as total_withdrawn";
        }

        $query = "
			SELECT
				*,
				core_course_level.id as level_id
				$profiles_created_sql
				$total_submitted_sql
				$total_rejected_sql
				$profiles_created_fv_sql
				$total_submitted_fv_sql
				$total_rejected_fv_sql
				$total_withdrawn_sql
			FROM
				core_course_level
        LEFT JOIN form_schools ON form_schools.id = core_course_level.usergroup
        $course_left_join
        $partner_programmes_left_join_specific
			WHERE
        1
        AND (core_course_level.rec_archive IS NULL OR core_course_level.rec_archive = '')
				$search_sql
				$id_sql
				$title_sql
				$filter_sql_lite
        $school_id_sql
        $partner_id_sql_specific
        GROUP BY core_course_level.id
        $order_sql
				$limit_sq
			";

        if ($_GET['show_level_sql']) {
            echo '<pre>';
            print_r($query);
            echo '</pre>';
        }
        //exit();


        $results_list = array();
        $applicants = new Students;

        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($results as $row) {
            $level_info = array(
                "id" => $row['level_id'],
                "title" => $row['db343'],
                "order" => $row['db887'],
                "levels_year" => $args['cohort'],
                "short_course" => ($row['db16595'] == 'short_course' || $row['db16595'] == 'short') ? 'yes' : 'no'
            );

            if ($args['include_profiles_created']) {

                // $applicants_args = array('cohort'=>$args['cohort'],'school_id'=>$args['school_id'],'course_level_id'=>$row['level_id'],'total'=>true,'has_been_on_stage_id'=>0);
                // $applicants_results = $applicants->get($applicants_args);
                // $level_info['total_applicants'] = $applicants_results;
                $level_info['total_applicants'] = $row['total_applicants'];
                $level_info['total_applicants_fv'] = $row['total_applicants_fv'];
            }

            if ($args['include_total_submitted']) {
                // $applicants_args = array('cohort'=>$args['cohort'],'school_id'=>$args['school_id'],'course_level_id'=>$row['level_id'],'total'=>true,'has_been_on_stage_id'=>12);
                // $applicants_results = $applicants->get_from_view($applicants_args);
                // $level_info['total_submitted'] = $applicants_results;
                $level_info['total_submitted'] = $row['total_submitted'];
                $level_info['total_submitted_fv'] = $row['total_submitted_fv'];
            }


            if ($args['include_total_rejected']) {
                // $applicants_args = array('cohort'=>$args['cohort'],'school_id'=>$args['school_id'],'course_id'=>$row['course_id'],'total'=>true,'has_been_on_stage_id'=>12);
                // $applicants_results = $applicants->get($applicants_args);
                //$courser_info['total_submitted'] = $applicants_results;
                $level_info['total_rejected'] = $row['total_rejected'];
                //$level_info['total_rejected_fv'] = $row['total_rejected_fv'];
            }

            if ($args['include_total_withdrawn']) {
                // $applicants_args = array('cohort'=>$args['cohort'],'school_id'=>$args['school_id'],'course_id'=>$row['course_id'],'total'=>true,'has_been_on_stage_id'=>12);
                // $applicants_results = $applicants->get($applicants_args);
                //$courser_info['total_submitted'] = $applicants_results;
                $level_info['total_withdrawn'] = $row['total_withdrawn'];
            }

            if ($args['include_offers_made']) {
                $offers_count_args = array('cohort' => $args['cohort'], 'school_id' => $args['school_id'], 'course_level_id' => $row['level_id']);
                $got_offer_count = $applicants->total_offers_count($offers_count_args);
                $level_info['total_offers'] = $got_offer_count;

                $got_offer_count_fv = $applicants->total_offers_count(array('cohort' => $args['cohort'], 'school_id' => $args['school_id'], 'course_level_id' => $row['level_id'], 'funnel_view' => true));
                $level_info['total_offers_fv'] = $got_offer_count_fv;
            }

            if ($args['include_offers_accepted']) {
                $accept_offer_count = $applicants->total_offers_count(array('cohort' => $args['cohort'], 'school_id' => $args['school_id'], 'course_level_id' => $row['level_id'], 'accepted' => 'yes'));
                $level_info['total_offers_accepted'] = $accept_offer_count;

                $accept_offer_count_fv = $applicants->total_offers_count(array('cohort' => $args['cohort'], 'school_id' => $args['school_id'], 'course_level_id' => $row['level_id'], 'accepted' => 'yes', 'funnel_view' => true));
                $level_info['total_offers_accepted_fv'] = $accept_offer_count_fv;
            }

            if ($args['include_enrolled']) {
                $enrolled_offer_count = $applicants->enrolled_count(array('cohort' => $args['cohort'], 'stage_in' => '8,9', 'school_id' => $args['school_id'], 'course_level_id' => $row['level_id']));
                $level_info['total_enrolled'] = $enrolled_offer_count;
            }

            if ($_GET['enrolled']) die(json_encode($level_info));


            $results_list[] = $level_info;
        }

        if ($args['id'] || $args['title']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    function get_school_course_years($school_id)
    {
        global $db;
        $dbh = get_dbh();
        $query = "SELECT DISTINCT(db890) as year FROM `core_students` where usergroup =$school_id and db890<> '' order by year DESC";
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }


    /** ===================================
     * Get Courses
     * ====================================    */
    function get($args = array())
    {
        global $db, $paginator;
        $dbh = get_dbh();
        $online_learning = new OnlineLearning;
        $cohort_sql = $level_id_sql = $profiles_created_sql = $payments_join = $partner_programmes_left_join = '';
        $total_submitted_sql = $payments_sql = $total_withdrawn_sql = $total_rejected_sql = $total_rejected_fv_sql = '';
        $id_sql = $ids_in_sql = $no_route_sql = $title_sql = $title_filter_sql = $published_sql = '';
        $filter_sql_lite = $school_id_sql = $search_sql = $partner_id_sql = $department_id_sql = $department_in_sql = $intake_id_sql = '';

        if (!empty($args['id'])) {
            $args['id'] = (int)$args['id'];
            $id_sql = "AND core_courses.id=" . $args['id'];
        }
        if (array_key_exists('ids_in', $args) && !empty($args['ids_in'])) {
            $ids_in_sql = "AND core_courses.id in(" . implode(',', $args['ids_in']) . ")";
        }
        if (!empty($args['not_level_id'])) {
            $level_id_sql = "AND core_courses.db341!='" . $args['not_level_id'] . "'";
        }
        if (!empty($args['level_id'])) {
            $level_id_sql = "AND core_courses.db341='" . $args['level_id'] . "'";
        }

        if (!empty($args['title'])) {
            $title_sql = "AND core_courses.db232='" . $args['title'] . "'";
        }

        if (!empty($args['credits'])) {
            $title_sql = "AND core_courses.db121283='" . $args['credits'] . "'";
        }
        if (!empty($args['filter_sql_lite'])) {
            $filter_sql_lite = $args['filter_sql_lite'];
        }
        if (!empty($args['published'])) {
            $published_sql = "AND core_courses.db340 ='on'";
        }
        if (!empty($args['school_id'])) {
            $school_id_sql = "AND core_courses.usergroup=" . $args['school_id'];
        } else {
            $school_id_sql = "AND core_courses.usergroup=" . $_SESSION['usergroup'];
        }

        if (!empty($args['route_id'])) {
            $route_id_sql = "AND core_courses.db22005='" . $args['route_id'] . "'";
        }

        if (!empty($args['no_route'])) {
            $no_route_sql = "AND core_courses.db22005=''";
        }

        if (!empty($args['department_id'])) {
            $department_id_sql = "AND core_courses.db25610 = " . $args['department_id'] . "";
        }

        if (!empty($args['department_in'])) {
            $department_in_sql = "AND core_courses.db25610 IN (" . $args['department_id'] . ") ";
        }

        if (!empty($args['intake_id'])) {
            $intake_id_sql = "AND core_students.db19356='" . $args['intake_id'] . "' ";
        }

        if (!empty($args['cohort'])) {
            if ($args['cohort'] != "all") {
                $cohort_sql = "AND core_students.db890 = '" . $args['cohort'] . "'";
            }
        }

        if (!empty($args['payments'])) {
            $payments_join = "LEFT JOIN form_payment_options ON form_payment_options.rel_id = core_courses.id";
            $payments_sql = ", form_payment_options.id AS form_payment_options_id ";
        }

        if (!empty($args['search'])) {
            $search_sql = "AND core_courses.db232 LIKE '%" . $args['search'] . "%'";
        }

        if (!empty($args['partner_id'])) {
            $partner_programmes_left_join = "LEFT JOIN core_partner_programmes ON core_partner_programmes.db33566 = core_courses.id AND (core_partner_programmes.rec_archive IS NULL OR core_partner_programmes.rec_archive = '')";
            $partner_id_sql = "AND core_partner_programmes.db33565='" . $args['partner_id'] . "'";
        }

        if (array_key_exists('order', $args) && !empty($args['order'])) {

            $order = explode(" ", $args['order']);
            $order_field = $order[0];
            $order_type = $order[1];

            if (empty($order_type)) {
                $order_type = "ASC";
            }
            if ($order_field == "id") {
                $order_field = "core_courses.id";
            }
            if ($order_field == "title") {
                $order_field = "db232";
            }
            if ($order_field == "programme_name") {
                $order_field = "db232";
            }
            if ($order_field == "publish") {
                $order_field = "db235";
            }
            if ($order_field == "course_level") {
                $order_field = "db341";
            }
            if ($order_field == "created") {
                $order_field = "total_applicants";
            }
            if ($order_field == "department") {
                $order_field = "db25609";
            }
            if ($order_field == "abbreviation") {
                $order_field = "db233";
            }
            if ($order_field == "submitted") {
                $order_field = "total_submitted";
            }
            if ($order_field == "terms") {
                $order_field = "terms_order";
            }
            if ($order_field == "credits") {
                $order_field = "db121283";
            }
            $order_sql = "ORDER BY " . $order_field . " " . $order_type;
        } else {
            $order_sql = "ORDER BY db232 ASC";
        }


        if (!empty($args['include_profiles_created'])) {
            $profiles_created_sql = ",(
					SELECT count(DISTINCT core_students.id) as total_applicants
					FROM core_students
					LEFT JOIN dir_appli_stages ON dir_appli_stages.id = core_students.db41
					LEFT JOIN dir_stage_tracker ON dir_stage_tracker.rel_id = core_students.id
					LEFT JOIN form_schools ON form_schools.id = core_students.usergroup
					LEFT JOIN core_courses ON core_courses.id = core_students.db889
					WHERE(core_students.rec_archive IS NULL OR core_students.rec_archive = '')
					AND(core_students.db135 != 'yes' OR core_students.db135 IS NULL)
					AND core_students.usergroup =  '" . $args['school_id'] . "'
					$cohort_sql
					$level_id_sql
					AND core_courses.id = course_id
					) as total_applicants";

            $profiles_created_fv_sql = ",(
					SELECT count(DISTINCT core_students.id) as total_applicants_fv
					FROM core_students
					LEFT JOIN core_courses ON core_courses.id = core_students.db889
					WHERE(core_students.rec_archive IS NULL OR core_students.rec_archive = '')
					AND(core_students.db135 != 'yes' OR core_students.db135 IS NULL)
					AND core_students.usergroup =  '" . $args['school_id'] . "'
					$cohort_sql
					$level_id_sql
					AND core_courses.id = course_id
					AND core_students.db41 = '0'
					) as total_applicants_fv";
        }


        if (!empty($args['include_total_submitted'])) {
            $total_submitted_sql = ",(
					SELECT count(DISTINCT core_students.id) as total_applicants
					FROM core_students
					LEFT JOIN dir_appli_stages ON dir_appli_stages.id = core_students.db41
					LEFT JOIN dir_stage_tracker ON dir_stage_tracker.rel_id = core_students.id
					LEFT JOIN form_schools ON form_schools.id = core_students.usergroup
					LEFT JOIN core_courses ON core_courses.id = core_students.db889
					WHERE(core_students.rec_archive IS NULL OR core_students.rec_archive = '')
					AND(core_students.db135 != 'yes' OR core_students.db135 IS NULL)
					AND core_students.usergroup =  '" . $args['school_id'] . "'
					$cohort_sql
					$level_id_sql
					AND core_courses.id = course_id
					AND (dir_stage_tracker.rec_archive IS NULL OR dir_stage_tracker.rec_archive = '')
					AND dir_stage_tracker.db1142 = '12'
					) as total_submitted";

            $total_submitted_fv_sql = ",(
					SELECT count(DISTINCT core_students.id) as total_applicants_fv
					FROM core_students
					LEFT JOIN core_courses ON core_courses.id = core_students.db889
					WHERE(core_students.rec_archive IS NULL OR core_students.rec_archive = '')
					AND(core_students.db135 != 'yes' OR core_students.db135 IS NULL)
					AND core_students.usergroup =  '" . $args['school_id'] . "'
					$cohort_sql
					$level_id_sql
					AND core_courses.id = course_id
					AND core_students.db41 = '12'
					) as total_submitted_fv";
        }

        if (!empty($args['include_total_rejected'])) {
            $total_rejected_sql = ",(
					SELECT count(DISTINCT core_students.id) as total_applicants
					FROM core_students
					LEFT JOIN dir_stage_tracker ON dir_stage_tracker.rel_id = core_students.id
					LEFT JOIN core_courses ON core_courses.id = core_students.db889
					WHERE(core_students.rec_archive IS NULL OR core_students.rec_archive = '')
					AND(core_students.db135 != 'yes' OR core_students.db135 IS NULL)
					AND core_students.usergroup =  '" . $args['school_id'] . "'
					$cohort_sql
					$level_id_sql
					AND core_courses.id = course_id
					AND dir_stage_tracker.db1142 = '-2'
					) as total_rejected";

            $total_rejected_fv_sql = ",(
					SELECT count(DISTINCT core_students.id) as total_applicants_fv
					FROM core_students
					LEFT JOIN dir_stage_tracker ON dir_stage_tracker.rel_id = core_students.id
					LEFT JOIN core_courses ON core_courses.id = core_students.db889
					WHERE(core_students.rec_archive IS NULL OR core_students.rec_archive = '')
					AND(core_students.db135 != 'yes' OR core_students.db135 IS NULL)
					AND core_students.usergroup =  '" . $args['school_id'] . "'
					$cohort_sql
					$level_id_sql
					AND core_courses.id = course_id
					AND core_students.db41 = '-2'
					) as total_rejected_fv";
        }

        if (!empty($args['include_total_withdrawn'])) {
            $total_withdrawn_sql = ",(
					SELECT count(DISTINCT core_students.id) as total_applicants
					FROM core_students
					LEFT JOIN dir_stage_tracker ON dir_stage_tracker.rel_id = core_students.id
					LEFT JOIN form_schools ON form_schools.id = core_students.usergroup
					LEFT JOIN core_courses ON core_courses.id = core_students.db889
					WHERE(core_students.rec_archive IS NULL OR core_students.rec_archive = '')
					AND(core_students.db135 != 'yes' OR core_students.db135 IS NULL)
					AND core_students.usergroup =  '" . $args['school_id'] . "'
					$cohort_sql
					$level_id_sql
					AND core_courses.id = course_id
					AND dir_stage_tracker.db1142 = '-1'
					) as total_withdrawn";
        }

        $query = "
			SELECT
				*,
				core_courses.id as course_id,
				(SELECT group_concat(db32333) from dir_cohorts WHERE dir_cohorts.usergroup='" . $_SESSION['usergroup'] . "' AND dir_cohorts.db18719=core_courses.id) as terms_order,
				core_courses.usergroup as course_usergroup
				$profiles_created_sql
				$total_submitted_sql
				$payments_sql
				$total_withdrawn_sql
				$total_rejected_sql
				$total_rejected_fv_sql
			FROM
				core_courses
				LEFT JOIN core_course_level ON core_course_level.id =  CAST(core_courses.db341  AS UNSIGNED)
				LEFT JOIN form_schools ON form_schools.id = core_courses.usergroup
				LEFT JOIN `core_departments` ON core_departments.id = CAST(core_courses.db25610 AS UNSIGNED)
				$payments_join
				$partner_programmes_left_join
			WHERE
				(core_courses.rec_archive IS NULL or core_courses.rec_archive = '')
				$id_sql
				$ids_in_sql
				$level_id_sql
				$no_route_sql
				$title_sql
				$title_filter_sql
				$published_sql
				$filter_sql_lite
				$school_id_sql
				$search_sql
				$partner_id_sql
				$department_id_sql
				$department_in_sql
				$intake_id_sql
				$order_sql";

        $limit_sql = '';

        if (!empty($args['paginate'])) {
            $limit_sql = $paginator->limit_sql();
            $paginator->calculate_total_entries($query);
        }

        if (!empty($args['limit'])) {
            $limit_sql = " LIMIT " . $args['limit'];
        }

        $query = $query . $limit_sql;

        if (!empty($_GET['show_courses_sql'])) {
            echo '<pre>';
            echo $query;
            echo '</pre>';
        }

        $route = new Routes;
        $students = $this->studentsModel;

        $results_list = array();
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($results as $row) {
            $programme_image='';
            if (!empty($row['db32206'])) {
                $programme_image = STATIC_MEDIA_URL . $row['db32206'];
            }


			if (!empty($args['for_filter_only']) || !empty($_GET['export_data'])) {
				$courser_info = array(
					"id" => $row['course_id'],
					"title" => $row['db232']
				);
			} else {
				//cant use not empty because  0000  is evaluated as empty
				if ($row['db709'] != ''){
					$deadlineTime = str_split($row['db709'], 2);
					$row['db709'] = $deadlineTime[0].':'.($deadlineTime[1]??'');
				}
				
				if ($row['db333796'] != ''){
					$reference_deadlineTime = str_split($row['db333796'], 2);
					$row['db333796'] = $reference_deadlineTime[0].':'.$reference_deadlineTime[1];
				}
                $eval_forms_stmt = $dbh->prepare("SELECT id, name FROM system_forms WHERE system_forms.category='14' AND usergroup={$_SESSION['usergroup']}");
                $eval_forms_stmt->execute();
                $eval_forms = $eval_forms_stmt->fetchAll(PDO::FETCH_ASSOC);

                $course_age_groups_stmt = $dbh->prepare("SELECT id, db44830 as `name` FROM core_age_group WHERE (rec_archive is null or rec_archive = '') AND usergroup={$_SESSION['usergroup']}");
                $course_age_groups_stmt->execute();
                $course_age_groups = $course_age_groups_stmt->fetchAll(PDO::FETCH_ASSOC);

                $course_types_stmt = $dbh->prepare("SELECT id, db242 as `name` FROM core_course_category WHERE (rec_archive is null or rec_archive = '') AND usergroup={$_SESSION['usergroup']}");
                $course_types_stmt->execute();
                $course_types = $course_types_stmt->fetchAll(PDO::FETCH_ASSOC);
                $eoi_forms_stmt = $dbh->prepare("SELECT id, name FROM system_forms WHERE system_forms.category='12' AND usergroup={$_SESSION['usergroup']}");
                $eoi_forms_stmt->execute();
                $eoi_forms = $eoi_forms_stmt->fetchAll(PDO::FETCH_ASSOC);
                $enquiry_forms_stmt = $dbh->prepare("SELECT id, name FROM system_forms WHERE system_forms.category='2' AND usergroup={$_SESSION['usergroup']}");
                $enquiry_forms_stmt->execute();
                $enquiry_forms = $enquiry_forms_stmt->fetchAll(PDO::FETCH_ASSOC);

				$courser_info = array(
					"id" => $row['course_id'],
					"title" => $row['db232'],
					"usergroup_id" => $row['course_usergroup'],
					"school" => $row['db27'],
					"tutor_id" => $row['db592'],
					"image" => $programme_image,
					"image_name" => $row['db32206'],
					"payment_id" => $row['form_payment_options_id'] ?? false,
					"category" => $row['db231'],
					"abbreviation" => $row['db233'],
					"publish" => $row['db235'],
					"course_level_id" => $row['db341'],
					"course_level" => $row['db343'],
					"course_type" => $row['db16595'],
					"uses_eoi_or_enquiries" => is_null($row['db86300'])? '' : $row['db86300'],
					"enquiry_form" => is_null($row['db86303']) ? '': $row['db86303'],
					"department" => array(
						'id' => $row['db25610'],
						'title' => $row['db25609'],
					),
					"eoi_forms" => $eoi_forms,
					"enquiry_forms" => $enquiry_forms,
					"route" => $route->get(array('id' => $row['db22005'])),
					"short_course" => ($row['db16595'] == 'short_course' || $row['db16595'] == 'short') ? 'yes' : 'no',
					"state" => $row['db340'],
					"three_year_route" => $row['db710'],
					"two_year_route" => $row['db711'],
					"fee" => $row['db712'],
					"age_group" => $row['db22001'],
					"internal_code" => $row['db231'],

                    "hide_apply_button" => $row['db33218'],

                    "start_date" => $row['db29048'],
                    "deadline_date" => $row['db710'],
                    "references_deadline_date" => $row['db237680'],
                    "deadline_time" => $row['db709'],
                    "reference_deadline_time" => $row['db333796'],
                    "study_mode" => is_null($row['db29046']) ? '' : $row['db29046'],
                    "participation_mode" => $row['db29047'],
                    "duration" => $row['db29045'],
                    "location" => $row['db32194'],
                    'summary' => $row['db234'],
                    'program_inclusions' => $row['db32198'],
                    'language' => $row['db32199'],
                    'delivery_and_assesment' => $row['db32197'],
                    'entry_requirements' => $row['db32195'],
                    'career_opportunities' => $row['db32196'],
                    'testimonials' => $row['db32504'],
                    'url_name' => $row['db90866'],
                    'youtube' => $row['db32194'],
                    'no_confirmed_tutors' => $row['db67076'],
                    'intakes' => $students->get_cohorts(array("course_id" => $row['course_id'], 'present_and_future_only' => $args['present_and_future_only']??false, 'client_archive' => 'yes', 'term' => (!empty($args['term']) ? $args['term'] : ''), 'published' => (!empty($args['published']) ? $args['published'] : ''))),
                    'topics' => $this->get_topics(array("course_id" => $row['course_id'])),
                    'requirements' => $this->get_requirements(array("course_id" => $row['course_id'])),
                    'locations' => $this->get_locations(array("course_id" => $row['course_id'])),
                    'course_status' => $row['db66752'],
                    'course_qa_status' => $row['db107036'],
                    "eval_form" => is_null($row['db111752']) ? '' : $row['db111752'],
                    "eval_forms" => $eval_forms,//$dbh->query("SELECT id, name FROM system_forms WHERE system_forms.category='14' AND usergroup={$_SESSION['usergroup']}")->fetch(PDO::FETCH_ASSOC),
                    "credits" => $row['db121283'],
                    "course_groups" => $row['db66774'],
                    "mandatory_course" => is_null($row['db66775']) ? 'no' : $row['db66775'],
                    "chosen_email_template_id" => $row['db206927'],
                    "course_age_groups" => $course_age_groups,//$dbh->query("SELECT id, db44830 as `name` FROM core_age_group WHERE (rec_archive is null or rec_archive = '') AND usergroup={$_SESSION['usergroup']}")->fetch(PDO::FETCH_ASSOC),
                    "courseCondition" => pull_field('core_course_meta', 'db1420', "WHERE rel_id = {$row['course_id']}"),
                    "selectField" => pull_field('core_course_meta', 'db1419', "WHERE rel_id = {$row['course_id']}"),
                    "core_course_type" => is_null($row['db66751']) ? '' : $row['db66751'],
                    "courseTypes" => $course_types,//$dbh->query("SELECT id, db242 as `name` FROM core_course_category WHERE (rec_archive is null or rec_archive = '') AND usergroup={$_SESSION['usergroup']}")->fetch(PDO::FETCH_ASSOC),
                    "enquiry_form_url" => $row['db333799'],
                );
                if ($courser_info['short_course'] == 'yes') {
                    $courser_info['course_sessions'] = $this->get_course_sessions(array("course_id" => $row['course_id']));
                    $courser_info['course_materials'] = $this->get_course_materials(array("course_id" => $row['course_id']));
                    $courser_info['course_material_types'] = $this->get_course_material_types(array('school_id' => $_SESSION['usergroup']));
                    $courser_info['course_material_statuses'] = array("active", "inactive");
                }
            }
            if (!empty($args['process_shortcodes'])) {
                $courser_info['description'] = $online_learning->proccess_short_codes($row['db29050']);
            } else {
                $courser_info['description'] = $row['db29050'];
            }


            $applicants = $this->studentsModel;

            if (!empty($args['include_profiles_created'])) {
                // $applicants_args = array('cohort'=>$args['cohort'],'school_id'=>$args['school_id'],'course_id'=>$row['course_id'],'total'=>true,'has_been_on_stage_id'=>0);
                // $applicants_results = $applicants->get($applicants_args);
                // $courser_info['total_applicants'] = $applicants_results;
                $courser_info['total_applicants'] = $row['total_applicants'];
                $courser_info['total_applicants_fv'] = $row['total_applicants_fv'];
            }

            if (!empty($args['include_total_submitted'])) {
                // $applicants_args = array('cohort'=>$args['cohort'],'school_id'=>$args['school_id'],'course_id'=>$row['course_id'],'total'=>true,'has_been_on_stage_id'=>12);
                // $applicants_results = $applicants->get($applicants_args);
                //$courser_info['total_submitted'] = $applicants_results;
                $courser_info['total_submitted'] = $row['total_submitted'];
                $courser_info['total_submitted_fv'] = $row['total_submitted_fv'];
            }

            if (!empty($args['include_total_rejected'])) {
                // $applicants_args = array('cohort'=>$args['cohort'],'school_id'=>$args['school_id'],'course_id'=>$row['course_id'],'total'=>true,'has_been_on_stage_id'=>12);
                // $applicants_results = $applicants->get($applicants_args);
                //$courser_info['total_submitted'] = $applicants_results;
                $courser_info['total_rejected'] = $row['total_rejected'];
                //$level_info['total_rejected_fv'] = $row['total_rejected_fv'];
            }

            if (!empty($args['include_total_withdrawn'])) {
                // $applicants_args = array('cohort'=>$args['cohort'],'school_id'=>$args['school_id'],'course_id'=>$row['course_id'],'total'=>true,'has_been_on_stage_id'=>12);
                // $applicants_results = $applicants->get($applicants_args);
                //$courser_info['total_submitted'] = $applicants_results;
                $courser_info['total_withdrawn'] = $row['total_withdrawn'];
            }

            if (!empty($args['include_offers_made'])) {
                $offers_made_args = array('cohort' => $args['cohort'], 'school_id' => $args['school_id'], 'course' => $row['course_id']);
                if (!empty($args['level_id'])) {
                    $offers_made_args['course_level_id'] = $args['level_id'];
                }
                $total_offers_count = $applicants->total_offers_count($offers_made_args);
                $courser_info['total_offers'] = $total_offers_count;
                $offers_made_args['funnel_view'] = true;
                $total_offers_count_fv = $applicants->total_offers_count($offers_made_args);
                $courser_info['total_offers_fv'] = $total_offers_count_fv;
            }

            if (!empty($args['include_offers_accepted'])) {
                $offers_accepted_args = array('cohort' => $args['cohort'], 'school_id' => $args['school_id'], 'course' => $row['course_id'], 'accepted' => 'yes');

                if (!empty($args['level_id'])) {
                    $offers_accepted_args['course_level_id'] = $args['level_id'];
                }
                $total_offers_accepted = $applicants->total_offers_count($offers_accepted_args);
                $courser_info['total_offers_accepted'] = $total_offers_accepted;
                $offers_accepted_args['funnel_view'] = true;
                $total_offers_accepted_fv = $applicants->total_offers_count($offers_accepted_args);
                $courser_info['total_offers_accepted_fv'] = $total_offers_accepted_fv;
            }

            if (!empty($args['include_enrolled'])) {
                $enrolled_args = array('cohort' => $args['cohort'], 'stage_in' => '8,9', 'school_id' => $args['school_id'], 'course' => $row['course_id']);
                if ($args['level_id']) {
                    $enrolled_args['course_level_id'] = $args['level_id'];
                }
                $enrolled_offer_count = $applicants->enrolled_count($enrolled_args);
                $courser_info['total_enrolled'] = $enrolled_offer_count;
            }


            $results_list[] = $courser_info;
        }

        if (!empty($args['id']) || !empty($args['title'])) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    function get_mrn_courses($args = array())
    {
        global $db, $paginator;
        $dbh = get_dbh();
        $query = "SELECT *,
				core_courses.id as course_id,
				core_courses.usergroup as course_usergroup
			FROM
				core_courses
				LEFT JOIN form_schools ON form_schools.id = core_courses.usergroup";
        $results_list = array();

        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($results as $row) {

            $courser_info = array(
                "id" => $row['course_id'],
                "title" => $row['db232'],
                "usergroup_id" => $row['course_usergroup'],
                "school" => $row['db27'],
                "category" => $row['db231'],
                "abbreviation" => $row['db233'],
                "publish" => $row['db235'],
                "course_level_id" => $row['db341'],
                "state" => $row['db340'],
                "three_year_route" => $row['db710'],
                "two_year_route" => $row['db711'],
                "fee" => $row['db712'],
                "age_group" => $row['db22001'],
                "internal_code" => $row['db231'],
                "hide_apply_button" => $row['db33218'],
                "start_date" => $row['db29048'],
                "deadline_date" => $row['db710'],
                "references_deadline_date" => $row['db237680'],
                "deadline_time" => $row['db709'],
                "study_mode" => $row['db29046'],
                "participation_mode" => $row['db29047'],
                "duration" => $row['db29045'],
                "location" => $row['db32194'],
                'summary' => $row['db234'],
                'program_inclusions' => $row['db32198'],
                'language' => $row['db32199'],
                'delivery_and_assesment' => $row['db32197'],
                'entry_requirements' => $row['db32195'],
                'career_opportunities' => $row['db32196'],
                'testimonials' => $row['db32504'],
                'youtube' => $row['db32194'],
                //                    'locations'=>$this->get_locations(array("course_id"=>$row['course_id'])),
            );

            $results_list[] = $courser_info;
        }

        if ($args['id'] || $args['title']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /** ===================================
     * Course Sessions
     * ====================================    */
    function get_course_sessions($args = array())
    {
        global $db;
        $dbh = get_dbh();
        if ($args['school_id']) {
            $school_id_sql = "AND sis_course_sessions.usergroup='" . $args['school_id'] . "'";
        } else {
            $school_id_sql = "AND sis_course_sessions.usergroup='" . $_SESSION['usergroup'] . "'";
        }

        if ($args['name']) {
            $name_sql = "AND sis_course_sessions.db59829='" . $args['name'] . "'";
        }

        if ($args['id']) {
            $id_sql = "AND sis_course_sessions.id='" . $args['id'] . "'";
        }

        if ($args['course_id']) {
            $course_id_sql = "AND sis_course_sessions.rel_id='" . $args['course_id'] . "'";
        }

        if ($args['order']) {
            $order_by_sql = "ORDER BY " . $args['order'] . " ASC";
        } else {
            $order_by_sql = "ORDER BY db59831 ASC";
        }

        $query = "
				SELECT *, sis_course_sessions.id as course_session_id,sis_course_sessions.rel_id as course_id
				FROM
					sis_course_sessions
					LEFT JOIN core_courses ON sis_course_sessions.rel_id = core_courses.id
				WHERE 1
					AND (sis_course_sessions.rec_archive IS NULL OR sis_course_sessions.rec_archive = '')
					$school_id_sql
					$name_sql
					$id_sql
					$course_id_sql
					$order_by_sql
				";

        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        dev_debug(__METHOD__ . ": " . $query);
        $results_list = array();
        foreach ($results as $entry) {

            $results_list[] = array(
                'id' => $entry['course_session_id'],
                'name' => $entry['db59829'],
                'description' => $entry['db59830'],
                'order' => $entry['db59831'],
                'course' => array(
                    'id' => $entry['course_id'],
                ),
            );
        }

        if ($args['id'] || $args['name']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /** ===================================
     * Course Sessions
     * ====================================    */
    function get_course_materials($args = array())
    {
        global $db;
        $dbh = get_dbh();
        if ($args['school_id']) {
            $school_id_sql = "AND core_course_materials.usergroup='" . $args['school_id'] . "'";
        } else {
            $school_id_sql = "AND core_course_materials.usergroup='" . $_SESSION['usergroup'] . "'";
        }
        $name_sql = '';
        if ($args['name']) {
            $name_sql = "AND core_course_materials.db63983='" . $args['name'] . "'";
        }
        $id_sql = '';
        if ($args['id']) {
            $id_sql = "AND core_course_materials.id='" . $args['id'] . "'";
        }
        $course_id_sql = '';
        if ($args['course_id']) {
            $course_id_sql = "AND core_course_materials.rel_id='" . $args['course_id'] . "'";
        }

        if ($args['order']) {
            if (strpos($args['order'], 'asc') || strpos($args['order'], 'desc')) {
                $order_by_sql = "ORDER BY " . $args['order'];
            } else {
                $order_by_sql = "ORDER BY " . $args['order'] . " ASC";

            }
        } else {
            $order_by_sql = "ORDER BY db63983 ASC";
        }
        $activeSql = '';
        if (!empty($args['active'])) {
            $activeSql = " and db63982 = 'active'";
        }

        $query = "
				SELECT *, core_course_materials.id as course_material_id,core_course_materials.rel_id as course_id, core_course_materials.date As materialDate
				FROM
					core_course_materials
					LEFT JOIN core_courses ON core_course_materials.rel_id = core_courses.id
				WHERE 1
					AND (core_course_materials.rec_archive IS NULL OR core_course_materials.rec_archive = '')
					$school_id_sql
					$name_sql
					$id_sql
					$course_id_sql
				    $activeSql
					$order_by_sql
				";

        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        dev_debug(__METHOD__ . ": " . $query);
        $results_list = array();
        foreach ($results as $entry) {

            $results_list[] = array(
                'id' => $entry['course_material_id'],
                'name' => $entry['db63983'],
                'type' => $entry['db63979'],
                'link' => $entry['db63980'],
                'password' => $entry['db63981'],
                'status' => $entry['db63982'],
                'course' => array(
                    'id' => $entry['course_id'],
                ),
                'materialDate' => $entry['materialDate'],
                'course_session' => $entry['db313084']
            );
        }

        if ($args['id'] || $args['name']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /** ===================================
     * Get course material types
     * ====================================    */
    function get_course_material_types($args = array())
    {
        global $db, $paginator;
        $dbh = get_dbh();
        if ($args['school_id']) {
            $school_id_sql = "AND core_course_material_types.usergroup='" . $args['school_id'] . "'";
        }


        $query = "
			SELECT *
			FROM
				core_course_material_types
				
			WHERE
			1
		  AND(core_course_material_types.rec_archive IS NULL OR core_course_material_types.rec_archive = '')
			$school_id_sql
			
			ORDER BY db63976 ASC ";


        $results_list = array();

        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($results as $row) {

            $courser_info = array(
                "id" => $row['id'],
                "name" => $row['db63976'],
                "merge_tag" => $row['db63977'],
            );

            $results_list[] = $courser_info;
        }

        if ($args['id'] || $args['name']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Get course topics
     * ====================================    */
    function get_topics($args = array())
    {
        global $db, $paginator;
        $dbh = get_dbh();
        $course_id_sql = '';
        $rel_left_join ='';
        if (!empty($args['course_id'])) {
            $course_id_sql = "AND core_course_topics_rel.rel_id='" . $args['course_id'] . "'";
            $rel_left_join = "LEFT JOIN core_course_topics_rel on core_course_topics_rel.db32203 = core_course_topics.id";
        }
        $school_id_sql ='';
        if (!empty($args['school_id'])) {
            $school_id_sql = "AND core_course_topics.usergroup='" . $args['school_id'] . "'";
        }

        $in_use_only_sql ='';
        $in_use_only_left_join ='';
        if (!empty($args['in_use_only'])) {
            $in_use_only_sql = "AND core_course_topics_rel.id!=''";
            $in_use_only_left_join = "LEFT JOIN core_course_topics_rel on core_course_topics_rel.db32203 = core_course_topics.id";
        }
        //get topics with courses published only and topics tagged to a program only
        $get_topics_join ='';
        $public_courses_with_topics ='';
        $courses_with_matching_topics = '';
        $topic_level ='';
        if (!empty($args['get_topics'])) {
            $get_topics_join = 'RIGHT JOIN core_courses ON core_course_topics_rel.rel_id = core_courses.id';
            $public_courses_with_topics = 'AND db235="public"';
            $courses_with_matching_topics = "AND (db32201 IS NOT NULL OR  db32201= '')";

            if (!empty($_GET['level_id'])) {
                $topic_level = "AND db341 = " . $_GET['level_id'];
            }
        }

        $query = "
			SELECT
				*, core_course_topics.id as entry_id
			FROM
				core_course_topics
				$rel_left_join
				$in_use_only_left_join
				$get_topics_join
			WHERE
			1
		  AND(core_course_topics.client_archive IS NULL OR core_course_topics.client_archive = '')
			$course_id_sql
			$in_use_only_sql
			$public_courses_with_topics
			$courses_with_matching_topics
			$topic_level
			$school_id_sql
			GROUP BY core_course_topics.id
			ORDER BY db32201 ASC ";

        $results_list = array();

        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($results as $row) {

            $courser_info = array(
                "id" => $row['entry_id'],
                "title" => $row['db32201'],
            );

            $results_list[] = $courser_info;
        }

        if (!empty($args['id']) || !empty($args['title'])) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    function get_recommended_topics()
    {
        global $db, $paginator;
        $dbh = get_dbh();
        $unique_searches = pull_field("core_course_topics", "CONCAT('\'',GROUP_CONCAT(db32201 SEPARATOR '\',\''),'\'')", " WHERE usergroup=42");
        $query = "
			SELECT id,
db231929 as title,
COUNT(*) as 'count'
FROM ols_course_search_log  WHERE usergroup = '42'
AND db231929 NOT IN($unique_searches) GROUP BY db231929 ORDER BY 'count' DESC";
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    }


    /** ===================================
     * Get course locations
     * ====================================    */
    function get_locations($args = array())
    {
        global $db, $paginator;
        $dbh = get_dbh();

        if (!empty($args['school_id'])) {
            $school_id_sql = "AND core_course_locations.usergroup='" . $args['school_id'] . "'";
        } else {
            $school_id_sql = "AND core_course_locations.usergroup='" . $_SESSION['usergroup'] . "'";
        }
        $course_id_sql ='';
        $rel_left_join ='';
        $school_id_sql ='';
        if (!empty($args['course_id'])) {
            $course_id_sql = "AND core_course_locations_rel.rel_id=" . $args['course_id'];
            $rel_left_join = "INNER JOIN core_course_locations_rel on core_course_locations_rel.db32202 = core_course_locations.id";
            $school_id_sql = "AND core_course_locations_rel.usergroup='" . $_SESSION['usergroup'] . "'";
        }
        $in_use_only_sql ='';
        $in_use_only_left_join ='';
        if (!empty($args['in_use_only'])) {
            $in_use_only_sql = "AND core_course_locations_rel.id!=''";
            $in_use_only_left_join = "INNER JOIN core_course_locations_rel on core_course_locations_rel.db32202 = core_course_locations.id";
        }


        $query = "

			SELECT
				*, core_course_locations.id as entry_id
			FROM
				core_course_locations
				$rel_left_join
				$in_use_only_left_join
			WHERE
			1
		AND(core_course_locations.client_archive IS NULL OR core_course_locations.client_archive = '')
			$course_id_sql
			$in_use_only_sql
			$school_id_sql
			GROUP BY core_course_locations.id
			ORDER BY db32200,db32336";

        // echo $query."<br>";

        $results_list = array();
        dev_debug(__METHOD__ . " : " . $query);
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);;
        foreach ($results as $row) {

            $courser_info = array(
                "id" => $row['entry_id'],
                "title" => $row['db37260'],
                "main_contact" => $row['db37244'],
                "telephone" => $row['db37243'],
                "address_line_1" => $row['db37240'],
                "address_line_2" => $row['db37241'],
                "city" => $row['db32336'],
                "country" => $row['db32200']
            );

            $results_list[] = $courser_info;
        }

        if (!empty($args['id']) || !empty($args['title'])) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Get course locations
     * ====================================    */
    function get_countries($args = array())
    {
        global $db, $paginator;
        $dbh = get_dbh();

        if ($args['school_id']) {
            $school_id_sql = "AND core_course_locations.usergroup='" . $args['school_id'] . "'";
        } else {
            $school_id_sql = "AND core_course_locations.usergroup='" . $_SESSION['usergroup'] . "'";
        }

        if ($args['course_id']) {
            $course_id_sql = "AND core_course_locations_rel.rel_id='" . $args['course_id'] . "'";
            $rel_left_join = "LEFT JOIN core_course_locations_rel on core_course_locations_rel.db32202 = core_course_locations.id";
            $school_id_sql = "AND core_course_locations_rel.usergroup='" . $_SESSION['usergroup'] . "'";
        }

        if ($args['in_use_only']) {
            $in_use_only_sql = "AND core_course_locations_rel.id!=''";
            $in_use_only_left_join = "LEFT JOIN core_course_locations_rel on core_course_locations_rel.db32202 = core_course_locations.id";
        }


        $query = "

			SELECT
				*, core_course_locations.id as entry_id
			FROM
				core_course_locations
				$rel_left_join
				$in_use_only_left_join
			WHERE
			1
		  AND(core_course_locations.client_archive IS NULL OR core_course_locations.client_archive = '')
			$course_id_sql
			$in_use_only_sql
			$school_id_sql
			GROUP BY core_course_locations.id
			ORDER BY db32200,db32336";

        // echo $query."<br>";

        $results_list = array();

        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($results as $row) {

            $courser_info = array(
                "id" => $row['entry_id'],
                "title" => $row['db37260'],
                "main_contact" => $row['db37244'],
                "telephone" => $row['db37243'],
                "address_line_1" => $row['db37240'],
                "address_line_2" => $row['db37241'],
                "city" => $row['db32336'],
                "country" => $row['db32200']
            );

            $results_list[] = $courser_info;
        }

        if ($args['id'] || $args['title']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    function get_cities($args = array())
    {
        global $db, $paginator;
        $dbh = get_dbh();

        if ($args['school_id']) {
            $school_id_sql = "AND core_course_locations.usergroup='" . $args['school_id'] . "'";
        } else {
            $school_id_sql = "AND core_course_locations.usergroup='" . $_SESSION['usergroup'] . "'";
        }

        if ($args['course_id']) {
            $course_id_sql = "AND core_course_locations_rel.rel_id='" . $args['course_id'] . "'";
            $rel_left_join = "LEFT JOIN core_course_locations_rel on core_course_locations_rel.db32202 = core_course_locations.id";
            $school_id_sql = "AND core_course_locations_rel.usergroup='" . $_SESSION['usergroup'] . "'";
        }

        if ($args['in_use_only']) {
            $in_use_only_sql = "AND core_course_locations_rel.id!=''";
            $in_use_only_left_join = "LEFT JOIN core_course_locations_rel on core_course_locations_rel.db32202 = core_course_locations.id";
        }


        $query = "

			SELECT
				*, core_course_locations.id as entry_id
			FROM
				core_course_locations
				$rel_left_join
				$in_use_only_left_join
			WHERE
			1
		  AND(core_course_locations.client_archive IS NULL OR core_course_locations.client_archive = '')
			$course_id_sql
			$in_use_only_sql
			$school_id_sql
			GROUP BY core_course_locations.id
			ORDER BY db32200,db32336";

        // echo $query."<br>";

        $results_list = array();

        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($results as $row) {

            $courser_info = array(
                "id" => $row['entry_id'],
                "title" => $row['db37260'],
                "main_contact" => $row['db37244'],
                "telephone" => $row['db37243'],
                "address_line_1" => $row['db37240'],
                "address_line_2" => $row['db37241'],
                "city" => $row['db32336'],
                "country" => $row['db32200']
            );

            $results_list[] = $courser_info;
        }

        if ($args['id'] || $args['title']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    function get_requirements($args = array())
    {
        global $db, $paginator;
        $dbh = get_dbh();

        if (!empty($args['school_id'])) {
            $school_id_sql = "AND app_course_assignments.usergroup='" . $args['school_id'] . "'";
        } else {
            $school_id_sql = "AND app_course_assignments.usergroup='" . $_SESSION['usergroup'] . "'";
        }
        $course_id_sql ='';
        $rel_left_join ='';
        $school_id_sql ='';
        if (!empty($args['course_id'])) {
            $course_id_sql = "AND app_course_assignment_rel.rel_id='" . $args['course_id'] . "'";
            $rel_left_join = "LEFT JOIN app_course_assignment_rel on app_course_assignment_rel.db73250 = app_course_assignments.id";
            $school_id_sql = "AND app_course_assignment_rel.usergroup='" . $_SESSION['usergroup'] . "'";
        }
        $in_use_only_sql ='';
        $in_use_only_left_join ='';
        if (!empty($args['in_use_only'])) {
            $in_use_only_sql = "AND app_course_assignment_rel.id!=''";
            $in_use_only_left_join = "LEFT JOIN app_course_assignment_rel on app_course_assignment_rel.db73250 = app_course_assignments.id";
        }


        $query = "

			SELECT
				*, app_course_assignments.id as entry_id
			FROM
				app_course_assignments
				$rel_left_join
				$in_use_only_left_join
			WHERE
			1
		  AND(app_course_assignments.rec_archive IS NULL OR app_course_assignments.rec_archive = '')
			$course_id_sql
			$in_use_only_sql
			$school_id_sql
			GROUP BY app_course_assignments.id
			ORDER BY app_course_assignments.id";

        // echo $query."<br>";

        $results_list = array();

        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($results as $row) {

            $courser_info = array(
                "id" => $row['entry_id'],
                "title" => $row['db69917'],
                "max_points" => $row['db69911']
            );

            $results_list[] = $courser_info;
        }

        if (!empty($args['id']) || !empty($args['title'])) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }


    /** ===================================
     * Delete_course_image
     * ====================================    */
    function delete_course_image($args = array())
    {
        global $db;

        $file1 = STATIC_MEDIA_PATH . "programmes/programme" . $args['id'] . "." . $args['current_image_extension'];
        if (file_exists($file1)) {
            unlink($file1);
        }

        $file2 = STATIC_MEDIA_PATH . "programmes/programme" . $args['id'] . "_thumb." . $args['current_image_extension'];
        if (file_exists($file2)) {
            unlink($file2);
        }


        $entry_info['db32206'] = "";
        $where = array('id' => $args['id']);
        $db->update('core_courses', $entry_info, $where);
    }


    /** ===================================
     * Duplicate Course
     * ====================================    */
    function duplicate_course($args = array())
    {
        global $db;


        //get the course
        $courses_args = array('id' => $args['id'], 'school_id' => $_SESSION['usergroup']);
        $programme_args = $this->get($courses_args);
        $programme_args['department'] = $programme_args['department']['id'];
        $programme_args['route'] = $programme_args['route']['id'];
        $programme_args['title'] = $args['title'];
        $programme_args['image'] = $args['title'];
        $programme_args['old_id'] = $programme_args['id'];
        unset($programme_args['id']);
        unset($programme_args['image']);

        //Remove the IDs on the intakes
        // $new_intakes = array();
        // foreach($programme_args['intakes'] as $intake){
        //   unset($intake['id']);
        //   $new_intakes[] = $intake;
        // }


        //Remove the intakes. The use will manually create them
        $programme_args['intakes'] = array();

        //Remove the IDs on the locations
        $new_locations = array();
        foreach ($programme_args['locations'] as $location) {
            $new_locations[] = $location['id'];
        }
        $programme_args['locations'] = $new_locations;

        //Remove the IDs on the topics
        $new_topics = array();
        foreach ($programme_args['topics'] as $topic) {
            $new_topics[] = $topic['id'];
        }
        $programme_args['topics'] = $new_topics;


        //Create it
        $programme_id = $this->insert_update_course($programme_args);

        return $programme_id;
    }


    /** ===================================
     * Get Courses
     * ====================================    */
    function get_all_courses($args = array())
    {
        global $db, $paginator;
        $params = [];
        $dbh = get_dbh();
        if (!empty($args['id'])) {
            $id_sql = "AND core_courses.id=:id";
            $params['id'] = $args['id'];
        } else {
            $id_sql = '';
        }

        if ($args['level_id']) {
            $level_id_sql = "AND core_courses.db341=:level_id";
            $params['level_id'] = $args['level_id'];
        }

        if ($args['title']) {
            $title_sql = "AND core_courses.db232=':title'";
            $params['title'] = $args['title'];
        }

        if ($args['term']) {
            if ($args['term'] == "Spring_Break")
                $term_sql = "AND (db32333=:term OR db32333='Spring Break')";
            else
                $term_sql = "AND db32333=:term";
            $params['term'] = $args['term'];
        }

        if ($args['search']) {
            $search_sql = "AND core_courses.db232 LIKE '%:search%'";
            $params['search'] = $args['search'];
        }

        if ($args['course_level_search']) {
            $search_sql = "AND core_course_level.db343 LIKE '%:course_level_search%'";
            $params['course_level_search'] = $args['course_level_search'];
        }

        if ($args['location_id']) {
            $location_id_sql = "AND core_course_locations_rel.db32202 =:location_id";
            $params['location_id'] = $args['location_id'];
        }

        if ($args['topic_id']) {
            $topic_id_sql = "AND core_course_topics_rel.db32203 =:topic_id";
            $params['topic_id'] = $args['topic_id'];
        }


        if ($args['school_id']) {
            $school_id_sql = "AND core_courses.usergroup=:school_id";
            $params['school_id'] = $args['school_id'];
        }

        if ($args['public_only']) {
            $public_only_sql = "AND db235='public'";
        }


        if (array_key_exists('order', $args)) {

            $order = explode(" ", $args['order']);
            $order_field = $order[0];
            $order_type = $order[1];

            if (!$order_type) {
                $order_type = "ASC";
            }

            $order_sql = "ORDER BY " . $order_field . " " . $order_type;
        } else {
            $order_sql = "ORDER BY db232 ASC";
        }


        $query = "
			SELECT
				*,
				core_courses.id as course_id,
				core_courses.usergroup as course_usergroup
			FROM
				core_courses
				LEFT JOIN core_course_level ON core_course_level.id = core_courses.db341
				LEFT JOIN form_schools ON form_schools.id = core_courses.usergroup
				LEFT JOIN dir_cohorts ON db18719 = core_courses.id
				LEFT JOIN core_course_locations_rel ON core_course_locations_rel.rel_id = core_courses.id
				LEFT JOIN core_course_locations ON core_course_locations.id = core_course_locations_rel.db32202

				LEFT JOIN core_course_topics_rel ON CAST(core_courses.id as CHAR) = core_course_topics_rel.rel_id
				LEFT JOIN core_course_topics ON core_course_topics.id = core_course_topics_rel.db32203
			WHERE
				(core_courses.rec_archive IS NULL OR core_courses.rec_archive = '')
				$public_only_sql
				$id_sql
				$level_id_sql
				$search_sql
				$title_sql
				$topic_id_sql
				$term_sql
				$location_id_sql
				$school_id_sql
			GROUP BY core_courses.id
			$order_sql";
        $results_list = array();
        dev_debug(__METHOD__ . ": {$query}");
        if (array_key_exists('paginate', $args)) {
            $limit_sql = $paginator->limit_sql();
            $paginator->calculate_total_entries($query);
        }


        $query = $query . $limit_sql;
        $applicants = new Students;
        $route = new Routes;
        $stmt = $dbh->prepare($query);
        $stmt->execute($params);
        $results = $stmt->fetchAll();

        foreach ($results as $row) {


            $courser_info = array(
                "id" => $row['course_id'],
                "title" => $row['db232'],
                "tutors" => $row['db592'],
                "category" => $row['db231'],
                "abbreviation" => $row['db233'],
                "publish" => $row['db235'],
                "detailed_description" => $row['db29050'],

                "course_level_id" => $row['db341'],
                "course_level" => $row['db343'],
                "short_course" => ($row['db16595'] == 'short_course' || $row['db16595'] == 'short') ? 'yes' : 'no',
                "state" => $row['db340'],
                "deadline_time" => $row['db709'],
                "reference_deadline_time" => $row['db333796'],
                "three_year_route" => $row['db710'],
                "two_year_route" => $row['db711'],
                "fee" => $row['db712'],

                "hide_apply_button" => $row['db33218'],


                "duration" => $row['db29045'],
                "study_mode" => $row['db29046'],
                "participation_mode" => $row['db29047'],

                "start_date" => $row['db29048'],
                "deadline_date" => $row['db710'],
                "references_deadline_date" => $row['db237680'],
                "mandatory_course" => $row['db66775'],
                "study_mode" => $row['db29046'],
                "participation_mode" => $row['db29047'],
                "duration" => $row['db29045'],
                "location" => $row['db32194'],
                'description' => $row['db29050'],
                'summary' => $row['db234'],
                'program_inclusions' => $row['db32198'],
                'language' => $row['db32199'],
                'delivery_and_assesment' => $row['db32197'],
                'entry_requirements' => $row['db32195'],
                'career_opportunities' => $row['db32196'],
                'testimonials' => $row['db32504'],
                'youtube' => $row['db32194'],
                'topics' => $this->get_topics(array("course_id" => $row['course_id'])),
                'locations' => $this->get_locations(array("course_id" => $row['course_id'])),
                'intakes' => $applicants->get_cohorts(array("course_id" => $row['course_id'], "client_archive" => "yes", 'term' => (!empty($args['term']) ? $args['term'] : ''))),
                'course_sessions' => $this->get_course_sessions(array("course_id" => $row['course_id'])),
            );


            $results_list[] = $courser_info;
        }

        if ($args['id'] || $args['title']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    ///#####Get All Courses for API endpoint

    function get_api_courses($args = array())
    {
        global $db, $paginator;
        $dbh = get_dbh();
        $online_learning = new OnlineLearning;

        if (!empty($args['id'])) {
            $id_sql = "AND core_courses.id='" . $args['id'] . "'";
        }

        if (!empty($args['school_id'])) {
            $school_id_sql = "AND core_courses.usergroup=$args[school_id]";
        } else {
            $school_id_sql = "AND core_courses.usergroup=$_SESSION[usergroup]";
        }

        /*if ($args['route_id']) {
            $route_id_sql = "AND core_courses.db22005='" . $args['route_id'] . "'";
        }

        if ($args['no_route']) {
            $no_route_sql = "AND core_courses.db22005=''";
        }

        if ($args['department_id']) {
            $department_id_sql = "AND core_courses.db25610 = " . $args['department_id'] . "";
        }

        if ($args['department_in']) {
            $department_in_sql = "AND core_courses.db25610 IN (" . $args['department_id'] . ") ";
        }

        if ($args['intake_id']) {
            $intake_id_sql = "AND core_students.db19356='" . $args['intake_id'] . "' ";
        }

        if ($args['intake_id']) {
            $intake_id_sql = "AND core_students.db19356='" . $args['intake_id'] . "' ";
        }

        if ($args['cohort']) {
            if ($args['cohort'] != "all") {
                $cohort_sql = "AND core_students.db890 = '" . $args['cohort'] . "'";
            }
        }*/

        if (!empty($args['search'])) {
            $search_sql = "AND core_courses.db232 LIKE '%" . $args['search'] . "%'";
        }


        $query = "
			SELECT
				*,
				core_courses.id as course_id,
				core_courses.usergroup as course_usergroup
			FROM
				core_courses
				LEFT JOIN core_course_level ON core_course_level.id = core_courses.db341
				LEFT JOIN form_schools ON form_schools.id = core_courses.usergroup
				LEFT JOIN `core_departments` ON core_departments.id = core_courses.db25610
				#LEFT JOIN `dir_cohorts` ON dir_cohorts.db18719 = core_courses.id
			WHERE
				(core_courses.rec_archive IS NULL OR core_courses.rec_archive = '')
				$school_id_sql
				$search_sql";


        if (array_key_exists('paginate', $args)) {
            $limit_sql = $paginator->limit_sql();
            $paginator->calculate_total_entries($query);
        }

        if (array_key_exists('limit', $args)) {
            $limit_sql = " LIMIT " . $args['limit'] . "";
        }

        $query = $query . $limit_sql;

        if ($_GET['show_courses_sql']) {
            echo '<pre>';
            echo $query;
            echo '</pre>';
        }

        // $route = new Routes;
        $students = new Students;

        $results_list = array();
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($results as $row) {

            if (!empty($row['db32206'])) {
                $programme_image = STATIC_MEDIA_URL . $row['db32206'];
            }
            $courser_info = array(
                "id" => $row['course_id'],
                "title" => $row['db232'],
                //"usergroup_id"=>$row['course_usergroup'],
                //"school"=>$row['db27'],
                "tutor_id" => $row['db592'],
                "image" => $programme_image,
                "image_name" => $row['db32206'],
                //"payment_id" => $row['form_payment_options_id'] ?: false,
                "category" => $row['db231'],
                "abbreviation" => $row['db233'],
                "publish" => $row['db235'],
                //"course_level_id"=>$row['db341'],
                "course_level" => $row['db343'],
                "department" => array(
                    'id' => $row['db25610'],
                    'title' => $row['db25609'],
                ),
                //"route"=>$route->get(array('id'=>$row['db22005'])),
                "short_course" => $row['db16595'] == 'short_course' ? 'yes' : 'no',
                "state" => $row['db340'],
                "three_year_route" => $row['db710'],
                "two_year_route" => $row['db711'],
                "fee" => $row['db712'],
                "age_group" => $row['db22001'],
                "internal_code" => $row['db231'],

                "hide_apply_button" => $row['db33218'],

                "start_date" => $row['db29048'],
                "deadline_date" => $row['db710'],
                "references_deadline_date" => $row['db237680'],
                "deadline_time" => $row['db709'],
				"reference_deadline_time" => $row['db333796'],
                "study_mode" => $row['db29046'],
                "participation_mode" => $row['db29047'],
                "duration" => $row['db29045'],
                "location" => $row['db32194'],
                'summary' => $row['db234'],
                'program_inclusions' => $row['db32198'],
                'language' => $row['db32199'],
                'delivery_and_assesment' => $row['db32197'],
                'entry_requirements' => $row['db32195'],
                'career_opportunities' => $row['db32196'],
                'testimonials' => $row['db32504'],
                'youtube' => $row['db32194'],
                'intakes' => $students->get_cohorts(array("course_id" => $row['course_id'])),
                'topics' => $this->get_topics(array("course_id" => $row['course_id'])),
                'locations' => $this->get_locations(array("course_id" => $row['course_id'])),
                'course_sessions' => $this->get_course_sessions(array("course_id" => $row['course_id'])),
            );
            if (!empty($args['process_shortcodes'])) {
                $courser_info['description'] = $online_learning->proccess_short_codes($row['db29050']);
            } else {
                $courser_info['description'] = $row['db29050'];
            }


            $results_list[] = $courser_info;
        }

        if ($args['id'] || $args['title']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    /** ===================================
     * Insert or update course_session
     * ====================================    */
    function insert_update_course_session($args)
    {
        global $db;

        if (array_key_exists('name', $args)) {
            $entry_info['db59829'] = $args['name'];
        }
        if (array_key_exists('description', $args)) {
            $entry_info['db59830'] = $args['description'];
        }
        if (array_key_exists('order', $args)) {
            $entry_info['db59831'] = $args['order'];
        }
        if (array_key_exists('course_id', $args)) {
            $entry_info['rel_id'] = $args['course_id'];
        }

        if ($args['id']) {
            $s_where = array('id' => $args['id'], "usergroup" => $_SESSION['usergroup']);
            $db->update('sis_course_sessions', $entry_info, $s_where);
            $entry_id = $args['id'];
        } else {
            $db->system_table_insert_or_update('sis_course_sessions', $entry_info);
            $entry_id = $db->lastInsertId();
        }

        return $entry_id;
    }


    /**
     * Delete a course_session
     *
     * @param int $args {
     *     Optional. Arguments to insert/update a cpirse_session
     *     available arguments.
     *
     * @type int        id
     * @type int        school_id
     * }
     * @return int -  1 to say deleted
     */
    function delete_course_session($args)
    {
        global $db;

        if ($args['id']) {
            $where = array('id' => $args['id']);

            if ($args['school_id']) {
                $where['usergroup'] = $args['school_id'];
            }
            $db->archive('sis_course_sessions', $where);
        }
        return $args['id'];
    }

    /** ===================================
     * Insert or update course_materials
     * ====================================    */
    function insert_update_course_material($args)
    {
        global $db;

        if (array_key_exists('name', $args)) {
            $entry_info['db63983'] = $args['name'];
        }
        if (array_key_exists('type', $args)) {
            $entry_info['db63979'] = $args['type'];
        }
        if (array_key_exists('link', $args)) {
            $entry_info['db63980'] = $args['link'];
        }
        if (array_key_exists('password', $args)) {
            $entry_info['db63981'] = $args['password'];
        }
        if (array_key_exists('status', $args)) {
            $entry_info['db63982'] = $args['status'];
        }
        if (array_key_exists('course_id', $args)) {
            $entry_info['rel_id'] = $args['course_id'];
        }
        if (array_key_exists('course_session', $args)) {
            $entry_info['db313084'] = $args['course_session'];
        }

        if ($args['id']) {
            $s_where = array('id' => $args['id'], "usergroup" => $_SESSION['usergroup']);
            $db->update('core_course_materials', $entry_info, $s_where);
            $entry_id = $args['id'];
        } else {
            $db->system_table_insert_or_update('core_course_materials', $entry_info);
            $entry_id = $db->lastInsertId();
        }

        return $entry_id;
    }


    /**
     * Delete a course_material
     *
     * @param int $args {
     *     Optional. Arguments to insert/update a course_material
     *     available arguments.
     *
     * @type int        id
     * @type int        school_id
     * }
     * @return int -  1 to say deleted
     */
    function delete_course_material($args)
    {
        global $db;

        if ($args['id']) {
            $where = array('id' => $args['id']);

            if ($args['school_id']) {
                $where['usergroup'] = $args['school_id'];
            }
            $db->archive('core_course_materials', $where);
        }
        return $args['id'];
    }

    /** ===================================
     * Cohorts List
     * ====================================    */
    function years_list()
    {
        global $db;
        $dbh = get_dbh();
        $query = "SELECT figures FROM system_table WHERE db_field_name='db1680'";
        //echo $query;
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        //var_dump($results);
        $results_list = array();
        $years = [];
        foreach ($results as $entry) {
            if ($entry['figures']) {
                $figures = explode(',', $entry['figures']);
                foreach ($figures as $year) {
                    if (!in_array($year, $years) && is_numeric($year)) {
                        $results_list[] = array('title' => $year);
                        $years[] = $year;
                    }
                }
            }
        }
        return $results_list;
    }

    function course_history($args)
    {
        global $db;
        $dbh = get_dbh();
        $db_fields = [
            'publish' => 'db235',
            'state' => 'db340',
            'course_status' => 'db66752'
        ];

        $course = [
            'publish' => $args['publish'],
            'state' => $args['state'],
            'course_status' => $args['course_status']
        ];

        //get the current status, course status and publish status of the course
        $current_values_sql = "SELECT db235 as publish, db340 as state , db66752 AS course_status 
			FROM core_courses 
			WHERE id=" . $args['id'] . " AND usergroup =" . $_SESSION['usergroup'];
        $stmt = $dbh->prepare($current_values_sql);
        $stmt->execute();
        $current_values = $stmt->fetch(PDO::FETCH_ASSOC);

        //if different add a new entry to the course_history table
        if ($course != $current_values) {
            $result = array_diff($course, $current_values);
            $keys = array_keys($result);

            foreach ($keys as $key) {
                $course_info = [
                    'rel_id' => $args['id'],
                    'db64489' => $db_fields[$key],
                    'db105698' => $current_values[$key],
                    'db105701' => $course[$key],
                    'db105704' => 'Update on course'

                ];

                $db->system_table_insert_or_update('core_course_history', $course_info);
            }
        }
    }

    public function get_courses($args)
    {

        global $paginator, $db;
        $dbh = get_dbh();
        $all_columns_sql = '';
        $fields = new Fields;
        $pages = [18 => false];
        $active_fields = $fields->columns_get($pages);
        $active_columns = array_filter(array_column($active_fields, 'db_field_name'));
        $active_columns = array_filter($active_columns, function ($c) {
            $exceptions = array('id', 'username_id', 'date');
            return !in_array($c, $exceptions);
        });

        if (array_key_exists('search', $args) && $args['search']) {
            $args['search'] = str_replace("/", "-", $args['search']);
            $term = $args['search'];
            $search_sql = " AND  ( core_courses.id LIKE '%{$term}%' OR ";
            foreach ($active_columns as $key => $field) {
                $search_sql .= "{$field} LIKE '%{$term}%' " . (count($active_columns) - 1 == $key ? '' : " OR ");
            }
            $search_sql .= ")";
        }


        if (count($active_columns) > 0) {

            $active_columns = $this->fields_dimensions($active_columns);

            $all_columns_sql = implode(",", $active_columns);
        }

        if ($args['id']) {
            $args['id'] = (int)$args['id'];
            $id_sql = "AND core_courses.id='" . $args['id'] . "'";
        }
        if (array_key_exists('ids_in', $args) && !empty($args['ids_in'])) {
            $ids_in_sql = "AND core_courses.id in(" . implode(',', $args['ids_in']) . ")";
        } else {
            $ids_in_sql = '';
        }
        if ($args['level_id']) {
            $level_id_sql = "AND core_courses.db341='" . $args['level_id'] . "'";
        }

        if ($args['title']) {
            $title_sql = "AND core_courses.db232='" . $args['title'] . "'";
        }

        if ($args['filter_sql_lite']) {
            $filter_sql_lite = $args['filter_sql_lite'];
        }
        if ($args['published'] == true) {
            $published_sql = "AND core_courses.db340 ='on'";
        }
        if ($args['school_id']) {
            $school_id_sql = "AND core_courses.usergroup='" . $args['school_id'] . "'";
        } else {
            $school_id_sql = "AND core_courses.usergroup='" . $_SESSION['usergroup'] . "'";
        }

        if ($args['route_id']) {
            $route_id_sql = "AND core_courses.db22005='" . $args['route_id'] . "'";
        }

        if ($args['no_route']) {
            $no_route_sql = "AND core_courses.db22005=''";
        }

        if ($args['department_id']) {
            $department_id_sql = "AND core_courses.db25610 = " . $args['department_id'] . "";
        }

        if ($args['department_in']) {
            $department_in_sql = "AND core_courses.db25610 IN (" . $args['department_id'] . ") ";
        }

        if ($args['intake_id']) {
            $intake_id_sql = "AND core_students.db19356='" . $args['intake_id'] . "' ";
        }

        if ($args['intake_id']) {
            $intake_id_sql = "AND core_students.db19356='" . $args['intake_id'] . "' ";
        }

        if ($args['cohort']) {
            if ($args['cohort'] != "all") {
                $cohort_sql = "AND core_students.db890 = '" . $args['cohort'] . "'";
            }
        }


        if ($args['payments']) {
            $payments_join = "LEFT JOIN form_payment_options ON form_payment_options.rel_id = core_courses.id";
            $payments_sql = ", form_payment_options.id AS form_payment_options_id ";
        }


        if (array_key_exists('partner_id', $args)) {
            $partner_programmes_left_join = "LEFT JOIN core_partner_programmes ON core_partner_programmes.db33566 = core_courses.id AND (core_partner_programmes.rec_archive IS NULL OR core_partner_programmes.rec_archive = '')";
            $partner_id_sql = "AND core_partner_programmes.db33565='" . $args['partner_id'] . "'";
        }

        if ($args['order']) {
            $order_by_sql = "ORDER BY " . $args['order'];
        } else {
            $order_by_sql = "ORDER BY core_courses.id ASC";
        }

        if (!empty($args['filter_sql'])) {
            $filter_sql = $args['filter_sql'];
        }
        $count_sql = "";

        if (array_key_exists('total', $args)) {
            $count_sql = "count(DISTINCT core_courses.id) as total_count,";
            $group_by_sql = "";
        } else {
            if (!empty($all_columns_sql)) {
                $count_sql = "$all_columns_sql,";
            }
        }

        $query = "
			SELECT 
			$count_sql
			core_courses.id
			$payments_sql
			FROM
			core_courses 
			LEFT JOIN core_course_level ON core_course_level.id = core_courses.db341
			LEFT JOIN form_schools ON form_schools.id = core_courses.usergroup
			LEFT JOIN `core_departments` ON core_departments.id = core_courses.db25610
			$payments_join
			$partner_programmes_left_join
			WHERE
			(core_courses.rec_archive IS NULL or core_courses.rec_archive = '')
			$id_sql
			$ids_in_sql
			$level_id_sql
			$no_route_sql
			$title_sql
			$title_filter_sql 
			$published_sql
			$school_id_sql
			$search_sql
			$filter_sql
			$partner_id_sql
			$department_id_sql
			$department_in_sql
			$intake_id_sql
			$order_by_sql";

        if (array_key_exists('paginate', $args) && !empty($args['paginate'])) {
            $limit_sql = $paginator->limit_sql();
            $paginator->calculate_total_entries($query);
        }

        $query = $query . $limit_sql;
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        dev_debug(__METHOD__ . ": " . $query);

        return $results;
    }


    public function fields_dimensions($active_columns)
    {

        $fields_to_use = [
            'db52600' => '(select db52617 from sis_tutor_stages where id=db52600) as db52600',

            'db52605' => "(SELECT GROUP_CONCAT(db232 SEPARATOR ', ')
								FROM core_courses
								 where find_in_set (id,db52605)
								) as db52605",
            'db66774' => '(select db66772 from sis_course_groups where id=db66774) as db66774',
        ];

        foreach ($active_columns as $c_key => $c_value) {
            if (array_key_exists($c_value, $fields_to_use)) {

                $active_columns[$c_key] = $fields_to_use[$c_value];
            }
        }


        return $active_columns;
    }


    /** ===================================
     * Process Bulk Actions
     * ====================================    */
    function process_bulk_action($args = array())
    {

        if ($_POST['bulk_action']) {
            if (!$_POST['select_all_entries']) {
                $selected_rows = array();
                foreach ($_POST as $key => $value) {
                    if (strpos($key, 'table_row_') !== false) {
                        $row_id = str_replace("table_row_", "", $key);
                        $selected_rows[] = $row_id;
                    }
                }
            }
            $bulk_action_list_list = array();


            foreach ($args['results'] as $entry_info) {
                if (in_array($entry_info['id'], $selected_rows)) {
                    $bulk_action_list_list[] = $entry_info;
                }
            }


            if ($_POST['select_all_entries']) {
                $bulk_action_list_list = $args['results'];
            }


            /** ===================================
             * Bulk Update Status
             * ====================================    */

            if ($_POST['bulk_action'] == "change_status" && $_POST['bulk_status_id']) {
                foreach ($bulk_action_list_list as $entry) {
                    $stage_args = array(
                        'course_id' => $entry['id'],
                        'status' => $_POST['bulk_status_id'],
                    );
                    $this->update_stage($stage_args);
                }

                return "The status has now been updated successfully.";
            }

            /** ===================================
             * Bulk Update Publish
             * ====================================    */

            if ($_POST['bulk_action'] == "change_publish" && $_POST['bulk_status_id']) {
                foreach ($bulk_action_list_list as $entry) {
                    $args = array(
                        'course_id' => $entry['id'],
                        'publish_status' => $_POST['bulk_status_id'],
                    );
                    $this->update_publish($args);
                }

                return "The publish status has now been updated successfully.";
            }


            /** ===================================
             * Bulk Archive
             * ====================================    */

            if ($_POST['bulk_action'] == "archive") {
                $ids = array_map(function ($item) {
                    return $item['id'];
                }, $bulk_action_list_list);
                $n = count($ids);
                $ids = implode(',', $ids);
                $sql = "UPDATE core_courses SET rec_archive = :user, rec_lstup = CURRENT_TIMESTAMP, rec_lstup_id = :user WHERE id IN ({$ids})";
                $dbh = get_dbh();
                $st = $dbh->prepare($sql);
                $st->bindParam(':user', $_SESSION['uid'], PDO::PARAM_INT);
                $st->execute();
                return "{$n} courses were archived successfully.";
            }

            if ($_POST['bulk_action'] == "archive_evaluations") {
                $ids = array_map(function ($item) {
                    return $item['id'];
                }, $bulk_action_list_list);
                $n = count($ids);
                $ids = implode(',', $ids);
                $sql = "UPDATE sis_course_feedback SET rec_archive = :user, rec_lstup = CURRENT_TIMESTAMP, rec_lstup_id = :user WHERE id IN ({$ids})";
                $dbh = get_dbh();
                $st = $dbh->prepare($sql);
                $st->bindParam(':user', $_SESSION['uid'], PDO::PARAM_INT);
                $st->execute();
                return "{$n} courses evaluations were deleted successfully.";
            }
        }
    }


    public function update_stage($args)
    {

        $sql = "UPDATE core_courses SET db340= :db340 WHERE id = :id AND usergroup = :usergroup ";
        $dbh = get_dbh();
        $st = $dbh->prepare($sql);
        $st->execute(
            [
                ':db340' => $args['status'],
                ':id' => $args['course_id'],
                ':usergroup' => $_SESSION['usergroup']
            ]
        );
    }

    public function update_publish($args)
    {

        $sql = "UPDATE core_courses SET db235= :db235 WHERE id = :id AND usergroup = :usergroup ";
        $dbh = get_dbh();
        $st = $dbh->prepare($sql);
        $st->execute(
            [
                ':db235' => $args['publish_status'],
                ':id' => $args['course_id'],
                ':usergroup' => $_SESSION['usergroup']
            ]
        );
    }

    public function get_qa_status($args)
    {
        global $db;
        $dbh = get_dbh();

        $sql = "SELECT id, db106670 as status from sis_course_qa_status where usergroup=" . $args['school_id'];
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
	
	/**
	 * @throws \Doctrine\DBAL\Exception
	 * @throws \Doctrine\DBAL\Driver\Exception
	 */
	public function waitinglist($args)
    {
        global $db;
        $dbh = get_dbh();
        $course_id = $args['course_id'];
        $school_id = $args['school_id'];

        $query = "
			SELECT 
			db39,
			db40,
			CAST(core_waitinglist.id as CHAR(55)) as id,
			DATE_FORMAT(core_waitinglist.date, '%d/%m/%Y') as date, 
			(select db48826 from sis_course_geog_coverage where id=db62923) as geog_area,
			db62944,
			db62945,
			db62946,
			db62947
			FROM
			core_waitinglist
			LEFT JOIN core_students on core_students.id = core_waitinglist.rel_id 
			WHERE
			(core_waitinglist.rec_archive IS NULL OR core_waitinglist.rec_archive = '')
			AND db16146= {$course_id}
			AND (db62944 IS NULL OR db62944= '')
 			AND (db62945 IS NULL OR db62945= '')
			AND core_waitinglist.usergroup={$school_id}
			ORDER BY core_waitinglist.id desc	
			";
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }


    public function course_evals($args)
    {
        global $paginator, $db;
        $dbh = get_dbh();
        $course_id = $args['course_id'];

        ///get form linked to course
        $query = "SELECT db111752, db232  FROM core_courses
				WHERE core_courses.id = {$course_id}";
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $course = $stmt->fetch(PDO::FETCH_ASSOC);

        $course_eval_form = $course['db111752'];

        if (empty($course_eval_form)) {
            //get default form
            $course_eval_form = pull_field('lead_preferences', 'db111755', "WHERE usergroup={$_SESSION['usergroup']}");
        }

        $args['course_eval_form'] = $course_eval_form;

        //get evaluations
        $evals = $this->get_evaluations($args);
        $evals['course_name'] = $course['db232'];
        return $evals;
    }

    public function get_evaluations($args)
    {
        $dbh = get_dbh();
        global $paginator, $db;
        $course_id = $args['course_id'];
        $sch_course_id = $args['sch_course_id'];
        $school_id = $args['school_id'];
        $fields = [];
        $evaluations = [];
        $filter_sql = "";
        $view_sql = "";

        if (!empty($args['filter_sql'])) {
            $filter_sql = $args['filter_sql'];
        }

        if (array_key_exists('order', $args) && $args['order']) {

            $order = explode(" ", $args['order']);
            $order_field = $order[0];
            $order_type = $order[1];

            if (!$order_type) {
                $order_type = "ASC";
            }

            $order_sql = "ORDER BY " . $order_field . " " . $order_type;
        } else {

            $order_sql = "ORDER BY sis_course_feedback.id desc";
        }

        $query = "
				SELECT name, db_field_name, type, 1 as active, name as title
				FROM system_form_fields
				LEFT JOIN system_table ON system_form_fields.system_table_id = system_table.form_id
				LEFT JOIN system_pages ON system_pages.page_id = system_table.pg_id
				LEFT JOIN system_cat ON system_cat.sys_cat_id = system_pages.project
				WHERE 1
				AND system_forms_id = '{$args['course_eval_form']}'
				AND system_form_fields.usergroup = '{$_SESSION['usergroup']}'
				AND type NOT IN ('title', 'textonly', 'hidden', 'hidden_session', 'subtitle')
				ORDER BY pg_id
			";

        dev_debug("query 8 9 " . $query);

        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        //get all scheduled courses

        if (!empty($course_id)) {
            $scheduled_query = "
                SELECT GROUP_CONCAT(sis_course_schedule.id) AS sch_id  
                FROM sis_course_schedule
				LEFT JOIN core_courses on db14946 = core_courses.id
				WHERE core_courses.id = {$course_id} 
			";

            $scheduled_courses = $db->get_row($scheduled_query);
            $scheduled_course_id = $scheduled_courses['sch_id'];
        }

        if (!empty($sch_course_id)) {
            $scheduled_course_id = $sch_course_id;
        }

        if (!empty($results)) {
            $columns = array_column($results, 'db_field_name');
            $final_columns = implode(",", $columns);

            if (array_key_exists('search', $args) && $args['search']) {
                $term = addslashes($args['search']);
                $search_sql = " AND  (sis_course_feedback.id LIKE '%{$term}%' OR (select DATE_FORMAT(db14947, '%d/%m/%Y')  from sis_course_schedule where id=sis_course_feedback.rel_id) LIKE '%{$term}%' OR 
				(SELECT group_concat(CONCAT(db52597,' ',db52598)) FROM sis_course_tutors WHERE FIND_IN_SET(sis_course_tutors.id, (select group_concat(db59798)
				from sis_scheduled_tutors where sis_scheduled_tutors.rel_id = sis_course_feedback.rel_id))) LIKE '%{$term}%' OR  ";
                foreach ($columns as $key => $field) {
                    $search_sql .= "{$field} LIKE '%{$term}%' " . (count($columns) - 1 == $key ? '' : " OR ");
                }
                $search_sql .= ")";
            }

            if (empty($scheduled_course_id)) {
                return [
                    'fields' => $fields,
                    'evaluations' => $evaluations,
                ];
            }

            $evaluations_sql = "
				SELECT 
				{$final_columns},
				CAST(sis_course_feedback.id as CHAR(55)) as id,
				CAST(sis_course_feedback.rel_id as CHAR(55)) as sched_id,
				DATE_FORMAT(sis_course_feedback.date, '%d/%m/%Y') as date,
				(select DATE_FORMAT(db14947, '%d/%m/%Y')  from sis_course_schedule where id=sis_course_feedback.rel_id) as sched_start_date,
				(select sis_course_schedule.username_id  from sis_course_schedule where id=sis_course_feedback.rel_id) as sched_uid,
				(SELECT group_concat(CONCAT(db52597,' ',db52598)) FROM sis_course_tutors WHERE FIND_IN_SET(sis_course_tutors.id, (select group_concat(db59798) 
				from sis_scheduled_tutors where sis_scheduled_tutors.rel_id = sis_course_feedback.rel_id))) as tutors
				FROM sis_course_feedback
				WHERE (sis_course_feedback.rec_archive IS NULL OR sis_course_feedback.rec_archive = '')
				AND rel_id in ({$scheduled_course_id})
				AND sis_course_feedback.usergroup={$school_id}
				{$search_sql}
				{$filter_sql}
				{$order_sql}						
				";
            if (array_key_exists('paginate', $args) && !empty($args)) {
                $limit_sql = $paginator->limit_sql();
                $paginator->calculate_total_entries($evaluations_sql);
                $fields = $results;
            } else {
                /// get field names
                foreach ($results as $result) {
                    $fields[$result['db_field_name']] = $result['name'];
                }
            }
            $evaluations_sql = $evaluations_sql . $limit_sql;

            $evaluations = $db->get_rows($evaluations_sql);
        }

        $evals = [
            'fields' => $fields,
            'evaluations' => $evaluations,
        ];

        return $evals;
    }

    public function get_sched_courses($args)
    {
        global $db;
        $dbh = get_dbh();
        $sql = "SELECT id as value, DATE_FORMAT(db14947, '%d/%m/%Y') as label 
		FROM sis_course_schedule WHERE db14946={$args} and (rec_archive is null or rec_archive= '') order by db14947 desc";
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function get_course_groups($args)
    {
        global $db;
        $dbh = get_dbh();
        $sql = "SELECT id as value, db66772 as label
		FROM sis_course_groups WHERE usergroup={$args['school_id']} and (rec_archive is null or rec_archive= '') order by db66772 desc";
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function get_email_templates($args)
    {
        global $db;
        $dbh = get_dbh();
        $sql = "SELECT id, db1083 as email_template_name
		FROM coms_template WHERE usergroup={$args['school_id']} and (rec_archive is null or rec_archive= '') order by id desc";
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * @param $args
     * @return array|false|mixed[]|string
     */
    public function get_course_types($args)
    {
        global $db;
        $dbh = get_dbh();
        $sql = "SELECT id as value, db242 as label
		FROM core_course_category WHERE usergroup={$args['school_id']} and (rec_archive is null or rec_archive= '') order by db886 desc";
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
