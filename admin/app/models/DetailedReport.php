<?php

/**
 * Reports for MRN
 */
class DetailedReport
{
    private $css_classes = [
        "status_primary",
        "status_warning",
        "status_danger",
        "status_attention",
        "status_info",
        "status_draft",
        "status_success",
    ];

    function pull_field($table,$field,$where=""){
        $dbh = get_dbh();
        $sth = $dbh->prepare("SELECT $field FROM $table $where");
        dev_debug("PULL FIELD: SELECT $field FROM $table $where");
        //echo "PULL FIELD: SELECT $field FROM $table $where </br>";
        $sth->execute();
        // error_log("Wordpay - SELECT $field FROM $table $where");

        return $sth->fetchColumn();
    }
    function get_dashboard_stats($args = false)
    {
        $dbh = get_dbh();
        $reg_sql = "";
        $mrn_applicants_only = "";
        $months_from_now = '0';
        if(!empty($args['months_from_now'])){
            $months_from_now = $args['months_from_now'];
        }
        if(!empty($args["isMRN"]) && $args["isMRN"]=="yes"){
            $mrn_applicants_only = " AND core_students.rel_id=0";
        }

        $num_students_stmt = "SELECT SUM(id>-1) as current_number,
       SUM(date < DATE_SUB(NOW(), INTERVAL 1 Month)) as month_ago
FROM core_students WHERE usergroup = {$_SESSION['usergroup']} $mrn_applicants_only {$_REQUEST['time_sql']}";
        dev_debug($num_students_stmt);
        $num_students_stmt = $dbh->prepare($num_students_stmt);
        $num_students_stmt->execute();
        $num_students = $num_students_stmt->fetchAll(2);

            $dates_sql = "SELECT  LAST_DAY(DATE_SUB(NOW(), INTERVAL ".($months_from_now)." MONTH)) as first_month";
            if (!empty($args['num_of_months'])) {
                if($args["num_of_months"]>1){
                    $dates_sql.=", ";
                    $reg_sql.=",";
                    for ($i = 2; $i <= $args['num_of_months']; $i++) {
                        $prev = $i - 1;
                        $reg_sql .= "SUM(date BETWEEN DATE_ADD(LAST_DAY(NOW()-INTERVAL ".($months_from_now+$i)." MONTH ), INTERVAL 1 DAY) AND DATE_ADD(LAST_DAY(NOW()-INTERVAL ".($months_from_now+$i-1)." MONTH ), INTERVAL 1 DAY)) as {$prev}_months_ago";
                        $dates_sql .= "LAST_DAY(NOW()-INTERVAL ".($months_from_now + $prev)." MONTH ) as {$prev}_months_ago";
                        if ($i != $args['num_of_months']) {
                            $reg_sql .= ",
                            ";
                            $dates_sql .= ",
                            ";
                        }
                    }
                }
            }
            dev_debug($dates_sql);
            $date_map_stmt = $dbh->prepare($dates_sql);
        $date_map_stmt->execute();
        $date_map = $date_map_stmt->fetchAll(2);
        if(!empty($args["isMRN"]) && $args["isMRN"]=="yes"){
            $registration_sql = "SELECT SUM(date BETWEEN DATE_ADD(LAST_DAY(NOW()-INTERVAL ".($months_from_now+1)." MONTH ), INTERVAL 1 DAY) AND LAST_DAY(NOW()-INTERVAL ".($months_from_now)." MONTH )) as first_month
            $reg_sql
            FROM sis_scheduled_booking WHERE usergroup = {$_SESSION['usergroup']} {$_REQUEST['time_sql']}";
        }else{
            $registration_sql = "SELECT SUM(date BETWEEN DATE_ADD(LAST_DAY(NOW()-INTERVAL ".($months_from_now+1)." MONTH ), INTERVAL 1 DAY) AND LAST_DAY(NOW()-INTERVAL ".($months_from_now)." MONTH )) as first_month
            $reg_sql
            FROM core_students WHERE usergroup = {$_SESSION['usergroup']} $mrn_applicants_only {$_REQUEST['time_sql']}";

        }
        dev_debug($registration_sql);
        $stmt = $dbh->prepare($registration_sql);
        $stmt->execute();
        $reg_results = $stmt->fetchAll(2);
        if(!empty($args["isMRN"]) && $args["isMRN"]=="yes"){
            $enquiry_sql = "SELECT SUM(date BETWEEN LAST_DAY(NOW()-INTERVAL ".($months_from_now)." MONTH ) AND NOW()) as first_month
        $reg_sql
FROM sis_profiles WHERE usergroup = {$_SESSION['usergroup']} {$_REQUEST['time_sql']}";

            //Bookings
            $booking_time_sql = str_replace('sis_profiles', 'sis_sched_booking_detail', $_REQUEST['time_sql'] );
            $total_course_bookings = $this->pull_field("sis_sched_booking_detail", "count(id)", "WHERE usergroup='$_SESSION[usergroup]'  AND (rec_archive IS NULL OR rec_archive = '') {$booking_time_sql}");
            $active_course_bookings = $this->pull_field("sis_sched_booking_detail", "count(id)", "WHERE usergroup='$_SESSION[usergroup]'  AND (rec_archive IS NULL OR rec_archive = '') AND db59978 IN ('2','3','14') {$booking_time_sql}");
            $unique_student_bookings = $this->pull_field("sis_sched_booking_detail", "count(distinct((SELECT rel_id from core_students where core_students.id = sis_sched_booking_detail.rel_id)))", "WHERE usergroup='$_SESSION[usergroup]'  AND (rec_archive IS NULL OR rec_archive = '') {$booking_time_sql}");

            //Sessions held
            $scheduled_session_time_sql = str_replace('sis_profiles.date', 'sis_scheduled_sessions.db59835', $_REQUEST['time_sql'] );
            $scheduled_course_time_sql = str_replace('sis_profiles.date', 'sis_course_schedule.db14947', $_REQUEST['time_sql'] );
            $total_course_no_sessions = $this->pull_field("sis_course_schedule", "count(id)", "WHERE usergroup='$_SESSION[usergroup]'  AND (rec_archive IS NULL OR rec_archive = '') AND (SELECT count(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = CAST(sis_course_schedule.id AS CHAR)) = 0  {$scheduled_course_time_sql}");
            $total_sessions = $this->pull_field("sis_scheduled_sessions", "count(id)", "WHERE usergroup='$_SESSION[usergroup]'  AND (rec_archive IS NULL OR rec_archive = '')  {$scheduled_session_time_sql}");
            $total_sessions = $total_sessions + $total_course_no_sessions;

            $total_course_no_sessions_active = $this->pull_field("sis_course_schedule", "count(id)", "WHERE usergroup='$_SESSION[usergroup]'  AND (rec_archive IS NULL OR rec_archive = '') AND (SELECT count(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = CAST(sis_course_schedule.id AS CHAR)) = 0 AND db14959 IN ('3','4','7','11') {$scheduled_course_time_sql}");
            $total_sessions_active = $this->pull_field("sis_scheduled_sessions", "count(id)", "WHERE usergroup='$_SESSION[usergroup]'  AND (rec_archive IS NULL OR rec_archive = '') AND db59908 IN ('3','4','7','11') {$scheduled_session_time_sql}");
            $total_sessions_active = $total_sessions_active + $total_course_no_sessions_active;


            //bookings of sessions held
            $total_course_bookings_no_sessions = $this->pull_field("sis_sched_booking_detail JOIN sis_scheduled_booking ON sis_scheduled_booking.id = CAST(db15052 as UNSIGNED) JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_sched_booking_detail.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_sched_booking_detail.id)", "WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]'  AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (SELECT count(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = CAST(sis_course_schedule.id AS CHAR)) = 0 AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='')  {$scheduled_course_time_sql}");
            $total_course_sessions_bookings = $this->pull_field("sis_session_bookings JOIN sis_scheduled_sessions ON sis_scheduled_sessions.id = CAST(db59901 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_session_bookings.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_session_bookings.id)", "WHERE sis_session_bookings.usergroup='$_SESSION[usergroup]'  AND (sis_session_bookings.rec_archive IS NULL OR sis_session_bookings.rec_archive = '') AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') {$scheduled_session_time_sql}");
            $total_course_sessions_bookings = $total_course_sessions_bookings + $total_course_bookings_no_sessions;

            $total_course_bookings_no_sessions_active = $this->pull_field("sis_sched_booking_detail JOIN sis_scheduled_booking ON sis_scheduled_booking.id = CAST(db15052 as UNSIGNED) JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_sched_booking_detail.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_sched_booking_detail.id)", "WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]'  AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (SELECT count(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = CAST(sis_course_schedule.id AS CHAR)) = 0 AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND sis_sched_booking_detail.db59978 IN ('2','3','14') {$scheduled_course_time_sql}");
            $total_course_sessions_bookings_active = $this->pull_field("sis_session_bookings JOIN sis_scheduled_sessions ON sis_scheduled_sessions.id = CAST(db59901 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_session_bookings.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_session_bookings.id)", "WHERE sis_session_bookings.usergroup='$_SESSION[usergroup]'  AND (sis_session_bookings.rec_archive IS NULL OR sis_session_bookings.rec_archive = '') AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db59902 IN ('2','3','14') {$scheduled_session_time_sql}");
            $total_course_sessions_bookings_active = $total_course_sessions_bookings_active + $total_course_bookings_no_sessions_active;

            //unique student
            $unique_students_bookings_no_sessions = $this->pull_field("sis_sched_booking_detail JOIN sis_scheduled_booking ON sis_scheduled_booking.id = CAST(db15052 as UNSIGNED) JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_sched_booking_detail.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "group_concat(distinct((SELECT rel_id from core_students where core_students.id = sis_sched_booking_detail.rel_id)))", "WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]'  AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (SELECT count(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = CAST(sis_course_schedule.id AS CHAR)) = 0 AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='')  {$scheduled_course_time_sql}");
            $unique_students_sessions_bookings = $this->pull_field("sis_session_bookings JOIN sis_scheduled_sessions ON sis_scheduled_sessions.id = CAST(db59901 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_session_bookings.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(distinct((SELECT rel_id from core_students where core_students.id = sis_session_bookings.rel_id)))", "WHERE sis_session_bookings.usergroup='$_SESSION[usergroup]'  AND (sis_session_bookings.rec_archive IS NULL OR sis_session_bookings.rec_archive = '') AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND core_students.id NOT IN ($unique_students_bookings_no_sessions) {$scheduled_session_time_sql}");
            $unique_students_sessions_bookings = $unique_students_sessions_bookings + count(explode(',',$unique_students_bookings_no_sessions));

            //attendance of sessions held
            $total_course_bookings_no_sessions_status_attended = $this->pull_field("sis_sched_booking_detail JOIN sis_scheduled_booking ON sis_scheduled_booking.id = CAST(db15052 as UNSIGNED) JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_sched_booking_detail.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_sched_booking_detail.id)", "WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]'  AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (SELECT count(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = CAST(sis_course_schedule.id AS CHAR)) = 0 AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db64664 IN ('1','2') {$scheduled_course_time_sql}");
            $total_course_sessions_bookings_status_attended = $this->pull_field("sis_session_bookings JOIN sis_scheduled_sessions ON sis_scheduled_sessions.id = CAST(db59901 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_session_bookings.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_session_bookings.id)", "WHERE sis_session_bookings.usergroup='$_SESSION[usergroup]'  AND (sis_session_bookings.rec_archive IS NULL OR sis_session_bookings.rec_archive = '') AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db59903 IN ('1','2') {$scheduled_session_time_sql}");
            $total_course_sessions_bookings_status_attended = $total_course_sessions_bookings_status_attended + $total_course_bookings_no_sessions_status_attended;

            //did not attend attendance status of sessions held
            $total_course_bookings_no_sessions_status_did_not_attend = $this->pull_field("sis_sched_booking_detail JOIN sis_scheduled_booking ON sis_scheduled_booking.id = CAST(db15052 as UNSIGNED) JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_sched_booking_detail.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_sched_booking_detail.id)", "WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]'  AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (SELECT count(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = CAST(sis_course_schedule.id AS CHAR)) = 0 AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db64664 IN ('3') {$scheduled_course_time_sql}");
            $total_course_sessions_bookings_status_did_not_attend = $this->pull_field("sis_session_bookings JOIN sis_scheduled_sessions ON sis_scheduled_sessions.id = CAST(db59901 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_session_bookings.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_session_bookings.id)", "WHERE sis_session_bookings.usergroup='$_SESSION[usergroup]'  AND (sis_session_bookings.rec_archive IS NULL OR sis_session_bookings.rec_archive = '') AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db59903 IN ('3') {$scheduled_session_time_sql}");
            $total_course_sessions_bookings_status_did_not_attend = $total_course_sessions_bookings_status_did_not_attend + $total_course_bookings_no_sessions_status_did_not_attend;

            //cancelled attendance status of sessions held
            $total_course_bookings_no_sessions_status_cancelled = $this->pull_field("sis_sched_booking_detail JOIN sis_scheduled_booking ON sis_scheduled_booking.id = CAST(db15052 as UNSIGNED) JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_sched_booking_detail.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_sched_booking_detail.id)", "WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]'  AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (SELECT count(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = CAST(sis_course_schedule.id AS CHAR)) = 0 AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db64664 IN ('4','5','8','11','14') {$scheduled_course_time_sql}");
            $total_course_sessions_bookings_status_cancelled = $this->pull_field("sis_session_bookings JOIN sis_scheduled_sessions ON sis_scheduled_sessions.id = CAST(db59901 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_session_bookings.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_session_bookings.id)", "WHERE sis_session_bookings.usergroup='$_SESSION[usergroup]'  AND (sis_session_bookings.rec_archive IS NULL OR sis_session_bookings.rec_archive = '') AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db59903 IN ('4','5','8','11','14') {$scheduled_session_time_sql}");
            $total_course_sessions_bookings_status_cancelled = $total_course_sessions_bookings_status_cancelled + $total_course_bookings_no_sessions_status_cancelled;

            //cancelled attendance status of sessions held - Cancelled by learner prior
            $total_course_bookings_no_sessions_status_cancelled_by_learner_prior = $this->pull_field("sis_sched_booking_detail JOIN sis_scheduled_booking ON sis_scheduled_booking.id = CAST(db15052 as UNSIGNED) JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_sched_booking_detail.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_sched_booking_detail.id)", "WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]'  AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (SELECT count(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = CAST(sis_course_schedule.id AS CHAR)) = 0 AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db64664 IN ('4') {$scheduled_course_time_sql}");
            $total_course_sessions_bookings_status_cancelled_by_learner_prior = $this->pull_field("sis_session_bookings JOIN sis_scheduled_sessions ON sis_scheduled_sessions.id = CAST(db59901 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_session_bookings.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_session_bookings.id)", "WHERE sis_session_bookings.usergroup='$_SESSION[usergroup]'  AND (sis_session_bookings.rec_archive IS NULL OR sis_session_bookings.rec_archive = '') AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db59903 IN ('4') {$scheduled_session_time_sql}");
            $total_course_sessions_bookings_status_cancelled_by_learner_prior = $total_course_sessions_bookings_status_cancelled_by_learner_prior + $total_course_bookings_no_sessions_status_cancelled_by_learner_prior;

            //cancelled attendance status of sessions held - Cancelled by college
            $total_course_bookings_no_sessions_status_cancelled_by_college = $this->pull_field("sis_sched_booking_detail JOIN sis_scheduled_booking ON sis_scheduled_booking.id = CAST(db15052 as UNSIGNED) JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_sched_booking_detail.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_sched_booking_detail.id)", "WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]'  AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (SELECT count(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = CAST(sis_course_schedule.id AS CHAR)) = 0 AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db64664 IN ('5') {$scheduled_course_time_sql}");
            $total_course_sessions_bookings_status_cancelled_by_college = $this->pull_field("sis_session_bookings JOIN sis_scheduled_sessions ON sis_scheduled_sessions.id = CAST(db59901 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_session_bookings.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_session_bookings.id)", "WHERE sis_session_bookings.usergroup='$_SESSION[usergroup]'  AND (sis_session_bookings.rec_archive IS NULL OR sis_session_bookings.rec_archive = '') AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db59903 IN ('5') {$scheduled_session_time_sql}");
            $total_course_sessions_bookings_status_cancelled_by_college = $total_course_sessions_bookings_status_cancelled_by_college + $total_course_bookings_no_sessions_status_cancelled_by_college;

            //cancelled attendance status of sessions held - Cancelled by college Scheduled Course NOT cancelled
            $total_course_bookings_no_sessions_status_cancelled_by_college_course_not_cancelled = $this->pull_field("sis_sched_booking_detail JOIN sis_scheduled_booking ON sis_scheduled_booking.id = CAST(db15052 as UNSIGNED) JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_sched_booking_detail.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_sched_booking_detail.id)", "WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]'  AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (SELECT count(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = CAST(sis_course_schedule.id AS CHAR)) = 0 AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db64664 IN ('5') AND db14959 !='5' {$scheduled_course_time_sql}");
            $total_course_sessions_bookings_status_cancelled_by_college_course_not_cancelled = $this->pull_field("sis_session_bookings JOIN sis_scheduled_sessions ON sis_scheduled_sessions.id = CAST(db59901 AS UNSIGNED) JOIN sis_course_schedule ON sis_course_schedule.id = CAST(sis_scheduled_sessions.rel_id AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_session_bookings.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_session_bookings.id)", "WHERE sis_session_bookings.usergroup='$_SESSION[usergroup]'  AND (sis_session_bookings.rec_archive IS NULL OR sis_session_bookings.rec_archive = '') AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db59903 IN ('5') AND db14959 !='5' {$scheduled_session_time_sql}");
            $total_course_sessions_bookings_status_cancelled_by_college_course_not_cancelled = $total_course_sessions_bookings_status_cancelled_by_college_course_not_cancelled + $total_course_bookings_no_sessions_status_cancelled_by_college_course_not_cancelled;


            //cancelled attendance status of sessions held - Cancelled by learner during
            $total_course_bookings_no_sessions_status_cancelled_by_learner_during = $this->pull_field("sis_sched_booking_detail JOIN sis_scheduled_booking ON sis_scheduled_booking.id = CAST(db15052 as UNSIGNED) JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_sched_booking_detail.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_sched_booking_detail.id)", "WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]'  AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (SELECT count(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = CAST(sis_course_schedule.id AS CHAR)) = 0 AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db64664 IN ('8') {$scheduled_course_time_sql}");
            $total_course_sessions_bookings_status_cancelled_by_learner_during = $this->pull_field("sis_session_bookings JOIN sis_scheduled_sessions ON sis_scheduled_sessions.id = CAST(db59901 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_session_bookings.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_session_bookings.id)", "WHERE sis_session_bookings.usergroup='$_SESSION[usergroup]'  AND (sis_session_bookings.rec_archive IS NULL OR sis_session_bookings.rec_archive = '') AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db59903 IN ('8') {$scheduled_session_time_sql}");
            $total_course_sessions_bookings_status_cancelled_by_learner_during = $total_course_sessions_bookings_status_cancelled_by_learner_during + $total_course_bookings_no_sessions_status_cancelled_by_learner_during;

            //cancelled attendance status of sessions held - Cancelled within 48 hours
            $total_course_bookings_no_sessions_status_cancelled_by_learner_within48hours = $this->pull_field("sis_sched_booking_detail JOIN sis_scheduled_booking ON sis_scheduled_booking.id = CAST(db15052 as UNSIGNED) JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_sched_booking_detail.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_sched_booking_detail.id)", "WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]'  AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (SELECT count(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = CAST(sis_course_schedule.id AS CHAR)) = 0 AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db64664 IN ('11') {$scheduled_course_time_sql}");
            $total_course_sessions_bookings_status_cancelled_by_learner_within48hours = $this->pull_field("sis_session_bookings JOIN sis_scheduled_sessions ON sis_scheduled_sessions.id = CAST(db59901 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_session_bookings.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_session_bookings.id)", "WHERE sis_session_bookings.usergroup='$_SESSION[usergroup]'  AND (sis_session_bookings.rec_archive IS NULL OR sis_session_bookings.rec_archive = '') AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db59903 IN ('11') {$scheduled_session_time_sql}");
            $total_course_sessions_bookings_status_cancelled_by_learner_within48hours = $total_course_sessions_bookings_status_cancelled_by_learner_within48hours + $total_course_bookings_no_sessions_status_cancelled_by_learner_within48hours;

            //cancelled attendance status of sessions held - Cancelled due to DNA
            $total_course_bookings_no_sessions_status_cancelled_due_to_dna = $this->pull_field("sis_sched_booking_detail JOIN sis_scheduled_booking ON sis_scheduled_booking.id = CAST(db15052 as UNSIGNED) JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_sched_booking_detail.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_sched_booking_detail.id)", "WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]'  AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (SELECT count(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = CAST(sis_course_schedule.id AS CHAR)) = 0 AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db64664 IN ('14') {$scheduled_course_time_sql}");
            $total_course_sessions_bookings_status_cancelled_due_to_dna  = $this->pull_field("sis_session_bookings JOIN sis_scheduled_sessions ON sis_scheduled_sessions.id = CAST(db59901 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_session_bookings.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_session_bookings.id)", "WHERE sis_session_bookings.usergroup='$_SESSION[usergroup]'  AND (sis_session_bookings.rec_archive IS NULL OR sis_session_bookings.rec_archive = '') AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND db59903 IN ('14') {$scheduled_session_time_sql}");
            $total_course_sessions_bookings_status_cancelled_due_to_dna  = $total_course_sessions_bookings_status_cancelled_due_to_dna  + $total_course_bookings_no_sessions_status_cancelled_due_to_dna;

            //blank attendance status of sessions held
            $total_course_bookings_no_sessions_status_blank = $this->pull_field("sis_sched_booking_detail JOIN sis_scheduled_booking ON sis_scheduled_booking.id = CAST(db15052 as UNSIGNED) JOIN sis_course_schedule ON sis_course_schedule.id = CAST(db14977 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_sched_booking_detail.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_sched_booking_detail.id)", "WHERE sis_sched_booking_detail.usergroup='$_SESSION[usergroup]'  AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND (SELECT count(*) FROM sis_scheduled_sessions WHERE sis_scheduled_sessions.rel_id = CAST(sis_course_schedule.id AS CHAR)) = 0 AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='')  AND (db64664 IS NULl OR db64664 = '') {$scheduled_course_time_sql}");
            $total_course_sessions_bookings_status_blank = $this->pull_field("sis_session_bookings JOIN sis_scheduled_sessions ON sis_scheduled_sessions.id = CAST(db59901 AS UNSIGNED) JOIN core_students on core_students.id = CAST(sis_session_bookings.rel_id as UNSIGNED) JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id", "count(sis_session_bookings.id)", "WHERE sis_session_bookings.usergroup='$_SESSION[usergroup]'  AND (sis_session_bookings.rec_archive IS NULL OR sis_session_bookings.rec_archive = '') AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive ='') AND (db59903 IS NULl OR db59903 = '') {$scheduled_session_time_sql}");
            $total_course_sessions_bookings_status_blank = $total_course_sessions_bookings_status_blank + $total_course_bookings_no_sessions_status_blank;

            $eois = $this->pull_field("sis_profiles","count(id)","WHERE usergroup='$_SESSION[usergroup]' {$_REQUEST['time_sql']}");
            $applications = $this->pull_field("sis_profiles JOIN sis_profile_convert ON sis_profiles.rel_id = sis_profile_convert.rel_id","count(*)","WHERE sis_profiles.usergroup='$_SESSION[usergroup]' AND db63026 = '5' {$_REQUEST['time_sql']}");
            $students = $this->pull_field("sis_profiles JOIN sis_profile_convert ON sis_profiles.rel_id = sis_profile_convert.rel_id","count(*)","WHERE sis_profiles.usergroup='$_SESSION[usergroup]' AND db63026 = '10' {$_REQUEST['time_sql']}");

            $total_eois = $this->pull_field("sis_profiles","count(id)","WHERE usergroup='$_SESSION[usergroup]' AND  ((SELECT db166247 FROM lead_mrn_stages WHERE id = db48581)  IN ('eois')) AND (rec_archive IS NULL OR rec_archive = '')");
            $total_applications = $this->pull_field("sis_profiles","count(*)","WHERE sis_profiles.usergroup='$_SESSION[usergroup]' AND  ((SELECT db166247 FROM lead_mrn_stages WHERE id = db48581)  IN ('applicants')) AND (rec_archive IS NULL OR rec_archive = '')");
            $total_students = $this->pull_field("sis_profiles","count(*)","WHERE sis_profiles.usergroup='$_SESSION[usergroup]' AND  ((SELECT db166247 FROM lead_mrn_stages WHERE id = db48581)  IN ('students')) AND (rec_archive IS NULL OR rec_archive = '')");


            $active_eoi_learners_text = $this->pull_field ("lead_mrn_stages","group_concat(lower(db49848))","WHERE db49851 = 'eois' AND db166247 = 'eois'");
            $active_applicants_learners_text = $this->pull_field ("lead_mrn_stages","group_concat(lower(db49848))","WHERE db49851 = 'applicants' AND db166247 = 'applicants'");
            $active_students_learners_text = $this->pull_field ("lead_mrn_stages","group_concat(lower(db49848))","WHERE db49851 = 'students' AND db166247 = 'students'");

            $inactive_eoi_learners_text = $this->pull_field ("lead_mrn_stages","group_concat(lower(db49848))","WHERE db49851 = 'eois' AND db166247 != 'eois'");
            $inactive_applicants_learners_text = $this->pull_field ("lead_mrn_stages","group_concat(lower(db49848))","WHERE db49851 = 'applicants' AND db166247 != 'applicants'");
            $inactive_students_learners_text = $this->pull_field ("lead_mrn_stages","group_concat(lower(db49848))","WHERE db49851 = 'students' AND db166247 != 'students'");

            $cancelled_courses = $this->pull_field("sis_course_schedule", "count(id)", "WHERE db14959='5' AND usergroup='$_SESSION[usergroup]' AND (rec_archive IS NULL OR rec_archive = '') {$scheduled_course_time_sql}");

            $attendance = $this->pull_field("sis_sched_booking_detail", "count(id)", "WHERE db64664 IN ('1','2') AND usergroup='$_SESSION[usergroup]' AND (rec_archive IS NULL OR rec_archive = '') {$booking_time_sql}");
            $attendance_percent = round(($attendance/$total_course_bookings)*100);
            $attendance_text = ($total_course_bookings==0)?"0 (0%)":"$attendance ($attendance_percent)%";

            $cancelled_by_learner_attendance =
            $key_stats['0. Current Total Profiles'] = [
                [
                    'title' => ' Current Total EOIs ',
                    'class' => 'status_info',
                    'count' => $total_eois
                ],
                [
                    'title' => 'Current Total Applications ',
                    'class' => 'status_info',
                    'count' => $total_applications
                ],
                [
                    'title' => 'Current Total Students',
                    'class' => 'status_info',
                    'count' => $total_students
                ],
            ];
            $key_stats['1. Profiles'] = [
                [
                    'title' => 'EOIs - total profiles created',
                    'class' => 'status_info',
                    'count' => $eois
                ],
                [
                    'title' => 'Applications - created profiles converted to applicant',
                    'class' => 'status_warning',
                    'count' => $applications
                ],
                [
                    'title' => 'Students - created profiles converted to student',
                    'class' => 'status_success',
                    'count' => $students
                ],
                [
                    'title' => "Active Profiles - all eois ($active_eoi_learners_text), applications ($active_applicants_learners_text) and students ($active_students_learners_text)",
                    'class' => 'status_primary',
                    'count' => pull_field("sis_profiles", "count(id)", "WHERE usergroup='$_SESSION[usergroup]' AND  ((SELECT db166247 FROM lead_mrn_stages WHERE id = db48581)  IN ('eois','applicants','students')) AND (rec_archive IS NULL OR rec_archive = '') {$_REQUEST['time_sql']}"),
                ],
                [
                    'title' => "Inactive Profiles - all eois ($inactive_eoi_learners_text), applications ($inactive_applicants_learners_text) and students ($inactive_students_learners_text)",
                    'class' => 'status_draft',
                    'count' => pull_field("sis_profiles", "count(id)", "WHERE usergroup='$_SESSION[usergroup]' AND (((SELECT db166247 FROM lead_mrn_stages WHERE id = db48581)  NOT IN ('eois','applicants','students')) AND (rec_archive IS NULL OR rec_archive = '')) {$_REQUEST['time_sql']}"),
                ],
                [
                    'title' => 'Archived Profiles',
                    'class' => 'status_draft',
                    'count' => pull_field("sis_profiles", "count(id)", "WHERE usergroup='$_SESSION[usergroup]' AND  (rec_archive IS NOT NULL AND rec_archive != '') {$_REQUEST['time_sql']}"),
                ],
            ];
            $key_stats['2. Bookings'] = [

                [
                    'title' => 'Course Bookings - total bookings (all statuses) made for any student and course',
                    'class' => 'status_success',
                    'count' => $total_course_bookings,
                ],
                [
                    'title' => 'Active Course Bookings - total bookings (booked,confirmed attending,in learning,completed) ',
                    'class' => 'status_success',
                    'count' => $active_course_bookings,
                ],
                [
                    'title' => 'Distinct profile bookings - number of distinct profiles booked (any status) ',
                    'class' => 'status_success',
                    'count' => $unique_student_bookings,
                ],
                [
                    'title' => 'Cancelled Scheduled Courses',
                    'class' => 'status_info',
                    'count' => $cancelled_courses,
                ],
            ];
            $key_stats['3. Sessions'] = [
                [
                    'title' => 'Scheduled Courses (single session) plus Scheduled Sessions start date (any status)',
                    'class' => 'status_success',
                    'count' => $total_sessions,
                ],
                [
                    'title' => 'Scheduled Courses (single session) plus Scheduled Sessions start date (available for booking,full,complete,available for admin only)',
                    'class' => 'status_success',
                    'count' => $total_sessions_active,
                ],
            ];
            $key_stats['4. Session Bookings'] = [
                [
                    'title' => 'Scheduled Courses (single session) plus Scheduled Sessions total bookings (any status)',
                    'class' => 'status_success',
                    'count' => $total_course_sessions_bookings,
                ],

                [
                    'title' => 'Scheduled Courses (single session) plus Scheduled Sessions active bookings (booked,confirmed attending,in learning,completed)',
                    'class' => 'status_success',
                    'count' => $total_course_sessions_bookings_active,
                ],
                [
                    'title' => 'Scheduled Courses (single session) plus Scheduled Sessions total bookings (any status) distinct profiles',
                    'class' => 'status_success',
                    'count' => $unique_students_sessions_bookings,
                ],
            ];
            $key_stats['5. Session Attendance'] = [
                [
                    'title' => 'Scheduled Courses (single session) plus Scheduled Sessions any bookng status - attended (attended, partially attended)',
                    'class' => 'status_success',
                    'count' => $total_course_sessions_bookings_status_attended,
                ],


                [
                    'title' => 'Scheduled Courses (single session) plus Scheduled Sessions any bookng status - Did Not Attend',
                    'class' => 'status_danger',
                    'count' => $total_course_sessions_bookings_status_did_not_attend,
                ],
                [
                    'title' => 'Scheduled Courses (single session) plus Scheduled Sessions any bookng status - Attendance Cancelled (cancelled by learner prior, cancelled by college, cancelled by learner during, cancelled within 48 hours, cancelled due to DNA) ',
                    'class' => 'status_warning',
                    'count' => $total_course_sessions_bookings_status_cancelled,
                ],
                [
                    'title' => 'Scheduled Courses (single session) plus Scheduled Sessions any bookng status Blank attendance status',
                    'class' => 'status_draft',
                    'count' => $total_course_sessions_bookings_status_blank
                ],
            ];
            $key_stats['6. Session Cancelled Attendance'] = [
                [
                    'title' => 'Scheduled Courses (single session) plus Scheduled Sessions any bookng status - Attendance Cancelled by learner prior ',
                    'class' => 'status_warning',
                    'count' => $total_course_sessions_bookings_status_cancelled_by_learner_prior,
                ],
                [
                    'title' => 'Scheduled Courses (single session) plus Scheduled Sessions any bookng status - Attendance Cancelled by college ',
                    'class' => 'status_warning',
                    'count' => $total_course_sessions_bookings_status_cancelled_by_college,
                ],
                [
                    'title' => 'Scheduled Courses (single session) plus Scheduled Sessions any bookng status - Attendance Cancelled by college - Course not Cancelled ',
                    'class' => 'status_warning',
                    'count' => $total_course_sessions_bookings_status_cancelled_by_college_course_not_cancelled,
                ],
                [
                    'title' => 'Scheduled Courses (single session) plus Scheduled Sessions any bookng status - Attendance Cancelled by learner during ',
                    'class' => 'status_warning',
                    'count' => $total_course_sessions_bookings_status_cancelled_by_learner_during,
                ],
                [
                    'title' => 'Scheduled Courses (single session) plus Scheduled Sessions any bookng status - Attendance Cancelled by learner within 48 hours',
                    'class' => 'status_warning',
                    'count' => $total_course_sessions_bookings_status_cancelled_by_learner_within48hours,
                ],
                [
                    'title' => 'Scheduled Courses (single session) plus Scheduled Sessions any bookng status - Attendance Cancelled due to DNA',
                    'class' => 'status_warning',
                    'count' => $total_course_sessions_bookings_status_cancelled_due_to_dna,
                ],
                /*[
                    'title' => 'Courses held at community venues',
                    'class' => 'status_info',
                    'count' => $this->pull_field("sis_course_schedule", "count(id)", "WHERE db14954 NOT IN ('491', '536') AND usergroup='$_SESSION[usergroup]' AND (rec_archive IS NULL OR rec_archive = '') {$_REQUEST['time_sql']}"),
                ],*/
            ];
            if(!empty($_REQUEST['export'])){
                $applicants2 = new Students;
                $applicants2->export_formated2($key_stats,'csv',"Report info");
                exit();
            }
        }else{
            $enquiry_sql = "SELECT SUM(date BETWEEN LAST_DAY(NOW()-INTERVAL ".($months_from_now)." MONTH ) AND NOW()) as first_month
        $reg_sql
FROM lead_profiles WHERE usergroup = {$_SESSION['usergroup']} {$_REQUEST['time_sql']}";
            $key_stats[''] = [
                [
                    'title' => 'New Enquiries',
                    'class' => 'status_info',
                    'count' => pull_field("lead_profiles", "count(id)", "WHERE usergroup='$_SESSION[usergroup]' AND (rec_archive IS NULL OR rec_archive = '') AND (db1056 <= 1 or db1056 = '' or db1056 is null) ")
                ],
                [
                    'title' => 'Engaged Enquiries',
                    'class' => 'status_warning',
                    'count' => pull_field("lead_profiles", "count(id)", "WHERE usergroup='$_SESSION[usergroup]' AND (rec_archive IS NULL OR rec_archive = '') AND db1056 = 4 ")
                ],
                [
                    'title' => 'New Applications',
                    'class' => 'status_success',
                    'count' => pull_field("core_students", "count(id)", "WHERE usergroup='$_SESSION[usergroup]' AND db41=0"),

                ],
            ];
        }
        dev_debug($enquiry_sql);
        $stmt = $dbh->prepare($enquiry_sql);
        $stmt->execute();
        $booking_results = $stmt->fetchAll(2);

        dev_debug(json_encode($date_map));
        $reg_res = [];
        $booking_res = [];
        foreach ($date_map[0] as $key => $month) {
            array_push($reg_res, ['mDate' => $month, 'count' => $reg_results[0][$key]]);
            array_push($booking_res, ['mDate' => $month, 'count' => $booking_results[0][$key]]);
        }

        return ['registration_results' => $reg_res, 'booking_results' => $booking_res, 'key_stats' => $key_stats];
    }

    function get_line_chart_data($args = false): array
    {
        global $db;
        $dbh = get_dbh();
        $row = $db->rawQuery("SELECT * FROM core_reports where db68351 = '{$args['url_name']}'  and usergroup='{$_SESSION['usergroup']}' ")[0];

        $main_table = $this->get_floating(session_floating($row['db14898']));
        $query_table = empty(session_floating($row['db14897'])) ? session_floating($row['db16141']) : session_floating($row['db14897']);
        $where_override = $this->get_floating(session_floating($row['db14899']));


        $reg_sql = "";
        $months_from_now = '0';
        if(!empty($args['months_from_now'])){
            $months_from_now = $args['months_from_now'];
        }

            $dates_sql = "SELECT  LAST_DAY(DATE_SUB(NOW(), INTERVAL ".($months_from_now)." MONTH)) as first_month";
            if (!empty($args['num_of_months'])) {
                if($args["num_of_months"]>1){
                    $dates_sql.=", ";
                    $reg_sql.=",";
                    for ($i = 2; $i <= $args['num_of_months']; $i++) {
                        $prev = $i - 1;
                        $reg_sql .= "SUM(date BETWEEN DATE_ADD(LAST_DAY(NOW()-INTERVAL ".($months_from_now+$i)." MONTH ), INTERVAL 1 DAY) AND DATE_ADD(LAST_DAY(NOW()-INTERVAL ".($months_from_now+$i-1)." MONTH ), INTERVAL 1 DAY)) as {$prev}_months_ago";
                        $dates_sql .= "LAST_DAY(NOW()-INTERVAL ".($months_from_now + $prev)." MONTH ) as {$prev}_months_ago";
                        if ($i != $args['num_of_months']) {
                            $reg_sql .= ",
                            ";
                            $dates_sql .= ",
                            ";
                        }
                    }
                }
            }
            dev_debug($dates_sql);
            $date_map_stmt = $dbh->prepare($dates_sql);
        $date_map_stmt->execute();
        $date_map = $date_map_stmt->fetchAll(2);
        $results = [];
        $tables = json_decode($row['db9637'], true);
        $key_stats = [
            [
                'title' => "Grand total",
                'class' => $this->css_classes[6],
                'count' => 0,
            ]
        ];
        $grand_total = 0;
        foreach ($tables as $k => $table){
            $total = 0;
            $where = "";
            if(!empty($table['where'])){
                $where = " AND {$table['where']}";
            }
            if (!empty($where_override) && stripos($where_override, 'WHERE') != 0) {
                if (!empty($table['name'])) {
                    $final_where_override = " WHERE {$table['name']}.usergroup={$_SESSION['usergroup']}  and ({$table['name']}.rec_archive is null or {$table['name']}.rec_archive ='') AND ($where_override) $where";
                }
            } else {
                if (!empty($table['name'])) {
                    $final_where_override = " WHERE {$table['name']}.usergroup={$_SESSION['usergroup']} and ({$table['name']}.rec_archive is null or {$table['name']}.rec_archive ='') $where";
                }
            }
            dev_debug("final_where_override**$final_where_override**ROW**$row[id]**where_override**$where_override**".json_encode(strpos($where_override, 'WHERE') === false)."**");
            $from = $table['from']??$table['name'];

            $query = "SELECT SUM(date BETWEEN DATE_ADD(LAST_DAY(NOW()-INTERVAL ".($months_from_now+1)." MONTH ), INTERVAL 1 DAY) AND LAST_DAY(NOW()-INTERVAL ".($months_from_now)." MONTH )) as first_month
            $reg_sql
            FROM $from $final_where_override";

            dev_debug($query);

            //error_log ("ANITA line chart data **"+$query+"**");
            $stmt = $dbh->prepare($query);
            $stmt->execute();
            $reg_results = $stmt->fetchAll(2);

            $reg_res = [];
            if($args["num_of_months"]>1){
                foreach ($date_map[0] as $key => $month) {
                    $total += $reg_results[0][$key];
                    array_push($reg_res, ['mDate' => $month, 'count' => $reg_results[0][$key]]);
                }
                $grand_total += $total;
                $table['data'] = $reg_res;
                $table['chart_title'] = $row["db14903"];
                $table['chart_description'] = $row["db14904"];
                $table['chart_x_axis_title'] = $row["db68363"];
                $table['chart_y_axis_title'] = $row["db68366"];
                $results[] = $table;

                $key_stats [] = [
                    'title' => $table['title'],
                    'class' => $this->css_classes[$k % 7],
                    'count' => $total,
                ];
            }else{
                $table['data'] = [];
                $results[] = $table;
            }

        }
        $key_stats[0]["count"] = $grand_total;
        return ["data" =>$results, "key_stats"=>$key_stats];
    }

    function set_time_sql($url_name){
        global $db;
        $report = $db->rawQuery("SELECT * FROM core_reports where db68351 = '{$url_name}'  and usergroup='{$_SESSION['usergroup']}' ")[0];
		if(empty($report)){
			$report = $db->rawQuery("SELECT * FROM core_reports where db68351 = '{$url_name}'  and usergroup='{$this->main_ug}' ")[0];
		}
        $multi_date_occurrances = substr_count(($report['db9637'].$report['db14897'].$report['db14899']), "multi_date_sql");
        if($multi_date_occurrances>0){
            $dates_array = json_decode($report['db16143'],2);
            for ($i=0; $i<$multi_date_occurrances; $i++){
                if(!empty($_REQUEST['cohort'])) {
                    $_REQUEST['multi_date_sql_'.$i] = " AND db890 = {$_REQUEST['year']} ";
                    $_REQUEST['time_sql'] = " AND db890 = {$_REQUEST['year']} ";
                    $_REQUEST['table_time_sql'] = " AND db890 = {$_REQUEST['year']} ";
                }
                if(!empty($_REQUEST['cohort_like'])) {
                    $_REQUEST['multi_date_sql_'.$i] = " AND db890 like '%{$_REQUEST['year']}%' ";
                    $_REQUEST['time_sql'] = " AND db890 like '%{$_REQUEST['year']}%' ";
                    $_REQUEST['table_time_sql'] = " AND db890 like '%{$_REQUEST['year']}%' ";
                }
                if(!empty($_REQUEST['year'])) {
                    $_REQUEST['multi_date_sql_'.$i] = " AND YEAR({$dates_array[$i]}) = {$_REQUEST['year']} ";
                    if (!empty($_REQUEST['date_field'])) {
                        $_REQUEST['time_sql'] = " AND YEAR({$_REQUEST['date_field']}) = {$_REQUEST['year']} ";
                        $_REQUEST['table_time_sql'] = " AND YEAR({$_REQUEST['date_field']}) = {$_REQUEST['year']} ";
                    }
                }
                if(!empty($_REQUEST['time_frame'])) {
                    $_REQUEST['multi_date_sql_'.$i] = " AND DATE({$dates_array[$i]}) >= now()-interval {$_REQUEST['time_frame']} month ";
                    $_REQUEST['time_sql'] = " AND DATE({$_REQUEST['date_field']}) >= now()-interval {$_REQUEST['time_frame']} month ";
                    $_REQUEST['table_time_sql'] = " AND DATE({$_REQUEST['date_field']}) >= now()-interval {$_REQUEST['time_frame']} month ";
                }
                if(!empty($_REQUEST['time_range'])) {
                    $_REQUEST['multi_date_sql_'.$i] = " AND DATE({$dates_array[$i]}) between {$_REQUEST['time_range']} ";
                    $_REQUEST['time_sql'] = " AND DATE({$_REQUEST['date_field']}) between {$_REQUEST['time_range']} ";
                    $_REQUEST['table_time_sql'] = " AND DATE({$_REQUEST['date_field']}) between {$_REQUEST['time_range']} ";
                }
            }
        }else{
            if($_REQUEST['date_field']=="cohort"){
                $year = $_REQUEST['year']?:pull_field("form_schools", "db36", "WHERE id={$_SESSION['usergroup']}");
                $_REQUEST['time_sql'] = " AND db890 = {$year} ";
                $_REQUEST['table_time_sql'] = " AND db890 = {$year} ";
            }else if(!empty($_REQUEST['cohort_like'])) {
                $year = $_REQUEST['year']?:pull_field("form_schools", "db36", "WHERE id={$_SESSION['usergroup']}");
                $_REQUEST['time_sql'] = " AND db890 like '%$year%' ";
                $_REQUEST['table_time_sql'] = " AND db890 like '%$year%' ";
            }else{
                if (!empty($_REQUEST['date_field'])) {
                    if(!empty($_REQUEST['year'])) {
                        $_REQUEST['time_sql'] = " AND YEAR({$_REQUEST['date_field']}) = {$_REQUEST['year']} ";
                        $_REQUEST['table_time_sql'] = " AND YEAR({$_REQUEST['date_field']}) = {$_REQUEST['year']} ";
                    }
                    if(!empty($_REQUEST['time_frame'])) {
                            $school_type = pull_field("form_schools","db30","WHERE id = $_SESSION[usergroup]");
                            //if ($school_type == 12) {//MRN
                            //    $_REQUEST['time_sql'] = " AND YEAR({$_REQUEST['date_field']}) = YEAR(now()-interval {$_REQUEST['time_frame']} month) AND MONTH({$_REQUEST['date_field']}) = MONTH(now()-interval {$_REQUEST['time_frame']} month) ";
                            //    $_REQUEST['table_time_sql'] = " AND YEAR({$_REQUEST['date_field']}) = YEAR(now()-interval {$_REQUEST['time_frame']} month) AND MONTH({$_REQUEST['date_field']}) = MONTH(now()-interval {$_REQUEST['time_frame']} month) ";

                            //} else {
                                $_REQUEST['time_sql'] = " AND DATE({$_REQUEST['date_field']}) > now()-interval {$_REQUEST['time_frame']} month AND DATE({$_REQUEST['date_field']}) <= now()";
                                $_REQUEST['table_time_sql'] = " AND DATE({$_REQUEST['date_field']}) > now()-interval {$_REQUEST['time_frame']} month AND DATE({$_REQUEST['date_field']}) <= now()";
                            //}
                        }
                    if(!empty($_REQUEST['time_range'])) {
                        $_REQUEST['time_sql'] = " AND DATE({$_REQUEST['date_field']}) between {$_REQUEST['time_range']} ";
                        $_REQUEST['table_time_sql'] = " AND DATE({$_REQUEST['date_field']}) between {$_REQUEST['time_range']} ";
                    }
                }

            }
            dev_debug ("TIME SQL $_REQUEST[time_sql]");
            if (strpos($_REQUEST['time_sql'],'db59835') !== false) {
                $my_time = $_REQUEST['time_sql'];
                $_GET['SESSION_START_TIME_SQL'] = $_REQUEST['time_sql'];
                $_GET['COURSE_START_TIME_SQL'] = str_replace('sis_scheduled_sessions.db59835', 'sis_course_schedule.db14947', $my_time);
            }
        }
    }

    function usergroups_management()
    {
// if admin show everything not just group
        if (session_info("ulevel") == 9) {
            $usergroups = "usergroup!=''";
        } else {
            $usergroups = "usergroup='$_SESSION[usergroup]'";
        }
        return $usergroups;
    }

    function get_aggregated_data($args)
    {
        global $db;
        $dbh = get_dbh();
        $usergroups = $this->usergroups_management();
        $row = $db->rawQuery("SELECT * FROM core_reports where db68351 = '{$args['url_name']}'  and usergroup='{$_SESSION['usergroup']}' ")[0];
        if (empty($row)) {
            $row = $db->rawQuery("SELECT * FROM core_reports where db68351 = '{$args['url_name']}'  and usergroup='{$this->main_ug}'")[0];
        }
        dev_debug("Full Query $row[db254774]");
        dev_debug ("TIME SQL $_REQUEST[time_sql]");
        dev_debug("SESSION_START_TIME_SQL $_GET[SESSION_START_TIME_SQL]");
        dev_debug("COURSE_START_TIME_SQL $_GET[COURSE_START_TIME_SQL]");
        $full_query = $this->get_floating(session_floating($row['db254774']));
        if (empty($full_query)) {
            $table_for_query = empty(session_floating($row['db14897'])) ? $this->get_floating(session_floating($row['db16141'])) : $this->get_floating(session_floating($row['db14897']));
            $main_table = $this->get_floating(session_floating($row['db14898']));
            $where_override = $this->get_floating(session_floating($row['db14899']));
            //$hasWhere = is_numeric(strpos($where_override, strtolower('WHERE')));

            if (!empty($where_override)) {
                if (!empty($main_table)) {
                    $final_where_override = " WHERE $main_table.usergroup={$_SESSION['usergroup']}  and ($main_table.rec_archive is null or $main_table.rec_archive ='') AND ($where_override)";
                }
            } else {
                if (!empty($main_table)) {
                    $final_where_override = " WHERE $main_table.usergroup={$_SESSION['usergroup']} and ($main_table.rec_archive is null or $main_table.rec_archive ='')";
                }
            }

            dev_debug("final_where_override**$final_where_override**ROW**$row[id]**where_override**$where_override**" . json_encode($hasWhere) . "**");
            $parent_report = pull_field("core_reports", 'db14895', "WHERE db68351 = '{$args['url_name']}'");
            if (!empty($_GET['filter'])) {
                $filter_sql = $args['filter_sql'];
            }
            $sql = "SELECT count(*) FROM {$table_for_query} {$final_where_override} {$filter_sql} {$_REQUEST['time_sql']}"; //WHERE $usergroups and (rec_archive is null or rec_archive ='') AND ($where_override)";
            $sql_count = $dbh->prepare("$sql");
            dev_debug($sql);
            dev_debug(json_encode($row, JSON_PRETTY_PRINT));
            $sql_count->execute();
            $count_results = $sql_count->fetchColumn();
            $group_by = $row['db14900'];
            $legend = session_floating($row['db14902']);
            $detailed_page = $row['db14908'];
            //do the code
            //if the report has an order by value then let that i.e db14901 be used for ordering
            if (!empty($row['db14901'])) {
                $order_by = $row['db14901'];
                $order_by = "CAST($order_by AS SIGNED) ASC";
            }
            else {
                $order_by = 'Total_Value DESC';
            }
            $code = "SELECT $group_by as 'my_group', count(*) as 'Total_Value',($legend) AS 'type' FROM $table_for_query {$final_where_override} {$filter_sql} {$_REQUEST['time_sql']} GROUP BY my_group ORDER BY $order_by";
//        echo"<pre>$code</pre>";

            $dbh->query("SET group_concat_max_len = 18446744073709547520");
            $sql = $dbh->prepare("$code");
            $sql->execute();
            dev_debug($code);
            $results = $sql->fetchAll(PDO::FETCH_ASSOC);
        } else {

            //dev_debug("Full Query $full_query");
            //dev_debug ("TIME SQL $_REQUEST[time_sql]");
            //dev_debug("SESSION_START_TIME_SQL $_GET[SESSION_START_TIME_SQL]");
            //dev_debug("COURSE_START_TIME_SQL $_GET[COURSE_START_TIME_SQL]");
            //$code = $this->get_floating(session_floating($full_query));
            $dbh->query("SET group_concat_max_len = 18446744073709547520");
            $sql = $dbh->prepare("$full_query");
            $sql->execute();
            dev_debug(" Pure SQL $full_query");
            $my_results = $sql->fetchAll(PDO::FETCH_ASSOC);
            $i=0;
            foreach ($my_results as $my_result) {
                if ($my_result['my_group'] == 'TOTAL') {
                    $count_results =   $my_result['Total_Value'];
                    unset($my_results[$i]);
                    break;
                }
                $i++;
            }
            $results = $my_results;

        }
        dev_debug("COUNT RESULTS ". count($my_results) . "**". $count_results. "**");
        $label_array = (array_map(function ($item) {
            return $item['type'];
        }, $results));

        $data_array = (array_map(function ($item) {
            return intval($item['Total_Value']);
        }, $results));

        $results = (array_map(function ($item) use ($count_results) {
            return ['type' => $item['type'], 'total' => intval($item['Total_Value']), 'group' => $item['my_group'], 'percentage' => round(intval($item['Total_Value']) / intval($count_results) * 100, 2)];
        }, $results));

        $chart_stats = (array_map(function ($item) {
            return ['name' => empty($item['type']) ? "Not Specified" : $item['type'], 'y' => $item['total']];
        }, $results));


        $res = [
            'title' => $row['db14903'],
            'description' => $row['db14904'],
            'chart_type' => $row['db14905'],
            'x_axis_title' => $row['db68363'],
            'y_axis_title' => $row['db68366'],
            'labels' => $label_array,
            'data' => $data_array,
            'total' => $count_results,
            'series' => $chart_stats,
            'stats' => $results,
            'detailed_page' => $detailed_page
        ];

        return $res;
    }

    function get_custom_aggregated_data($args)
    {
        if ($_SESSION['usergroup'] == 1) {
            return $this->get_internal_report($args);
        }
        global $db;
        global $paginator;
        $dbh = get_dbh();


        $report = $db->rawQuery("SELECT * FROM core_reports where db68351 = '{$args['url_name']}'  and usergroup='{$_SESSION['usergroup']}' ")[0];
		if (empty($report)){
			$report = $db->rawQuery("SELECT * FROM core_reports where db68351 = '{$args['url_name']}'  and usergroup='{$this->main_ug}' ")[0];
		}

        $query = $this->get_floating(session_floating($report['db9637']));
        $main_table = session_floating($report['db14898']);
        $query_table = empty(session_floating($report['db14897'])) ? session_floating($report['db16141']) : session_floating($report['db14897']);
        $where = session_floating($report['db14899']);
        $group_by = session_floating($report['db14900']);
        $group_by_sql = empty(session_floating($report['db14900'])) ? "" : " GROUP BY " . $group_by;
        //for aggregated report click-throughs which are in terms of group
        $group = $_REQUEST["group"];
        if (!empty($group)) {
            $group_sql = $group == 'all' ? '' : " AND ($group_by = '$group')";
            $group_by_sql = "";
        }
        // The above group goes into the where condition
        $order_by = empty(session_floating($report['db14901'])) ? "" : " ORDER BY " . session_floating($report['db14901']);
        $having_sql = "";

        if (!empty($_REQUEST['sort'])) {
            $order_by = "";
            $sort_array = json_decode($_REQUEST['sort']);
            $numItems = count($sort_array);
            $i = 0;
            foreach ($sort_array as $filter_item) {
                if ($i === 0) {
                    $order_by = ' ORDER BY ';
                }
                $order_by .= " `$filter_item->sort_by` $filter_item->sort_order";
                if (++$i !== $numItems) {
                    $order_by .= ',';
                }
            }
        }
        if (!empty($args['filter_sql'])) {
            $filter_sql = $args['filter_sql'];
        } else {
            $filter_sql = '';
        }
        if (!empty($_REQUEST['search'])) {
            $search_sqls = $this->get_search_sql($query);
            $search_sql = $search_sqls['search'];
            $having_sql = $search_sqls['having'];
        }
        if (!empty($_REQUEST['filter_array'])) {
            $new_having = "";
            $filter_array = json_decode($_REQUEST['filter_array'], 1);
            $numItems = count($filter_array);
            $i = 0;
            foreach ($filter_array as $filter_item) {
                $operators = [
                    "equals" => "= '{$filter_item['filterValue']}'",
                    "not_equal" => "<>'{$filter_item['filterValue']}'",
                    "starts_with" => "LIKE '{$filter_item['filterValue']}%'",
                    "contains" => "LIKE '%{$filter_item['filterValue']}%'",
                    "ends_with" => "LIKE '%{$filter_item['filterValue']}'",
                    "does_not_contain" => "NOT LIKE '%{$filter_item['filterValue']}%'",
                    "greater_than" => ">'{$filter_item['filterValue']}'",
                    "less_than" => "<'{$filter_item['filterValue']}'",
                    "before" => "< DATE('{$filter_item['filterValue']}')",
                    "after" => "> DATE('{$filter_item['filterValue']}')",
                    "between" => "BETWEEN {$filter_item['filterValue']}"];
                if($filter_item['operator']=='is_not_blank'){
                    $new_having .= " AND `{$filter_item['field']}` IS NOT NULL AND `{$filter_item['field']}` != '' ";
                }else if($filter_item['operator']=='is_blank'){
                    $new_having .= " AND `{$filter_item['field']}` IS NULL OR `{$filter_item['field']}` = '' ";
                }else{
                    $new_having .= " AND `{$filter_item['field']}` " . $operators[$filter_item['operator']];
                }
            }
            $having_sql = empty($having_sql) ? " HAVING 1 $new_having " : $having_sql . " AND $new_having";
        }
        if (empty($where)) {
            $where = "$main_table.usergroup={$_SESSION['usergroup']} and ($main_table.rec_archive is null or $main_table.rec_archive ='')";
        }

        $sql = "$query FROM $query_table WHERE $where $filter_sql $search_sql {$_REQUEST['time_sql']} $group_sql $group_by_sql $having_sql $order_by";
        if (!$_GET['export']) {
            $paginator->calculate_total_entries($sql);
            $limit_sql = $paginator->limit_sql();
        } else {
            $limit_sql = "";
        }
        $sql = $sql . " " . $limit_sql;
        dev_debug($sql);
        $sql = $dbh->prepare($sql);
        $sql->execute();
        $results = $sql->fetchAll(PDO::FETCH_ASSOC);

        if ($_REQUEST['export']) {
            ini_set('memory_limit', '-1');
            $csv_results = array();
            $csv_titles = array();
            foreach ($results[0] as $key => $value) {
                if (in_array($key, array("Manage", "manage"))) {
                    $key = "ID";
                }
                if ($key !== 'edit_link' && $key !== 'view_link') $csv_titles[$key] = ltrim($key);
            }
            $csv_results[] = $csv_titles;

            $i = 1;
            foreach ($results as $entry) {
                foreach ($entry as $key => $value) {
                    if ($key !== 'edit_link' && $key !== 'view_link') $csv_results[$i][$key] = ltrim($value);
                }
                $i++;
            }
            $applicants2 = new Students;
            $applicants2->export_formated2($csv_results,'csv',$report['db850']);
            exit();
        }
        if ($_REQUEST['json']) {
            $results = ['results' => $results, 'links' => $paginator->ajax_links(), 'total' => $paginator->total];
            die(json_encode($results));
        }
        return ['results' => $results, 'links' => $paginator->ajax_links(), 'total' => $paginator->total];
    }

    function get_list_view_data($args)
    {
        global $db;
        global $paginator;
        $dbh = get_dbh();
        $report = $db->rawQuery("SELECT * FROM core_reports where db68351 = '{$args['url_name']}' and usergroup='{$_SESSION['usergroup']}'")[0];
		if (empty($report)){
			$report = $db->rawQuery("SELECT * FROM core_reports where db68351 = '{$args['url_name']}' and usergroup='{$this->main_ug}'")[0];
		}
        $full_query = $this->get_floating(session_floating($report['db254774']));
        if (empty($full_query)) {
            $query = $this->get_floating(session_floating($report['db9637']));
            $main_table = $this->get_floating(session_floating($report['db14898']));
            $query_table = empty(session_floating($report['db14897'])) ? $this->get_floating(session_floating($report['db16141'])) : $this->get_floating(session_floating($report['db14897']));
            $where = $this->get_floating(session_floating($report['db14899']));
            $group_by = $this->get_floating(session_floating($report['db14900']));
            $group_by_sql = empty(session_floating($report['db14900'])) ? "" : " GROUP BY " . $group_by;
            //for aggregated report click-throughs which are in terms of group
            $group = $_REQUEST["group"];
            if (!empty($group)) {
                $group_sql = $group == 'all' ? '' : " AND ($group_by = '$group')";
                $group_by_sql = "";
            }
            // The above group goes into the where condition
            $order_by = empty(session_floating($report['db14901'])) ? "" : " ORDER BY " . session_floating($report['db14901']);
            $having_sql = "";

            if (!empty($_REQUEST['sort'])) {
                $order_by = "";
                $sort_array = json_decode($_REQUEST['sort']);
                $numItems = count($sort_array);
                $i = 0;
                foreach ($sort_array as $filter_item) {
                    if ($i === 0) {
                        $order_by = ' ORDER BY ';
                    }
                    $order_by .= " `$filter_item->sort_by` $filter_item->sort_order";
                    if (++$i !== $numItems) {
                        $order_by .= ',';
                    }
                }
            }
            if (!empty($args['filter_sql'])) {
                $filter_sql = $args['filter_sql'];
            } else {
                $filter_sql = '';
            }
            if (!empty($_REQUEST['search'])) {
                $search_sqls = $this->get_search_sql($query);
                $search_sql = $search_sqls['search'];
                $having_sql = $search_sqls['having'];
            }
            if (!empty($_REQUEST['filter_array'])) {
                $new_having = "";
                $filter_array = json_decode($_REQUEST['filter_array'], 1);
                $numItems = count($filter_array);
                $i = 0;
                foreach ($filter_array as $filter_item) {
                    $operators = [
                        "equals" => "= '{$filter_item['filterValue']}'",
                        "not_equal" => "<>'{$filter_item['filterValue']}'",
                        "starts_with" => "LIKE '{$filter_item['filterValue']}%'",
                        "contains" => "LIKE '%{$filter_item['filterValue']}%'",
                        "ends_with" => "LIKE '%{$filter_item['filterValue']}'",
                        "does_not_contain" => "NOT LIKE '%{$filter_item['filterValue']}%'",
                        "is_not_blank" => "NOT LIKE '%{$filter_item['filterValue']}%'",
                        "greater_than" => ">'{$filter_item['filterValue']}'",
                        "less_than" => "<'{$filter_item['filterValue']}'",
                        "before" => "< DATE('{$filter_item['filterValue']}')",
                        "after" => "> DATE('{$filter_item['filterValue']}')",
                        "between" => "BETWEEN {$filter_item['filterValue']}"];
                    if ($filter_item['operator'] == 'is_not_blank') {
                        $new_having .= " AND `{$filter_item['field']}` IS NOT NULL AND `{$filter_item['field']}` != '' ";
                    } else if ($filter_item['operator'] == 'is_blank') {
                        $new_having .= " AND `{$filter_item['field']}` IS NULL OR `{$filter_item['field']}` = '' ";
                    } else {
                        $new_having .= " AND `{$filter_item['field']}` " . $operators[$filter_item['operator']];
                    }
                }
                $having_sql = empty($having_sql) ? " HAVING 1 $new_having " : $having_sql . " AND $new_having";
            }
            if (empty($where)) {
                $where = "$main_table.usergroup={$_SESSION['usergroup']} and ($main_table.rec_archive is null or $main_table.rec_archive ='')";
            } else {
                $where = "$where AND $main_table.usergroup={$_SESSION['usergroup']} and ($main_table.rec_archive is null or $main_table.rec_archive ='')";
            }

            $sql = "$query FROM $query_table WHERE $where $filter_sql $search_sql {$_REQUEST['time_sql']} $group_sql $group_by_sql $having_sql $order_by";
            $sql = str_replace("?", "/?", $sql);
        } else {
            $sql = $full_query;
        }
        if (!$_GET['export']) {
            $paginator->calculate_total_entries($sql, false, false, true);
            $limit_sql = $paginator->limit_sql();
        } else {
            $limit_sql = "";
        }
        $sql = $sql . " " . $limit_sql;
        dev_debug("Detailed Report List: $sql" );

        //error_log("ANITA Detailed Report List: $sql" );
        //error_log("Detailed Report List: $sql" );
        $sql = $dbh->prepare($sql);
        $sql->execute();
        $results = $sql->fetchAll(PDO::FETCH_ASSOC);
//error_log("ANITA PATH**".base_path . '**/**' . front_header_file_location . "**/admin/detailed_reports.php**");
        if (file_exists( front_header_file_location . "/admin/detailed_reports.php")) {
            include_once( front_header_file_location . "/admin/detailed_reports.php");
        }

        if ($_REQUEST['export']) {
            ini_set('memory_limit', '-1');
            $csv_results = array();
            $csv_titles = array();
            foreach ($results[0] as $key => $value) {
                if (in_array($key, array("Manage", "manage"))) {
                    $key = "ID";
                }
                if ($key !== 'edit_link' && $key !== 'view_link') $csv_titles[$key] = ltrim($key);
            }
            $csv_results[] = $csv_titles;

            $i = 1;
            foreach ($results as $entry) {
                foreach ($entry as $key => $value) {
                    if ($key !== 'edit_link' && $key !== 'view_link') $csv_results[$i][$key] = ltrim($value);
                }
                $i++;
            }
            $applicants2 = new Students;
            $applicants2->export_formated2($csv_results,'csv',$report['db850']);
            exit();
        }
        if ($_REQUEST['json']) {
            $results = ['results' => $results, 'links' => $paginator->ajax_links(), 'total' => $paginator->total];
            die(json_encode($results));
        }
        return ['results' => $results, 'links' => $paginator->ajax_links(), 'total' => $paginator->total];
    }

    function get_internal_report($args)
    {
        global $db;
        global $paginator;
        $dbh = get_dbh();
        $sql = "Select id, db46326 as query, 
db73295 as clickthrough_query, 
db73298 as from_table, 
db73262 as navigation_parent, 
db46327 as where_query, 
db46328 as order_by, 
db46329 as group_by, 
db46419 as `view`, 
db59777 as `table` 
from form_internal_report where db73268='$args[url_name]'";
        $report = $db->rawQuery($sql)[0];
        $custom_where = "";

        if(!empty($_REQUEST['group_by_array'])){
            $group_by_array = json_decode($_REQUEST['group_by_array'], 1);
            foreach ($group_by_array as $item){
                $get_custom_where = $this->get_where_from_group_by($report['id'], $item);
                if(!empty($get_custom_where)) {
                    $custom_where .= " AND " . $get_custom_where;
                }
            }
        }

        if(!empty($_REQUEST['status'])){
            $get_custom_where = $this->get_where_from_group_by($report['id'], ['key'=>'status', 'value'=> $_REQUEST['status']]);
            if(!empty($get_custom_where)) {
                $custom_where .= " AND " . $get_custom_where;
            }
        }

        $query = session_floating($report['query']);
        $main_table = session_floating($report['table']);
        $query_table = empty(session_floating($report['from_table'])) ? session_floating($report['table']) : session_floating($report['from_table']);
        $where = session_floating($report['where_query']);
        $group_by = empty(session_floating($report['group_by'])) ? '' : "GROUP BY " . session_floating($report['group_by']);

        // The above group goes into the where condition
        $order_by = empty(session_floating($report['order_by'])) ? "" : " ORDER BY " . session_floating($report['order_by']);
        $having_sql = "";

        if (!empty($_REQUEST['sort'])) {
            $order_by = "";
            $sort_array = json_decode($_REQUEST['sort']);
            $numItems = count($sort_array);
            $i = 0;
            foreach ($sort_array as $filter_item) {
                if ($i === 0) {
                    $order_by = ' ORDER BY ';
                }
                $order_by .= " `$filter_item->sort_by` $filter_item->sort_order";
                if (++$i !== $numItems) {
                    $order_by .= ',';
                }
            }
        }
        if (!empty($args['filter_sql'])) {
            $filter_sql = $args['filter_sql'];
        } else {
            $filter_sql = '';
        }
        if (!empty($_REQUEST['search'])) {
            $search_sqls = $this->get_search_sql($query);
            $search_sql = $search_sqls['search'];
            $having_sql = $search_sqls['having'];
        }
        if (!empty($_REQUEST['filter_array'])) {
            $new_having = "";
            $filter_array = json_decode($_REQUEST['filter_array'], 1);
            $numItems = count($filter_array);
            $i = 0;
            foreach ($filter_array as $filter_item) {
                $operators = [
                    "equals" => "= '{$filter_item['filterValue']}'",
                    "not_equal" => "<>'{$filter_item['filterValue']}'",
                    "starts_with" => "LIKE '{$filter_item['filterValue']}%'",
                    "contains" => "LIKE '%{$filter_item['filterValue']}%'",
                    "ends_with" => "LIKE '%{$filter_item['filterValue']}'",
                    "does_not_contain" => "NOT LIKE '%{$filter_item['filterValue']}%'",
                    "is_not_blank" => "NOT LIKE '%{$filter_item['filterValue']}%'",
                    "greater_than" => ">'{$filter_item['filterValue']}'",
                    "less_than" => "<'{$filter_item['filterValue']}'",
                    "before" => "< DATE('{$filter_item['filterValue']}')",
                    "after" => "> DATE('{$filter_item['filterValue']}')",
                    "between" => "BETWEEN {$filter_item['filterValue']}"];
                if($filter_item['operator']=='is_not_blank'){
                    $new_having .= " AND `{$filter_item['field']}` IS NOT NULL AND `{$filter_item['field']}` != '' ";
                }else if($filter_item['operator']=='is_blank'){
                    $new_having .= " AND `{$filter_item['field']}` IS NULL OR `{$filter_item['field']}` = '' ";
                }else{
                    $new_having .= " AND `{$filter_item['field']}` " . $operators[$filter_item['operator']];
                }
            }
            $having_sql = empty($having_sql) ? " HAVING 1 $new_having " : $having_sql . " AND $new_having";
        }
        if (empty($where)) {
            $where = "$main_table.usergroup={$_SESSION['usergroup']} and ($main_table.rec_archive is null or $main_table.rec_archive ='')";
        }

        $sql = "$query FROM $query_table WHERE $where $filter_sql $search_sql $custom_where $group_by $having_sql $order_by";

        if (!$_GET['export']) {
            $paginator->calculate_total_entries($sql);
            $limit_sql = $paginator->limit_sql();
        } else {
            $limit_sql = "";
        }
        $sql = $sql . " " . $limit_sql;
        dev_debug($sql);

        $sql = $dbh->prepare($sql);
        $sql->execute();
        $results = $sql->fetchAll(PDO::FETCH_ASSOC);

        if ($_REQUEST['export']) {
            ini_set('memory_limit', '-1');
            $csv_results = array();
            $csv_titles = array();
            foreach ($results[0] as $key => $value) {
                if (in_array($key, array("Manage", "manage"))) {
                    $key = "ID";
                }
                if ($key !== 'edit_link' && $key !== 'view_link') $csv_titles[$key] = ltrim($key);
            }
            $csv_results[] = $csv_titles;

            $i = 1;
            foreach ($results as $entry) {
                foreach ($entry as $key => $value) {
                    if ($key !== 'edit_link' && $key !== 'view_link') $csv_results[$i][$key] = ltrim($value);
                }
                $i++;
            }
            $applicants2 = new Students;
            $applicants2->export_formated2($csv_results,'csv',$report['db850']);
            exit();
        }
        if ($_REQUEST['json']) {
            $results = ['results' => $results, 'links' => $paginator->ajax_links(), 'total' => $paginator->total, 'group_by' => $this->explode_group_by($report['group_by'])];
            die(json_encode($results));
        }
        return [
            'results' => $results,
            'links' => $paginator->ajax_links(),
            'total' => $paginator->total,
            'group_by' => $this->explode_group_by($report['group_by']),
            'status_columns' => $this->get_status_columns($report['navigation_parent'])
        ];
    }

    function get_clickthrough_data($args)
    {

        //error_log("ANITA**CLICKTHROUGHSQL**jaon_encode($args)");
        global $db;
        global $paginator;
        $dbh = get_dbh();
        $sql = "Select db46326 as query, 
db73295 as clickthrough_query, 
db73298 as from_table, 
db46327 as where_query, 
db46328 as order_by, 
db46329 as group_by, 
db46419 as `view`, 
db59777 as `table` 
from form_internal_report where db73268='$args[url_name]'";
        $report = $db->rawQuery($sql)[0];

        $query = session_floating($report['clickthrough_query']);
        $main_table = session_floating($report['table']);
        $query_table = empty(session_floating($report['from_table'])) ? session_floating($report['table']) : session_floating($report['from_table']);
        $where = session_floating($report['where_query']);

        // The above group goes into the where condition
        $order_by = empty(session_floating($report['order_by'])) ? "" : " ORDER BY " . session_floating($report['order_by']);
        $having_sql = "";

        if (!empty($_REQUEST['sort'])) {
            $order_by = "";
            $sort_array = json_decode($_REQUEST['sort']);
            $numItems = count($sort_array);
            $i = 0;
            foreach ($sort_array as $filter_item) {
                if ($i === 0) {
                    $order_by = ' ORDER BY ';
                }
                $order_by .= " `$filter_item->sort_by` $filter_item->sort_order";
                if (++$i !== $numItems) {
                    $order_by .= ',';
                }
            }
        }
        if (!empty($_REQUEST['group_by_array'])) {
            $group_by_array = json_decode($_REQUEST['group_by_array'], 1);
            foreach ($group_by_array as $item) {
                $having_sql .= " AND `{$item['key']}` = '{$item['value']}'";
            }
        }

        if (!empty($_REQUEST['filter_array'])) {
            $new_having = "";
            $filter_array = json_decode($_REQUEST['filter_array'], 1);
            $numItems = count($filter_array);
            $i = 0;
            foreach ($filter_array as $filter_item) {
                $operators = [
                    "equals" => "= '{$filter_item['filterValue']}'",
                    "not_equal" => "<>'{$filter_item['filterValue']}'",
                    "starts_with" => "LIKE '{$filter_item['filterValue']}%'",
                    "contains" => "LIKE '%{$filter_item['filterValue']}%'",
                    "ends_with" => "LIKE '%{$filter_item['filterValue']}'",
                    "does_not_contain" => "NOT LIKE '%{$filter_item['filterValue']}%'",
                    "is_not_blank" => "NOT LIKE '%{$filter_item['filterValue']}%'",
                    "greater_than" => ">'{$filter_item['filterValue']}'",
                    "less_than" => "<'{$filter_item['filterValue']}'",
                    "before" => "< DATE('{$filter_item['filterValue']}')",
                    "after" => "> DATE('{$filter_item['filterValue']}')",
                    "between" => "BETWEEN {$filter_item['filterValue']}"];
                if($filter_item['operator']=='is_not_blank'){
                    $new_having .= " AND `{$filter_item['field']}` IS NOT NULL AND `{$filter_item['field']}` != '' ";
                }else if($filter_item['operator']=='is_blank'){
                    $new_having .= " AND `{$filter_item['field']}` IS NULL OR `{$filter_item['field']}` = '' ";
                }else{
                    $new_having .= " AND `{$filter_item['field']}` " . $operators[$filter_item['operator']];
                }
            }
            $having_sql = empty($having_sql) ? "$new_having" : $having_sql . " AND $new_having";
        }
        if (!empty($args['filter_sql'])) {
            $filter_sql = $args['filter_sql'];
        } else {
            $filter_sql = '';
        }
        if (empty($where)) {
            $where = "$main_table.usergroup={$_SESSION['usergroup']} and ($main_table.rec_archive is null or $main_table.rec_archive ='') $filter_sql";
        }

        $sql = "$query FROM $query_table WHERE $where  HAVING 1 $having_sql $order_by";
        die($sql);
        if (!$_GET['export']) {
            $paginator->calculate_total_entries($sql);
            $limit_sql = $paginator->limit_sql();
        } else {
            $limit_sql = "";
        }
        $sql = $sql . " " . $limit_sql;
        dev_debug($sql);
        //error_log("ANITA**CLICKTHROUGHSQL**$sql");

        $sql = $dbh->prepare($sql);
        $sql->execute();
        $results = $sql->fetchAll(PDO::FETCH_ASSOC);

        if ($_REQUEST['export']) {
            ini_set('memory_limit', '-1');
            $csv_results = array();
            $csv_titles = array();
            foreach ($results[0] as $key => $value) {
                if (in_array($key, array("Manage", "manage"))) {
                    $key = "ID";
                }
                if ($key !== 'edit_link' && $key !== 'view_link') $csv_titles[$key] = ltrim($key);
            }
            $csv_results[] = $csv_titles;

            $i = 1;
            foreach ($results as $entry) {
                foreach ($entry as $key => $value) {
                    if ($key !== 'edit_link' && $key !== 'view_link') $csv_results[$i][$key] = ltrim($value);
                }
                $i++;
            }

            $applicants2 = new Students;
            $applicants2->export_formated2($csv_results,'csv',$report['db850']);
            exit();
        }
        if ($_REQUEST['json']) {
            $results = ['results' => $results, 'links' => $paginator->ajax_links(), 'total' => $paginator->total, 'group_by' => $this->explode_group_by($report['group_by'])];
            die(json_encode($results));
        }
        return ['results' => $results, 'links' => $paginator->ajax_links(), 'total' => $paginator->total, 'group_by' => $this->explode_group_by($report['group_by'])];
    }

    function get_ajax_data($args)
    {
        if ($_SESSION['usergroup'] == 1) {
            return $this->get_internal_report($args);
        }
        return $this->get_list_view_data($args);
    }

    function get_course_completion_stats()
    {
        $id = $_GET['parent_id'];
        $dbh = get_dbh();
        $currentMonth = date('F');
        $num_months = intval($_GET['num_months']) ?: 3;
        $select_months = "";
        $timeHash = [];
        for ($i = 0; $i < $num_months; $i++) {
            $select_months .= ",
            (SELECT count(*) FROM sis_scheduled_booking JOIN sis_profiles on sis_scheduled_booking.rel_id = sis_profiles.id JOIN sis_course_schedule on sis_scheduled_booking.db16136 = sis_course_schedule.id WHERE sis_scheduled_booking.usergroup='{$_SESSION['usergroup']}' AND (sis_scheduled_booking.rec_archive is null or sis_scheduled_booking.rec_archive = '') and (db14979='3' or db14979 ='4' or db14979='9') AND ssg.id = db48609 AND db16136 = '{$_GET['course_id']}' AND sis_course_schedule.db14947 <= last_day(now() - INTERVAL 0 MONTH)) as 'month$i',
        (SELECT count(*) FROM sis_scheduled_booking JOIN sis_profiles on sis_scheduled_booking.rel_id = sis_profiles.id JOIN sis_course_schedule on sis_scheduled_booking.db16136 = sis_course_schedule.id WHERE sis_scheduled_booking.usergroup='{$_SESSION['usergroup']}' AND (sis_scheduled_booking.rec_archive is null or sis_scheduled_booking.rec_archive = '') and (db14979='3' or db14979 ='4' or db14979='9') AND (db14981=1 OR db14981 =2) AND ssg.id = db48609 AND db16136 = '{$_GET['course_id']}' AND sis_course_schedule.db14947 <= last_day(now()-INTERVAL 0 MONTH)) as 'attendedmonth$i'";

            $timeHash["month$i"] = "Booked " . Date('M', strtotime(" -$i month"));
            $timeHash["attendedmonth$i"] = "Attended " . Date('M', strtotime(" -$i month"));
        }
        $query = "SELECT  ssg.id, db48829 as type $select_months
FROM sis_primary_groups ssg
where (rec_archive is null or rec_archive ='')";
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $table = (array_map(function ($item) use ($timeHash, $num_months) {
            $res = [
                'group' => $item['type']
            ];
            for ($i = 0; $i < $num_months; $i++) {
                $res[$timeHash["month$i"]] = $item["month$i"];
                $res[$timeHash["attendedmonth$i"]] = $item["attendedmonth$i"];
            }
            return $res;
        }, $results));
        $label_array = (array_map(function ($item) {
            return $item['type'];
        }, $results));
        $data_array = (array_map(function ($item) {
            return intval($item['Total_Value']);
        }, $results));
        $results = (array_map(function ($item) {
            return ['type' => $item['type'], 'total' => intval($item['now']), 'group' => $item['id'], 'percentage' => $item['now'] == 0 ? 0 : round(intval($item['attendednow']) / intval($item['now']) * 100)];
        }, $results));
        $res = [
            'labels' => $label_array,
            'data' => $data_array,
            'stats' => $results,
            'table' => $table,
        ];
        return $res;
    }

    function get_all_ilp_stats($args = null)
    {
        if (!empty($args['filter_sql'])) {
            $filter_sql = $args['filter_sql'];
        }
        $start_date_from = $_REQUEST['start_date_from'];
        $start_date_to = $_REQUEST['start_date_to'];
        $limit_all_ilp_dates = '';

        if (isset($start_date_from) && $start_date_from != '') {
            $limit_all_ilp_dates = "AND (db61500 >= '$start_date_from' AND db61500 <='$start_date_to')";
        }
        $usergroups = usergroups_management();
        $dbh = get_dbh();
        $sql_count = $dbh->prepare("
SELECT count(*)
FROM sis_ind_learner_plan ilp
LEFT JOIN sis_profiles p ON p.id = ilp.rel_id
WHERE (ilp.rec_archive IS NULL OR ilp.rec_archive = '') AND ilp.$usergroups $limit_all_ilp_dates $filter_sql");
        $query = "SELECT 
ilp.rel_id AS Manage,
sis_profiles.username_id,
    CONCAT(db48566, ' ', db48567) as 'full_name',
    date_format(db61500,'%d/%m/%Y') AS 'Date_of_Review',
    db61498 AS 'Type_of_ILP',
    db61499 AS 'Conducted_by',
db61530 AS 'What_will_help_you',
db61531 AS 'What_the_College_will_do',
ilp.id AS 'rec'
FROM sis_ind_learner_plan ilp
left join sis_profiles on ilp.rel_id = sis_profiles.id
WHERE (ilp.rec_archive IS NULL OR ilp.rec_archive = '') AND ilp.$usergroups
$limit_all_ilp_dates $filter_sql
    ORDER BY db61500 DESC";
        dev_debug($query);

        $sql = $dbh->prepare($query);


        $sql->execute();
        $ilps = $sql->fetchAll(PDO::FETCH_OBJ);
        $sql_count->execute();
        $count = $sql_count->fetchColumn();
        return [$ilps, $count];
    }

    function get_student_enrolled_stats($args = null)
    {
        if (!empty($args['filter_sql'])) {
            $filter_sql = $args['filter_sql'];
        }
        $dbh = get_dbh();

        $sql = "SELECT
    db48829 as 'group',
    (SELECT Count(*) FROM sis_profiles JOIN sis_ind_learner_plan ON sis_ind_learner_plan.rel_id=sis_profiles.id JOIN sis_scheduled_booking ON sis_scheduled_booking.rel_id = sis_profiles.id  where (sis_profiles.usergroup = '" . $_SESSION['usergroup'] . "' and (sis_profiles.rec_archive is null or sis_profiles.rec_archive ='')) AND (db48609 != '' and sis_primary_groups.id = db48609) AND sis_scheduled_booking.db14977=(select id from core_courses where (rec_archive is null or rec_archive = '') and db66775='yes' and usergroup = '" . $_SESSION['usergroup'] . "' LIMIT 1) AND db14981 = 1 AND db40388 = 'Student' AND db61498='inital_ILP'AND db61500 is not null AND (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) is not null AND CASE WHEN db61500 > (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) THEN db61500 ELSE (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) END <= last_day(now())) as 'now',
    (SELECT Count(*) FROM sis_profiles JOIN sis_ind_learner_plan ON sis_ind_learner_plan.rel_id=sis_profiles.id JOIN sis_scheduled_booking ON sis_scheduled_booking.rel_id = sis_profiles.id  where (sis_profiles.usergroup = '" . $_SESSION['usergroup'] . "' and (sis_profiles.rec_archive is null or sis_profiles.rec_archive ='')) AND (db48609 != '' and sis_primary_groups.id = db48609) AND sis_scheduled_booking.db14977=(select id from core_courses where (rec_archive is null or rec_archive = '') and db66775='yes' and usergroup = '" . $_SESSION['usergroup'] . "' LIMIT 1) AND db14981 = 1 AND db40388 = 'Student' AND db61498='inital_ILP'AND db61500 is not null AND (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) is not null AND CASE WHEN db61500 > (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) THEN db61500 ELSE (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) END <= last_day(now()-INTERVAL 1 MONTH)) as 'month1',
    (SELECT Count(*) FROM sis_profiles JOIN sis_ind_learner_plan ON sis_ind_learner_plan.rel_id=sis_profiles.id JOIN sis_scheduled_booking ON sis_scheduled_booking.rel_id = sis_profiles.id  where (sis_profiles.usergroup = '" . $_SESSION['usergroup'] . "' and (sis_profiles.rec_archive is null or sis_profiles.rec_archive ='')) AND (db48609 != '' and sis_primary_groups.id = db48609) AND sis_scheduled_booking.db14977=(select id from core_courses where (rec_archive is null or rec_archive = '') and db66775='yes' and usergroup = '" . $_SESSION['usergroup'] . "' LIMIT 1) AND db14981 = 1 AND db40388 = 'Student' AND db61498='inital_ILP'AND db61500 is not null AND (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) is not null AND CASE WHEN db61500 > (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) THEN db61500 ELSE (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) END <= last_day(now()-INTERVAL 2 MONTH)) as 'month2',
    (SELECT Count(*) FROM sis_profiles JOIN sis_ind_learner_plan ON sis_ind_learner_plan.rel_id=sis_profiles.id JOIN sis_scheduled_booking ON sis_scheduled_booking.rel_id = sis_profiles.id  where (sis_profiles.usergroup = '" . $_SESSION['usergroup'] . "' and (sis_profiles.rec_archive is null or sis_profiles.rec_archive ='')) AND (db48609 != '' and sis_primary_groups.id = db48609) AND sis_scheduled_booking.db14977=(select id from core_courses where (rec_archive is null or rec_archive = '') and db66775='yes' and usergroup = '" . $_SESSION['usergroup'] . "' LIMIT 1) AND db14981 = 1 AND db40388 = 'Student' AND db61498='inital_ILP'AND db61500 is not null AND (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) is not null AND CASE WHEN db61500 > (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) THEN db61500 ELSE (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) END <= last_day(now()-INTERVAL 3 MONTH)) as 'month3',
    (SELECT Count(*) FROM sis_profiles JOIN sis_ind_learner_plan ON sis_ind_learner_plan.rel_id=sis_profiles.id JOIN sis_scheduled_booking ON sis_scheduled_booking.rel_id = sis_profiles.id  where (sis_profiles.usergroup = '" . $_SESSION['usergroup'] . "' and (sis_profiles.rec_archive is null or sis_profiles.rec_archive ='')) AND (db48609 != '' and sis_primary_groups.id = db48609) AND sis_scheduled_booking.db14977=(select id from core_courses where (rec_archive is null or rec_archive = '') and db66775='yes' and usergroup = '" . $_SESSION['usergroup'] . "' LIMIT 1) AND db14981 = 1 AND db40388 = 'Student' AND db61498='inital_ILP'AND db61500 is not null AND (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) is not null AND CASE WHEN db61500 > (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) THEN db61500 ELSE (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) END <= last_day(now()-INTERVAL 4 MONTH)) as 'month4',
    (SELECT Count(*) FROM sis_profiles JOIN sis_ind_learner_plan ON sis_ind_learner_plan.rel_id=sis_profiles.id JOIN sis_scheduled_booking ON sis_scheduled_booking.rel_id = sis_profiles.id  where (sis_profiles.usergroup = '" . $_SESSION['usergroup'] . "' and (sis_profiles.rec_archive is null or sis_profiles.rec_archive ='')) AND (db48609 != '' and sis_primary_groups.id = db48609) AND sis_scheduled_booking.db14977=(select id from core_courses where (rec_archive is null or rec_archive = '') and db66775='yes' and usergroup = '" . $_SESSION['usergroup'] . "' LIMIT 1) AND db14981 = 1 AND db40388 = 'Student' AND db61498='inital_ILP'AND db61500 is not null AND (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) is not null AND CASE WHEN db61500 > (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) THEN db61500 ELSE (SELECT db14949 from sis_course_schedule where sis_course_schedule.id = db14978) END <= last_day(now()-INTERVAL 5 MONTH)) as 'month5'
FROM sis_primary_groups where (sis_primary_groups.rec_archive is null or sis_primary_groups.rec_archive = '') $filter_sql";
        //ToDO: Add this condition back -- /*FIND_IN_SET (sis_primary_groups.id,(SELECT db19360 FROM core_preferences WHERE usergroup =$_SESSION[usergroup] and (core_preferences.rec_archive is NULL or core_preferences.rec_archive = '')))*/ AND
        dev_debug($sql);
        $sql = $dbh->prepare($sql);


        $sql->execute();
        $results = $sql->fetchAll(\PDO::FETCH_OBJ);
        return $results;
    }

    function get_search_sql($query)
    {
        require_once(ABSPATH . "/app/libs/sql_parser/PHPSQLParser.php");

        // echo $_REQUEST['search'];

        $search_term = htmlspecialchars($_REQUEST['search']);
        $search_term = addslashes($search_term);
        $parser = new PHPSQLParser($query, true);
        $search_fields = $parser->parsed['SELECT'];

        $search_sql = "AND (";
        $having_sql = "HAVING ((";
        $search_count = 0;
        $having_count = 0;
        $search_expression = true;
        $having_expression = false;
        foreach ($search_fields as $field) {
            if ($search_count != 0 & $search_expression) {
                $search_sql .= " OR ";
            }
            if ($having_count != 0) {
                $having_sql .= " OR ";
            }

            if ($field['base_expr'] == "DATE_FORMAT") {
                $field['base_expr'] = "date";
            }
            if ($field['base_expr'] != 'IF' && $field['base_expr'] != 'if' && $field['expr_type'] != 'expression') {
                $search_sql .= "(" . $field['base_expr'] . " LIKE '%" . trim($search_term) . "%')";
                $search_count++;
                $search_expression = true;
            } else {
                $search_expression = false;
                $having_expression = true;
            }
            $having_count++;
            $having_sql .= "(" . str_replace("'", "`", $field['alias']['name']) . " LIKE '%" . trim($search_term) . "%')";
        }
        $search_sql .= ")";
        $having_sql .= "))";
        return ['search' => $search_sql, 'having' => $having_sql];
    }

    function explode_group_by($group_by_string)
    {
        return explode(",", str_replace('`', '', $group_by_string));
    }

    function get_status_columns($nav_parent)
    {
        if ($nav_parent == 'tickets') {
            return explode(",", pull_field("system_table", "figures", "WHERE db_field_name = 'db92'"));
        } elseif ($nav_parent == 'tasks') {
            return explode(",", pull_field("system_table", "figures", "WHERE db_field_name = 'db31899'"));
        } elseif ($nav_parent == 'internal_projects') {
            return explode(",", pull_field("system_table", "figures", "WHERE db_field_name = 'db31891'"));
        }
        return [];
    }
    function get_where_from_group_by($report_id, $group_by_item)
    {
        if ($report_id == 16) {
            switch ($group_by_item['key']){
                case 'internal_project':
                    return "db34628 = '{$group_by_item['value']}'";
                case 'status':
                    if($group_by_item['value'] == 'blank'){
                        return "(db92 IS NULL OR db92='')";
                    }
                    if($group_by_item['value'] == 'new_and_active'){
                        return "(db92 IS NULL OR db92='' OR db92=2 OR db92='not specified' OR db92='no' OR db92=5)";
                    }
                    if($group_by_item['value'] == 'count_incomplete'){
                        return "(db92<>8 AND db92<>9)";
                    }
                    return "db99 = '{$group_by_item['value']}'";
                case 'release':
                    return "db41046 = '{$group_by_item['value']}'";
                case 'school_id':
                    return "form_support_ticket.usergroup = '{$group_by_item['value']}'";
                case 'priority_id':
                    return " db94 = '{$group_by_item['value']}'";
                case 'allocated_user_id':
                    return " db22 = '{$group_by_item['value']}'";
                default:
                    break;
            }
        } elseif ($report_id == '14') {
            switch ($group_by_item['key']){
                case 'epic_id':
                    return "db33637 = '{$group_by_item['value']}'";
                case 'status':
                    if($group_by_item['value'] == 'count_incomplete'){
                        return "(db31891 <> 'pushed_to_live' AND db31891 <> 'excluded')";
                    }
                    return "db31891 = '{$group_by_item['value']}'";
                case 'release':
                    return "db41046 = '{$group_by_item['value']}'";
                case 'school_id':
                    return "form_internal_projects.usergroup = '{$group_by_item['value']}'";
                case 'priority_id':
                    return " db31887 = '{$group_by_item['value']}'";
                case 'allocated_user_id':
                    return " ',{$group_by_item['value']},' LIKE CONCAT('%,',db31889,',%')";
                default:
                    break;
            }
        } elseif ($report_id == '15') {
            switch ($group_by_item['key']){
                case 'epic_id':
                    return "db33637 = '{$group_by_item['value']}'";
                case 'status':
                    if($group_by_item['value'] == 'count_incomplete'){
                        return "(db31899 <> 'completed' AND db31899 <> 'disregard' AND db31899 <> 'blocker')";
                    }
                    return "db31899 = '{$group_by_item['value']}'";
                case 'release':
                    return "db41046 = '{$group_by_item['value']}'";
                case 'school_id':
                    return "form_internal_projects.usergroup = '{$group_by_item['value']}'";
                case 'priority_id':
                    if(empty($group_by_item['value'])){
                        return " (db58503 IS NULL OR db58503 ='not specified' OR db58503 ='') ";
                    }
                    return " db58503 = '{$group_by_item['value']}'";
                case 'allocated_user_id':
                    return " db39188 = '{$group_by_item['value']}'";
                default:
                    break;
            }
        }
        return "";
    }

    function get_years(){
        $years = [];
        $year = date("Y");
        for($i=-10; $i<3; $i++){
            array_push($years, ['year'=>$year+$i]);
        }
        return $years;
    }
    function prev_date( $months){

        $date = new DateTime("now", new DateTimeZone('Europe/London') );

        // We extract the day of the month as $start_day
        $start_day = $date->format('j');

        // We add 1 month to the given date
        $date->modify("-{$months} month");

        // We extract the day of the month again so we can compare
        $end_day = $date->format('j');

        if ($start_day != $end_day)
        {

            // The day of the month isn't the same anymore, so we correct the date
            $date->modify('last day of last month');

        }

        return $date;
    }
    function get_floating($field){
        $occurrences = substr_count(($field), "[GET]");
        if($occurrences>0) {
            for ($i = 0; $i < $occurrences; $i++) {
                $new_val = get_string_between($field, "[GET]", "[/GET]");
                $new_val_value = $_REQUEST[$new_val]; // this
                if (empty($new_val_value)) {
                    $new_val_value = $_GET[$new_val];
                }
                $field = str_replace("[GET]".$new_val."[/GET]", $new_val_value, $field);
            }
        }

        return $field;
    }
}

?>
