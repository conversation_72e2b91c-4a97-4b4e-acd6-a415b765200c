<?php

use App\core\support\facades\Log;

if (isset($_GET['testing_errors']) && $_GET['testing_errors']) {
    echo "HERE";
    error_reporting(E_ALL | E_STRICT);
    ini_set('display_errors', 1);
}
$values_array = array();
$filter = array();

if (!function_exists('noHTML')) {
    //function noHTML($input, $encoding = 'ISO-8859-1')
    function noHTML($input, $encoding = 'UTF-8')
    {
        //return $input;
        return htmlentities($input, ENT_QUOTES, $encoding);
    }
}
/*===================================
* Global Search
*====================================	*/
if (!function_exists('get_search')) {
    function get_search($string, $args = array())
    {
        $string = trim($string);
        $str = " AND ";
        $count = 1;
        foreach ($args as $sterm) {
            if ($count != 1) {
                $str .= " OR ";
            }
            $str .= " " . $sterm . " LIKE '%" . $string . "%' ";
            $count++;
        }
        return $str;
    }
}
/*===================================
* Random Dark Colors
*====================================	*/
if (!function_exists('random_color_part')) {
    function random_color_part()
    {
        $dt = '';
        for ($o = 1; $o <= 3; $o++) {
            $dt .= str_pad(dechex(mt_rand(0, 127)), 2, '0', STR_PAD_LEFT);
        }
        return '#' . $dt;
    }
}
/*===================================
* Global GET Params to URL
*====================================	*/
if (!function_exists('get_params_to_url')) {
    function get_params_to_url($params = array(), $skipFirst = false, $except = array())
    {
        $get_link = "";
        if ($params) {
            $keys = array_keys($params);
            $size = sizeof($params);
            for ($i = 0; $i < $size; $i++) {
                if (!in_array($keys[$i], $except)) {
                    if ($i == 0 && !$skipFirst)
                        $get_link = "?" . $keys[$i] . "=" . $params[$keys[$i]];
                    else
                        $get_link .= "&" . $keys[$i] . "=" . $params[$keys[$i]];
                }
            }
        }
        return $get_link;
    }
}

/** ===================================
 * Change array keys
 *====================================    */
if (!function_exists('change_key')) {
    function change_key($array, $old_key, $new_key)
    {
        if (!array_key_exists($old_key, $array))
            return $array;

        $keys = array_keys($array);
        $keys[array_search($old_key, $keys)] = $new_key;

        return array_combine($keys, $array);
    }
}
/** ===================================
 * Dev Debug
 * ====================================    */
load_helper('debug');
if (!function_exists('dev_debug')) {
    function dev_debug($msg)
    {
        //echo "HERE<>".$msg;
        $debugger = false;
        if (isset($_GET['debug_mode'])) $debugger = $_GET['debug_mode'];
        elseif (isset($_SESSION['debug_mode'])) $debugger = $_SESSION['debug_mode'];
        //$debugger='';
        if ($debugger === 'new') {
            $bt = debug_backtrace();
            $caller = array_shift($bt);
            Debug_helper::log($msg, $caller['file'], $caller['line'], $bt);
        } elseif ($debugger === 'yes') {

            if (!empty($_GET['track_time'])) {
                global $phptime_start;
                $thimeexc = (microtime(true) - $phptime_start);
                $bg = "";
                if ($thimeexc > 0.01) {
                    $bg = "background:aquamarine;";
                }
                $time = microtime();
                $time = explode(' ', $time);
                $time = $time[1] + $time[0];
                $finish = $time;
                $total_time = round(($finish - start), 4);
                echo "<div><pre class=\"alert_colour\" style=\"text-align:left;{$bg}\"><strong>DEBUG MSG: </strong> <br>TIME :" . $thimeexc . " <br> <br>TOTTIME :" . $total_time . " <br> $msg</pre></div>";
                $phptime_start = microtime(true);

            } else {
                echo "<div><pre class=\"alert_colour\" style=\"text-align:left;\"><strong>DEBUG MSG:</strong>$msg</pre></div>";
            }
        }
    }
}

/** ===================================
 * Set Up Filter Field Dynamic
 * ====================================    */
if (!function_exists('filter_field_dynamic')) {
    function filter_field_dynamic($db_field_names = array(), $usergroup_specific = true)
    {
        dev_debug(__METHOD__ . " " . __FUNCTION__ . " " . __FILE__);
        global $db;
        $fieldClass = new Fields;
        foreach ($db_field_names as $key => $value) {
            $val = is_numeric($key) ? $value : $key;
            $title = is_numeric($key) ? '' : $value;
            $fd = array('id' => $val);

            $field_params = array('db_field_name' => $val, 'school_id' => $_SESSION['usergroup'], 'masked_fields_only' => false, 'usergroup_specific' => $usergroup_specific);

            $field_data = $fieldClass->get($field_params);
            $fd['title'] = $title ?: $field_data['title'];
            if ($field_data) {
                if (in_array($field_data['type'], array('dynamic_list_group', 'dynamic_list', 'dynamic_list_group_applicant_hidden_session'))) {
                    $query = explode(',', $field_data['default']);
                    if ($query[1]) {
                        $from_part = explode("order", $query[0]);
                        if ($from_part[1]) {
                            $from_string = $from_part[0] . (strpos($from_part[0], "WHERE") !== false ? " AND (usergroup = '" . $_SESSION['usergroup'] . "' XOR usergroup = '1' )" : " WHERE 1 AND (usergroup = '" . $_SESSION['usergroup'] . "' XOR usergroup = '1' )") . " ORDER " . $from_part[1];
                        } else {
                            $from_string = $from_part[0] . (strpos($from_part[0], "WHERE") !== false ? " AND (usergroup = '" . $_SESSION['usergroup'] . "' XOR usergroup = '1' )" : " WHERE 1 AND (usergroup = '" . $_SESSION['usergroup'] . "' XOR usergroup = '1' )");
                        }
                        if (strpos($query[1], "+") !== false) {
                            $select_array = explode("+", $query[1]);
                            $label = $select_array[0];
                        } else {
                            $label = $query[1];
                        }

                        $sql = "SELECT id as value , $label as label FROM $from_string";
                        $sql = str_replace("[SESSION]usergroup[/SESSION]", $_SESSION['usergroup'], $sql);
                        $sql = str_replace("[session]usergroup[/session]", $_SESSION['usergroup'], $sql);
                        dev_debug(__METHOD__ . " : " . $sql);
                        $fd['options'] = $db->query($sql);
                    }

                } else if ($field_data['type'] == 'dropdown') {
                    $opts = explode(',', $field_data['default']);
                    $options = array();
                    foreach ($opts as $key => $value) {
                        $options[] = array(
                            'value' => $value,
                            'label' => str_replace("_", " ", $value)
                        );
                    }
                    $fd['options'] = $options;
                } else if ($field_data['type'] == 'radio_fromlist') {
                    $opts = explode('|', $field_data['default']);
                    $options = array();
                    foreach ($opts as $key => $value) {
                        $options[] = array(
                            'value' => $value,
                            'label' => str_replace("_", " ", $value)
                        );
                    }
                    $fd['options'] = $options;
                } else if ($field_data['type'] == 'radio_yes_no') {
                    $opts = explode(',', 'yes,no');
                    $options = array();
                    foreach ($opts as $key => $value) {
                        $options[] = array(
                            'value' => $value,
                            'label' => str_replace("_", " ", $value)
                        );
                    }
                    $fd['options'] = $options;
                } else {
                    $fd['extra_options'] = true;
                }
            }
            $all_fields[] = $fd;
        }
        return $all_fields;
    }
}


/** ===================================
 * Get Applicant's Reviewer
 * ====================================    */
if (!function_exists('applicant_reviewer')) {
    function applicant_reviewer($reviewer_id, $join = "and ((")
    {
        return ("$join {{operator_type}} (select count(id) from core_assignations where core_assignations.usergroup=" . $_SESSION['usergroup'] . " AND (db69884 = '2') and (core_assignations.db69743 is null or core_assignations.db69743 ='') AND (core_assignations.rec_archive IS NULL OR core_assignations.rec_archive = '') and db69836='" . $reviewer_id . "' and core_assignations.rel_id = core_students.id) >0 ");
    }
}

if (!function_exists('applicant_reviewer_or')) {
    function applicant_reviewer_or($reviewer_id)
    {
        return applicant_reviewer($reviewer_id, "or (");
    }
}


/** ===================================
 * Get Booking
 * ====================================    */
if (!function_exists('applicant_exam_booking')) {
    function applicant_exam_booking($eventID, $join = "and ((")
    {
        global $replacement;
        $replacement['field_type'] = 'dropdown';
        $op = stripos($replacement['operator_type'], 'not') !== false ? ' = 0' : ' > 0';
        return ("$join (SELECT COUNT(id) FROM sis_event_booking WHERE db32400='" . $eventID . "' AND db31802='47' AND rel_id=core_students.id) $op");
    }
}

if (!function_exists('applicant_exam_booking_or')) {
    function applicant_exam_booking_or($eventID)
    {
        return applicant_exam_booking($eventID, "or (");
    }
}

/** ===================================
 * Get Date of last login
 * ====================================    */
if (!function_exists('date_of_last_login')) {
    function date_of_last_login($date = "", $join = "and ((")
    {
        //if($bracket_count=="2"){ $end_brackets = "))"; }
        if ($date == "not_empty") {
            return ("$join (SELECT COUNT(db1276) FROM form_last_login where form_last_login.rec_id=core_students.rec_id) > 0" . $end_brackets);
        } elseif ($date) {
            return ("$join (SELECT db1276 FROM form_last_login where form_last_login.rec_id=core_students.rec_id ORDER BY id DESC LIMIT 1)  LIKE '" . date_search_term($date, true) . "'" . $end_brackets);
        }
        return ("$join (SELECT COUNT(db1276) FROM form_last_login where form_last_login.rec_id=core_students.rec_id) = 0" . $end_brackets);

    }
}

if (!function_exists('date_of_last_login_or')) {
    function date_of_last_login_or($date)
    {
        return date_of_last_login($date, "or (");
    }
}

/** ===================================
 * Get Date of last login
 * ====================================    */
if (!function_exists('all_checklist_stages_completed')) {
    function all_checklist_stages_completed($stage_id = "", $join = "and ((")
    {
        if ($stage_id == "not_empty") {
            return ("$join (SELECT COUNT(*) FROM dir_stage_tracker WHERE dir_stage_tracker.rel_id=core_students.id ) > 0");
        } elseif ($stage_id) {
            return ("$join (SELECT COUNT(*) FROM dir_stage_tracker WHERE dir_stage_tracker.rel_id=core_students.id AND db1142='{$stage_id}') > 0");
        }
        return ("$join (SELECT COUNT(*) FROM dir_stage_tracker WHERE dir_stage_tracker.rel_id=core_students.id) = 0");

    }
}

if (!function_exists('all_checklist_stages_completed_or')) {
    function all_checklist_stages_completed_or($stage_id)
    {
        return all_checklist_stages_completed($stage_id, "or (");
    }
}

if (!function_exists('enquiry_reviewer')) {
    function enquiry_reviewer($reviewer_id, $join = "and ((")
    {
        $bracket_count = substr_count($join, '(');
        //if($reviewer_id)
        //return("$join  RPAD(LPAD( c_assign.db69836,LENGTH(c_assign.db69836)+1,','),LENGTH(c_assign.db69836)+2,',') LIKE '%,".$reviewer_id.",%'".$end_brackets);
        if ($reviewer_id) {
            return ("$join select count(*) from core_assignations WHERE  core_assignations.rel_id = lead_profiles.id AND db69836 {{operator_type_equal}} {$reviewer_id} AND core_assignations.usergroup=$_SESSION[usergroup] AND (db69884 = '1') and (core_assignations.db69743 is null or core_assignations.db69743 ='') AND (core_assignations.rec_archive IS NULL OR core_assignations.rec_archive = '') > 0" . $end_brackets);
        }

        $query = "(Select group_concat(db69836) from
             core_assignations where core_assignations.rel_id = lead_profiles.id AND core_assignations.usergroup=$_SESSION[usergroup] AND (db69884 = '1') and (core_assignations.db69743 is null or core_assignations.db69743 ='') AND (core_assignations.rec_archive IS NULL OR core_assignations.rec_archive = '') )";
        return ("$join  ($query IS {{operator_type}} NULL OR $query {{operator_type_equal}} '')");
    }
}

if (!function_exists('enquiry_reviewer_or')) {
    function enquiry_reviewer_or($reviewer_id)
    {
        return enquiry_reviewer($reviewer_id, "or (");
    }
}

if (!function_exists('enquiry_form_type')) {
    function enquiry_form_type($type, $join = " ((")
    {
        $matches = [
            "contact" => ['https://gogonihon.com/en/contact/',
                'https://gogonihon.com/it/contattaci/',
                'https://gogonihon.com/es/contacto/',
                'https://gogonihon.com/de/kontaktiere-uns/',
                'https://gogonihon.com/fr/contact/',
                'https://gogonihon.com/sv/kontakta/',
                'https://gogonihon.com/pt/contato/',],
            "study_trip" => [
                'https://studytrip.com/contact-us',
                'https://studytrip.com/trip',
                'https://gogonihon.com/en/trips/'
            ],
            "convention" => 4127, //Note : ids are from system_forms
            "digital_nomad" => 4724,
            "long_term_visa" => 4919,
        ];
        if ($type && !empty($matches[$type]) && is_numeric($matches[$type])) {
            return ("$join db32506 = 'https://gogoworld.heiapply.com/enquiry-form/embed/{$matches[$type]}'");
        }
        if (is_array($matches[$type])) {
            $combined_string = "$join ( ";
            foreach ($matches[$type] as $key => $match) {
                $combined_string .= " db32506='{$match}' " . ($key == count($matches[$type]) - 1 ? "" : "OR ");
            }
            $combined_string .= ")";
            return $combined_string;
        }
        return '0';
    }
}

if (!function_exists('enquiry_form_type_or')) {
    function enquiry_form_type_or($type)
    {
        return enquiry_form_type($type, " (");
    }
}

if (!function_exists('filter_string_replace')) {
    function filter_string_replace($subject)
    {
        return str_replace(" ", "_", $subject);
    }
}

/** ===================================
 * Filter Values Replacement
 * ====================================    */
$replacement = array();
if (!function_exists('filter_values_replacement')) {
    function filter_values_replacement($filter, $object)
    {
        $replacement = array();
        if (strpos($object, "||")) {
            $values_array = explode("||", $object);
            /*
				*$values_array[0] = qualified db_fieild_name
				*$values_array[1] = field type
				*$values_array[2] = model/type
				*$values_array[3] = method/True_or_false On columns
				*$values_array[4] = join_type/params for general functions (if manipulations differ based on join type)/Column True Value
				*$values_array[5] = params for general functions
				 */
            if ($filter['option']) {
                $val = $filter['option'];
            } else if ($filter['to'] && $filter['from']) {
                $val = array($filter['from'], $filter['to']);
            } else {
                $val = $filter['value'];
            }
            if (!$val) $val = "";
            $replacement['operator_type'] = $filter['operator_type'];
            if (strpos($values_array[0], "*") !== false) {
                $two_type = explode("*", $values_array[0]);
                $replacement['type'] = $two_type[0];
                $replacement['if_contains'] = $two_type[1];
            } else {
                $replacement['type'] = $values_array[0];
            }
            $replacement['field_type'] = $values_array[1];
            $method__ = $values_array[3];
            if (isset($values_array[4]) && $filter['join'] == "or") {
                $method__ .= "_or";
            }

            if (!empty($_GET['values_array'])) {
                echo "<pre>" . print_r($values_array, 1) . "</pre>";
            }

            if ($values_array[0] == 'sql') {
                $replacement['sql'] = true;
                if ($values_array[1] == 'yes_no') {
                    if (isset($_GET['db_temo'])) {
                        echo "<pre>" . print_r($filter, 1) . "</pre>";
                        echo "<pre>" . print_r($values_array, 1) . "</pre>";
                        exit();
                    }
                    if ((int)$filter['option']) {
                        if ($filter['operator_type'] == 'Is not')
                            $replacement['option'] = $values_array[3];
                        else
                            $replacement['option'] = $values_array[2];
                    } else {
                        if ($filter['operator_type'] == 'Is not')
                            $replacement['option'] = $values_array[2];
                        else
                            $replacement['option'] = $values_array[3];
                    }
                }
                if ($values_array[1] == 'string_yes_no') {
                    if ($filter['option'] == 'yes') {
                        $replacement['option'] = $values_array[2];
                    } else {
                        $replacement['option'] = $values_array[3];
                    }
                }
            } elseif ($values_array[2] == 'general') {
                $array = json_decode($values_array[5], true);
                if (count($array) > 0) {
                    $replacement['value'] = $method__($val, $array);
                } else {
                    $replacement['value'] = $method__($val);
                }

                if ($filter['operator_type'] == 'Is not') {
                    $replacement['value'] = str_replace("{{operator_type}}", " NOT ", $replacement['value']);
                    $replacement['value'] = str_replace("{{operator_type_equal}}", " != ", $replacement['value']);
                } else {
                    $replacement['value'] = str_replace("{{operator_type}}", " ", $replacement['value']);
                    $replacement['value'] = str_replace("{{operator_type_equal}}", " = ", $replacement['value']);
                }

                if (strpos($replacement['value'], "LIKE")) {
                    if ($filter['operator_type'] == "Is not") {
                        $replacement['value'] = str_replace("LIKE", " NOT LIKE", $replacement['value']);
                    }
                    $replacement['custom_value'] = $replacement['value'];
                }

                if ($values_array[0] == 'custom_sql') {
                    if ($values_array[1] == 'sql') {
                        $replacement['sql'] = true;
                    }
                    $replacement['custom_value'] = $replacement['value'];
                }
            } elseif ($values_array[2] == 'column') {
                if ((int)$filter['option']) {
                    $replacement['value'] = $values_array[3];
                    $replacement['option'] = $values_array[3];
                    $replacement['custom_value'] = $values_array[3];
                    $replacement['is_not'] = $values_array[4];
                } else {
                    $replacement['custom_value'] = $values_array[4];
                }
            } elseif ($values_array[2] == 'like_custom') {
                if ((int)$filter['option']) {
                    $replacement['value'] = $val;
                    $replacement['option'] = $val;
                    $replacement['custom_value'] = "LIKE '%" . $val . "%'";
                } else {
                    $replacement['custom_value'] = "LIKE '%" . $val . "%'";
                }
            } else {
                $class__ = $values_array[2];
                $class_object = new $class__();
                $replacement['value'] = $class_object->$method__($val);
            }

            if ($replacement['field_type'] == 'dropdown') {
                $replacement['option'] = $replacement['value'];
            }

            return $replacement;
        }
        $if_contains = false;
        if (strpos($object, "*") !== false) {
            $two_type = explode("*", $object);
            $object = $two_type[0];
            $if_contains = $two_type[1];
        }

        // if($filter['field_type'] == 'age'){
        // 	$filter['value'] = $object.$filter['value'];
        // }
        if ($filter['field_type'] === 'date') {
            return filter_values_replacement($filter, $object . '||string||general||date_search_term');
        }

        if ($filter['field_type'] == 'daterange') {
            if (str_contains($filter['from'], '/')) {
                $filter['from'] = DateTime::createFromFormat('d/m/Y', $filter['from'])->format('Y-m-d');
            }

            if (str_contains($filter['to'], '/')) {
                $filter['to'] = DateTime::createFromFormat('d/m/Y', $filter['to'])->format('Y-m-d');
            }
            $filter['value'] = array($filter['from'], $filter['to']);
            $filter['operator_type'] = "Between";
        }

        return array('type' => $object, 'value' => $filter['value'], 'option' => $filter['option'], 'if_contains' => $if_contains, 'operator_type' => $filter['operator_type']);
    }
}


/** ===================================
 * Date search term with time
 * ====================================    */
if (!function_exists('date_search_term_time')) {
    function date_search_term_time($term)
    {
        return date_search_term($term, true);
    }
}
if (!function_exists('date_search_term_time_like')) {
    function date_search_term_time_like($term)
    {
        return date_search_term($term, true, "LIKE");
    }
}
/** ===================================
 *Days Greater or Lesser Than [=;]
 * ====================================    */
if (!function_exists('date_greater_or_less_than')) {
    function date_greater_or_less_than($term, $params = array())
    {
        $table = $params['table'];
        $t_array = explode(" ", trim($term));
        $sql = "DATEDIFF(NOW()," . ($table ? $table . '.date' : 'date') . ")  " . (isset($t_array[0]) && $t_array[0] != 'Exactly' ? $t_array[0] : '=') . " " . (isset($t_array[1]) && $t_array[1] != 'matching' ? $t_array[1] : 'null');
        return $sql;
    }
}

/** ===================================
 * Date search ter
 * ====================================    */
if (!function_exists('date_search_term')) {
    function date_search_term($term, $with_time = false, $joiner = "")
    {
        $date2 = false;
        if (is_array($term)) {
            $date2 = date_search_term($term[1], $with_time);
            $term = $term[0];
        }
        $date = explode("/", $term);
        $day = (int)$date[0] ? $date[0] : '__';
        $month = (int)$date[1] ? $date[1] : '__';
        $y = explode(" ", $date[2]);
        $date[2] = $y[0];
        $time = $y[1];
        $year = (int)$date[2] ? $date[2] : '____';
        if ($month != "__") {
            $month = strlen("$month") == 2 ? $month : "0" . $month;
        }

        if ($day != "__") {
            $day = strlen("$day") == 2 ? $day : "0" . $day;
        }

        if ($year != "____") {
            $year = strlen("$year") == 1 ? $year . "___" : $year;
            $year = strlen("$year") == 2 ? $year . "__" : $year;
            $year = strlen("$year") == 3 ? $year . "_" : $year;
        }
        $dd = $year . '-' . $month . '-' . $day;
        if ($dd == '____-__-__') {
            return "%" . $term . "%";
        }

        if ($date2) {
            return ($with_time ? $dd . " " . $time . "% ||" . $date2 : $dd . "||" . $date2);
        }
        return $joiner ? " " . $joiner . " '" . ($with_time ? $dd . " " . $time . "%" : $dd) . "'" : ($with_time ? $dd . " " . $time . "%" : $dd);
    }
}

/** ===================================
 * Prepare Filter ARGS
 * ====================================    */
if (!function_exists('prepare_filter_args')) {
    function prepare_filter_args($filters)
    {
        global $filter;
        $students_args = array();
        $filter_args = array();
        $number = count($filters);
        for ($i = 0; $i < $number; $i++) {
            $filter = $filters[$i];
            if ($filter['option']) {
                $val = $filter['option'];
            } else {
                $val = $filter['value'];
            }
            if ($filter['join'] == 'and') {
                if ($filter['operator_type'] == "Exactly matching" || !$filter['operator_type'] || empty($filter['operator_type'])) {
                    $operator_type[] = " = '" . $val . "'";
                }

                if ($filter['operator_type'] == "Containing") {
                    $operator_type[] = " LIKE '%" . $val . "%'";
                }

                if ($filter['operator_type'] == "Is empty") {
                    $operator_type[] = " IS NULL";
                }

                if ($filter['operator_type'] == "Is not empty") {
                    $operator_type[] = " !=''";
                }

                if (in_array($filter['operator_type'], array('>', '<', '='))) {
                    $operator_type[] = " ";
                }
                if (isset($filters[($i + 1)])) {
                    for ($j = $i + 1; $j < $number; $j++) {
                        $next = $filters[$j];
                        if ($next['join'] == 'or') {
                            if ($next['option']) {
                                $or_val = $next['option'];
                            } else {
                                $or_val = $next['value'];
                            }
                            if ($next['operator_type'] == "Exactly matching" || !$next['operator_type'] || empty($next['operator_type'])) {
                                array_push($operator_type, " = '" . $or_val . "'");
                            }

                            if ($next['operator_type'] == "Containing") {
                                array_push($operator_type, " LIKE '%" . $or_val . "%'");
                            }

                            if ($next['operator_type'] == "Is empty") {
                                array_push($operator_type, " IS NULL");
                            }

                            if ($next['operator_type'] == "Is not empty") {
                                array_push($operator_type, " !=''");
                            }

                            if (in_array($next['operator_type'], array('>', '<', '='))) {
                                $operator_type[] = "";
                            }
                        } else {
                            $j = $number;//Make falase and exit loop
                        }
                    }

                }
                $filter_args[$filter['type']] = $operator_type;
            }
        }
        return $filter_args;
    }
}

/** ===================================
 * Prepare Filter ARGS
 * ====================================    */
if (!function_exists('prepare_filter_sql')) {
    function prepare_filter_sql($filters, $ids = false)
    {

        if (!empty($_GET['print_filter'])) {
            echo "<pre>";
            print_r($ids);
            echo "</pre>";
        }
        $filters_sql = "";
        $number = count($filters);
        for ($i = 0; $i < $number; $i++) {
            $filter = $filters[$i];
            $custom_value = false;
            $do_the_work = $filter['type'];
            if ($ids) {
                if (array_key_exists($filter['type'], $ids) && $ids[$filter['type']]) {
                    $replacement = filter_values_replacement($filter, $ids[$filter['type']]);
                    $filter['og_name'] = $filter['type'];
                    $filter['type'] = $replacement['type'];
                    $filter['if_contains'] = $replacement['if_contains'];
                    $filter['value'] = $replacement['value'];
                    $filter['sql'] = $replacement['sql'];
                    $filter['custom_value'] = $replacement['custom_value'];
                    $filter['field_type'] = $replacement['field_type'];
                    $filter['is_not'] = $replacement['is_not'];
                    $filter['operator_type'] = $replacement['operator_type'];
                    if ($replacement['option'] || $replacement['option'] === '0') {
                        $filter['option'] = $replacement['option'];
                    }
                    if ($replacement['option'] === '0' || ($replacement['value'] === '0' && in_array($filter['og_name'], [
                                'previous_contacts'
                            ]))) {
                        $filter['sql'] = true;
                        $filter['custom_value'] = "((({$filter['type']} IS NULL OR {$filter['type']} ='' OR {$filter['type']} ='0')";
                    }
                } else {
                    $do_the_work = false;
                }
            }
            if ($do_the_work) {
                if ($filter['custom_value']) {
                    $custom_value = true;
                    $val = $filter['custom_value'];
                } elseif ($filter['option'] || $filter['option'] === '0') {
                    $val = $filter['option'];
                } else {
                    if (strpos($filter['value'], "||")) {
                        $filter['operator_type'] = "Between";
                        $val = explode("||", $filter['value']);
                    } else {
                        $val = $filter['value'];
                    }
                }
                if ($_GET['print_filter']) {
                    echo "<pre>";
                    print_r($filter);
                    echo "</pre>";
                }
                if ($filter['join'] == 'and' || ($i == 0 && $filter['join'] == 'or')) {
                    if ($filter['operator_type'] == "Exactly matching" || !$filter['operator_type'] || empty($filter['operator_type'])) {
                        if (empty($val)) {
                            $final_val = " is null  or " . $filter['type'] . " = '' ";
                        } else {
                            $final_val = " = '" . addslashes($val) . "'";
                        }
                        $operator_type = $custom_value ? $val : $final_val;
                    }

                    if ($filter['operator_type'] == "Containing") {
                        if ($filter['field_type'] == 'date') {
                            $operator_type = $custom_value ? $val : " LIKE '" . addslashes($val) . "'";
                        } else {
                            if ($filter['if_contains']) {
                                $filter['type'] = $filter['if_contains'];
                            }
                            $operator_type = $custom_value ? $val : " LIKE '%" . addslashes($val) . "%'";
                        }
                    }

                    if ($filter['operator_type'] == "StartsWith") {
                        $operator_type = $custom_value ? $val : " LIKE '" . addslashes($val) . "%'";
                    }

                    if ($filter['operator_type'] == "Is empty") {
                        $operator_type = $custom_value ? $val : " IS NULL";
                    }

                    if ($filter['operator_type'] == "Between") {
                        $operator_type = $custom_value ? $val : " between '" . addslashes($val[0]) . "' AND '" . addslashes($val[1]) . "'";
                    }
                    if (in_array($filter['operator_type'], array('>', '<', '=', '>=', "<="))) {
                        $operator_type = $val;
                        //$filter['type'] = "";
                    }

                    if ($filter['operator_type'] == "Is not empty") {
                        $operator_type = $custom_value ? $val : " !=''";
                    }

                    if ($filter['operator_type'] == "Is not") {
                        if ($filter['is_not']) {
                            $val = $filter['is_not'];
                        }

                        $not = '';
                        $second_condition = ' or ';
                        if (empty($val)) {
                            $not = 'NOT';
                            $second_condition = ' AND ';
                        }
                        $operator_type = $custom_value ? $val : " !='" . addslashes($val) . "'" . $second_condition . $filter['type'] . " IS " . $not . " NULL ";
                    }
                    $val_array = explode(" ", $val);
                    if (strtolower($val_array[0]) === 'and' && !$filter['sql']) {
                        $filters_sql .= " " . $val . "";
                    } else {
                        $filters_sql .= $filter['sql'] ? " " . $filter['join'] . " " . $val : " " . $filter['join'] . " ( (" . $filter['type'] . "  " . $operator_type . "";
                    }


                    if ($filter['operator_type'] == "Is empty" && !$filter['value']) {
                        $operator_type = " = ''";
                        $filters_sql .= " OR " . $filter['type'] . "  " . $operator_type . "";
                    }
                    $filters_sql .= " )";
                    if (isset($filters[($i + 1)])) {
                        for ($j = $i + 1; $j < $number; $j++) {
                            $next = $filters[$j];
                            $custom_value_or = false;
                            $do_the_work_or = $next['type'];
                            if ($ids) {
                                if (array_key_exists($next['type'], $ids) && $ids[$next['type']]) {
                                    $replacement_or = filter_values_replacement($next, $ids[$next['type']]);
                                    $next['type'] = $replacement_or['type'];
                                    $next['value'] = $replacement_or['value'];
                                    $next['if_contains'] = $replacement_or['if_contains'];
                                    $next['sql'] = $replacement_or['sql'];
                                    $next['custom_value'] = $replacement_or['custom_value'];
                                    $next['field_type'] = $replacement_or['field_type'];
                                    $next['is_not'] = $replacement_or['is_not'];
                                    if ($replacement_or['option']) {
                                        $next['option'] = $replacement_or['option'];
                                    }
                                } else {
                                    $do_the_work_or = false;
                                }
                            }
                            if (!empty($_GET['next'])) {
                                var_export($next);
                            }
                            if ($do_the_work_or && $next['join'] == 'or') {
                                if ($next['custom_value']) {
                                    $custom_value_or = true;
                                    $or_val = $next['custom_value'];
                                } elseif ($next['option']) {
                                    $or_val = $next['option'];
                                } else {
                                    if (strpos($next['value'], "||")) {
                                        $next['operator_type'] = "Between";
                                        $or_val = explode("||", $next['value']);
                                    } else {
                                        $or_val = $next['value'];
                                    }
                                }
                                if ($next['operator_type'] == "Exactly matching" || !$next['operator_type'] || empty($next['operator_type'])) {
                                    if (empty($or_val)) {
                                        $final_val = " is null  or " . $next['type'] . " = '' ";
                                    } else {
                                        $final_val = " = '" . addslashes($or_val) . "'";
                                    }
                                    $operator_type_or = $custom_value_or ? $or_val : $final_val;
                                }
                                if ($next['operator_type'] == "Containing") {
                                    if ($next['field_type'] == 'date') {
                                        $operator_type_or = $custom_value_or ? $or_val : " LIKE '" . addslashes($or_val) . "'";
                                    } else {
                                        if ($next['if_contains']) {
                                            $next['type'] = $next['if_contains'];
                                        }
                                        $operator_type_or = $custom_value_or ? $or_val : " LIKE '%" . addslashes($or_val) . "%'";
                                    }
                                }

                                if ($next['operator_type'] == "StartsWith") {
                                    $operator_type_or = $custom_value_or ? $or_val : " LIKE '" . addslashes($or_val) . "%'";
                                }

                                if ($next['operator_type'] == "Is empty") {
                                    $operator_type_or = $custom_value_or ? $or_val : " IS NULL";
                                }

                                if ($next['operator_type'] == "Between") {
                                    $operator_type_or = $custom_value_or ? $or_val : " between '" . addslashes($or_val[0]) . "' AND '" . addslashes($or_val[1]) . "'";
                                }

                                if ($next['operator_type'] == "Is not empty") {
                                    $operator_type_or = $custom_value_or ? $or_val : " !=''";
                                }

                                if ($next['operator_type'] == "Is not") {
                                    if ($next['is_not']) {
                                        $or_val = $next['is_not'];
                                    }
                                    $next_not = '';
                                    $next_second_condition = ' or ';
                                    if (empty($or_val)) {
                                        $next_not = 'NOT';
                                        $next_second_condition = ' AND ';
                                    }
                                    $operator_type = $custom_value ? $or_val : " !='" . addslashes($or_val) . "'" . $next_second_condition . $next['type'] . " IS " . $next_not . " NULL ";
                                }
                                if (in_array($next['operator_type'], array('>', '<', '=', '>=', "<="))) {
                                    $operator_type_or = $or_val;
                                    //$next['type'] = "";
                                }

                                if (strpos(strtolower($or_val), "or") == 0 && strpos(strtolower($or_val), "or") !== false && !$next['sql']) {
                                    $filters_sql .= " " . $or_val . "";
                                } else {
                                    $filters_sql .= $next['sql'] ? " " . $next['join'] . " " . $or_val : " " . $next['join'] . " (" . $next['type'] . "  " . $operator_type_or . "";
                                }
                                if ($next['operator_type'] == "Is empty" && !$next['value']) {
                                    $operator_type_or = " = ''";
                                    $filters_sql .= " OR " . $next['type'] . "  " . $operator_type_or . "";
                                }
                                $filters_sql .= " )";
                            } else {
                                $j = $number;//Make falase and exit loop
                            }

                        }

                    }
                    if (!empty($_GET['stop'])) {
                        echo $val . '<br>';
                        echo json_encode($filters);

                    }
                    //$filters_sql .= " /";
                    $filters_sql .= " )";

                    if (!empty($_GET['filter_sql'])) {
                        echo 'filter sql <br>';
                        echo json_encode($filters_sql);
                    }
                }
            }


        }
        return $filters_sql;
    }
}

/** ===================================
 * filter_numbers_from_filter
 * ====================================    */
if (!function_exists('filter_numbers_from_filter')) {
    function filter_numbers_from_filter($data, $filters, $new_field_names = array())
    {
        foreach ($filters as $key => $v) {
            $final_results = array_filter($data, function ($o) use ($key, $v, $new_field_names) {
                $ol = array_change_key_case($o, CASE_LOWER);
                $ok = array_keys($ol);
                $ov = array_values($ol);
                $object = array();
                for ($i = 0; $i < count($ol); $i++) {
                    if (array_key_exists($ok[$i], $new_field_names)) {
                        $object[$new_field_names[$ok[$i]]] = $ov[$i];
                    } else {
                        $object[str_replace(" ", "_", $ok[$i])] = $ov[$i];
                    }
                }
                foreach ($v as $value) {
                    if (strpos(strtolower($value), "like")) {
                        $like = explode("%", $value);
                        if (strpos((string)"|" . $object[$key], (string)$like[1])) return true;
                    }

                    if (strpos(strtolower($value), "=")) {
                        $like = explode("'", $value);
                        if ($object[$key] == $like[1]) return true;
                    }

                    if (strpos(strtolower($value), "null")) {
                        if ($object[$key] == '' || empty($object[$key])) return true;
                    }

                    if (strpos(strtolower($value), "!=")) {
                        if ($object[$key] != '' || !empty($object[$key])) return true;
                    }
                }
                return false;
            });
        }
        return $final_results;
    }
}

/** ===================================
 * filter_ids_from_results
 * ====================================    */
if (!function_exists('filter_ids_from_results')) {
    function filter_ids_from_results($data, $id_field)
    {
        $ids = array();
        foreach ($data as $key => $value) {
            $ids[] = $value["$id_field"];
        }
        return implode(",", $ids);
    }
}


if (!function_exists('setup_filter_data')) {
    function setup_filter_data($data, $type = 'filter_types', $unset = array(), $replacements = array(), $append_options = array(), $special_field_types = array())
    {
        if (isset($data[0])) {

            $records = $data[0];

            if (count($unset) > 0) {
                foreach ($unset as $u) {
                    unset($records[$u]);
                }
            }

            if (!empty($special_field_types)) {
                foreach ($special_field_types as $field => $_type) {
                    if (array_key_exists($field, $records)) {
                        $records[$field] = $_type;
                    }
                }
            }

            if (!empty($append_options)) {
                foreach ($append_options as $key => $value) {
                    $options = array();
                    if (array_key_exists($key, $records)) {
                        if (is_array($value)) {
                            $options = $value;
                        } else {
                            $operations = explode("|", $value);
                            $class = $operations[0];
                            $method = $operations[1];
                            $params = json_decode($operations[2], true);
                            if ($class == 'general') {
                                $results = $method($params);
                            }
                            if ($class == 'fields') {
                                $class_handle = new $class();
                                $temp = $class_handle->$method($params);
                                $results = $temp['options'];
                            } else if ($class == 'custom') {
                                if ($method == 'publish') {
                                    $results = array(
                                        array('title' => 'Yes', 'value' => 'yes'),
                                        array('title' => 'No', 'value' => 'no'),
                                        array('title' => 'Blank', 'value' => '')
                                    );
                                }
                            } else {

                                $class_handle = new $class();
                                $results = $class_handle->$method($params);
                            }
                            $fields = json_decode($operations[3], true);
                            foreach ($results as $result) {
                                $field_value = explode("+", $fields[1]);
                                $row = array();
                                if (count($field_value) == 1) {
                                    $row['label'] = $result[$fields[1]];
                                } else {
                                    $primary_value = $result[$field_value[0]];
                                    if (strpos($field_value[1], '=>')) {
                                        $inner_array = explode('=>', $field_value[1]);
                                        $secondary_value = $result[$inner_array[0]][$inner_array[1]];
                                    } else {
                                        $secondary_value = $result[$field_value[1]];
                                    }
                                    $row['label'] = $primary_value . ($secondary_value ? " [" . $secondary_value . "]" : '');
                                }
                                $row['value'] = ($class == 'fields' ? str_replace(" ", "_", $result[$fields[0]]) : $result[$fields[0]]);
                                $options[] = $row;
                            }
                        }
                    }
                    $records[$key] = $options;
                }
            }


            if (!empty($replacements)) {
                foreach ($replacements as $old => $new) {
                    $records = change_key($records, $old, $new);
                }
            }

            $columns = array_keys($records);


            if (strpos($type, '_types')) {
                foreach ($columns as $select) {
                    $custom_id = explode("_#_", $select);//db_field_name_#_FieldTitle
                    $filter_type = array(
                        'id' => isset($custom_id[1]) ? $custom_id[0] : strtolower(str_replace(" ", "_", $select)),
                        'title' => ucwords(strtolower(str_replace("_", " ", (isset($custom_id[1]) ? $custom_id[1] : $select)))),
                        'extra' => true,
                        'options' => (is_array($records[$select]) ? $records[$select] : false),
                        'field_type' => (!is_array($records[$select]) ? $records[$select] : false),
                    );

                    $filter_types[] = $filter_type;
                }
                return $filter_types;
            }

            if (strpos($type, 'mensions')) {
                foreach ($columns as $select) {
                    $custom_id = explode("_#_", $select);//db_field_name_#_FieldTitle
                    $dimensions[0]['All Fields'][] = array(
                        'id' => isset($custom_id[1]) ? $custom_id[0] : strtolower(str_replace(" ", "_", $select)),
                        'title' => ucwords(strtolower(str_replace("_", " ", (isset($custom_id[1]) ? $custom_id[1] : $select)))),
                        'active' => 1,
                        'field_type' => (!is_array($records[$select]) ? $records[$select] : false)
                    );
                }
                return $dimensions;
            }
        }
        return array();
    }
}

if (!function_exists('filter_arg_statement')) {
    function filter_arg_statement($args, $field)
    {
        $last = array_pop($args);
        $sql = "AND ( ";
        foreach ($args as $arg) {
            $sql .= " $field " . $arg . " OR ";

        }
        $sql .= " $field " . $last . " )";
        return $sql;
    }
}


if (!function_exists('pull_field')) {
    function pull_field($table, $field, $where = "")
    {
        $res = '';
        try {
            $dbh = get_dbh();
            $query = "SELECT $field FROM $table $where";
            $sth = $dbh->prepare($query);
            if (!$sth) {
                throw new Exception("Failed to prepare statement: $query");
            }
            dev_debug("PULL FIELD: $query");
            $sth->execute();
            $res = $sth->fetchColumn();

        } catch (Exception $exception) {
            Log::error("pull_field error: " . $exception->getMessage());
        }
        return $res;
    }
}

if (!function_exists('custom_date_and_time')) {
    /*------------------------------
			// DATE AND TIMES USED IN INSERT SCRIPTS
			---------------------------------*/
    function custom_date_and_time()
    {
        return date('Y-m-d H:i:s');
    }
}

if (!function_exists('get_view_loop')) {
    ////////////////////////////////////////////////////////
//  GET VIEW DETAILS
///////////////////////////////////////////////////////
    function get_view_loop($parent_page, $section = 'tabs', $group_category = false, $only_show_these_ids = '', $isAngularDriven = false)
// parent id -> the id of the mother page ++ section ->  loop result by tab or block
    {
        $modules = new Modules;
        global $candidate_id;
        global $class_type;
        global $school_info;
        $sub_modules = $modules->get_submodules(array('group_concat' => true, 'submodules' => $school_info['modules']));
        $schools_sub_modules = explode(',', $sub_modules);
        sort($schools_sub_modules);
        $schools_sub_modules = implode(',', $schools_sub_modules);
        global $personal_view_settings;

        if ($group_category) {
            $group_category_list = "AND db18717 = '$group_category' ";
        }

        if ($only_show_these_ids) {
            $only_show_these_ids_sql = " AND form_create_view.id IN($only_show_these_ids) ";
        }
        //echo $group_category_list;

        $dbh = get_dbh();
        // GET THE VALUES OF THE CHOSEN PAGE
        if ($_SESSION['ulevel'] == 9) {
            $sql = "SELECT form_create_view.id as idss,db,db1,db193,db190,db4,db5,db82,db282,db284,db298,db32668,form_submodules.id,form_submodules.db766
		FROM form_create_view LEFT JOIN form_submodules
		ON form_create_view.id=form_submodules.db327
		WHERE db193=?
		AND db82='yes'
		AND form_submodules.id IN ($schools_sub_modules)
		$group_category_list
		$only_show_these_ids_sql
		order by db259 ASC";
            $sth = $dbh->prepare($sql);
            dev_debug($sql);
            $sth->execute(array($parent_page));
        } else {
            $sql = "SELECT form_create_view.id as idss,db,db1,db193,db190,db4,db5,db82,db282,db284,db298,db32668,form_submodules.id,form_submodules.db766
		FROM form_create_view LEFT JOIN form_submodules ON form_create_view.id=form_submodules.db327
		WHERE db193=? AND db82='yes' AND form_submodules.id IN ({$schools_sub_modules}) {$group_category_list} {$only_show_these_ids_sql}";
            //$sql .= $_SESSION['ulevel'] > 4 ? " AND form_submodules.db766 = ? " : " AND form_submodules.db766 >= ? ";
            $sql .= $_SESSION['ulevel'] > 4 ? " AND (form_submodules.db766 = ? OR FIND_IN_SET(?,db278000))" : " AND (form_submodules.db766 >= ? OR FIND_IN_SET(?,db278000))";
            $sql .= " order by db259 ASC";

            $sth = $dbh->prepare($sql);
            // echo $sql. '-'. $parent_page."-".$_SESSION['ulevel'];
            dev_debug($sql . '-' . $parent_page . '-' . $_SESSION['ulevel']);
            $sth->execute(array($parent_page, $_SESSION['ulevel'], $_SESSION['ulevel']));
        }

        $active_one = !isset($_GET['tab']);

        $rows = $sth->fetchAll();
        foreach ($rows as $row) {
            $view_id = $row['idss'];// table name
            $view_name = $row['db'];// Page per limit
            $view_display_heading = $row['db1'];// page name
            $view_parent = $row['db193'];// page level
            $view_pop_edit = $row['db190'];// the page that will show up when the user chosed to add or edit
            $view_limit = $row['db4'];//page template & update link
            $view_default_order = $row['db5'];//content of page
            $view_filer_option = $row['db82'];//desc of page
            $view_module = $row['db282'];//Addon - Custom Script(db282)
            $view_show_add_button = $row['db284'];
            $view_show_edit_button = $row['db298'];
            $view_url_name = $row['db32668'];

            $linker = $view_url_name ?: "view_" . $view_id;
            //return array($view_id,$view_name,$view_display_heading,$view_parent,$view_limit,$view_default_order,$view_filter_option);
            //if $_GET['tab']  is not set on first load set it to the first record url name
            if (!isset($_GET['tab'])) {
                $_GET['tab'] = $rows[0]['db32668'] ?: "view_" . $rows[0]['idss'];
            }

            if ($section == 'tabs') {
                //echo '<li><a href="#tab_view_'.$view_id.'" class="selected">'.$view_name.'</a></li>';
                echo '<li id="' . $view_id . '" ' . ((isset($_GET['tab']) && $_GET['tab'] == $linker) ? 'class="active"' : '') . '><a href="#' . $linker . '" data-toggle="tab">' . $view_name . '</a></li>'; // simple tabs
            } else {
                if ($isAngularDriven) {
                    echo "<div ng-show=\"selected_tab.idss == {$view_id}\" role=\"tabpanel\" class=\"tab-pane\" id=\"'.$linker.'\">";
                } else {
                    echo '<div role="tabpanel" class="tab-pane ' . ((isset($_GET['tab']) && $_GET['tab'] == $linker) ? 'active' : '') . '" id="' . $linker . '">';
                }
                render_dynamic_view_for_tab($view_id);
                echo '</div>';
            }
            $active_one = false;

        }
    }
}


if (!function_exists('get_lead_invoice_settings')) {
    function get_lead_invoice_settings($id)
    {

        $dbh = get_dbh();
        $sql = "SELECT *,
		(select abv from system_currency where system_currency.id=db14987) as currency
		FROM lead_invoice_settings WHERE id='$id'";
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $lead_invoice_settings_id = $row['id'];
        $lead_invoice_settings_rec_id = $row['rec_id'];
        $lead_invoice_settings_usergroup = $row['usergroup'];
        $lead_invoice_settings_rel_id = $row['rel_id'];
        $lead_invoice_settings_select_currency = $row['currency'];
        $lead_invoice_settings_issue_date = format_date("d F Y", $row['db14988']);
        $lead_invoice_settings_due_date = format_date("d F Y", $row['db14989']);
        $lead_invoice_settings_reminder_date = format_date("d F Y", $row['db14990']);
        $lead_invoice_settings_select_office = $row['db14991'];
        $lead_invoice_settings_recipient_type = $row['db14992'];
        $lead_invoice_settings_select_recipient = $row['db14993'];
        $lead_invoice_settings_select_payment_option = $row['db14995'];
        $lead_invoice_settings_number_of_instalments_allowed = $row['db14996'];
        $lead_invoice_settings_delivery_options = $row['db14997'];
        $lead_invoice_settings_include_vat = $row['db14998'];
        $lead_invoice_settings_enter_discount_amount = $row['db14999'];
        $lead_invoice_settings_comments = $row['db15000'];
        $lead_invoice_settings_deposit = $row['db15002'];
        $lead_invoice_settings_deposit_value = $row['db15003'];
        $lead_invoice_settings_deposit_due_date = format_date("d F Y", $row['db15004']);
        $lead_invoice_settings_username = $row['username_id'];
        $lead_invoice_status = $row['db15005'];
        $lead_invoice_username_id = $row['username_id'];

        return array($lead_invoice_settings_id, $lead_invoice_settings_rec_id, $lead_invoice_settings_usergroup, $lead_invoice_settings_rel_id, $lead_invoice_settings_select_currency, $lead_invoice_settings_issue_date, $lead_invoice_settings_due_date, $lead_invoice_settings_reminder_date, $lead_invoice_settings_select_office, $lead_invoice_settings_recipient_type, $lead_invoice_settings_select_recipient, $lead_invoice_settings_select_payment_option, $lead_invoice_settings_number_of_instalments_allowed, $lead_invoice_settings_delivery_options, $lead_invoice_settings_include_vat, $lead_invoice_settings_enter_discount_amount, $lead_invoice_settings_comments, $lead_invoice_settings_deposit, $lead_invoice_settings_deposit_value, $lead_invoice_settings_deposit_due_date, $lead_invoice_settings_username, $lead_invoice_status, $lead_invoice_username_id);

    }
}


if (!function_exists('render_dynamic_view_for_tab')) {
    /** ===================================
     * View
     * ==================================== */
    function render_dynamic_view_for_tab($view_id)
    {
        global $paginator, $db, $school_info;
        $views = new Views;
        $view_info = $views->get(array('id' => $view_id));
        //check to see if there is a spacific view for this usergroup
        $usergroup_specific_view = pull_field("form_create_view", "id", "WHERE db = '$view_info[title]' AND usergroup = '$_SESSION[usergroup]' and (rec_archive is null or rec_archive = '')");
        if (isset($usergroup_specific_view) && $usergroup_specific_view != '') {
            $view_info = $views->get(array('id' => $usergroup_specific_view));
        }
        $view_info['linker'] = $view_info['url_name'] ?: "view_" . $view_info['id'];
        $search_sql = "";

        // echo '<pre>';
        // print_r($view_info);
        // echo '</pre>';

        require_once(ABSPATH . "/app/libs/sql_parser/PHPSQLParser.php");
        dev_debug(json_encode($view_info));

        if ($_GET['order'] && ($_GET['v'] == $view_id)) {
            $get_vars = $_SERVER['QUERY_STRING'];
            $get_vars = explode("&", $get_vars);

            foreach ($get_vars as $var) {
                $get_var = explode("=", $var);
                if ($get_var[0] == "order") {
                    $order = $get_var[1];
                }
            }

            $order = explode("+", $order);
            $order_count = count($order);
            $view_info['order_by'] = urldecode("" . $order[0] . "");
            $view_info['default_order'] = "" . $order[$order_count - 1] . "";
        }
        dev_debug(json_encode($view_info));

        //Use all fields to search
        if ($_REQUEST['search'] && ($_GET['tab'] == $view_id)) {

            $search_term = htmlspecialchars($_REQUEST['search']);
            $parser = new PHPSQLParser($view_info['query'], true);
            $search_fields = $parser->parsed['SELECT'];

            $search_sql = "AND (";
            $search_count = 0;
            foreach ($search_fields as $feild) {
                if ($search_count != 0) {
                    $search_sql .= " OR ";
                }
                echo $feild['base_expr'];
                if ($feild['base_expr'] != 'IF' && $feild['base_expr'] != 'if') {
                    $search_sql .= "(" . $feild['base_expr'] . " LIKE '%" . $search_term . "%')";
                    $search_count++;
                }
            }
            $search_sql .= " )";
        }


        //Apply the filters if any
        if (is_countable($filter['description']) && count($filter['description'])) {
            $filters_sql = "";
            foreach ($filter['description'] as $item) {

                if ($item['operator_type'] == "Exactly matching") {
                    $operator_type = " = '" . $item['value'] . "'";
                }

                if ($item['operator_type'] == "Containing") {
                    $operator_type = " LIKE '%" . $item['value'] . "%'";
                }

                if ($item['operator_type'] == "Is empty") {
                    $operator_type = " IS NULL";
                }

                if ($item['operator_type'] == "Is not empty") {
                    $operator_type = " !=''";
                }

                $filters_sql .= " " . $item['join'] . " " . $item['type'] . "  " . $operator_type . "";
            }
        }

        // echo '<pre>';
        // print_r($this->filter['description']);
        // echo '</pre>';
        // exit();

        // check if function exists and also if it has the right words it it
        if ($view_info['where_over_ride'] && preg_match_search(strtolower($view_info['where_over_ride']), 'where')) {
            $where_over_ride = $view_info['where_over_ride'];
            $where_over_ride = str_replace("[GET]partner_id[/GET]", $_GET['partner_id'], $where_over_ride);
            $where_over_ride = str_replace("[SESSION]partner_id[/SESSION]", $_SESSION['partner_id'], $where_over_ride);
            //check if the where has a usergroup
            if (preg_match_search(strtolower($where_over_ride), 'usergroup')) {
                $where_over_ride = str_replace('[SESSION]usergroup[/SESSION]', $_SESSION['usergroup'], $where_over_ride);
            } else {
                $where_over_ride = $where_over_ride . " AND usergroup='" . $school_info['id'] . "' AND (rec_archive IS NULL OR rec_archive ='') ";
            }

        } else {
            $where_over_ride = "WHERE usergroup='" . $school_info['id'] . "' AND (rec_archive IS NULL OR rec_archive ='' ) AND rel_id='$_GET[ref]'";
        }

        $where_over_ride = session_floating($where_over_ride);
        $where_over_ride = get_floating($where_over_ride);
        //run query
        $query = $view_info['query'] . " $where_over_ride " . $view_info['and_over_ride'] . " " . $search_sql . " " . $filters_sql . " ORDER BY " . $view_info['order_by'] . " " . $view_info['default_order'];

        dev_debug($query);
        dev_debug(json_encode($view_info));

        if (!$view_info["add_on_custom_script"]) {
            $parser = new PHPSQLParser($view_info['query'], true);

            //Set the Filters List
            $filter_types = array();
            foreach ($parser->parsed['SELECT'] as $select) {
                $filter_types[] = array(
                    'id' => $select['base_expr'],
                    'title' => $select['alias']['name'],
                    'extra' => true,
                );
            }

            if (strtolower($key) == "manage") {
                echo "ID";
            } else {
                echo $key;
            }

            //Set the Dimension List
            $dimensions[] = array();
            foreach ($parser->parsed['SELECT'] as $select) {
                $dimensions[0]['All Fields'][] = array(
                    'id' => $select['base_expr'],
                    'title' => $select['alias']['name'],
                    'active' => 1
                );
            }

            $query = $query . $limit_sql;
            $dbh = get_dbh();
            $stmt = $dbh->prepare($query);
            $sth = $stmt->execute();
            $results = $stmt->fetchAll(2);


            $data = array(
                'meta_title' => 'Views',
                'view_file' => 'dynamic/index',
                'view' => $view_info,
                'results' => $results,
                'dimensions' => $dimensions,
                'filter_types' => $filter_types,
                'filter' => $filter,
                'filters_args' => $filters_args,
                'tab' => $view_id,
                'filter_page_name' => 'dynamic_view_' . $view_info['id'],
                'query' => $query

            );
            compile_view_data_for_tab($data);
        } else {
            include($_SERVER['DOCUMENT_ROOT'] . '/engine/modules/' . $view_info['add_on_custom_script']);

        }
    }
}


if (!function_exists('compile_view_data_for_tab')) {
    function compile_view_data_for_tab($data)
    {
        $viewModel = new views;
        ?>
        <div class="inner_container">
            <div class="top_section" style="margin-bottom:50px;">
                <div class="buttons_wrap">
                    <?php if ($data['view']['show_add_button'] == "yes") { ?>
                        <a href="<?php echo engine_url('controller.php?width=850&height=600&pick_page=' . $data['view']['subpage_popup_on_edit'] . '&ref=' . $_GET['ref'] . '&jqmRefresh=true'); ?><?php echo($_GET['partner_id'] ? '&partner_id=' . $_GET['partner_id'] : ''); ?>&tab=<?php echo $data['view']['id']; ?>"
                           class="btn btn-primary pull-right thickbox">Add a new entry</a>
                    <?php } ?>

                    <?php if ($data['view']['show_export_button'] == "yes") { ?>
                        <div class="pull-right">
                            <form action="<?php echo engine_url('/tools/export_xls/export.php'); ?>" method="post"
                                  name="export_to_xls" target="_blank" class="float_right">
                                <input name="exportsql" type="hidden" value="<?php echo $data['query']; ?>"/>
                                <input name="pagename" type="hidden" value="<?php echo $data['view']['title'] ?>"/>
                                <input name="ExportType" type="hidden" value="export-to-excel"/>
                                <input name="Export" type="submit" value="Export current data to xls"
                                       class="btn btn-primary"/>
                            </form>
                        </div>
                    <?php } ?>

                    <?php if ($data['view']['allow_bulk_actions'] == "yes") { ?>
                        <div class="pull-right"><?php echo get_bulk_actions($data['view']['id']); ?></div>
                    <?php } ?>
                </div>
            </div>

            <div class="stnrd_box dynamic_wrap" style="margin-top:20px;">

                <section id="filters_wrap"></section>

                <?php $url_sanitized = explode("?", current_url());
                if ($data['view']['search_fields']) { ?>
                    <div class="top_filter">
                        <form method="get" action="<?php echo $url_sanitized['0']; ?>">
                            <div class="row" style="margin-left: 20px;">
                                <div class="col-md-10" style="margin: 0; padding: 0;"><input type="text"
                                                                                             class="form-control searchtext"
                                                                                             placeholder="Start typing to search..."
                                                                                             name="search"
                                                                                             value="<?php echo $_GET['search']; ?>">
                                </div>
                                <div class="col-md-2" style="margin: 0; padding: 0;"><a
                                            href="<?php echo $url_sanitized['0']; ?>/?tab=<?php echo $data['view']['linker'] ?>"
                                            class="btn btn-default btn-lg">Clear Search</a></div>
                            </div>
                            <input type="hidden" name="tab" value="<?php echo $data['view']['linker'] ?>">
                        </form>
                    </div>
                <?php } ?>

                <style type="text/css">
                    .left_table {
                        position: absolute;
                        left: 0px;
                        width: 145px; /*border-right: solid 1px #e1e1e1;*/
                    }

                    .schools_list {
                        overflow: auto;
                        background-color: #fff;
                    }

                    .dynamic_wrap {
                        position: relative;
                    }

                    .dynamic_wrap table tr th {
                        width: 1px;
                        white-space: nowrap;
                        font-size: 14px;
                        height: 37px;
                    }

                    .dynamic_wrap table tr td {
                        width: 1px;
                        white-space: nowrap;
                        font-size: 14px;
                        height: 37px;
                    }
                </style>

                <?php if (count($data['results'])) { ?>

                    <table class="left_table table table-condensed table-striped">
                        <thead>
                        <tr>
                            <?php
                            if (
                                ($data['view']['show_edit_button'] == "yes") ||
                                ($data['view']['show_view_button'] == "yes") ||
                                ($data['view']['hide_delete_button'] == "no") ||
                                ($data['view']['allow_bulk_actions'] == "yes") ||

                                count($data['view']['extra_buttons'])
                            ) {
                                ?>
                                <style type="text/css"> .schools_list {
                                        margin-left: 145px;
                                    }</style>
                                <th>&nbsp;</th>
                            <?php } ?>
                        </tr>
                        </thead>
                        <tbody>
                        <?php

                        $ki = 1;
                        foreach ($data['results'][0] as $key => $value) {
                            if ($ki == 1) {
                                $main_key = $key;
                            }
                            $ki++;
                        }


                        foreach ($data['results'] as $entry) {

                            $link = "#";
                            $copy_link = engine_url('controller.php?pg=' . $data['view']['subpage_popup_on_edit'] . '&rec=' . $entry[$main_key] . '&cpy=1&ref=' . $_GET['ref'] . '&width=850&height=600&jqmRefresh=true');
                            $edit_link = engine_url('controller.php?pg=' . $data['view']['subpage_popup_on_edit'] . '&rec=' . $entry[$main_key] . '&ref=' . $_GET['ref'] . '&width=850&height=600&jqmRefresh=true');
                            if (isset($data['view']['main_link'][0]['href']) && $data['view']['main_link'][0]['href'] != '') {
                                $view_link = $data['view']['main_link'][0]['href'] . $entry[$main_key] . '?pg=' . $data['view']['subpage_popup_on_edit'] . '&vw=' . $entry["username_id"] . '&ref=' . $entry[$main_key] . '&event=1';
                            } else {
                                $view_link = engine_url('direct/proc?pg=' . $data['view']['subpage_popup_on_edit'] . '&vw=' . $entry["username_id"] . '&ref=' . $entry[$main_key] . '&event=1');
                            }
                            dev_debug("VIEW BUTTON ** " . ($data['view']['main_link'][0]['href'] ?? '') . " ** $view_link");

                            dev_debug("COPY BUTTON ** " . ($data['view']['show_copy_button'] ?? '') . " ** $view_link");
                            $delete_link = engine_url('controller.php?pg=' . $data['view']['subpage_popup_on_edit'] . '&rec=' . $entry[$main_key] . '&del=' . $entry[$main_key] . '&width=850&height=600&jqmRefresh=true');
                            ?>
                            <tr>
                                <?php
                                if (
                                    ($data['view']['show_edit_button'] == "yes") ||
                                    ($data['view']['show_view_button'] == "yes") ||
                                    ($data['view']['show_copy_button'] == "yes") ||
                                    ($data['view']['hide_delete_button'] == "no") ||
                                    ($data['view']['allow_bulk_actions'] == "yes") ||
                                    !empty($data['view']['extra_buttons'])
                                ) {
                                    ?>
                                    <td class="text-center">
                                        <?php if ($data['view']['allow_bulk_actions'] == "yes") { ?>
                                            <input type="checkbox" class="bulk_action_record"
                                                   id="<?php echo $entry[$main_key] ?>"/>
                                        <?php } ?>
                                        <?php if ($data['view']['show_edit_button'] == "yes") { ?>
                                            <a href="<?php echo $edit_link; ?>" class="thickbox btn btn-default btn-xs"
                                               title="Edit Record" alt="Edit Record">
                                                <span class="glyphicon glyphicon-pencil" aria-hidden="true"></span>
                                            </a>
                                        <?php } ?>
                                        <?php if ($data['view']['show_view_button'] == "yes") { ?>
                                            <a href="<?php echo $view_link; ?>" class="btn btn-default btn-xs"
                                               title="View Record" alt="View Record">
                                                <span class="glyphicon glyphicon-file" aria-hidden="true"></span>
                                            </a>
                                        <?php } ?>
                                        <?php if ($data['view']['show_copy_button'] == "yes") { ?>
                                            <a href="<?php echo $copy_link; ?>" class="thickbox btn btn-default btn-xs"
                                               title="Copy Record" alt="Copy Record">
                                                <span class="glyphicon glyphicon-copy" aria-hidden="true"></span>
                                            </a>
                                        <?php } ?>
                                        <?php if ($data['view']['hide_delete_button'] == "no") { ?>
                                            <a href="<?php echo $delete_link; ?>"
                                               class="thickbox btn btn-default btn-xs" title="Delete Record"
                                               alt="Delete Record">
                                                <span class="glyphicon glyphicon-trash" aria-hidden="true"></span>
                                            </a>
                                        <?php } ?>
                                        <?php foreach ($data['view']['extra_buttons'] as $button) {
                                            if ($button['type'] == 'small') { ?>
                                                <a href="<?php echo $viewModel->fix_extra_button_link($button['href'], $entry); ?>"
                                                   class="<?php echo $button['class']; ?> btn btn-default btn-xs"
                                                   title="<?php echo $button['title']; ?>"
                                                   alt="<?php echo $button['title']; ?>">
                                                    <i class="fa fa-<?php echo $button['icon']; ?>"></i>
                                                </a>
                                            <?php }
                                        } ?>
                                    </td>
                                <?php } ?>
                            </tr>
                        <?php } ?>

                        </tbody>
                    </table>

                    <div class="schools_list">
                        <div class="table_wrap">
                            <table class=" table table-condensed table-striped">
                                <thead>
                                <tr>
                                    <?php
                                    $ki = 1;
                                    foreach ($data['results'][0] as $key => $value) {
                                        if (substr( $key, 0, 4 ) != 'hdn_') {
                                        ?>
                                        <th>
                                            <a href="<?php echo th_order($ki, 'asc'); ?>&v=<?= $data['view']['id'] ?>&tab<?= $data['view']['url_name'] ?>">
                                                <?php
                                                if (strtolower($key) == "manage") {
                                                    echo "ID";
                                                } else {
                                                    echo $key;

                                                }
                                                ?>
                                            </a>
                                        </th>
                                        <?php }$ki++;
                                    } ?>
                                </tr>
                                </thead>
                                <tbody class="sortable_objects">
                                <?php

                                foreach ($data['results'] as $entry) {
                                    $link = "#";
                                    $edit_link = engine_url('controller.php?pg=' . $data['view']['subpage_popup_on_edit'] . '&rec=' . $entry[$main_key] . '&width=850&height=600&jqmRefresh=true' . ($_GET['partner_id'] ? '&partner_id=' . $_GET['partner_id'] : ''));
                                    ?>
                                    <tr>
                                        <?php
                                        foreach ($entry as $key => $value) {
                                            $value = session_floating($value);
                                            if (substr( $key, 0, 4 ) != 'hdn_') {?>
                                                <td><a href="<?php echo $edit_link; ?>" class="thickbox" title="Edit Record"
                                                   alt="Edit Record"><?php echo $value; ?></a></td>
                                        <?php }
                                            }?>

                                        <?php
                                        foreach ($data['view']['extra_buttons'] as $button) {
                                            if ($button['type'] == 'big') {
                                                ?>
                                                <td class="text-right">
                                                    <a href="<?php echo $viewModel->fix_extra_button_link($button['href'], $entry); ?>"
                                                       class="<?php echo $button['class']; ?> btn btn-default btn-xs"
                                                       title="<?php echo $button['title']; ?>"
                                                       alt="<?php echo $button['title']; ?>">
                                                        <i class="fa fa-<?php echo $button['icon']; ?>"></i> <?php echo $button['title']; ?>
                                                    </a>
                                                </td>
                                            <?php }
                                        } ?>
                                    </tr>
                                <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <?php global $paginator;
                    echo $paginator->links(); ?>
                <?php } else { ?>

                    <div class="no_results">
                        <?php if ($_GET['search']) { ?>
                            <span class="glyphicon glyphicon-search"></span>
                            <h2>No match for your search criteria</h2>
                        <?php } else { ?>
                            <span class="glyphicon glyphicon-ban-circle" aria-hidden="true"></span>
                            <h2>No results found</h2>
                            <?php if ($data['view']['show_add_button'] == "yes") { ?>
                                <a href="<?php echo engine_url('controller.php?width=850&height=600&pick_page=' . $data['view']['subpage_popup_on_edit'] . '&ref=' . $_GET['ref'] . '&jqmRefresh=true'); ?><?php echo($_GET['partner_id'] ? '&partner_id=' . $_GET['partner_id'] : ''); ?>&tab=<?php echo $data['view']['id']; ?>"
                                   class="btn btn-primary thickbox">Add a new entry</a>
                            <?php } ?>
                        <?php } ?>

                    </div>
                <?php } ?>


            </div>
            <?php
            require_once(ABSPATH . "/app/views/applicants/inc_filter_modals.php"); ?>
        </div>
    <?php }
}

/** ===================================
 * uri_string
 * ====================================  */
function uri_string($exclude_admin = false)
{
    $url = $_SERVER["REQUEST_URI"];
    $url = strtok($url, '?');
    if (!$exclude_admin) {
        $url = str_replace("/admin/", "", $url);
    }
    $url = str_replace("//", "/", $url);
    return $url;
}

/** ===================================
 *URL Segments
 * ====================================    */
if (!function_exists('uri_segement')) {
    function uri_segement($segment, $exclude_admin = false)
    {
        $url = uri_string($exclude_admin);

        $url = explode("/", $url);
        //print_r($final_segments);
        $i = 1;
        foreach ($url as $u) {
            $final_segments[$i] = $u;
            $i++;
        }
        return $final_segments[$segment]??'';
    }
}

/** ===================================
 *Sanitise
 * ====================================    */
if (!function_exists('sanitise')) {
    function sanitise($value)
    {
        $value = trim($value);
        $value = stripslashes($value);
        $value = str_replace('\'', '\'\'', $value);
        $value = wordwrap($value, 70, "\r\n");

        return $value;
    }
}

//
/** ===================================
 * Encode
 * ====================================    */
if (!function_exists('encode')) {
    function encode($in, $salt = "")
    {

        if ($salt == 'unsalted') {
            $salted = $in;
        } else {
            //add salt to input
            $salted = $in . '' . $_SESSION['salt'];
        }

        return base64_encode(rawurlencode(serialize($salted)));
    }
}


if (!function_exists('get_string_between')) {
    function get_string_between($string, $start, $end)
    {
        $string = " " . $string;
        $ini = strpos($string, $start);
        if ($ini == 0) return "";
        $ini += strlen($start);
        $len = strpos($string, $end, $ini) - $ini;
        return substr($string, $ini, $len);
    }
}

/** ===================================
 * uri_string
 * 13th October 2023 Added functionality to deal with 2 [GET][/GET] in one string
 * First check if there is a [GET]
 * If there is and there is only one [GET] then business as usual
 * otherwise loop through the [GET]s and replace the value
 * ====================================  */
if (!function_exists('get_floating')) {
    function get_floating($field)
    {
        $field_array = explode('[GET]', $field);
        if (count($field_array) > 1) {
            if (count($field_array) == 2) {  //just one [GET]
                $new_val = get_string_between($field, "[GET]", "[/GET]");
                $new_val_value = $_REQUEST[$new_val]; // this
                $field = str_replace($new_val, $new_val_value, $field);
                $field = str_replace('[GET]', '', $field);
                $field = str_replace('[/GET]', "", $field);
            } else { //More thnan one [GET]
                $concat_field = $field_array[0];
                for ($i = 1; $i < count($field_array); $i++) {
                    $my_field = $field_array[$i];
                    $new_val = explode('[/GET]', $my_field);
                    $new_val_value = $_REQUEST[$new_val[0]];
                    $my_field = str_replace($new_val[0], $new_val_value, $my_field);
                    $my_field = str_replace('[GET]', '', $my_field);
                    $my_field = str_replace('[/GET]', "", $my_field);
                    $concat_field .= $my_field;

                }
                $field = $concat_field;
            }
        }
        return $field;
    }
}


/** ===================================
 * Session Floating
 * ====================================    */
if (!function_exists('session_floating')) {
    function session_floating($field, $id = '')
    {
        $string = $field;
        // if get then get the actual floating
        ///////////////////////////////////////////////////////////
        // fix get or post request query
        // simply replace the get value with the live value
        $str_to_find = "[SESSION]";
        $str_length = strlen($str_to_find);
        $last_found = 0;
        $end = "[/SESSION]";
        while (($ini = strpos($string, $str_to_find, $last_found)) !== false) {
            $ini += strlen($str_to_find);
            $len = strpos($string, $end, $ini) - $ini;
            $new_val = substr($string, $ini, $len);
            $session_val = $_SESSION[$new_val];
            if (4 != $_SESSION['ulevel'] && ('student_id' == $new_val || 'application_id' == $new_val)) {
                $session_val = $id;
            }
            $field = str_replace('[SESSION]' . $new_val . '[/SESSION]', $session_val, $field);
            $last_found = $ini + $len + strlen($end);
        }
        return $field;
    }
}


/** ===================================
 * Translate
 * ====================================    */
if (!function_exists('text_translate')) {
    function text_translate($text_to_translate, $language_id = "")
    {
        dev_debug("Translate $text_to_translate to language $language_id");
        $translate = new translateModel;
        $translation = $translate->translate($text_to_translate, $language_id);
        return $translation;
    }
}


//
/** ===================================
 * Is In Array
 * ====================================    */
if (!function_exists('is_in_array')) {
    function is_in_array($array, $key, $key_value)
    {
        $within_array = false;
        foreach ($array as $k => $v) {
            if (is_array($v)) {
                $within_array = is_in_array($v, $key, $key_value);
                if ($within_array == 'yes') {
                    break;
                }
            } else {
                if (strtolower($v) == $key_value && strtolower($k) == $key) {
                    $within_array = 'yes';
                    break;
                }
            }
        }
        return $within_array;
    }
}


/** ===================================
 * Is In Array
 * ====================================    */
if (!function_exists('truncate')) {
    function truncate($string, $length, $append = "...")
    {
        global $site;

        $string = trim($string);
        $string = strip_tags($string);

        if (strlen($string) > $length) {
            $string = wordwrap($string, $length);
            $string = explode("\n", $string, 2);
            $string = $string[0] . $append;
        }

        return $string;
    }
}

//
/** ===================================
 * This is an inbuilt function for php 5.5+
 * ====================================    */
if (!function_exists('array_column')) {
    function array_column(array $input, $columnKey, $indexKey = null)
    {
        $array = array();
        foreach ($input as $value) {
            if (!array_key_exists($columnKey, $value)) {
                trigger_error("Key \"$columnKey\" does not exist in array");
                return false;
            }
            if (is_null($indexKey)) {
                $array[] = $value[$columnKey];
            } else {
                if (!array_key_exists($indexKey, $value)) {
                    trigger_error("Key \"$indexKey\" does not exist in array");
                    return false;
                }
                if (!is_scalar($value[$indexKey])) {
                    trigger_error("Key \"$indexKey\" does not contain scalar value");
                    return false;
                }
                $array[$value[$indexKey]] = $value[$columnKey];
            }
        }
        return $array;
    }
}

//
/** ===================================
 * Encode
 * ====================================    */
if (!function_exists('time_to_here')) {
    function time_to_here($section_name)
    {
        //todo:temp for console
        if(!defined('start')){
            $time = microtime();
            $time = explode(' ', $time);
            $time = $time[1] + $time[0];
            define('start',$time);
        }
        $time = microtime();
        $time = explode(' ', $time);
        $time = $time[1] + $time[0];
        $finish = $time;
        $total_time = round(($finish - start), 4);
        if (!empty($_GET['show_timing'])) {
            echo '<pre>' . $section_name . ":" . $total_time . '</pre>';
        }
    }
}

/** ===================================
 * Decode
 * ====================================    */
if (!function_exists('decode')) {
    function decode($in, $salt = "")
    {

        if ($salt == 'unsalted') {
            $in = unserialize(rawurldecode(base64_decode($in)));
            return $in;
        } else {
            $in = unserialize(rawurldecode(base64_decode($in)));
            return str_replace($_SESSION['salt'], "", $in);
        }
    }
}

/** ===================================
 * Decode
 * ====================================    */
if (!function_exists('isJSON')) {
    function isJSON($string)
    {
        return is_string($string) && is_object(json_decode($string)) ? true : false;
    }
}

/** ===================================
 * Usergroups management
 * ====================================    */
if (!function_exists('usergroups_management')) {
    function usergroups_management()
    {
        if (session_info("ulevel") == 9) {
            $usergroups = "usergroup!=''";
        } else {
            $usergroups = "usergroup='$_SESSION[usergroup]'";
        }
        return $usergroups;
    }
}

/** ===================================
 * Current Session
 * ====================================    */
if (!function_exists('session_info')) {
    function session_info($id)
    {
        return "$_SESSION[$id]";
    }
}


/** ===================================
 * Check Login
 * ====================================  */
if (!function_exists('check_login')) {
    function check_login()
    {
        global $school_info;

        if (uri_segement(2) == "portal") {
            $login_link = front_end_url() . "login?redirect=";
        } else {
            $login_link = base_url() . "login?redirect=";
        }


        if (!$_SESSION['uid']) {
            $redirect_link = $_SERVER['REQUEST_URI'];
            //added by AFY 9/1/20119 to stop redirect to /admin/admin
            if ($redirect_link == '/admin') {
                $redirect_link = '/';
            }//end
            header("Location: " . $login_link . $redirect_link);
            exit();
        } else {
            // if($_SESSION['ulevel']=="4" || $_SESSION['ulevel']=="7"){
            // 	$redirect_link = $_SERVER['REQUEST_URI'];
            // 	header("Location: ".$login_link.$redirect_link);
            // 	exit();
            // }


            if (uri_segement(2, true) == "admin") {
                if ($_SESSION['ulevel'] == "4" || $_SESSION['ulevel'] == "7" || $_SESSION['ulevel'] == "13") {
                    //Redirect to new portals
                    header("Location: " . $school_info['href'] . "/applications/");
                    exit();
                }
                if ($_SESSION['ulevel'] == "19" || $_SESSION['ulevel'] == "5") { //professional
                    //Redirect to new portals
                    header("Location: " . $school_info['href'] . "/application/Checklist");
                    exit();
                }

                //if($_SESSION['ulevel']=="16"){
                //  //Redirect to new portals
                //  header("Location: ".$school_info['href']."/online-learning/");
                //  exit();
                //}


                if ($_SESSION['ulevel'] == "17" || $_SESSION['ulevel'] == "18") {
                    //Redirect to new portals
                    header("Location: " . $school_info['href'] . "/portal/dashboard/");
                    exit();
                }
            }
        }
    }
}


/*------------------------------
	// Send Request
	---------------------------------*/
function send_api_request($args = array())
{

    $type = $args['method'];
    $fields = $args['params'];

    $fields = (is_array($fields)) ? http_build_query($fields) : $fields;

    $url_new = $args['url'];

    if ($type == "GET" && $fields) {
        $url_new = $url_new . "?" . $fields;
    }

    $ch = curl_init($url_new);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $type);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    if ($type == "PUT" || $type == "DELETE") {
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Length: ' . strlen($fields)));
    }
    curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
    curl_setopt($ch, CURLOPT_URL, $url_new);

    $headers = array();
    if ($args['key']) {
        $headers[] = 'Authorization: ' . $args['key'];
    }

    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $response = curl_exec($ch);

    if ($type == "GET") {
        $response = @json_decode($response);
        $response = objectToArray($response);
    }
    return $response;
}


/*------------------------------
	// GENERATE RANDONM NUMBER
	---------------------------------*/
if (!function_exists('random')) {
    function random()
    {
        // generate unique ID for order based on date -----------------
        $UniqueID = date("DWHi");  // year month day
        // generate random letter - ----------------
        srand((double)microtime() * 1000000);
        $randnum = rand(1, 26);
        $tempbackground = array("", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z");
        $RandLetter = $tempbackground[$randnum];
        $UniqueID .= "-";
        $UniqueID .= $RandLetter;
        srand((double)microtime() * 1000000);
        $randnum = rand(1, 99);
        $UniqueID .= $randnum;

        return $UniqueID;
    }
}


/*------------------------------
	// Object to Array Convertor
	---------------------------------*/
function objectToArray($d)
{
    if (is_object($d)) {
        $d = get_object_vars($d);
    }

    if (is_array($d)) {
        return array_map(__FUNCTION__, $d);
    } else {
        return $d;
    }
}


/*------------------------------
	// Array to Object Convertor
	---------------------------------*/
function arrayToObject($d)
{
    if (is_array($d)) {
        return (object)array_map(__FUNCTION__, $d);
    } else {
        return $d;
    }
}


/*------------------------------
	// Time Ago
	---------------------------------*/
if (!function_exists('timeago')) {
    function timeago($feed_time)
    {
        $now = time();
        $seconds = $now - $feed_time;
        $minutes = floor($seconds / ( int )60);
        $left = $minutes * ( int )60;
        $a_seconds = $seconds - $left;
        return $minutes;
    }
}


/*------------------------------
	// File Size from bytes
	---------------------------------*/
if (!function_exists('file_size_convert')) {
    function file_size_convert($bytes)
    {
        $bytes = floatval($bytes);
        $arBytes = array(
            0 => array(
                "UNIT" => "TB",
                "VALUE" => pow(1024, 4)
            ),
            1 => array(
                "UNIT" => "GB",
                "VALUE" => pow(1024, 3)
            ),
            2 => array(
                "UNIT" => "MB",
                "VALUE" => pow(1024, 2)
            ),
            3 => array(
                "UNIT" => "KB",
                "VALUE" => 1024
            ),
            4 => array(
                "UNIT" => "B",
                "VALUE" => 1
            ),
        );

        foreach ($arBytes as $arItem) {
            if ($bytes >= $arItem["VALUE"]) {
                $result = $bytes / $arItem["VALUE"];
                $result = str_replace(".", ",", strval(round($result, 2))) . " " . $arItem["UNIT"];
                break;
            }
        }
        return $result;
    }
}


/*------------------------------
	// Engine URL
	---------------------------------*/
if (!function_exists('engine_url')) {
    function engine_url($path = "")
    {

        if ($path) {
            $first_character = substr($path, 0, 1);

            if ($first_character == "/") {
                $path = ltrim($path, '/');
            }
        }
        $domain='';
        if(!empty($_SERVER['HTTP_HOST'])){
            $domain = $_SERVER['HTTP_HOST'];
        }elseif(!empty($_SESSION['subdomain'])){
            $domain = $_SESSION['subdomain'].'.'.env('APP_URL');
        }
        if (strpos($domain, 'heiapplylocal.co.uk') !== false) {
            return env('PROTOCOL') . $domain . '/engine/' . $path;
        } else {
            return env('PROTOCOL') . $domain . '/engine/' . $path;
        }
    }
}

/*------------------------------
	// Online Learning URL
	---------------------------------*/
if (!function_exists('online_learning_url')) {
    function online_learning_url($path = "")
    {
        if ($path) {
            $first_character = substr($path, 0, 1);

            if ($first_character == "/") {
                $path = ltrim($path, '/');
            }
        }

        $domain = $_SERVER['HTTP_HOST'];
        return 'https://' . $domain . '/online-learning/' . $path;
    }
}


/*------------------------------
	// Admin URL
	---------------------------------*/
if (!function_exists('th_order_icon')) {
    function th_order_icon($title)
    {
        $current_order = explode(' ', $_REQUEST["order"]);
        if ($current_order[0] == $title) {
            if ($current_order[1] == "desc") {
                $info = '<span class="glyphicon glyphicon-chevron-up pull-right"></span>';
            } else {
                $info = '<span class="glyphicon glyphicon-chevron-down pull-right"></span>';
            }
            return $info;
        }
    }

    function th_order($title, $order)
    {
        $current_order = explode(' ', $_REQUEST["order"]);
        if ($current_order[0] == $title) {
            if ($current_order[1] == "desc") {
                $order = "asc";
            } else {
                $order = "desc";
            }
        } else {
            $order = $order;
        }

        $other_variables = $_GET;
        if ($other_variables['offset']) {
            $other_variables['offset'] = 0;
            $other_variables['page'] = 0;
        }

        foreach ($other_variables as $key => $value) {
            if ($key != 'order') {
                $more .= "&" . $key . "=" . $value;
            }
        }

        $info = "?order=" . $title . "+" . $order . $more;
        return $info;
    }
}

/*------------------------------
	// Admin URL
	---------------------------------*/
if (!function_exists('base_url')) {
    function base_url($path = "")
    {
        if ($path) {
            $first_character = substr($path, 0, 1);

            if ($first_character == "/") {
                $path = ltrim($path, '/');
            }
        }

        $domain = $_SERVER['HTTP_HOST'];
        return (env('PROTOCOL') ?? "https://") . $domain . '/admin/' . $path;

    }
}


/*------------------------------
	// Admin URL
	---------------------------------*/
if (!function_exists('front_end_url')) {
    function front_end_url($path = "")
    {
        if ($path) {
            $first_character = substr($path, 0, 1);

            if ($first_character == "/") {
                $path = ltrim($path, '/');
            }
        }

        $domain = $_SERVER['HTTP_HOST'];
        if (strpos($domain, 'heiapplylocal.co.uk') !== false) {
            return 'http://' . $domain . '/portal/' . $path;
        } elseif ($_SESSION['new_portal'] == true) {
            return 'https://' . $domain . '/portal/' . $path;
        } else {
            return 'https://' . $domain . '/portal/' . $path;
        }
    }
}


if (!function_exists('getRequestProtocol')) {
    function getRequestProtocol()
    {
        if (!empty($_SERVER['HTTP_X_FORWARDED_PROTO']))
            return $_SERVER['HTTP_X_FORWARDED_PROTO'];
        else
            return !empty($_SERVER['HTTPS']) ? "https" : "http";
    }
}


/*------------------------------
	// Current URL
	---------------------------------*/
if (!function_exists('current_url')) {
    function current_url()
    {
        $url = getRequestProtocol() . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
        return $url;
    }
}


/*------------------------------
	// Is Serialized
	---------------------------------*/
if (!function_exists('is_serialized')) {
    function is_serialized($data)
    {
        // if it isn't a string, it isn't serialized
        if (!is_string($data))
            return false;
        $data = trim($data);
        if ('N;' == $data)
            return true;
        if (!preg_match('/^([adObis]):/', $data, $badions))
            return false;
        switch ($badions[1]) {
            case 'a' :
            case 'O' :
            case 's' :
                if (preg_match("/^{$badions[1]}:[0-9]+:.*[;}]\$/s", $data))
                    return true;
                break;
            case 'b' :
            case 'i' :
            case 'd' :
                if (preg_match("/^{$badions[1]}:[0-9.E-]+;\$/", $data))
                    return true;
                break;
        }
        return false;
    }
}


/*------------------------------
	// Get option
	---------------------------------*/
if (!function_exists('get_option')) {
    function get_option($option_name, $logged_in_user = false)
    {
        global $db;

        if ($logged_in_user) {
            $logged_in_user_sql = " AND `rec_id` = {$_SESSION['uid']} ";
        } else $logged_in_user_sql = '';
        $sql = "SELECT * FROM form_profile_settings WHERE db1233='" . $option_name . "' {$logged_in_user_sql} AND usergroup='" . $_SESSION['usergroup'] . "'";
        $results = $db->query($sql);
        foreach ($results as $option_info) {

            if (is_serialized($option_info['db1234'])) {
                $option = unserialize($option_info['db1234']);
                $option = objectToArray($option);
            } else {
                $option = $option_info['db1234'];
            }
        }

        return $option;
    }
}


/*------------------------------
	// Add option
	---------------------------------*/
if (!function_exists('add_option')) {
    function add_option($option_name, $option_value)
    {
        global $db;

        if (is_array($option_value)) {
            $option_value = serialize($option_value);
        }

        $args = array("db1233" => $option_name, "db1234" => $option_value, "db1235" => "yes", 'rel_id' => $_SESSION['uid']);
        $results = $db->system_table_insert_or_update("form_profile_settings", $args);
        return $results;

    }
}


/*------------------------------
	// Update option
	---------------------------------*/
if (!function_exists('update_option')) {
    function update_option($option_name, $option_value, $add_non_existant = false, $logged_in_user = false)
    {
        global $db;

        if (is_array($option_value)) {
            $option_value = serialize($option_value);
        }

        if ($logged_in_user) {
            $logged_in_user_sql = " AND `rec_id` = {$_SESSION['uid']} ";
        } else $logged_in_user_sql = '';

        $sql = "SELECT * FROM form_profile_settings WHERE db1233='" . $option_name . "' {$logged_in_user_sql} AND usergroup='" . $_SESSION['usergroup'] . "'";

        $results = $db->query($sql);

        if (!empty($results)) {

            $args = array("db1233" => $option_name, "db1234" => $option_value, "db1235" => "yes");
            $row = $results[0];
            $args['id'] = $row['id'];
            $results = $db->system_table_insert_or_update("form_profile_settings", $args);
            return $results;
        } else {
            if ($add_non_existant) {
                return add_option($option_name, $option_value);
            }
        }
    }
}


/*------------------------------
	// Email Error Message
	-------------------------------*/
if (!function_exists('email_error_msg')) {
    function email_error_msg($error_code, $category = "Route Error found")
    {
        $emails = new Emails;
        $email_args = array(
            'to' => '<EMAIL>',
            'from' => '<EMAIL>',
            'subject' => "system error/alert logged",
            'text' => $error_code,
            'html' => $error_code,
            'category' => $category,
            'recipient_id' => random()
        );
        $emails->send($email_args);
    }
}

if (!function_exists("get_pagename")) {
    function get_pagename()
    {
        $page_name = basename(($_SERVER['PHP_SELF']), '.php');
        return $page_name;
    }
}


/*------------------------------
	// Currencies
	-------------------------------*/
if (!function_exists('get_currencies')) {
    function get_currencies()
    {
        global $db;
        $results = $db->get_rows('*', 'system_currency', [], 'country asc');

        $results_list = array();
        foreach ($results as $row) {

            $results_list[] = array(
                'id' => $row['id'],
                'country' => $row['country'],
                'currency' => $row['currency'],
                'abv' => $row['abv']
            );

        }

        return $results_list;
    }
}


/*------------------------------
	// Record Exists
	-------------------------------*/
if (!function_exists('empty_array')) {
    function empty_array($data)
    {
        foreach ($data as $k => $v) {
            if (empty($v) || $v = '' || !$v)
                unset($data[$k]);
        }

        return count($data) > 0 ? false : true;
    }
}


/*------------------------------
	// Track Use
	---------------------------------*/
if (!function_exists('track_use')) {
    function track_use($action, $track_cat = '')
    {

        $url = getRequestProtocol() . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";

        $ip_add = (stripos($_SERVER['SERVER_SOFTWARE'], "nginx") !== false) ? $_SERVER['HTTP_X_REAL_IP'] : $_SERVER['REMOTE_ADDR'];
        $timestamp = time();
//	    $url= curPageURL();
        $page_url = $url;

        $db658 = $_SESSION['usergroup'];//$_REQUEST['db658'];// user
        $db659 = $ip_add; //$_REQUEST['db659'];//ip add
        $db660 = $url;//$_REQUEST['db660'];//pageurl
        $db661 = $timestamp;//$_REQUEST['db661'];//timestamp
        $db662 = $action;//$_REQUEST['db662'];//action
        $db663 = $track_cat;//$_REQUEST['db663'];//track category

        $dbh = get_dbh();

        $sth = $dbh->prepare("INSERT INTO form_usage_tracker (username_id, rec_id, usergroup, rel_id, db658, db659, db660, db661, db662, db663)
	                          VALUES (?,?,?,?,?,?,?,?,?,?)");
        $sth->execute(array(random(), session_info("uid"), session_info("access"),
            $_GET['ref'], $db658, $db659, $db660, $db661, $db662, $db663));

        //$sth = $dbh->prepare("INSERT INTO tracker VALUES (?,?,?,?,?,?,?,?)");
        //$sth->execute(array('',$_SESSION[uid],$ip_add,$page_url,$timestamp,$action,$track_cat,$_SESSION['usergroup']));
    }
}


/*------------------------------
		// Download Send Headers. Used for when downloading CSV Files
		---------------------------------*/
if (!function_exists('download_send_headers')) {
    function download_send_headers($filename)
    {
        // disable caching
        $now = gmdate("D, d M Y H:i:s");
        header("Expires: Tue, 03 Jul 2001 06:00:00 GMT");
        header("Cache-Control: max-age=0, no-cache, must-revalidate, proxy-revalidate");
        header("Last-Modified: {$now} GMT");

        // force download
        header("Content-Type: application/force-download");
        header("Content-Type: application/octet-stream");
        header("Content-Type: application/download");

        // disposition / encoding on response body
        header("Content-Disposition: attachment;filename={$filename}");
        header("Content-Transfer-Encoding: binary");
    }
}


if (!function_exists('force_download')) {
    /**
     * Forces browser to download file
     * @param $file_path
     */
    function force_download($file_path, $content_type = "application/octet-stream")
    {
        ob_end_clean();  //  Clean (erase) the output buffer and turn off output buffering
        ob_clean();
        header('Content-Type: ' . $content_type);
        header("Content-Type: application/force-download");
        header("Content-Type: application/octet-stream");
        header("Content-Type: application/download");
        header("Content-Transfer-Encoding: Binary");
        header("Content-disposition: attachment; filename=\"" . basename($file_path) . "\"");
        header('Content-Length: ' . filesize($file_path));
        readfile($file_path);
        exit();
    }
}
/*------------------------------
		// Array to CSV
		---------------------------------*/
if (!function_exists('array2csv')) {
    function array2csv(array &$array, $filename = 'data')
    {
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename . '.csv');

        if (count($array) == 0) {
            return null;
        }
        ob_start();
        $df = fopen("php://output", 'w');
        fputcsv($df, array_keys(reset($array[0])));
        foreach ($array as $row) {
            fputcsv($df, $row);
        }
        fclose($df);
        return ob_get_clean();
    }
}


/*------------------------------
		// Unparse URL
		---------------------------------*/
function unparse_url($parsed_url)
{
    $scheme = isset($parsed_url['scheme']) ? $parsed_url['scheme'] . '://' : '';
    $host = isset($parsed_url['host']) ? $parsed_url['host'] : '';
    $port = isset($parsed_url['port']) ? ':' . $parsed_url['port'] : '';
    $user = isset($parsed_url['user']) ? $parsed_url['user'] : '';
    $pass = isset($parsed_url['pass']) ? ':' . $parsed_url['pass'] : '';
    $pass = ($user || $pass) ? "$pass@" : '';
    $path = isset($parsed_url['path']) ? $parsed_url['path'] : '';
    $query = isset($parsed_url['query']) ? '?' . $parsed_url['query'] : '';
    $fragment = isset($parsed_url['fragment']) ? '#' . $parsed_url['fragment'] : '';
    return "$scheme$user$pass$host$port$path$query$fragment";
}

/*------------------------------
		// Remove Paramter from URL
		---------------------------------*/
function removeQueryParam($url, $which_argument = false)
{
    return preg_replace('/' . ($which_argument ? '(\&|)' . $which_argument . '(\=(.*?)((?=&(?!amp\;))|$)|(.*?)\b)' : '(\?.*)') . '/i', '', $url);
}


/*------------------------------
		// BROWSER DETECTION
		// Usage:  get_browser_name($_SERVER['HTTP_USER_AGENT']);
		---------------------------------*/
if (!function_exists('get_browser_name')) {
    function get_browser_name($user_agent)
    {
        if (strpos($user_agent, 'Opera') || strpos($user_agent, 'OPR/')) return 'Opera';
        elseif (strpos($user_agent, 'Edge')) return 'Edge';
        elseif (strpos($user_agent, 'Chrome')) return 'Chrome';
        elseif (strpos($user_agent, 'Safari')) return 'Safari';
        elseif (strpos($user_agent, 'Firefox')) return 'Firefox';
        elseif (strpos($user_agent, 'MSIE') || strpos($user_agent, 'Trident/7')) return 'Internet Explorer';

        return 'Other';
    }
}


if (!function_exists('get_string_between')) {
    function get_string_between($string, $start, $end)
    {
        $string = ' ' . $string;
        $ini = strpos($string, $start);
        if ($ini == 0) return '';
        $ini += strlen($start);
        $len = strpos($string, $end, $ini) - $ini;
        return substr($string, $ini, $len);
    }
}

/*------------------------------
		// CONVERT DATABSE DATE TO uk VERSION
		---------------------------------*/
if (!function_exists('format_date')) {
    function format_date($style = 'd/m/Y', $date='')
    {
        // retrieve format from lead preferences
        $format_preference = pull_field("lead_preferences", "db50772", "WHERE usergroup='$_SESSION[usergroup]'");
        if (!empty($format_preference)) {
            if (strpos($date, '/') !== false) {
                $our_date = DateTime::createFromFormat('d/m/Y', $date)->format($format_preference);
            } else {
                $our_date = date($format_preference, strtotime($date));
            }            // if format in lead preferences is invalid falback to code format
            if ($our_date == "") {
                $our_date = date($style, strtotime($date));
            }
        } else {
            if (strpos($date, '/') !== false) {
                $our_date = DateTime::createFromFormat('d/m/Y', $date)->format($style);
            } else {
                $our_date = date($style, strtotime($date));
            }
        }
        return (strpos($our_date, '1970') !== false && strpos($date, '1970') === false) ? 'Not set' : $our_date;
    }
}

/*----------------------------------------------------
		//PREGMATCH SEARCH
		------------------------------------------------------*/
if (!function_exists('preg_match_search')) {
    function preg_match_search($string, $search_term)
    {
        //echo "$string,$search_term";
        if (strpos($string, $search_term) !== false) {
            return true;
        }

    }
}

if (!function_exists('proccess_frontend_twig_file')) {
    function proccess_frontend_twig_file($args)
    {

        global $user_info, $school_info, $settings;

        //Get TWIG
        $file = $_SERVER['DOCUMENT_ROOT'] . "/vendor/autoload.php";
        if (file_exists($file)) {
            require_once($file);
        }

        //Get the Usergroup Header if it exists
        $file = FRONTEND_PATH . 'media/' . $school_info['subdomain'] . '/' . $args['file_name'];
        if (file_exists($file)) {
            $section_html = file_get_contents($file);
        }

        //Use the default Header
        if (!$section_html) {
            $default_file = FRONTEND_PATH . 'media/default/' . $args['file_name'];
            $section_html = file_get_contents($default_file);
        }

        //Load TWIG
        $loader = new Twig\Loader\ArrayLoader(array('index' => $section_html));
        $twig = new Twig\Environment($loader, array('strict_variables' => false));

        //Create Extra Functions
        $function = new Twig\TwigFunction('front_end_url', function ($url) {
            return front_end_url($url);
        });
        $twig->addFunction($function);

        $translate_function = new Twig\TwigFunction('translate', function ($word, $lang) {
            return translate($word, $lang);
        });

        $twig->addFunction($translate_function);

        $variables = array(
            "user_info" => $user_info,
            "school_info" => $school_info,
            "settings" => $settings,
            "links" => array(),
            "session" => $_SESSION
        );

        if (count($args['additional_variables'])) {
            $variables = array_merge($variables, $args['additional_variables']);
        }

        $html = $twig->render('index', $variables);
        return $html;
    }
}
/** ===================================
 * Process Short Codes
 * ====================================  */
if (!function_exists('proccess_short_codes')) {
    function proccess_short_codes($text)
    {
//convert all &quot; and &nbsp;....


        $text = str_replace('&quot;', '"', $text);
        $text = str_replace('&nbsp;', ' ', $text);
        $file = ABSPATH . 'app/libs/shortcodes/index.php';
        if (file_exists($file)) {
            require($file);
        }
        return $text;
    }
}
/** ===================================
 * Process Email Variables
 * ====================================  */
if (!function_exists('process_email_variables')) {
    function process_email_variables($args)
    {
        dev_debug('process_email_variables' . print_r($args, 1));

        // ini_set('display_errors', 1);
        // ini_set('display_startup_errors', 1);
        // error_reporting(E_ALL);

        // echo $args['email_html'];

        //Get the School Info


        if (!empty($_SESSION['usergroup'])) {
            $schools = new Schools;
            $school_args = array('id' => $_SESSION['usergroup']);
            $school_info = $schools->get($school_args);
            $sub_domain=$school_info['subdomain'];
        }


        // check if a custom fields exists, if not then use the default first
        $external_profile_url =__DIR__.'/../../../static/'.$sub_domain."/admin/coms_custom_fields.php";// link to external admin file
        // Check if the external file exists

        // echo "<pre>".print_r($external_profile_url,1)."</pre>";

        if (file_exists($external_profile_url)) {
            include("$external_profile_url");
        }
        $user_info =[];
        if (!empty($args['user_id'])) {
            $users = new Users;
            $users_args = array("id" => $args['user_id'], 'school_id' => $_SESSION['usergroup']);
            $user_info = $users->get($users_args);
        }

        if (!empty($args['student_id'])) {
            $af = new Students;
            $form_templates = new FormTemplates;
            $page_columns = $form_templates->get_page_columns(['page' => 'applicants']);
            $page_columns[] = 'db1681';
            $page_columns[] = 'db1678';
            $page_columns[] = 'db1679';
            $page_columns[] = 'db18718';
            $applicant_args = array('id' => $args['student_id'], 'no_test_check' => true, 'columns' => $page_columns);
            if (!empty($args['include_assigned_users'])) {
                $applicant_args['include_assigned_users'] = true;
            }
            $applicant_args['events_data'] = true;
            $applicant = $af->get($applicant_args);
            //replace unique id here for use with feedback forms
            $args['email_html'] = str_replace('{{unique_id}}', $applicant['username_id'], $args['email_html']);

            $args['email_html'] = str_replace('{{student_id}}', $args['student_id'], $args['email_html']);

            $args['email_html'] = str_replace('{{current_day_of_the_week}}', date("l"), $args['email_html']);

            $applicant['admin_profile_page_url'] = engine_url('direct/proc?pg=4&vw=' . $args['student_id'] . '&ref=' . $args['student_id'] . '');
            $applicant = array_filter($applicant);


            dev_debug("applicant data to resolve" . print_r($applicant, 1));
            if (!empty($_GET['halt_ops'])) {
                echo "applicant <pre>" . print_r($applicant, 1) . "</pre>";
            }


            if (!empty($applicant['events_data'][0])) {
                foreach ($applicant['events_data'][0] as $key => $value) {
                    if (!empty(explode(" ", $key)[0])) {
                        $applicant[strtolower(explode(" ", $key)[0])] = $value;
                    }

                }
            }

            //echo "applicant <pre>".print_r($applicant,1)."</pre>";

            if (empty($args['including_defaults'])) {
                # code...
                $args['including_defaults'] = false;
            }

            $extra_fields = get_usergroup_addtional_fields($args['including_defaults']);
            dev_debug(json_encode($extra_fields));
            //echo "test <pre>".print_r($extra_fields,1)."</pre>";
            //$_SESSION['debug_mode']='yes';
            // if (isset($extra_fields[0])) {
            foreach ($extra_fields as $key => $field) {
                $db_field_name = pull_field("system_table", "db_field_name", "WHERE form_id='" . $field['db1318'] . "'");
                $db_field_type = pull_field("system_table", "type", "WHERE form_id='" . $field['db1318'] . "'");
                if ($db_field_name != "") {

                    if (!empty($_POST)) {
                        if (!empty($_POST[$db_field_name])) {
                            $args['email_html'] = str_replace($field['db1316'], str_replace("_", " ", $_POST[$db_field_name]), $args['email_html']);
                        }
                    }

                    if (isset($applicant[$db_field_name]) && $applicant[$db_field_name] != "" && !in_array($db_field_type, ['dynamic_list_group', 'countrylist'])) {
                        // to remove when new tags are live
                        //end  to remove when new tags are live
                        // new tags ticket #12996
                        // refactored by Kuda 13/07/2023
                        if (stripos($field['db1316'], "gender_") !== false) {
                            $args['email_html'] = gender_replace($field['db1316'], $applicant[$db_field_name], $args['email_html']);
                        }

                        if (strpos($field['db1316'], "_date") !== false) {
                            $applicant[$db_field_name] = notification_date_format($applicant[$db_field_name]);
                        }

                        if (!empty($field['db185702'])) {
                            $applicant[$db_field_name] = general_resolve_further_defined_fields_field($field['db185702'], $args['student_id'], $custom_field_value);
                        }

                        $args['email_html'] = str_replace($field['db1316'], str_replace("_", " ", $applicant[$db_field_name]), $args['email_html']);
                    } else {
                        // to remove when new tags are live
                        //end  to remove when new tags are live
                        // new tags ticket #12996
                        // refactored by Kuda 13/07/2023
                        if (stripos($field['db1316'], "gender_") !== false) {
                            $args['email_html'] = gender_replace($field['db1316'], general_get_data_by_field($db_field_name, $args['student_id']), $args['email_html']);
                        }

                        $custom_field_value = general_get_data_by_field($db_field_name, $args['student_id'], $field['db1318']);
                        $usergroup = $_SESSION['usergroup'] ?? 1;
                        // If preferred name tag is used and value is empty, use firstname (db39)
                        if (empty($custom_field_value) && $field['db1316'] == '{{preferred_name}}' && $field['db1317'] == '4') {
                            $custom_field_value = general_get_data_by_field("db39", $args['student_id']);
                        } elseif (empty($custom_field_value) && $field['db1316'] == '{{enquirer_preferred_name}}' && $field['db1317'] == '97') {
                            $custom_field_value = general_get_data_by_field("db1041", $args['student_id']);
                        }

                        if (strpos($field['db1316'], "_date") !== false) {
                            $custom_field_value = notification_date_format($custom_field_value);

                        }
                        // custom conditional fields resolve eg prefered name
                        if (!empty($field['db185702'])) {
                            $custom_field_value = general_resolve_further_defined_fields_field($field['db185702'], $args['student_id'], $custom_field_value);
                        }

                        $args['email_html'] = str_replace($field['db1316'], str_replace("_", " ", $custom_field_value??''), $args['email_html']??'');
                    }
                } elseif (!empty($field['db185702']) && $db_field_name == "") {
                    //echo "HERE@@@####";
                    $custom_field_value = general_resolve_further_defined_fields_field($field['db185702'], $args['student_id'], '');
                    $args['email_html'] = str_replace($field['db1316'], str_replace("_", " ", $custom_field_value), $args['email_html']);
                }
            }
            // }
        }


        //unset($_SESSION['debug_mode']);exit();
        $email_variables = array(
            "applicant" => $applicant,
            "user" => $user_info,
            "applicant_login_page_url" => $school_info['href'] . "/application/login/",
        );
        dev_debug('Email Variables:');
        dev_debug(print_r($email_variables, true));
        $file = $_SERVER['DOCUMENT_ROOT'] . "/vendor/autoload.php";
        if (file_exists($file)) {
            require_once($file);
        }
        $email_html = $args['email_html'];
        email_replace_recursive($email_html, $email_variables);
        //echo "email_html <pre>".print_r($email_html,1)."</pre>";exit();
        try {
            $loader = new Twig\Loader\ArrayLoader(array('index' => $email_html));

            $test_mode = pull_field("lead_preferences", "db62991", "WHERE usergroup=" . $_SESSION['usergroup']);
            $mode = false;
            if ("on" == $test_mode) {
                $mode = true;
            }

            $twig = new Twig\Environment($loader, array('strict_variables' => $mode));

            $email_html = $twig->render('index', $email_variables);
        } catch (Exception $e) {
            //echo $e->getMessage();

        }


        return $email_html;
    }
}

if (!function_exists('email_replace_recursive')) {
    function email_replace_recursive(&$email_html, $args)
    {
        foreach ($args as $key => $value) {
            if (is_array($value)) {
                email_replace_recursive($email_html, $value);
            }
         if(is_string($value)){
             $email_html = str_replace("{{" . $key . "}}", $value, $email_html);
         }
            //echo "key <pre>".print_r("{{".$key."}} :".$value,1)."</pre>";

        }
    }
}


if (!function_exists('gender_replace')) {
    function gender_replace($field, $value = "", $html='')
    {
        $value = preg_replace("/[^a-zA-Z0-9]+/", "", html_entity_decode(strtolower($value)));
        $gender_elements = [
            "{{gender_pronoun}}" => ["his", "her", "their"],
            "{{gender_noun}}" => ["he", "she", "them"],
            "{{gender_subject_pronoun}}" => ["he", "she", "they"],
            "{{gender_subject_pronoun_capital}}" => ["He", "She", "They"],
            "{{gender_third_person_possessive_pronoun}}" => ["his", "her", "their"],
            "{{gender_third_person_possessive_pronoun_capital}}" => ["His", "Her", "Their"],
            "{{gender_third_person_pronoun}}" => ["him", "her", "them"],
        ];
        //Early return
        if (!empty($_GET['checkvars'])) {
            echo "field <pre>" . print_r($field, 1) . "</pre>";
            echo "gender_elements<pre>" . print_r($gender_elements, 1) . "</pre>";
            echo var_dump($value);
            echo "html<pre>" . print_r($html, 1) . "</pre>";
        }
        //if (!array_key_exists($field, $gender_elements)) return $html;
        //	echo "$field - $value <br />";
        if ($value == "male") {
            //echo "here1";
            $html = str_replace($field, $gender_elements[$field][0], $html);
        } elseif ($value == "female") {
            //echo "here2";
            $html = str_replace($field, $gender_elements[$field][1], $html);
        } else {
            //echo "here3";
            $html = str_replace($field, $gender_elements[$field][2], $html);
        }


        if (!empty($_GET['checkvars'])) {
            echo "html2<pre>" . print_r($html, 1) . "</pre>";
            exit();
        }

        return $html;
    }
}

//dd/mm/YYYY(26/08/2019),YYYY/mm/dd(2019/8/26),YYYY.mm.dd(2019.8.26),YYYY-mm-dd(2019-8-26),dd_MM_YYYY(26_August_2019)
if (!function_exists('notification_date_format')) {
    function notification_date_format($date, $format = 'Y-m-d')
    {
        $format_preference = pull_field("lead_preferences", "db111512", "WHERE usergroup='$_SESSION[usergroup]'");
        switch ($format_preference) {
            case 'dd/mm/YYYY(26/08/2019)':
                $format_preference = 'd/m/Y';
                break;
            case 'YYYY/mm/dd(2019/8/26)':
                $format_preference = 'Y/m/d';
                break;

            case 'YYYY.mm.dd(2019.8.26)':
                $format_preference = 'Y.m.d';
                break;

            case 'dd_MM_YYYY(26_August_2019)':
                $format_preference = 'j F Y';
                break;
            case 'ddS_MM_YYYY(26th_August_2019)':
                $format_preference = 'jS F Y';
                break;
            default:
                $format_preference = "";
                break;
        }
        if ($format_preference != "" && $format_preference != "not specified" && validateDate($date)) {
            $our_date = date($format_preference, strtotime($date));
            // if format in lead preferences is invalid falback to code format
            if ($our_date == "") {
                $our_date = date($format, strtotime($date));
            }
        } else {
            //$our_date = date('d/m/Y', strtotime($custom_field_value));
            $our_date = $date;
        }
        //$custom_field_value=$our_date;
        return $our_date;
    }
}


if (!function_exists('validateDate')) {
    function validateDate($date, $format = 'Y-m-d')
    {
        $d = DateTime::createFromFormat($format, $date??'');
        // The Y ( 4 digits year ) returns TRUE for any integer with any number of digits so changing the comparison from == to === fixes the issue.
        return $d && $d->format($format) === $date;
    }
}


if (!function_exists('student_start_course_reminder')) {

    /**
     * @param int $usergroup School
     * @return array Logged emails
     */
    function student_start_course_reminder($usergroup, $run = 0)
    {
        global $db;
        $dbh = get_dbh();
        $query = "SELECT cs.id,
                            cs.db39 first_name,
                            cs.db40 last_name,
                            concat(cs.db39,' ',cs.db40) full_name,
                            cs.db764 email,
                            uap.date
                            from ols_user_access_plan uap
                            LEFT JOIN ols_user_progress up ON uap.rel_id = up.rel_id INNER JOIN core_students cs ON cs.id = uap.rel_id
                            WHERE up.id IS NULL AND db22031='2' AND (cs.rec_archive is null or cs.rec_archive= '')
                            AND (up.rec_archive IS NULL OR up.rec_archive = '')
                            AND (uap.rec_archive IS NULL OR uap.rec_archive = '')  AND cs.usergroup=" . $usergroup . " AND uap.date >DATE_SUB(curdate(),INTERVAL 5 WEEK) GROUP BY  cs.db764";
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $students = [];
        $query = "SELECT * FROM coms_template WHERE  usergroup='" . $usergroup . "' AND db1147 =19 LIMIT 1";
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $one_week_template = $stmt->fetch(PDO::FETCH_ASSOC);
        $query = "SELECT * FROM coms_template WHERE  usergroup='" . $usergroup . "' AND db1147 =20 LIMIT 1";
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $fourth_week_template = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($one_week_template && $fourth_week_template) {
            $emails = [];
            $schools = new Schools;
            $school_info = $schools->get(array('id' => $usergroup));
            foreach ($results as $result) {
                //figure out which template to use
                $weeks = date_diff(date_create($result['date']), date_create(date("Y-m-d H:i:s")))->format("%a") / 7;
                if ($weeks == 1) {
                    //get 1 week email template
                    $email_template = $one_week_template['db1085'];
                    $subject = $one_week_template['db1086'];
                } elseif ($weeks == 4) {
                    //get 4 week email template
                    $email_template = $fourth_week_template['db1085'];
                    $subject = $fourth_week_template['db1086'];
                } else {
                    continue;
                }

                $email_template = str_replace('{{first_name}}', $result['first_name'], $email_template);
                $email_template = str_replace('{{last_name}}', $result['last_name'], $email_template);
                $email_template = str_replace('{{full_name}}', $result['full_name'], $email_template);
                //store  email_log
                array_push($students, $email_template);
                array_push($emails, [
                    "db1153" => $result['email'],
                    "db1154" => $school_info['email_address'],
                    "db1151" => $email_template,
                    "rel_id" => $result['id'],
                    "db1150" => $email_template,
                    "db1149" => $subject,
                    "db1152" => 'alert',
                    'db1161' => 'no',
                    "usergroup" => $usergroup,
                    "username_id" => random(),
                ]);

            }
            if ($run == 1) {
                $db->insertMulti('form_email_log', $emails);
            }
            return $emails;
        } else {
            return [];
        }
    }

}

if (!function_exists('weekly_task_reminders')) {

    /**
     * Sends weekly task reminders to students with pending tasks
     * @param int $usergroup School
     * @param int $run
     * $param string $past
     * $param string $future
     * @return array
     */
    function weekly_task_reminders($usergroup, $run = 0, $past = 'yes', $future = 'no', $school_info = [])
    {
        global $db;
        $dbh = get_dbh();
        $query = "SELECT * FROM coms_template WHERE  usergroup='" . $usergroup . "' AND db1147 =18 LIMIT 1";
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $main_template = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$main_template) {
            $query = "SELECT * FROM coms_template WHERE  usergroup='1' AND db1147 =18 LIMIT 1";
            $stmt = $dbh->prepare($query);
            $stmt->execute();
            $main_template = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        if ($main_template) {
            if ($past == 'yes' && $future == 'no') {
                $date_condition = "AND db1744 < NOW()";
            } elseif ($past == 'no' && $future == 'yes') {
                $date_condition = "AND db1744 > NOW()";
            } else {
                $date_condition = "";
            }
            if (!empty($school_info['cohort'])) {
                $school_cyclecheck = " and db890=" . $school_info['cohort'] . " ";
            } else {
                $school_cyclecheck = " ";
            }

            $query = "SELECT
                        core_tasks.id AS manage,
                        db1744 AS 'date_due',
                        DATE_FORMAT(core_tasks.date, '%d/%m/%Y @%H%i') AS 'crafted',
                        DATEDIFF(db1744,NOW()) AS 'time_left',
                        db1741 AS 'title',
                        db1743 AS 'description',
                        db22441 AS 'Attached To',
                        db764 AS 'relating_to_email',
                        db39 AS 'first_name',
                        db40 AS 'surname',
                        core_tasks.usergroup AS usergroup,
                        concat(db39,' ',db40) AS 'relating_to',
                        core_students.username_id AS 'relating_to_uniqueid',
                        core_students.id AS 'relating_to_id',
                        (SELECT db106 FROM form_users WHERE id=db1745) AS 'assigned_to',
                        db22444 AS 'party_responsible',
                        db1746 AS 'status',
                        db1747 AS 'completion_date',
                        db23146 AS 'parent_task',
                        db24376 AS 'task_order',
                        db24375 AS 'completion_confirmed',
                        db37730 AS 'release_date'
                        FROM core_tasks
                        JOIN core_students ON core_students.id = core_tasks.rel_id
                        WHERE core_tasks.usergroup='$usergroup'  AND (core_students.rec_archive IS NULL OR core_students.rec_archive = '')
                        AND db1746='active' and (db47669 is null OR db47669='' or db47669=2)
                        AND db41 NOT IN ('-3','-1','-2','13','9','10')
                        AND (core_tasks.rec_archive IS NULL OR core_tasks.rec_archive = '')
                        AND db22444='Applicant'
                        $school_cyclecheck
                        $date_condition
                        ORDER BY task_order";
            dev_debug($query);
            $stmt = $dbh->prepare($query);
            $stmt->execute();
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            //group records by student ID
            $students = [];
            foreach ($results as $key => $event) {
                if (!array_key_exists($event['relating_to_id'], $students)) {
                    $students[$event['relating_to_id']] = [$key];

                } else {
                    array_push($students[$event['relating_to_id']], $key);
                }

            }
            //loop through the grouped users and build the template
            $emails = [];
            $schools = new Schools;
            foreach ($students as $student) {
                $email_template = $main_template['db1085'];
                $student_name = '';
                $student_email = '';
                $recipient_id = '';
                $school_email = '';
                $tasks_past = 0;
                $tasks_future = 0;
                $tasks_past_template = '<table style="border: 1px solid black; border-collapse: collapse;"><thead><th style="border: 1px solid black;padding: 10px;">Task</th><th style="border: 1px solid black;padding: 10px;">Due Date</th></thead><tbody>';
                $tasks_future_template = '<table style="border: 1px solid black; border-collapse: collapse;"><thead><th style="border: 1px solid black;padding: 10px;">Task</th><th style="border: 1px solid black;padding: 10px;">Due Date</th></thead><tbody>';
                foreach ($student as $key) {
                    $event = $results[$key];
                    $student_name = $event['relating_to'];
                    $first_name = $event['first_name'];
                    $surname = $event['surname'];
                    $student_email = $event['relating_to_email'];
                    $recipient_id = $event['relating_to_id'];
                    $school_email = $schools->get(array('id' => $usergroup))['email_address'];
                    if ($event["date_due"] > date("Y-m-d")) {
                        //task due to be completed where TODAY is before the task deadline
                        $tasks_future = $tasks_future + 1;
                        $tasks_future_template .= '<tr><td style="border: 1px solid black;padding: 10px;">' . $event["title"] . '</td><td style="border: 1px solid black;"padding: 10px;>' . date_format(date_create($event["date_due"]), "d/m/Y") . '</td>';
                    } else {
                        // task have yet to be completed and passed the due date deadline
                        $tasks_past = $tasks_past + 1;
                        $tasks_past_template .= '<tr><td style="border: 1px solid black;padding: 10px;">' . $event["title"] . '</td><td style="border: 1px solid black;padding: 10px;">' . date_format(date_create($event["date_due"]), "d/m/Y") . '</td>';

                    }

                }
                $tasks_past_template .= '</tbody></table>';
                $tasks_future_template .= '</tbody></table>';
                //build the overall template
                $email_template = str_replace('{{student_name}}', $student_name, $email_template);
                $email_template = str_replace('{{name}}', $student_name, $email_template);
                $email_template = str_replace('{{first_name}}', $first_name, $email_template);
                $email_template = str_replace('{{surname}}', $surname, $email_template);
                ($tasks_future > 0) ? $email_template = str_replace('{{future_tasks}}', $tasks_future_template, $email_template) : $email_template = str_replace('{{future_tasks}}', '', $email_template);
                ($tasks_past > 0) ? $email_template = str_replace('{{past_tasks}}', $tasks_past_template, $email_template) : $email_template = str_replace('{{past_tasks}}', '', $email_template);
                array_push($emails, [
                    "db1153" => $student_email,
                    "db1154" => $school_email,
                    "db1151" => $email_template,
                    "rel_id" => $recipient_id,
                    "db1150" => '',
                    "db1149" => $main_template['db1086'], //subject
                    "db1152" => 'Task Reminder ' . $event['manage'],
                    'db1161' => 'no',
                    "usergroup" => $usergroup
                ]);
            }
            if ($run == 1) {
                $db->insertMulti('form_email_log', $emails);
            }
            return $emails;
        } else {
            return [];
        }
    }
}
if (!function_exists('student_past_future_tasks')) {

    /**
     * @param int $id
     * @return array
     */
    function student_past_future_tasks($id)
    {
        global $db;
        $dbh = get_dbh();
        $events = [];
        $past_tasks = [];
        $future_tasks = [];
        $query = "SELECT
                                core_tasks.id AS manage,
                                db1744 AS 'date_due',
                                DATE_FORMAT(core_tasks.date, '%d/%m/%Y @%H%i') AS 'crafted',
                                DATEDIFF(db1744,NOW()) AS 'time_left',
                                db1741 AS 'title',
                                db1743 AS 'description',
                                db22441 AS 'Attached To',
                                core_tasks.usergroup AS usergroup,
                                db22444 AS 'party_responsible',
                                db1746 AS 'status',
                                db1747 AS 'completion_date',
                                db23146 AS 'parent_task',
                                db24376 AS 'task_order',
                                db24375 AS 'completion_confirmed',
                                db37730 AS 'release_date'
                                FROM core_tasks
                                WHERE core_tasks.rel_id='$id'
                                AND db1746='active'
                                AND (core_tasks.rec_archive IS NULL OR core_tasks.rec_archive = '')
                                AND (db22445!='on' or db22445 is null)
                                ORDER BY task_order";
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($results as $key) {

            if ($key["date_due"] > date("Y-m-d")) {
                array_push($future_tasks, ["title" => $key['title'], "due_date" => $key['due_date']]);
            } else {
                array_push($past_tasks, ["title" => $key['title'], "due_date" => $key['due_date']]);
            }

        }
        $events["past_tasks"] = $past_tasks;
        $events["future_tasks"] = $future_tasks;
        return $events;

    }
}
if (!function_exists('get_user_level_premissions')) {

    /**
     * Function to check for customer user permissions and fields to display
     * @param int $ulevel_id
     * @param boolean $admin
     * @return mixed
     */
    function get_user_level_premissions($ulevel_id, $admin = "")
    {
        global $db;
        $dbh = get_dbh();
        $students = new Students();
        $fields = $students->all_students_fields_dimensions(array('only_general_info' => 1))[0]["General Information"];
        //set checked to false
        foreach ($fields as $key => $value) {
            $fields[$key]["checked"] = 0;
        }
        if ($admin) {
            //take the nearest permission
            $query = "SELECT * FROM page_filters WHERE 1 AND usergroup='" . $_SESSION['usergroup'] . "' AND page like '%user_level_permissions%' and description !='[]' and description !='' LIMIT 1";
        } else {
            $query = "SELECT * FROM page_filters WHERE 1 AND usergroup='" . $_SESSION['usergroup'] . "' AND page='user_level_permissions_" . $ulevel_id . "' LIMIT 1";
        }
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$result) {
            //create the permission
            $db->insert("page_filters", ["usergroup" => $_SESSION['usergroup'], "title" => "User Level Permission " . $ulevel_id, "description" => json_encode([]), "page" => "user_level_permissions_" . $ulevel_id]);
            $query = "SELECT * FROM page_filters WHERE 1 AND usergroup='" . $_SESSION['usergroup'] . "' AND page='user_level_permissions_" . $ulevel_id . "' LIMIT 1";
            $stmt = $dbh->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        $decoded_permissions = json_decode($result['description']);
        if (!is_array($decoded_permissions)) {
            $decoded_permissions = [];
        }
        foreach ($fields as $key => $value) {
            if (in_array($value["id"], $decoded_permissions)) {
                $fields[$key]["checked"] = 1;
            }
        }

        return $fields;
    }
}

if (!function_exists("isAjax")) {
    //function tries to determine if a request is an ajax request
    function isAjax()
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest';
    }
}

//function strips a get parameter/s from a url
if (!function_exists("strip_param_from_url")) {
    function strip_param_from_url($url, $param)
    {
        $base_url = strtok($url, '?');              // Get the base url
        $parsed_url = parse_url($url);              // Parse it
        $query = $parsed_url['query'];              // Get the query string
        parse_str($query, $parameters);           // Convert Parameters into array
        unset($parameters[$param]);               // Delete the one you want
        $new_query = http_build_query($parameters); // Rebuilt query string
        return $base_url . '?' . $new_query;            // Finally url is ready
    }
}

// ------------------------------------------------------------------------

if (!function_exists('url_title')) {
    /**
     * Create URL Title
     *
     * Takes a "title" string as input and creates a
     * human-friendly URL string with a "separator" string
     * as the word separator.
     *
     * @param string $str Input string
     * @param string $separator Word separator
     *          (usually '-' or '_')
     * @param bool $lowercase Whether to transform the output string to lowercase
     * @return  string
     * @todo    Remove old 'dash' and 'underscore' usage in 3.1+.
     */
    function url_title($str, $separator = '-', $lowercase = FALSE)
    {
        if ($separator === 'dash') {
            $separator = '-';
        } elseif ($separator === 'underscore') {
            $separator = '_';
        }

        $q_separator = preg_quote($separator, '#');

        $trans = array(
            '&.+?;' => '',
            '[^\w\d _-]' => '',
            '\s+' => $separator,
            '(' . $q_separator . ')+' => $separator
        );

        $str = strip_tags($str);
        foreach ($trans as $key => $val) {
            $str = preg_replace('#' . $key . '#i', $val, $str);
        }

        if ($lowercase === TRUE) {
            $str = strtolower($str);
        }

        return trim(trim($str, $separator));
    }
}

if (!function_exists('num_to_char')) {
    function num_to_char($num)
    {
        $map = ['c', 'z', 'm', 'p', 'v', 'y', 'x', 'f', 'q', 'r'];
        $str = strval($num);
        $arr = str_split($str);
        $chars = '';
        foreach ($arr as $v) {
            if (isset($map[$v])) $chars .= $map[$v];
        }
        return $chars;
    }
}
if (!function_exists('char_to_num')) {
    function char_to_num($chars)
    {
        $map = ['c' => '0', 'z' => '1', 'm' => '2', 'p' => '3', 'v' => '4', 'y' => '5', 'x' => '6', 'f' => '7', 'q' => '8', 'r' => '9'];
        $str = strval($chars);
        $arr = str_split($str);
        $nums = '';
        $success = TRUE;
        foreach ($arr as $v) {
            if (isset($map[$v])) $nums .= $map[$v];
            else {
                $success = FALSE;
                break;
            }
        }
        if ($success) return intval($nums);
        else return FALSE;
    }
}

// ------------------------------------------------------------------------

if (!function_exists('redirect')) {
    /**
     * Header Redirect
     *
     * Header redirect in two flavors
     * For very fine grained control over headers, you could use the Output
     * Library's set_header() function.
     *
     * @param string $uri URL
     * @param string $method Redirect method
     *            'auto', 'location' or 'refresh'
     * @param int $code HTTP Response status code
     * @return    void
     */
    function redirect($uri = '', $method = 'auto', $code = NULL)
    {
        if (!preg_match('#^(\w+:)?//#i', $uri)) {
            $uri = base_url($uri);
        }

        // IIS environment likely? Use 'refresh' for better compatibility
        if ($method === 'auto' && isset($_SERVER['SERVER_SOFTWARE']) && strpos($_SERVER['SERVER_SOFTWARE'], 'Microsoft-IIS') !== FALSE) {
            $method = 'refresh';
        } elseif ($method !== 'refresh' && (empty($code) or !is_numeric($code))) {
            if (isset($_SERVER['SERVER_PROTOCOL'], $_SERVER['REQUEST_METHOD']) && $_SERVER['SERVER_PROTOCOL'] === 'HTTP/1.1') {
                $code = ($_SERVER['REQUEST_METHOD'] !== 'GET')
                    ? 303    // reference: http://en.wikipedia.org/wiki/Post/Redirect/Get
                    : 307;
            } else {
                $code = 302;
            }
        }

        switch ($method) {
            case 'refresh':
                header('Refresh:0;url=' . $uri);
                break;
            default:
                header('Location: ' . $uri, TRUE, $code);
                break;
        }
        exit;
    }
}

// ------------------------------------------------------------------------

if (!function_exists('anchor')) {
    /**
     * Anchor Link
     *
     * Creates an anchor based on the local URL.
     *
     * @param string    the URL
     * @param string    the link title
     * @param mixed    any attributes
     * @return    string
     */
    function anchor($uri = '', $title = '', $attributes = '')
    {
        $title = (string)$title;

        $site_url = is_array($uri)
            ? base_url($uri)
            : (preg_match('#^(\w+:)?//#i', $uri) ? $uri : base_url($uri));

        if ($title === '') {
            $title = $site_url;
        }

        if ($attributes !== '') {
            $attributes = _stringify_attributes($attributes);
        }

        return '<a href="' . $site_url . '"' . $attributes . '>' . $title . '</a>';
    }
}

if (!function_exists('_stringify_attributes')) {
    /**
     * Stringify attributes for use in HTML tags.
     *
     * Helper function used to convert a string, array, or object
     * of attributes to a string.
     *
     * @param mixed    string, array, object
     * @param bool
     * @return    string
     */
    function _stringify_attributes($attributes, $js = FALSE)
    {
        $atts = NULL;

        if (empty($attributes)) {
            return $atts;
        }

        if (is_string($attributes)) {
            return ' ' . $attributes;
        }

        $attributes = (array)$attributes;

        foreach ($attributes as $key => $val) {
            $atts .= ($js) ? $key . '=' . $val . ',' : ' ' . $key . '="' . $val . '"';
        }

        return rtrim($atts, ',');
    }
}


if (!function_exists('floating_info')) {
    function floating_info($ref, $override = '')
    {
        if ($override !== '') {
            return $override;
        } else {

            if (empty($_GET['ref'])) {
                return $_SESSION['application_id'];
            } else {
                return sanitise($_GET['ref']);
            }
        }
    }
}

if (!function_exists('get_internal_status_names')) {
    /**
     * Get ticket internal status names.
     *
     * @param none
     * @return      associative array
     */
    function get_internal_status_names()
    {
        /* discarded this section as fields.php was throwing errors of it`s own.
          require_once('../admin/app/models/fields.php');
          $args =  [ 'db2_field_name'=>'db37375', 'table_name'=>'form_support_ticket' ];
          $fields = new Fields();
          $rows = $fields->get($args);
      */

        $pdo = get_dbh();
        $sql = "SELECT DISTINCT db37375 as status FROM form_support_ticket WHERE db37375 IS NOT NULL";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $rows = $stmt->fetchAll();
        dev_debug($sql);
        if (is_array($rows) && !empty($rows)) {
            foreach ($rows as $key => $value) {
                $row = trim($value['status']);
                if (empty($row)) unset($rows[$key]);
            }
            return $rows;
        }
        return [];
    }
}

if (!function_exists('ReportDateDropdown')) {
////////////////////////////////
// REPORT DATE DROPDOWN
/////////////////////////////////
    function ReportDateDropdown($year_interval, $year_interval_type, $field_name, $db_date = '', $class = 'instruction_box_blue')
    {
        // set up vars
        $year_interval_type = strtolower($year_interval_type);

        if (!empty ($class))
            $class = " $class";


        // now work out which date to display
        // if (!empty ($db_date) and intval (str_replace ('-', '',$db_date)) > 0)
        if (!empty ($db_date) and intval($db_date) > 0) {
            $formatted_db_date = strtotime($db_date);

            $year = date('Y', $formatted_db_date);
            $month = date('n', $formatted_db_date);
            $day = date('j', $formatted_db_date);
        } else {
            $year = date("Y");
            $month = '01';
            $day = '01';
        }


        // output the day
        echo "<select class=\"formbkg$class\" name=\"{$field_name}_day\" id=\"{$field_name}_day\" onchange=\"updateDateField('$field_name');showUser(this.value)\">\r\n";

        for ($day_i = 0; $day_i <= 31; ++$day_i) {
            $day_value = sprintf('%02d', $day_i);
            $day_text = ($day_i > 0 ? $day_value : '');
            $day_selected = ($day_value == $day ? ' selected="selected"' : '');

            echo "<option value=\"$day_value\"$day_selected>$day_text</option>\r\n";
        }

        echo '</select>' . "\r\n";


        // separator
        echo ' / ';


        // output the month
        echo "<select class=\"formbkg$class\" name=\"{$field_name}_month\" id=\"{$field_name}_month\" onchange=\"updateDateField('$field_name');showUser(this.value)\">\r\n";

        for ($month_i = 0; $month_i <= 12; ++$month_i) {
            $month_value = sprintf('%02d', $month_i);
            $month_text = ($month_i > 0 ? $month_value : '');
            $month_selected = ($month_value == $month ? ' selected="selected"' : '');

            echo "<option value=\"$month_value\"$month_selected>$month_text</option>\r\n";
        }

        echo '</select>' . "\r\n";


        // separator
        echo ' / ';


        // output the year
        echo "<select class=\"formbkg$class\" name=\"{$field_name}_year\" id=\"{$field_name}_year\" onchange=\"updateDateField('$field_name');showUser(this.value)\">\r\n";

        $current_year = date('Y');

        if ($year_interval_type === 'past') {
            $year_i_start = $current_year - $year_interval;
            $year_i_end = $current_year;
        } else if ($year_interval_type === 'future') {
            $year_i_start = $current_year;
            $year_i_end = $current_year + $year_interval;
        } else if ($year_interval_type === 'both') {
            $year_i_start = $current_year - $year_interval;
            $year_i_end = $current_year + $year_interval;
        }

        echo '<option value="0000"></option>' . "\r\n";

        for ($year_i = $year_i_start; $year_i <= $year_i_end; $year_i += 1) {
            $year_value = $year_i;
            $year_text = $year_i;
            $year_selected = ($year_value == $year ? ' selected="selected"' : '');

            echo "<option value=\"$year_value\"$year_selected>$year_text</option>\r\n";
        }

        echo '</select>' . "\r\n";

        // now output the hidden form input
        //YYYY-MM-DD HH:MM:SS
        echo "</select><input id=\"$field_name\" name=\"$field_name\" type=\"hidden\" value=\"$year-$month-$day\" class=\"filter\" onchange=\"showUser(this.value)\">\n";
    }
}

if (!function_exists('list_of_data')) {
    /**
     * /*--------------------------------------------------
     * // LIST OF ENTIRES
     * ---------------------------------------------------- */
    function list_of_data($sql, $field, $title = '', $add_other_option = false)
    {

        global $check_usergroup, $pre_select; // usergroup is checked by default. this can kill it

        $dbh = get_dbh();

        if ($check_usergroup !== 'no') {
            // if admin show everything not just group
            $usergroups = usergroups_management();
            //make sure all sqls only show the logged usergroup
            $sql = str_replace("WHERE", " WHERE $usergroups AND", $sql);
            //echo $sql;
        }
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $options = "";

        foreach ($results as $row) {

            $field_name = str_replace("_", " ", $row[$field]);
            if ($pre_select == $field_name && $field_name !== '' && $pre_select !== '') {
                $selected = "selected=\"selected\"";
            }
            if ($title !== '') {
                echo "<OPTION VALUE=\"$field_name\" $selected>$row[$title]</option>";
            } else {
                echo "<OPTION VALUE=\"$field_name\" $selected>" . $field_name . "</option>";
            }
            //resdet
            $selected = '';
        }
        $title = '';

        // check if other should be added at the bottom
        if ($add_other_option) {
            echo "<OPTION VALUE=\"0\" $selected>Other</option>";
        }

    }
}
if (!function_exists('json_list_of_forms')) {
    /**
     * /*--------------------------------------------------
     * // LIST OF ENTIRES
     * ---------------------------------------------------- */
    function json_list_of_forms($field_name, $pre_select)
    {
        // get global tag if any
        global $conditional_tag, $add_other_option;

        $dbh = get_dbh();
        $sth = $dbh->prepare("SELECT * FROM system_pages WHERE project!='1'");
        $sth->execute();

        $options = [];

        array_push($options, ["value" => null, "title" => "Select"]);

        while ($row = $sth->fetch()) {
            $val = $row['page_name'];// name of view
            $val_id = $row['page_id'];// name of view
            array_push($options, ["value" => $val_id, "title" => $val]);
        }
        // check if other should be added at the bottom
        if ($add_other_option) {
            array_push($options, ["value" => "0", "title" => "Other"]);
        }
        return $options;
    }
}
/////////////////////////////
// FIND SLUG BASED ON VALUE IN URL
/////////////////////////////
if (!function_exists('find_slug')) {
    function find_slug($place = 1, $page_name = '')
    {
        global $checkSlug;

        $checkDir = $checkSlug ? count($checkSlug) : 0; // count elements
        $n = $checkDir - $place; // find last element
        $findSlug = $checkSlug[$n]; // isolate the last part of the url

        $slug = str_replace("$page_name.php?id=", "", $findSlug); // isolate the slug
        //error_log(" :: n = ".$n."place = ".$place." find slug = ".$findSlug." final slug returned = ".$slug);
        return $slug;
    }
}

if (!function_exists('get_internal_status_names')) {
    /**
     * Get ticket internal status names.
     *
     * @param none
     * @return      associative array
     */
    function get_internal_status_names()
    {
        /* discarded this section as fields.php was throwing errors of it`s own.
          require_once('../admin/app/models/fields.php');
          $args =  [ 'db2_field_name'=>'db37375', 'table_name'=>'form_support_ticket' ];
          $fields = new Fields();
          $rows = $fields->get($args);
      */

        $pdo = get_dbh();
        $sql = "SELECT DISTINCT db37375 as status FROM form_support_ticket WHERE db37375 IS NOT NULL";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $rows = $stmt->fetchAll();
        dev_debug($sql);
        if (is_array($rows) && !empty($rows)) {
            foreach ($rows as $key => $value) {
                $row = trim($value['status']);
                if (empty($row)) unset($rows[$key]);
            }
            return $rows;
        }
        return [];
    }
}
if (!function_exists('dynamic_archive_record')) {
    /**
     * Get ticket internal status names.
     *
     * @param none
     * @return      associative array
     */
    function dynamic_archive_record($args)
    {
        /** ===================================
         * Archive Record
         * ====================================    */
        global $db;
        $archive = (isset($args['unarchive']) && $args['unarchive'] == true) ? 'null' : $_SESSION['uid'];
        $sql = "update $args[table] set client_archive = $archive where id = $args[id]";
        dev_debug($sql);
        $result = $db->rawQuery($sql);
        return $result;
    }
}

///////////////////////////////
//LOG All notifications
////////////////////////////////
if (!function_exists('log_notification')) {
    function log_notification($rel_id, $message = '', $read_by = '', $origin='')
    {
        /***************** GRAB POSTS/GETS*****************/
        $db39395 = sanitise($origin);///actual table notification sits in.
        $db39486 = sanitise($message);///actual body of the notification.
        $db39485 = sanitise($tagged);///actual users tagged on the notification.
        $db39484 = sanitise($read_by);

//only add if form_id exists
        if ($db39395 && $rel_id) {

            $_con = get_dbh();
            $query = "SELECT COUNT(*) as count FROM form_notification_logs WHERE usergroup='" . session_info("usergroup") . "' AND rel_id='" . $rel_id . "' AND db39395 =\"$origin\"";
            $th = $_con->prepare($query);
            $th->execute();
            $result = $th->fetchColumn();
            track_use($query);
            if (!(bool)$result) {
                /***************** INSERT FUNCTION *****************/
                $sql = "INSERT INTO form_notification_logs(username_id, rec_id, usergroup, rel_id, db39395, db39486 , db39485, db39484) VALUES ('" . random() . "', '" . session_info('uid') . "', '" . session_info("usergroup") . "', :rel_id , :origin, :message, :tagged, :read_by)";
                track_use($sql);
                $th_insert = $_con->prepare($sql);
                return $th_insert->execute(array(
                    'rel_id' => $rel_id,
                    'origin' => $db39395,
                    'message' => $db39486,
                    'tagged' => $db39485,
                    'read_by' => $db39484
                ));
            }

        }// end if email exists
        else {
            track_use("Location of notification required $db339395");
            return array('success' => true);
        }

        //echo '<div class="success">Your email has been logged and is ready to send</div>';
    }
}

if (!function_exists('mark_as_read_notifications')) {
    function mark_as_read_notifications($objects = false)
    {
        foreach ($objects as $key => $value) {
            mark_as_read_notification($value);
        }
    }
}
if (!function_exists('mark_as_read_notification')) {
    function mark_as_read_notification($params = false)
    {

        if (!$params)
            return false;
        $con = get_dbh();
        $table = '';
        switch ($params['type']) {
            case 'general':
                $table = "form_file";
                break;
            case 'priority':
                $table = "core_tasks";
                break;
            case 'applicant_tasks_due_today':
                $table = "core_tasks";
                break;
            case 'applicant_tasks_due_tommorrow':
                $table = "core_tasks";
                break;
            case 'applicant_tasks_over_due':
                $table = "core_tasks";
                break;
            case 'reminders':
                $table = "core_tasks";
                break;
            case 'tagged':
                $table = "dir_internal_notes";
                break;
            case 'payment':
                $table = "sis_student_fees";
                break;
            case 'enquirychat':
                $table = "lead_chats";
                break;
            case 'admin_task_replies':
                $table = "core_task_notes";
                break;
            case 'my_tasks_replies':
                $table = "core_task_notes";
                break;
            case 'applicant_task_replies':
                $table = "core_task_notes";
                break;
            case 'task_replies':
                $table = "core_task_notes";
                break;
            case 'tutor_comms':
                $table = "sis_tutor_comms";
                break;
            case 'task_notes':
                $table = "core_task_notes";
                break;
            default:
                $table = 'core_notes';
                break;
        }
        $args = array(
            'rel_id' => $params['id'],
            'origin' => $table
        );
        if ("form_file" == $table) {
            $select = $con->prepare("SELECT id,db39484 FROM form_notification_logs WHERE rel_id  in (" . $params['id'] . ") AND db39395 ='$table'");
            $select->execute();
        } else {
            $select = $con->prepare("SELECT id,db39484 FROM form_notification_logs WHERE rel_id =:rel_id AND db39395 =:origin");
            $select->execute($args);
        }

        $column = $select->fetch(PDO::FETCH_OBJ);
        if ($column->id) {

            $args['reader_id'] = $_SESSION['uid'];
            if ($column->db39484 != "") {
                $args['reader_id'] = $column->db39484 . "," . $args['reader_id'];
            }
            //$args['reader_id'] = $_SESSION['uid'];
            if ("form_file" == $table) {

                $sth = $con->prepare("UPDATE form_notification_logs SET db39484 = '" . $args['reader_id'] . "' WHERE rel_id  in (" . $params['id'] . ") AND db39395 ='$table'");
                if ($sth->execute()) {
                    return $args;
                }
            } else {
                if ("core_notes" == $table) {
                    $sth = $con->prepare("UPDATE core_notes SET db139 = :reader_id WHERE id =:rel_id");
                    $sth->execute(array(
                        'reader_id' => $_SESSION['uid'],
                        'rel_id' => $params['id']

                    ));
                }
                $sth = $con->prepare("UPDATE form_notification_logs SET db39484 = :reader_id WHERE rel_id =:rel_id AND db39395 =:origin");
                if ($sth->execute($args)) {
                    return $args;
                }
            }
        } else {
            if ("form_file" != $table) {
                return log_notification($params['id'], '', $_SESSION['uid'], $table);
            }
        }
    }
}

if (!function_exists('database_error_matrix')) {
    function database_error_matrix($params = array())
    {
        $emails = new Emails;
        $q = "SELECT COUNT(id) as count from form_database_error_logs WHERE form_database_error_logs.date >= NOW() - INTERVAL 1 DAY";
        $con = get_dbh();
        $th = $con->prepare($q);
        $th->execute();
        $count = $th->fetchColumn();
        $results = array('count' => $count, 'class' => 'badge badge-default badge-top');
        switch ($count) {
            case ($count < 25):
                $results['class'] = 'badge badge-default badge-top';
                break;
            case ($count < 50):
                $results['class'] = 'badge badge-success badge-top';
                break;
            case ($count < 100):
                $results['class'] = 'badge badge-warning badge-top';
                break;
            default:
                $results['class'] = 'badge badge-danger badge-top';
                break;
        }
        return $results;
    }
}

if (!function_exists('get_core_students')) {
    /*--------------------------------
	// FUNCTION TO get_core_students
	---------------------------------*/
    function get_core_students($id)
    {
        $dbh = get_dbh();
        $sql = "SELECT *,
		db889 AS db889_original,
	    (SELECT db232 FROM core_courses WHERE id=db889) AS db889,
		(SELECT db1681 FROM dir_cohorts WHERE id=db1682) AS db1682
	    FROM core_students WHERE id=?";

        // if admin show everything not just group
        $usergroups = usergroups_management();
        //make sure all charts only show the logged usergroup
        $sql = str_replace("WHERE", "WHERE $usergroups AND", $sql);

        $sth = $dbh->prepare($sql);
        $sth->execute(array($id));

        while ($row = $sth->fetch()) {
            $core_students_id = $row['id'];
            $unique_id = $row['username_id'];
            $core_students_rec_id = $row['rec_id'];
            $core_students_usergroup = $row['usergroup'];
            $core_students_rel_id = $row['rel_id'];
            $core_students_deleted = $row['rec_archive'];
            $core_students_first_name = $row['db39'];
            $core_students_middle_name = $row['db46'];
            $core_students_surname = $row['db40'];
            $core_students_email_address = $row['db764'];
            $core_students_telephone_number = $row['db765'];
            $core_students_date_of_birth = checkIsAValidDate($row['db53']) ? $row['db53'] : '';
            $core_students_gender = $row['db44'];
            $core_students_source_of_applicant = $row['db510'];
            $core_students_cohort = $row['db890'];
            $core_students_cohort_intake = $row['db1682'];
            $core_students_course_of_study = $row['db889'];
            $core_students_level_of_entry = $row['db50'];
            $core_students_country_of_origin = $row['db763'];
            $core_students_application_status = $row['db41'];
            $core_students_has_applied = $row['db888'];
            $core_students_archive_record = $row['db135'];
            $core_student_application_route = $row['db2280']; // route applicant is attched to
            $core_student_application_ucas = $row['db1173']; // ucas invite
            $core_student_ucas_pass = $row['db1105']; // ucas pass
            $core_students_course_of_study_id = $row['db889_original'];
            $core_students_course_audio = $row['db25618'];
            $core_students_length_of_study = $row['db30665'];
            $core_students_mobile_number = $row['db28467'];
        }


        return array($core_students_id, $core_students_rec_id, $core_students_usergroup, $core_students_rel_id, $core_students_first_name, $core_students_middle_name, $core_students_surname, $core_students_email_address, $core_students_telephone_number, $core_students_date_of_birth, $core_students_gender, $core_students_source_of_applicant, $core_students_cohort, $core_students_course_of_study, $core_students_level_of_entry, $core_students_country_of_origin, $core_students_application_status, $core_students_has_applied, $core_students_archive_record, $unique_id, $core_student_application_route, $core_students_cohort_intake, $core_students_deleted, $core_student_application_ucas, $core_student_ucas_pass, $core_students_course_of_study_id, $core_students_course_audio, $core_students_length_of_study, $core_students_mobile_number);
    }

}

if (!function_exists('checkIsAValidDate')) {

    //function checks if a date is valid
    function checkIsAValidDate($myDateString)
    {
        if ($myDateString == "0000-00-00") {
            return false;
        } else {
            return (bool)strtotime($myDateString);
        }
    }

}

if (!function_exists('relative_date')) {

//Relative Date Function
    function relative_date($time)
    {

        $today = strtotime(date('M j, Y'));

        $reldays = ($time - $today) / 86400;

        if ($reldays >= 0 && $reldays < 1) {

            return 'Today';

        } else if ($reldays >= 1 && $reldays < 2) {

            return 'Tomorrow';

        } else if ($reldays >= -1 && $reldays < 0) {

            return 'Yesterday';

        }

        //if (abs($reldays) < 7) {

        if ($reldays > 0) {

            $reldays = floor($reldays);

            return 'In ' . $reldays . ' day' . ($reldays != 1 ? 's' : '');

        } else {

            $reldays = abs(floor($reldays));

            return $reldays . ' day' . ($reldays != 1 ? 's' : '') . ' ago';

        }

        //}

        if (abs($reldays) < 182) {

            return date('l, j F', $time ? $time : time());

        } else {

            return date('l, j F, Y', $time ? $time : time());

        }

    }
}


if (!function_exists('track_application_stages')) {


    function track_application_stages($stage, $candidate_id)
    {

        $dbh = get_dbh();

        /***************** CHECK FOR DUPES *****************/
        $num = pull_field("dir_stage_tracker ", "db1142", "WHERE usergroup='$_SESSION[usergroup]' AND db1142='$stage' AND  rel_id='$candidate_id' ORDER by id DESC LIMIT 1");

        if ($num !== "$stage") {
            /***************** INSERT FUNCTION *****************/
            $sql = "INSERT INTO dir_stage_tracker (username_id, rec_id, usergroup, rel_id, db1142) VALUES ('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '" . $candidate_id . "', '$stage')";
            $stmt = $dbh->prepare($sql);
            $stmt->execute();
        }

    }

}

if (!function_exists('update_application_stage')) {

    function update_application_stage($stage, $candidate_id, $update_stage = "")
    {
        if ($update_stage == 1) {
            $dbh = get_dbh();
            $sql = "UPDATE core_students SET db41='$stage' WHERE id='$candidate_id'";
            track_use($sql);
            dev_debug($sql);
            $stmt = $dbh->prepare($sql);
            $stmt->execute();
            mysqli_query($dbcon, $sql);
            if ($stmt->errorInfo()[2])
                error_log('mysql error: ' . $stmt->errorInfo()[2]);

            // insert tracker
            track_application_stages($stage, $candidate_id);
        }

    }
}

if (!function_exists('sentenceCase')) {
    function sentenceCase($string)
    {
        $sentences = preg_split('/([.?!]+)/', $string, -1, PREG_SPLIT_NO_EMPTY | PREG_SPLIT_DELIM_CAPTURE);
        $newString = '';
        foreach ($sentences as $key => $sentence) {
            $newString .= ($key & 1) == 0 ?
                ucfirst(strtolower(trim($sentence))) :
                $sentence . ' ';
        }
        return trim($newString);
    }
}

if (!function_exists('filled_percentage_on_new_forms')) {

    function filled_percentage_on_new_forms($core_students_id, $additionalFiles)
    {

        ini_set('memory_limit', '512M');  //increase size needed for fetch all below
        $preference_with_files = pull_field('lead_preferences', 'db40620', "WHERE usergroup='$_SESSION[usergroup]'");
        if ($_SESSION['ulevel'] == 4) {
            $preference_with_files = str_replace('required_supplementary_form_fields', '', $preference_with_files);
        }
        $supplementary_required = false;
        $forms_category = "AND form_cms.db656='forms'";
        if (strpos($preference_with_files, 'required_supplementary_form_fields') !== false) {
            $supplementary_required = true;
            $forms_category = "AND (form_cms.db656='forms' OR form_cms.db656='supplementary_forms')";
        }
        $pages_ids = get_page_list_based_on_rules($core_students_id, $supplementary_required); //get the pages that we shoudl check for required fields

        ///concat(db19263,',',IFNULL(db33769,''))

        $dbh = get_dbh();
        //Get all the fields required
        $required_fields_sql = "
				SELECT *
				FROM system_form_fields
					LEFT JOIN system_forms ON system_forms.id = system_form_fields.system_forms_id
					LEFT JOIN system_table ON system_form_fields.system_table_id = system_table.form_id
					LEFT JOIN system_pages ON system_pages.page_id = system_table.pg_id
					LEFT JOIN system_cat ON system_cat.sys_cat_id = system_pages.project
					INNER JOIN form_cms ON form_cms.db26231 = system_forms.id
				WHERE
					system_table.required IN ('required','yes')
					AND system_form_fields.usergroup='" . $_SESSION['usergroup'] . "'
					AND form_cms.id IN ($pages_ids)
					AND locked!=9
					$forms_category
				ORDER BY system_table.pg_id
				";

        dev_debug('filled_percentage_on_new_forms = ' . $required_fields_sql);

        $system_table_names = array();
        $sth = $dbh->prepare($required_fields_sql);
        $sth->execute();
        $results = $sth->fetchAll(PDO::FETCH_ASSOC);

        foreach ($results as $field) {
            $i++;

            $table_name = $field['sys_cat_abv'] . "_" . $field['page_name'];

            if (!array_key_exists($table_name, $system_table_names)) {
                $i = 0;
                $system_table_names[$table_name] = array(
                    'title' => $table_name,
                    'id' => $field['pg_id'],
                    'page_name' => $field['db647'],
                    'fields' => array()
                );
            }
            $system_table_names[$table_name]['fields'][$i] = $field;
        }

        $required_filled = 0;
        $required_fields = 0;

        foreach ($system_table_names as $table) {
            //get the table data
            $student_data_sql = "SELECT * FROM " . $table['title'] . " WHERE rel_id='" . $core_students_id . "' AND usergroup='" . $_SESSION['usergroup'] . "' AND  (rec_archive IS NULL OR rec_archive = '') ORDER BY id ASC LIMIT 1";

            $sth = $dbh->prepare($student_data_sql);
            $sth->execute();
            $student_data = $sth->fetchAll(PDO::FETCH_ASSOC);

            #dev_debug("table-fields = ".json_encode($table));
            #dev_debug("student_data = ".json_encode($student_data));

            //check the fields
            $i = 0;
            foreach ($table['fields'] as $field) {

                //all required
                if ($student_data[0][$field['db_name']] || $student_data[0][$field['db_name']] == '' || $student_data[0][$field['db_name']] == 'not_specified' || $student_data[0][$field['db_name']] == 'not specified' || $student_data[0][$field['db_name']] == '0000-00-00') {
                    $required_fields += 1;
                }

                //all missing
                if (!$student_data[0][$field['db_name']] || $student_data[0][$field['db_name']] == '' || $student_data[0][$field['db_name']] == 'not_specified' || $student_data[0][$field['db_name']] == 'not specified' || $student_data[0][$field['db_name']] == '0000-00-00') {
                    $required_missing += 1;
                }

                #echo '<div class="hidden">student_data '.$i.$field['db_name'].'= **'.$student_data[0][$field['db_name']].'**</br></div>';
                $i++;

            }

        }

        $required_filled = ($required_fields - $required_missing);
        dev_debug("filled_percentage_on_new_forms = '.$required_fields.' - '.$required_filled");

        if (strpos($preference_with_files, 'required_files') !== false) {
            //START From applicant side
            //try the required files
            $must_upload_files = required_file_rules($core_students_id);

            list ($have_uploaded_ids, $have_uploaded_desc) = explode("||", pull_field("form_file", "concat(group_concat(id),'||',group_concat(db200))", "WHERE rel_id='$core_students_id' AND (rec_archive IS NULL OR rec_archive = '') and db202!='archived'"));
            $have_uploaded = explode(",", $have_uploaded_desc);
            //$have_uploaded_ids_array = explode(",",$have_uploaded_ids);
            $files_they_should_have_uploaded = explode(",", pull_field("form_file_category", "group_concat(id,'|',db743)", "WHERE id IN($must_upload_files) AND usergroup='$_SESSION[usergroup]'"));
            //          $result = implode(",",array_diff($must_upload_files, $have_uploaded)); // get odd ones out
            $uploaded = 0;

            foreach ($files_they_should_have_uploaded as $must_file_names) {
                //breakup the result
                $must_file_names = explode("|", $must_file_names);
                //check if upload was done
                if (in_array($must_file_names[0], $have_uploaded)) {
                    $uploaded++;
                }
            }
            $required_fields += count($files_they_should_have_uploaded);
            $required_filled += $uploaded;
            //END From applicant side
        }

        if (!empty($additionalFiles)) {
            if ($additionalFiles['requiredFiles'] >= $additionalFiles['uploadedFiles']) {
                $required_fields = $required_fields + $additionalFiles['requiredFiles'];
            } else {
                $required_fields = $required_fields + $additionalFiles['uploadedFiles'];
            }
            $required_filled = $required_filled + $additionalFiles['uploadedFiles'];
        }


        //update applicant DB with new filled %
        $filled_percentage_amt = round(100 * $required_filled / $required_fields, 0, PHP_ROUND_HALF_DOWN);
        $update = $dbh->prepare("UPDATE core_students SET db33597 = '$filled_percentage_amt' WHERE id =$core_students_id AND usergroup = $_SESSION[usergroup] LIMIT 1");
        $update->execute();
        #	dev_debug("UPDATE core_student SET db33597 = '$required_filled' WHERE id =$core_students_id LIMIT 1");

        return array($required_fields, $required_filled);

    }//END FUNCTION
}


if (!function_exists('filled_percentage')) {

    function filled_percentage($core_students_id)
    {
        $dbh = get_dbh();

        global $form_fields;//

        $preference_with_files = pull_field('lead_preferences', 'db40620', "WHERE usergroup='$_SESSION[usergroup]'");
        $supplementary_required = false;
        if ($_SESSION['ulevel'] == 4) {
            $preference_with_files = str_replace('required_supplementary_form_fields', '', $preference_with_files);
        }
        if (strpos($preference_with_files, 'required_supplementary_form_fields') !== false) {
            $supplementary_required = true;
        }

        //check if this page should be checked
        if ($form_fields) {
            //custom pages to check
            $ugs = explode(',', $form_fields);
        } else {
            //standard pages to check
            $page_rules_values = get_page_list_based_on_rules($core_students_id, $supplementary_required);
            if (isset($page_rules_values) && $page_rules_values !== '') {
                //get the pages fromthe page rules if they exist
                $active_form_values = get_forms_list_based_on_rules($page_rules_values, 'db655');
                $ugs = explode(",", $active_form_values);
            } else {
                //get standard stock
                $ugs = array(48, 55, 69, 70, 71, 72, 73, 74, 75, 118, 208, 213, 218, 229, 307, 229, 307, 439);
            }

        }

        // create array page_id -> page_name
        $sql = "SELECT page_id, page_name, sys_cat_abv FROM system_pages
			inner join system_cat ON system_cat.sys_cat_id = system_pages.project WHERE page_id IN (" . implode(',', $ugs) . ")";
        //echo "incomplete - $sql";
        $pg_name = $sql; //used fo debugging
        $stmt = $dbh->prepare($sql);
        $stmt->execute();

        while (list($page_id, $page_name, $sys_cat_abv) = $stmt->fetch(PDO::FETCH_ASSOC))
            $page_names[$page_id] = "$sys_cat_abv" . "_$page_name";;

        // Are we dealing with one of the registration tables?
        $ug_check = in_array($option4, $ugs);
        // $select_single_field - set in pages that only need to see one  field

        // Get the number of required fields and required filled fields
        $required_fields = 0;
        $required_filled = 0;
        // loop over registration stages
        foreach ($ugs as $i) {
            $sql = "SELECT system_table.db_field_name AS field_name
							FROM system_pages
							INNER JOIN system_table ON system_pages.page_id=system_table.pg_id
							WHERE system_pages.page_id = '$i'
							AND (system_table.required = 'yes' || system_table.required LIKE '%required%')
							AND usergroup = '$_SESSION[usergroup]'
							AND system_table.type NOT IN ('title','subtitle','instruction','warning','textonly')";
            $stmt = $dbh->prepare($sql);
            $stmt->execute();
            $fields_debug .= $sql; //used fo debugging

            #if($_SESSION['uid']==52598){
            #echo "<br/>SQL - fields: $sql";
            #}

            $field_names = array();
            while (list($fname) = $stmt->fetch(PDO::FETCH_ASSOC)) $field_names[] = $fname;

            // If no required fields just skip the calculation
            if (count($field_names) === 0) continue;

            // Add to total required
            $required_fields += count($field_names);

            // Fetch the record owned by the student and check what is filled in
            $sql = "SELECT " . implode(',', $field_names) . " FROM " . $page_names[$i] . "
							WHERE rel_id = '$core_students_id'";

            #if($_SESSION['uid']==52598){
            # echo "<br/>Required_fields = ".$sql;
            # echo "<br/><hr/>";
            #}

            //error_log("SQL - stu: $sql");
            $stmt = $dbh->prepare($sql);
            $stmt->execute();
            $student_reg_data = $stmt->fetch(PDO::FETCH_ASSOC);

            // Go through fields checking if they are filled in
            foreach ($student_reg_data as $field_data)
                if ($field_data != "" && $field_data !== 'not specified')
                    $required_filled += 1;
        }

        /* if($_SESSION['student_id']==47715){
							echo "<pre>";
							echo "= $pg_name<br/>";
							print_r($page_names);
							echo "= $fields_debug<br/>";
							print_r($field_names);
							echo "= $sql<br/>";
							echo "-";
							print_r($student_reg_data);
							echo "<br/>".$page_names[$i];
							echo "<br/>fields: ".$required_fields;
							echo "<br/>filled: ".$required_filled;
							echo "</pre>";
						}*/

        if (strpos($preference_with_files, "required_files")) {

            //START From applicant side
            //try the required files
            $must_upload_files = required_file_rules($core_students_id);

            list ($have_uploaded_ids, $have_uploaded_desc) = explode("||", pull_field("form_file", "concat(group_concat(id),'||',group_concat(db200))", "WHERE rel_id='$core_students_id' AND (rec_archive IS NULL OR rec_archive = '') and db202!='archived'"));
            $have_uploaded = explode(",", $have_uploaded_desc);
            //$have_uploaded_ids_array = explode(",",$have_uploaded_ids);
            $files_they_should_have_uploaded = explode(",", pull_field("form_file_category", "group_concat(id,'|',db743)", "WHERE id IN($must_upload_files) AND usergroup='$_SESSION[usergroup]'"));
            //          $result = implode(",",array_diff($must_upload_files, $have_uploaded)); // get odd ones out
            $uploaded = 0;

            foreach ($files_they_should_have_uploaded as $must_file_names) {
                //breakup the result
                $must_file_names = explode("|", $must_file_names);
                //check if upload was done
                if (in_array($must_file_names[0], $have_uploaded)) {
                    $uploaded++;
                }
            }
            $required_fields += count($files_they_should_have_uploaded);
            $required_filled += $uploaded;
            //END From applicant side
        }
        return array($required_fields, $required_filled);
    }//END FUNCTION
}
if (!function_exists('sortByOrder')) {
    function sortByOrder($a, $b)
    {
        return $a['weight'] - $b['weight'];
    }
}


/*--------------------------------
	// FUNCTION TO GET_COMS_TEMPLATE
	---------------------------------*/
if (!function_exists('get_coms_template')) {
    function get_coms_template($id)
    {

        if (5 == $id) {
            //try to pull usergroup specific template here
            //just get a template (older setup compatibility)
            $select_email_template = pull_field("coms_template", "id", "WHERE usergroup='" . session_info('usergroup') . "' AND (db1147='44') and (rec_archive IS NULL or rec_archive='')"); //
            if (!empty($select_email_template)) {
                $id = $select_email_template;
            }
        }
        $dbh = get_dbh();
        $sql = "SELECT * FROM coms_template WHERE id=? LIMIT 1";
        $sth = $dbh->prepare($sql);
        $sth->execute(array($id));
        dev_debug(__FUNCTION__ . ": " . $sql . ":id=" . $id);
        while ($row = $sth->fetch()) {

            $coms_template_id = $row['id'];
            $coms_template_rec_id = $row['rec_id'];
            $coms_template_usergroup = $row['usergroup'];
            $coms_template_rel_id = $row['rel_id'];
            $coms_template_template_name = $row['db1083'];
            $coms_template_subject_line = $row['db1086'];
            $coms_template_plain_text_version = $row['db1084'];
            $coms_template_html_version = $row['db1085'];
            $coms_template_email_address_to_send_from = $row['db1090'];
            $coms_template_tags = $row['db1320'];
        }

        return array($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from, $coms_template_tags);
    }
}

/*------------------------------
// EMAIL TEMPLATE REPLACEMENT PROCESSOR
---------------------------------*/
if (!function_exists('email_template_replace_values')) {
    function email_template_replace_values($value, $replacement, $message_body)
    {
        return str_replace("$value", "$replacement", "$message_body");
    }
}


if (!function_exists('get_usergroup_addtional_fields')) {
    function get_usergroup_addtional_fields($all)
    {
        $dbh = get_dbh();
        global $db;
        if ($all) {
            $usergroup_sql = " usergroup in (1," . $_SESSION['usergroup'] . ")";
        } else {
            $usergroup_sql = " usergroup='" . $_SESSION['usergroup'] . "'";
        }
        $sql = "SELECT * FROM coms_custom_fields WHERE db26230='yes' AND $usergroup_sql";
        dev_debug($sql);
        $sth = $dbh->prepare($sql);
        $sth->execute();
        //echo "<pre>".print_r($sql,1)."</pre>";
        // $results = $db->query($sql);
        return $sth->fetchAll(PDO::FETCH_ASSOC);

    }
}

if (!function_exists('general_get_row_by_page_id')) {
    function general_get_row_by_page_id($page_id, $rel_id)
    {
        global $db;
        $sql = "select c.sys_cat_abv,p.page_name from system_pages p
        inner join system_cat c on c.sys_cat_id=p.project where page_id='$page_id'";
        $table = $db->query($sql);
        //echo "<pre>".print_r($table,1)."</pre>";exit();

        if (isset($table[0])) {
            $where = '';
            $table_name = $table[0]['sys_cat_abv'] . "_" . $table[0]['page_name'];
            switch ($page_id) {
                case '4':
                    $where .= " and id='$rel_id'";
                    break;

                case '12':
                    //$rec_id=pull_field("core_students",'rec_id',"WHERE id='".$rel_id."'");
                    $where .= " and id='$rel_id'";
                    break;

                default:
                    $where .= " and rel_id='$rel_id'";
                    break;
            }
            $sql = "select * from $table_name
        where (rec_archive IS null or rec_archive = '') and usergroup='$_SESSION[usergroup]' $where order by id desc";
            $data = $db->query($sql);
            dev_debug("ran " . $sql);
            return $data;
        }
    }
}

if (!function_exists('general_resolve_further_defined_fields_field')) {
    function general_resolve_further_defined_fields_field($fields_formulae, $rel_id, $existing_return)
    {
        //second field fallback handling
        if (strpos($fields_formulae, "??") !== false) {
            $options = explode("??", $fields_formulae);
            if (!empty($options[1])) {
                $first_value = general_get_data_by_field($options[0], $rel_id);
                $second_value = general_get_data_by_field($options[1], $rel_id);
                return !empty(trim($first_value)) ? $first_value : $second_value;
            }
        }

        if (strpos($fields_formulae, "custom_sql?&#") !== false) {
            $options = explode("?&#", $fields_formulae);
            if (!empty($options[1])) {
                //$first_value = general_get_data_by_field($options[0], $rel_id);
                //$second_value = general_get_data_by_field($options[1], $rel_id);
                $sql = str_replace('$applicant_id', $rel_id, session_floating($options[1]));
                $dbh = get_dbh();
                $sth = $dbh->prepare($sql);
                //$_GET['debug_mode']='yes';
                dev_debug("custom_sql?&#: $sql");
                //$_GET['debug_mode']='no';
                ///echo "SELECT $field FROM $table $where<br>";
                $sth->execute();
                // error_log("Wordpay - SELECT $field FROM $table $where");
                return $sth->fetchColumn();
                //return !empty(trim($first_value)) ? $first_value : $second_value;
            }
        }
        return $existing_return;
    }
}

if (!function_exists('general_get_data_by_field')) {
    function general_get_data_by_field($db_field_name, $rel_id, $form_id = null)
    {
        if (!class_exists('Db_helper')) load_helper('db');

        $db = new Db_helper();
        $where = '';
        if ($form_id != null) {
            $where .= " AND t.form_id='$form_id'";
        }
        $sql = "select c.sys_cat_abv,p.page_name,t.pg_id,t.type,t.figures,t.form_id from system_table t
        inner join system_pages p on p.page_id=t.pg_id
        inner join system_cat c on c.sys_cat_id=p.project
        where db_field_name='$db_field_name' and t.usergroup =" . $_SESSION['usergroup'] . " $where";
        $table = $db->query($sql);
        //echo "<pre>".print_r($table,1)."</pre>";
        if (!isset($table[0])) {
            $sql = "select c.sys_cat_abv,p.page_name,t.pg_id,t.type,t.figures,t.form_id
        from system_table t
        inner join system_pages p on p.page_id=t.pg_id
        inner join system_cat c on c.sys_cat_id=p.project
        where db_field_name='$db_field_name'  $where";
            $table = $db->query($sql);
        }
        if (isset($table[0])) {
            $table_name = $table[0]['sys_cat_abv'] . "_" . $table[0]['page_name'];
            $where2 = "";
            switch ($table[0]['pg_id']) {
                case '4':
                    $where2 = " and id={$rel_id}";
                    //$db->where("id", $rel_id);
                    break;

                case '12':
                    $rec_id = pull_field("core_students", 'rec_id', "WHERE id='" . $rel_id . "'");
                    $where2 = " and id={$rec_id}";
                    //$db->where("id", $rec_id);
                    break;

                default:
                    $where2 = " and rel_id={$rel_id}";
                    //$db->where("rel_id", $rel_id);
                    break;
            }
            $sql = "select * from $table_name
                where  (rec_archive IS null or rec_archive = '') 
                and usergroup = {$_SESSION['usergroup']}
                {$where2}
                order by id desc";
            $data = $db->query($sql);
            dev_debug("ran " . $sql);
            if (isset($data[0])) {
                if ($table[0]['type'] == 'dynamic_list_group') {
                    # code...
                    list($default_tables, $default_field) = explode(",", $table[0]['figures']);
                    list($default_table) = explode(" ", $default_tables);
                    if (strpos($default_field, "+") !== false) {
                        $default_field = "concat(" . str_replace("+", ",' ',", $default_field) . ")";
                    }
                    return pull_field($default_table, $default_field, "WHERE id='" . $data[0][$db_field_name] . "' LIMIT 1");
                } else {
                    return $data[0][$db_field_name];
                }
            }
        }
    }
}
if (!function_exists('insert_last_login_date')) {
    function insert_last_login_date($args)
    {
        $user = new Users;
        $login_check = $user->login_activity_check($args);
        $date_today = date('Y-m-d H:i:s');
        $track_page_url = curPageURL();
        $dbh = get_dbh();
        if ($login_check > 0) {
            $sql_update_last_log = "UPDATE form_last_login SET db1276 = '$date_today',db1275='$track_page_url' WHERE rec_id='$_SESSION[uid]' and usergroup = '$_SESSION[usergroup]'";
        } else {
            $sql_update_last_log = "INSERT INTO form_last_login  (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id, db1275, db1276) VALUES ('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '" . session_info("uid") . "', '" . custom_date_and_time() . "', '" . session_info("uid") . "', '$track_page_url', '$date_today')";
        }
        dev_debug('last login ' . $sql_update_last_log);
        $sth = $dbh->prepare($sql_update_last_log);
        $sth->execute();

    }
}

if (!function_exists('curPageURL')) {
    function curPageURL()
    {
        $pageURL = getRequestProtocol();
        $pageURL .= "://";
        if ($_SERVER["SERVER_PORT"] != "80") {
            $pageURL .= $_SERVER["HTTP_HOST"] . ":" . $_SERVER["SERVER_PORT"] . $_SERVER["REQUEST_URI"];
        } else {
            $pageURL .= $_SERVER["HTTP_HOST"] . $_SERVER["REQUEST_URI"];
        }
        return $pageURL;
    }
}
if (!function_exists('getOptions')) {
    function getOptions($pg_id, $db_field)
    {
        $dbh = get_dbh();
        $field = !empty(pull_field("system_table", "CONCAT(figures, '||||', type)", " WHERE pg_id='{$pg_id}' AND db_field_name='{$db_field}' AND usergroup={$_SESSION['usergroup']}")) ?
            pull_field("system_table", "CONCAT(figures, '||||', type)", " WHERE pg_id='{$pg_id}' AND db_field_name='{$db_field}' AND usergroup={$_SESSION['usergroup']}") :
            pull_field("system_table", "CONCAT(figures, '||||', type)", " WHERE pg_id='{$pg_id}' AND db_field_name='{$db_field}'");
        list($sql, $field_type) = explode("||||", $field);

        if (preg_match_search($sql, '[SESSION]')) {
            $sql = session_floating($sql);
        }
        list($from_sql, $field_list) = explode(",", $sql);
        if (preg_match_search($field_list, "+")) {
            $fields = str_replace('+', ', ', $field_list);
        } else {
            $fields = "id, " . $field_list;
        }

        if ($field_type === "dynamic_list_group") {
            if (preg_match_search($from_sql, "WHERE")) {
                $from_sql .= " AND usergroup = {$_SESSION['usergroup']} AND (rec_archive IS NULL OR rec_archive = '')";
            } else {
                $from_sql .= " WHERE usergroup = {$_SESSION['usergroup']} AND (rec_archive IS NULL OR rec_archive = '')";
            }
        }

        $sql = " SELECT $fields as title FROM $from_sql";
        dev_debug("Get options $sql");
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(2);
    }
}
if (!function_exists('getSections')) {
    function getSections($parent_page, $section = 'tabs', $group_category = false)
    {
        global $school_info;
        $modules = $school_info['module_ids'];
        $dbh = get_dbh();
        if ($group_category) {
            $group_category_list = "AND db18717 = '$group_category' ";
        }
        if ($_SESSION['ulevel'] == 9) {
            $sth = $dbh->prepare("SELECT form_create_view.id as idss,db40638,db,db1,db193,db190,db4,db5,db82,db282,db284,db298,form_submodules.id,form_submodules.db766
                    FROM form_create_view LEFT JOIN form_submodules
                    ON form_create_view.id=form_submodules.db327
                    WHERE db193=?
                    AND db82='yes'
                    AND form_submodules.id IN ($modules)
                    $group_category_list
                    order by db259 ASC");
            $sth->execute(array($parent_page));
        } else {
            $sth = $dbh->prepare("SELECT form_create_view.id as idss,db40638,db,db1,db193,db190,db4,db5,db82,db282,db284,db298,form_submodules.id,form_submodules.db766
                    FROM form_create_view LEFT JOIN form_submodules
                    ON form_create_view.id=form_submodules.db327
                    WHERE db193=?
                    AND db82='yes'
                    AND form_submodules.id IN ($modules)
                    $group_category_list
                    AND (?<=form_submodules.db766 OR FIND_IN_SET(?,db278000))
                    order by db259 ASC");
            $sth->execute(array($parent_page, $_SESSION['ulevel'], $_SESSION['ulevel']));
        }
        $rows = [];
        $results = $sth->fetchAll(2);
        foreach ($results as $row) {
            $row['linker'] = !empty($row['db32668']) ? $row['db32668'] : "view_" . $row['idss'];
            array_push($rows, $row);
        }
        return $rows;
    }
}

////////////////////////////////////////////////////////
//  GET BULK ACTIONS
///////////////////////////////////////////////////////
if (!function_exists('get_bulk_actions')) {
    function get_bulk_actions($view_id)
    {
        $create_view_allow_bulk_actions = "no";

        if (pull_field("form_bulk_action_link", "count(*)", "WHERE db29329 = $view_id AND (rec_archive IS null or rec_archive = '') AND usergroup = " . $_SESSION["usergroup"]) > 0) {
            $create_view_allow_bulk_actions = "yes";
        }
        if ($create_view_allow_bulk_actions == "yes") {
            $dbh = get_dbh();
            $sql = $dbh->prepare("SELECT fba.id as id, db29322, db29326, db29323, db29327
                            FROM form_create_view fcv
                             INNER JOIN form_bulk_action_link fbal ON fbal.db29329 = fcv.id
                             INNER JOIN form_bulk_actions fba ON fbal.db29330 = fba.id
                             where fcv.id = ? AND (fbal.rec_archive ='' OR fbal.rec_archive IS NULL) AND fbal.usergroup = $_SESSION[usergroup]");
            $sql->execute(array($view_id));
            $bulk_actions = $sql->fetchAll(PDO::FETCH_OBJ);
            if (count($bulk_actions) > 0) {
                $html = '

            <div class="btn-group" style="margin-right: 10px;">
                    <button type="button" class="btn btn-primary"><i class="fa fa-plus-square-o"></i> Bulk Actions</button>
                    <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-expanded="false"  style="height: 32px;">
                        <span class="caret"></span>
                        <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <ul class="dropdown-menu" role="menu">
                    ';
                foreach ($bulk_actions as $bulk_action) {
                    $html .= '<li><a  class="miller" data-url="' . website_url . '/static/bulk_actions/' . $bulk_action->db29327 . '" data-toggle="modal" data-target="#bulk_actions_modal_views_admin"><i class="fa fa-file-text-o"></i> ' . $bulk_action->db29322 . '</a>
                </li>';
                }
                $html .= '</ul>
                    </div>';

            } else {
                $html = "";
            }


        } else {
            $html = "";
        }
        return $html;
    }
}

if (!function_exists('abort')) {
    function abort($code, $message)
    {
        die($message);
    }
}

if (!function_exists('application_pages')) {
    function application_pages($application_id, $school_id, $page_url = "")
    {
        //Get Student pages
        $students = new Students;
        global $school_info;

        # Get current page
        $cms_pages = new FrontendPages;
        if ($page_url) {
            $pages_args = array('title' => $page_url);
            $cms_page = $cms_pages->get($pages_args);
        }

        $pages_args = array("student_id" => $application_id);
        $page_ids = $students->get_student_pages($pages_args);

        # Get the list of Application pages
        $pages_args = array('publish' => 'yes', 'id_in' => $page_ids);
        $application_pages = $cms_pages->get($pages_args);


        #Fix the page order
        $page_ids = explode(",", $page_ids);
        $ordered_pages_list = array();
        foreach ($page_ids as $pi) {
            foreach ($application_pages as $page) {
                if ($pi == $page['id']) {
                    $ordered_pages_list[] = $page;
                }
            }
        }

        #Default Page
        if (!$page_url) {
            $cms_page = $application_pages[0];
            $page_url = $cms_page['title'];
        }

        //Calculate the Previous and Next Page
        $i = 0;
        foreach ($ordered_pages_list as $page) {
            if ($page['title'] == $page_url) {
                $next_page = $ordered_pages_list[$i + 1];
                $previous_page = $ordered_pages_list[$i - 1];
            }
            $i++;
        }

        return [
            'page_ids' => $page_ids,
            'application_pages' => $application_pages,
            'ordered_pages_list' => $ordered_pages_list,
            'cms_page' => $cms_page,
            'page_url' => $page_url,
            'next_page' => $next_page,
            'previous_page' => $previous_page,

        ];
    }
}


/////////////////////////////////////////
// CONVERT THE ENGLISH TO OTHER LANGUAGES
//////////////////////////////////////////
if (!function_exists('translate')) {
    function translate($word, $lang = 1)
    {

        // echo $word . ' ' . $lang;

        #$word = addslashes($word);
        $word = str_replace("'", "&#39;", $word);
        //check
        ($lang == '' ? 'en' : $lang);
        $language = new Translations($lang);
        return $language->translate($word);
    }
}


/****************************************
 *   TERMINOLOGY
 *****************************************/

if (!function_exists('terminology')) {
    /////////////////////////////////////////
    // Define your own terms
    //////////////////////////////////////////
    function terminology($our_word, $url, $our_description = '', $return = false)
    {
        global $terminologies;
        $url = preg_replace('/[0-9]+/', '', $url);
        dev_debug('our word - ' . $our_word);
        //check if array key exist in array or not
        if (array_key_exists($our_word, $terminologies)) {
            dev_debug('key exists- value is' . $terminologies[$our_word]);
            if (!empty($terminologies[$our_word])) {
                $word_to_use = $terminologies[$our_word];
            } else {
                $word_to_use = $our_word;
            }
        } else {
            dev_debug('key does not exists, term added to terminology - word is' . $our_word);
            $terminology = new Terminology();
            $args = [
                'our_word' => $our_word,
                'url' => $url,
                'our_description' => $our_description
            ];
            $terminologies = $terminology->add_terminology($args);
            $word_to_use = $our_word;
        }

        if (!empty($return)) {
            return $word_to_use;
        } else {
            echo $word_to_use;
        }
    }
}

if (!function_exists('terminology_translate')) {
    function terminology_translate($word, $lang, $description = "", $url = "")
    {
        return translate(terminology($word, $url, $description, true), $lang);
    }
}

if (!function_exists('add_page_columns')) {
    function add_page_columns($page)
    {
        $form_templates = new FormTemplates;
        $columns_args = array('page' => $page);
        if (isset($_GET['filter'])) {
            $columns_args['filter_id'] = $_GET['filter'];
        }
        $page_columns = $form_templates->get_page_columns($columns_args);

        if (empty($page_columns)) {

            if (empty($page_columns) && !empty($_GET['filter'])) {
                unset($columns_args['filter_id']);
                $page_columns = $form_templates->get_page_columns($columns_args);
            } elseif (get_option('applicants_page_fields')) {
                $df = json_encode(get_option('applicants_page_fields'));
            } else {
                $df = '["id","internal_reference","first_name","middle_name","email_address"]';
            }
            $column_args = array(
                'columns' => $df,
                'page' => $page
            );
            $form_templates->add_page_columns($column_args);
            $page_columns = json_decode($df);
        }

        return ($page_columns);
    }
}
if (!function_exists('text_to_html')) {
    function text_to_html($text)
    {
        return nl2br($text);
    }
}


/*------------------------------
    // EMAIL TEMPLATE REPLACEMENT PROCESSORS FROM DB
    ---------------------------------*/
if (!function_exists('email_template_replace_values_from_db')) {
    function email_template_replace_values_from_db($content, $contact_id = 570, $contact_type = 'applicant', $record_id = '')
    {
        //DEAL WITH RECORD ID
        $field_list = '';
        // explode it to see how it to use what you need
        $record_id = explode("|", $record_id);
        //EXTRACT ALL THE CUSTOM FIELDS IN MESSAGE BODY
        preg_match_all('/\{{(.*?)\}}/', $content, $matches);
        $arr = $matches[0];
        $field_list .= "'";
        foreach ($arr as $field) {
            $field_list .= $field;
            if (next($arr)) {
                $field_list .= '\',';
            }
            $field_list .= "'";
            // Add comma for all elements instead of last
        }

        //PULL  THE CUSTOM FIELDS VALUES FROM DB
        $usergroups = empty($_SESSION['usergroup']) ? "1" : "1, " . $_SESSION['usergroup'];
        $listed_custom_fields = pull_field("coms_custom_fields", "group_concat(db1318)", "WHERE db1316 IN($field_list) AND usergroup IN ($usergroups)");
        //echo $field_list.'---'.$listed_custom_fields;
        dev_debug($field_list);
        // GET THE REPLACEMENT FIELDS FROM THE DB
        $dbh = get_dbh();
        $sql = "SELECT
            db_field_name,
            type,
            figures,
            db1316 as custom_field,
            db_field_name,
            concat(sys_cat_abv,'_',page_name) as db_table
            FROM system_cat a,system_pages b,system_table c,coms_custom_fields d
            WHERE b.page_id=c.pg_id
            AND a.sys_cat_id = b.project
            AND c.form_id = d.db1318
            AND form_id IN( $listed_custom_fields )";
        $q = $dbh->prepare($sql);
        $q->execute();
        $results = $q->fetchAll(PDO::FETCH_ASSOC);
        dev_debug($sql);
        //echo json_encode($results);
        $existing_contact_id = $contact_id;
        $usergroup = $_SESSION['usergroup'] ?? 1;

        foreach ($results as $key => $row) {
            $form_id = $row['form_id'];    //form id
            $table = $row['db_table'];    //table id
            $db_field_name = $row['db_field_name'];    //field name
            $custom_field = $row['custom_field'];    //field name

            $field_type = $row['type'];
            $external_table_data = $row['figures'];

            //REALTIONAL CONNECTION
            //work out which relational info to use
            if ($contact_type == 'applicant') {
                // $contact_type is a student
                $relational_field = ($table == "core_students" || $table == 'form_users' ? $where_rel = "id" : $where_rel = "rel_id");
                //if student table then it's ID if not then it's rel_id
            } elseif ($contact_type == 'lead') {
                $relational_field = ($table == "lead_profiles" ? $where_rel = "id" : $where_rel = "id");
            } elseif ($contact_type == 'contact') {
                $relational_field = "id";// $contact_type is a contact
            }
            /* echo "
                $db_field_name=='db889'<br/>
                $custom_field_value = pull_field(\"$table\",\"$db_field_name\",\"WHERE $where_rel = '$contact_id'\"); <br/>
                $custom_field_value = pull_field(\"core_courses\",\"db232\",\"WHERE db235='public' AND id='$contact_id' order by db341 ASC LIMIT 1
                ";
                die();
                */

            // GET THE CUSTOM VALUE
            //check if there is a unique reference on offer table
            if ($table == "$record_id[0]") {
                $unique_record_id = "AND id='$record_id[1]'";
            } else {
                $unique_record_id = '';// reset
            }

            if ($table == 'form_users') {
                $contact_id = pull_field("core_students", "rec_id", "WHERE id= '$contact_id' ");
            } else {
                $contact_id = $existing_contact_id;
            }


            //if($_SESSION['uid']=="332"){
            $v .= "table = $record_id[0]<br/>";
            $v .= "id = $record_id[1]<br/>";
            $v .= "uid = $unique_record_id<br/>";
            $v .= "$table $db_field_name WHERE $where_rel = '$contact_id' $unique_record_id<br/><hr/>";
            dev_debug("search replace values=" . $v);
            //}


            $custom_field_value = pull_field("$table", "$db_field_name", "WHERE $where_rel = '$contact_id' $unique_record_id");
            // If preferred name tag is used and value is empty, use firstname (db39)
            if (empty($custom_field_value) && $custom_field == '{{preferred_name}}' && $table == 'core_students' && $usergroup == 52) {
                $custom_field_value = pull_field("$table", "db39", "WHERE $where_rel = '$contact_id' $unique_record_id");
            } elseif (empty($custom_field_value) && $custom_field == '{{enquirer_preferred_name}}' && $table == 'lead_profiles' && $usergroup == 52) {
                $custom_field_value = pull_field("$table", "db1041", "WHERE $where_rel = '$contact_id' $unique_record_id");
            }

            // Lookout for foreign based fields and process them differently
            if ($field_type == 'dynamic_list_group') {
                list($default_tables, $default_field) = explode(",", $external_table_data);
                list($default_table) = explode(" ", $default_tables);
                $custom_field_value = pull_field($default_table, $default_field, "WHERE id='$custom_field_value' LIMIT 1");
            }

            if ($field_type == 'date_field') {
                $custom_field_value = format_date("j F Y", $custom_field_value);
            }

            $custom_field = str_replace(array("{{", "}}"), "", $custom_field);// remove curley brackets
            $data[$custom_field] = str_replace("_", " ", $custom_field_value);//clean the tag

        }

        /*echo "<pre>";
            print_r($data);

            echo "</pre>";
            */
        dev_debug(implode("/", $data));

        //SEARCH & REPLACE ALL THE FIELDS
        $pattern = '/{{(.*?)[\|\|.*?]?}}/';

        $replace = preg_replace_callback($pattern, function ($match) use ($data) {
            $match = explode('||', $match[1]);

            return isset($data[$match[0]]) ? $data[$match[0]] : $data[$match[1]];
        }, $content);
        return $replace;
    }
}

///////////////////////////////
//LOG ALetter - this function will log a lettert url sent
////////////////////////////////
if (!function_exists('log_letter_sent')) {
    function log_letter_sent($subject, $url, $recipient_id, $letter_text = '', $usergroup = 0, $status = '')
    {
        if ($usergroup == 0) $usergroup = session_info("usergroup");
        /***************** GRAB POSTS/GETS*****************/
        $db20301 = $subject;///subject
        $db20300 = $url;///message text version
        $db69980 = $status;//Letter status draft/sent
        $letter_text = sanitise($letter_text);
        dev_debug('letter_text:' . $letter_text);

        if (($db20300 && $db20300 != '') || ($letter_text && $letter_text != '')) {
            $dbh = get_dbh();
            /***************** INSERT FUNCTION *****************/
            // $sql = "INSERT  INTO  dir_letters_sent (username_id, rec_id, usergroup, rel_id, db20300, db20301,db31870,db69980)  VALUES  (?,?,?,?,?,?,?,?)";

            $random = random();
            $sql = "INSERT  INTO  dir_letters_sent (username_id, rec_id, usergroup, rel_id, db20300, db20301,db31870,db69980)  
                VALUES  ('{$random}', '{$_SESSION['uid']}', '{$usergroup}', '{$recipient_id}', '{$db20300}', '{$db20301}', '{$letter_text}',  '{$db69980}')";
            dev_debug($sql);
            $sth = $dbh->prepare($sql);
            $sth->execute();
            $last_id = $dbh->lastInsertId();
            track_use($sql);
            //mysqli_query($dbcon,$sql);
            return $last_id;

        } else {

            track_use("Letter URL not found  $subject - $recipient_id");

            return '';

        }

    }

    if (!function_exists('get_shared_resources')) {
        function get_shared_resources($args)
        {

            $dbh = get_dbh();
            $where = array();
            if (!empty($args['student_id'])) {
                $where [] = "( db34634 IN (" . $args['student_id'] . ") AND db34640 = 'applicant')";
            }

            if (!empty($args['intake'])) {
                $where [] = "( db34637 IN (" . $args['intake'] . ") AND db34640 = 'intake' )";
            }

            if (!empty($args['programme'])) {
                $where [] = "( db34636  IN (" . $args['programme'] . ") AND db34640 = 'programme')";
            }

            if (!empty($args['stage'])) {
                $where [] = "( db34639 IN (" . $args['stage'] . ") AND db34640 = 'stage' )";
            }

            if (!empty($args['date_of_last_log_in'])) {
                $date_of_last_login_sql = $args['date_of_last_log_in'];
            }

            $where [] = "( db34640 = 'everyone')";


            $where_sql = implode(" OR ", $where);

            $sql = "SELECT *,DATE_FORMAT(`date`,'%d/%m/%Y %H:%i:%s') as `date` FROM core_shared_resource WHERE
		    usergroup = '" . session_info('usergroup') . "' AND ($where_sql)
		    AND (rec_archive ='' OR  rec_archive IS NULL)
		    AND (client_archive ='' OR  client_archive IS NULL)
		    $date_of_last_login_sql
		    order by id desc
		    ";
            dev_debug($sql);
            $sth = $dbh->prepare($sql);
            $sth->execute();
            $results_array = $sth->fetchAll(PDO::FETCH_ASSOC);

            if (!empty($args['date_of_last_log_in'])) {
                return count($results_array);
            } else {
                foreach ($results_array as $key => $value) {
                    $sql_file = "SELECT * FROM form_file WHERE id IN (" . $value['db34633'] . ")";
                    $sth2 = $dbh->prepare($sql_file);
                    $sth2->execute();
                    $results_array[$key]['files'] = $sth2->fetchAll(PDO::FETCH_ASSOC);
                }
                return $results_array;

            }
        }
    }

    if (!function_exists('payment_file')) {
        function payment_file()
        {
            $folder = explode(".", $_SERVER['HTTP_HOST']);
            $url = front_header_file_location . "/admin/payment_link.php";
            return $url;
        }
    }

    if (!function_exists('old_portal_url')) {
        function old_portal_url()
        {
            $url = getRequestProtocol() . '://' . $_SERVER['HTTP_HOST'] . '/application';
            return $url;
        }
    }

    if (!function_exists('get_cms_nav')) {
        function get_cms_nav($subpage = "", $type = "public", $where = "", $published = "yes", $active_class = "nav_selected", $display_auto_number = '')
        {
            global $website_url, $page_id;
            //get page id for other languages
            $page_link = pull_field("form_cms", "db647", "where id=$page_id");
            if ($page_link != "") {
                $dbh = get_dbh();
                $lang1_sql = (isset($_SESSION['lang']) && $_SESSION['lang']) ? "AND db21281 LIKE 'en%'" : '';
                $sql = "select form_cms.id from form_cms LEFT JOIN form_languages ON form_languages.id = form_cms.db47583 where  form_cms.usergroup='$_SESSION[usergroup]' $lang1_sql and db647='" . $page_link . "' LIMIT 1";

                dev_debug("page id for the language " . $sql);

                $stmt = $dbh->prepare($sql);
                $stmt->execute();
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $other_lang_page_id = $row['id'];
                    dev_debug("other_lang_page_id: " . $other_lang_page_id);
                }
            }

            if ($type == "private") {
                $lock = "AND db739='yes'";
            } elseif ($type == "ucas") {
                $lock = "";
            } else {
                $lock = "AND db739!='yes'";
            }

            if ($where) {
                $where = "AND " . str_replace('id', 'form_cms.id', $where);
            }

            //PUBLISHED OR NOT
            if ($published == "yes") {
                $published_only = "AND db652='yes'";
            }

            //if where has an order, then don't use default
            if (stripos($where, 'order by') !== false) {
                $order_by = '';
            } else {
                $order_by = "ORDER BY db748";
            }

            $lang_sql = (isset($_SESSION['lang']) && $_SESSION['lang']) ? "AND db21281 LIKE '{$_SESSION['lang']}%'" : '';
            $lang_sql = (stripos($where, 'ORDER BY FIELD') === FALSE) ? $lang_sql : '';

            $dbh = get_dbh();
            $sql = "SELECT *, db21281,form_cms.id as form_cms_id FROM form_cms
                    LEFT JOIN form_languages ON form_languages.id = form_cms.db47583
                    WHERE (form_cms.rec_archive IS NULL OR form_cms.rec_archive = '') AND form_cms.usergroup='$_SESSION[usergroup]'
                    $published_only $lock $where $lang_sql $order_by";

            dev_debug($sql);
            $stmt = $dbh->prepare($sql);
            $stmt->execute();
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $num = count($results);

            if ($num == 0) {
                $sql2 = "SELECT * FROM form_cms WHERE (rec_archive IS NULL OR rec_archive = '') AND usergroup='1' $published_only $lock $where ORDER BY db748";
                dev_debug($sql2);
                //echo $sql2;
                $stmt = $dbh->prepare($sql2);
                $stmt->execute();
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {

                    $id = $row['id'];
                    $cms_category = $row['db656'];
                    $cms_page_name = $row['db647'];
                    $cms_heading = $row['db648'];
                    $cms_brief = $row['db649'];
                    $cms_article = $row['db650'];
                    $cms_data = $row['db651'];
                    $cms_publish = $row['db652'];
                    $cms_page_title = $row['db653'];
                    $cms_keywords = $row['db654'];
                    $cms_page_description = $row['db738'];
                    $cms_included_form = $row['db655'];
                    $cms_privacy = $row['db739'];

                    if ($id == $page_id || $other_lang_page_id == $id) {
                        $sty = 'active';
                    }
                    echo '<li class="' . $active_class . ' ' . $sty . '"><a href="' . website_url . '/' . $subpage . '/' . $cms_page_name . '">' . translate($cms_page_title, $_SESSION['lang']) . '</a></li>';
                    $sty = '';
                }
            } else {

                $i = 1;//start auto number counter
                foreach ($results as $row) {

                    $id = $row['form_cms_id'];
                    $cms_category = $row['db656'];
                    $cms_page_name = $row['db647'];
                    $cms_heading = $row['db648'];
                    $cms_brief = $row['db649'];
                    $cms_article = $row['db650'];
                    $cms_data = $row['db651'];
                    $cms_publish = $row['db652'];
                    $cms_page_title = $row['db653'];
                    $cms_keywords = $row['db654'];
                    $cms_page_description = $row['db738'];
                    $cms_included_form = $row['db655'];
                    $cms_privacy = $row['db739'];

                    if ($id == $page_id || $other_lang_page_id == $id) {
                        $sty = 'active';
                    }
                    dev_debug("activeness: $id==$page_id || $other_lang_page_id== $id");
                    echo '<li class="' . $active_class . ' ' . $sty . '">
                    <a href="' . website_url . '/' . $subpage . '/' . $cms_page_name . '">';

                    if ($display_auto_number) {
                        echo "<span>" . $i++ . " - </span>";
                    }
                    echo translate($cms_page_title, $_SESSION['lang']) . "</a></li>";
                    $sty = '';
                }
            }
        }

    }

    if (!function_exists('navigation_page')) {
        function navigation_page($application_id)
        {
            $studentsModel = new Students;
            $dbh = get_dbh();
            $get_user_data_sql = "SELECT *,(SELECT db232 FROM core_courses WHERE id=db889) AS course
    		FROM core_students
    		WHERE usergroup=" . $_SESSION['usergroup'] . "
    		AND id=" . $application_id . "
    		AND (core_students.rec_archive IS NULL OR core_students.rec_archive ='' ) ";
            dev_debug($get_user_data_sql);
            $sth = $dbh->prepare($get_user_data_sql);
            $sth->execute();
            $user_info = $sth->fetch(PDO::FETCH_ASSOC);

            $page_to_navigate_to = $studentsModel->get_applicant_navigate_page($user_info);
            dev_debug('page_to_navigate_to' . $page_to_navigate_to);

            if ($page_to_navigate_to !== '') {
                //$page_to_navigate_to;
                $personal_info = $page_to_navigate_to;
            } else {
                //all other
                $personal_info = "personal_information";
            }
            return $personal_info;
        }
    }

}
if (!function_exists('hslToRgb')) {

    function hslToRgb($h, $s, $l)
    {
#    var r, g, b;
        if ($s == 0) {
            $r = $g = $b = $l; // achromatic
        } else {
            if ($l < 0.5) {
                $q = $l * (1 + $s);
            } else {
                $q = $l + $s - $l * $s;
            }
            $p = 2 * $l - $q;
            $r = hue2rgb($p, $q, $h + 1 / 3);
            $g = hue2rgb($p, $q, $h);
            $b = hue2rgb($p, $q, $h - 1 / 3);
        }
        $return = array(floor($r * 255), floor($g * 255), floor($b * 255));
        return $return;
    }
}
if (!function_exists('hue2rgb')) {

    function hue2rgb($p, $q, $t)
    {
        if ($t < 0) {
            $t++;
        }
        if ($t > 1) {
            $t--;
        }
        if ($t < 1 / 6) {
            return $p + ($q - $p) * 6 * $t;
        }
        if ($t < 1 / 2) {
            return $q;
        }
        if ($t < 2 / 3) {
            return $p + ($q - $p) * (2 / 3 - $t) * 6;
        }
        return $p;
    }
}
if (!function_exists('numberToColorHsl')) {

    /**
     * Convert a number to a color using hsl, with range definition.
     * Example: if min/max are 0/1, and i is 0.75, the color is closer to green.
     * Example: if min/max are 0.5/1, and i is 0.75, the color is in the middle between red and green.
     * @param i (floating point, range 0 to 1)
     * param min (floating point, range 0 to 1, all i at and below this is red)
     * param max (floating point, range 0 to 1, all i at and above this is green)
     */
    function numberToColorHsl($i, $min, $max)
    {
        $ratio = $i;
        if ($min > 0 || $max < 1) {
            if ($i < $min) {
                $ratio = 0;
            } elseif ($i > $max) {
                $ratio = 1;
            } else {
                $range = $max - $min;
                $ratio =$range>0? ($i - $min) / $range:0;
            }
        } else {
            $range = $max - $min;
            $ratio = $range>0?($i - $min) / $range:0;
        }
        // as the function expects a value between 0 and 1, and red = 0° and green = 120°
        // we convert the input to the appropriate hue value
        $hue = $ratio * 1.2 / 3.60;
        //if (minMaxFactor!=1) hue /= minMaxFactor;
        //console.log(hue);

        // we convert hsl to rgb (saturation 100%, lightness 50%)
        $rgb = hslToRgb($hue, 1, .5);
        // we format to css value and return
        return 'rgb(' . $rgb[0] . ',' . $rgb[1] . ',' . $rgb[2] . ')';
    }
}

if (!function_exists('log_email')) {
    function log_email($to, $subject, $msg_txt, $msg_html, $from, $cat = 'alert', $recipient_id = '', $triggered_by = '', $login_url = '', $subdomain = '', $usergroup = null, $cc = '')//$to,$subject,$msg_txt,$msg_html,$from
    {
        $email = new Emails();
        $email->send([
            'to' => $to,
            'subject' => $subject,
            'text' => $msg_txt,
            'html' => $msg_html,
            'from' => $from,
            'category' => $cat,
            'recipient_id' => $recipient_id,
            'ccemails' => $cc,
        ]);
    }
}

if (!function_exists('is_child')) {
    function is_child($id)
    {
        $dbh = get_dbh();
        $child = false;
        $primary_email = '';
        $ccs = [];
        $separate = '';
        $parents = pull_field('form_users', 'concat_ws("|", db106,db111,db119)', "WHERE id= (SELECT rec_id FROM core_students WHERE id='{$id}') AND db112='7'");
        if (!empty($parents)) {
            $child = true;
            $parent = explode("|", $parents);
            $stmt = $dbh->prepare("SELECT db12163 as email, db73805 as `primary`, db64498 as `separate` FROM core_parents p LEFT JOIN core_student_parent_links c ON c.db64491=p.id WHERE c.rel_id='{$id}' AND (db73805 ='yes' OR db64497='yes' OR db64498='yes') AND (c.rec_archive IS NULL OR c.rec_archive='') ORDER BY c.id ASC");
            $stmt->execute();
            $linked_parents = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($linked_parents as $parent) {
                if ($parent['primary'] == 'yes' && empty($primary_email)) {
                    $primary_email = $parent['email'];
                } elseif ($parent['separate'] == 'yes' && empty($separate)) {
                    $separate = $parent['email'];
                } else {
                    $ccs[] = $parent['email'];
                }
            }
        }
        return [
            'child' => $child,
            'parent' => $parent,
            'primary' => $primary_email,
            'separate' => $separate,
            'ccs' => $ccs
        ];
    }
}

if (!function_exists('getUKPostcodeFirstPart')) {
    function getUKPostcodeFirstPart($postcode)
    {
// validate input parameters
        $postcode = strtoupper($postcode);

// UK mainland / Channel Islands (simplified version, since we do not require to validate it)
        if (preg_match('/^[A-Z]([A-Z]?\d(\d|[A-Z])?|\d[A-Z]?)\s*?\d[A-Z][A-Z]$/i', $postcode))
            return preg_replace('/^([A-Z]([A-Z]?\d(\d|[A-Z])?|\d[A-Z]?))\s*?(\d[A-Z][A-Z])$/i', '$1', $postcode);
// British Forces
        if (preg_match('/^(BFPO)\s*?(\d{1,4})$/i', $postcode))
            return preg_replace('/^(BFPO)\s*?(\d{1,4})$/i', '$1', $postcode);
// overseas territories
        if (preg_match('/^(ASCN|BBND|BIQQ|FIQQ|PCRN|SIQQ|STHL|TDCU|TKCA)\s*?(1ZZ)$/i', $postcode))
            return preg_replace('/^([A-Z]{4})\s*?(1ZZ)$/i', '$1', $postcode);

// well ... even other form of postcode... return it as is
        return $postcode;
    }
}

if (!function_exists('validateUKPostcode')) {
    function validateUKPostcode($postcode)
    {
        // Define the regex pattern for UK postcodes
        $pattern = '/^(GIR\s0AA|(?:[A-Z]{1,2}\d{1,2}[A-Z]?\s?\d[A-Z]{2})|(?:[A-Z]{1,2}\d[A-Z]\s?\d[A-Z]{2})|(?:BFPO\s?\d{1,4}))$/i';

        // Remove spaces and convert to uppercase
        $postcode = strtoupper(str_replace(' ', '', $postcode));

        // Apply the regex pattern
        if (preg_match($pattern, $postcode)) {
            return true;
        } else {
            return false;
        }
    }
}


///////////////////////////////
//LOG AN EMAIL - this function will log an email to be sent and get it ready to be sent
////////////////////////////////
/*
 * @deprecated
 */
if (!function_exists('log_email')) {
    function log_email($to, $subject, $msg_txt, $msg_html, $from, $cat = 'alert', $recipient_id = '', $triggered_by = '', $login_url = '', $subdomain = '', $usergroup = null, $cc = '')//$to,$subject,$msg_txt,$msg_html,$from
    {
        //trigger_error('Function ' . __FUNCTION__ . ' is deprecated', E_USER_DEPRECATED);
        if (!class_exists('Email_helper')) load_helper('email');
        $email = new Email_helper();

        $email->to($to)->subject($subject)->plain($msg_txt)->html($msg_html)->replyTo($from)->category($cat);
        if ($recipient_id) $email->setRelId($recipient_id);
        if ($usergroup) $email->usergroup($usergroup);
        if ($cc) $email->cc($cc);
        if ($triggered_by) $email->setUserId($triggered_by);
        if ($login_url) $email->setLoginUrl($login_url);
        $sent = $email->send();
        if (!$sent) dev_debug($email->getError());
        return $email->getError();

        /***************** GRAB POSTS/GETS*****************/
        $db1150 = sanitise($msg_txt);///message text version
        $db1151 = sanitise(add_login_link("$subject", "$msg_html", "$to", "$login_url"));/// add login link
        $db1152 = sanitise($cat);///Category Group
        $db1153 = sanitise($to);///Message To
        $db1149 = sanitise($subject);///subject

//  $db1151 .= insert_login_link($db1153);

//if from empty
        if ($from == '') {
            $db1154 = master_email;///Message From
        } else {
            $db1154 = sanitise($from);///Message From
        }

//if triggred by is set then use it otherwise default
        if ($triggered_by == '') {
            $triggered_by = session_info("uid");///record default
        }
        $usergroup = empty($usergroup) ? session_info("usergroup") : $usergroup;
        dev_debug("email details: to $to, subject: $subject, msg: $msg_html, from: $from ");
        dev_debug('email exists = checking');
//only add if an email exists
        if ($db1153) {
            dev_debug("db1153 is $db1153");
            $dbh = get_dbh();
            /***************** INSERT FUNCTION *****************/
            $sql = "INSERT INTO form_email_log (username_id, rec_id, usergroup, rel_id, db1149, db1150, db1151, db1152, db1153, db1154, db1161,db1155,db58550) VALUES ('" . random() . "', '" . $triggered_by . "', '" . $usergroup . "', '$recipient_id', '$db1149', '$db1150', '$db1151', '$db1152', '$db1153', '$db1154','no','logged','" . $cc . "')";

            track_use($sql);
            dev_debug('inserting email into email logs: ' . $sql);
            $stmt = $dbh->prepare($sql);
            return $stmt->execute();
        }// end if email exists
        else {
            track_use("Email not found $db1149 - $db1152");
        }

        //echo '<div class="success">Your email has been logged and is ready to send</div>';
    }
}

/**
 * Tries to convert the given HTML into a plain text format - best suited for
 * e-mail display, etc.
 *
 * <p>In particular, it tries to maintain the following features:
 * <ul>
 *   <li>Links are maintained, with the 'href' copied over
 *   <li>Information in the &lt;head&gt; is lost
 * </ul>
 *
 * @param html the input HTML
 * @return the HTML converted, as best as possible, to text
 */
if (!function_exists('convert_html_to_text')) {

    function convert_html_to_text($html)
    {
        $html = fix_newlines($html);

        $doc = new DOMDocument();
        if (!$doc->loadHTML($html))
            throw new Html2TextException("Could not load HTML - badly formed?", $html);

        $output = iterate_over_node($doc);

        // remove leading and trailing spaces on each line
        $output = preg_replace("/[ \t]*\n[ \t]*/im", "\n", $output);

        // remove leading and trailing whitespace
        $output = trim($output);

        return $output;
    }
}

if (!function_exists('create_link_button')) {
    function create_link_button($url, $text = "Login to account")
    {
        if (empty($url)) {
            return "";
        } else {
            $pref_bg_color = pull_field("lead_preferences", "db50675", "where usergroup=$_SESSION[usergroup]");
            if (empty($pref_bg_color)) $pref_bg_color = "#01b3e3";
            $link = '<table width="100%"><tr style="background-color:#ffffff">
                      <td style="text-align:left;max-width:600px">
                          <table align="center"
                                 style="background-color:#ffffff;text-align:center;max-width:600px">
                              <tbody>
                              <tr>
                                  <td align="center" valign="top"
                                      style="padding-top:20px;padding-bottom:40px;margin:0 auto;background-color:#ffffff">
                                      <table width="240" border="0"
                                             cellspacing="0" cellpadding="0"
                                             style="background-color:' . $pref_bg_color . ';border-radius:4px;border-collapse:separate!important">
                                          <tbody>
                                          <tr>
                                              <td class="m_3513362776129727005hover"
                                                  width="240" height="46"
                                                  align="center"
                                                  valign="middle"
                                                  style="font-size:16px;font-family:\'roboto\', sans-serif, arial;font-weight:300">
                                                  <a class="m_3513362776129727005hover"
                                                     href="' . $url . '"
                                                     title="' . $text . '"
                                                     style="text-decoration:none;color:#ffffff;display:block;border-radius:4px;font-family:\'roboto\', sans-serif, arial;padding-top:10px;border:2px solid ' . $pref_bg_color . ';padding-bottom:10px;font-weight:bold;"
                                                     target="_blank">' . $text . '</a></td>
                                          </tr>
                                          </tbody>
                                      </table>
                                  </td>
                              </tr>
                              </tbody>
                          </table>
                      </td>
                  </tr></table>';
            return $link;
        }
    }
}
if (!function_exists('wrap_in_template')) {
    function wrap_in_template($subject, $body, $usergroup = null)
    {
        if (empty($usergroup)) $usergroup = $_SESSION['usergroup'];
        $sending_header = pull_field("form_schools", 'db1242', "WHERE id='$usergroup'");
        if (!empty($sending_header)) {
            return $body;
        }
        $template = '<table height="100%" width="100%" cellpadding="0" cellspacing="0" border="0">
        <tbody>
        <tr>
            <td valign="top" align="left" style="background-color:#f7f7f7">
                <center align="center" style="width:100%">
                    <div class="m_3513362776129727005preheader"
                         style="font-size:1px;line-height:1px;display:none!important">HEIApply notification
                    </div>
                    <table align="center" border="0" cellpadding="0" cellspacing="0"
           style="font-family:\'roboto\', sans-serif, arial;width:100%">
        <tbody>
        <tr>
            <td align="center" border="0" cellpadding="0" cellspacing="0" height="auto" valign="top"
                style="margin:0 auto">
                <div id="m_3513362776129727005maincontent" align="center" style="margin:0 auto">
                    <table align="center" border="0" cellpadding="0" cellspacing="0" style="width:100%">
                        <tbody>
                        <tr>
                            <td align="center" style="padding:0px 0px 60px">
                                <table border="0" cellpadding="0" cellspacing="0" style="width:100%">
                                    <tbody>
                                    <tr>
                                        <td style="background-color: #F7F7F7">
                                            <table align="center" border="0" cellpadding="0" cellspacing="0"
                                                   style="margin:0 auto;font-family:\'roboto\', sans-serif, arial;font-size:14px;line-height:24px;background-color:#ffffff;max-width:600px;width:100%">
                                                <tbody>
                                                <tr>
                                                    <td style="background-color:rgb(50, 58, 69);padding-top:30px;padding-bottom:30px">
                                                        <table align="center">
                                                            <tbody>
                                                            <tr>
                                                                <td><img alt="HEIApply" border="0"
                                                                         height="50"
                                                                         src="https://adminlite.heiapply.com/images/signin-logo.png"
                                                                         style="color:#e5eaec;height:50px!important;\'roboto\', sans-serif, arial;font-size:50px;line-height:60px;font-weight:300;text-align:center;letter-spacing:1px"
                                                                         class="CToWUd"></td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="background-color:#ffffff;padding-top:30px">
                                                        <div style="display:inline-block;width:100%;max-width:600px">
                                                            <table align="left" border="0" cellpadding="0"
                                                                   cellspacing="0"
                                                                   style="margin:0 auto;width:100%">
                                                                <tbody>
                                                                <tr>
                                                                    <td class="m_3513362776129727005mobileheadline"
                                                                        style="padding:0px 40px 20px 40px;color:#102231;font-size:20px;font-family:\'roboto\', sans-serif, arial;line-height:20px;font-weight:lighter">
                                                                        HEIApply - ' . $subject . '
                                                                    </td>
                                                                </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align:left;padding:0px 40px 0px 40px;font-size:13px;line-height:14px;color:#102231;font-weight:lighter;background-color:#ffffff">
                                                        <div style="display:inline-block;width:100%;max-width:520px">
                                                        ' . $body . '
                                                        </div>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </td>
        </tr>
        </tbody>
    </table>
                </center>
            </td>
        </tr>
        </tbody>
    </table>';
        return $template;
    }
}

if (!function_exists('link_linkify')) {
    function link_linkify($string)
    {
        $url = '@(http(s)?)?(://)?(([a-zA-Z])([-\w]+\.)+([^\s\.]+[^\s]*)+[^,.\s])@';
        return preg_replace($url, '<a href="http$2://$4" target="_blank" title="$0">$0</a>', $string);
    }
}
if (!function_exists('generate_random_domain')) {
    function generate_random_domain($prefix = 'newaccount', $useAutoincrementID = true)
    {
        $domain = $prefix;
        if ($useAutoincrementID) {
            $lastID = pull_field('form_schools', 'id', 'order by id desc limit 1');
            $domain .= $lastID;
        } else {
            $domain .= mt_rand(1000, 100000);
        }
        return $domain;
    }
}
// Create a new page/model
if (!function_exists('create_model')) {
    function create_model($page_name, $project, $user)
    {
        $dbh = get_dbh();

        $page_data = array(
            'page_name' => str_replace(' ', '_', $page_name),
            'user' => $user,
            'project' => $project,
            'pg_keys' => random()
        );

        $sql = "INSERT INTO system_pages (page_name, dt, user, project, pg_keys)
        VALUES (:page_name, CURRENT_TIMESTAMP, :user, :project, :pg_keys)";
        $sth = $dbh->prepare($sql);
        $sth->execute($page_data);

        // Return the ID of the new model
        return $dbh->lastInsertId();
    }
}

// Copies fields from an existing old model to a new model. Both are passed as
// IDs of models as defined in system_pages
if (!function_exists('copy_model_fields')) {
    function copy_model_fields($old_model, $new_model, $new_usergroup = '')
    {
        $dbh = get_dbh();

        // check if dest table empty
        $sql = "SELECT COUNT(*) FROM system_table WHERE pg_id = ?";
        $sth = $dbh->prepare($sql);
        $sth->execute(array($new_model));
        $new_count = $sth->fetchColumn();
        if ($new_count > 0) return array('status' => 'error');

        // Query for inserting copied fields
        $insert_q = "INSERT INTO system_table (
            name, db_field_name, type, box_size, description, required,
            figures, pg_id, form_order, locked, conditional, usergroup
        ) VALUES (
            :name, :db_field_name, :type, :box_size, :description, :required,
            :figures, :pg_id, :form_order, :locked, :conditional, :usergroup
        )";
        $ins_sth = $dbh->prepare($insert_q);

        // go over list of fileds that belong to the old model
        $sql = "SELECT name, db_field_name, type, box_size, description, required,
        figures, pg_id, form_order, locked, conditional, usergroup
        FROM system_table WHERE pg_id = ? ORDER BY form_order";
        $sth = $dbh->prepare($sql);
        $sth->execute(array($old_model));

        while ($field_og = $sth->fetch(PDO::FETCH_ASSOC)) {
            // select field name
            $fid_q = "SELECT MAX(form_id) FROM system_table";
            $fid_sth = $dbh->query($fid_q);
            $last_form_id = $fid_sth->fetchColumn();
            $field_og['db_field_name'] = $field_og['type'] === "title" ? "" : "db" . $last_form_id;
            $field_og['pg_id'] = $new_model;
            $field_og['usergroup'] = $new_usergroup;

            // create copy of field
            error_log('trying to copy field: ' . json_encode($field_og));
            $ins_sth->execute($field_og);
        }
    }
}


if (!function_exists('calculate_payments')) {
    function calculate_payments($invoice, $userGroup = '')
    {
        $dbh = get_dbh();
        if (empty($userGroup)) {
            $userGroup = $_SESSION['usergroup'];
        }

        $payments_sql = "SELECT IFNULL((SELECT concat(format(SUM(db1495),2)) FROM sis_student_fees WHERE rel_id = lead_invoice_settings.rel_id and db1494 = lead_invoice_settings.username_id AND (sis_student_fees.rec_archive is NULL OR sis_student_fees.rec_archive = '') AND db34450='Payment'),0) AS 'payments_made', ifnull((SELECT concat(format(SUM(db1495),2)) FROM sis_student_fees WHERE rel_id = lead_invoice_settings.rel_id and db1494 = lead_invoice_settings.username_id AND (sis_student_fees.rec_archive is NULL OR sis_student_fees.rec_archive = '') AND db34450='Refund'),0) as 'refunds_made', ifnull((SELECT SUM(db15020*db15022) FROM lead_invoice_items where lead_invoice_items.rel_id = lead_invoice_settings.id AND (lead_invoice_items.rec_archive IS NULL OR lead_invoice_items.rec_archive = '')),0) as 'amount_due'
			FROM lead_invoice_settings WHERE (lead_invoice_settings.rec_archive is NULL OR lead_invoice_settings.rec_archive='') AND username_id='{$invoice}' AND usergroup ='{$userGroup}'";
        dev_debug('payments_sql' . $payments_sql);

        $sth = $dbh->prepare($payments_sql);
        $sth->execute();
        $payments = $sth->fetch(PDO::FETCH_ASSOC);
        return $payments;
    }
}

if (!function_exists('get_course_types')) {
    function get_course_types()
    {
        $dbh = get_dbh();

        $course_types_sql = "select figures from system_table where db_field_name='db16595'";
        dev_debug('course_types_sql' . $course_types_sql);

        $sth = $dbh->prepare($course_types_sql);
        $sth->execute();
        $course_types = $sth->fetch(PDO::FETCH_ASSOC);

        $courses_types = explode(',', $course_types['figures']);

        $current_course_type_sql = "select db16595 from core_course_level where usergroup={$_SESSION['usergroup']} GROUP BY db16595";
        dev_debug('current_course_type_sql' . $current_course_type_sql);

        $sth1 = $dbh->prepare($current_course_type_sql);
        $sth1->execute();
        $current_course_types = $sth1->fetchAll(PDO::FETCH_ASSOC);


        $final_course_types = array_intersect(array_column($current_course_types, 'db16595'), $courses_types);

        return $final_course_types;
    }
}


function safe_json_encode($value, $options = 0, $depth = 512)
{
    if (version_compare(PHP_VERSION, '5.4.0') >= 0) {
        $encoded = json_encode($value, JSON_PRETTY_PRINT);
    } else {
        $encoded = json_encode($value);
    }
    if ($encoded === false && $value && json_last_error() == JSON_ERROR_UTF8) {
        $encoded = json_encode(utf8ize($value), $options, $depth);
    }
    switch (json_last_error()) {
        case JSON_ERROR_NONE:
            return $encoded;
        case JSON_ERROR_DEPTH:
            return 'Maximum stack depth exceeded'; // or trigger_error() or throw new Exception()
        case JSON_ERROR_STATE_MISMATCH:
            return 'Underflow or the modes mismatch'; // or trigger_error() or throw new Exception()
        case JSON_ERROR_CTRL_CHAR:
            return 'Unexpected control character found';
        case JSON_ERROR_SYNTAX:
            return 'Syntax error, malformed JSON'; // or trigger_error() or throw new Exception()
        case JSON_ERROR_UTF8:
            $clean = utf8ize($value);
            return safe_json_encode($clean);
        default:
            return 'Unknown error'; // or trigger_error() or throw new Exception()
    }
}


function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } else if (is_string($mixed)) {
        return utf8_encode($mixed);
    }
    return $mixed;
}

if (!function_exists('get_timezone_name')) {
    /**
     * @param string $usergroup
     * @return mixed
     */
    function get_timezone_name(string $userGroup = "")
    {
        if (empty($userGroup)) {
            $userGroup = $_SESSION['usergroup'];
        }
        $preferenceTimezone = pull_field("lead_preferences", "db106496", "WHERE usergroup = $userGroup AND (rec_archive is null or rec_archive = '') LIMIT 1");
        if (empty($preferenceTimezone)) {
            $preferenceTimezone = 1001; //"Europe/London"
        }
        return pull_field("system_timezone", "timezone", "WHERE id = $preferenceTimezone");
    }
}

if (!function_exists('convert_datetime_to_timezone')) {
    /**
     * @param $datetimeToConvert
     * @param $timezoneName
     * @param $dateFormat
     * @param $userGroup
     * @return string
     * @throws Exception
     */
    function convert_datetime_to_timezone($datetimeToConvert, $timezoneName = "", $dateFormat = "", $userGroup = ""): string
    {
        if (empty($userGroup)) {
            $userGroup = $_SESSION['usergroup'];
        }
        if (empty($timezoneName)) {
            $timezoneName = get_timezone_name($userGroup);
        }
        //timezone check
        $datetime = new DateTime($datetimeToConvert);
        $timezoneDatetime = new DateTimeZone($timezoneName);
        $datetime->setTimezone($timezoneDatetime);
        if (!empty($dateFormat)) {
            $datetime_string = $datetime->format($dateFormat);
        } else {
            $datetime_string = $datetime->format('Y-m-d H:i:s');
        }
        return $datetime_string;
    }
}

if (!function_exists('get_preferred_date_format')) {
    /**
     * @param string $usergroup
     * @return mixed
     */
    function get_preferred_date_format(string $userGroup = "")
    {
        if (empty($userGroup)) {
            $userGroup = $_SESSION['usergroup'];
        }
        $preferredDateFormat = pull_field("lead_preferences", "db50772", "WHERE usergroup = $userGroup AND (rec_archive is null or rec_archive = '') LIMIT 1");
        if (empty($preferredDateFormat)) {
            $preferredDateFormat = 'd/m/Y'; //"Europe/London"
        }
        return $preferredDateFormat;
    }
}
if (!function_exists('get_sms_log')) {
    /**
     * @param $args
     * @return array|bool|int|mixed|mixed[]|object|string
     */
    function get_sms_log($args)
    {
        $preferred_date_format = get_preferred_date_format() . ' H:i';
        $timezone_name = get_timezone_name();
        $date_sql = "DATE_FORMAT(CONVERT_TZ( form_sms_log.date, 'UTC', '$timezone_name'),'$preferred_date_format')";

        $school_type = pull_field('core_schools_type left join form_schools on core_schools_type.id = form_schools.db30', 'db277', "WHERE form_schools.id ='$_SESSION[usergroup]'");

        if ($school_type == 'MRN') {
            $learner_name_sql = "(SELECT CONCAT('<a href=/admin/mrn_profiles/profile/',sis_profiles.id ,'&%&%vw=',sis_profiles.username_id ,'&ref=',form_sms_log.rel_id,'&profile_id=',sis_profiles.id,'>', db48566,' ',db48568,'</a>') FROM sis_profiles where sis_profiles.rel_id = form_sms_log.rel_id and (sis_profiles.rec_archive is null or sis_profiles.rec_archive='') order by id asc LIMIT 1)";
        } else {
            $learner_name_sql = "SELECT CONCAT(db15054,' ',db15055 ) from core_students where id = form_sms_log.rel_id)";
        }

        $mark_as_read_sql = "CASE WHEN db25671='Receive' THEN IF((db213845 IS NULL OR db213845='' OR db213845='no'),(SELECT CONCAT('<a href=\"#\" class=\"marksmsasread\" data-sms-id=\"',form_sms_log.id,'\">mark as read</a>')) ,(SELECT CONCAT ('read by ',(SELECT CONCAT (db106, ' ', db111 ) FROM form_users where id=db213845)))) ELSE '' END";


        if (!class_exists('Db_helper')) {
            load_helper('db');
        }
        $db = new Db_helper();
        dev_debug("Get Email Args: " . json_encode($args));
        if (!empty($args['id'])) {
            $db->where('form_sms_log.id', $args['id']);
        }
        if (!empty($args['student_id'])) {
            $db->where('form_sms_log.rel_id', $args['student_id']);
        }
        if (!empty($args['subject'])) {
            $db->where('form_sms_log.db25666', $args['subject']);
        }
        if (!empty($args['to'])) {
            $db->where('form_sms_log.db25667', $args['to']);
        }
        if (!empty($args['orgtype_school_ids'])) {
            $db->where_in('form_sms_log.usergroup', explode(',', $args['orgtype_school_ids']));
        } elseif (!empty($args['school_id'])) {
            $db->where('form_sms_log.usergroup', $args['school_id']);
        }
        if (!empty($args['filter_sql'])) {
            $db->where($args['filter_sql']);
        }

        if (!empty($args['search'])) {
            $search_string = explode(" ", $args['search']);
            $db->group_start();
            foreach ($search_string as $i => $piece) {
                $piece = trim($piece);
                $search_sql .= "AND CONCAT(db25666,db25667,db25668) LIKE '%$piece%'\n";
                if ($i == 0) {
                    $db->like("CONCAT(db25666,db25667,db25668)", $piece, 'both');
                } else {
                    $db->or_like("CONCAT(db25666,db25667,db25668)", $piece, 'both');
                }
            }
            $db->group_end();
        }

        if (!empty($args['send'])) {
            $db->where('db25671', 'Send');
        } elseif (!empty($args['receive'])) {
            $db->where('db25671', 'Receive');
        }
        if (!empty($args['non_read'])) {
            $db->where("(db213845 IS NULL OR db213845='' OR db213845='no')");
        }
        if (!empty($args['order'])) {
            $order = explode(" ", $args['order']);
            $order_field = $order[0];
            $order_type = $order[1];
            if (!$order_type) $order_type = "DESC";
            $db->order_by("$order_field $order_type");
        } else {
            $db->order_by("form_sms_log.id DESC");
        }


        $db->where('(form_sms_log.rec_archive IS NULL OR form_sms_log.rec_archive = "")');
        $db->join('form_schools', 'form_schools.id = form_sms_log.usergroup', 'left');
        $db->join('coms_sms_status_codes', 'form_sms_log.db63208 = coms_sms_status_codes.db211145', 'left');

        $select = [
            'school_title' => 'db27',
            "student_name" => "$learner_name_sql",
            "subject" => 'db25666',
            "message" => 'db25668',
            "to" => 'db25667',
            "send_receive" => 'db25671',
            "status" => 'db211148',
            'id' => 'form_sms_log.id',
            'date' => 'form_sms_log.date',
            'read_by' => "$mark_as_read_sql",
        ];
        if (!empty($args['total'])) {
            return $db->fetch_field('count(DISTINCT form_sms_log.id)', 'form_sms_log');
        } elseif (!empty($args['id'])) {
            return $db->get_row($select, 'form_sms_log');
        }

        if (!empty($args['paginate'])) {
            $db->paginate(true);
        }

        return $db->get_rows($select, 'form_sms_log');
    }
}

if (!function_exists('last_logged_in')) {
    function last_logged_in($id, $format = "d/m/Y")
    {
        //last loggedin
        $date = pull_field("form_last_login", "db1276", "where form_last_login.rec_id='$id' ORDER BY id DESC LIMIT 1");
        //$date = pull_field("tracker","FROM_UNIXTIME(timestamp)","where user_id='$id' ORDER BY track_id DESC LIMIT 1,1");
        if (!$date) {
            return "";
        } else {
            return format_date($format, $date);
        }
    }
}

if (!function_exists('currency_list')) {
    function currency_list($current = '', $default = '')
    {
        dev_debug(" IN Currency list **$current ** $default**");
        $remove_default_from_query = '';
        $accepted_currencies = array_filter(explode(',', pull_field('lead_preferences', 'db214334', "WHERE usergroup='$_SESSION[usergroup]'")));
        //get the default and loop through
        if ($default) {
            //cleanse default
            $default_list = explode(",", $default);

            foreach ($default_list as $piece) {
                $outlined .= "'$piece',";
            }

            $outlined = substr($outlined, 0, -1);
            $remove_default_from_query = "AND country NOT IN ($outlined) ";
        }

        $dbh = get_dbh();
        $sql = "SELECT id,country,abv FROM system_currency where 1 and  (symbol is not null or symbol <> '')
                $remove_default_from_query  group by currency ORDER by country ASC";

//    if ($_SESSION['usergroup'] == 21) {
        //       $sql = "SELECT id,country,abv FROM system_currency  where 1 and  (symbol is not null or symbol <> '') $remove_default_from_query AND abv = 'GBP'  group by currency ORDER by country ASC";
        //   }
        dev_debug("Currency list" . $sql);
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        //echo "<option value=\"\">select</option>";

        if (!empty($default)) {

            // highlight default
            $default_list = explode(",", $default);

            foreach ($default_list as $default) {

                $default_values = explode("//", pull_field("system_currency", "concat(id,'//',country,'//',abv)", "WHERE country='$default' "));

                $default_id = $default_values[0];
                $default_country = $default_values[1];
                $default_abv = $default_values[2];
                if (!empty($accepted_currencies) && !in_array($default_abv, $accepted_currencies)) {
                    continue;
                }

                //check if selected
                if ($default_id == $current) {
                    if ($current == $default) {
                        $selected = "selected=\"selected\"";
                    }
                } else {
                    if ($default_abv == $default) {
                        $selected = "selected=\"selected\"";
                    }
                }
                echo "<option value=\"$default_id\" $selected>$default_country $default_abv</option>";

                $selected = ''; // reset
            }

            echo "<option value=\"\" disabled>-----------</option>";
        }

        foreach ($results as $row) {
            if (!empty($accepted_currencies) && !in_array($row['abv'], $accepted_currencies)) {
                continue;
            }
            if (!empty($current)) {
                if ($row['id'] == $current) {
                    $selected = "selected=\"selected\"";
                }
            } else {
                if ($row['abv'] == $default) {
                    $selected = "selected=\"selected\"";
                }
            }
            echo "<option value=\"$row[id]\" $selected>$row[country]-$row[abv]</option>";
            $selected = ''; // reset
        }
    }
}


if (!function_exists('replace_offer_tags')) {
    function replace_offer_tags($letter, $studentId)
    {
        if (empty($letter)) return $letter;
        $date = format_date("d/m/Y", custom_date_and_time());
        $letter = str_replace('{{offer_letter_date}}', $date, $letter);

        if (empty($studentId)) {
            dev_debug("Student ID is required to replace tags");
            return $letter;
        }
        if (!class_exists('Db_helper')) load_helper('db');
        $db = new Db_helper();

        $location = "COALESCE( db37260, CONCAT_WS(', ', COALESCE(db32336,''), COALESCE(db32200,'') ) )";

        $select = ['{{name}}' => 'db39', '{{surname}}' => 'db40', '{{course}}' => 'db232', '{{term}}' => 'db1681', '{{year}}' => 'db890', '{{location}}' => $location, '{{preferred_name}}' => 'db46'];
        $db->join('core_courses', 'core_courses.id=db889', 'left');
        $db->join('dir_cohorts', 'dir_cohorts.id=db1682', 'left');
        $db->join('core_course_locations', 'core_course_locations.id = db44828', 'left');

        $student = $db->get_row($select, 'core_students', ['core_students.id' => $studentId]);
        dev_debug("student {$studentId} : " . json_encode($student, JSON_PRETTY_PRINT));
        if (!empty($student)) {
            if (strlen($student['{{location}}']) < 3) unset($student['{{location}}']);
            foreach ($student as $tag => $value) {
                if (empty($value) && $tag == '{{preferred_name}}') {
                    $value = $student['{{name}}'];
                }
                if (!empty($value)) $letter = str_replace($tag, $value, $letter);
                else dev_debug("Tag {$tag} has no value");
            }
        } else {
            dev_debug("Unable to find student with ID " . $studentId);
        }
        $letter = offer_leter_tags_replace($letter, $studentId);
        return $letter;
    }
}

if (!function_exists('table_fields_formless')) {
    function table_fields_formless($pg_id, $exclude_fields = [], $args = [])
    {
        $form_templates = new FormTemplates;
        $form_args = ['id' => $pg_id, 'locked_fields_only' => true];
        if (!empty($exclude_fields)) {
            $form_args['exclude_fields'] = $exclude_fields;
        }

        if (!empty($args['usergroup'])) {
            $form_args['school_id'] = $args['usergroup'];
        }


        $template = $form_templates->get($form_args);
        $template['include_js'] = true;


        if (!empty($args['rel_id'])) {
            $template['answers'] = $form_templates->answers(['rel_id' => $args['rel_id'], 'form_name' => $template['table_name']]);
        }

        if (!empty($args['record_id'])) {
            $template['answers'] = $form_templates->answers(['id' => $args['record_id'], 'form_name' => $template['table_name']]);
        }


        if (!empty($_GET['fields_array'])) {
            echo "<pre>" . print_r($template, 1) . '<pre>';
            exit();
        }
        $form_templates->form_html_only($template);


        //$fields= new Fields();
    }
}


if (!function_exists('form_fields_formless')) {
    function form_fields_formless($pg_id, $exclude_fields = [], $args = [])
    {
        $form_templates = new FormTemplates;
        $form_args = ['id' => $pg_id, 'locked_fields_only' => true];
        if (!empty($exclude_fields)) {
            $form_args['exclude_fields'] = $exclude_fields;
        }

        if (!empty($args['usergroup'])) {
            $form_args['school_id'] = $args['usergroup'];
        }


        $template = $form_templates->get_custom_forms($form_args);
        $template['include_js'] = true;


        if (!empty($args['rel_id'])) {
            $template['answers'] = $form_templates->answers(['rel_id' => $args['rel_id'], 'form_name' => $template['table_name']]);
        }

        if (!empty($args['record_id'])) {
            $template['answers'] = $form_templates->answers(['id' => $args['record_id'], 'form_name' => $template['table_name']]);
        }


        if (!empty($_GET['fields_array'])) {
            echo "<pre>" . print_r($template, 1) . '<pre>';
            exit();
        }
        $form_templates->form_html_only($template);


        //$fields= new Fields();
    }
}

if (!function_exists('offer_leter_tags_replace')) {
    function offer_leter_tags_replace($content, $target_id)
    {
        // custom search replace
        $intake_data = explode("/", pull_field("dir_cohorts a,core_students b", "concat(IFNULL(db1678,''),'/',IFNULL(db1679,''),'/',IFNULL(db46274,''), '/', IFNULL(db18718,''))", "  WHERE a.id=b.db1682 AND b.id = '$target_id' AND b.usergroup='$_SESSION[usergroup]' "));
        $currency = '';
        /*$currency = pull_field( 'lead_preferences', 'system_currency.symbol', "JOIN system_currency ON system_currency.id = db33624 WHERE lead_preferences.usergroup = {$_SESSION['usergroup']}" );*/
        $fee = $currency . number_format($intake_data[3] ?? 0, 2);

        if ($_SESSION['usergroup'] == 49) {
            $university_data = explode("|", pull_field("core_students b,core_courses y, core_course_locations_rel r, core_course_locations l,core_contact u", "CONCAT_WS('|',db37260,db37240,db37241,db32336,db32200,db37242,db37243,db37244,db41794)", "WHERE u.id=l.db37244 AND y.id=b.db889 AND r.rel_id=y.id AND r.db32202=l.id AND b.id = '$target_id' AND b.usergroup='$_SESSION[usergroup]' "));
            $student_coodinators_data = explode("|", pull_field("core_students b,dir_offers m, form_users u", "CONCAT_WS('|',db106,db111)", "WHERE m.rel_id=b.id AND m.db26221=u.id AND b.id = '$target_id' AND b.usergroup='$_SESSION[usergroup]' "));
            $school_coodinators_data = explode("|", pull_field("core_students b,dir_offers m, form_users u", "CONCAT_WS('|',db106,db111)", "WHERE m.rel_id=b.id AND m.db26223=u.id AND b.id = '$target_id' AND b.usergroup='$_SESSION[usergroup]' "));
            $dir_offers_course_commencement_date = pull_field("core_students b,dir_offers m", "DATE_FORMAT(db1806,'%d %M %Y')", "WHERE m.rel_id=b.id AND b.id = '$target_id' AND b.usergroup='$_SESSION[usergroup]'");
            $gurantordata = explode("|", pull_field("core_contact", "CONCAT_WS('|',concat(db211,' ',db1092),db42881,db42882,db42883,db42884,db42885,db42886,db42887)", "WHERE id = '$university_data[7]' AND usergroup='$_SESSION[usergroup]' AND (rec_archive IS NULL OR rec_archive= '') "));

            $temp_keysarray = ["Name:", "Tel:", "Address:", "Date of birth:", "Sex:", "Relationship to applicant:", "Profession or occupation and position:", "Nationality and immigration status:"];
            $offerterms = pull_field("core_students b,dir_offers m", "db1800", "WHERE m.rel_id=b.id AND b.id = '$target_id' AND b.usergroup='$_SESSION[usergroup]' ");

            $info = "";
            foreach ($gurantordata as $key => $value) {
                if ($value != "") {
                    $info .= $temp_keysarray[$key] . " " . $value . "\n";
                }
            }
            $university_data[7] = $info;
        } else {
            $university_data = explode("|", pull_field("core_universities c LEFT JOIN core_course_uni_link a ON c.id = a.db31917 LEFT JOIN core_students b ON a.db31916 = b.db889 LEFT JOIN core_course_locations_rel d ON d.rel_id = b.db889 LEFT JOIN core_course_locations e ON e.id = d.db32202", "CONCAT_WS('|',db20163,db27605,db33573,db27604,db29339,db27606,db20164,db41794)", "WHERE a.db31916=b.db889 AND c.id =a.db31917 AND b.id = '$target_id' AND b.usergroup='$_SESSION[usergroup]'  and (a.rec_archive is null or a.rec_archive='')"));
            $dir_offers_course_commencement_date = pull_field("dir_cohorts", "DATE_FORMAT(db1678,'%d %M %Y')", "WHERE id=$dir_offers_intake");
        }

        //get address
        $line1 = "";
        $line1 .= "db1167,";//address line 1
        #$line1.= "db6771,";//address line 2
        #$line1.= "db846,";
        $line1 .= "db845,";//city
        $line1 .= "db848,";//country
        #$line1.= "db1168,";
        #$line1.= "db1169,";
        $line1 .= "db1170";//pcode
        $address = pull_field("dir_registration1", "concat_ws(',',$line1)", "WHERE rel_id=$target_id");
        $address_lines = explode(",", $address);


        $university_name = $university_data[0];

        if ($university_data[1]) {
            $university_address .= ', ' . $university_data[1];
            $university_address1 = $university_data[1];
        }
        if ($university_data[2]) {
            $university_address .= ', ' . $university_data[2];
            $university_address2 = $university_data[2];
        }
        if ($university_data[3]) {
            $university_address .= ', ' . $university_data[3];
            $university_address3 = $university_data[3];
        }
        if ($university_data[4]) {
            $university_address .= ', ' . $university_data[4];
            $university_address4 = $university_data[4];
        }
        if ($university_data[5]) {
            $university_address .= ', ' . $university_data[5];
            $university_address5 = $university_data[5];
        }
        if ($university_data[6]) {
            $university_telephone = $university_data[6];
        }
        if ($university_data[7]) {
            $university_main_contact = $university_data[7];
        }
        if ($university_data[8]) {
            $location_additional_information = $university_data[8];
        }


        $university_address = trim($university_address, ",");

        //$content = replace_offer_tags($content, $target_id);

        $content = str_replace("{{course_intake_start_date}}", format_date("j F Y", $intake_data[0]), $content);
        $content = str_replace("{{course_commencement_date}}", $dir_offers_course_commencement_date, $content);
        $content = str_replace("{{course_intake_end_date}}", format_date("j F Y", $intake_data[1]), $content);
        $content = str_replace("{{student_university_name}}", $university_name, $content);
        $content = str_replace("{{date}}", format_date("j F Y", custom_date_and_time()), $content);
        $content = str_replace("{{university_full_address}}", $university_address, $content);

        //tags on template 161
        $content = str_replace("{{applicant.course.locations[0].address_line_1}}", $university_address1, $content);
        $content = str_replace("{{applicant.course.locations[0].address_line_2}}", $university_address2, $content);
        $content = str_replace("{{applicant.course.locations[0].city}}", $university_address3, $content);
        $content = str_replace("{{applicant.course.locations[0].country}}", $university_address4, $content);
        $content = str_replace("{{applicant.course.locations[0].post_code}}", $university_address5, $content);
        $content = str_replace("{{applicant.course.locations[0].telephone}}", $university_telephone, $content);
        $content = str_replace("{{applicant.course.locations[0].main_contact}}", nl2br($university_main_contact), $content);
        $content = str_replace("{{applicant.intake.start_date}}", format_date("j F Y", $intake_data[0]), $content);
        $content = str_replace("{{applicant.intake.fee}}", $fee, $content);
        $content = str_replace("{{offer_student_coordinator_first_name}}", $student_coodinators_data[0], $content);
        $content = str_replace("{{offer_student_coordinator_last_name}}", $student_coodinators_data[1], $content);
        $content = str_replace("{{offer_school_coordinator_first_name}}", $school_coodinators_data[0], $content);
        $content = str_replace("{{offer_school_coordinator_last_name}}", $school_coodinators_data[1], $content);
        $content = str_replace("{{applicant.course.locations[0].additional_information}}", $location_additional_information, $content);
        $content = str_replace("{{offer_terms_go_here}}", $offerterms, $content);

        $content = str_replace("{{student_address}}", $address_lines, $content);
        #$content = str_replace("{{student_city}}",$student_city,$content);
        #$content = str_replace("{{student_country}}",$student_city,$content);
        $content = str_replace("{{course_intake_study_duration}}", $intake_data[2], $content);


        $parent = explode("|", pull_field('form_users', 'concat_ws("|", db106,db111,db119)', "WHERE id= (SELECT rec_id FROM core_students WHERE id='{$target_id}') AND db112='7'"));
        $child_opts = array('is_child' => false);
        if (!empty($parent)) {
            $child_opts['is_child'] = true;
        }

        if (strrpos($content, '{{preferred_label_salutation}}') != false) {
            if ($child_opts['is_child']) {
                $preferred_label_salutation = pull_field('core_student_parent_links', 'db74258', "WHERE rel_id= '$target_id' AND  usergroup = '$_SESSION[usergroup]' AND db73805 ='yes' AND (rec_archive IS NULL OR rec_archive='') ORDER BY db73805 DESC");
            } else {
                $preferred_label_salutation = pull_field('form_communication_option', 'db57293', "WHERE rel_id= '$target_id' AND  usergroup = '$_SESSION[usergroup]'");
            }

            if ($preferred_label_salutation == "yes") {
                //Use Joint Label Salutation
                if ($child_opts['is_child']) {
                    $preferred_label_salutation = pull_field('core_student_parent_links', 'db73808', "WHERE rel_id= '$target_id' AND  usergroup = '$_SESSION[usergroup]' AND db73805 ='yes' AND (rec_archive IS NULL OR rec_archive='')  ORDER BY db73805 DESC");
                } else {
                    $preferred_label_salutation = pull_field('form_communication_option', 'db57290', "WHERE rel_id= '$target_id' AND  usergroup = '$_SESSION[usergroup]'");
                }
            } else {
                //Use Single  Label Salutation
                if ($child_opts['is_child']) {
                    $preferred_label_salutation = pull_field('core_student_parent_links', 'db74255', "WHERE rel_id= '$target_id' AND  usergroup = '$_SESSION[usergroup]' AND db73805 ='yes' AND (rec_archive IS NULL OR rec_archive='')  ORDER BY db73805 DESC");
                } else {
                    $preferred_label_salutation = pull_field('form_communication_option', 'db57288', "WHERE rel_id= '$target_id' AND  usergroup = '$_SESSION[usergroup]'");
                }
            }
            $content = str_replace("{{preferred_label_salutation}}", $preferred_label_salutation, $content);
        }

        //PREFERRED SALUTATIONS
        if (strrpos($content, '{{preferred_letter_salutation}}') != false) {
            if ($child_opts['is_child']) {
                $preferred_letter_salutation = pull_field('core_student_parent_links', 'db74258', "WHERE rel_id= '$target_id' AND  usergroup = '$_SESSION[usergroup]' AND db73805 ='yes' AND (rec_archive IS NULL OR rec_archive='')  ORDER BY db73805 DESC");
            } else {
                $preferred_letter_salutation = pull_field('form_communication_option', 'db57293', "WHERE rel_id= '$target_id' AND  usergroup = '$_SESSION[usergroup]'");
            }
            if ($preferred_letter_salutation == "yes") {
                //Use Joint Letter Salutation
                if ($child_opts['is_child']) {
                    $preferred_letter_salutation = pull_field('core_student_parent_links', 'db74261', "WHERE rel_id= '$target_id' AND  usergroup = '$_SESSION[usergroup]' AND db73805 ='yes' AND (rec_archive IS NULL OR rec_archive='')  ORDER BY db73805 DESC");
                } else {
                    $preferred_letter_salutation = pull_field('form_communication_option', 'db57289', "WHERE rel_id= '$target_id' AND  usergroup = '$_SESSION[usergroup]'");
                }
            } else {
                //Use Single  Letter Salutation
                if ($child_opts['is_child']) {
                    $preferred_letter_salutation = pull_field('core_student_parent_links', 'db74252', "WHERE rel_id= '$target_id' AND  usergroup = '$_SESSION[usergroup]' AND db73805 ='yes' AND (rec_archive IS NULL OR rec_archive='')  ORDER BY db73805 DESC");
                } else {
                    $preferred_letter_salutation = pull_field('form_communication_option', 'db57287', "WHERE rel_id= '$target_id' AND  usergroup = '$_SESSION[usergroup]'");
                }
            }
            $content = str_replace("{{preferred_letter_salutation}}", $preferred_letter_salutation, $content);
        }
        if (strrpos($content, '{{primary_contact_first_name}}') != false || strrpos($content, '{{primary_contact_surname}}') != false || strrpos($content, '{{primary_contact_second_name}}') != false) {
            $contact_info = explode('|', pull_field('core_student_parent_links l', "concat_ws('|', db12157 ,db12159,db12160)", "LEFT JOIN core_parents p  ON l.db64491=p.id WHERE l.rel_id= '$target_id' AND  l.usergroup = '$_SESSION[usergroup]' AND db73805 ='yes' AND (l.rec_archive IS NULL OR l.rec_archive='')  ORDER BY db73805 DESC"));
            $content = str_replace('{{primary_contact_first_name}}', $contact_info[0], $content);
            $content = str_replace('{{primary_contact_second_name}}', $contact_info[1], $content);
            $content = str_replace('{{primary_contact_surname}}', $contact_info[2], $content);
        }

        return $content;
    }
}

if (!function_exists('start_form_jobs')) {
    function start_form_jobs($uri)
    {
        require_once($_SERVER['DOCUMENT_ROOT'] . '/vendor/autoload.php');
        $base = "https://{$_SERVER['HTTP_HOST']}";
        if (46 == $_SESSION['usergroup']) {
            //$_GET['debug_mode']="yes";
            dev_debug("In start_form_jobs ");
        }
        try {
            $client = new GuzzleHttp\Client(['base_uri' => $base, 'verify' => false, 'timeout' => 5]); // maybe 2 seconds was too short on uat for the connection to be actually established.
            //$client = new GuzzleHttp\Client(['base_uri' => $base]);
            $response = $client->request('GET', $uri);
            //$promise = $client->getAsync('api/cronjobs/Cron_run_scheduled_user_imports.php'); // job not starting
            dev_debug("response $base **" . $response->getBody());
            return true;
        } catch (Exception $e) {
            dev_debug("Exception $base **" . $e->getMessage());
            return $e->getMessage();
        }
        if (46 == $_SESSION['usergroup']) {
            $_GET['debug_mode'] = "no";
        }
    }
}

if (!function_exists('makeUrltoLink')) {
    function makeUrltoLink($string)
    {
        // The Regular Expression filter
        $reg_pattern = "/(\[[\w\s]+])(((http|https|ftp|ftps)\:\/\/)|(www\.))[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,3}(\:[0-9]+)?(\/\S*)?/";
        // make the urls to hyperlinks
        return preg_replace_callback($reg_pattern, function ($match) {
            return '<a href="' . str_replace($match[1], '', $match[0]) . '" class="btn btn-default btn-sm resource_file_dw" target="_blank"><i class="fa fa-download"></i>' . str_replace(['[', ']'], ['', ''], $match[1]) . '</a>';
        }, $string);
    }
}

if (!function_exists('get_student_avatar')) {
    function get_student_avatar($sid, $array = false)
    {
        $dbh = get_dbh();
        $sql = "SELECT form_users.db1730 FROM form_users INNER JOIN core_students ON form_users.id=core_students.rec_id WHERE core_students.id = '$sid' AND form_users.db112='4'";

        dev_debug("get_student_avatar=" . $sql);
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        list($avatar) = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!is_null($avatar) and $avatar !== '') {
            return engine_url . "/media/dl.php?fl=" . encode($avatar);
        }

        $meta = pull_field("dir_core_students_meta", "CONCAT(id,'|',db62732)", "WHERE rel_id='{$sid}' AND usergroup={$_SESSION['usergroup']}");
        $student_meta = $meta ? explode("|", $meta) : [];
        if (isset($student_meta[1])) {
            $url = engine_url . "/media/dl.php?fl=" . encode($student_meta[1]);
            return $array ? ['id' => $student_meta[0], 'avatar' => $url] : $url;
        }

        dev_debug("get_student_avatar =avatar NOT found");
        $sql = "SELECT db44 FROM core_students WHERE id='$sid'";
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        list($gender) = $stmt->fetch(PDO::FETCH_ASSOC);
        $gender = strtolower($gender);

        if (!$gender) {
            return engine_url . "/images/male_icon.jpg";
        } elseif ($gender == "other") {
            return engine_url . "/images/male_icon.jpg";
        } else {
            return engine_url . "/images/{$gender}_icon.jpg";
        }
    }
}


if (!function_exists('get_application_campaign')) {
    function get_application_campaign($core_students_id)
    {
        $dbh = get_dbh();
        $sql = "SELECT *,(SELECT db232 FROM core_courses WHERE id=db889) AS course  FROM core_students WHERE usergroup='$_SESSION[usergroup]' AND (id='$core_students_id') AND (core_students.rec_archive IS NULL OR core_students.rec_archive ='' ) ORDER BY id ASC";
        $app = $dbh->prepare($sql);
        $app->execute();
        $applicant_info = $app->fetch(PDO::FETCH_ASSOC);

        $studentsModel = new Students;
        $campaign_id = $studentsModel->get_applicant_navigate_page($applicant_info, true);

        return $campaign_id;
    }
}
if (!function_exists("uploadFile")) {
    function uploadFile($args)
    {
        $dbh = get_dbh();
        $server_domain_name = env('APP_URL');
        $target_path = "/var/www/vhosts/$server_domain_name/private/media/";

        // For example, pass in 'storageOption' => 'vault' in $args if you want to upload to vault
        // e.g., $args['storageOption'] = 'vault';
        $storageOption = isset($args['storageOption']) ? $args['storageOption'] : 'vault';

        $imageFileType = strrchr(basename($_FILES['value']['name']), '.');
        $upload_error = -27;

        // If no filename was provided in db204, generate one:
        if (empty($args['db204'])) {
            $path = $args['directory'];
            $target = "";
            $uploaded_file_name = random() . $imageFileType;
            $file_name = stripslashes($uploaded_file_name);
            $args['db204'] = $path . "/" . $file_name;
        }

        // If the directory doesn’t exist, create it
        if (!empty($args['directory'])) {
            $direct = $target_path . $args['directory'];
            if (!is_dir($direct)) {
                $old_umask = umask(0);
                mkdir("$direct", 0755, true);
                umask($old_umask);
            }
        }

        // Example limit check (5MB).  Adjust as needed or remove.
        if ($_FILES['uploadFile']['size'] > 5000000) {
            $upload_error = 1;
        }

        // Basic extension checks. (You’d probably want something more robust.)
        if (
            $imageFileType != ".jpg"
            && $imageFileType != ".png"
            && $imageFileType != ".jpeg"
            && $imageFileType != ".gif"
        ) {
            $upload_error = 1;
        }

        if ($upload_error < 1) {
            // Only proceed if we have a valid target path
            if ($target_path) {
                // Make sure the parent folder exists
                if (!is_dir($target_path)) {
                    $old_umask = umask(0);
                    mkdir("$target_path", 0755, true);
                    umask($old_umask);
                }

                // Full local path we’re writing to
                $target = $target_path . $args['db204'];

                dev_debug("target=" . $target);

                $copy = false; // track success/failure
                try {
                    // Move the file to the local server path
                    $copy = move_uploaded_file($_FILES['value']['tmp_name'], $target);
                } catch (Exception $e) {
                    var_dump($e);
                }

                if (!$copy) {
                    $file_name = basename($args['db204']);
                    $error_msg_text = "$file_name - Your Image File Could Not Be Uploaded!";
                    echo json_encode([
                        'success' => false,
                        'message' => $error_msg_text . " path :" . $target
                    ]);
                    exit();
                } else {
                    // File has been successfully moved locally.
                    // If the user wants to store in vault, do that now.
                    if ($storageOption === 'vault') {
                        // $fileStorage presumably is your own S3 helper object, e.g. $fileStorage = new FileStorage();
                        global $fileStorage;

                        // Build the Key used in the vault
                        // e.g. remove leading slashes, etc.:
                        $key = "media/" . ltrim($args['db204'], '/');

                        try {
                            $file = $fileStorage->putToS3([
                                'Key' => $key,
                                'Body' => file_get_contents($target)
                            ]);
                            // If upload is successful, remove local file:
                            //echo  $key ;exit();
                            unlink($target);
                        } catch (Exception $e) {
                            // If you can’t upload to vault, either revert or keep local.
                            // Decide how you want to handle the exception.
                            dev_debug("Vault upload error: " . $e->getMessage());
                        }
                    }

                    // Insert reference into `form_file` table
                    $username_id = random();
                    $rec_id = session_info("uid");
                    $db199 = $args['db199'];
                    $db200 = $args['db200'] ?? '';
                    $db201 = $args['db201'] ?? '';
                    $db202 = $args['db202'] ?? '';
                    $db203 = $args['db203'] ?? '';
                    $db204 = $args['db204'];

                    $sql = "INSERT INTO form_file 
                            (username_id, rec_id, usergroup, rel_id, db199, db200, db201, db202, db203, db204)
                            VALUES 
                            (:username_id, :rec_id, :usergroup, :rel_id, :db199, :db200, :db201, :db202, :db203, :db204)";

                    $sth = $dbh->prepare($sql);
                    $sth->execute([
                        ':username_id' => $username_id,
                        ':rec_id' => $rec_id,
                        ':usergroup' => $_SESSION['usergroup'],
                        ':rel_id' => '',
                        ':db199' => $db199,
                        ':db200' => $db200,
                        ':db201' => $db201,
                        ':db202' => $db202,
                        ':db203' => $db203,
                        ':db204' => $db204
                    ]);
                }
            }
        }

        // Return the final path (either local or the path you store in vault)
        return $args['db204'];
    }
}


if (!function_exists('fix_newlines')) {
    function fix_newlines($text)
    {
        // replace \r\n to \n
        $text = str_replace("\r\n", "\n", $text);
        // remove \rs
        $text = str_replace("\r", "\n", $text);

        return $text;
    }
}

if (!function_exists('iterate_over_node')) {
    function iterate_over_node($node)
    {
        if ($node instanceof DOMText) {
            return preg_replace("/\\s+/im", " ", $node->wholeText);
        }
        if ($node instanceof DOMDocumentType) {
            // ignore
            return "";
        }

        $nextName = next_child_name($node);
        $prevName = prev_child_name($node);

        $name = strtolower($node->nodeName);

        // start whitespace
        switch ($name) {
            case "hr":
                return "------\n";

            case "style":
            case "head":
            case "title":
            case "meta":
            case "script":
                // ignore these tags
                return "";

            case "h1":
            case "h2":
            case "h3":
            case "h4":
            case "h5":
            case "h6":
                // add two newlines
                $output = "\n";
                break;

            case "p":
            case "div":
                // add one line
                $output = "\n";
                break;

            default:
                // print out contents of unknown tags
                $output = "";
                break;
        }

        // debug
        //$output .= "[$name,$nextName]";

        for ($i = 0; $i < $node->childNodes->length; $i++) {
            $n = $node->childNodes->item($i);

            $text = iterate_over_node($n);

            $output .= $text;
        }

        // end whitespace
        switch ($name) {
            case "style":
            case "head":
            case "title":
            case "meta":
            case "script":
                // ignore these tags
                return "";

            case "h1":
            case "h2":
            case "h3":
            case "h4":
            case "h5":
            case "h6":
                $output .= "\n";
                break;

            case "p":
            case "br":
                // add one line
                if ($nextName != "div")
                    $output .= "\n";
                break;

            case "div":
                // add one line only if the next child isn't a div
                if ($nextName != "div" && $nextName != null)
                    $output .= "\n";
                break;

            case "a":
                // links are returned in [text](link) format
                $href = $node->getAttribute("href");
                if ($href == null) {
                    // it doesn't link anywhere
                    if ($node->getAttribute("name") != null) {
                        $output = "[$output]";
                    }
                } else {
                    if ($href == $output) {
                        // link to the same address: just use link
                        $output;
                    } else {
                        // replace it
                        $output = "[$output]($href)";
                    }
                }

                // does the next node require additional whitespace?
                switch ($nextName) {
                    case "h1":
                    case "h2":
                    case "h3":
                    case "h4":
                    case "h5":
                    case "h6":
                        $output .= "\n";
                        break;
                }

            default:
                // do nothing
        }

        return $output;
    }
}

if (!function_exists('next_child_name')) {
    function next_child_name($node)
    {
        // get the next child
        $nextNode = $node->nextSibling;
        while ($nextNode != null) {
            if ($nextNode instanceof DOMElement) {
                break;
            }
            $nextNode = $nextNode->nextSibling;
        }
        $nextName = null;
        if ($nextNode instanceof DOMElement && $nextNode != null) {
            $nextName = strtolower($nextNode->nodeName);
        }

        return $nextName;
    }
}

if (!function_exists('prev_child_name')) {
    function prev_child_name($node)
    {
        // get the previous child
        $nextNode = $node->previousSibling;
        while ($nextNode != null) {
            if ($nextNode instanceof DOMElement) {
                break;
            }
            $nextNode = $nextNode->previousSibling;
        }
        $nextName = null;
        if ($nextNode instanceof DOMElement && $nextNode != null) {
            $nextName = strtolower($nextNode->nodeName);
        }

        return $nextName;
    }
}


if (!function_exists("getAge")) {
    /*--------------------------------------------------
// CALCULATE AGE
---------------------------------------------------- */
    function getAge($Birthdate)
    {

        //return floor((time() - strtotime($dob))/(60*60*24*365.2425));
        if (empty($Birthdate)) {
            return false;
        }

        list($BirthYear, $BirthMonth, $BirthDay) = explode("-", $Birthdate);
        $YearDiff = date("Y") - $BirthYear;
        $MonthDiff = date("m") - $BirthMonth;
        $DayDiff = date("d") - $BirthDay;

        // If the birthday has not occured this year
        if ($MonthDiff == 0) {
            if ($DayDiff < 0)
                $YearDiff--;
        }

        if ($MonthDiff < 0) {
            $YearDiff--;
        }
        return $YearDiff;
    }
}

if (!function_exists('courseStartSearch')) {
    function courseStartSearch($value, $join = '('): string
    {
        //var_dump($value);
        return (" {$join} (EXISTS (SELECT db14978 FROM core_students
            JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
            JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
            JOIN sis_course_schedule ON sis_course_schedule.id = db14977 WHERE core_students.rel_id = sis_profiles.rel_id
            AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
            AND (db64664 = '1' OR db64664 = '2') AND db14978  between '{$value[0]}' AND '{$value[1]}' )");
    }
}


if (!function_exists('courseStartSearch_or')) {
    function courseStartSearch_or($value): string
    {
        return courseStartSearch($value, "");
    }
}

if (!function_exists('courseSessionStartSearch')) {
    function courseSessionStartSearch($value, $join = '('): string
    {
        return (" {$join} (EXISTS (SELECT db14947 FROM core_students
            JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
            JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
            JOIN sis_course_schedule ON sis_course_schedule.id = db14977 
            WHERE core_students.rel_id = sis_profiles.rel_id
            AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
            AND (db59978 = '1' OR db59978 = '2' OR db59978 = '14') AND db14947  between '{$value[0]}' AND '{$value[1]}' ) ) 
            OR (EXISTS (SELECT db59835 FROM core_students
            JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
            JOIN sis_session_bookings ON sis_session_bookings.rel_id = convert(cast(core_students.id as CHAR) using utf8)
            JOIN sis_scheduled_sessions ON sis_scheduled_sessions.id = CAST(sis_session_bookings.db59901 AS SIGNED)
            WHERE core_students.rel_id = sis_profiles.rel_id
            AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
            AND (sis_session_bookings.rec_archive IS NULL OR sis_session_bookings.rec_archive = '')
            AND (db59902 = '1' OR db59902 = '2' OR db59902 = '14') AND db59835  between  '{$value[0]}' AND '{$value[1]}' )");
    }
}


if (!function_exists('courseSessionStartSearch_or')) {
    function courseSessionStartSearch_or($value): string
    {
        return courseSessionStartSearch($value, "");
    }
}

if (!function_exists('initialILPSearch')) {
    function initialILPSearch($value, $join = '('): string
    {
        return (" {$join}  (SELECT db61500 FROM sis_ind_learner_plan 
                                    WHERE sis_ind_learner_plan.rel_id = convert(cast(sis_profiles.rel_id as CHAR) using utf8)
                                    AND (sis_ind_learner_plan.rec_archive IS NULL OR sis_ind_learner_plan.rec_archive = '')
                                    AND db61498 = 'inital_ILP' AND db61500  between '{$value[0]}' AND '{$value[1]}'
                                    ORDER BY db61500 DESC LIMIT 1");

    }
}


if (!function_exists('initialILPSearch_or')) {
    function initialILPSearch_or($value): string
    {
        return initialILPSearch($value, "");
    }
}

if (!function_exists('lastILPReviewSearch')) {
    function lastILPReviewSearch($value, $join = '('): string
    {
        return (" {$join}  (SELECT db61500 FROM sis_ind_learner_plan 
                                    WHERE sis_ind_learner_plan.rel_id = convert(cast(sis_profiles.rel_id as CHAR) using utf8)
                                    AND (sis_ind_learner_plan.rec_archive IS NULL OR sis_ind_learner_plan.rec_archive = '')
                                    AND db61498 != 'inital_ILP' AND db61500  between '{$value[0]}' AND '{$value[1]}'
                                    ORDER BY db61500 DESC LIMIT 1");

    }
}


if (!function_exists('lastILPReviewSearch_or')) {
    function lastILPReviewSearch_or($value): string
    {
        return lastILPReviewSearch($value, "");
    }
}

if (!function_exists('hasBookedOn')) {
    function hasBookedOn($value, $join = '('): string
    {
        //var_dump($value);
        return (" {$join} (EXISTS (SELECT db16136 FROM core_students
            JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
            JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
            JOIN sis_course_schedule ON sis_course_schedule.id = db14977 WHERE core_students.rel_id = sis_profiles.rel_id
            AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
            AND db16136  = '{$value}')");
    }
}

if (!function_exists('hasNotBookedOn')) {
    function hasNotBookedOn($value, $join = '('): string
    {
        //var_dump($value);
        return (" {$join} (NOT EXISTS (SELECT db16136 FROM core_students
            JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
            JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
            JOIN sis_course_schedule ON sis_course_schedule.id = db14977 WHERE core_students.rel_id = sis_profiles.rel_id
            AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
            AND db16136  = '{$value}')");
    }
}

if (!function_exists('hasBookedOn_or')) {
    function hasBookedOn_or($value): string
    {
        return hasBookedOn($value, "");
    }
}

if (!function_exists('hasNotBookedOn_or')) {
    function hasNotBookedOn_or($value): string
    {
        return hasNotBookedOn($value, "");
    }
}


if (!function_exists('hasAttended')) {
    function hasAttended($value, $join = '('): string
    {
        //var_dump($value);
        return (" {$join} (EXISTS (SELECT db16136 FROM core_students
            JOIN sis_sched_booking_detail ON sis_sched_booking_detail.rel_id = convert(cast(core_students.id as CHAR) using utf8)
            JOIN sis_scheduled_booking ON sis_scheduled_booking.id = sis_sched_booking_detail.db15052
            JOIN sis_course_schedule ON sis_course_schedule.id = db14977 WHERE core_students.rel_id = sis_profiles.rel_id
            AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '')
            AND (db64664 = '1' OR db64664 = '2') AND db16136  = '{$value}')");
    }
}


if (!function_exists('hasAttended_or')) {
    function hasAttended_or($value): string
    {
        return hasAttended($value, "");
    }
}

if (!function_exists('get_page_list_based_on_rules')) {
    /**
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    function get_page_list_based_on_rules($student_id_val = '', $supplementary_required = false, $return_ruleid = false, $admin_only_forms = false)
    {

        //if non use candidate id
        if ($student_id_val == '') {
            $student_id_val = $_SESSION['student_id'];
        }

        //START check if student id exists
        if ($student_id_val) {
            //get rules for usergroup
            $dbh = get_dbh();
            $page_rule_query = "
                SELECT db_field_name AS 'name', pr.id AS 'rule_id', db19262 AS 'condition' 
                FROM dir_page_rules pr INNER JOIN system_table ON form_id=db19266
                WHERE (pr.rec_archive IS NULL or pr.rec_archive='')
                AND pr.usergroup = $_SESSION[usergroup] ORDER BY db19264 ASC
            ";
            $sql = $dbh->prepare($page_rule_query);
            dev_debug($page_rule_query);
            $sql->execute();
            $page_rules = $sql->fetchAll(PDO::FETCH_OBJ);
            //loop through rules for usergroup
            $pages_id = $ret_rule_id = "";

            foreach ($page_rules as $page_rule) {

                $column = "db19263";
                if ($supplementary_required) {
                    $column = "concat(db19263,',',IFNULL(db33769,''))";
                }
                if ($admin_only_forms) {
                    $column = "concat(" . $column . ",',',IFNULL(db166655,''))";
                }
                if ($pages_id == "") {
                    if ($page_rule->condition == "default") {
                        $query_3 = "
                            SELECT $column
                            FROM dir_page_rules pr
                            WHERE  pr.id = $page_rule->rule_id
							AND (pr.rec_archive IS NULL or pr.rec_archive='')
					        ORDER BY db19264
					    ";
                        dev_debug($query_3);
                        $sql = $dbh->prepare($query_3);
                    } else {
                        //pull the actual field value
                        $page_rule_value = pull_field("core_students", "$page_rule->name", "WHERE id = $student_id_val");

                        //test the rule
                        $sql_val = "SELECT $column
					        FROM dir_page_rules pr
							INNER JOIN core_students cs ON db19262 = '$page_rule_value'
					        WHERE $_SESSION[usergroup]
							AND cs.id=$student_id_val
							AND pr.id = $page_rule->rule_id
							AND (pr.rec_archive IS NULL or pr.rec_archive='')
					        ORDER BY db19264
					    ";
                        dev_debug($sql_val);
                        $sql = $dbh->prepare("$sql_val");

                    }
                    $sql->execute();
                    $result = $sql->fetchColumn();
                    if ($result) {
                        $ids = array_filter(explode(",", $result));
                        $pages_id = implode(',', $ids);
                        $ret_rule_id = $page_rule->rule_id;
                    }
                } else {
                    //do nothing
                }
            }

            if ($return_ruleid) {
                return $ret_rule_id;
            } else {
                return $pages_id;
            }

        } //END check if student id exists


    } //end function

}

if (!function_exists('take_to_background_process')) {
    function take_to_background_process($final_results, $category, $display_messaage, $return_values = false, $add_details = [])
    {
        $dir = media_store . 'reports/';
        $session = $_SESSION;
        if (!empty($session['debugbar'])) unset($session['debugbar']);
        $exp_temphoder = uniqid();
        $payload = [
            'category' => $category,
            'request' => $_REQUEST,
            'get' => $_GET,
            'post' => $_POST,
            'session' => $session,
            'tracker_file' => $dir . $exp_temphoder . '_files_traker.txt',
            'final_results' => $final_results
        ];

        if (!empty($add_details) && is_array($add_details)) {
            foreach ($add_details as $key => $value) {
                $payload[$key] = $value;
            }
        }


        $store_link = $dir . $exp_temphoder . ".json";
        file_put_contents($store_link, json_encode($payload));
        $dbh = get_dbh();
        $stmt = $dbh->prepare("INSERT INTO form_jobs(rec_id,rel_id,usergroup,`date`,rec_lstup,db57005) VALUES (?,?,?,NOW(),NOW(),?)");
        $stmt->execute([$_SESSION['uid'], $_SESSION['uid'], $_SESSION['usergroup'], json_encode(['store_link' => $store_link])]);
        $id = $dbh->lastInsertId();
        $started = start_form_jobs('admin/cronjob/export_custom_report?id=' . $id);
        if (!$return_values) {
            echo json_encode(['tracker_file' => $dir . $exp_temphoder . '_files_traker.txt', 'message' => $display_messaage . '
        <p id="pprog" hidden>Processed <span id="processed"></span> <span>/</span> <span id="total"></span> records</p>
        <div id="myProgress" hidden>
          <div id="myBar"></div>
        </div>']);
        } else {
            return ['tracker_file' => $dir . $exp_temphoder . '_files_traker.txt', 'store_link' => $store_link];
        }

    }
}

if (!function_exists('log_bulk_action_progress')) {
    function log_bulk_action_progress($store_link, $payload)
    {
        file_put_contents($store_link, json_encode($payload));
    }
}

if (!function_exists('getNextApplicantStagePages')) {
    /**
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    function getNextApplicantStagePages($args): string
    {
        $dbh = get_dbh();
        $core_student_current_stage = $args['stage'];
        $campaign_id = $args['campaign_id'];
        ///now get the pages for this stage
        $currentStageSql = $campaignSql = '';
        if (empty($args['allCampaigns'])) {
            if (empty($campaign_id) || empty(trim($core_student_current_stage))) {
                return '';
            }

            $currentStageSql = "AND dir_page_rules_stages.db85664 IN({$core_student_current_stage})";
            $campaignSql = " AND dir_page_rules_stages.rel_id='{$campaign_id}'";
        }

        $stage_sql = "SELECT dir_page_rules_multiple.db85673 AS 'page_id'
            FROM dir_page_rules_multiple
            LEFT JOIN dir_page_rules_stages on dir_page_rules_stages.id=db85670
            WHERE 1
            {$currentStageSql}
            AND dir_page_rules_stages.db85667='yes'
            AND dir_page_rules_stages.usergroup='$_SESSION[usergroup]'
            {$campaignSql}
        ";
        //   echo $stage_sql;
        dev_debug($stage_sql);
        $stages_sth = $dbh->prepare($stage_sql);
        $stages_sth->execute();
        $stages_raw_results = $stages_sth->fetchAll();
        dev_debug(json_encode($stages_raw_results));
        $raw_stage_pages = array();
        foreach ($stages_raw_results as $result) {
            $raw_stage_pages[] = $result['page_id'];
        }

        if (empty($args['firstPage'])) {
            $stage_pages = implode(',', $raw_stage_pages);
            dev_debug(json_encode($stage_pages));
            return $stage_pages;

        } else {
            return pull_field('form_cms', 'db647', "WHERE id={$stages_raw_results[0]['page_id']}");
        }

    }
}

if (!function_exists('removeEmptyKeysFromJson')) {
    function removeEmptyKeysFromJson($json)
    {
        // Decode the JSON string into an associative array
        $array = json_decode($json, true);

        // Remove empty keys from the array
        $array = removeEmptyKeys($array);

        // Encode the array back to a JSON string
        return json_encode($array);
    }
}


// Function to recursively remove empty keys
if (!function_exists('removeEmptyKeys')) {

    function removeEmptyKeys(array $array)
    {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $array[$key] = removeEmptyKeys($value);
            }
            if ($value === "" || $value === null || $value === [] || $value === false) {
                unset($array[$key]);
            }
        }
        return $array;
    }


}
if (!function_exists('get_sms_status_codes')) {
    function get_sms_status_codes()
    {

        $dbh = get_dbh();
        $query = "SELECT db211145 as value, db211148 as label FROM coms_sms_status_codes WHERE (rec_archive is null OR rec_archive = '')  ORDER BY db211145 ASC";
        dev_debug($query);
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}


if (!function_exists('get_reference_questions_data')) {
    function get_reference_questions_data($form_id, $reference_field, $reference_id)
    {
        if (empty($form_id) || empty($reference_field) || empty($reference_id)) return [];
        if (!class_exists('Db_helper')) load_helper('db');
        $db = new Db_helper();
        if ('4' == $_SESSION['ulevel']) {
            return [];
        }

        $select = "archive,status,pg_id,db_field_name,name, type";
        $db->where_not_in('system_table.type', ['hidden_session']);
        $db->join('system_table', 'system_table.form_id = system_table_id', 'inner');
        $db->order_by("order_by");
        $fields = $db->get_rows($select, 'system_form_fields', ['system_forms_id' => $form_id]);
        if (empty($fields)) {
            dev_debug("get_reference_questions_data: unable to find form fields");
            return [];
        }

        $page_id = $fields[0]['pg_id'] ?? '';
        if (empty($page_id)) {
            dev_debug("get_reference_questions_data: unable to find page ID");
            return [];
        }

        $db->join('system_cat', 'sys_cat_id = system_pages.project', 'inner');
        $table = $db->fetch_field("concat(sys_cat_abv,'_',page_name)", 'system_pages', ['page_id' => $page_id]);

        if (empty($table)) {
            dev_debug("get_reference_questions_data: unable to find table");
            return [];
        }

        $db->where('usergroup', $_SESSION['usergroup']);
        $data = $db->get_row('*', $table, [$reference_field => $reference_id, "(rec_archive is null OR  rec_archive !='')"]);
        dev_debug("get_reference_questions_data: " . json_encode($data, JSON_PRETTY_PRINT));
        $mapped = [];
        foreach ($fields as $f) {
            $key = $f['db_field_name'];
            if (isset($data[$key]) && empty($f['archive']) && $f['status'] == 'published') {
                $mapped[$key] = ['title' => strip_tags($f['name']), 'value' => $data[$key], 'field_type' => $f['type']];
            } else {
                //return blank for applications list view
                $mapped[$key] = ['title' => strip_tags($f['name']), 'value' => ''];
            }
        }
        dev_debug("get_reference_questions_data: " . json_encode($mapped, JSON_PRETTY_PRINT));
        return $mapped;
    }
}


if (!function_exists('get_cms')) {
// get cms page and content
    function get_cms($by = 'id', $value = 1, $core_students_level_of_entry = '')
    {

        // if admin show everything not just group
        $usergroups = usergroups_management();

        //if language then show it
        if (isset($_SESSION['lang']) && $_SESSION['lang'] !== '' && $_SESSION['lang'] !== 'en') {
            $lang_check = "AND db47583=(SELECT id FROM form_languages WHERE db21281 ='$_SESSION[lang]' LIMIT 1) ";
        }

        //error_log('usergroup_managemant: '.$usergroups);
        //error_log('core_students_level_of_entry: '.$core_students_level_of_entry);

        $dbh = get_dbh();

        $user_exepmt_ul = array(21, 11); // theses are schools that use the usergroup condition to decide what to show to different users. These UGs will undergo changes soon

        if ($core_students_level_of_entry && in_array($_SESSION['usergroup'], $user_exepmt_ul)) {
            if ($by == 'id') {
                $sql = "SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND $usergroups AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.id != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.id='$value' $lang_check AND $usergroups LIMIT 1";
            } else {
                $sql = "SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND $usergroups AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.db647 != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.db647='$value' $lang_check AND $usergroups LIMIT 1";
            }
        } else {
            if ($by == 'id') {
                $sql = "SELECT *, db863 as 'next_page' FROM form_cms WHERE id='$value' $lang_check AND $usergroups and (rec_archive IS NULL OR rec_archive = '') ORDER BY id DESC LIMIT 1";
            } else {
                $sql = "SELECT *, db863 as 'next_page' FROM form_cms WHERE db647='$value' $lang_check  AND $usergroups and (rec_archive IS NULL OR rec_archive = '')  ORDER BY id DESC LIMIT 1";
            }
        }
        //echo $sql;
        dev_debug("form_cms - check - $sql");
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $num = count($results);

        if ($num == 0) {

            if ($core_students_level_of_entry) {
                if ($by == 'id') {
                    $sql2 = "SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND cms1.usergroup='1' AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.id != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.id='$value' $lang_check AND cms.usergroup='1' LIMIT 1";
                } else {
                    $sql2 = "SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND cms1.usergroup='1' AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.db647 != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.db647='$value' $lang_check AND cms.usergroup='1' LIMIT 1";
                }
            } else {

                // error_log('core_students_level_of_entry1: '."SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND $usergroups AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL)  AND cms1.db748 >= cms.db748  and cms1.id != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.id='$value' AND $usergroups LIMIT 1");
                // error_log('core_students_level_of_entry2: '."SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND $usergroups AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.db647 != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.db647='$value' AND.$usergroups LIMIT 1");
                //error_log('core_students_level_of_entry3: '."SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND cms1.usergroup='1' AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.id != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.id='$value' AND cms.usergroup='1' LIMIT 1");
                //error_log('core_students_level_of_entry4: '."SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND cms1.usergroup='1' AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.db647 != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.db647='$value' AND.cms usergroup='1' LIMIT 1");
                if ($by == 'id') {
                    $sql2 = "SELECT *, db863 as 'next_page' FROM form_cms WHERE id='$value' $lang_check  AND usergroup='1' LIMIT 1";
                } else {
                    $sql2 = "SELECT *, db863 as 'next_page' FROM form_cms WHERE db647='$value' $lang_check AND usergroup='1' LIMIT 1";
                }
            }
            //echo $sql;
            $stmt = $dbh->prepare($sql2);
            $stmt->execute();
            $results2 = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($results2 as $row) {

                $id = $row['id'];
                $cms_category = $row['db656'];
                $cms_page_name = $row['db647'];
                $cms_heading = $row['db648'];
                $cms_brief = $row['db649'];
                $cms_article = $row['db650'];
                $cms_data = $row['db651'];
                $cms_publish = $row['db652'];
                $cms_page_title = $row['db653'];
                $cms_keywords = $row['db654'];
                $cms_page_description = $row['db738'];
                $cms_included_form = $row['db655'];
                $cms_privacy = $row['db739'];
                $cms_refreshpage_on_add = $row['db823'];
                $nextpage_url = $row['next_page'];
                $cms_introductory_instructions = $row['db1198'];
                $cms_pull_file = $row['db1440'];
                $cms_save_action = $row['db9853'];
                $cms_system_form_id = $row['db26231'];
            }
        } else {
            //error_log('core_students_level_of_entry1: '."SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND $usergroups AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.id != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.id='$value' AND $usergroups LIMIT 1");
            // error_log('core_students_level_of_entry2: '."SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND $usergroups AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.db647 != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.db647='$value' AND.$usergroups LIMIT 1");
            // error_log('core_students_level_of_entry3: '."SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND cms1.usergroup='1' AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.id != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.id='$value' AND cms.usergroup='1' LIMIT 1");
            //error_log('core_students_level_of_entry4: '."SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND cms1.usergroup='1' AND )FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.db647 != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.db647='$value' AND.cms usergroup='1' LIMIT 1");


            foreach ($results as $row) {

                $id = $row['id'];
                $cms_category = $row['db656'];
                $cms_page_name = $row['db647'];
                $cms_heading = $row['db648'];
                $cms_brief = $row['db649'];
                $cms_article = $row['db650'];
                $cms_data = $row['db651'];
                $cms_publish = $row['db652'];
                $cms_page_title = $row['db653'];
                $cms_keywords = $row['db654'];
                $cms_page_description = $row['db738'];
                $cms_included_form = $row['db655'];
                $cms_privacy = $row['db739'];
                $cms_refreshpage_on_add = $row['db823'];
                $nextpage_url = $row['next_page'];
                //        $cms_introductory_instructions = $row['db1198'];
                $cms_pull_file = $row['db1440'];
                $cms_save_action = $row['db9853'];
                $cms_system_form_id = $row['db26231'];
                $cms_system_quiz_id = $row['db26741'];
            }
        }

        // check for sessions in field
        $cms_article = session_floating($cms_article);
        $cms_brief = session_floating($cms_brief);
        $cms_heading = session_floating($cms_heading);
        $cms_page_description = session_floating($cms_page_description);
        $cms_introductory_instructions = session_floating($cms_introductory_instructions);

        //check for get queries in field
        $cms_brief = get_floating($cms_brief);
        $cms_article = get_floating($cms_article);
        $cms_heading = get_floating($cms_heading);
        $cms_page_description = get_floating($cms_page_description);

        // look for subdomain and correct it to current session
        $cms_article = str_replace("subdomain", "$_SESSION[subdomain]", $cms_article);

        //LOOK FOR AND CONVERT SHORT CODES 2018-11-28 TAWANDA ONYIMO
        include_once(base_path . "/admin/config.php");
        $online = new OnlineLearning;
        $cms_article = $online->proccess_short_codes($cms_article);
        //$cms_article .= website_url.'app/libs/shortcodes/index.php';
        // add some eye candy tot he instructions
        if (4 == $_SESSION['ulevel'] && !empty($_SESSION['student_id'])) {
            //Apply the email variables
            $template_args = array(
                "email_html" => $cms_article,
                "student_id" => $_SESSION['student_id'],
                "include_assigned_users" => true
            );
            $cms_article = process_email_variables($template_args);
        }

        $non_active_admin = array(2, 3, 9);
        if (in_array($_SESSION['ulevel'], $non_active_admin) && $cms_introductory_instructions !== '') {
            $cms_introductory_instructions = '<div class="info" id="instructions" style="width:60%">' . $cms_introductory_instructions . '</div>';
        } else {
            $cms_introductory_instructions = '';
        }

        return array($id, $cms_category, $cms_page_name, $cms_heading, $cms_brief, $cms_article, $cms_data, $cms_publish, $cms_page_title, $cms_keywords, $cms_page_description, $cms_included_form, $cms_privacy, $cms_refreshpage_on_add, $nextpage_url, $cms_introductory_instructions, $cms_pull_file, $cms_save_action, $cms_system_form_id, $cms_system_quiz_id);
    }
}

if (!function_exists('debug_pdo')) {
    function debug_pdo($sql, $params)
    {
        foreach ($params as $key => $value) {
            // If the parameter is named, prepend a colon
            $placeholder = is_string($key) ? ':' . ltrim($key, ':') : '?';

            // Quote the value if it's a string
            if (is_string($value)) {
                $value = "'" . addslashes($value) . "'";
            } elseif (is_null($value)) {
                $value = 'NULL';
            } elseif (is_bool($value)) {
                $value = $value ? '1' : '0';
            }

            // Replace the first occurrence of the placeholder with the value
            $sql = preg_replace('/' . preg_quote($placeholder, '/') . '/', $value, $sql, 1);
        }

        return $sql;
    }
}
if (!function_exists('get_dir_offers')) {
    /*--------------------------------
// FUNCTION TO GET_DIR_OFFERS
---------------------------------*/
    function get_dir_offers($by = 'unique_id', $value = ''): array
    {

        // if admin show everything not just group
        $usergroups = usergroups_management();

        $dbh = get_dbh();
        if ($by == 'unique_id') {
            $sql = "SELECT * FROM dir_offers WHERE username_id='$value' AND $usergroups ORDER BY id DESC LIMIT 1";
        } else {
            $sql = "SELECT * FROM dir_offers WHERE id='$value' AND $usergroups ORDER BY id DESC LIMIT 1";
        }
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($results as $row) {

            $dir_offers_id = $row['id'];
            $dir_offers_date = $row['date'];
            $dir_offers_rec_id = $row['rec_id'];
            $dir_offers_usergroup = $row['usergroup'];
            $dir_offers_rel_id = $row['rel_id'];
            $dir_offers_offer_title = $row['db1812'];
            $dir_offers_offer_type = $row['db1799'];
            $dir_offers_select_template = $row['db1811'];
            $dir_offers_terms_of_offer = $row['db1800'];
            $dir_offers_course_offered = $row['db1801'];
            $dir_offers_course_code = $row['db1802'];
            $dir_offers_course_level = $row['db1803'];
            $dir_offers_level_of_entry = $row['db1804'];
            $dir_offers_ucas_code = $row['db1805'];
            $dir_offers_course_commencement_date = $row['db1806'];
            $dir_offers_duration_of_course = $row['db1807'];
            $dir_offers_enrolment_and_inductions_date = $row['db1808'];
            $dir_offers_academic_session = $row['db1809'];
            $dir_offers_course_fee = $row['db1810'];
            $dir_offers_course_completion = $row['db1814'];
            $dir_offers_intake = $row['db18720'];
            $dir_offers_username_id = $row['username_id'];
            $dir_offers_additional_1 = str_replace("_", " ", $row['db26221']);
            $dir_offers_additional_2 = str_replace("_", " ", $row['db26223']);
            $dir_offers_additional_3 = str_replace("_", " ", $row['db26224']);
            $dir_offers_additional_4 = str_replace("_", " ", $row['db26225']);
            $dir_offers_terms_and_conditions = str_replace("_", " ", $row['db33623']);
            $dir_offers_date_time_signing = $row['rec_lstup'];
        }

        return array($dir_offers_id, $dir_offers_date, $dir_offers_rec_id, $dir_offers_usergroup, $dir_offers_rel_id, $dir_offers_offer_title, $dir_offers_offer_type, $dir_offers_select_template, $dir_offers_terms_of_offer, $dir_offers_course_offered, $dir_offers_course_code, $dir_offers_course_level, $dir_offers_level_of_entry, $dir_offers_ucas_code, $dir_offers_course_commencement_date, $dir_offers_duration_of_course, $dir_offers_enrolment_and_inductions_date, $dir_offers_academic_session, $dir_offers_course_fee, $dir_offers_course_completion, $dir_offers_intake, $dir_offers_username_id, $dir_offers_additional_1, $dir_offers_additional_2, $dir_offers_additional_3, $dir_offers_additional_4, $dir_offers_terms_and_conditions, $dir_offers_date_time_signing);
    }
}


if (!function_exists('mark_applicant_as_reviewed')) {
    function mark_applicant_as_reviewed($args): void
    {
        $dbh = get_dbh();
        $username = random();
        $rec_id = $args['reviewer_id'];
        $usergroup = $_SESSION['usergroup'];
        $date = date("Y-m-d H:i:s");
        $db338533 = $args['reviewer_id'];
        $db338543 = $args['email_sent'];
        $rel_id = $args['applicant_id'];
        $insert_sql = "INSERT INTO dir_reviewed_applications (username_id,rec_id, usergroup, date, rel_id, db338533, db338543)
                values ('{$username}','{$rec_id}', '{$usergroup}', '{$date}', '{$rel_id}', '{$db338533}', '{$db338543}')";
        dev_debug($insert_sql);
        $sth = $dbh->prepare($insert_sql);
        $sth->execute();
    }
}



if (!function_exists('str_ends_with')) {
    function str_ends_with($haystack, $needle) {
        return substr($haystack, -strlen($needle)) === $needle;
    }
}
