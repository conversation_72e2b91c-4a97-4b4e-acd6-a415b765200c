<?php
//Multi languages Model

class languages_model
{

    function get_form_data($args)
    {
        global $db;
        if (array_key_exists('user_group', $args)) {
            $usergroup_sql = "AND system_forms.usergroup ='" . $args['user_group'] . "'";
            $usergroup_sql1 = "AND form_cms.usergroup ='" . $args['user_group'] . "'";
            $usergroup_sql_table = "AND system_table.usergroup ='" . $args['user_group'] . "'";
        }
        $query = "
        SELECT 
        system_forms.id AS og_form_id,
        system_form_fields.id AS 'system_form_fields_id (do not change)',
        system_form_fields.db_name AS unique_name,
        system_table.type AS field_type,
        system_table.name AS english_title,
        system_table.description AS english_description,
         case system_table.type
            when 'dropdown' then system_table.figures
            when 'checkbox' then system_table.figures
            when 'countrylist' then system_table.figures
            when 'nationality' then system_table.figures
            when 'checkboxes_fromlist' then system_table.figures
            when 'radio_fromlist' then system_table.figures
            when 'radio_fromlist_horizontal' then system_table.figures
        end as english_options,
        '[add your title translation here]' AS lang1_title,
        '[add your description translation here]'  AS lang1_description,
        '[add your option translation here]'  AS lang1_options
    FROM
        system_forms
            INNER JOIN
        system_form_fields ON system_forms.id = system_form_fields.system_forms_id
         INNER JOIN
        system_table ON system_form_fields.system_table_id = system_table.form_id
            WHERE
            1
            $usergroup_sql	
			$usergroup_sql_table
			AND locked!=9
			GROUP BY system_table.NAME
			
            UNION

            SELECT
                    system_pages.page_id AS og_form_id,
                    system_table.form_id AS 'system_form_fields_id (do not change)',
                    system_table.name AS unique_name,
                    system_table.type AS field_type,
                    system_table. NAME AS english_title,
                    system_table.description AS english_description,
                    CASE system_table.type
                WHEN 'dropdown' THEN
                    system_table.figures
                WHEN 'checkbox' THEN
                    system_table.figures
                WHEN 'countrylist' THEN
                    system_table.figures
                WHEN 'nationality' THEN
                    system_table.figures
                WHEN 'checkboxes_fromlist' THEN
                    system_table.figures
                WHEN 'radio_fromlist' THEN
                    system_table.figures
                WHEN 'radio_fromlist_horizontal' THEN
                    system_table.figures
                END AS english_options,
                '[add your title translation here]' AS lang1_title,
                '[add your description translation here]' AS lang1_description,
                '[add your option translation here]' AS lang1_options
            FROM
                form_cms
            LEFT JOIN system_pages ON system_pages.page_id = form_cms.db655
            INNER JOIN system_table ON   system_table.pg_id = system_pages.page_id
            WHERE
            1 $usergroup_sql1 
			$usergroup_sql_table
			AND locked!=9
			AND page_id IN (48,55,13,53)
			GROUP BY system_table.NAME	
            ";

        if (isset($_GET['debugging'])) {
            echo $query;
            exit();
        }


        $results_list = array();
        $results = $db->query($query);
        $title_info = array(
            // "Id"=>"Id",
            "Unique Name" => "Unique Name(Do Not Change)",
            // "Field Type"=>"Field Type",
            "English Title" => "English Title",
            "English Description" => "English Description",
            "English Options" => "English Options",
            "Lang1 Title" => "Language Title",
            "Lang1 Description" => "Language Description",
            "Lang1 Options" => "Language Options",
            "Lang1 Symbol" => "Language Symbol"
        );
        $results_list[] = $title_info;
        // array_push($results_list,$title_info);

        foreach ($results as $row) {
            $form_info = array(
                // "id"=>$row['system_form_fields_id'],
                "unique_name" => $row['system_form_fields_id (do not change)'],
                // "field_type"=>$row['field_type'],
                "english_title" => $row['english_title'],
                "english_description" => $row['english_description'],
                "english_options " => $row['english_options '],
                "lang1_title" => $row['lang1_title'],
                "lang1_description" => $row['lang1_description'],
                "lang1_options" => $row['lang1_options'],
                "lang1_symbol" => "[add your language symbol here, either as eng,fre,ar,por,UR,EN or BG]"
            );
            $results_list[] = $form_info;
            // array_push($results_list,$form_info);
        }
        if (isset($_GET['debugging'])) {
            echo $query;
            die();
            $this->get_extra_fields();
        }
        ///export results to csv
        $csv_results = array2csv($results_list, 'Form Data');
        //print_r($results_list);
        echo $csv_results;
        exit();
    }

    function get_extra_fields()
    {
        global $db;
        $extrafields = array();
        $tables = array('dir_student_public_results', 'dir_reg_work_exp', 'form_file', 'dir_projects');
        foreach ($tables as $table_name) {

            $query = "SHOW COLUMNS FROM `$table_name`";
            $results_list = array();
            $results = $db->query($query);
            //

            foreach ($results as $key => $value) {
                if (strpos($value['Field'], 'db') !== false) {
                    array_push($extrafields, $value['Field']);
                }
            }


        }

        echo "<pre>" . print_r($extrafields, 1) . "</pre>";
        exit();

    }


    function get_multi_lang_data($args)
    {
        global $db;
        if (array_key_exists('user_group', $args)) {
            $usergroup_sql = "AND form_multi_translations.usergroup ='" . $args['user_group'] . "'";
        }
        if (array_key_exists('id', $args)) {
            $id_sql = "AND form_multi_translations.id='" . $args['id'] . "'";
        }
        $groupby_sql = "group by db35401";

        ///first get the db names concerned
        $query1 = "
            select db35401 as select_db_name from form_multi_translations 
            WHERE
            1
            $usergroup_sql
            $groupby_sql";
        $results_list1 = array();
        $results1 = $db->query($query1);
        foreach ($results1 as $row1) {
            $query = "
                SELECT id,db35401 as db_name,db35402 as lang_title from form_multi_translations
                WHERE db35401 ='" . $row1['select_db_name'] . "' 
                $usergroup_sql";

            $results_list = array();
            $results = $db->query($query);
            foreach ($results as $row) {
                $form_info = array(
                    "id" => $row['id'],
                    "lang_title" => $row['lang_title'],
                    "db_name" => $row['db_name']
                );
                $results_list[] = $form_info;
            }
            $results_list1[] = $results_list;
        }
        // if($args['id']){
        //     return $results_list1[0];
        // }else{
        //     return $results_list1;
        // }
        // echo "<pre>";
        // print_r($results_list1);
        // echo "</pre>";
        return $results_list1;
    }

    function get_gen_lang_data($args)
    {
        global $db;
        if (array_key_exists('user_group', $args)) {
            $usergroup_sql = "AND form_lang_translation.usergroup ='" . $args['user_group'] . "'";
        }
        if (array_key_exists('id', $args)) {
            $id_sql = "AND form_lang_translation.id='" . $args['id'] . "'";
        }
        if (array_key_exists('search', $args)) {

            $gedified_hack = explode(" ", $args['search']);
            foreach ($gedified_hack as $piece) {

                $value = trim($piece);
                $value = stripslashes($value);
                $piece = str_replace('\'', '\'\'', $value);

                $outlined .= "AND db21283 LIKE '%$piece%'\n";
            }

            $search_sql = $outlined; //"AND form_users.db119 LIKE '%".$args['search']."%'";
        }

        $query = "
            SELECT id,db21283 as english_word from form_lang_translation
            WHERE
            1
            $usergroup_sql
            $id_sql
            $search_sql";

        $results_list = array();
        $results = $db->query($query);
        foreach ($results as $row) {
            $form_info = array(
                "id" => $row['id'],
                "english_word" => $row['english_word']
            );
            $results_list[] = $form_info;
        }
        if ($args['id']) {
            return $results_list[0];
        } else {
            return $results_list;
        }
    }

    function get_all_languages($args = [])
    {
        global $paginator;
        $dbh = get_dbh();
        $search_sql = "";
        $params = [];
        if (!empty($_REQUEST['search'])) {
            $search_sql .= " AND db21280 like '%{$_REQUEST['search']}%' OR db21281 like '%{$_REQUEST['search']}%' ";
        }
        if (!empty($args['id'])) {
            $search_sql .= " AND id={$args['id']} ";
        }
        $query = "Select id, 
       db21280 as name , 
       db21281 as code,
       (SELECT COUNT(id) FROM ols_online_courses WHERE db31066=form_languages.id AND usergroup='{$_SESSION['usergroup']}') as number_of_courses
from form_languages
WHERE 1 $search_sql
order by number_of_courses DESC";
        dev_debug($query);

        $limit_sql = $paginator->limit_sql();
        $paginator->calculate_total_entries($query);
        $total = $paginator->total;
        $sql = $query . ' ' . $limit_sql;
        $sth = $dbh->prepare($sql);
        $sth->execute($params);
        $results = $sth->fetchAll(PDO::FETCH_ASSOC);
        return $results;
    }

    function get_language_meta($id = 1,$cache_results=false)
    {
        $dbh = get_dbh();
        global $paginator;
                $results=[];
        if ($cache_results) {
            if (empty($_SESSION['get_language_meta'])) {
                $query = "Select form_languages_meta.rel_id,form_languages_meta.id as id, db236753 as meta_key, db236756 as meta_value, db236882 as type
                   FROM form_languages_meta
                   WHERE 
                      form_languages_meta.usergroup = '{$_SESSION['usergroup']}'
                      AND (form_languages_meta.rec_archive='' OR form_languages_meta.rec_archive IS NULL)";
                    dev_debug($query);
                    $sth = $dbh->prepare($query);
                    $sth->execute();
                    $_SESSION['get_language_meta'] = $sth->fetchAll(PDO::FETCH_ASSOC);
            }

            if (!empty($_SESSION['get_language_meta'])) {
               
                foreach ($_SESSION['get_language_meta'] as $rowkey => $rowvalue) {
                    if ($rowvalue['rel_id']==$id) {
                        $results[]=$rowvalue;
                    }
                }
            }

        }else{
              $query = "Select form_languages_meta.id as id, db236753 as meta_key, db236756 as meta_value, db236882 as type
               FROM form_languages_meta
               WHERE form_languages_meta.rel_id=$id 
                 AND form_languages_meta.usergroup = '{$_SESSION['usergroup']}'
                  AND (form_languages_meta.rec_archive='' OR form_languages_meta.rec_archive IS NULL)";
                dev_debug($query);

                $limit_sql = $paginator->limit_sql();
                $paginator->calculate_total_entries($query);
                $sql = $query . ' ' . $limit_sql;
                $sth = $dbh->prepare($sql);
                $sth->execute();
                $results = $sth->fetchAll(PDO::FETCH_ASSOC);
            }
        return $results;
    }

    function set_language_meta($args)
    {
        $db_helper = new Db_helper();
        if (empty($args['language_id']) || empty($args['key']) || empty($args['type']) || empty($args['value'])) {
            return false;
        }
        if (empty($args["id"])) {
            $lang_meta = $this->get_language_meta($args["language_id"]);
            $index = array_search($args['key'], array_column($lang_meta, "meta_key"));
            $id = is_numeric($index) ? $lang_meta[$index]["id"] : false;
        } else {
            $id = $args["id"];
        }
        $db_helper->delete("form_languages_meta", ["rel_id" => $args['language_id'],
            "db236753" => $args['key'], "usergroup" => $_SESSION["usergroup"],]);
        $result = $db_helper->insert("form_languages_meta", [
            "rel_id" => $args['language_id'],
            "db236753" => $args['key'],
            "db236756" => $args['value'],
            "db236882" => $args['type'],

        ]);
        return $result;
    }

    function get_languages_by_page_name($page_name)
    {
        $dbh = get_dbh();
        $sql = "SELECT form_languages.id, db21280 AS `name` FROM form_languages 
            INNER JOIN form_cms on form_languages.id = db47583 WHERE db647=:page_url AND form_cms.usergroup=:usergroup ORDER BY db21280 ASC";

        $params = [':page_url' => $page_name, ':usergroup' => $_SESSION['usergroup']];

        $debug_sql = str_replace(array_keys($params), $params, $sql);
        dev_debug($debug_sql);

        $st = $dbh->prepare($sql);
        $st->execute($params);
        $results = $st->fetchAll(PDO::FETCH_ASSOC);
        return $results ? $results : [['id' => 1, 'name' => 'English']];
    }


}