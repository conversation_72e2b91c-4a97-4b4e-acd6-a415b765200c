<?php
	require_once __DIR__.'/../../../vendor/autoload.php';
	load_helper('file');
	load_helper('url');
	load_helper('json');
	use Pelago\Emogrifier\CssInliner;
	use Twig\Environment;
	use Twig\Extra\Intl\IntlExtension;
	use Twig\Loader\FilesystemLoader;

	class sisProducts
	{
		use urlHelper;
		protected $json;
		protected $db;
		protected $file;
		public $user_id;
		private $usergroup;
		private $settings = false;

		protected const sis_products = [ 'id'=>'sis_products.id', 'username_id'=>'sis_products.username_id', 'usergroup'=>'sis_products.usergroup', 'title'=>'db34755', 'price'=>'db34756', 'description'=>'db34836', 'image'=>'db57015', 'min_order'=>'db62715', 'excerpt'=>'db68606', 'category'=>'db68600', 'course_id'=>'db37742','has_prerequisites'=>'db87467','prerequisite_id'=>'db87470',"prereq_desc"=>'db106028','other_categories'=>'db110657'];

		protected const product_categories = [ 'pc_id'=>'sis_product_categories.id', 'pc_name'=>'db68588', 'pc_description'=>'db68591' ];

		protected const sis_address = [ 'ad_id'=>'sis_address.id', 'house_number'=>'db215', 'area'=>'db216','city' =>'db217','region'=>'db218','country'=>'db219' ,'postcode'=>'db220','type'=>'db221','address_owner_type'=>'db62710','address_owner_id'=>'db62711'];

		protected const products_cart_get = [ 'cart_id'=>'sis_products_cart.id', 'product_id'=>'db69062', 'quantity'=>'sum(db69068)', 'basket_id'=> 'db86315' ];

		protected const products_cart = [ 'cart_id'=>'sis_products_cart.id', 'product_id'=>'db69062', 'quantity'=>'db69068', 'basket_id'=> 'db86315' ];

		protected const files = [ 'file_id'=>'form_file.id', 'file_title'=>'db199', 'file_name'=>'db200', 'file_path'=> 'db204'];

		protected const order_item_prerequisites= ['pre_req_id'=>'sis_order_item_prerequisites.id','short_course_date'=>'db87476','short_course_location'=>'db87479','short_course_certificate'=>'db87482','cart_id'=>'db90581','status'=>'db90635','course_id'=>'db105662'];

		protected const cart_table = 'sis_products_cart';

		protected const shipping_methods_table = 'sis_product_shipping_methods';

	    protected const shipping_methods = ['id'=>'id','shipping_method_name'=>'db87275','status'=>'db87278'];


		protected const preferences = [ 'id'=>'lead_preferences.id', 'authorize_enabled'=>'db59832', 'paypal_enabled'=>'db59833', 'stripe_enabled'=>'db43975', 'products_enabled'=>'db61270', 'billing_address'=>'db61431','quotation_title'=>'db63212','quotation_terms'=>'db63213','purchase_order_number'=>'db63214', 'quotation_explanation'=>'db63974','quotation_enabled'=>'db63973','account_info_tooltip'=>'db63975', 'booking_terms'=>'db63985', 'unsupported_browser_message'=>'db66517', 'currency'=>'db33624', 'abv'=>'system_currency.abv', 'symbol'=>'system_currency.symbol', 'logo_image'=>'db15437', 'vat'=>'db15030', 'idle_timeout'=>'db40591'
		];
		protected const booking_org_type = [ 'id'=>'id', 'name'=>'db59820' ];

		private const invoice_settings = [ 'id'=>'id', 'rel_id'=>'rel_id', 'rec_id'=>'rec_id', 'username_id'=>'username_id', 'currency'=>'db14987','issue_date'=>'db14988', 'due_date'=>'db14989','payment_method'=>'db14995','include_vat'=>'db14998','comments'=>'db15000','status'=>'db15005','invoice_number'=>'db25617', 'short_course'=>'db57181', 'invoice_total'=>'db57251' ];

		private const invoice_items = [ 'id'=>'id', 'rel_id'=>'rel_id', 'rec_id'=>'rec_id', 'invoice_username'=>'username_id', 'title'=>'db15016','name'=>'db15017','description'=>'db15018','category'=>'db15018','type'=>'db15019','quantity'=>'db15020','units'=>'db15021','unit_price'=>'db15022','course'=>'db15024','start_date'=>'db15025','end_date'=>'db15026','short_course'=>'db20169','intake'=>'db20215' ];

		private const invoice_account = ['id'=>'id', 'rel_id'=>'rel_id', 'rec_id'=>'rec_id', 'purchase_order_number'=>'db57257',
		    'ext_ref_nubmer'=>'db107045',
			'delivery_name'=>'db57258', 'organisation'=>'db57263', 'training_start_date'=>'db57268', 'uda'=>'db57270',
			'organisation_type'=>'db58507','organisation_type_other'=>'db58508',
			'delivery_address'=>'db57259', 'billing_address'=>'db57264',
			'delivery_contact_name'=>'db57260', 'billing_contact_name'=>'db57265',
			'delivery_contact_number'=>'db57261', 'billing_contact_number'=>'db57266',
			'delivery_contact_email'=>'db57262', 'billing_contact_email'=>'db57267',
			'delivery_city'=>'db58509', 'billing_city'=>'db58513',
			'delivery_county'=>'db58510', 'billing_county'=>'db58514',
			'delivery_country'=>'db58511', 'billing_country'=>'db58515',
			'delivery_postcode'=>'db58512', 'billing_postcode'=>'db58516',
		];

		private const coms_template = [ 'id'=>'id', 'rel_id'=>'rel_id', 'rec_id'=>'rec_id','usergroup'=>'usergroup','name'=>'db1083','subject'=>'db1086','plain'=>'db1084','html'=>'db1085','send_from'=>'db1090','tags'=>'db1320', 'language'=>'db47591', 'group'=>'db1147' ];
		private const coms_email_tags = ['tag_name'=>'db15461', 'description'=>'db15462'];

		private const product_orders = [ 'id'=>'id', 'rec_id'=>'rec_id', 'date'=>'date', 'order_status'=>'db69986', 'payment_status'=>'db69989',
			'order_total'=>'db69992', 'subtotal'=>'db69995', 'vat_amount'=>'db69998', 'payment_method'=>'db70001', 'invoice_id'=>'db70004', 'currency'=>'db73166', 'booking_id' => 'db86273','payment_intent'=>'db90641'];

		private const product_order_items = [ 'id'=>'id', 'rec_id'=>'rec_id', 'product_id'=>'db70007', 'product_name'=>'db70010', 'quantity'=>'db70013',
			'price'=>'db70016', 'subtotal'=>'db70019', 'vat_amount'=>'db70022', 'status'=>'db70025','basket_id'=>'db86435','shipping_method'=>'db106118','o_notes'=>'db106667'];

		private const student_fees = ['id'=>'id', 'rel_id'=>'rel_id', 'rec_id'=>'rec_id', 'date'=>'date', 'payment_method'=>'db1492','comment'=>'db1493','payment_for'=>'db1494','amount'=>'db1495','short_course'=>'db16995','currency'=>'db30479','payment_or_refund'=>'db34450','token'=>'db37345' ];

		private const course = [ 'id'=>'id', 'name'=>'db232', 'level'=>'db341', 'summary'=>'db234', 'details'=>'db29050', 'duration'=>'db29045', 'study_mode'=>'db29046', 'entry_requirements'=>'db32195', 'url'=>'db66777' ];

		private const stripe_intent = [ 'id'=>'id', 'payment_method'=>'db43880', 'product_name'=>'db43881', 'payment_for'=>'db43882', 'amount'=>'db43883',
			'payment_date'=>'db43884', 'currency'=>'db43885', 'payment_or_refund'=>'db43886', 'token'=>'db43887', 'intent_id'=>'db43888',
			'intent_status'=>'db43889', 'invoice_id'=>'db43890', 'customer_email'=>'db43973', 'customer_name'=>'db43974', 'business_name'=>'db43976',
			'invoice_username_id'=>'db43977', 'short_course_id'=>'db44513', 'product_id'=>'db44514', 'product_username_id'=>'db44515' ];
		
		private const cart_basket = ['cart_basket_id'=>'sis_cart_basket.id', 'rel_id'=> 'sis_cart_basket.rel_id', 'rec_id'=>'sis_cart_basket.rec_id', 'basket_name' => 'db86309', 'basket_ad_id' => 'db86312', 'contact_name'=>'db87257','contact_number'=>'db87260', 'contact_email'=>'db105653', 'organization_name'=>'db106115','order_id'=>'db87437'];

		private const payment_status = [ 'payment_status_id'=>'id', 'payment_status_name'=>'db16145' ];
		private const order_status = [ 'order_status_id'=>'id', 'order_status_name'=>'db63040' , 'order_status_description'=>'db63041'];
		private const fulfilment_status = [ 'fulfilment_status_name'=>'db73139' ];

		private const payment_statuses = [ 'paid'=>6, 'agreed'=>3, 'pending'=>2, 'cancelled'=>7 , 'authorised'=>11,'proxy_payment'=>14];
		private const order_statuses = [ 'pending'=>2, 'awaiting_payment'=>3, 'awaiting_prereq_aproval'=>4,'need_more_information'=>5, 'processing'=>8, 'awaiting_shipment'=>11, 'awaiting_pickup'=>14, 'partially_shipped'=>17, 'shipped'=>20, 'completed'=>23, 'cancelled'=>26, 'declined'=>29 ,'awaiting_approval'=>7,'onhold'=>32];
		private const fulfilment_statuses = [ 'pending'=>2, 'processing'=>5, 'awaiting_shipment'=>8, 'partially_shipped'=>11, 'shipped'=>14, 'delivered'=>17, 'distributed'=>20, 'delivery_rescheduled'=>23, 'cancelled'=>26 ];

		protected const sort_orders = [
			1 => [ 'title'=>'Newest Orders', 'sort'=>'sis_product_orders.id desc', 'token'=>'id desc' ],
			2 => [ 'title'=>'Oldest Orders', 'sort'=>'sis_product_orders.id asc', 'token'=>'id asc' ],
			3 => [ 'title'=>'Order Total (low to high)', 'sort'=>self::product_orders['order_total'].' asc', 'token'=>'order_total asc' ],
			4 => [ 'title'=>'Order Total (high to low)', 'sort'=>self::product_orders['order_total'].' desc', 'token'=>'order_total desc' ],
			5 => [ 'title'=>'Organisation Asc', 'sort'=>self::invoice_account['organisation'].' asc', 'token'=>'organisation asc' ],
			6 => [ 'title'=>'Organisation Desc', 'sort'=>self::invoice_account['organisation'].' desc', 'token'=>'organisation desc' ],
			7 => [ 'title'=>'Date Asc', 'sort'=>'sis_product_orders.date asc', 'token'=>'date asc' ],
			8 => [ 'title'=>'Date Desc', 'sort'=>'sis_product_orders.date desc', 'token'=>'date desc' ],
			9 => [ 'title'=>'Total Items Asc', 'sort'=>'total_items asc', 'token'=>'total_items asc' ],
			10 => [ 'title'=>'Total Items Desc', 'sort'=>'total_items desc', 'token'=>'total_items desc'  ],
			11 => [ 'title'=>'Amount Paid Asc', 'sort'=>'amount_paid asc', 'token'=>'amount_paid asc' ],
			12 => [ 'title'=>'Amount Paid Desc', 'sort'=>'amount_paid desc', 'token'=>'amount_paid desc' ],
			13 => [ 'title'=>'Payment Status Asc', 'sort'=>self::product_orders['payment_status'].' asc', 'token'=>'payment_status asc' ],
			14 => [ 'title'=>'Payment Status Desc', 'sort'=>self::product_orders['payment_status'].' desc', 'token'=>'payment_status desc' ],
			15 => [ 'title'=>'Order Status Asc', 'sort'=>self::product_orders['order_status'].' asc', 'token'=>'order_status asc' ],
			16 => [ 'title'=>'Order Status Desc', 'sort'=>self::product_orders['order_status'].' desc', 'token'=>'order_status desc' ],
			17 => [ 'title'=>'First Name Asc', 'sort'=>self::form_users['first_name'].' asc', 'token'=>'first_name asc' ],
			18 => [ 'title'=>'First Name Desc', 'sort'=>self::form_users['first_name'].' desc', 'token'=>'first_name desc' ],
			19 => [ 'title'=>'Booking ID Asc', 'sort'=>self::product_orders['booking_id'].' asc', 'token'=>'booking_id asc' ],
			20 => [ 'title'=>'Booking ID Desc', 'sort'=>self::product_orders['booking_id'].' desc', 'token'=>'booking_id desc' ],
			21 => [ 'title'=>'Payment Option Asc', 'sort'=>self::product_orders['payment_method'].' asc', 'token'=>'payment_method asc' ],
			22 => [ 'title'=>'Payment Option Desc', 'sort'=>self::product_orders['payment_method'].' desc', 'token'=>'payment_method desc' ],
		];
		protected const sort = [
			1 => [ 'title'=>'Most Popular', 'sort'=>'order_count desc', 'token'=>'order_count desc' ],
			2 => [ 'title'=>'Recently Added', 'sort'=>'sis_products.id desc', 'token'=>'id desc' ],
			3 => [ 'title'=>'Name Ascending', 'sort'=>self::sis_products['title'].' asc', 'token'=>'title asc' ],
			4 => [ 'title'=>'Name Descending', 'sort'=>self::sis_products['title'].' desc', 'token'=>'title desc' ],
			5 => [ 'title'=>'Price (low to high)', 'sort'=>self::sis_products['price'].' asc', 'token'=>'price asc' ],
			6 => [ 'title'=>'Price (high to low)', 'sort'=>self::sis_products['price'].' desc', 'token'=>'price desc' ],
			7 => [ 'title'=>'Category Ascending', 'sort'=>self::product_categories['pc_name'].' asc', 'token'=>'category asc' ],
			8 => [ 'title'=>'Category Descending', 'sort'=>self::product_categories['pc_name'].' desc', 'token'=>'category desc' ],
		];

		protected const form_users = [ 'user_id'=>'id', 'first_name'=>'db106', 'surname'=>'db111', 'email'=>'db119' ];
		private const form_schools = ['school_name'=>'db27', 'email'=>'db1118'];

		private const fulfilment_history = [ 'id'=>'id', 'order_item_id'=>'db73274', 'dispatch_or_return'=>'db73277', 'date_actioned'=>'db73280', 'method'=>'db73283', 'notes'=>'db73286', 'quantity'=>'db73289', 'reference'=>'db73292' ];

		private const order_history = [ 'id'=>'id', 'date'=>'date', 'status'=>'db73727', 'subject'=>'db73730', 'comment'=>'db73733', 'email'=>'db73736', 'email_log_id'=>'db73739', 'phone'=>'db73742', 'sms_log_id'=>'db73745' ];

		private const email_log = [ 'id'=>'id', 'email'=>'db1153', 'delivered'=>'db1155', 'processed'=>'db1161' ];
		private const sms_log = [ 'id'=>'id', 'phone'=>'db25667', 'status'=>'db63208', 'message_id'=>'db63209', 'status_message'=>'db63210', 'message'=>'db25668' ];

		private const log_actions = [ 'pre_req_update'=>'Prerequisite Status Update','status_update'=>'Status Update', 'message'=>'New Message', 'products_change'=>'Products Update', 'invoice_update'=>'Invoice Details Update', 'payment'=>'Payment', 'refund'=>'Refund' , 'order_baskets_update'=>"Order Baskets Update","order_booking_status_update"=>"order booking status update"];

		private const delivery_export_fields = [ 'Order ID'=>'order_id', "Invoice Number"=>'invoice_number','Customer Name'=>'customer', /*'Delivery Address'=>'address'*/'Invoice Billing Address'=>'cart_address', 'Basket Address'=>'basket_address', 'Product Name'=>'product_name', 'Quantity Ordered'=>'quantity', 'Quantity Shipped'=>'shipped', 'Quantity to Ship'=>'to_ship', 'Status'=>'fulfilment_status_name', 'Date Dispatched'=>'dispatch_date', 'Dispatch Ref. No.'=>'dispatch_ref',"Content if mix of folders and inserts boxes " =>'cnt_mixed',"No of Jiffy’s/boxes"=>'jiffy','Delivery Method'=>'shipping_method','Contact telephone number'=>'contact_number'];

		private const finance_export_fields = [ 'Order ID'=>'order_id','Customer Name'=>'customer', /*'Delivery Address'=>'address'*/'Invoice Billing Address'=>'cart_address', 'Basket Address'=>'basket_address', 'Product Name'=>'product_name', 'Quantity Ordered'=>'quantity', 'Quantity Shipped'=>'shipped', 'Quantity to Ship'=>'to_ship', 'Status'=>'fulfilment_status_name','Contact telephone number'=>'contact_number','Category of product'=>'product_category','No of sessions'=>'number_of_sessions','Payment/Refund Date'=>"payment_date","Customer number"=>"ext_ref_nubmer","Payment/Refund"=>"payment_or_refund","Payment method selected"=>"payment_method","Payment reference number/token (D number)"=>"payment_ref",/*'Price'=>'price',*/'Total amount per line/product'=>'total_price'];
		private const prereqs_reject_reasons = [ 'id'=>'id', 'prereq_item_id'=>'rel_id', 'rec_id'=>'rec_id','usergroup'=>'usergroup','reason'=>'db106514' ];


		protected const customer_references_table = 'lead_partners';

	    protected const customer_references = ['name'=>'db52640','reference'=>'db107129','status'=>"db52641"];


	    private const scheduled_booking = [ 'id'=>'id', 'rel_id'=>'rel_id', 'rec_id'=>'rec_id','username_id'=>'username_id', 'student_id'=>'db16135', 'payment_method'=>'db14984', 'status'=>'db14983', 'price'=>'db15104', 'deposit'=>'db15111', 'manual_booking'=>'db19380', 'course_title'=>'db14982', 'notes'=>'db14980', 'invoice'=>'db15438', 'vat'=>'db17743', 'event_type'=>'db19469', 'address'=>'db40387', 'scheduled_course_id'=>'db14977','payment_status'=>'db14979', 'start_date'=>"db14978", 'access_plan_id'=>'db73802'];

	    private const sched_booking_detail = [ 'id'=>'id', 'rel_id'=>'rel_id', 'username_id'=>'username_id', 'rec_id'=>'rec_id', 'first_name'=>'db15054', 'surname'=>'db15055', 'email'=>'db15056', 'phone'=>'db15073', 'gender'=>'db15057', 'date_of_birth'=>'db15058', 'nationality'=>'db15059', 'passport_number'=>'db15060', 'passport_expiry'=>'db15061', 'country_of_origin'=>'db15062', 'country_of_permanent_residence'=>'db15063', 'county'=>'db15069', 'country'=>'db15070', 'post_code'=>'db15071', 'invoice_address'=>'db15439', 'address_record'=>'db15440', 'invoice_fao'=>'db19347', 'invoice_house_number'=>'db19348', 'invoice_town'=>'db19349', 'invoice_county'=>'db19350', 'invoice_country'=>'db19351', 'invoice_post_code'=>'db19352', 'invoice_address_record'=>'db19353', 'scheduled_booking_id'=>'db15052', 'dietary_requirements'=>'db58494','other_requirements'=>'db15084','job_title'=>'db58493', 'booking_status'=>'db59978', 'attendance_status'=>'db64664','name_for_certificate' => 'db102200'
	    ];



		function __construct(){
			$this->db = new Db_helper();
			$this->file = new File_helper(false);
			$this->json = new Json_helper();
			$this->user_id = $_SESSION['uid'];
			$this->usergroup = $_SESSION['usergroup'];
			$this->settings = $this->preferences();
		}
		
		function setUserId($id){
			$this->user_id = $id;
		}

		function setUsergroup($usergroup){
			$this->usergroup = $usergroup;
		}

		function get_products($args=[]){
			$one = false;
			if (!empty($args['related_to'])) {
				$args['categories']=explode(',', $this->db->where('id',$args['related_to'])->fetch_field( 'concat('.self::sis_products['category'].",',',".self::sis_products['other_categories'].')', 'sis_products' ) );
				unset($args['category']);
				//echo $this->db->get_last_query();
				//echo "<pre>".print_r($args,1)."</pre>";exit();
			}

			if( isset($args['id']) && is_numeric($args['id']) ){
				$this->db->where( 'sis_products.id', $args['id'] );
				$one = true;
			}

			if(isset($args['customers_only'])){
				$this->db->where_in('db106142',[2,8]);
			}


			if(isset($args['admins_only'])){
				$this->db->where_in('db106142',[2,5]);
			}

			$count = isset($args['count']) && $args['count'];

			if( !$count && isset($args['limit']) && is_numeric($args['limit'])){
				$start = isset($args['start']) && is_numeric($args['start']) ? $args['start'] : 0;
				$this->db->limit( $args['limit'], $start );
			}

			$f = array_merge( self::sis_products, self::product_categories );
			$this->db->join( 'sis_product_categories', 'sis_product_categories.id = '.$f['category'], 'left' );
			$this->db->where('sis_products.usergroup', $this->usergroup)->where("(sis_products.rec_archive IS NULL OR sis_products.rec_archive = '' )");

			$search = empty($args['search']) ? false : trim($args['search']);
			if( $search ){
				$this->db->group_start();
				$this->db->like( self::sis_products['title'], $search );
				$this->db->or_like( self::sis_products['description'], $search );
				$this->db->or_like( self::product_categories['pc_name'], $search );
				$this->db->group_end();
			}

			if(!empty($args['category'])){
				$this->db->group_start();
				$this->db->where( self::sis_products['category'], trim($args['category']) );
				$this->db->or_where('FIND_IN_SET("'.trim($args['category']).'", '.self::sis_products['other_categories'].')');
				$this->db->group_end();
			}

			if(!empty($args['not_id'])){
				$this->db->where( 'sis_products.id !=', $args['not_id'] );
			}

			if (!empty($args['categories'])) {
				$this->db->where_in(self::sis_products['category'], $args['categories']);
			}

			if($count){
				return $this->db->fetch_field( 'count(sis_products.id)', 'sis_products' );
			}

			if(isset($args['paginate'])) $this->db->paginate($args['paginate']);

            $order_count_inner_query = "SELECT COUNT(id) FROM sis_product_order_items WHERE ".self::product_order_items['product_id']." = sis_products.id";
			if( isset($args['sort']) && $args['sort'] && isset( self::sort[$args['sort']] ) ){
				$sort = self::sort[$args['sort']];
				$this->db->order_by( $sort['sort'] );
                if( $args['sort'] == 1 ){
                    $this->db->select( "($order_count_inner_query)", "order_count");
                }
			}
			else{
				$this->db->order_by( self::sort[1]['sort'] );
                $this->db->select( "($order_count_inner_query)", "order_count");
			}
			//echo "<pre>".print_r($f,1)."</pre>";exit();

			$products = $one ?  $this->db->get_row($f, 'sis_products') : $this->db->get_rows($f, 'sis_products');
			$q = $this->db->get_last_query();
			//$products['query']=$q;

			if(!$products) return [];
			if($one===false) {
				foreach($products as $k => $v) {
					$products[$k]['image'] = $v['image'] ? $this->file->get_image_link($v['image']) : $this->base_url('products/resources/img/noimage.svg');

					$products[$k]['prerequisites_list']=$this->product_prereq_list(['product_ids'=>$v['prerequisite_id']]);
					//echo $this->db->get_last_query();exit();
				}
			}
			else{
				$products['image'] = $products['image'] ? $this->file->get_image_link($products['image']) : $this->base_url('products/resources/img/noimage.svg');
				if( $products['course_id'] ){
					$products['course'] = $this->get_course_details($products['course_id']);
				}
			}

			return $products;
		}

		function get_product_categories($args=[]){

			$one = false;
			if( isset($args['id']) && is_numeric($args['id']) ){
				$this->db->where( 'sis_product_categories.id', trim($args['id']) );
				$one = true;
			}

			$this->db->where("rec_archive IS NULL")->where('usergroup', $this->usergroup);

			$c = $one ?  $this->db->get_row(self::product_categories, 'sis_product_categories') : $this->db->get_rows(self::product_categories, 'sis_product_categories');

			if(!$c) return [];
			if($one===false) {
				foreach($c as $k => $v) {
					$c[$k]['uri'] = $this->url_title( $v['pc_name'], '-', true );
				}
			}
			else{
				$c['uri'] = $this->url_title( $c['pc_name'], '-', true );
			}

			return $c ? $c : [];
		}

		function get_cart(){
			$this->merge_carts();
			$f = array_merge( self::sis_products, self::products_cart_get );
			$this->db->join( 'sis_products', "{$f['product_id']} = {$f['id']}", 'inner' );
			$this->db->where('sis_products_cart.usergroup', $this->usergroup )->where( 'sis_products.usergroup', $this->usergroup );
			$this->db->where('sis_products_cart.rec_id', $this->user_id);
			$this->db->where( 'sis_products_cart.rec_archive IS NULL' )->where('sis_products.rec_archive IS NULL');
			$this->db->group_by('db69062');
			$cart = $this->db->get_rows($f, 'sis_products_cart');

			if(!$cart) return [];

			foreach($cart as $k => $v) {
				$cart[$k]['image'] = $v['image'] ? $this->file->get_image_link($v['image']) : $this->base_url('products/resources/img/noimage.svg');
			}

			return $cart;
		}

		function add_to_cart($args){
			try {
				if(!is_numeric($args['quantity'])) throw new Exception("A valid number is required for product quantity");
				if($args['quantity']<1) throw new Exception('A minimum number of 1 is required for product quantity');
				if(!is_numeric($args['basket_id'])) throw new Exception('Basket ID is required to add to cart');
                $uid = $this->user_id;
				$this->db->where('sis_products_cart.usergroup', $this->usergroup)->where("sis_products_cart.rec_archive IS NULL")->where('sis_products_cart.rec_id', $uid)->where('db69062',$args['product_id'])->where('db86315',$args['basket_id']);
			    $products =  $this->db->get_rows(self::products_cart, 'sis_products_cart');
			    if (isset($products[0])&&$products[0]['quantity']>0) {
				    	$q = [ 'quantity' => ($products[0]['quantity']+$args['quantity']) ];
						$data = $this->db->prepare_data(self::products_cart, $q);
						$this->db->where('sis_products_cart.usergroup', $this->usergroup)->where("sis_products_cart.rec_archive IS NULL")->where('sis_products_cart.rec_id', $uid)->where('db69062',$args['product_id'])->where('db86315',$args['basket_id']);
						$updated = $this->db->update( 'sis_products_cart', $data );
						if(!$updated) throw new Exception('Unable to update cart. Please check your data and try again');
						return [ 'success'=>true, 'message'=>"Cart updated successfully" ];				    
			    }else{
				
				$this->db->where('sis_products.usergroup', $this->usergroup)->where("sis_products.rec_archive IS NULL");
				$this->db->where('id', $args['product_id']);
				$product = $this->db->get_row(self::sis_products, 'sis_products');
				if(!isset($product['id'])) throw new Exception("Unable to add item to cart. Product not found or currently unavailable");
				$data = $this->db->prepare_data(self::products_cart, $args);
				$id = $this->db->insert( 'sis_products_cart', $data );
				if(!$id) throw new Exception('Unable to add product to cart. Please check your data and try again');
				return [ 'success'=>true, 'message'=>"{$product['title']} successfully added to cart" ];
			}
			}
			catch(Exception $e){
				return [ 'success'=>false, 'message'=>$e->getMessage(), 'Q'=>$this->db->get_last_query() ];
			}
		}


		function update_cart($args){
			try {
				if(!is_numeric($args['quantity'])) throw new Exception("A valid number is required for product quantity");
				
                $products=$this->db->where( self::products_cart['cart_id'], $args['cart_id'] )->get_rows(self::products_cart,'sis_products_cart');
                $product_details=$this->db->where('id',$products[0]['product_id'])->get_rows(self::sis_products,'sis_products');
			    //echo "<pre>".print_r($args,1)."</pre>";exit();
			    if ($product_details[0]['min_order']>0) {
			    	if (($products[0]['quantity']<=$product_details[0]['min_order'])&&$args['decreamenting']=='true') {
			    		throw new Exception("You can't order less than".$product_details[0]['min_order'].' of this product');
			    	}
			    }
				if($args['quantity']<1) throw new Exception('A minimum number of 1 is required foreach												or product quantity');
				$q = [ 'quantity' => $args['quantity'] ];
				$data = $this->db->prepare_data(self::products_cart, $q);
				$this->db->where( self::products_cart['cart_id'], $args['cart_id'] );
				$updated = $this->db->update( 'sis_products_cart', $data );
				if(!$updated) throw new Exception('Unable to update cart. Please check your data and try again');
				return [ 'success'=>true, 'message'=>"Cart updated successfully" ];
			}
			catch(Exception $e){
				return [ 'success'=>false, 'message'=>$e->getMessage(), 'Q'=>$this->db->get_last_query(), 'cart'=>$this->get_cart() ];
			}
		}

		function remove_from_cart($product_id){
			try {
				$uid=$this->user_id;
				$deleted = $this->db->delete( 'sis_products_cart', ['rec_id'=>$uid,'db69062'=>$product_id] );
				if(!$deleted) throw new Exception('Unable to update cart. Please check your data and try again');
				return [ 'success'=>true, 'message'=>"Cart updated successfully" ];
			}
			catch(Exception $e){
				return [ 'success'=>false, 'message'=>$e->getMessage(), 'Q'=>$this->db->get_last_query()];
			}
		}

		// function merge_carts(){
		// 	$cart = isset($_SESSION['temp_cart']) ? $_SESSION['temp_cart'] : [];
		// 	if(empty($cart)) {
  //                  $this->add_empty_primary_cart();
  //                  return;
		// 	};

		// 	if( empty($this->user_id) ) return;
		// 	$uid = $this->user_id;

		// 	foreach( $cart as $id=>$v ){
		// 		$this->db->where( 'rec_id', $uid )->where( 'usergroup', $this->usergroup )->where( self::products_cart['product_id'], $id );
		// 		$line = $this->db->fetch_field( self::products_cart['cart_id'], 'sis_products_cart' );
		// 		if( $line ){
		// 			$this->db->update( 'sis_products_cart', [ self::products_cart['quantity']=>$v['quantity'] ], ['id',$line] );
		// 		}
		// 		else{
		// 			$data = $this->db->prepare_data( self::products_cart, [ 'product_id'=>$id, 'quantity'=>$v['quantity'] ] );
		// 			$this->db->insert( 'sis_products_cart', $data );
		// 		}
		// 	}
		// 	$this->check_cart_basket();
		// 	$_SESSION['temp_cart'] = [];
		// }


		function merge_carts(){
			$cart = isset($_SESSION['temp_cart']) ? $_SESSION['temp_cart'] : [];
			if(empty($cart)) {
                   $this->add_empty_primary_cart();
                   return;
			};

			if( empty($this->user_id) ) return;
			$uid = $this->user_id;

			//$temp_baskets=

			foreach( $_SESSION['baskets'] as $bid=>$bv ){

                $existing_bs=$this->db->where( 'rec_id', $uid )->where( 'usergroup', $this->usergroup )->where(self::cart_basket['basket_name'],$bv['basket_name'])->where('(db87437 is null or db87437="")')->fetch_field( self::cart_basket['cart_basket_id'], 'sis_cart_basket' );

                if ($existing_bs) {
                	//update basket
                	$bv['cart_basket_id']=$existing_bs;
                	$this->add_basket($bv);
                }else{
                    //insert basket
                    unset($bv['cart_basket_id']);
                    $this->add_basket($bv);	
                    $existing_bs=$this->db->where( 'rec_id', $uid )->where( 'usergroup', $this->usergroup )->where(self::cart_basket['basket_name'],$bv['basket_name'])->where('(db87437 is null or db87437="")')->fetch_field( self::cart_basket['cart_basket_id'], 'sis_cart_basket' );
                }



               if ($existing_bs&&isset($_SESSION['temp_baskets'][$bid])) {
                foreach( $_SESSION['temp_baskets'][$bid] as $id=>$v ){
					$this->db->where( 'rec_id', $uid )->where( 'usergroup', $this->usergroup )->where( self::products_cart['product_id'], $id )->where(self::products_cart['basket_id'],$existing_bs);
				$line = $this->db->fetch_field( self::products_cart['cart_id'], 'sis_products_cart' );
				if( $line ){
					$this->db->update( 'sis_products_cart', [ self::products_cart['quantity']=>$v['quantity'] ], ['id',$line] );
				}
				else{
						$data = $this->db->prepare_data( self::products_cart, [ 'product_id'=>$id, 'quantity'=>$v['quantity'],'basket_id'=> $existing_bs] );
					$this->db->insert( 'sis_products_cart', $data );
					}
			    }

				}
			}
			$this->check_cart_basket();
			$_SESSION['temp_cart'] = [];
			$_SESSION['temp_baskets'] = [];
			$_SESSION['baskets'] = [];
		}


		function preferences(){

			if($this->settings) return $this->settings;

			$this->db->join( 'form_schools', 'form_schools.id = lead_preferences.usergroup', 'inner' );
			$this->db->join( 'system_currency', 'system_currency.id = '.self::preferences['currency'], 'left' );
			$this->db->where( 'lead_preferences.usergroup', $this->usergroup );
			$this->db->where( 'lead_preferences.rec_archive IS NULL' );
			$fields = array_merge( self::preferences, self::form_schools );
			$data = $this->db->get_row($fields, 'lead_preferences' );

			$data['booking_org_types'] = $this->db->get_rows(self::booking_org_type, 'sis_booking_org_type',['usergroup'=>$this->usergroup]);

			if( isset($data['idle_timeout']) ){
				// auto logout minutes
				$data['idle_timeout'] = 60;
				// is_numeric( $data['idle_timeout'] ) ? $data['idle_timeout']*1 : 20;
			}

			$folder = $_SESSION['subdomain'];
			$dir = realpath(__DIR__.'/../../../static/'.$folder.'/resources/img/');
			if(file_exists("{$dir}/logo.jpg")) $logo = $this->base_url("static/{$folder}/resources/img/logo.jpg");
			elseif(file_exists("{$dir}/logo.png")) $logo = $this->base_url("static/{$folder}/resources/img/logo.png");
			else {
				$file = new File_helper(false);
				$logo = isset($data['logo_image']) ? $file->get_image_link($data['logo_image']) : $file->get_image_link(false);
			}
			if($data) $data['logo'] = $logo;
            if($data) $data['sort'] = $this->get_product_sort();
			return $data ? $data : [];
		}

		function get_countries(){
			$fields = [ 'id', 'name'=>'country_name' ];
			$order = [ "FIELD (country_name, 'United Kingdom') desc", 'id'];
			$c = $this->db->get_rows($fields,'system_countrylist', [], $order);
			if(!is_array($c)) $c = [];
			return $c;
		}

		function cart_total(){
			$cart = $this->get_cart();
			if(!$cart) return 0;
			$total = 0;
			foreach( $cart as $v ){
				$total += ( $v['price'] * $v['quantity'] );
				// add VAT calculations if available
			}
			return $total;
		}

		function place_order($args=[]){
			//echo "<pre>".print_r($args,1)."</pre>";exit();
			if ($args['organisation']==""&&$args['organization_name']!="") {
				$args['organisation']=$args['organization_name'];
			}
			$this->make_order_address_primary($args);
			try {
				$preferences = $this->preferences();
				$products = $this->get_cart();

				$this->db->trans_start();
				// create invoice
				$args['currency'] = $preferences['currency'];
				$invoice_id = $this->create_invoice($args, null,$products);
				if(!$invoice_id) throw new Exception("Unable to create invoice. Check your data and try again");

                if (count($this->get_cart_prerequisites(true)['cart'])==0) {
				// process payments
				$payment = $this->process_payment( $args, $invoice_id );
				if(!$payment['success']) throw new Exception($payment['message']);
                }
				// add delivery / billing details
				$invoice_account = $this->add_invoice_details( $invoice_id, $args );
				if(!$invoice_account) throw new Exception("Unable to save invoice details.");

				// add invoice items
				$this->add_invoice_items( $invoice_id, $products );

				// create product order
				$args['invoice_id'] = $invoice_id;
				$order = $this->create_order( $args, $products, $preferences );
				if( !isset($order['id']) ) throw new Exception("Unable to create your order.");

				$order_id = $order['id'];

				$this->db->update('lead_invoice_settings',['rel_id'=>$order_id],['id'=>$invoice_id]);

				// add product order items
				$this->add_order_items( $order, $products, $preferences );


				//update baskets with order id
				$this->db->where('rec_id',$this->user_id)->where('(db87437 is NULL or db87437="")');
				$this->db->update('sis_cart_basket',['db87437'=>$order_id]);
				
				
				// clear cart
				$this->empty_cart();


				// log order history
				$h = [ 'order_id'=>$order_id, 'status'=>$order['order_status'], 'message'=>'Order created' ];
				$this->log_order_history( self::log_actions['status_update'], $h );

				// send emails
				$this->send_order_confirmation($order_id);



				$this->db->trans_commit();

				//create blank carts from the used carts

				$this->duplicate_baskets($order_id);

				return [ 'success'=>true, 'message'=>"Order placed successfully", 'order_id'=>$order_id, 'apq'=>$order['apq'] ];
			}
			catch(Exception $e){
				if( $this->db->in_transaction() ) $this->db->trans_rollback();
				return [ 'success'=>false, 'message'=>$e->getMessage() ];
			}

		}

		function place_booking_order($args=[]){
			try {
				$preferences = $this->preferences();
				if (strpos($args['payment_method'], 'invoice') !== false) {
					$args['payment_method'] = 'invoice';
				}else{
					$args['payment_method'] = 'card';
				}
				$products = $args['products']??false;

				$order_address=$args['order_address']??false;

				//$basket_id=$this->get_default_basket();
			    //if(!$basket_id){
			    	$this->add_empty_primary_cart();
			    	$basket_id=$this->get_default_basket();
			    //}
				//if(!$products) throw new Exception("You need to add at least one product.");
			    if (isset($products[0])) {
				foreach ($products as $key => $value) {
					// code...
					$products[$key]['basket_id']=$basket_id;
				    }
				}


				///echo $this->db->get_last_query();exit();

				////echo "<pre>".print_r($products,1)."<pre>";exit();

				$this->db->trans_start();

				$this->make_order_address_primary($order_address);

				//echo $this->db->get_last_query();exit();
				// create invoice
				$args['currency'] = $preferences['currency'];
				$invoice_id = $args['invoice_id']??false;
				if(!$invoice_id) throw new Exception("Invoice ID is required.");

				// create product order
				$args['invoice_id'] = $invoice_id;
				if (empty($products)) {
					$order = $this->create__empty_order( $args, $products, $preferences );
					$order_id = $order['id'];
				}else{
				$order = $this->create_order( $args, $products, $preferences );
				if( !isset($order['id']) ) throw new Exception("Unable to create your order.");
				$order_id = $order['id'];
				// add product order items
				$this->add_order_items( $order, $products, $preferences );
                }
				//update baskets with order id
				$this->db->where('id',$basket_id);
				$this->db->update('sis_cart_basket',['db87437'=>$order_id]);

				// log order history
				$h = [ 'order_id'=>$order_id, 'status'=>$order['order_status'], 'message'=>'Order created' ];
				$this->log_order_history( self::log_actions['status_update'], $h );

				//update
				//$this->duplicate_baskets($order_id);

				$this->db->trans_commit();

				return [ 'success'=>true, 'message'=>"Order placed successfully", 'order_id'=>$order_id, 'apq'=>$order['apq'] ];
			}
			catch(Exception $e){
				if( $this->db->in_transaction() ) $this->db->trans_rollback();
				return [ 'success'=>false, 'message'=>$e->getMessage() ];
			}

		}

		function create_invoice($args,$order_id,$products=[])
		{
			// create invoice
			$date = date("Y-m-d H:i:s");
			$invoice_number = random();
			$invoice = [ 'rel_id'=>$order_id, 'currency'=>$args['currency'],'issue_date'=>$date,
				'payment_method'=>$args['payment_method'],'include_vat'=>'','comments'=>$args['notes'],'status'=>'approved',
				'invoice_number'=>$invoice_number, 'username_id'=>$invoice_number ];

			if (isset($args['rec_id'])) {
				$invoice['rec_id']=$args['rec_id'];
			}
			
			if (!empty($products)) {
				$total=0;
					foreach($products as $k=>$p){
                       $vat_price = $price = 1 * $p['price'];
						$vat_amount = 0;
						if($vat){ // price with vat = price + ( price * vat )
							$price = $vat_price / ( 1 + ($vat/100) ); // price without vat = price with vat / ( 1 + vat)
							$vat_amount = $vat_price - $price;
						}
						$total =$total+$vat_price * $p['quantity'];
				    }
				$invoice['invoice_total']=$total;
			}

			$lis = self::invoice_settings;
			$data = $this->db->prepare_data( self::invoice_settings, $invoice );
			//echo "<pre>".print_r($data,1)."</pre>";exit();
			$id = $this->db->insert( 'lead_invoice_settings', $data );
			return $id;
		}

		function add_invoice_details($invoice_id,$invoice_account){

			if( $invoice_account['uda']===true || $invoice_account['uda']==='true' ){
				foreach(  $invoice_account as $k=>$ia){
					if( substr_compare( $k, 'delivery_', 0, 9 ) === 0 ){
						$f = substr_replace( $k, 'billing_', 0, 9 );
						if( empty($invoice_account[$f]) ) $invoice_account[$f] = $invoice_account[$k];
					}
				}
			}

			$invoice_account['uda'] = ($invoice_account['uda']===true || $invoice_account['uda']==='true') ? 1 : 0;
			$user_id=$this->user_id;

			$info=$this->db->where('rec_id',$user_id)->order_by('lead_invoice_account.id desc')->get_rows(self::invoice_account['ext_ref_nubmer'],'lead_invoice_account');
			if (isset($info[0][self::invoice_account['ext_ref_nubmer']])&&""!=$info[0][self::invoice_account['ext_ref_nubmer']]) {
				$invoice_account['ext_ref_nubmer']=$info[0][self::invoice_account['ext_ref_nubmer']];
			}

			$data = $this->db->prepare_data(self::invoice_account,$invoice_account);
			$data['rel_id'] = $invoice_id;
			$id = $this->db->insert('lead_invoice_account',$data);
			return $id;
		}

		function update_invoice_details($args)
		{
			if( !isset($args['id']) ) return [ 'success'=>false, 'message'=>'Invoice Account ID is required' ];
			if( !isset($args['invoice_id']) ) return [ 'success'=>false, 'message'=>'Invoice ID is required' ];

			$current_details = $this->get_invoice_details( $args['invoice_id'] );

			if( $args['payment_method']==='account' && $args['uda']===true || $args['uda']==='true' ){
				foreach($args as $k=> $ia){
					if( substr_compare( $k, 'delivery_', 0, 9 ) === 0 ){
						$f = substr_replace( $k, 'billing_', 0, 9 );
						if( empty($args[$f]) ) $args[$f] = $args[$k];
					}
				}
			}
			$args['uda'] = ($args['uda']===true || $args['uda']==='true') ? 1 : 0;

			$changes = $this->invoice_changes( $current_details, $args );

			$data = $this->db->prepare_data(self::invoice_account,$args);
			foreach($data as $k=>$v){
				if( $v==='null' ) $data[$k] = NULL;
			}
			$e = $this->db->update('lead_invoice_account', $data, ['id'=>$args['id']] );

            if (isset($args['order_id'])&&""!=$args['order_id']) {
            	$order_baskets=$this->db->where(self::cart_basket['order_id'],$args['order_id'])->get_rows('sis_cart_basket');

            	if (!isset($order_baskets[0])) {
                    $order_basket_info['basket_name'] = "My Basket";
                    $order_basket_info['order_id'] = $args['order_id'];
                    $order_basket_info['organization_name'] =  $args['organisation'];
                    $order_basket_info['contact_name'] = $args['delivery_contact_name'];
                    $order_basket_info['contact_number'] = $args['delivery_contact_number'];
                    $order_basket_info['contact_email'] = $args['delivery_contact_email'];
                    $order_basket_info['house_number'] = $args['delivery_address'];
                    //$order_basket_info['area'] = $args['order_id'];
                    $order_basket_info['region'] = $args['delivery_county'];
                    $order_basket_info['country'] = $args['delivery_country'];
                    $order_basket_info['postcode'] = $args['delivery_postcode'];
                    $order_basket_info['city'] = $args['delivery_city'];
            		$this->add_basket($order_basket_info);
            	}
            }
			

			if($changes){
				$where = [self::product_orders['invoice_id']=>$args['invoice_id'], 'usergroup'=>$this->usergroup ];
				$order_id = $this->db->fetch_field( 'id', 'sis_product_orders', $where );
				$order = $this->orders(['id'=>$order_id]);
				$logs = [];
				if( $order ){
					$logs = $this->send_order_updated_email( $order, $changes, $order['user_id'] == $this->user_id );
				}

				$h = [ 'order_id'=>$order_id, 'message'=>$changes ];
				$log_id = $this->log_order_history( self::log_actions['invoice_update'], $h , $logs);
			}

			$log = false;
			if($log_id){
				$log = $this->get_order_history( $args['order_id'], $log_id );
			}

			$details = $this->get_invoice_details( $args['invoice_id'] );
			$contacts = $e ? $this->order_contacts($args['invoice_id']): false;
            $order = $this->orders(['id'=>$args['order_id']??""]);
			return $e ? [ 'success'=>true, 'message'=>'Invoice details updated successfully. ', 'details'=>$details, 'contacts'=>$contacts, 'log'=>$log,'order'=>$order ]
				: [ 'success'=>false, 'Unable to update invoice details. Please check your data and try again.', 'details'=>$details ];
		}

		function invoice_changes( $old, $new ){
			$fields = self::invoice_account;
			$changes = '';
			$ignore = ['uda'];
			foreach( $fields as $f=>$v ){
				if( isset($old[$f]) && isset($new[$f]) && !in_array( $f, $ignore ) ){
					$c = $old[$f] != $new[$f];
					if($c){
						$t = ucwords( str_replace('_', ' ', $f) );
						$changes .= "<li>{$t} changed from <b> {$old[$f]}</b> to <b>{$new[$f]}</b></li>";
					}
				}
			}
			return $changes ? '<ol>'.$changes.'</ol>':'';
		}

		function add_invoice_items($invoice_id, $products, $keys=[]){
			foreach($products as $k=>$p){
				$line = [ 'rel_id'=>$invoice_id,'title'=>$p['title'],'name'=>$p['title'], 'description'=>$p['title'],'category'=>'product',
					'quantity'=>$p['quantity'],'units'=>'Number of items','unit_price'=>$p['price'],'course'=>$p['course_id'],
					'short_course'=>'yes', 'type'=>1 ];
				$data = $this->db->prepare_data(self::invoice_items,$line);
				if(isset($keys[$k])) $data['id'] = $keys[$k];
				$this->db->insert( 'lead_invoice_items', $data );
			}
		}

		function update_invoice_items($args)
		{
			//echo "<pre>".print_r($args,1)."</pre>";exit();
			if( !isset($args['order_id']) ) return [ 'success'=>false, 'message'=>'Order ID is required' ];
			if( !isset($args['invoice_id']) ) return [ 'success'=>false, 'message'=>'Invoice ID is required' ];
			$error = [ 'success'=>false, 'message'=>'Unable to update your order. Check your data and try again' ];

			$preferences = $this->preferences();
			$vat = isset($preferences['vat']) && $preferences['vat'] = $preferences['vat'] = 0;

			$order_id = $args['order_id'];
			$invoice_id = $args['invoice_id'];
			if (isset($args['products'])) {
			$products = $args['products'];
			$products = $products ? $this->json->decode($products, true) : [];
			}elseif (isset($args['baskets'])) {
				$baskets=$args['baskets'];
				$baskets = $baskets ? $this->json->decode($baskets, true) : [];
				//echo "<pre>".print_r($baskets,1)."</pre>";exit();
				$products=[];
				foreach ($baskets as $bkey => $bvalue) {
					if (count($bvalue['items'])>0) {
						foreach ($bvalue['items'] as $ikey => $ivalue) {
							$ivalue['basket_id']=$bvalue['cart_basket_id'];
							if ($ivalue['product_id']>0) {
								$products[]=$ivalue;
							}
							
						}
					}
				}
			}

			
			//echo "<pre>".print_r($products,1)."</pre>";exit();
			if(is_array($products)) {
				try {
					$this->db->trans_start();
					$where_invoice = ['rel_id' => $invoice_id, self::invoice_items['category'] => 'product', 'usergroup' => $this->usergroup];
					$current = $this->db->get_rows('id', 'lead_invoice_items', $where_invoice);
					$invoice_item_keys = $current ? array_map(function($row) {
						return $row['id'];
					}, $current) : [];
					$this->db->delete('lead_invoice_items', $where_invoice);

					$where_order = ['rel_id' => $order_id, 'usergroup' => $this->usergroup];
					$current = $this->db->get_rows(self::product_order_items, 'sis_product_order_items', $where_order);
					$order_item_keys = $current ? array_map(function($row) {
						return $row['id'];
					}, $current) : [];
					$this->db->delete('sis_product_order_items', $where_order);

					$products = $this->product_changes( $products, $current );
					$comment = '';
					$order_total = $subtotal = $vat_amount = 0;
					//get booking id
					$bookind_id = $this->db->fetch_field(self::product_orders['booking_id'],'sis_product_orders', ['id'=>$order_id]);
					$added_attendees = false;

					if ($bookind_id) {
						$select = "count(sis_sched_booking_detail.id) as quantity, db14960 as payment_required, db14951 as price, db14982 as product_name, db14977 as product_id";
						$this->db->join('sis_scheduled_booking', 'sis_scheduled_booking.id = db15052');
						$this->db->join('sis_course_schedule', 'sis_course_schedule.id = db14977');
						$this->db->where("(db59978 = 2 or db59978 = 3)");
						$this->db->where('sis_scheduled_booking.id', $bookind_id);

						$attendees = $this->db->get_row($select, 'sis_sched_booking_detail'); 
						if (isset($attendees['payment_required'])&& $attendees['payment_required'] !='no') {
							$added_attendees = true;
							$products[] = $attendees;
						}

					}
					foreach($products as $k => $p) {
						$products[$k]['title'] = $p['product_name'];
						$products[$k]['id'] = $p['product_id'];
						$t = $st = $p['quantity'] * $p['price'];
						$va = 0;
						if($vat) { // price with vat = price + ( price * vat )
							$st = $t / (1 + ($vat / 100)); // price without vat = price with vat / ( 1 + vat)
							$va = $t - $st;
						}
						$order_total += $t;
						$subtotal += $st;
						$vat_amount += $va;
						if(isset($p['comment'])) $comment .= '<li>'.$p['comment'].'</li>';
					}
					if ($added_attendees) {
						array_pop($products);
					}
					if($comment) $comment  = '<ol>'.$comment.'</ol>';
					$f = self::product_orders;
					$data[$f['order_total']] = $order_total;
					$data[$f['subtotal']] = $subtotal;
					$data[$f['vat_amount']] = $vat_amount;

					$order = $this->db->get_row(self::product_orders, 'sis_product_orders', ['id' => $order_id, 'usergroup' => $this->usergroup]);

					$this->add_invoice_items($invoice_id, $products, $invoice_item_keys);
					$d = $this->add_order_items($order, $products, $preferences, $order_item_keys);
					if($d!==true) throw new Exception('Unable to update order items. Please check your products and try again.');

					$u = $this->db->update('lead_invoice_settings', [self::invoice_settings['invoice_total'] => $order_total], ['id' => $invoice_id, 'usergroup' => $this->usergroup]);
					if(!$u) throw new Exception('Unable to update invoice. Please check your details and try again');

					$u = $this->db->update('sis_product_orders', $data, ['id' => $order_id, 'usergroup' => $this->usergroup]);
					if(!$u) throw new Exception('Unable to update order. Please check your details and try again');

					$order_details = $this->orders(['id'=>$order_id]);
					$logs = [];
					if( $order_details && $comment ){
						$logs = $this->send_order_updated_email( $order_details, $comment, $order_details['user_id'] == $this->user_id );
					}

					$h = [ 'order_id'=>$order_id, 'message'=>$comment ];
					$log_id = $this->log_order_history( self::log_actions['products_change'], $h, $logs );

					$this->db->trans_commit();

					$log = false;
					if($log_id){
						$log = $this->get_order_history( $args['order_id'], $log_id );
					}

					$order = $this->orders(['id'=>$order_id]);
					return [ 'success'=>true, 'message'=>'Order updated successfully', 'order'=>$order, 'log'=>$log ];
				}
				catch(Exception $e){
					if( $this->db->in_transaction() ) $this->db->trans_rollback();
					return [ 'success'=>false, 'message'=>$e->getMessage() ];
				}
			}
			return $error;
		}

		function product_changes( $new, $old )
		{
			$products = [];
			foreach( $old as $k=>$v ){
				$products[$v['product_id']] = $v;
			}
			foreach( $new as $k=>$v ){
				$pid = $v['product_id'];
				if( isset($products[$pid]) ){
					$op = $products[$pid];
					$new[$k]['changed'] = $c = $op['quantity'] != $v['quantity'];
					if($c) $new[$k]['comment'] = "{$op['product_name']} <b>quantity changed</b> from <b>{$op['quantity']}</b> to <b>{$v['quantity']}</b>.";
				}
				else{
					$new[$k]['changed'] = true;
					$new[$k]['comment'] = "Added new product, {$new[$k]['product_name']}. ";
				}
			}
			return $new;
		}

		function create_order( $args, $products, $prefs ){

			$data = [ 'invoice_id'=>$args['invoice_id'], 'currency'=>$args['currency'] ];

			// calculate order totals
			$vat = isset($prefs['vat']) && $prefs['vat'] = $prefs['vat'] = 0;
			$order_total = $subtotal = $vat_amount = 0;
			if (!empty($args['attendees'])) {
				$products[] = $args['attendees'];
			}
			foreach( $products as $p ){
				$t = $st = $p['quantity'] * $p['price'];
				$va = 0;
				if($vat){ // price with vat = price + ( price * vat )
					$st = $t / ( 1 + ($vat/100) ); // price without vat = price with vat / ( 1 + vat)
					$va = $t - $st;
				}
				$order_total += $t;
				$subtotal += $st;
				$vat_amount += $va;
			}

			$data['order_total'] = $order_total;
			$data['subtotal'] = $subtotal;
			$data['vat_amount'] = $vat_amount;
			$amq = ''; // amount paid query
			// payment and order status
			if( $args['payment_method']=='invoice' ){
				$data['payment_status'] = self::payment_statuses['agreed'];
				$data['payment_method'] = 'invoice';
				$data['order_status'] = self::order_statuses['awaiting_approval']; 
				if (count($this->get_cart_prerequisites(true)['cart'])>0) { 
                    //$data['payment_status'] = self::payment_statuses['authorised'];
				    $data['order_status'] = self::order_statuses['awaiting_prereq_aproval'];
				}				
				
			}
			elseif($args['payment_method']=='card'){
				$amt_field = self::student_fees['amount'];
				$this->db->where( 'sis_student_fees.usergroup', $this->usergroup );
				$this->db->where( 'lead_invoice_settings.id', $args['invoice_id'] );
				$this->db->join( 'lead_invoice_settings', "lead_invoice_settings.username_id = ".self::student_fees['payment_for'], 'inner' );
				$amount_paid = $this->db->fetch_field( "SUM({$amt_field})", 'sis_student_fees' );
				$apq = $this->db->get_last_query();

				if($amount_paid < $order_total ){
					$data['payment_status'] = self::payment_statuses['pending'];
					$data['order_status'] = self::order_statuses['awaiting_payment'];
				}
				else{
					$data['order_status'] = self::order_statuses['processing'];
					$data['payment_status'] = self::payment_statuses['paid'];
				}
				if (count($this->get_cart_prerequisites(true)['cart'])>0) { 
                    $data['payment_status'] = self::payment_statuses['authorised'];
				    $data['order_status'] = self::order_statuses['awaiting_prereq_aproval'];
				}
				$data['payment_method'] = 'card';
			}

			if (!empty($args['booking_id'])) {
				$data['booking_id'] = $args['booking_id'];
			}
			$card = $args['payment_details'] ? $args['payment_details'] : [];
			if( is_string($card) ) $card = $this->json->decode($card, true);
			if( isset($card['payment_method']) && $card['payment_method']==='stripe' && isset($card['paymentIntent']) ){
				$data['payment_intent'] = $card['paymentIntent']['id'];
			}
            //echo "<pre>".print_r($data,1)."</pre>";exit();
			$data2 = $this->db->prepare_data( self::product_orders, $data  );
			//echo "<pre>".print_r($data2,1)."</pre>";exit();
			$id = $this->db->insert('sis_product_orders', $data2);
			if($id) $data['id'] = $id;
			$data['apq'] = $apq;
			return $data;
		}

		function create__empty_order($args, $products, $prefs){
            $data = [ 'invoice_id'=>$args['invoice_id'], 'currency'=>$args['currency'] ];

            $inv = $this->db->get_row( 'username_id as payment_for, '.self::invoice_settings['invoice_total'].' as  amount, '.self::invoice_settings['currency'].' as currency', 'lead_invoice_settings', ['id'=>$args['invoice_id']] );

			// calculate order totals
			$vat = isset($prefs['vat']) && $prefs['vat'] = $prefs['vat'] = 0;
			$order_total = $subtotal = $vat_amount = 0;
			$data['order_total'] = $inv['amount'];
			$data['subtotal'] = $subtotal;
			$data['vat_amount'] = $vat_amount;
			$amq = ''; // amount paid query
			// payment and order status
			if( $args['payment_method']=='invoice' ){
				$data['payment_status'] = self::payment_statuses['agreed'];
				$data['payment_method'] = 'invoice';
				$data['order_status'] = self::order_statuses['awaiting_approval']; 
			}
			elseif($args['payment_method']=='card'){
				$amt_field = self::student_fees['amount'];
				$this->db->where( 'sis_student_fees.usergroup', $this->usergroup );
				$this->db->where( 'lead_invoice_settings.id', $args['invoice_id'] );
				$this->db->join( 'lead_invoice_settings', "lead_invoice_settings.username_id = ".self::student_fees['payment_for'], 'inner' );
				$amount_paid = $this->db->fetch_field( "SUM({$amt_field})", 'sis_student_fees' );
				$apq = $this->db->get_last_query();
					$data['payment_status'] = self::payment_statuses['pending'];
					$data['order_status'] = self::order_statuses['awaiting_payment'];
				
				$data['payment_method'] = 'card';
			}

			if (!empty($args['booking_id'])) {
				$data['booking_id'] = $args['booking_id'];
			}
			$card = $args['payment_details'] ? $args['payment_details'] : [];
			if( is_string($card) ) $card = $this->json->decode($card, true);
			if( isset($card['payment_method']) && $card['payment_method']==='stripe' && isset($card['paymentIntent']) ){
				$data['payment_intent'] = $card['paymentIntent']['id'];
			}

			$data2 = $this->db->prepare_data( self::product_orders, $data  );
			//echo "<pre>".print_r($data2,1)."</pre>";exit();
			$id = $this->db->insert('sis_product_orders', $data2);
		    //echo $this->db->get_last_query();exit();
			if($id) $data['id'] = $id;
			$data['apq'] = $apq;
			return $data;
		}

		function add_order_items($order, $products, $preferences, $keys=[] ){
			$r = true;
			$vat = isset($preferences['vat']) && $preferences['vat'] = $preferences['vat'] = 0;

			foreach($products as $k=>$p){
				$line = [ 'product_id'=>$p['id'], 'product_name'=>$p['title'], 'quantity'=>$p['quantity'],'o_notes'=>$p['o_notes'] ];

				if( isset($p['changed']) ){
					if( $p['changed'] ){
						$line['status'] = $line['quantity']<1 ? self::fulfilment_statuses['cancelled'] : self::fulfilment_statuses['pending'];
					}
					elseif(isset($p['status'])) $line['status'] = $p['status'];
					else{
						$line['status'] = $order['order_status'] >= self::order_statuses['processing'] ? self::fulfilment_statuses['processing'] : self::fulfilment_statuses['pending'];
					}
				}
				else{
					$line['status'] = $order['order_status'] >= self::order_statuses['processing'] ? self::fulfilment_statuses['processing'] : self::fulfilment_statuses['pending'];
				}

				$vat_price = $price = 1 * $p['price'];
				$vat_amount = 0;
				if($vat){ // price with vat = price + ( price * vat )
					$price = $vat_price / ( 1 + ($vat/100) ); // price without vat = price with vat / ( 1 + vat)
					$vat_amount = $vat_price - $price;
				}
				if (isset($p['basket_id'])) {
					// code...
					$line['basket_id']=$p['basket_id'];
				}

				$line['price'] = $price;
				$line['subtotal'] = $vat_price * $p['quantity'];
				$line['vat_amount'] = $vat_amount;

				$data = $this->db->prepare_data(self::product_order_items,$line);
				$data['rel_id'] = $order['id'];
				if( isset($keys[$k]) ) $data['id'] = $keys[$k];
				$e = $this->db->insert( 'sis_product_order_items', $data );
				//update prerequisites 
				$this->db->update('sis_order_item_prerequisites',['rel_id'=>$e],['db90581'=>$p['cart_id']]);
				$r = $e && $r;
			}
			return $r;
		}

		function empty_cart(){
			return $this->db->delete( 'sis_products_cart', ['rec_id'=>$this->user_id] );
		}

		function orders($args=[]){
			$one = false;
            $sf = self::student_fees;
			if (!empty($args['order_items_filter_sql'])) {
				$this->db->where('1 '.$args['order_items_filter_sql']);
				$order_ids=$this->db->get_rows('rel_id','sis_product_order_items');
				$order_id1_sql= ' and sis_product_orders.id in ('.$this->db->get_last_query().')';

				//echo "<pre>".print_r($order_ids,1)."</pre>";exit();
			}

			if (!empty($args['order_items_fulfilment_filter_sql'])) {
				$this->db->where('1 '.$args['order_items_fulfilment_filter_sql']);
				$order_ids=$this->db->get_rows('rel_id','sis_product_order_fulfilment_history');
				$order_id2_sql= ' and sis_product_orders.id in ('.$this->db->get_last_query().')';

				//echo "<pre>".print_r($order_ids,1)."</pre>";exit();
			}


			if (!empty($args['order_basket_filter_sql'])) {
				$this->db->where('1 '.$args['order_basket_filter_sql']);
				$order_ids=$this->db->get_rows('db87437','sis_cart_basket');
				$order_id3_sql= ' and CASTsis_product_orders.id AS CHAR) in ('.$this->db->get_last_query().')';

				echo "<pre>".print_r($order_ids,1)."</pre>";
			}


			if (!empty($args['order_payments_filter_sql'])) {
				$this->db->where('1 '.$args['order_payments_filter_sql']);
				$this->db->join( 'lead_invoice_settings', "lead_invoice_settings.username_id = ".self::student_fees['payment_for'], 'inner' );
				$order_ids=$this->db->get_rows('lead_invoice_settings.id','sis_student_fees');
				$order_id4_sql= ' and sis_product_orders.'.self::product_orders['invoice_id'].' in ('.$this->db->get_last_query().')';

				//echo "<pre>".print_r($order_ids,1)."</pre>";exit();
			}

			$search = empty($args['search']) ? false : trim($args['search']);
			if( $search ){
                $this->db->like(self::student_fees['token'], $search);
				$this->db->join( 'lead_invoice_settings', "lead_invoice_settings.username_id = ".self::student_fees['payment_for'], 'inner' );
				$order_ids=$this->db->get_rows('lead_invoice_settings.id','sis_student_fees');
				$order_payments_search_sql= ' sis_product_orders.'.self::product_orders['invoice_id'].' in ('.$this->db->get_last_query().')';

				foreach (self::invoice_account as $invoice_accountkey => $invoice_accountvalue) {
					if (!in_array($invoice_accountkey, ['rel_id','id','rec_id'])) {
						$this->db->or_like(self::invoice_account[$invoice_accountkey], $search);
			}
				}

				//echo "<pre>".print_r(self::invoice_account,1)."</pre>";
                
				//$this->db->join( 'lead_invoice_settings', "lead_invoice_settings.username_id = ".self::student_fees['payment_for'], 'inner' );
				$order_ids=$this->db->get_rows('rel_id','lead_invoice_account');
				$order_invoice_account_search_sql= ' sis_product_orders.'.self::product_orders['invoice_id'].' in ('.$this->db->get_last_query().')';

				//echo $order_invoice_account_search_sql;exit();

				$f = array_merge( self::cart_basket, self::sis_address );
			    $this->db->join('sis_address',self::cart_basket['basket_ad_id'].'=sis_address.id','left' );
			    foreach ($f as $invoice_accountkey => $invoice_accountvalue) {
					if (!in_array($invoice_accountkey, ['rel_id','id','rec_id'])) {
						$this->db->or_like($f[$invoice_accountkey], $search);
					}
				}

				$order_ids=$this->db->get_rows("CAST(".self::cart_basket['order_id']." AS SIGNED)",'sis_cart_basket');
				$order_basket_search_sql= ' sis_product_orders.'.self::product_orders['id'].' IN ('.$this->db->get_last_query().')';

			}

           //echo "<pre>".print_r($args,1)."</pre>";exit();
			if( isset($args['id']) && is_numeric($args['id']) ){
				$this->db->where( 'sis_product_orders.id', $args['id'] );
				$one = true;
			}

			$count = isset($args['count']) && $args['count'];
			if( !$count && isset($args['limit']) && is_numeric($args['limit'])){
				$start = isset($args['start']) && is_numeric($args['start']) ? $args['start'] : 0;
				$this->db->limit( $args['limit'], $start );
			}

			if(isset($args['status']) && $args['status']){
				$status = $args['status'];
				if($status==='active'){ $this->db->where( self::product_orders['order_status'].' not in (5,23,26,29)' );
				}elseif($status==='awaitingshipment'){ $this->db->where( self::product_orders['order_status'].'  in (14,17)' );

				}elseif($status==='complete'){ $this->db->where( self::product_orders['order_status'], self::order_statuses['completed'] );
				}elseif($status==='onhold'){ $this->db->where( self::product_orders['order_status'], self::order_statuses['onhold'] );
				}elseif($status==='notyetshipped'){ 
					//echo "(".self::product_orders['order_status']."= ".self::order_statuses['awaiting_shipment'] ." or ". self::product_orders['order_status']." = " .self::order_statuses['partially_shipped']." or ". self::product_orders['order_status']." = ".self::order_statuses['processing']." or ". self::product_orders['order_status']." = ".self::order_statuses['awaiting_prereq_aproval'].")";
					 $this->db->where("(".self::product_orders['order_status']." = ".self::order_statuses['awaiting_shipment'] ." or ". self::product_orders['order_status']." = ".self::order_statuses['partially_shipped']." or ". self::product_orders['order_status']."=".self::order_statuses['processing']." or ". self::product_orders['order_status']."=".self::order_statuses['awaiting_prereq_aproval'].")" )->where("(".self::product_orders['order_total'].">0)");
				} elseif($status==='partlyshipped'){ $this->db->where( self::product_orders['order_status'], self::order_statuses['partially_shipped'] );
				} elseif($status==='awaitingpayment'){
				 $this->db->where( self::product_orders['payment_method'], "invoice" )->having("(order_total-IFNULL(amount_paid,0))>0 and (SELECT count({$sf['amount']}) FROM sis_student_fees WHERE {$sf['payment_for']} = lead_invoice_settings.username_id AND ({$sf['payment_or_refund']} LIKE 'invoice_update' AND  {$sf['payment_method']} LIKE '3rd_party_invoice'))>0");
				}elseif($status==='raiseinvoice'){
					 $this->db->where( self::product_orders['payment_method'], 'invoice' )->where( self::product_orders['order_status'].'  in (8,11,14,17,20)' )->where( self::product_orders['payment_status'].' not in (6)')->having('(order_total-IFNULL(amount_paid,0))>0 and IFNULL(amount_paid,0)=0');
				}elseif($status==='failed'){ $this->db->where( self::product_orders['order_status'].' in (26,29)');
			     }
			}

			if(isset($args['paginate'])) $this->db->paginate($args['paginate']);

			$f1 = $this->db->prepend_fields( self::product_orders, 'sis_product_orders' );
			$f2 = $this->db->prepend_fields( self::payment_status, 'sis_sched_booking_stages' );
			$f3 = $this->db->prepend_fields( self::order_status, 'sis_product_order_status' );
			$f4 = $this->db->prepend_fields( self::form_users, 'form_users' );
			$fields = array_merge( $f1, $f2, $f3, $f4 );
			$fields['invoice_username'] = 'lead_invoice_settings.username_id';
			$fields['organisation'] = self::invoice_account['organisation'];
			$fields['usergroup'] = 'sis_product_orders.usergroup';
			$fields['booking_username'] = 'sis_scheduled_booking.username_id';


			if($this->user_id !== false ) $this->db->where( 'sis_product_orders.rec_id', $this->user_id );
			$this->db->where( 'sis_product_orders.usergroup', $this->usergroup );
			$this->db->where('sis_product_orders.rec_archive IS NULL');

			if($count){
				return $this->db->fetch_field( 'count(sis_product_orders.id)', 'sis_product_orders' );
			}

			$search = empty($args['search']) ? false : trim($args['search']);
			if( $search ){
				if(is_numeric($search)){ 
                    $this->db->group_start();
					$this->db->where('sis_product_orders.id',$search);
					$this->db->or_like(self::invoice_account['purchase_order_number'], $search);
					$this->db->or_like(self::product_orders['booking_id'],$search);
					$this->db->or_like($order_invoice_account_search_sql);
					$this->db->or_like($order_basket_search_sql);
					
					$this->db->group_end();
		           }else {
					$this->db->group_start();
					$this->db->like(self::invoice_account['organisation'], $search);
					$this->db->or_like(self::invoice_account['purchase_order_number'], $search);
					$this->db->or_like(self::form_users['first_name'], $search);
					$this->db->or_like(self::form_users['surname'], $search);
					$this->db->or_like('lead_invoice_settings.username_id', $search);
					$this->db->or_like($order_payments_search_sql);
					$this->db->or_like($order_invoice_account_search_sql);
					$this->db->or_like($order_basket_search_sql);
					$this->db->group_end();
				}
			}

			if (!empty($args['filter_sql'])) {
				$this->db->where($args['filter_sql']);
			}

			if (!empty($args['order_items_filter_sql'])) {
				$this->db->where($order_id1_sql);
			}

			if (!empty($args['order_items_fulfilment_filter_sql'])) {
				$this->db->where($order_id2_sql);
			}

			if (!empty($args['order_basket_filter_sql'])) {
				$this->db->where($order_id3_sql);
			}

			if (!empty($args['order_payments_filter_sql'])) {
				$this->db->where($order_id4_sql);
			}





			$this->db->join('sis_sched_booking_stages',"sis_sched_booking_stages.id = ".self::product_orders['payment_status'],'left' );
			$this->db->join('sis_product_order_status', "sis_product_order_status.id = ".self::product_orders['order_status'], 'left');
			$this->db->join( 'lead_invoice_settings', 'lead_invoice_settings.id = '.self::product_orders['invoice_id'], 'left' );
			$this->db->join( 'form_users', 'form_users.id = sis_product_orders.rec_id', 'left' );
			$this->db->join( 'lead_invoice_account', 'lead_invoice_account.rel_id = lead_invoice_settings.id', 'left' );
			$this->db->join( 'sis_scheduled_booking', 'sis_scheduled_booking.id ='.self::product_orders['booking_id'], 'left' );
			$this->db->select( 'SUM('.self::product_order_items['quantity'].')', 'total_items' );
			$this->db->select( 'abv, symbol' );
			$this->db->join( 'system_currency', 'system_currency.id = '.self::product_orders['currency'], 'left' );

			// Get amount paid
			$inner_q = "(SELECT SUM({$sf['amount']}) FROM sis_student_fees WHERE {$sf['payment_for']} = lead_invoice_settings.username_id AND ({$sf['payment_or_refund']} LIKE 'Payment' OR {$sf['payment_or_refund']} LIKE 'invoice_update' AND  {$sf['payment_method']} LIKE '3rd_party_invoice')) - COALESCE((SELECT SUM({$sf['amount']}) FROM sis_student_fees WHERE {$sf['payment_for']} = lead_invoice_settings.username_id AND {$sf['payment_or_refund']} LIKE 'Refund'),0)";
			$this->db->select( "($inner_q)", 'amount_paid' );
			//$this->db->select( 'SUM('.self::student_fees['amount'].')', 'amount_paid' );
			//$this->db->join('sis_student_fees',"lead_invoice_settings.username_id = ".self::student_fees['payment_for'],'inner' );

			$this->db->join( 'sis_product_order_items', 'sis_product_order_items.rel_id = sis_product_orders.id', 'left' );
			$this->db->group_by( 'sis_product_orders.id' );
			// $this->db->order_by( 'sis_product_orders.id desc' );
			if( isset($args['sort']) && $args['sort'] && isset( self::sort_orders[$args['sort']] ) ){
				$sort = self::sort_orders[$args['sort']];
				$this->db->order_by( $sort['sort'] );
			}
			else{
				$this->db->order_by( self::sort_orders[1]['sort'] );
			}

			$orders = $one ?  $this->db->get_row($fields, 'sis_product_orders') : $this->db->get_rows($fields, 'sis_product_orders');


			//echo $this->db->get_last_query();exit();
			//echo "<pre>".print_r($orders,1)."</pre>";exit();
			if($orders && $one){
				$orders['items'] = $this->get_order_items($orders['id']);
				$orders['baskets'] = $this->get_order_baskets_items($orders['id']);
				$orders['details'] = $this->get_invoice_details($orders['invoice_id']);
				//echo $this->db->get_last_query();exit();
				$orders['pre_requisites']=$this->get_order_prequisites($orders['id']);
			}
            //echo "<pre>".print_r($orders['baskets'],1)."</pre>";exit();
			return $orders ? $orders : [];

		}

		function last_query(){
			return $this->db->get_last_query();
		}

		function get_invoice_details($invoice_id){
			$this->db->where( 'rel_id', $invoice_id )->where( 'usergroup', $this->usergroup )->where( 'rec_id', $this->user_id );
			$this->db->select( 'dc.country_name', 'delivery_country_name' );
			$this->db->select( 'bc.country_name', 'billing_country_name' );
			$this->db->join( 'system_countrylist dc', 'dc.id = '.self::invoice_account['delivery_country'], 'left' );
			$this->db->join( 'system_countrylist bc', 'bc.id = '.self::invoice_account['billing_country'], 'left' );
			$inv = $this->db->prepend_fields( self::invoice_account, 'lead_invoice_account' );
			return $this->db->get_row( $inv, 'lead_invoice_account' );
		}

		function get_order_baskets_items($order_id){
             $this->db->where('sis_cart_basket.usergroup', $this->usergroup)->where("sis_cart_basket.rec_archive IS NULL")->where('db87437',$order_id);
			$baskets =  $this->db->get_rows(self::cart_basket, 'sis_cart_basket');
			$baskets_query = $this->db->get_last_query();
			dev_debug($baskets_query);
			$new_basket=[];
			if (isset($baskets[0])) {


				foreach ($baskets as $bkey => $bvalue) {
					// code...
                   
					$baskets[$bkey]['items']=$this->get_order_items($order_id,$bvalue['cart_basket_id']);
					$new_basket[$bvalue['cart_basket_id']]=$baskets[$bkey];
				}
			}

          //echo
			return $new_basket;
		}

		function get_order_items($order_id,$basket_id=null,$group_baskets=null){
			$f = $this->db->prepend_fields( self::product_order_items, 'p' );
			$f['shipping_method_name']=self::shipping_methods['shipping_method_name'];
			$this->db->select( self::fulfilment_status );
			$this->db->join( 'sis_product_order_fulfilment_status fs', 'fs.id = '.self::product_order_items['status'], 'left' );
			$this->db->where( 'p.rel_id', $order_id )->where( 'p.usergroup', $this->usergroup );
			// $this->where( 'p.rec_id', $this->user_id );  /* causing problems since admin can also add products to someone's order */
			if (null!=$basket_id) {
				// code...
				$this->db->where('db86435',$basket_id);
			}

			if (null!=$group_baskets) { 
                $this->db->group_by('db86435');
			}
			$this->db->join( 'sis_product_shipping_methods sh', 'sh.id = '.self::product_order_items['shipping_method'], 'left' );
			$this->db->from( 'sis_product_order_items', 'p' );
			$order_items= $this->db->get_rows( $f );
			//echo $this->db->get_last_query(); exit();
			if (isset($order_items[0])) {
			   foreach ($order_items as $orkey => $orvalue) {
			   	// code...
			   	$order_items[$orkey]['prerequisites']=$this->get_order_item_prerequisites($orvalue['id']);

			   }
			}

			//echo "<pre>".print_r($order_items,1)."</pre>";exit();

			return $order_items;
		}


		function get_order_item_prerequisites($line_item_id){
			$this->db->where('rel_id',$line_item_id);
			$pre_req= $this->db->get_rows(self::order_item_prerequisites,'sis_order_item_prerequisites');
			//echo "<pre>".print_r($pre_req,1)."</pre>";exit();
			if (isset($pre_req[0])) {
				foreach ($pre_req as $key => $value) {
					$files=$this->db->where('id',$value['short_course_certificate'])->get_rows(self::files,'form_file');
					foreach ($files as $fkey => $fvalue) {
						// code...
						$files[$fkey]['mimetype']=mime_content_type(env('FILES_STORAGE_PATH').'/media/'.$fvalue['file_path']);
						$files[$fkey]['filesize']=(filesize(env('FILES_STORAGE_PATH').'/media/'.$fvalue['file_path'])) ;
						$files[$fkey]['uri']=engine_url("/media/dl.php?fl=".encode($fvalue['file_path']));
						$files[$fkey]['dropzone_link']=(isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]".("/admin/sis_products/dropzone_links/".($fvalue['file_path']));
					}
					$pre_req[$key]['attachments']=$files;
				}
			}

			return $pre_req;
		}

		function get_order_prequisites($order_id){
			$this->db->join('sis_order_item_prerequisites', 'p.id = sis_order_item_prerequisites.rel_id', 'inner' );
			$this->db->join( 'core_courses', "db105662 = core_courses.id", 'inner' );
			//$this->db->join( 'sis_product_order_items', 'sis_product_order_items.rel_id = sis_product_orders.id', 'left' );
            $this->db->join('sis_products','db70007=sis_products.id','left');
			$this->db->where('p.rel_id', $order_id )->where('p.usergroup', $this->usergroup );
			$inf=self::order_item_prerequisites;
			$inf['course_name']='db232';
			$inf['product_title']='db34755';
			$inf['product_id']='db70007';
			$pre_req= $this->db->get_rows($inf,'sis_product_order_items p');
			//echo $this->db->get_last_query();exit();

			if (isset($pre_req[0])) {
				foreach ($pre_req as $key => $value) {
					$files=$this->db->where_in('id',explode(',',$value['short_course_certificate']))->get_rows(self::files,'form_file');
					foreach ($files as $fkey => $fvalue) {
						// code...
						
						$files[$fkey]['mimetype']=mime_content_type(env('FILES_STORAGE_PATH').'/media/'.$fvalue['file_path']);
						$files[$fkey]['filesize']=(filesize(env('FILES_STORAGE_PATH').'/media/'.$fvalue['file_path']));
						//$files[$fkey]['storage_path']=env('FILES_STORAGE_PATH').'/media/'.$fvalue['file_path'];
						$files[$fkey]['uri']=engine_url("/media/dl.php?fl=".encode($fvalue['file_path']));
						$files[$fkey]['dropzone_link']=(isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]".("/admin/sis_products/dropzone_links/".($fvalue['file_path']));
					}
					$pre_req[$key]['attachments']=$files;
					$reason=$this->db->where('rel_id',$value['pre_req_id'])->get_row(self::prereqs_reject_reasons,'sis_prereqs_reject_reasons');
					$pre_req[$key]['rejection_reason']=$reason['reason']??'';
				}
			}

			//echo "<pre>".print_r($pre_req,1)."</pre>";exit();

			return $pre_req;

		}




		function process_payment($args, $invoice_id){
			$success = [ 'success'=>true, 'message'=>'Payment process successfully' ];
			if($args['payment_method']=='card'){
				$card = $args['payment_details'] ? $args['payment_details'] : [];
				if( is_string($card) ) $card = $this->json->decode($card, true);
				if( isset($card['payment_method']) && $card['payment_method']==='stripe' && isset($card['paymentIntent']) ){
					$success = $this->process_stripe($card['paymentIntent'],$invoice_id);
				}
			}

			return $success;
		}

		function process_invoice_payment($args,$invoice_id ){

				$data = [
					'token'=>'',
					'payment_method'=>'invoice',
					'payment_for'=>'',
					// 'amount'=> $intent['amount'] / 100,
					// 'currency'=>$intent['currency'],
					'payment_or_refund'=>'Payment',
					'comment'=>''
				];
				//$this->db->select( self::invoice_settings['currency'], 'currency' );
				// self::invoice_settings['invoice_total'] self::invoice_settings['currency']
				$inv = $this->db->get_row( 'username_id as payment_for, '.self::invoice_settings['invoice_total'].' as  amount, '.self::invoice_settings['currency'].' as currency', 'lead_invoice_settings', ['id'=>$invoice_id] );
				if($inv) $data = array_merge( $data, $inv );
				if (isset($args['order_total'])&&array_key_exists('amount_paid', $args)) {
					//echo "here";
					$data['amount']=$args['order_total']-$args['amount_paid'];

					if ($data['amount']<0) {
						return;
					}
				}
				// echo "<pre>".print_r($inv,1)."</pre>";
				//  echo "<pre>".print_r($args,1)."</pre>";
				// echo "<pre>".print_r($data,1)."</pre>";exit();
				$e = $this->add_payment($data);
				return $e ? [ 'success'=>true, 'message'=>'Stripe payment recorded successfully' ] : [ 'success'=>false, 'message'=>'Could not record payment details' ];
			
		}


		function process_stripe( $intent, $invoice_id ){
			if( isset($intent['id']) && isset($intent['amount']) ){
				$data = [
					'token'=>$intent['id'],
					'payment_method'=>'stripe',
					'payment_for'=>'',
					'amount'=> $intent['amount'] / 100,
					'currency'=>$intent['currency'],
					'payment_or_refund'=>'Payment',
					'comment'=>$intent['status']
				];
				$this->db->select( self::invoice_settings['currency'], 'currency' );
				$inv = $this->db->get_row( 'username_id as payment_for', 'lead_invoice_settings', ['id'=>$invoice_id] );
				if($inv) $data = array_merge( $data, $inv );
				if($intent['status']!=='succeeded'){
					$data['amount'] = 0;
				}
				$e = $this->add_payment($data);
				return $e ? [ 'success'=>true, 'message'=>'Stripe payment recorded successfully' ] : [ 'success'=>false, 'message'=>'Could not record payment details' ];
			}
			return [ 'success'=>false, 'message'=>'Invalid payment details' ];
		}

		function add_manual_payment($args){

			if (array_key_exists('payment_id',$args)) {
				$e = true;
				$this->setUsergroup($args['usergroup']);

				//get order id
				$order_id = $this->db->fetch_field( 'id', 'sis_product_orders', [self::product_orders['invoice_id'] => $args['invoice_id'], 'usergroup' => $this->usergroup]);
				if ($order_id) {
					$args['order_id'] = $order_id;
				}

				// get booker details phone number email etc encoded
				$contacts = $this->order_contacts($args['invoice_id']);
				$payment = $this->db->get_row( self::student_fees, 'sis_student_fees', ['id' => $args['payment_id'], 'usergroup' => $this->usergroup]);
				$query = $this->db->get_last_query();
				if(!empty($payment)){
					$args = array_merge($payment, $args);
				}
				$args['phone'] = json_encode([]);
				$emails = array_keys($contacts['email']);
				$args['email'] = (!empty($emails[0]) ? json_encode([$emails[0]]):json_encode([]));
			}else{
				
				$data = [
					'token'=>$args['token'],
					'payment_method'=>$args['payment_method'],
					'payment_for'=>$args['invoice_username'],
					'amount'=> $args['amount']*1,
					'currency'=>$args['currency'],
					'payment_or_refund'=>$args['payment_or_refund'],
					'comment'=>$args['comment']
				];
				if(!empty($args['date'])) $data['date'] =str_replace(",", '', $args['date']);
				if (!empty($args['id'])) $data['id']=$args['id'];
				$e = $this->add_payment($data);
			}
		
			if($e){
				$log_id = $this->update_order_payment_status($args);

				$log = $log_id ? $this->get_order_history( $args['order_id'], $log_id ) : false;

				$payments = $this->get_payments($args['invoice_username']);
				$order = $this->orders(['id'=>$args['order_id']]);
				return [ 'success'=>true, 'message'=>'Payment recorded successfully', 'order'=>$order, 'payments'=>$payments, 'log'=>$log ];
			}
			return [ 'success'=>false, 'message'=>'Could not record payment. Please check your data and try again' ];
		}



		function get_currency_symbol($id){
			$s = $this->db->fetch_field('symbol','system_currency',['id'=>$id]);
			return $s?$s:'??';
		}

		function add_payment( $data ){
			$insert = $this->db->prepare_data( self::student_fees, $data );
			// unset($insert['date']);
			if (!empty($data['id'])) {
				return $this->db->update( 'sis_student_fees', $insert,['id'=>$data['id']] );
			}else{
			return $this->db->insert( 'sis_student_fees', $insert );
			}
		
		}

		function get_stripe_keys($type=false){
			$this->db->select('db43856', 'mode')->select( 'db37349', 'live_pk' )->select( 'db37351', 'test_pk' );
			$this->db->select('db37350','live_secret')->select('db37352', 'test_secret');

			$this->db->where( 'usergroup', $this->usergroup );
			$this->db->where( 'rec_archive IS NULL' );
			$keys = $this->db->get_row( false,'lead_preferences' );
			if($keys){
				if($type && $type==='pk') return $keys['mode']==='production' ? $keys['live_pk'] : $keys['test_pk'];
				if($type && $type==='secret') return $keys['mode']==='production' ? $keys['live_secret'] : $keys['test_secret'];
				return $keys;
			}
			return '';
		}

		function get_course_details($id){
			$this->db->where( 'usergroup', $this->usergroup )->where('id', $id)->where('rec_archive IS NULL');
			return $this->db->get_row(self::course, 'core_courses');
		}

		function stripe_intent($payload, $intent){

			$trackid = [
				'rec_id'=>$payload['applicantId'],
				'rel_id'=>$payload['applicantId'],
				'db43890' => $payload['invoiceId'] ,
				'db43888' => $intent->id ,
				'db43885'=>$payload['currency'],
				'db43883'=>$payload['amount']/100,
				'db43976'=>$payload['businessName'],
				'db43881'=>$payload['productName'],
				'db43973'=>$payload['customerEmail'],
				'db43974'=>$payload['customerName'],
				'db43882'=>$payload['paymentTowards'],
				'db43977'=>$payload['invoice_usernameid'],
				"db44513"=>$payload['application_short_course_id'],
				"db44514"=>$payload['stripe_product_id'],
				"db44515"=>$payload['stripe_product_username_id'],
				"db49861"=>$payload['stripe_ols_plan_id'],
				"db49862"=>$payload['stripe_ols_payment_details']
			];

		}

		function get_sort_id($token, $table='orders'){
			$t = $table=='orders' ? self::sort_orders : self::sort;
			$found = array_filter( $t, function($row) use ($token){
				return ( isset($row['token']) && $row['token'] === $token );
			});
			return $found ? key($found) : 1;
		}

        function get_product_sort(){
            $sort = [];
            foreach(self::sort as $key=>$data){
                $sort[] = ['id'=>$key, 'title'=>$data['title']];
            }
            return $sort;
        }

		function get_order_statuses(){
			return $this->db->get_rows( self::order_status, 'sis_product_order_status' );
		}
		function get_payment_statuses(){
			return $this->db->get_rows( self::payment_status, 'sis_sched_booking_stages' );
		}

		function get_fulfilment_statuses(){
			return $this->db->get_rows( array_merge(self::fulfilment_status,['id'=>'id']), 'sis_product_order_fulfilment_status' );
		}

		function get_payments( $invoice__username ){
			$f = $this->db->prepend_fields( self::student_fees, 'sis_student_fees' );
			$u = $this->db->prepend_fields( self::form_users, 'form_users' );
			$f['symbol'] = 'system_currency.symbol';
			$f['abv'] = 'system_currency.abv';
			$fields = array_merge( $f, $u );
			$this->db->join( 'system_currency', 'system_currency.id = '.$f['currency'], 'left' );
			$this->db->join( 'form_users', 'form_users.id = sis_student_fees.rec_id', 'left' );
			$this->db->where( self::student_fees['payment_for'], $invoice__username );
			$this->db->where( 'sis_student_fees.usergroup', $this->usergroup );
			$this->db->where( 'sis_student_fees.rec_archive IS NULL' );
			$this->db->order_by('sis_student_fees.id desc');
			$p = $this->db->get_rows( $fields, 'sis_student_fees' );
			return $p?$p:[];
		}

		function update_order_status($args){
			if(!isset($args['order_id'])) return [ 'success'=>false, 'message'=>'Order ID is required' ];
			else $order_id = trim($args['order_id']);
			if(!isset($args['status'])) return [ 'success'=>false, 'message'=>'Order Status ID is required' ];
			else $status = trim($args['status']);

			$args['invoice_username'] = isset($args['invoice_username']) ? $args['invoice_username'] : $this->get_invoice_number($args['order_id']);

			$e = $this->db->update( 'sis_product_orders', [ self::product_orders['order_status']=>$status ], ['id'=>$order_id,'usergroup'=>$this->usergroup] );

			$f = [ 'order_status'=>self::product_orders['order_status'], 'order_status_name'=>self::order_status['order_status_name'] ];
			$this->db->join( 'sis_product_order_status', 'sis_product_order_status.id = '.self::product_orders['order_status'], 'left' );
			$d = $this->db->get_row( $f, 'sis_product_orders', [ 'sis_product_orders.id'=>$order_id, 'sis_product_orders.usergroup'=>$this->usergroup ] );
			if($d){
				$args['subject'] = "Order {$args['invoice_username']} has been updated.";
				$emails = json_decode($args['email'], true);
				$phones = json_decode($args['phone'], true);
				$m = ( count($emails)>0 || count($phones)>0 ) ? $this->send_message($args, false) : [];
				$d['success'] = true;
				$d['message'] = 'Order status updated successfully';
				$d['messages'] = isset($m['messages'])?$m['messages']:false;
				$action = self::log_actions['status_update'];
				$log_id = $this->log_order_history( $action, $args, $m );
				$d['log'] = $this->get_order_history( $order_id, $log_id );
				return $d;
			}
			return [ 'success'=>false, 'message'=>'Unable to update order status.' ];
		}

		function get_invoice_number($order_id){
			$this->db->join('sis_product_orders', "lead_invoice_settings.id = sis_product_orders.".self::product_orders['invoice_id'], 'inner');
			$this->db->where( 'sis_product_orders.id', $order_id );
			$this->db->where('lead_invoice_settings.usergroup', $this->usergroup);
			return $this->db->fetch_field('lead_invoice_settings.username_id', 'lead_invoice_settings');
		}

		function log_order_history($action, $args, $messages=[] ){
			$f = self::order_history;

			if(!isset($args['order_id']) || !$args['order_id'] ) return false;
			$status = isset($args['status']) ? $args['status'] : $this->db->fetch_field( self::product_orders['order_status'], 'sis_product_orders', ['id'=>$args['order_id']] );

			$data = [
				'rel_id'=>$args['order_id'],
				$f['status']=>$status,
				$f['subject']=>$action,
				$f['comment']=>$args['message'],
				// $f['email'] => isset($args['email']) ? $args['email'] : null,
				$f['email'] => isset($messages['email_logs']) ? json_encode($messages['email_logs']) : null,
				// $f['phone'] => isset($args['phone']) ? $args['phone'] : null,
				$f['phone'] => isset($messages['sms_logs']) ? json_encode($messages['sms_logs']) : null,
			];
			return $this->db->insert( 'sis_product_order_status_history', $data );
		}

		function get_order_history($order_id, $id=false){
			$f1 = $this->db->prepend_fields( self::order_history, 'oh' );
			$this->db->select( self::form_users['first_name'], 'first_name' );
			$this->db->select( self::form_users['surname'], 'surname' );
			$this->db->select( self::order_status['order_status_name'], 'order_status_name' );
			$this->db->join( 'form_users', 'form_users.id = oh.rec_id', 'left' );
			$this->db->join( 'sis_product_order_status', 'sis_product_order_status.id = '.self::order_history['status'], 'left' );
			$this->db->where( 'oh.rel_id', $order_id )->where( 'oh.usergroup', $this->usergroup )->where( 'oh.rec_archive IS NULL' );
			if($id) $this->db->where( 'oh.id', $id );
			$this->db->order_by('oh.id desc');
			$logs = $this->db->get_rows( $f1, 'sis_product_order_status_history oh' );
			if(!$logs) return [];

			foreach($logs as $k=>$log){
				$emails = json_decode($log['email'], true);
				$ids = !empty($emails) ? array_map( function($a,$b){return $b;}, $emails, $emails ) : [];
				if(!empty($ids)){
					$this->db->where_in( 'id', $ids );
					$logs[$k]['email_log'] = $this->db->get_rows( self::email_log, 'form_email_log', ['usergroup'=>$this->usergroup] );
				}
				$phones = json_decode($log['phone'], true);
				$ids = !empty($phones) ? array_map( function($a,$b){return $b;}, $phones,$phones ) : [];
				if(!empty($ids)){
					$this->db->where_in( 'id', $ids );
					$logs[$k]['sms_log'] = $this->db->get_rows( self::sms_log, 'form_sms_log', ['usergroup'=>$this->usergroup] );
				}
			}
			return $id? $logs[0] : $logs;
		}


		function update_shipping_method($args){
			if(!isset($args['order_id'])) return [ 'success'=>false, 'message'=>'Order ID is required' ];
			else $order_id = trim($args['order_id']);
			if(!isset($args['order_item_id'])) return [ 'success'=>false, 'message'=>'Order Line Item ID is required' ];
			else $order_item_id = trim($args['order_item_id']);
			if(!isset($args['shipping_method'])) return [ 'success'=>false, 'message'=>'Order shipping_method ID is required' ];
			else $status = trim($args['shipping_method']);

			//if($status==self::fulfilment_statuses['cancelled']) return [ 'success'=>false, 'message'=>'You cannot manually set status to cancelled. Please "Edit Product" and set quantity to 0.' ];

			$e = $this->db->update( 'sis_product_order_items', [ self::product_order_items['shipping_method']=>$status ], ['rel_id'=>$order_id,'usergroup'=>$this->usergroup, 'id'=>$order_item_id ] );

			$f = [ 'shipping_method'=>self::product_order_items['shipping_method'], 'shipping_method_name'=>self::shipping_methods['shipping_method_name'] ];
			$this->db->join( 'sis_product_shipping_methods fs', 'fs.id = '.self::product_order_items['shipping_method'], 'left' );
			$d = $this->db->get_row( $f, 'sis_product_order_items poi', [ 'poi.id'=>$order_item_id, 'poi.usergroup'=>$this->usergroup, 'poi.rel_id'=>$order_id ] );
			if($d){
				$d['success'] = true;
				$d['message'] = 'Product Delivery Method  updated successfully';
				return $d;
			}
			return [ 'success'=>false, 'message'=>'Unable to update product Delivery Method .' ];
		}

		function update_fulfilment_status($args){
			if(!isset($args['order_id'])) return [ 'success'=>false, 'message'=>'Order ID is required' ];
			else $order_id = trim($args['order_id']);
			if(!isset($args['order_item_id'])) return [ 'success'=>false, 'message'=>'Order Line Item ID is required' ];
			else $order_item_id = trim($args['order_item_id']);
			if(!isset($args['status'])) return [ 'success'=>false, 'message'=>'Order Status ID is required' ];
			else $status = trim($args['status']);

			if($status==self::fulfilment_statuses['cancelled']) return [ 'success'=>false, 'message'=>'You cannot manually set status to cancelled. Please "Edit Product" and set quantity to 0.' ];

			$e = $this->db->update( 'sis_product_order_items', [ self::product_order_items['status']=>$status ], ['rel_id'=>$order_id,'usergroup'=>$this->usergroup, 'id'=>$order_item_id ] );

			$f = [ 'status'=>self::product_order_items['status'], 'fulfilment_status_name'=>self::fulfilment_status['fulfilment_status_name'] ];
			$this->db->join( 'sis_product_order_fulfilment_status fs', 'fs.id = '.self::product_order_items['status'], 'left' );
			$d = $this->db->get_row( $f, 'sis_product_order_items poi', [ 'poi.id'=>$order_item_id, 'poi.usergroup'=>$this->usergroup, 'poi.rel_id'=>$order_id ] );
			if($d){
				$d['success'] = true;
				$d['message'] = 'Product fulfilment status updated successfully';
				return $d;
			}
			return [ 'success'=>false, 'message'=>'Unable to update product fulfilment status.' ];
		}

		function bulk_update_fulfilment_status($args){
			if(!isset($args['order_id'])) return [ 'success'=>false, 'message'=>'Order ID is required' ];
			else $order_id = trim($args['order_id']);
			if(!isset($args['order_item_ids'])) return [ 'success'=>false, 'message'=>'Order Line Item ID is required' ];
			else $order_item_ids = trim($args['order_item_ids']);
			if(!isset($args['status'])) return [ 'success'=>false, 'message'=>'Order Status ID is required' ];
			else $status = trim($args['status']);

			if($status==self::fulfilment_statuses['cancelled']) return [ 'success'=>false, 'message'=>'You cannot manually set status to cancelled. Please "Edit Products" and set quantity to 0.' ];

			$ids = json_decode($order_item_ids);

			$this->db->where_in( 'id', $ids );
			$e = $this->db->update( 'sis_product_order_items', [ self::product_order_items['status']=>$status ], ['rel_id'=>$order_id,'usergroup'=>$this->usergroup ] );

			$items = $this->get_order_items($order_id);
			$baskets = $this->get_order_baskets_items($order_id);
			$order=$this->orders(['id'=>$order_id]);
			if($items){
				return [ 'success'=>true,'order'=>$order, 'items'=>$items,'baskets'=>$baskets, 'message'=>'Product fulfilment status updated successfully' ];
			}
			return [ 'success'=>false, 'message'=>'Unable to update product fulfilment status.' ];
		}

		function bulk_fulfil($args){
			try {
				if(!isset($args['order_id'])) throw new Exception('Order ID is required');
				else $order_id = trim($args['order_id']);
				if(!isset($args['items'])) throw new Exception('At least one order line item is required');
				else $order_items = trim($args['items']);
				if(!isset($args['status'])) throw new Exception('Order Status ID is required');
				else $status = trim($args['status']);
				if($status == self::fulfilment_statuses['cancelled']) throw new Exception('You cannot manually set status to cancelled. Please "Edit Products" and set quantity to 0.');

				$items = json_decode($order_items,true);
				if(!is_array($items)) throw new Exception('Unable to update order fulfilment status. Please check your data and try again');
				$date = empty($args['date']) ? date("Y-m-d H:i:s") : date("Y-m-d H:i:s", strtotime($args['date']));

				$this->db->trans_start();
				$f = self::fulfilment_history;
				foreach($items as $k=>$item){
					$data = [
						$f['order_item_id']=>$item['id'],
						$f['dispatch_or_return']=>$args['dispatch_or_return'],
						$f['date_actioned']=>$date,
						$f['method']=>$args['method'],
						$f['quantity']=>$item['quantity'],
						$f['reference']=>empty($item['reference']) ? $args['reference'] : $item['reference'],
						$f['notes']=>empty($item['notes']) ? $args['notes'] : $item['notes'],
						'rel_id'=>$order_id
					];
					$inserted = $this->db->insert( 'sis_product_order_fulfilment_history', $data );
					if(!$inserted) throw new Exception("Unable to insert fulfilment tracking info. Please check your data and try again");
					$info=[self::product_order_items['status'] => $status];
					if (isset($args['method'])&&""!=$args['method']) {
						$info[self::product_order_items['shipping_method']]=$args['method'];
					}
					$this->db->where( 'id', $item['id'] )->where( 'rel_id', $order_id );
					$this->db->where( 'usergroup' , $this->usergroup );
					$this->db->update('sis_product_order_items',$info);
				}
				$this->db->trans_commit();

				$items = $this->get_order_items($order_id);
				$history = $this->get_fulfilment_history($order_id);
				$order=$this->orders(['id'=>$order_id]);
				if($items) {
					return ['success' => true,'order'=>$order,'items' => $items, 'history'=>$history, 'message' => 'Product fulfilment status updated successfully'];
				}
			}
			catch(Exception $e) {
				if($this->db->in_transaction()) $this->db->trans_rollback();
				return ['success' => false, 'message' => $e->getMessage()];
			}
		}

		function order_contacts($invoice_id){
			if($invoice_id) {
				$f = $this->db->prepend_fields(self::invoice_account, 'lead_invoice_account');
				$u = $this->db->prepend_fields(self::form_users, 'form_users');
				$fields = array_merge($f, $u);
				$this->db->join('form_users', 'form_users.id = lead_invoice_account.rec_id', 'inner');
				$this->db->where('lead_invoice_account.rel_id', $invoice_id);
				$this->db->where('lead_invoice_account.usergroup', $this->usergroup)->where('form_users.usergroup', $this->usergroup);
				$data = $this->db->get_row($fields, 'lead_invoice_account');
				if($data) {
					$del = $bil = false;
					$emails = $phone = [];
					if($data['email']) $emails[$data['email']] = ['email'=>$data['email'], 'first_name'=>$data['first_name'], 'surname'=>$data['surname'], 'full_name'=>$data['first_name'].' '.$data['surname']];
					if($data['delivery_contact_email']){
						$del = [ 'email'=>$data['delivery_contact_email'], 'full_name'=>$data['delivery_contact_name'] ];
						$names = explode(' ', $data['delivery_contact_name']);
						if($names && count($names)>1){
							$del['surname'] = array_pop($names);
							$del['first_name'] = implode( ' ', $names );
							$del['full_name'] = $del['first_name'].' '.$del['surname'];
						}
						else{
							$del['surname'] = '';
							$del['first_name'] = $del['full_name'] = $data['delivery_contact_name'];
						}
						$emails[$data['delivery_contact_email']] = $del;
					}
					if($data['billing_contact_email']){
						$bil = [ 'email'=>$data['delivery_contact_email'], 'full_name'=>$data['delivery_contact_name'] ];
						$names = explode(' ', $data['billing_contact_name']);
						if($names && count($names)>1){
							$bil['surname'] = array_pop($names);
							$bil['first_name'] = implode( ' ', $names );
							$bil['full_name'] = $bil['first_name'].' '.$bil['surname'];
						}
						else{
							$bil['surname'] = '';
							$bil['first_name'] = $bil['full_name'] = $data['billing_contact_name'];
						}
						$emails[$data['billing_contact_email']] = $bil;
					}
					if($data['delivery_contact_number']){
						$phone[$data['delivery_contact_number']] = ['phone'=>$data['delivery_contact_number'], 'first_name'=>$del?$del['first_name']:'',
							'surname'=>$del?$del['surname']:'', 'full_name'=>$del?$del['full_name']:'' ];
					}
					if($data['billing_contact_number']){
						$phone[$data['billing_contact_number']] = [ 'phone'=>$data['billing_contact_number'], 'first_name'=>$bil?$bil['first_name']:'',
							'surname'=>$bil?$bil['surname']:'', 'full_name'=>$bil?$bil['full_name']:'' ];
					}
					//$emails = array_unique($emails);
					//$phone = array_unique($phone);
					return ['email' => $emails, 'phone' => $phone];
				}
			}
			return ['email'=>[], 'phone'=>[]];
		}

		function get_email_templates($args=[]){
			$f1 = $this->db->prepend_fields( self::coms_template, 'coms_template' );
			$fields = array_merge( $f1, self::coms_email_tags );
			$this->db->join( 'coms_email_tags', 'coms_email_tags.id = '.self::coms_template['group'], 'left' );
			$this->db->where( 'coms_template.usergroup', $this->usergroup );
			$this->db->where( 'coms_template.rec_archive IS NULL' );
			return $this->db->get_rows( $fields, 'coms_template' );
		}

		function send_message($args, $write_log=true){
			try{
				$html_options = ['ignore_errors' => true ];
				$errors = $email_logs = $sms_logs = [];
				if( empty($args['email']) && empty($args['phone']) ) throw new Exception('Recipient email address or phone number is required');
				$n = $m = false;
				if( !empty($args['email']) ) {
					$n = 0;
					$emails = is_array($args['email']) ? $args['email'] : json_decode($args['email'], true);
					if(empty($args['previews'])) throw new Exception('Message to send is required');
					$previews = is_array($args['previews']) ? $args['previews'] : json_decode($args['previews'], true);
					if(count($emails) !== count($previews)) throw new Exception("Number of submitted messages does not match number of email address");
					$mailer = new Emails;
					foreach($emails as $k => $email) {
						$html = $previews[$k];
						// $plain = strip_tags($html);
						$plain = \Soundasleep\Html2Text::convert($html, $html_options);
						$email_args = [
							'to' => $email,
							'subject' => !empty($args['subject'])?$args['subject']:"New message on Order {$args['invoice_username']}",
							'text' => $plain,
							'html' => $html,
							'category' => "Product Only Order Updates",
							'recipient_id' => $args['user_id'],
						];
						$email_args["queue"] = true;
						$email_logs[$email] = $mailer->send($email_args);
						if ($email_logs[$email]) {
							$email_logs[$email] = $mailer->getEmailId();
						}
						$n++;
					}
					if($n === 0) $errors[] = [ 'success'=>false, 'message'=>"No emails were sent, please check your data and try again"];
					else {
						$n1 = $n==1 ? "{$n} recipient" : "{$n} recipients";
						$errors[] = [ 'success'=>true, 'message'=>"Email message successfully sent to {$n1}."];
					}
				}

				if(!empty($args['phone'])){
					$phones = is_array($args['phone']) ? $args['phone'] : json_decode($args['phone'], true);
					if(empty($args['sms'])) throw new Exception('Message to send is required');
					$previews = is_array($args['sms']) ? $args['sms'] : json_decode($args['sms'], true);
					if(count($phones) !== count($previews)) throw new Exception("Number of submitted messages does not match number of phone numbers");
					$m = 0;
					if(count($phones)>0) {
						load_helper('sms');
						$mailer = new Sms_helper();
						foreach($phones as $k => $to) {
							$message = $previews[$k];
							$sent = $mailer->send($to, $message, true);
							if($sent['success']) $m++;
							else $errors[] = [ 'success'=>false, 'message'=>$sent['message']];
							$sms_logs[$to] = $sent['log_id'];
						}
						if($m === 0) $errors[] = [ 'success'=>false, 'message'=>"No SMS messages were sent, please check your data and try again"];
						else {
							$m1 = $m == 1 ? "{$m} recipient" : "{$m} recipients";
							$errors[] = [ 'success'=>true, 'message'=>"SMS message successfully sent to {$m1}."];
						}
					}
				}

				$return = [ 'success'=>($n||$m), 'messages'=> $errors, 'email_logs'=>$email_logs, 'sms_logs'=>$sms_logs ];
			}
			catch(Exception $e){
				$errors[] = $e->getMessage();
				$return = [ 'success'=>false, 'messages'=>$errors, 'email_logs'=>$email_logs, 'sms_logs'=>$sms_logs ];
			}
			finally {
				if($write_log){
					$action = self::log_actions['message'];
					$log_id = $this->log_order_history($action, $args, ['email_logs'=>$email_logs, 'sms_logs'=>$sms_logs]);
					$return['log'] = $this->get_order_history( $args['order_id'], $log_id );
				}
				return $return;
			}
		}

		function get_fulfilment_history($order_id){
			$f1 = $this->db->prepend_fields( self::fulfilment_history, 'fh' );
			$f2 = $this->db->prepend_fields( self::form_users, 'form_users' );
			$f3 = $this->db->prepend_fields( self::shipping_methods, 'sis_product_shipping_methods' );
			$f = array_merge($f1,$f2,$f3);
			$this->db->select( self::product_order_items['product_name'], 'product_name' );
			$this->db->select( self::product_order_items['quantity'], 'total_items' );
			$this->db->join( 'sis_product_order_items', 'sis_product_order_items.id = fh.'.self::fulfilment_history['order_item_id'], 'inner' );
			$this->db->join( 'sis_product_shipping_methods', 'sis_product_shipping_methods.id = fh.'.self::fulfilment_history['method'], 'inner' );
			$this->db->join( 'form_users', 'form_users.id = fh.rec_id', 'left' );
			$this->db->where( 'fh.rel_id', $order_id )->where( 'fh.usergroup', $this->usergroup );
			$this->db->order_by( 'fh.id desc' );
			$data = $this->db->get_rows( $f, 'sis_product_order_fulfilment_history fh' );
			return $data?$data:[];
		}

		function get_email_letterhead($file='order_message', $order=false){

			$order = is_numeric($order) ? $this->orders(['id'=>$order]) : $order;

			if(!$order) return '';

			$data = [ 'order'=>$order, 'school_name'=>$this->settings['school_name'], 'logo'=>$this->settings['logo'], 'school_email'=>$this->settings['email'],
				'site_url'=>$this->base_url('products'), 'order_url'=>$this->base_url("products/orders/{$order['id']}"), 'ignore_status'=>true
			];

			$html = $this->load_template( $file, $data );
			return $html;
		}

		function send_order_confirmation( $order_id ){

			$order = $this->orders(['id'=>$order_id]);
			if(!$order) return;

			$data = [ 'order'=>$order, 'school_name'=>$this->settings['school_name'], 'logo'=>$this->settings['logo'], 'school_email'=>$this->settings['email'],
				'site_url'=>$this->base_url('products'), 'order_url'=>$this->base_url("products/orders/{$order['id']}"), 'ignore_status'=>false
			];

			// fetch email template
			$message = $this->load_template('order_confirmation', $data);
			if(!$message){
				$message = 'Dear '.$order['first_name'].'<br>
					You have successfully placed an order on '.$this->settings['school_name'].'.<br><br>
					You can view your order on <a href="'.$this->base_url("products/orders/{$order['id']}").'" target="_blank">this link</a><br><br>
					Thanks,<br>'.$this->settings['school_name'];
			}

			$order_url = $this->admin_url("productorders/details/{$order_id}?vw={$order['invoice_username']}&ref={$order_id}");
			$data = [ 'order'=>$order, 'school_name'=>$this->settings['school_name'], 'logo'=>$this->settings['logo'], 'school_email'=>$this->settings['email'],
				'site_url'=>$this->admin_url('productorders'), 'order_url'=>$order_url, 'ignore_status'=>false
			];

			// fetch admin email template
			$admin_message = $this->load_template( 'admin_order_confirmation', $data );
			if(!$admin_message){
				$order_url = $this->admin_url("productorders/details/{$order_id}?vw={$order['invoice_username']}&ref={$order_id}");
				$admin_message = 'Dear Admin<br>'.
					$order['first_name'].' '.$order['surname']. ' has successfully placed an order on '.$this->settings['school_name'].'.<br><br>
					You can view the order on <a href="'.$order_url.'" target="_blank">this link</a><br><br>
					Thanks,<br>'.$this->settings['school_name'];
			}

			// send email
			$args = [ 'email'=>[$order['email'], $this->settings['email'] ], 'previews'=>[$message, $admin_message] ];
			$args['invoice_username'] = $this->get_invoice_number($order_id);
			$messages = $this->send_message($args, false);

			// log order history
			$h = [ 'order_id'=>$order['id'], 'status'=>$order['order_status'], 'message'=>$message ];
			$this->log_order_history( self::log_actions['message'], $h, $messages );
		}

		function send_order_updated_email($order,$changes, $admin,$send_to=false){

			$order_url = $admin ? $this->admin_url("productorders/details/{$order['id']}?vw={$order['invoice_username']}&ref={$order['id']}")
				: $this->base_url("products/orders/{$order['id']}");
			$site_url = $admin ? $this->admin_url('productorders') : $this->base_url('products');

			$data = [ 'order'=>$order, 'school_name'=>$this->settings['school_name'], 'logo'=>$this->settings['logo'], 'school_email'=>$this->settings['email'],
				'site_url'=>$site_url, 'order_url'=>$order_url, 'admin'=>$admin, 'changes'=>$changes, 'ignore_status'=>false
			];

			// fetch email template
			$message = $this->load_template( 'order_updated', $data );
			if(!$message){
				$message = $admin ? 'Dear Admin<br>'.
					$order['first_name'].' '.$order['surname']. ' made changes to their order.<br>The following changes were made:<br><br>'.$changes.'<br><br>
					You can view the order on <a href="'.$order_url.'" target="_blank">this link</a><br><br>
					Thanks,<br>'.$this->settings['school_name']
					: 'Dear '.$order['first_name'].'<br>'
					.$this->settings['school_name'].' admin has made the following changes to your order:<br><br>'.$changes.'<br><br>
					You can view your order on <a href="'.$this->base_url("products/orders/{$order['id']}").'" target="_blank">this link</a><br><br>
					Thanks,<br>'.$this->settings['school_name'];
			}
			$email = $send_to && !empty($send_to['email']) ? $send_to['email'] : ( $admin ? [$this->settings['email']] : [$order['email']] );
			$previews = array_fill( 0, count($email), $message );
			$phone = $send_to && !empty($send_to['phone']) ? $send_to['phone'] : [];
			$sms = $phone ? array_fill( 0, count($phone), $changes ): [];
			// send email
			$subject = "Order {$order['invoice_username']} has been updated";
			$args = [ 'email'=>$email, 'previews'=>$previews, 'subject'=>$subject, 'phone'=>$phone, 'sms'=>$sms ];
			$args['invoice_username'] = $this->get_invoice_number($order['id']);
			return $this->send_message($args, false);
		}

		private function load_template($file, $data){
			$template = '';
			// fetch email template
			$folder = $_SESSION['subdomain'];
			$dir = realpath(__DIR__."/../../../static/{$folder}/resources/templates/");

			$has_ext = preg_match( '/\.\w{2,4}$/', $file );
			$ext = $has_ext ? '' : '.php';
			$path = $dir.'/'.$file.$ext;
			if(file_exists($path)){
				$loader = new FilesystemLoader($dir);
				$twig = new Environment($loader);
				$twig->addExtension(new IntlExtension());
				$html = $twig->render($file.$ext, $data);
				$template = CssInliner::fromHtml($html)->inlineCss()->render();
				$template = str_replace( '%7B%7B', '{{', $template );
				$template = str_replace( '%7D%7D', '}}', $template );
			}
			return $template;
		}

		function prepare_delivery_list ($ids){
			if(is_array($ids)) $this->db->where_in('sis_product_orders.id', $ids);
			elseif(is_numeric($ids)) $this->db->where('sis_product_orders.id', $ids);
			$orders = $this->orders();

			$date_actioned = function( $acc, $f ){
				$x = $f['dispatch_or_return']=='Dispatch'?'':'-';
				$x = $f['quantity']==1?"({$x}1 item)":"({$x}{$f['quantity']} items)";
				return $acc ? $acc . "\n".$f['date_actioned']." {$x}" : $acc . $f['date_actioned']." {$x}";
			};
			$reference = function( $acc, $f ){
				return $acc ? $acc . "\n".$f['reference'] : $acc . $f['reference'];
			};
			$shipped = function( $acc, $f ){
				return $acc + ( $f['dispatch_or_return']=='Dispatch' ? ($f['quantity']*1) : ($f['quantity']*(-1)) );
			};

			$data = [];
			foreach($orders as $order) {
				$items = $this->get_order_items($order['id']);
				$d = $this->get_invoice_details($order['invoice_id']);
				$fulfilment_history = $this->get_fulfilment_history($order['id']);
				//echo "<pre>".print_r($d,1)."</pre>";exit();

				$this->db->where( 'sis_student_fees.usergroup', $this->usergroup );
				$this->db->where( 'lead_invoice_settings.id', $order['invoice_id'] );
				$this->db->join( 'lead_invoice_settings', "lead_invoice_settings.username_id = ".self::student_fees['payment_for'], 'inner' );
				$this->db->join( 'lead_invoice_account', 'lead_invoice_account.rel_id = lead_invoice_settings.id', 'left' );
				$infp=self::student_fees;
				unset($infp['id']);unset($infp['rel_id']);unset($infp['rec_id']);
				$infp['date']='date(sis_student_fees.date)';
				$infp['ext_ref_nubmer']='db107045';
				$payment_details= $this->db->get_rows($infp, 'sis_student_fees' );
				$dates = array_column($payment_details, 'date');
                //echo "<pre>".print_r($dates,1)."</pre>";
				 //echo $this->db->get_last_query(); exit()

				//echo "<pre>".print_r($payment_details,1)."</pre>";exit();
				if($items) {
					$address = [$d['organisation'], $d['delivery_contact_name'], $d['delivery_address'], $d['delivery_city'] . ', ' . $d['delivery_county'], $d['delivery_country_name'].', '.$d['delivery_postcode']];
					$i=0;
					foreach($items as $item){
						if (""!=$item['basket_id']) {
								$f = $this->get_basket_data(['basket_id'=>$item['basket_id']]);
								//echo "<pre>".print_r($f,1)."</pre>";
							    if (isset($f['basket'])) {
							    	$b=$f['basket'];
							    	$basket_address = [$b['contact_name'], $b['organization_name'], $b['house_number'], $b['city'] . ', ' . $b['region'], $b['country_name'].', '.$b['postcode']
							    	//,'Contact-'.$b['contact_number']
							        ];
							        $item['contact_number']=$b['contact_number'];
							    	if (''!=$d['purchase_order_number']) {
							    		$basket_address[]="\n\n\n\nPO Number:".$d['purchase_order_number'];
							    	}else{
							    		$basket_address[]="\n\n\n\nPO Number:  N/A";
							    	}
							    }else{
							    	$basket_address=[];
							    }
							}else{
								$basket_address=[];
							}

					   //echo "<pre>".print_r($basket_address,1)."</pre>";exit();
					   $item['basket_address'] = implode( "\n", $basket_address );

						if($i === 0) {
							
							$item['order_id'] = $order['id'];
							$item['invoice_number'] = $order['invoice_username'];
							$item['customer'] = $order['first_name'] . ' ' . $order['surname'];
							$item['cart_address'] = implode( "\n", $address );
						} else {
							$item['order_id'] = '';
							$item['invoice_number'] = '';
							$item['customer'] = '';
							$item['cart_address'] = '';
							//$item['basket_address'] = '';
						}

						$fh = array_filter( $fulfilment_history, function($f) use ($item){
							return $f['order_item_id'] == $item['id'];
						});

                        if($fh) {
	                        $item['dispatch_date'] = array_reduce( $fh, $date_actioned, '' );
	                        $item['dispatch_ref'] = array_reduce( $fh, $reference, '' );
	                        $item['shipped'] = array_reduce($fh,$shipped);
	                        $item['to_ship'] = $item['quantity'] - $item['shipped'];
                        } else {
	                        $item['dispatch_date'] = '';
	                        $item['dispatch_ref'] = '';
	                        $item['shipped'] = 0;
	                        $item['to_ship'] = $item['quantity'];
                        }
                        $this->db->where( 'sis_products.id', $item['product_id'] );
				        $this->db->join( 'sis_product_categories', self::product_categories['pc_id']." = ".self::sis_products['category'], 'inner' );
				        $categoryitem=$this->db->fetch_field( self::product_categories['pc_name'],'sis_products');
				       // echo $this->db->get_last_query();exit();

                        $item['jiffy']="";
                        $item['cnt_mixed']="";
                        $item['shipping_method']="";
                        $item['product_category']=$categoryitem!=false?$categoryitem:"";
                        $item['number_of_sessions']="";
                        $item['payment_date']=implode("\n\n", array_column($payment_details, 'date'));
                        $item['ext_ref_nubmer']=implode("\n\n", array_column($payment_details, 'ext_ref_nubmer'));
                        $item['payment_or_refund']=implode("\n\n", array_column($payment_details, 'payment_or_refund'));
                        $item['payment_method']=implode("\n\n", array_column($payment_details, 'payment_method'));
                        $item['payment_ref']=implode("\n\n", array_column($payment_details, 'token'));
                        $i++;
						$data[] = $item;
                    }
                }
			}
			return $data;
		}

		function prepare_finance_list ($ids){
			if(is_array($ids)) $this->db->where_in('sis_product_orders.id', $ids);
			elseif(is_numeric($ids)) $this->db->where('sis_product_orders.id', $ids);
			$orders = $this->orders();

			$date_actioned = function( $acc, $f ){
				$x = $f['dispatch_or_return']=='Dispatch'?'':'-';
				$x = $f['quantity']==1?"({$x}1 item)":"({$x}{$f['quantity']} items)";
				return $acc ? $acc . "\n".$f['date_actioned']." {$x}" : $acc . $f['date_actioned']." {$x}";
			};
			$reference = function( $acc, $f ){
				return $acc ? $acc . "\n".$f['reference'] : $acc . $f['reference'];
			};
			$shipped = function( $acc, $f ){
				return $acc + ( $f['dispatch_or_return']=='Dispatch' ? ($f['quantity']*1) : ($f['quantity']*(-1)) );
			};

			$data = [];
			foreach($orders as $order) {
				$items = $this->get_order_items($order['id']);
				$d = $this->get_invoice_details($order['invoice_id']);
				$fulfilment_history = $this->get_fulfilment_history($order['id']);
				//echo "<pre>".print_r($order,1)."</pre>";exit();

				$this->db->where( 'sis_student_fees.usergroup', $this->usergroup );
				$this->db->where( 'lead_invoice_settings.id', $order['invoice_id'] );
				$this->db->join( 'lead_invoice_settings', "lead_invoice_settings.username_id = ".self::student_fees['payment_for'], 'inner' );
				$this->db->join( 'lead_invoice_account', 'lead_invoice_account.rel_id = lead_invoice_settings.id', 'left' );
				$infp=self::student_fees;
				unset($infp['id']);unset($infp['rel_id']);unset($infp['rec_id']);
				$infp['date']='date(sis_student_fees.date)';
				$infp['ext_ref_nubmer']='db107045';
				$payment_details= $this->db->get_rows($infp, 'sis_student_fees' );
				$dates = array_column($payment_details, 'date');
                //echo "<pre>".print_r($dates,1)."</pre>";
				 //echo $this->db->get_last_query(); exit()

				//echo "<pre>".print_r($payment_details,1)."</pre>";exit();
				$bookingamount=$order['order_total'];
				if($items&&isset($payment_details[0])) {
					$address = [$d['organisation'], $d['delivery_contact_name'], $d['delivery_address'], $d['delivery_city'] . ', ' . $d['delivery_county'], $d['delivery_country_name'].', '.$d['delivery_postcode']];
					$i=0;
					foreach($items as $item){
						if (""!=$item['basket_id']) {
								$f = $this->get_basket_data(['basket_id'=>$item['basket_id']]);
								//echo "<pre>".print_r($f,1)."</pre>";
							    if (isset($f['basket'])) {
							    	$b=$f['basket'];
							    	$basket_address = [$b['contact_name'], $b['organization_name'], $b['house_number'], $b['city'] . ', ' . $b['region'], $b['country_name'].', '.$b['postcode']
							    	//,'Contact-'.$b['contact_number']
							        ];
							        $item['contact_number']=$b['contact_number'];
							    	if (''!=$d['purchase_order_number']) {
							    		$basket_address[]="\n\n\n\nPO Number:".$d['purchase_order_number'];
							    	}else{
							    		$basket_address[]="\n\n\n\nPO Number:  N/A";
		}
							    }else{
							    	$basket_address=[];
							    }
							}else{
								$basket_address=[];
							}

					   //echo "<pre>".print_r($basket_address,1)."</pre>";exit();
					   $item['basket_address'] = implode( "\n", $basket_address );

						if($i === 0) {
							
							$item['order_id'] = $order['id'];
							$item['invoice_number'] = $order['invoice_username'];
							$item['customer'] = $order['first_name'] . ' ' . $order['surname'];
							$item['cart_address'] = implode( "\n", $address );
						} else {
							$item['order_id'] = '';
							$item['invoice_number'] = '';
							$item['customer'] = '';
							$item['cart_address'] = '';
							//$item['basket_address'] = '';
						}

						$fh = array_filter( $fulfilment_history, function($f) use ($item){
							return $f['order_item_id'] == $item['id'];
						});

                        if($fh) {
	                        $item['dispatch_date'] = array_reduce( $fh, $date_actioned, '' );
	                        $item['dispatch_ref'] = array_reduce( $fh, $reference, '' );
	                        $item['shipped'] = array_reduce($fh,$shipped);
	                        $item['to_ship'] = $item['quantity'] - $item['shipped'];
                        } else {
	                        $item['dispatch_date'] = '';
	                        $item['dispatch_ref'] = '';
	                        $item['shipped'] = 0;
	                        $item['to_ship'] = $item['quantity'];
                        }
                        $this->db->where( 'sis_products.id', $item['product_id'] );
				        $this->db->join( 'sis_product_categories', self::product_categories['pc_id']." = ".self::sis_products['category'], 'inner' );
				        $categoryitem=$this->db->fetch_field( self::product_categories['pc_name'],'sis_products');
				       // echo $this->db->get_last_query();exit();

                        $item['jiffy']="";
                        $item['cnt_mixed']="";
                        $item['shipping_method']="";
                        $item['product_category']=$categoryitem!=false?$categoryitem:"";
                        $item['number_of_sessions']="";
                        $item['payment_date']=implode("\n\n", array_column($payment_details, 'date'));
                        $item['ext_ref_nubmer']=implode("\n\n", array_column($payment_details, 'ext_ref_nubmer'));
                        $item['payment_or_refund']=implode("\n\n", array_column($payment_details, 'payment_or_refund'));
                        $item['payment_method']=implode("\n\n", array_column($payment_details, 'payment_method'));
                        $item['payment_ref']=implode("\n\n", array_column($payment_details, 'token'));
                        $this->db->where( 'sis_products.id', $item['product_id'] );
                        $price=$this->db->fetch_field( self::sis_products['price'],'sis_products');
                        $item['price']= $price;
                        $item['total_price']= $item['price']*$item['quantity'];
                        $i++;
                        $bookingamount=$bookingamount-$item['total_price'];
						$data[] = $item;
                         // add row for bookings
						
                    }
                }

                if (""!=$order['booking_id']) {
							$where = "INNER JOIN sis_course_schedule ON sis_course_schedule.id = {$invoice_settings['short_course']} ";
							$where .= "INNER JOIN core_courses ON core_courses.id = {$course['course_id']} ";
							$where .= "LEFT JOIN system_currency ON system_currency.id = {$invoice_settings['currency']} ";
							//$where .= "LEFT JOIN sis_student_fees ON {$sfees['payment_for']} = lead_invoice_settings.username_id ";
							$where .= "WHERE lead_invoice_settings.rec_id = {$user_id} ";

							$this->db->where( 'sis_student_fees.usergroup', $this->usergroup );
							$this->db->where( 'sis_scheduled_booking.id', $order['booking_id'] );
							$this->db->join( 'lead_invoice_settings', "lead_invoice_settings.username_id = ".self::student_fees['payment_for'], 'inner' );
							//$this->db->join( 'sis_course_schedule', "sis_course_schedule.id = {$invoice_settings['short_course']}", 'left' );
							$this->db->join( 'sis_scheduled_booking', "lead_invoice_settings.id = db15438", 'inner' );
							$infp=self::student_fees;
							//unset($infp['id']);unset($infp['rel_id']);unset($infp['rec_id']);
							// $infp['date']='date(sis_student_fees.date)';
							// $infp['ext_ref_nubmer']='db107045';
							$payment_details= $this->db->get_rows('db1492 AS `payment_method`,db1493 AS `comment`,db1494 AS `payment_for`,db1495 AS `amount`,sis_scheduled_booking.db14982 AS `short_course`,db30479 AS `currency`,db34450 AS `payment_or_refund`,db37345 AS `token`, `lead_invoice_settings`.username_id AS  invoice_username', 'sis_student_fees' );

							  $this->db->where( 'db15052', $order['booking_id'] );
							  $attendees=$this->db->fetch_field( "GROUP_CONCAT(concat(db15054,' ',db15055))",'sis_sched_booking_detail');

							//echo $this->db->get_last_query();exit();
							if ($payment_details) {
								foreach($payment_details as $payment){
									$item['product_name']=$payment['short_course']."\n Attendees:".$attendees;
                                    $item['order_id'] = $order['id'];
									$item['invoice_number'] = $payment['invoice_username'];
									$item['customer'] = $order['first_name'] . ' ' . $order['surname'];
									$item['cart_address'] = implode( "\n", $address );
									$item['dispatch_date'] = '';
			                        $item['dispatch_ref'] = '';
			                        $item['shipped'] = 0;
			                        $item['to_ship'] = "";
			                        $item['jiffy']="";
			                        $item['cnt_mixed']="";
			                        $item['shipping_method']="";
			                        $item['product_category']="Short course booking";
			                        $item['number_of_sessions']="";
			                        $item['payment_date']="";
			                        $item['ext_ref_nubmer']="";
			                        $item['payment_or_refund']=$payment['payment_or_refund'];
			                        $item['payment_method']=$payment['payment_method'];
			                        $item['payment_ref']=$payment['token'];
			                        $item['price']= $bookingamount;
                                    $item['total_price']= $bookingamount;
                                    $data[] = $item;
								}
                    }
                }
			}
			return $data;
		}

		function get_delivery_export_fields(){
			return self::delivery_export_fields;
		}

		function get_finance_export_fields(){
			return self::finance_export_fields;
		}


		public function add_empty_primary_cart(){
			$uid=$this->user_id;
			$check = $this->get_default_basket();
			//echo "<pre>".print_r($check,1),"</pre>";exit();
			if (empty($check)){
			try {
					$info = [
					//'rec_id'=> $uid,
						'usergroup' => $this->usergroup,
					'db86309' => 'My Basket'
				   ];
					$data = $this->db->prepare_data(self::cart_basket, $info);
					$id = $this->db->insert( 'sis_cart_basket', $data );
					if(!$id) {
						throw new Exception('Unable to add cart basket. Please check your data and try again');
					}
					
				}
				catch(Exception $e){
					return [ 'success'=>false, 'message'=>$e->getMessage(), 'Q'=>$this->db->get_last_query() ];
				}

			}
		}


		
		public function check_cart_basket()
		: array {
			$uid=$this->user_id;
			$check = $this->get_default_basket();
			//echo "<pre>".print_r($check,1),"</pre>";exit();
			if (empty($check)){
				// create a new basket with name My Basket
				try {
					$info = [
					//'rec_id'=> $uid,
						'usergroup' => $this->usergroup,
					'db86309' => 'My Basket'
				   ];
					$data = $this->db->prepare_data(self::cart_basket, $info);
					$id = $this->db->insert( 'sis_cart_basket', $data );
					if(!$id) {
						throw new Exception('Unable to add cart basket. Please check your data and try again');
					}
					//$def_cart=$this->db->where('rec_id',$uid)->where('db86315 is null')->update('sis_products_cart',['db86315'=>$id]);
					$def_cart=$this->db->update('sis_products_cart',['db86315'=>$id],' where db86315 is null and rec_id='.$uid);
					//echo "here1";exit();
					if ($def_cart) {
					return [ 'success'=>true, 'message'=>"successfully created a primary cart basket" ];

					}else{
						return [ 'success'=>true, 'message'=>"successfully created a primary cart basket but prosucts adding failed", 'Q'=>$this->db->get_last_query() ];

					}
				}
				catch(Exception $e){
					return [ 'success'=>false, 'message'=>$e->getMessage(), 'Q'=>$this->db->get_last_query() ];
				}
			}else{

			//$def_cart=$this->db->where('rec_id',$uid)->where('db86315 is null')->update('sis_products_cart',['db86315'=>$check]);
			$def_cart=$this->db->update('sis_products_cart',['db86315'=>$check],' where db86315 is null and rec_id='.$uid);
			if ($def_cart) {
				return [ 'success'=>true, 'message'=>"successfully created a primary cart basket" ];

			}else{
				return [ 'success'=>true, 'message'=>"successfully created a primary cart basket but prosucts adding failed", 'Q'=>$this->db->get_last_query() ];

			}
			//echo "here2";exit();
			//return [ 'success'=>true, 'message'=>"successfully retrieved primary cart basket"];
		   }
			
		}
		
		public function get_baskets()
		: array {
			$uid = $this->user_id;
			$this->db->where('sis_cart_basket.usergroup', $this->usergroup)->where("sis_cart_basket.rec_archive IS NULL")->where('sis_cart_basket.rec_id', $uid)->where('(db87437 is NULL or  db87437="")')->order_by("case when db86309='My Basket' then -1 else id end");
			$baskets =  $this->db->get_rows(self::cart_basket, 'sis_cart_basket');
			$baskets_query = $this->db->get_last_query();
			dev_debug($baskets_query);
			if(empty($baskets)) {
				$baskets = [];
			}
			return $baskets;
		}
		
		public function get_default_basket(){
			$uid = $this->user_id;
			$this->db->where( 'rec_id', $uid )->where( 'usergroup', $this->usergroup )->where('(db87437 is null or  db87437="")');
			return $this->db->fetch_field( self::cart_basket['cart_basket_id'], 'sis_cart_basket' );
		}

		public function duplicate_baskets($order_id){
			$uid = $this->user_id;
			try{
              $this->db->where( 'rec_id', $uid )->where( 'usergroup', $this->usergroup )->where('db87437',$order_id);
		        $baskets =  $this->db->get_rows(self::cart_basket, 'sis_cart_basket');
		        if (isset($baskets[0])) {
		        	foreach ($baskets as $baskey => $baskvalue) {
		        		if($baskvalue['basket_ad_id']!=''){
                         $address=$this->db->where( 'rec_id', $uid )->where( 'usergroup', $this->usergroup )->where('id',$baskvalue['basket_ad_id'])->get_rows(self::sis_address,'sis_address');
                            if (isset($address[0])) {
                         	$dats = $this->db->prepare_data(self::sis_address, $address[0]);
							 //echo "<pre>".print_r($data,1)."</pre>";exit();
							 unset($dats['sis_address.id']);
							 $addre_id = $this->db->insert( 'sis_address', $dats );
	                         }else{
	                         	$addre_id = '';
	                         }
                         
		        		}else{
		        		  $addre_id = '';
		        		}
		        		//echo $this->db->get_last_query();exit();
                        //$data = $this->db->prepare_data(self::cart_basket, $info);
                        $info = ['rec_id'=> $uid,
						'usergroup' => $this->usergroup,
					    'db86309' => $baskvalue['basket_name'],'db87257'=> $baskvalue['contact_name'],'db87260'=>$baskvalue['contact_number'],'db86312'=>$addre_id,'db105653'=>$baskvalue['contact_email'],'db106115'=>$baskvalue['organization_name']];
		        		$this->db->insert( 'sis_cart_basket', $info );
		        	}
		        }
		        return [ 'success'=>false, 'message'=>'Basket details were  duplicated' ];

			}catch(Exception $e){
                 return [ 'success'=>false, 'message'=>'Basket details were not duplicated', 'Q'=>$this->db->get_last_query() ];
			}
		}


		public function make_order_address_primary($args){

			$uid = $this->user_id;
			$f = array_merge( self::cart_basket, self::sis_address );
			$this->db->join('sis_address',self::cart_basket['basket_ad_id'].'=sis_address.id','left' )->where( 'sis_cart_basket.rec_id', $uid )->where( 'sis_cart_basket.usergroup', $this->usergroup )->where("(db87437 is null or  db87437='')");
		     $primary_basket=$this->db->get_rows( self::cart_basket, 'sis_cart_basket' );



		     if (isset($primary_basket[0])&&''==$primary_basket[0]['house_number']&&''==$primary_basket[0]['city']&&''==$primary_basket[0]['region']&&''==$primary_basket[0]['country']&&''==$primary_basket[0]['postcode']) {
		     	$postdata['cart_basket_id']=$primary_basket[0]['cart_basket_id'];
		     	$postdata['basket_name']=$primary_basket[0]['basket_name'];
		     	$postdata['house_number']=$args['delivery_address'];
		     	$postdata['city']=$args['delivery_city'];
		     	$postdata['region']=$args['delivery_county'];
		     	$postdata['country']=$args['delivery_country'];
		     	$postdata['postcode']=$args['delivery_postcode'];
		     	$postdata['contact_name']=$args['delivery_contact_name'];
		     	$postdata['contact_number']=$args['delivery_contact_number'];
		     	$postdata['contact_email']=$args['delivery_contact_email'];
		     	if (isset($args['organisation'])) {
		     		$postdata['organization_name']=$args['organisation'];
		     	}
		     	if (isset($args['organization_name'])) {
		     		$postdata['organization_name']=$args['organization_name'];
		     	}
		     	
		     	//$postdata['cart_basket_id']=$args['delivery_address'];
		     	//$postdata['cart_basket_id']=$args['delivery_address'];
		        //echo "<pre>".print_r($postdata,1)."</pre>";exit();
		     	$this->add_basket($postdata);
		     }



		}
 
		public function add_basket($post=null):array{
		    // create a new basket with name My Basket
			$uid = $this->user_id;
			try {
			 $info=$post;
			 $data = $this->db->prepare_data(self::sis_address, $info);
			 
			 unset($data['sis_address.id']);
             if ($post['ad_id']!=""&&$post['ad_id']!="undefined") {
             	$this->db->where( 'usergroup', $this->usergroup )->where('id',$post['ad_id'])->update('sis_address',$data);
             	$addre_id=$post['ad_id'];
             }else{
                $addre_id = $this->db->insert( 'sis_address', $data );
             }
			$this->db->where( 'rec_id', $uid )->where( 'usergroup', $this->usergroup )->where('db86309',$post['basket_name']);
		    $baskets =  $this->db->get_rows(self::cart_basket, 'sis_cart_basket');
		    $baskets_query = $this->db->get_last_query();
			dev_debug($baskets_query);
			
				$info = ['rec_id'=> $uid,
					'usergroup' => $this->usergroup,
				'db86309' => $post['basket_name'],'db87257'=> $post['contact_name'],'db87260'=>$post['contact_number'],'db86312'=>$addre_id,'db105653'=>$post['contact_email'],'db106115'=>$post['organization_name']];
				if (isset($post['order_id'])) {
					$info['db87437']=$post['order_id'];
				}
				//$data = $this->db->prepare_data(self::cart_basket, $info);
				if ($post['cart_basket_id']=="") {
					$id = $this->db->insert( 'sis_cart_basket', $info );
					$text ='added';
				}else{
					$id=$this->db->where( 'usergroup', $this->usergroup )->where('id',$post['cart_basket_id'])->update('sis_cart_basket',$info);
					$text ='updated';
				}

				
				
				
				
				
				
				if(!$id) {
					throw new Exception('Basket details were not '.$text);
				}
				$return=[ 'success'=>true, 'message'=>$post['basket_name']."  details submited!" ];

				if (isset($post['order_id'])) {
 
                    $this->setUserId(false); 
                    $h = [ 'order_id'=>$post['order_id'], 'message'=>'Order basket '.$post['basket_name']." ".$text ];
				    $this->log_order_history( self::log_actions['order_baskets_update'], $h );
					$return['order']=$this->orders(['id'=>intval($post['order_id'])]);
					$return['order_history']=$this->get_order_history($post['order_id']);
					$return['order_baskets']=$this->get_order_baskets($post['order_id']);

				}
				return $return;
			
		
			}
			catch(Exception $e){
				return [ 'success'=>false, 'message'=>$e->getMessage(), 'Q'=>$this->db->get_last_query() ];
			}
				
		}

		public function get_baskets_details()
		: array {
			$uid = $this->user_id;
			$this->db->where('sis_cart_basket.usergroup', $this->usergroup)->where("sis_cart_basket.rec_archive IS NULL")->where('sis_cart_basket.rec_id', $uid)->where("(db87437 is NULL or db87437 = '')")->order_by("case when db86309='My Basket' then -1 else id end");
			$baskets =  $this->db->get_rows(self::cart_basket, 'sis_cart_basket');
			$baskets_query = $this->db->get_last_query();
			//echo($baskets_query);
			//echo "<pre>".print_r($baskets,1)."</pre>";exit();
			if(empty($baskets)) {
				$baskets = [];
			}else{
				
				foreach($baskets as $basketkey=> $basket){
					    $f = array_merge( self::sis_products, self::products_cart_get );
						$this->db->join( 'sis_products', "{$f['product_id']} = {$f['id']}", 'inner' );
						$this->db->where('sis_products_cart.usergroup', $this->usergroup )->where( 'sis_products.usergroup', $this->usergroup );
						$this->db->where('sis_products_cart.rec_id', $this->user_id);
						$this->db->where( 'sis_products_cart.rec_archive IS NULL' )->where('sis_products.rec_archive IS NULL');
						$this->db->where('db86315',$basket['cart_basket_id']);
						$this->db->group_by('db69062');
						$cart = $this->db->get_rows($f, 'sis_products_cart');

						//if(!$cart) return [];

						if (isset($cart[0])) {
						
							foreach($cart as $k => $v) {
								$cart[$k]['image'] = $v['image'] ? $this->file->get_image_link($v['image']) : $this->base_url('products/resources/img/noimage.svg');
							}
						}
						$baskets[$basketkey]['products']=$cart;
				}
			}

			//echo "<pre>".print_r($baskets,1)."</pre>";exit();
			return $baskets;
		}

		public function remove_basket_item() 
		:array {
			try {
				$deleted = $this->db->delete( 'sis_products_cart', ['db69062'=>$_POST['product_id'],'db86315'=>$_POST['basket_id']] );
				if(!$deleted) throw new Exception('Unable to update cart. Please check your data and try again');
				return [ 'success'=>true, 'message'=>"Cart updated successfully" ];
			}
			catch(Exception $e){
				return [ 'success'=>false, 'message'=>$e->getMessage(), 'Q'=>$this->db->get_last_query()];
			}
		}

        public function dec_basket_item()
        :array {
            $uid = $this->user_id;
        	try {
        		$this->db->where('sis_products_cart.usergroup', $this->usergroup)->where("sis_products_cart.rec_archive IS NULL")->where('sis_products_cart.rec_id', $uid)->where('db69062',$_POST['product_id'])->where('db86315',$_POST['basket_id']);
			    $products =  $this->db->get_rows(self::products_cart, 'sis_products_cart');
			    $product_details=$this->db->where('id',$_POST['product_id'])->get_rows(self::sis_products,'sis_products');
			    //echo "<pre>".print_r($product_details,1)."</pre>";exit();
			    $products_qty =  $this->db->where('sis_products_cart.usergroup', $this->usergroup)->where("sis_products_cart.rec_archive IS NULL")->where('sis_products_cart.rec_id', $uid)->where('db69062',$_POST['product_id'])->get_rows(['quantity'=>'sum(db69068)'], 'sis_products_cart');
			    if (isset($products[0])&&$products[0]['quantity']>0) {

				    if ($product_details[0]['min_order']>0) {
				    	if ($products_qty[0]['quantity']<=$product_details[0]['min_order']) {
				    		throw new Exception("You can't order less than".$product_details[0]['min_order'].' of this product');
				    	}
				    }

			    	if ($products[0]['quantity']==1) {
				    	$deleted = $this->db->delete( 'sis_products_cart', ['db69062'=>$_POST['product_id'],'db86315'=>$_POST['basket_id']] );
						if(!$deleted) throw new Exception('Unable to update cart. Please check your data and try again');
						return [ 'success'=>true, 'message'=>"Cart updated successfully" ];
				    }else{

				    	$q = [ 'quantity' => ($products[0]['quantity']-1) ];
						$data = $this->db->prepare_data(self::products_cart, $q);
						$this->db->where('sis_products_cart.usergroup', $this->usergroup)->where("sis_products_cart.rec_archive IS NULL")->where('sis_products_cart.rec_id', $uid)->where('db69062',$_POST['product_id'])->where('db86315',$_POST['basket_id']);
						$updated = $this->db->update( 'sis_products_cart', $data );
						if(!$updated) throw new Exception('Unable to update cart. Please check your data and try again');
						return [ 'success'=>true, 'message'=>"Cart updated successfully" ];

				    }
			    }else{
			    	throw new Exception('Unable to update cart. Please check your data and try again');
			    }
			    
				
			}
			catch(Exception $e){
				return [ 'success'=>false, 'message'=>$e->getMessage(), 'Q'=>$this->db->get_last_query()];
			}
        }

        public function set_basket_item_quantity()
        :array {
            $uid = $this->user_id;
        	try {
				$q = [ 'quantity' => $_POST['quantity'] ];
				$data = $this->db->prepare_data(self::products_cart, $q);
				$this->db->where('sis_products_cart.usergroup', $this->usergroup)->where("sis_products_cart.rec_archive IS NULL")->where('sis_products_cart.rec_id', $uid)->where('db69062',$_POST['product_id'])->where('db86315',$_POST['basket_id']);
				$updated = $this->db->update( 'sis_products_cart', $data );
				if(!$updated) throw new Exception('Unable to update cart. Please check your data and try again');
				return [ 'success'=>true, 'message'=>"Cart updated successfully" ,'cart'=>$this->get_cart(),'basket_details'=>$this->get_baskets_details()];
			}
			catch(Exception $e){
				return [ 'success'=>false, 'message'=>$e->getMessage(), 'Q'=>$this->db->get_last_query()];
			}
        }

        public function inc_basket_item()
        :array {
        	$uid = $this->user_id;
        	try {

        		$this->db->where('sis_products_cart.usergroup', $this->usergroup)->where("sis_products_cart.rec_archive IS NULL")->where('sis_products_cart.rec_id', $uid)->where('db69062',$_POST['product_id'])->where('db86315',$_POST['basket_id']);
			    $products =  $this->db->get_rows(self::products_cart, 'sis_products_cart');
			    if (isset($products[0])&&$products[0]['quantity']>0) {
				    	$q = [ 'quantity' => ($products[0]['quantity']+1) ];
						$data = $this->db->prepare_data(self::products_cart, $q);
						$this->db->where('sis_products_cart.usergroup', $this->usergroup)->where("sis_products_cart.rec_archive IS NULL")->where('sis_products_cart.rec_id', $uid)->where('db69062',$_POST['product_id'])->where('db86315',$_POST['basket_id']);
						$updated = $this->db->update( 'sis_products_cart', $data );
						if(!$updated) throw new Exception('Unable to update cart. Please check your data and try again');
						return [ 'success'=>true, 'message'=>"Cart updated successfully" ];				    
			    }else{
			    	throw new Exception('Unable to update cart. Please check your data and try again');
			    }
			    
				
			}
			catch(Exception $e){
				return [ 'success'=>false, 'message'=>$e->getMessage(), 'Q'=>$this->db->get_last_query()];
			}
        }

        public function get_basket_data($post)
        : array {
           $uid = $this->user_id;
        	try {
				$this->db->where('sis_cart_basket.usergroup', $this->usergroup)->where("sis_cart_basket.rec_archive IS NULL");
				if ($post['basket_id']==0) {
					// code...
					$this->db->where('rec_id',$uid);
					$this->db->where("(db87437 is NULL or db87437 ='')");
				}else{
                   $this->db->where('id',$post['basket_id']);

				}
				$baskets =  $this->db->order_by("case when db86309='My Basket' then -1 else id end")->get_rows(self::cart_basket, 'sis_cart_basket');
				$baskets_query = $this->db->get_last_query();
				//dev_debug($baskets_query);

				//echo $baskets_query ."<br>";
				$data=[];
				$data['success']=true;
				if (isset($baskets[0])) {
					$data['basket']=$baskets[0];
					if ($baskets[0]['basket_ad_id']!="") {
						// code...
						$selects=self::sis_address;
						//echo "<pre>".print_r($selects,1)."</pre>";exit();
						$selects['country_name']='dc.country_name';
						//$this->db->select( 'dc.country_name', 'country_name' );
						$this->db->where('sis_address.usergroup', $this->usergroup)->where('sis_address.id',$baskets[0]['basket_ad_id']);
		             	//$this->db->select( 'bc.country_name', 'billing_country_name' );
			            $this->db->join( 'system_countrylist dc', 'dc.id = '.self::sis_address['country'], 'left' );
						$address =  $this->db->get_rows($selects, 'sis_address');
						$baskets_query = $this->db->get_last_query();
						//echo ($baskets_query);exit();
						if (isset($address[0])) {
							//$data['basket']['address']=$address[0];
							foreach ($address[0] as $key => $value) {
								$data['basket'][$key]=$value;
							}
						}else{
							$blank=new stdClass();
							foreach ($selects as $key => $value) {
								// code...
								//$blank->$key="";
								$data['basket'][$key]="";
							}
							//$data['basket']['address']=$blank;
						}
					}
				}
                 return $data;	
            }
			catch(Exception $e){
				return [ 'success'=>false, 'message'=>$e->getMessage(), 'Q'=>$this->db->get_last_query()];
			}
        }


        public function delete_basket():array
        {
        	if (!empty($_POST['order_id'])) {
        		$this->db->where('sis_cart_basket.usergroup', $this->usergroup)->where("sis_cart_basket.rec_archive IS NULL")->where('sis_cart_basket.db87437', $_POST['order_id']);
			     $baskets =  $this->db->get_rows(self::cart_basket, 'sis_cart_basket');
        	}else{
        	    $uid = $this->user_id;
        	    $baskets=$this->db->where('rec_id',$this->user_id)->where('(db87437 is NULL or db87437="")')->get_rows(self::cart_basket, 'sis_cart_basket');
        	}
        	if (count($baskets)==1) {
        		return [ 'success'=>true, 'message'=>"Cannot delete the only basket in this cart . Rename it or create another one and delete this one!" ];	
        	}else{
        	try {
			    $products =  $this->db->delete('sis_products_cart', ['db86315'=>$_POST['basket_id']]);
			    $baskets = $this->db->delete('sis_cart_basket', ['id'=>$_POST['basket_id']]);
			    if ($baskets) {
						return [ 'success'=>true, 'message'=>"Basket deleted Successifully!" ];				    
			    }else{
			    	throw new Exception('Unable to update cart. Please check your data and try again');
			    }
			    
				
			}
			catch(Exception $e){
				return [ 'success'=>false, 'message'=>$e->getMessage(), 'Q'=>$this->db->get_last_query()];
			}
			}

        }

        function get_shipping_methods(){
            $this->db->where('sis_product_shipping_methods.usergroup', $this->usergroup)->where("sis_product_shipping_methods.rec_archive IS NULL")->where('db87278', 'active');
			$records =  $this->db->get_rows(self::shipping_methods, 'sis_product_shipping_methods');
			//return array_column($records, 'name');
			return $records;
        }


        function bulk_update_payment_status($post){
        	
        	$this->db->where_in('id',$post['order_ids']);
        	if (14==$post['new_status']) {
              $this->db->where(self::product_orders['payment_method'],'invoice');
        	}
            $this->db->update('sis_product_orders',['db69989'=>$post['new_status']]);

            //echo $this->db->get_last_query();exit();

            if (14==$post['new_status']) {
        		foreach($post['order_ids'] as $order_id){

        			///$args=$this->db->get_row( self::product_orders, 'sis_product_orders', ['id'=>$order_id] );
        			$args=$this->orders(['id'=>$order_id]);
        			//echo "<pre>".print_r($args,1)."</pre>";exit();
        			if ($args['payment_method']=='invoice') {
        				$out=$this->process_payment($args, $args['invoice_id']);
        				if ($out['success']) {
        					//$this->db->where_in('id',$order_id);
                            $this->db->update('sis_product_orders',[self::product_orders['amount_paid']=>$args['order_total']-$args['amount_paid']],['id'=>$order_id]);
        				}
        			}
        			
        		}
        	}
        }



		function get_cart_prerequisites($all_reqs=false){

			$this->merge_carts();
			$f = array_merge( self::sis_products, self::products_cart );
			$this->db->join( 'sis_products', "{$f['product_id']} = {$f['id']}", 'inner' );
			$this->db->join( 'sis_products_prereq_pivot', "{$f['product_id']} = db105656", 'inner' );
			$this->db->join( 'core_courses', "db105659 = core_courses.id", 'inner' );
			//$this->db->join( 'sis_order_item_prerequisites', "sis_products_cart.id = db90581 and db105662=db105659", 'left' );
			$this->db->where('sis_products_cart.usergroup', $this->usergroup )->where( 'sis_products.usergroup', $this->usergroup );
			$this->db->where('sis_products_cart.rec_id', $this->user_id);
			$this->db->where( 'sis_products_cart.rec_archive IS NULL' )
			->where('sis_products.rec_archive IS NULL')
			->where("{$f['prerequisite_id']} IS NOT NULL")
			->where("{$f['has_prerequisites']}",'yes');
			if (!$all_reqs) {
				//$this->db->where('db90581 is null');
			}

			$this->db->group_by("db105656,db105659,sis_products_cart.id"); 
			$cart = $this->db->get_rows(["product_ids"=>"group_concat(DISTINCT(sis_products.id))","products"=> "group_concat(DISTINCT(db34755))","prereq_id"=>'db105659',"prereq_title"=>"group_concat(DISTINCT(db232))",'current_prereq_title'=>'db232',"all_prereq_id"=>"group_concat(DISTINCT(db105659))","preq_count"=>"(sum(DISTINCT(db69068)) - (select count(id) from sis_order_item_prerequisites where db90581= sis_products_cart.id and  db105662=db105659))"], 'sis_products_cart');
            //echo $this->db->get_last_query();exit();

			$this->db->join( 'sis_products', "{$f['product_id']} = {$f['id']}", 'inner' );
			$this->db->join( 'sis_products_prereq_pivot', "{$f['product_id']} = db105656", 'inner' );
			$this->db->join( 'core_courses', "db105659 = core_courses.id", 'inner' );
			//$this->db->join( 'sis_order_item_prerequisites', "sis_products_cart.id = db90581 and db105662=db105659", 'left' );
			$this->db->where('sis_products_cart.usergroup', $this->usergroup )->where( 'sis_products.usergroup', $this->usergroup );
			$this->db->where('sis_products_cart.rec_id', $this->user_id);
			$this->db->where( 'sis_products_cart.rec_archive IS NULL' )->where('sis_products.rec_archive IS NULL')->where("{$f['prerequisite_id']} IS NOT NULL")->where("{$f['has_prerequisites']}",'yes');
			if (!$all_reqs) {
				//$this->db->where('db90581 is null');
			}

			$this->db->group_by("sis_products.id"); 
			$cart2 = $this->db->get_rows(["products"=> "group_concat(DISTINCT(db34755))","prereq_id"=>'db105659',"prereq_title"=>"IF(db106028!='',db106028,group_concat(DISTINCT(db232)))","preq_count"=>"(((db69068)) - (select count(id) from sis_order_item_prerequisites where db90581= sis_products_cart.id  ))"], 'sis_products_cart');
            //echo $this->db->get_last_query();exit();

			//if(!$cart) return [];
			$count_string='';
			$count_preq=0;
			foreach ($cart as $key => $value) {
				$cart[$key]['prod_ids']=explode(',',$value['product_ids']);
				if (""==$count_string) {
					$count_string=$value['all_prereq_id'];
				}else{
					$count_string.=",".$value['all_prereq_id'];
				}
				//$cart[$key]['preq_count']=count(explode(',', $value['all_prereq_id']));
				$count_preq=$count_preq+$value['preq_count'];
			}

			$unique_prereqs=array_unique(explode(',', $count_string));


			$this->db->join( 'sis_products', "{$f['product_id']} = {$f['id']}", 'inner' );
			$this->db->join( 'sis_products_prereq_pivot', "{$f['product_id']} = db105656", 'inner' );
			$this->db->join( 'core_courses', "db105659 = core_courses.id", 'inner' );
			$this->db->join( 'sis_order_item_prerequisites', "sis_products_cart.id = db90581 and db105662=db105659", 'left' );
			$this->db->where('sis_products_cart.usergroup', $this->usergroup )->where( 'sis_products.usergroup', $this->usergroup );
			$this->db->where('sis_products_cart.rec_id', $this->user_id);
			$this->db->where( 'sis_products_cart.rec_archive IS NULL' )->where('sis_products.rec_archive IS NULL')->where("{$f['prerequisite_id']} IS NOT NULL")->where("{$f['has_prerequisites']}",'yes');
			if (!$all_reqs) {
				$this->db->where('db90581 is not null');
			}
			$this->db->where('sis_order_item_prerequisites.id is not null');
			$inf=self::order_item_prerequisites;
			$inf['course_name']='db232';
			$inf['product_title']='db34755';
			$pre_req= $this->db->get_rows($inf,'sis_products_cart');
			

			if (isset($pre_req[0])) {
				foreach ($pre_req as $key => $value) {
					$files=$this->db->where_in('id',explode(',',$value['short_course_certificate']))->get_rows(self::files,'form_file');
					foreach ($files as $fkey => $fvalue) {
						// code...
						
						$files[$fkey]['mimetype']=mime_content_type(env('FILES_STORAGE_PATH').'/media/'.$fvalue['file_path']);
						$files[$fkey]['filesize']=(filesize(env('FILES_STORAGE_PATH').'/media/'.$fvalue['file_path']));
						//$files[$fkey]['storage_path']=env('FILES_STORAGE_PATH').'/media/'.$fvalue['file_path'];
						$files[$fkey]['uri']=engine_url("/media/dl.php?fl=".encode($fvalue['file_path']));
						$files[$fkey]['dropzone_link']=(isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]".("/admin/sis_products/dropzone_links/".($fvalue['file_path']));
					}
					$pre_req[$key]['attachments']=$files;
				}
			}

			//return $;

			return [
				//'last_query'=>$this->db->get_last_query(),
				'prereqs_count'=>($count_preq),
				'unique_prereqs'=>$unique_prereqs,
				'cart'=>$cart,
				'prereq_summary'=>$cart2,
				'uploaded_prereq'=>$pre_req

			];
		}
 
		




		public function submit_prerequisite($post) 
        {
        	$uid = $this->user_id;
        	try {
        		$post['status']="";
        		if(isset($post['pre_req_id'])&&""!=$post['pre_req_id']){
        			if (isset($post['order_id'])) {
                       $up=$this->db->update('sis_product_orders',[self::product_orders['order_status']=>self::order_statuses['awaiting_prereq_aproval']],['id' => $post['order_id'], 'usergroup' => $this->usergroup]);
		              	if ($up) {
		              		$order = $this->orders(['id'=>$post['order_id']]);
		              		//$status=self::order_statuses['awaiting_prereq_aproval'];
		              		$comment = "<em>Order status</em> updated to <strong> Awaiting approval </strong><br<br><em>Comment:</em> <br>Customer updated prerequsites";

		              		$logs =  $this->send_order_updated_email( $order, $comment, true, [] ) ;

		              		//echo "<pre>".print_r($logs,1)."</pre>";exit();

                          }
        			}
        			$id=$post['pre_req_id'];
        			unset($post['pre_req_id']);

        			$data = $this->db->prepare_data(self::order_item_prerequisites, $post);
        			//echo "<pre>".print_r($data,1)."</pre>";exit();
        			$this->db->update('sis_order_item_prerequisites',$data,['id'=>$id]);
        			if (isset($post['order_id'])) {
        				$h = [ 'order_id'=>$post['order_id'], 'status'=>$post['order_status'], 'message'=>'Prerequisite '.$id.' updated successfully!!' ];
					    $this->log_order_history( self::log_actions['pre_req_update'], $h );
        			}
        			
        			return [ 'success'=>true, 'message'=>"Prerequisite updated successfully!" ];	

        		}elseif(isset($post['order_item_id'])&&""!=$post['order_item_id']){ 
        		   $post['course_id']=$post['prereq_id'];
        		   //$post['course_id']=$post['prereq_id'];
                   $data = $this->db->prepare_data(self::order_item_prerequisites, $post);
			    		//echo "<pre>".print_r($data,1)."</pre>";exit();
                   $data['rel_id']=$post['order_item_id'];
                  // echo "<pre>".print_r($data,1)."</pre>";exit();
				   $id = $this->db->insert( 'sis_order_item_prerequisites', $data );


			    	//}

			    	if ($id) {
			    		// log order history
			    		if (isset($post['order_id'])) {
        				    $h = [ 'order_id'=>$post['order_id'], 'status'=>$post['order_status'], 'message'=>'Prerequisite added successfully!' ];
					        $this->log_order_history( self::log_actions['pre_req_update'], $h );
        			    }
					
				    	return [ 'success'=>true, 'message'=>"Prerequisite added successfully!" ];	
				    }else{
				    	return [ 'error'=>true, 'message'=>"Prerequisite failed to add" , 'Q'=>$this->db->get_last_query() ];	
				    }
        		}else{
        		$this->db->join( 'sis_products_prereq_pivot', "db69062 = db105656", 'inner' )->where('sis_products_cart.rec_id', $this->user_id)->where('db105659', $post['prereq_id']);
        		$cart = $this->db->get_rows('sis_products_cart.id,db69062 as product_id,db105659 as course_id','sis_products_cart');
			    // echo "<pre>".print_r($cart,1)."</pre>";
			    //echo "<pre>".print_r($post,1)."</pre>";
			    if (isset($cart[0])) {
			    	$id=false;
			    	//foreach ($cart as $key => $item) {
			    		$post['cart_id']=$cart[0]['id'];
			    		$post['course_id']=$cart[0]['course_id'];
			    		$data = $this->db->prepare_data(self::order_item_prerequisites, $post);
			    		//echo "<pre>".print_r($data,1)."</pre>";exit();
					    $id = $this->db->insert( 'sis_order_item_prerequisites', $data );


			    	//}

			    	if ($id) {
			    		if (isset($post['order_id'])) {
	        				$h = [ 'order_id'=>$post['order_id'], 'status'=>$post['order_status'], 'message'=>'Prerequisite added successfully!' ];
						    $this->log_order_history( self::log_actions['pre_req_update'], $h );
        			    }
			    		
				    	return [ 'success'=>true, 'message'=>"Prerequisite added successfully!" ];	
				    }else{
				    	return [ 'error'=>true, 'message'=>"Prerequisite failed to add" , 'Q'=>$this->db->get_last_query() ];	
				    }
				    }
			    }
			    return [ 'error'=>true, 'message'=>"Prerequisite failed to add", 'Q'=>$this->db->get_last_query() ];
				
			}
			catch(Exception $e){
				return [ 'success'=>false, 'message'=>$e->getMessage(), 'Q'=>$this->db->get_last_query()];
			}

        }

        function add_file( $data ){
			$insert = $this->db->prepare_data( self::files, $data );
			return $this->db->insert( 'form_file', $insert );
		}

		function update_prereq_status($post){
            $order = $this->orders(['id'=>$post['order_id']]);
            //echo "<pre>".print_r( $order,1)."<pre>";exit();
            $logs=[];
			try {
              $order_items=$this->get_order_items($post['order_id']);
              $update_data=['db90635'=>$post['status']];
              if ("Rejected"==$post['status']) {
              	//$update_data['to_add_1']=$post['rejectionreason'];
              	if (!empty($post['prereq_item_id'])) {
              	
              	$argdata=['prereq_item_id'=>$post['prereq_item_id'],'reason'=>$post['rejectionreason'],'rec_id'=>$this->user_id,'usergroup'=>$this->usergroup];
              	$insertdata = $this->db->prepare_data(self::prereqs_reject_reasons, $argdata);
				$id = $this->db->insert( 'sis_prereqs_reject_reasons', $insertdata );

              	}
              	$up=$this->db->update('sis_product_orders',[self::product_orders['order_status']=>self::order_statuses['need_more_information']],['id' => $post['order_id'], 'usergroup' => $this->usergroup]);
              	if ($up) {
              		$status=self::order_statuses['need_more_information'];
              		$comment = "<em>Order status</em> updated to <strong> Need more information </strong><br<br><em>Comment:</em> <br>{$post['rejectionreason']}";

              		$logs =  $this->send_order_updated_email( $order, $comment, false, ['email'=>[$order['email']]] ) ;

              		//echo "<pre>".print_r($logs,1)."</pre>";exit();

              	}
              }else{
              	//$update_data['to_add_1']="";
              }
              if (empty($post['order_item'])&&empty($post['order_items'])&&empty($post['prereq_item_id'])) {

              	if ($order_items) {
              		foreach ($order_items as $key => $order_item) {
              			$this->db->update('sis_order_item_prerequisites',$update_data,['rel_id'=>$order_item['id']]);
              		}
              	}
              }
              if(!empty($post['order_item'])){
              	$this->db->update('sis_order_item_prerequisites',$update_data,['rel_id'=>$post['order_item']]);
              }

              if(!empty($post['order_items'])){
              	$this->db->where_in('rel_id',explode(',',$post['order_items']));
              	$this->db->update('sis_order_item_prerequisites',$update_data);
              }

              if(!empty($post['prereq_item_id'])){
              	 $this->db->update('sis_order_item_prerequisites',$update_data,['id'=>$post['prereq_item_id']]);
              }

              //  handle stripe payment intent here 
              $order_item_ids=[];
              foreach ($order_items as $key => $order_item) {
                 $order_item_ids[]=$order_item['id'];
              }
              $outstanding_pre_req=$this->db->where_in('rel_id',$order_item_ids)->where("(db90635 is null or db90635='' or db90635='rejected')")->get_rows('sis_order_item_prerequisites');
              $order = $this->db->get_row(self::product_orders, 'sis_product_orders', ['id' => $post['order_id'], 'usergroup' => $this->usergroup]);
              $h = [ 'order_id'=>$order['id'], 'status'=>$order['order_status'], 'message'=>"Prerequisite ".$post['status'].' '.$post['rejectionreason']??''];
			  $this->log_order_history( self::log_actions['pre_req_update'], $h,$logs );
              if (count($outstanding_pre_req)>0) {
              	//echo "<pre>".print_r($outstanding_pre_req,1)."</pre>";exit();
              }else{ 
              	$capture=$this->capture_order_payment($post['order_id']);
              	if ($capture['success']) {
              	    return ['reload'=>'reloading', 'success'=>true,'order'=>$this->orders(['id'=>$post['order_id']]), 'message'=>"Prerequisite ".$post['status']." successfully!",'order_history'=> $this->get_order_history($post['order_id']) ];	

              	}
              }
              return [ 'success'=>true,'order'=>$this->orders(['id'=>$post['order_id']]), 'message'=>"Prerequisite ".$post['status']." successfully!" , 'orrder_prerequisites'=>$this->get_order_prequisites($post['order_id']),'order_history'=> $this->get_order_history($post['order_id'])];	

		    }
			catch(Exception $e){
				return [ 'success'=>false, 'message'=>$e->getMessage(), 'Q'=>$this->db->get_last_query()];
			}


		}


		function capture_order_payment($order_id){
			// capture order payment
			require 'app/libs/vendor/autoload.php';
          	$payment_intent = $this->db->fetch_field( 'db90641', 'sis_product_orders', ['id'=>$order_id, 'usergroup' => $this->usergroup] );
          	//get invoice id
			$invoice_id = $this->db->fetch_field( self::product_orders['invoice_id'], 'sis_product_orders', ['id' => $order_id, 'usergroup' => $this->usergroup]);

			$order_total = $this->db->fetch_field( self::product_orders['order_total'], 'sis_product_orders', ['id' => $order_id, 'usergroup' => $this->usergroup]);

			if (""==$payment_intent) {
				return [ 'success'=>false, 'message'=>"payment intent empty"];
			}

			
          	$sk = $this->get_stripe_keys('secret');
          	\Stripe\Stripe::setApiKey($sk);
			$intent = \Stripe\PaymentIntent::retrieve($payment_intent);
			$pintent=$intent->capture(['amount_to_capture' => $order_total*100]);

			//$args['payment_method']='card';

			//$args['payment_details']=json_encode($thnet);
            
           // echo "<pre>".print_r($pintent,1)."<pre>";exit();
            if($pintent['status']==='succeeded'){
				$this->process_stripe( $pintent, $invoice_id );
				$update['db69986'] = self::order_statuses['processing'];
			    $update['db69989'] = self::payment_statuses['paid'];
			    $this->db->update( 'sis_product_orders', $update, ['id' => $order_id, 'usergroup' => $this->usergroup] );

			    return [ 'reload'=>'reloading', 'success'=>true, 'message'=>"payment captured sucessifully"]; 
		    }else{
		    	return [ 'success'=>false, 'message'=>"payment capture failed"]; 
		    }


			

		    
			//if(!$payment['success']) throw new Exception($payment['message']);
        }
        

        public function get_order_baskets($order_id):
         array {
			$uid = $this->user_id;
			$this->db->where('sis_cart_basket.usergroup', $this->usergroup)->where("sis_cart_basket.rec_archive IS NULL")->where('sis_cart_basket.db87437', $order_id);
			$baskets =  $this->db->get_rows(self::cart_basket, 'sis_cart_basket');
			$baskets_query = $this->db->get_last_query();
			dev_debug($baskets_query);
			//echo "<pre>".print_r($baskets,1)."</pre>";exit();
			$new_basket=[];
			if(empty($baskets)) {
				$baskets = [];
			}else{
				
				foreach($baskets as $basketkey=> $basket){
					    $this->db->where('sis_address.usergroup', $this->usergroup)->where("(sis_address.rec_archive IS NULL OR sis_address.rec_archive='')")->where('id',$basket['basket_ad_id']);
						$address =  $this->db->get_rows(self::sis_address, 'sis_address');
						$baskets_query = $this->db->get_last_query();
						dev_debug($baskets_query);
						if (isset($address[0])) {
							//$data['basket']['address']=$address[0];
							foreach ($address[0] as $key => $value) {
								$baskets[$basketkey][$key]=$value;
							}
						}else{
							//$blank=new stdClass();
							foreach (self::sis_address as $key => $value) {
								// code...
								//$blank->$key="";
								$baskets[$basketkey][$key]="";
							}
							//$data['basket']['address']=$blank;
						}
						$new_basket[$basket['cart_basket_id']]=$baskets[$basketkey];
				}
			}

			//echo "<pre>".print_r($baskets,1)."</pre>";exit();
			return $new_basket;
		}

		function rejected_requisites_list($order_id){

		}


		function product_prereq_list($args){

		//;
			if (isset($args['product_ids'])) {
				//$this->db->where("id in (".$args['product_ids'].")");
			}

			$data=$this->db->where("id in (".$args['product_ids'].")")->get_rows(["courses"=>"group_concat(db232)"], "core_courses");

			if (isset($data[0])) {
				// code...
				return $data[0]['courses'];
			}else{
				return "";
			}

		}


		function delete_cart_prerequisite($args){

			try {
				$uid=$this->user_id;
				$deleted = $this->db->delete( 'sis_order_item_prerequisites', ['id'=>$args['pre_req_id']] );
				if(!$deleted) throw new Exception('Unable to update cart. Please check your data and try again');
				return [ 'success'=>true, 'message'=>"Pre-requisiste updated successfully" ];
			}
			catch(Exception $e){
				return [ 'success'=>false, 'message'=>$e->getMessage(), 'Q'=>$this->db->get_last_query()];
			}

		}


		function get_order_prereq_products($post){
           $f = array_merge( self::sis_products, self::product_order_items );
			$this->db->join( 'sis_products', "{$f['product_id']} = sis_products.{$f['id']}", 'inner' );
			$this->db->join( 'sis_products_prereq_pivot', "{$f['product_id']} = db105656", 'inner' );
			$this->db->join( 'core_courses', "db105659 = core_courses.id", 'inner' );
			//$this->db->join( 'sis_order_item_prerequisites', "sis_products_cart.id = db90581 and db105662=db105659", 'left' );
			// $this->db->where('sis_products_cart.usergroup', $this->usergroup )->where( 'sis_products.usergroup', $this->usergroup );
			// $this->db->where('sis_products_cart.rec_id', $this->user_id);
			$this->db->where( 'sis_product_order_items.rec_archive IS NULL' )
			->where('sis_products.rec_archive IS NULL')
			->where("{$f['prerequisite_id']} IS NOT NULL")
			->where("{$f['has_prerequisites']}",'yes')
			->where("sis_product_order_items.rel_id",$post['order_id']); 
			if (!$all_reqs) {
				//$this->db->where('db90581 is null');
			} 

			$this->db->group_by("sis_products.id"); 
			$cart = $this->db->get_rows(["product_ids"=>"group_concat(DISTINCT(sis_products.id))","products"=> "group_concat(DISTINCT(db34755))","prereq_id"=>'db105659',"prereq_title"=>"group_concat(DISTINCT(db232))",'current_prereq_title'=>'db232',"all_prereq_id"=>"group_concat(DISTINCT(db105659))","preq_count"=>"(sum(DISTINCT(db70013)))",'order_item_id'=>"sis_product_order_items.id"], 'sis_product_order_items');

			return ['cart'=>$cart,'Q'=>$this->db->get_last_query() ];
		}


		function create_order_admin($post){
           //echo "<pre>".print_r($post,1)."</pre>";
			//credate nu
			$user_data = $this->db->prepare_data( self::form_users, $post  );
            $this->db->where(self::form_users['email'],$post['email']);
            $existing=$this->db->get_rows('form_users');

            if (isset($existing[0])) {
            	$post['rec_id']=$existing[0]['id'];
            }else{
            	$user_data['db112']=4; //position student
            	$pass2=random();
				$pass2_encrypted = md5($pass2);// encrypt password
				$user_data['db222']=$pass2_encrypted; //password
				$user_data['db223']="no"; //can delete
				$user_data['db307']=1; //suspend acc 2 is suspended
				$user_data['db308']=$post['first_name'].$post['surname']; //username
				$user_data['db1034']=$_SESSION['usergroup']; //user group manager
				$randomvalue=random();
                $post['rec_id'] = $this->db->insert('form_users', $user_data);
                $post['new_user']=true;
                $user_data['id']=$post['rec_id'];

                //update record with uid
				$update = [ 'rec_id'=>$post['rec_id'], 'rel_id'=>$post['rec_id'] ];
				$this->db->update( 'form_users', $update, ['id'=>$user_id] );
            }

			//echo "<pre>".print_r($user_data,1)."</pre>"; exit();
			$data2 = $this->db->prepare_data( self::product_orders, $post  );
			//echo "<pre>".print_r($data2,1)."</pre>";exit();
			$post['order_id'] = $this->db->insert('sis_product_orders', $data2);
			//if($id) $data['id'] = $id;

			$post['invoice_id']=$this->create_invoice($post,$post['order_id']);

			//invoice account record

			$inv_account['rel_id']=$post['invoice_id'];
			$inv_account['rec_id']=$post['rec_id'];
            $post['invoice_account_id'] = $this->db->insert('lead_invoice_account', $inv_account);
            //echo $this->db->get_last_query();exit();
			$this->db->update('sis_product_orders',[self::product_orders['invoice_id']=>$post['invoice_id']],['id'=>$post['order_id']]);
            $this->db->where('id',$post['invoice_id']);
			$post['invoice_username_id'] = $this->db->fetch_field( self::invoice_settings['username_id'], 'lead_invoice_settings' );
			//echo "<pre>".print_r($post,1)."</pre>";


			// log order history
			$h = [ 'order_id'=>$post['order_id'], 'status'=>$post['order_status'], 'message'=>'Order created by '.$_SESSION['fullname'] ];
			$this->log_order_history( self::log_actions['status_update'], $h );

			if (isset($post['new_user'])) {
				$post['email_sent']=$this->mail_user($user_data);
				if ($post['email_sent']) {
					$h = [ 'order_id'=>$post['order_id'], 'status'=>$post['order_status'], 'message'=>'Invitation email sent by '.$_SESSION['fullname'] ];
			        $this->log_order_history( self::log_actions['status_update'], $h );
				}
			}

			return $post;
		}


		function mail_user($data){
            $school_name = $_SESSION['school_name']??'';
			$domain = $_SESSION['domain']??'';
			$email_add=$data['db119']??'';
			$fname=$data['db106']??'';
			$surname=$data['db111']??'';
			$user_id=$data['id']??'';
			 $url = $_SESSION['domain']??'';
			$reset_link=$this->create_reset_link($data);
		     $this->db->where('id',$user_id)->where("(db307 is NULL OR db307!= 'yes')")->where('usergroup',$_SESSION['usergroup']);
    	     $existing=$this->db->get_rows('form_users');
			if( $domain&&isset($existing[0])){
				

        	//echo "<pre>".print_r($existing,1)."</pre>";
        		$reset_link=$this->create_reset_link($existing[0]);
				$message_plain_txt = "A new account has been created for {$fname} {$surname} on {$school_name}. Use this link to set your password {$domain}/products/resetpassword?code=".$reset_link['db1134']."&usr=".$reset_link['username_id']."  
				You can use your email address {$email_add} to login on {$domain}/application/login";
				$message_html = nl2br($message_plain_txt);
			}
			else{
				$message_plain_txt = $message_html = "A new account has been created for {$fname} {$surname} with email address {$email_add}.";
			}

			$msg[] = "Your account has been created successfully";
			$subject = "New account created";
			$emailFrom = "<EMAIL>";

			if(class_exists( 'Emails' )){
				$emails = new Emails();
				$email_args=array(
					'to'=> $email_add,
					'subject'=>$subject,
					'text'=>$message_plain_txt,
					'html'=> $message_html,
					'recipient_id'=> $user_id,
					'queue'=>true
				);
				$emails->send($email_args);

			}
			elseif( function_exists('log_email') ) {
				log_email($email_add, $subject, $message_plain_txt, $message_html, $emailFrom, "parent_welcome");
			}

			return true;
		}


		function create_reset_link($userdata){

			$username_id = $userdata['username_id'];
			$user_id = $userdata['id'];
			$data['db1134'] = random();
			$data['db1135'] = $userdata['username_id'];///Requestee username_id
			$data['db1136'] = $_SERVER['REMOTE_ADDR'];///ip address
			$data['username_id']=random();
			$data['rec_id']=$userdata['rec_id'];
			$data['usergroup']=$userdata['usergroup'];
			$data['rel_id']=$userdata['rel_id']??$userdata['rec_id'];
			$data['db1135']=$userdata['db119'];

			if ($this->db->insert('form_password_reset',$data)) {
				return $data;
			}else{
				return [];
			}

		}

		function get_cust_refs(){
			return $this->db->where('usergroup',$this->usergroup)->where(self::customer_references['status'],'active')->get_rows(self::customer_references, self::customer_references_table);
		}


		function update_order($args){
            if( !isset($args['order_id']) ) return [ 'success'=>false, 'message'=>'Order ID is required' ];
            ///echo "<pre>".print_r($args,1)."</pre>";exit();

            if ('card'==$args['payment_method']) {
            	// process payments
            	$invoice_id=$this->db->where(self::product_orders['id'],$args['order_id'])->fetch_field(self::product_orders['invoice_id'],'sis_product_orders');
				$payment = $this->process_payment( $args, $invoice_id );
				if(!$payment['success']) throw new Exception($payment['message']);

				$args['invoice_username']=$this->db->where(self::invoice_settings['id'],$invoice_id)->fetch_field(self::invoice_settings['username_id'],'lead_invoice_settings');
				$args['payment_or_refund']='Payment';
				$pref = $this->preferences();
				if(!isset($pref['abv'])) throw new Error('System currency not defined');
				$args['currency']=$pref['abv'];

				$card = $args['payment_details'] ? $args['payment_details'] : [];
				if( is_string($card) ) $card = $this->json->decode($card, true);
				if( isset($card['payment_method']) && $card['payment_method']==='stripe' && isset($card['paymentIntent']) ){
					$intent = $card['paymentIntent'];
					if( isset($intent['id']) && isset($intent['amount']) ){
                       $args['amount']=$intent['amount']/100;
                       $args['comment']="Customer payment on stripe";
                       $args['token']=$intent['id'];
					}
				}
				$args['email']='{}';
				$args['phone']='{}';

				$args['notify_admins']=true;
                $this->update_order_payment_status($args);
				
            }
            return ['success'=>true,'order_id'=>$args['order_id']];
		}


		function update_order_payment_status($args){
			//echo "<pre>".print_r($args,1)."</pre>";exit();
		    $sf = self::student_fees;
			$where = [ $sf['payment_for']=>$args['invoice_username'], $sf['payment_or_refund']=>'Payment', 'usergroup'=>$this->usergroup ];
			$pays = $this->db->fetch_field( "SUM({$sf['amount']})", 'sis_student_fees', $where);
			$where = [ $sf['payment_for']=>$args['invoice_username'], $sf['payment_or_refund']=>'invoice_update',$sf['payment_method']=>'3rd_party_invoice', 'usergroup'=>$this->usergroup ];
			$proxy_payment = $this->db->fetch_field( "SUM({$sf['amount']})", 'sis_student_fees', $where);
			//echo $this->db->get_last_query()."</br>";
			$where[$sf['payment_or_refund']] = 'Refund';
			$refunds = $this->db->fetch_field( "SUM({$sf['amount']})", 'sis_student_fees', $where);
			//echo $this->db->get_last_query()."</br>";
			$paid = ($proxy_payment*1) + ($pays*1) - ($refunds*1);
			$where = [ 'id'=>$args['order_id'], 'usergroup'=>$this->usergroup ];
			$select = [ 'order_total'=>self::product_orders['order_total'], 'payment_method'=>self::product_orders['payment_method'],'old_status'=>self::product_orders['payment_status'] ,'order_status'=>self::product_orders['order_status']];
			$order_details = $this->db->get_row( $select, 'sis_product_orders', $where );
             //echo $this->db->get_last_query()."</br>";
			$status_updated = $new_status = false;
			if( isset($order_details['order_total']) && $paid >= $order_details['order_total']){  // update payment status
				$update = [ self::product_orders['payment_status']=>self::payment_statuses['paid'] ];
				$new_status = self::payment_statuses['paid'];
				if ('3rd_party_invoice'==$args['payment_method']&&'invoice_update'==$args['payment_or_refund']&&'invoice'==$order_details['payment_method']) {
					$update = [ self::product_orders['payment_status']=>self::payment_statuses['proxy_payment'] ];
					$new_status = self::payment_statuses['proxy_payment'];
				}

				if( $order_details['order_status'] < self::order_statuses['processing'] ){
					$update[self::product_orders['order_status']] = self::order_statuses['processing'];
				}
                //echo "1<pre>".print_r($update,1)."<pre>";
				$status_updated = $this->db->update( 'sis_product_orders', $update, $where );
			}
			elseif( isset($order_details['order_total']) && $paid < $order_details['order_total'] && $order_details['order_status'] < self::order_statuses['processing'] ){
				$update = [];
				$new_status = $update[self::product_orders['payment_status']] = $order_details['payment_method']==='invoice'?self::payment_statuses['agreed']:self::payment_statuses['pending'];
				//echo "2<pre>".print_r($update,1)."<pre>";
				$status_updated = $this->db->update( 'sis_product_orders', $update, $where );
			}
            //echo $this->db->get_last_query();exit();
			$p = strtolower($args['payment_or_refund']);
			$amt = number_format( $args['amount'], 2 );
			$currency = is_numeric($args['currency']) ? $this->get_currency_symbol($args['currency']) : $args['currency'];
			$comment =  "Added a {$p} of <strong>{$currency}{$amt}</strong> to your order.<br><br>";
			$order = $this->orders(['id'=>$args['order_id']]);

			$emails = json_decode($args['email'], true);
			$phones = json_decode($args['phone'], true);
			$send = ( count($emails)>0 || count($phones)>0 ) ? ['email'=>$emails,'phone'=>$phones]: false;
            
			// insert order history if payment status is changed.
			if( $status_updated && $new_status && $order_details['old_status'] != $new_status ){
				$status = $this->db->fetch_field( self::payment_status['payment_status_name'], 'sis_sched_booking_stages', ['id'=>$new_status] );
				$comment .= "<em>Payment status</em> updated to <strong>{$status}</strong><br><em>Payment Ref:</em> {$args['token']}<br><em>Comment:</em> <br>{$args['comment']}";
				if (isset($args['notify_admins'])) {
					$logs = $this->send_order_updated_email( $order, $comment, true, $send );
				}else{
				    $logs = $send ? $this->send_order_updated_email( $order, $comment, false, $send ) : [];
				}
				$args['subject'] = "Order {$args['invoice_username']} has been updated.";
				$log_id = $this->log_order_history( $args['payment_or_refund'], [ 'order_id'=>$args['order_id'], 'message'=>$comment ], $logs );
			}
			else{
				$comment .= "<em>Payment Ref:</em> {$args['token']}<br><em>Comment:</em> <br>{$args['comment']}";
				if (isset($args['notify_admins'])) {
				    $logs =  $this->send_order_updated_email( $order, $comment, true, $send );
				}else{
                    $logs = $send ? $this->send_order_updated_email( $order, $comment, false, $send ) : false;
				}
				$log_id = $this->log_order_history( $args['payment_or_refund'], [ 'order_id'=>$args['order_id'], 'message'=>$comment ], $logs );
			}

			return $log_id;
		}


		function get_booking_stages(){
			return getOptions("253", "db14979");
		}


		function update_booking_status($args){
           $data = [self::scheduled_booking['status'] => $args['booking_status']];
	       $e1 = $this->db->update( 'sis_scheduled_booking', $data, ['id'=>$args['booking_id']] );

	       $data2= [self::sched_booking_detail['booking_status']=>$args['booking_status']];
	       $e2 = $this->db->update( 'sis_sched_booking_detail', $data2, [self::sched_booking_detail['scheduled_booking_id']=>$args['booking_id']] );

	       if($e1&&$e2){
				$args['subject'] = "Order booking status for booking {$args['booking_id']} has been updated.";
				$d['success'] = true;
				$d['message'] = "Order booking status  for booking {$args['booking_id']}  updated successfully";
				//$d['messages'] = isset($m['messages'])?$m['messages']:false;
				$d['order_id']=$args['order_id'];
				$action = self::log_actions['order_booking_status_update'];
				$log_id = $this->log_order_history( $action, $d );
				$d['log'] = $this->get_order_history( $args['order_id'], $log_id );
				return $d;
			}
			return [ 'success'=>false, 'message'=>'Unable to update order status.' ];
		}




        function bulk_update_order_status($args){
        	$this->db->where_in('id',$args['order_ids']);
            $order_update=$this->db->update('sis_product_orders',['db69986'=>$args['new_status']]);

            if (''!=$args['new_booking_status']) {
            	// $data2= [
            	// 	self::sched_booking_detail['booking_status']=>$args['new_booking_status'],
            	// 	self::scheduled_booking['status'] => $args['new_booking_status']
                //  ];
            	// $this->db->where_in('sis_product_orders.id',$args['order_ids']);
            	// $this->db->join('sis_scheduled_booking', 'sis_scheduled_booking.id ='.self::product_orders['booking_id']);
            	// $this->db->join('sis_sched_booking_detail', 'sis_scheduled_booking.id = '.self::sched_booking_detail['scheduled_booking_id']);
            	$booking_update=$this->db->set_query( "UPDATE sis_product_orders  JOIN `sis_scheduled_booking` ON sis_scheduled_booking.id =db86273 JOIN `sis_sched_booking_detail` ON sis_scheduled_booking.id = db15052 SET `db59978` = '".$args['new_booking_status']."', `db14983` = '".$args['new_booking_status']."' WHERE sis_product_orders.id IN (".implode(',', $args['order_ids']).")");
            	//echo $this->db->get_last_query();exit();
            	if ($booking_update) {
            		foreach ($args['order_ids'] as $key => $order_id) {
            			$args['subject'] = "Order booking status for order $order_id has been bulk updated.";
						$d['success'] = true;
						$d['message'] = "Order booking status  for order $order_id bulk  updated successfully";
						//$d['messages'] = isset($m['messages'])?$m['messages']:false;
						$action = self::log_actions['order_booking_status_update'];
						$d['order_id']=$order_id;
						$log_id = $this->log_order_history( $action, $d );
            		}
            		
	            }
            }
            if ($order_update) {
            	foreach ($args['order_ids'] as $key => $order_id) {
            			$args['subject'] = "Order  status has been bulk updated.";
						$d['success'] = true;
						$d['message'] = "Order  status bulk  updated successfully";
						//$d['messages'] = isset($m['messages'])?$m['messages']:false;
						$action = self::log_actions['status_update'];
						$d['order_id']=$order_id;
						$log_id = $this->log_order_history( $action, $d );
            		}

            }
        }


        function booking_get_order_id($booking_id){
        	return $this ->db->fetch_field('id','sis_product_orders',[self::product_orders['booking_id']=>$booking_id]);
        }
		
	}
