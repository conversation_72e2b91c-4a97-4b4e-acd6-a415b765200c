var methods = {
    add_line: function(){
        let vm =this
        const l = { id: 0, product_id: 0, quantity: 0, price: 0, product_name: '', fulfilment_status_name:'',shipped:0,to_ship:0,shipping_method_name:'',vat_amount:0 };
        //this.order.items.push(l);
        const keys = Object.keys(vm.order.baskets);
        keys.forEach((key, bindex) => {
            vm.order.baskets[key].items.push(l);
        })
    },
    inc_order: function (item){
        item.quantity++;
        if(item.quantity<0) item.quantity = 0;
    },
    dec_order: function (item,min=0){
        item.quantity--;
        if(item.quantity<min) item.quantity = min;
    },
    remove_item: function (index){
        this.order.items.splice( index, 1 );
    },
    check_qty: function(item,min=0){
        if(item.quantity < min){
            this.push_message( 'Quantity cannot be less than '+min, 'warning' );
            item.quantity = min;
        }
    },
    enable_edit_products: function (state){
        console.log(this.og_products)

        this.edit_products = state;
         console.log(state)
        if(state){
            this.og_products = JSON.parse(JSON.stringify(this.order));
            this.add_line();
        }


        return false;
    },
    cancel_products: function (event){

        if(this.og_products){
            const o = JSON.parse((JSON.stringify(this.og_products)));
            this.order = o;
        }
        this.enable_edit_products(false);
    },
    enable_edit_customer: function (state){
        this.edit_customer = state;
        if(state) this.og_products = JSON.parse(JSON.stringify(this.order));
        return false;
    },
    cancel_customer: function (){
        if(this.og_products){
            const o = JSON.parse((JSON.stringify(this.og_products)));
            this.order = o;
        }
        this.enable_edit_customer(false);
    },
    save_customer_changes: function (event){
        this.update_invoice_details(event);
        this.enable_edit_customer(false);
    },
    save_product_changes: function (event){
        this.order.items.pop();
        this.update_invoice_items(event);
    },
    update_invoice_details: function (event){
        let vm = this;
        vm.dbtn(event);
        let url = '/admin/productorders/update_invoice';
        const details = JSON.parse(JSON.stringify(vm.order.details));
        const params = new URLSearchParams();
        for( const item in details ){
            if( typeof details[item] === 'object' && details[item] !== null ){
                params.append( item, JSON.stringify(details[item]) );
            }
            else params.append( item, details[item] );
        }
        params.append( 'order_id', vm.order.id );
        params.append( 'invoice_id', vm.order.invoice_id );
        params.append( 'payment_method', vm.order.payment_method );

        axios.post(url,params)
            .then(function (r) {
                const flag = r.data.success ? 'success' : 'warning';
                if(r.data.details){
                    Vue.set( vm.order, 'details', r.data.details );
                    vm.og_products = JSON.parse(JSON.stringify(vm.order));
                }
                else{
                    vm.cancel_customer();
                }
                if( r.data.contacts ){
                    Vue.set(vm, 'contacts', r.data.contacts);
                }
                if(r.data.log ){
                    vm.order_history.unshift(r.data.log);
                }

                if(r.data.order){
                    Vue.set( vm, 'order', r.data.order );
                    vm.select_all = false;
                }
                if(r.data.message) vm.push_message(r.data.message, flag);
                else vm.push_message( 'An unknown error occurred.' )
                if(r.data.success) $('#edit_invoice_details').modal('hide');
            })
            .catch(function (error) {
                vm.push_message( error, 'error' );
                vm.cancel_customer();
            }).finally(()=>vm.dbtn(event,false));
    },
    update_invoice_items: function (event){
        let vm = this;
        let url = '/admin/productorders/update_order_items';
        const items = JSON.stringify(vm.order.items);
        const params = new URLSearchParams();
        params.append( 'order_id', vm.order.id );
        params.append( 'invoice_id', vm.order.invoice_id );
        //params.append( 'products', items );
        params.append('baskets',JSON.stringify(vm.order.baskets));
        this.dbtn(event);
        axios.post(url,params)
            .then(function (r) {
                const flag = r.data.success ? 'success' : 'warning';
                if(r.data.order){
                    Vue.set( vm, 'order', r.data.order );
                    vm.og_products = JSON.parse(JSON.stringify(vm.order));
                }
                else{
                    vm.cancel_products();
                }
                if(r.data.log ){
                    vm.order_history.unshift(r.data.log);
                }
                vm.push_message(r.data.message, flag);
            })
            .catch(function (error) {
                vm.push_message( error, 'error' );
                vm.cancel_products();
            })
            .finally(function (){
                vm.dbtn(event,false);
                vm.enable_edit_products(false);
            });
    },
    update_order_status: function (event){
        $('#change_order_status fieldset').prop( 'disabled', true );
        let vm = this;
        vm.dbtn(event,true);
        let url = '/admin/productorders/update_order_status';

        let emails = [];
        let sms = [];
        let status_name = false;
        if( vm.status_update.status && vm.order_status ){
            const status = vm.order_status.find((item)=>item.order_status_id==vm.status_update.status);
            if(status) status_name = status.order_status_name;
        }

        if( vm.status_update.message && vm.status_update.phone.length > 0 ) {
            sms = vm.replace_tags(vm.status_update.message, 'phone', vm.status_update, false, status_name);
        }
        let msg = vm.message_header ? vm.message_header : STATUS_MESSAGE_HEADER;
        msg = msg.replace( '{{headline}}', 'Order Update Alert' );
        msg = msg.replace( '{{subject}}', 'Your order has been updated.' );
        const body = vm.status_update.message ? vm.status_update.message : "<ol><li>Order status changed to <b>{{order_status}}</b></li></ol>"
        msg = msg.replace( '{{message_body}}', body );
        emails = vm.replace_tags( msg, 'email', vm.status_update, false, status_name );

        const params = new URLSearchParams();
        params.append( 'order_id', vm.order.id );
        params.append( 'invoice_id', vm.order.invoice_id );
        params.append( 'invoice_username', vm.order.invoice_username );
        params.append( 'status', vm.status_update.status );
        params.append( 'message', vm.status_update.message );
        params.append( 'email', JSON.stringify(vm.status_update.email) );
        params.append( 'phone', JSON.stringify(vm.status_update.phone) );
        params.append( 'previews', JSON.stringify(emails) );
        params.append( 'sms', JSON.stringify(sms) );
        axios.post(url,params)
            .then(function (r) {
                const flag = r.data.success ? 'success' : 'warning';
                if(r.data.order_status){
                    vm.order.order_status = r.data.order_status;
                    vm.order.order_status_name = r.data.order_status_name;
                }
                if(r.data.log ){
                    vm.order_history.unshift(r.data.log);
                }
                if(r.data.message) vm.push_message(r.data.message, flag);
                if(r.data.messages) vm.push_messages(r.data.messages);
                if(r.data.success){
                    $('#change_order_status').modal('hide');
                    if (vm.order.booking_id) {
                        $("#change_booking_status").modal('show')
                    }
                    console.log(typeof vm.order.booking_id)
                }
            })
            .catch(function (error) {
                vm.push_message( error, 'error' );
            })
            .finally(function (){
                $('#change_order_status fieldset').prop( 'disabled', false );
                vm.dbtn(event,false);
            });
    },
    update_booking_status: function(event){
         $('#change_booking_status fieldset').prop( 'disabled', true );
        let vm = this;
        vm.dbtn(event,true);
        let url = '/admin/productorders/update_booking_status';
        const params = new URLSearchParams();
        params.append( 'order_id', vm.order.id );
        params.append( 'booking_id', vm.order.booking_id);
        params.append( 'booking_status',  vm.status_update.booking_status  );

          axios.post(url,params)
            .then(function (r) {
                const flag = r.data.success ? 'success' : 'warning';
                if(r.data.log ){
                    vm.order_history.unshift(r.data.log);
                }
                if(r.data.message) vm.push_message(r.data.message, flag);
                if(r.data.messages) vm.push_messages(r.data.messages);
                if(r.data.success){
                    $('#change_booking_status').modal('hide');
                }
            })
            .catch(function (error) {
                vm.push_message( error, 'error' );
            })
            .finally(function (){
                $('#change_booking_status fieldset').prop( 'disabled', false );
                vm.dbtn(event,false);
            });
    },
    update_fulfilment_status: function (line, id){
        let vm = this;
        let url = '/admin/productorders/update_fulfilment_status';
        const params = new URLSearchParams();
        params.append( 'order_id', vm.order.id );
        params.append( 'invoice_id', vm.order.invoice_id );
        params.append( 'status', id );
        params.append( 'order_item_id', line.id );
        axios.post(url,params)
            .then(function (r) {
                const flag = r.data.success ? 'success' : 'warning';
                if(r.data.status){
                    line.status = r.data.status;
                    line.fulfilment_status_name = r.data.fulfilment_status_name;
                }
                vm.push_message(r.data.message, flag);
                vm.toggle_reminder(r.data.success);
            })
            .catch(function (error) {
                vm.push_message( error, 'error' );
            });
    },
    update_shipping_method:function(line,id){
         let vm = this;
        let url = '/admin/productorders/update_shipping_method';
        const params = new URLSearchParams();
        params.append( 'order_id', vm.order.id );
        params.append( 'invoice_id', vm.order.invoice_id );
        params.append( 'shipping_method', id );
        params.append( 'order_item_id', line.id );
        axios.post(url,params)
            .then(function (r) {
                const flag = r.data.success ? 'success' : 'warning';
                if(r.data.shipping_method){
                    line.shipping_method = r.data.shipping_method;
                    line.shipping_method_name = r.data.shipping_method_name;
                }
                vm.push_message(r.data.message, flag);
                //vm.toggle_reminder(r.data.success);
            })
            .catch(function (error) {
                vm.push_message( error, 'error' );
            });
    },
    bulk_update_fulfilment_status: function (id,basket=false){
        let vm = this;
        let url = '/admin/productorders/bulk_update_fulfilment_status';
        const params = new URLSearchParams();
        params.append( 'order_id', vm.order.id );
        params.append( 'invoice_id', vm.order.invoice_id );
        params.append( 'status', id );
        if (basket) {
            params.append( 'order_item_ids', JSON.stringify(vm.selected_basket) );
            console.log(JSON.stringify(vm.selected_basket));
            //return false;
        }else{
        params.append( 'order_item_ids', JSON.stringify(vm.selected) );
        }
        axios.post(url,params)
            .then(function (r) {
                const flag = r.data.success ? 'success' : 'warning';
                if(r.data.items){
                    Vue.set( vm.order, 'items', r.data.items );
                    vm.select_all = false;
                }
                if (r.data.baskets) {
                    Vue.set(vm.order,'baskets', r.data.baskets);
                }
                if(r.data.order){
                    Vue.set( vm, 'order', r.data.order );
                    vm.select_all = false;
                }

                vm.select_basket=[]
                vm.push_message(r.data.message, flag);
                vm.toggle_reminder(r.data.success);
            })
            .catch(function (error) {
                vm.push_message( error, 'error' );
            });
    },
    fulfilment_modal: function ( item, status,basket_mode=false ){
        let vm = this;
        // Vue.set(vm, 'fulfilment', JSON.parse(JSON.stringify(vm.fulfilment_copy)));
        vm.fulfilment.status = status;
        if( item !== false ){
            const l = JSON.parse(JSON.stringify(item));
            const id= l.id;
            if( typeof vm.q[id] !== undefined ){
                l.quantity = vm.q[id].to_ship;
                l.to_ship = vm.q[id].to_ship;
                l.shipped = vm.q[id].shipped;
            }
            else{
                l.to_ship = l.quantity;
                l.shipped = 0;
            }
            Vue.set( vm.fulfilment, 'items', [l] );
        }
        else{
            if (basket_mode) {
            Vue.set( vm.fulfilment, 'items', [] )
                //console.log(vm.selected_basket)
                //console.log(vm.order)

                const keys = Object.keys(vm.order.baskets);

                // print all keys

                //console.log(keys);

                keys.forEach((key, index) => {
                    //console.log(vm.order.baskets[key].items);
                    vm.order.baskets[key].items.forEach(function(line){
                      if (vm.selected_basket.includes(line.id)) {
                        var id=line.id
                        const l = JSON.parse(JSON.stringify(line));
                        if( typeof vm.q[id] !== undefined ){
                            l.quantity = vm.q[id].to_ship;
                            l.to_ship = vm.q[id].to_ship;
                            l.shipped = vm.q[id].shipped;
                        }
                        else{
                            l.to_ship = l.quantity;
                            l.shipped = 0;
                        }
                        if(line) vm.fulfilment.items.push(l);
                      }
                   })
                });
                // vm.order.baskets.forEach(function (basket){
                //    //console.log(id)
                //    //vm.or
                   
                // });
            }else{
                Vue.set( vm.fulfilment, 'items', [] )
                console.log(vm.selected)
            vm.selected.forEach(function (id){
                const line = vm.order.items.find( (item) => item.id==id );
                const l = JSON.parse(JSON.stringify(line));
                if( typeof vm.q[id] !== undefined ){
                    l.quantity = vm.q[id].to_ship;
                    l.to_ship = vm.q[id].to_ship;
                    l.shipped = vm.q[id].shipped;
                }
                else{
                    l.to_ship = l.quantity;
                    l.shipped = 0;
                }
                if(line) vm.fulfilment.items.push(l);
            });
        }
        }
    },
    dispatch_or_return: function (){
        const d = this.fulfilment.dispatch_or_return;
        this.fulfilment.items.forEach(function (item){
                item.quantity = d==='Dispatch' ? item.to_ship : item.shipped;
        });
    },
    fulfil_items: function (event){
        let vm = this;
        vm.dbtn(event,true);
        let url = '/admin/productorders/fulfil_items';
        const params = new URLSearchParams();
        for (const item in vm.fulfilment) {
            if( typeof vm.fulfilment[item]==='object' || Array.isArray(vm.fulfilment[item]) && vm.fulfilment[item] !== null) {
                params.append(item, JSON.stringify(vm.fulfilment[item]));
            } else params.append(item, vm.fulfilment[item]);
        }
        params.append( 'order_id', vm.order.id );
        params.append( 'invoice_id', vm.order.invoice_id );

        axios.post(url,params)
            .then(function (r) {
                const flag = r.data.success ? 'success' : 'warning';
                if(r.data.items){
                    Vue.set( vm.order, 'items', r.data.items );
                    vm.select_all = false;
                }
                if(r.data.history){
                    Vue.set( vm, 'fulfilment_history', r.data.history );
                }
                if(r.data.order){
                    Vue.set( vm, 'order', r.data.order );
                    vm.select_all = false;
                }
                vm.push_message(r.data.message, flag);
                if(r.data.success){
                    $('#fulfilment_modal').modal('hide');
                    const c = JSON.parse(JSON.stringify(vm.fulfilment_copy));
                    Vue.set( vm, 'fulfilment', c );
                    vm.toggle_reminder(r.data.success);
                }
            })
            .catch(function (error) {
                vm.push_message( error, 'error' );
            }).finally(()=>vm.dbtn(event,false));
    },
    add_payment: function (event){
        let vm = this;
        vm.dbtn(event,true);
        let url = '/admin/productorders/add_payment';
        vm.payment.payment_for = vm.order.invoice_username;
        vm.payment.currency = vm.order.currency;
        const params = new URLSearchParams();
        for( const item in vm.payment ){
            if( typeof vm.payment[item] === 'object' && vm.payment[item] !== null ){
                params.append( item, JSON.stringify(vm.payment[item]) );
            }
            else params.append( item, vm.payment[item] );
        }
        params.append( 'order_id', vm.order.id );
        params.append( 'invoice_id', vm.order.invoice_id );
        params.append( 'invoice_username', vm.order.invoice_username );
        axios.post(url,params)
            .then(function (r) {
                const flag = r.data.success ? 'success' : 'warning';
                if(r.data.order){
                    Vue.set( vm, 'order', r.data.order );
                }
                if(r.data.payments){
                    Vue.set( vm, 'payments', r.data.payments );
                }
                if(r.data.log ){
                    vm.order_history.unshift(r.data.log);
                }
                if(r.data.message) vm.push_message(r.data.message, flag);
                else vm.push_message( 'An unknown error occurred.' )
                if(r.data.success){
                    $('#add_payment').modal('hide');
                    const c = JSON.parse(JSON.stringify(vm.payment_copy));
                    Vue.set( vm, 'payment', c );
                }

                vm.payment={ token: '', payment_method:'', amount: '', payment_or_refund: '', comment: '', date:'', email:[], phone:[] };
            })
            .catch(function (error) {
                vm.push_message( error, 'error' );
            }).finally(()=>vm.dbtn(event,false));
    },
    select_all_items: function (){
        let vm = this;
        setTimeout( function (){
            const s = vm.select_all;
            vm.order.items.forEach( (item) => Vue.set(item, 'selected', s) );
        }, 200 );
    },
    select_item: function (b=null){
        let vm = this;
        if (b==null) {
        setTimeout( function (){
            vm.select_all = vm.order.items.length === vm.selected.length;
        }, 200);
        }else{
            setTimeout( function (){
              // console.log(vm.order.baskets[b].items.length)
              // vm.select_basket[b] = vm.order.baskets[b].items.length === vm.selected.length;
            }, 200);
        }
    },
    select_all_basket_items: function (basket_items,i){
        let vm = this;
        var checked = $(".basket_selector"+i+":checked").length;
        if (checked == 0) {
            vm.select_basket[i]=[];
        } else {
            $('.basket_entry'+i).click();
        }       
       
    },

    select_basket_item: function (b,product_id){
        let vm = this;
        b=String(b);
        product_id=String(product_id);
        if (vm.select_basket[b]) {
            if (vm.select_basket[b].includes(product_id)) {
                var product_idIndex = vm.select_basket[b].indexOf(product_id);//get  "product_id" index
                //remove product_id from the baskets array
                vm.select_basket[b].splice(product_idIndex, 1); 
            }else{
              vm.select_basket[b].push(product_id);
            }
        }else{
            vm.select_basket[b]=[product_id];
        }
        // console.log(vm.select_basket)
        // console.log(vm.select_basket.length)
        //this.selected_basket_2()
        vm.selected_basket=[];
        vm.select_basket.forEach(function(element) {
        
        // console.log(element)
        // console.log(element.length)
        if (element.length>0) {
        element.forEach(function(r){
           vm.selected_basket.push(r)
        })
        }

       })    
    },  
    push_message: function (msg,type,timeout=false){
        if(msg) {
            bootoast.toast({
                message: msg,
                type: type,
                icon: 'info-sign',
                position: 'rightBottom',
                timeout: timeout?timeout:(type==='success'?5:15)
            });
        }
    },
    push_messages: function (msgs){
        if( Array.isArray(msgs) ){
            let vm = this;
            msgs.forEach(function (item){
                const flag = item.success?'success':'warning';
                vm.push_message( item.message, flag );
            })
        }
    },
    compose: function (){
        if(this.show_reminder) this.toggle_reminder(false);
    },
    set_template: function (template){
        if( template ){
            this.compose_message.subject = template.subject;
            this.compose_message.message = template.html;
        }
    },
    set_status_template: function (template){
        if( template ){
            this.status_update.message = template.html;
        }
    },
    preview_message: function (){
        let vm = this;
        vm.compose_message.previews = [];
        let msg = '';
        if( vm.compose_message.message ){
            if( vm.compose_message.phone.length>0 ){
                vm.compose_message.sms = vm.replace_tags( vm.compose_message.message, 'phone', vm.compose_message );
            }
            else{
                Vue.set( vm.compose_message, 'sms', [] );
            }
            let msg = vm.message_header ? vm.message_header : MESSAGE_HEADER;
            msg = msg.replace( '{{headline}}', 'New Message Alert' );
            msg = msg.replace( '{{subject}}', 'You have received a new message on your order:' );
            msg = msg.replace( '{{message_body}}', vm.compose_message.message );
            auto = vm.compose_message.sms.length === 0;
            vm.compose_message.previews = vm.replace_tags( msg, 'email', vm.compose_message, auto );
        }
        return true;
    },
    replace_tags: function(msg,section,form,auto=false, order_status=false){
        let vm = this;
        let messages = [];
        const url = window.location.origin + '/products/orders/' + vm.order.id;
        msg = msg.replaceAll( '{{invoice_number}}', vm.order.invoice_username );
        msg = msg.replaceAll( '{{order_status}}', order_status ? order_status : vm.order.order_status_name );
        msg = msg.replaceAll( '{{payment_status}}', vm.order.payment_status_name );
        msg = msg.replaceAll( '{{order_total}}', vm.order.symbol+vm.order.order_total );
        msg = msg.replaceAll( '{{order_url}}', url );
        msg = msg.replaceAll( '{{admin_name}}', vm.admin_name );
        msg = msg.replaceAll( '{{school_name}}', vm.settings.school_name );
        msg = msg.replaceAll( '{{school_email}}', vm.settings.email );

        if( form[section].length>0 ){
            form[section].forEach(function (item){
                let m = msg;
                const user = vm.contacts[section][item] ? vm.contacts[section][item] : { first_name:item, surname:item, full_name:item };
                m = m.replaceAll( '{{first_name}}', user.first_name );
                m = m.replaceAll( '{{surname}}', user.surname );
                m = m.replaceAll( '{{full_name}}', user.full_name );
                if( section==='phone' ) m = m.replace( /(<([^>]+)>)/ig, '');    // strip html
                messages.push(m);
            });
        }
        else if(section==='email' && auto){
            form.email.push(vm.order.email);
            msg = msg.replaceAll( '{{first_name}}', vm.order.first_name );
            msg = msg.replaceAll( '{{surname}}', vm.order.surname );
            msg = msg.replaceAll( '{{full_name}}', vm.order.first_name+' '+vm.order.surname );
            messages.push(msg);
        }
        return messages;
    },
    send_message: function (props){
        let vm = this;
        const m = vm.compose_message.message ? vm.compose_message.message.trim: false;
        if( m && (vm.compose_message.email.length>0||vm.compose_message.phone.length>0) ) {
            props.nextTab();
            vm.message_status = 'info';
            vm.message_response = [];
            let url = '/admin/productorders/send_message';
            const params = new URLSearchParams();
            for (const item in vm.compose_message) {
                if( typeof vm.compose_message[item]==='object' || Array.isArray(vm.compose_message[item]) && vm.compose_message[item] !== null) {
                    params.append(item, JSON.stringify(vm.compose_message[item]));
                } else params.append(item, vm.compose_message[item]);
            }
            params.append('order_id', vm.order.id);
            params.append('invoice_id', vm.order.invoice_id);
            params.append('invoice_username', vm.order.invoice_username);
            params.append('user_id', vm.order.user_id);
            axios.post(url, params)
                .then(function (r) {
                    vm.message_status = r.data.success ? 'success' : 'warning';
                    if( r.data.messages ){
                        vm.message_response = r.data.messages;
                        vm.push_messages(r.data.messages);
                    }
                    if(r.data.log ){
                        vm.order_history.unshift(r.data.log);
                    }
                })
                .catch(function (error) {
                    vm.push_message(error, 'error');
                });
            return true;
        }
        props.prevTab();
        vm.push_message('Message body cannot be empty.');
        return false;
    },
    nl2br: function (str, is_xhtml) {
        if (typeof str === 'undefined' || str === null) {
            return '';
        }
        var breakTag = (is_xhtml || typeof is_xhtml === 'undefined') ? '<br />' : '<br>';
        return (str + '').replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g, '$1' + breakTag + '$2');
    },
    resetMessage: function (props){
        let vm = this;
        Vue.set( vm, 'compose_message', JSON.parse(JSON.stringify(vm.compose_message_copy)) );
        vm.message_status = '';
        vm.message_response = [];
        props.prevTab();
        props.prevTab();
        $('#compose_message').modal('hide');
        return true;
    },
    get_email_content: function (id,email){
        let vm = this;
        vm.email_to = email;
        let url = '/admin/system/get_email_body/'+id;
        axios.post(url)
            .then(function (r) {
                vm.email_content = r.data;
            })
            .catch(function (error) {
                vm.push_message( error, 'error' );
                vm.cancel_products();
            });
    },
    dbtn(event, disabled=true){
        event.target.disabled = disabled;
    },
    prepare_delivery_list: async function(){
        let vm = this;
        const response = await axios.get('/admin/productorders/prepare_delivery_list/'+vm.order.id);
        return response.data;
    },
    prepare_finance_list: async function(){
        let vm = this;
        const response = await axios.get('/admin/productorders/prepare_finance_list/'+vm.order.id);
        return response.data;
    },
    update_status: function (status){
        const c = JSON.parse(JSON.stringify(this.status_update_copy));
        Vue.set( this, 'status_update', c );
        this.status_update.status = status;
        this.status_update.email = [this.order.email];
        if(this.show_reminder) this.toggle_reminder(false);
    },
    toggle_reminder: function (show=false){
        let vm = this;
        vm.show_reminder = show;
        if(show){
            const h = $('#vueapp .reminder').height() + 30;
            $('#vueapp').css('padding-bottom', h+'px');
            $('.bootoast-container').css('margin-bottom', h+'px');
            if(vm.reminder_timer) clearTimeout(vm.reminder_timer);
            vm.reminder_timer = setTimeout( vm.toggle_reminder, 1000*15 );
        }
        else{
            $('#vueapp').css('padding-bottom', '0px');
            $('.bootoast-container').css('margin-bottom', '0px');
        }
    },
    sms_count: function(msg){
        return Math.ceil( msg.length / 160 );
    },
    get_basket_data: function(basket_id=0){
        if (basket_id==0) {
            let vm = this;
            vm.basket_edit['basket_name']='';
            vm.basket_edit['contact_name']='';
            vm.basket_edit['contact_number']='';
            vm.basket_edit['basket_ad_id']='';
            vm.basket_edit['house_number']='';
            vm.basket_edit['city']='';
            vm.basket_edit['region']='';
            vm.basket_edit['country']='';
            vm.basket_edit['postcode']='';
            vm.basket_edit['ad_id']='';
            vm.basket_edit['cart_basket_id']='';
            $('#basketeditor_text').text('Add New Basket')
        }else{
            let vm = this;
            let url = '/admin/sis_products/get_basket_data';
            const params = new URLSearchParams();
            params.append( 'basket_id', basket_id );
            axios.post(url,params)
                .then(function (r) {
                    console.log(r.data.basket)
                    vm.basket_edit=r.data.basket
                    $('#basketeditor_text').text('Edit Basket '+r.data.basket.basket_name)



                })
                .catch(function (error) {
                    vm.push_message(error, 'error');
                })
        }
    },
    prepare_delete_basket(id) {
        $('#delete_cart_basket_id').val(id);
        $('#deletebasketmodal').modal('show');
    },
    delete_basket: function(){
                let vm = this;
                let url = '/admin/sis_products/delete_basket';
                const params = new URLSearchParams();
                params.append('basket_id', $('#delete_cart_basket_id').val());
                params.append('order_id', $('#order_id').val());

                axios.post(url, params)
                    .then(function (r) {
                        const msg = r.data.message || 'An unknown response was received.';
                        vm.push_messages(msg);
                        console.log(msg);

                        // Show message in the modal
                        $('#delete_resp_message').html(msg).removeAttr('hidden');

                        // Check for block condition
                        const isBlocked = msg.includes('Cannot delete the only basket');

                        if ($('#refresh_when_done').val() === 'yes') {
                            setTimeout(function () {
                                $('#deletebasketModal').modal('hide'); // Close modal
                                window.location.reload();
                            }, 3000);
                        }
                    })
                    .catch(function (error) {
                        vm.push_message(error, 'error');
                    });
},
    add_basket: function (){
        let vm = this;
        let url = '/admin/sis_products/add_basket';
        const params = new URLSearchParams();
        params.append('basket_name', vm.basket_edit.basket_name);
        params.append('contact_name', vm.basket_edit.contact_name);
        params.append('contact_number', vm.basket_edit.contact_number);
        //params.append('cart_basket_id', vm.basket_info.cart_basket_id);
        params.append('house_number', vm.basket_edit.house_number);
        params.append('city', vm.basket_edit.city);
        params.append('region', vm.basket_edit.region);
        params.append('country', vm.basket_edit.country);
        params.append('postcode', vm.basket_edit.postcode);
        params.append('contact_email', vm.basket_edit.contact_email);
        params.append('organization_name', vm.basket_edit.organization_name);
        params.append('ad_id', vm.basket_edit.ad_id);
        params.append('cart_basket_id', vm.basket_edit.cart_basket_id );
        params.append('order_id', vm.order.id)
        //console.log($('#basket_name)
        axios.post(url,params)
          .then(function (r) {
             vm.push_messages(r.data.message);
             $("#closebasketmodal").click()
            vm.basket_edit={}
             Vue.set( vm, 'order', r.data.order );
             Vue.set( vm, 'rejection_state', false);
             Vue.set( vm, 'order_history', r.data.order_history );
              Vue.set( vm, 'order_baskets', r.data.order_baskets );
          })
          .catch(function (error) {
            vm.success = false;
            vm.messages = [error];
          });
    },

      update_prereq_status: function(order_id,status,order_item=null,order_items=null){
        let vm = this;
        let url = '/admin/productorders/update_prereq_status';
        const params = new URLSearchParams();
        params.append('order_id',order_id)
        params.append('status',status)
        if (order_item!=null) {
            //params.append('order_item',order_item)
        }
        if (order_items!=null) {
            //params.append('order_items',order_items)
        }
        if ($("#prereq_item_id").val()!="") {
            params.append('prereq_item_id',$("#prereq_item_id").val())
        }
        if (status=="Rejected") {
            params.append('rejectionreason', $("#rejectionreason").val());
        }
        axios.post(url,params)
            .then(function (r) {
                $("#prereq_item_id").val('')
                $("#PrerequisitesModal").modal('hide')
                 Vue.set( vm, 'order', r.data.order );
                 Vue.set( vm, 'rejection_state', false);
                 Vue.set( vm, 'order_history', r.data.order_history );
                 vm.push_message(r.data.message);
                 if ((typeof r.data.reload !=='undefined')) {
                    window.location.reload()
                 }
            })
            .catch(function (error) {
                vm.push_message(error, 'error');
            })
     },

     capture_order_payment: function(order_id){
        let vm = this;
        let url = '/admin/productorders/capture_order_payment';
        const params = new URLSearchParams();
        params.append('order_id',order_id)
        axios.post(url,params)
            .then(function (r) {
                 Vue.set( vm, 'order', r.data.order );
                 if ((typeof r.data.reload !=='undefined')) {
                    vm.push_message(r.data.message);
                    window.location.reload()
                 }else{
                    vm.push_message(r.data.message);
                 }
            })
            .catch(function (error) {
                vm.push_message(error, 'error');
            })
     },

     
};

const MESSAGE_HEADER = ''+
    'Dear {{first_name}}\n\n' +
    'You have received a new message on your {{school_name}} Order {{invoice_number}}.\n\n' +
    'Order Status: {{order_status}}\n' +
    'Payment Status: {{payment_status}}\n\n' +
    'Message:\n'+
    '{{message_body}}\n\n' +
    'You may view the order here\n' +
    '{{order_url}}\n' +
    '\n' +
    'Regards,\n' +
    '{{admin_name}}\n' +
    '{{school_name}}';

const STATUS_MESSAGE_HEADER = ''+
    'Dear {{first_name}}\n\n' +
    'Your order {{invoice_number}} on {{school_name}} has been updated.\n\n' +
    'Order Status: {{order_status}}\n' +
    'Payment Status: {{payment_status}}\n\n' +
    '{{message_body}}\n\n' +
    'You may view the order here\n' +
    '{{order_url}}\n' +
    '\n' +
    'Regards,\n' +
    '{{admin_name}}\n' +
    '{{school_name}}';
