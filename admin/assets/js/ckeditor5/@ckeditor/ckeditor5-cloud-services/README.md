CKEditor 5's Cloud Services integration
=======================================

[![npm version](https://badge.fury.io/js/%40ckeditor%2Fckeditor5-cloud-services.svg)](https://www.npmjs.com/package/@ckeditor/ckeditor5-cloud-services)
[![Coverage Status](https://coveralls.io/repos/github/ckeditor/ckeditor5/badge.svg?branch=master)](https://coveralls.io/github/ckeditor/ckeditor5?branch=master)
[![Build Status](https://travis-ci.com/ckeditor/ckeditor5.svg?branch=master)](https://travis-ci.com/ckeditor/ckeditor5)

CKEditor 5's [Cloud Services](https://ckeditor.com/ckeditor-cloud-services/) integration layer, which handles `config.cloudServices` and tokens.

## Documentation

See the [`@ckeditor/ckeditor5-cloud-services` package](https://ckeditor.com/docs/ckeditor5/latest/api/cloud-services.html) page in [CKEditor 5 documentation](https://ckeditor.com/docs/ckeditor5/latest/).

## License

Licensed under the terms of [GNU General Public License Version 2 or later](http://www.gnu.org/licenses/gpl.html). For full details about the license, please check the `LICENSE.md` file or [https://ckeditor.com/legal/ckeditor-oss-license](https://ckeditor.com/legal/ckeditor-oss-license).
