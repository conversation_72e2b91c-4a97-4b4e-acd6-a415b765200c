{"plugins": [{"name": "Bold", "className": "Bold", "description": "Implements bold formatting support. It is a part of the basic text styles package.", "docs": "features/basic-styles.html", "path": "src/bold.js", "uiComponents": [{"type": "<PERSON><PERSON>", "name": "bold", "iconPath": "theme/icons/bold.svg"}], "htmlOutput": [{"elements": "strong"}]}, {"name": "Code", "className": "Code", "description": "Implements inline code formatting support. It is a part of the basic text styles package.", "docs": "features/basic-styles.html", "path": "src/code.js", "uiComponents": [{"type": "<PERSON><PERSON>", "name": "code", "iconPath": "theme/icons/code.svg"}], "htmlOutput": [{"elements": "code"}]}, {"name": "Italic", "className": "Italic", "description": "Implements italic formatting support. It is a part of the basic text styles package.", "docs": "features/basic-styles.html", "path": "src/italic.js", "uiComponents": [{"type": "<PERSON><PERSON>", "name": "italic", "iconPath": "theme/icons/italic.svg"}], "htmlOutput": [{"elements": "i"}]}, {"name": "Strikethrough", "className": "Strikethrough", "description": "Implements strikethrough formatting support. It is a part of the basic text styles package.", "docs": "features/basic-styles.html", "path": "src/strikethrough.js", "uiComponents": [{"type": "<PERSON><PERSON>", "name": "strikethrough", "iconPath": "theme/icons/strikethrough.svg"}], "htmlOutput": [{"elements": "s"}]}, {"name": "Subscript", "className": "Subscript", "description": "Implements subscript formatting support. It is a part of the basic text styles package.", "docs": "features/basic-styles.html", "path": "src/subscript.js", "uiComponents": [{"type": "<PERSON><PERSON>", "name": "subscript", "iconPath": "theme/icons/subscript.svg"}], "htmlOutput": [{"elements": "sub"}]}, {"name": "Superscript", "className": "Superscript", "description": "Implements superscript formatting support. It is a part of the basic text styles package.", "docs": "features/basic-styles.html", "path": "src/superscript.js", "uiComponents": [{"type": "<PERSON><PERSON>", "name": "superscript", "iconPath": "theme/icons/superscript.svg"}], "htmlOutput": [{"elements": "sup"}]}, {"name": "Underline", "className": "Underline", "description": "Implements underline formatting support. It is a part of the basic text styles package.", "docs": "features/basic-styles.html", "path": "src/underline.js", "uiComponents": [{"type": "<PERSON><PERSON>", "name": "underline", "iconPath": "theme/icons/underline.svg"}], "htmlOutput": [{"elements": "u"}]}]}