
 Wed, 06 Feb 2019 09:30:45 +0000 INFO : [execute] (C:\xampp\htdocs\heia\system-lite\engine\tools\authorize_net\vendor\authorizenet\authorizenet\lib\net\authorize\api\controller\base\ApiOperationBase.php : 114) - Request Creation Begin
 Wed, 06 Feb 2019 09:30:45 +0000 DEBUG : [execute] (C:\xampp\htdocs\heia\system-lite\engine\tools\authorize_net\vendor\authorizenet\authorizenet\lib\net\authorize\api\controller\base\ApiOperationBase.php : 115) - net\authorize\api\contract\v1\CreateTransactionRequest Object
(
    [transactionRequest:net\authorize\api\contract\v1\CreateTransactionRequest:private] => net\authorize\api\contract\v1\TransactionRequestType Object
        (
            [transactionType:net\authorize\api\contract\v1\TransactionRequestType:private] => authCaptureTransaction
            [amount:net\authorize\api\contract\v1\TransactionRequestType:private] => 151.51
            [currencyCode:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [payment:net\authorize\api\contract\v1\TransactionRequestType:private] => net\authorize\api\contract\v1\PaymentType Object
                (
                    [creditCard:net\authorize\api\contract\v1\PaymentType:private] => net\authorize\api\contract\v1\CreditCardType Object
                        (
                            [cardCode:net\authorize\api\contract\v1\CreditCardType:private] => 
                            [isPaymentToken:net\authorize\api\contract\v1\CreditCardType:private] => 
                            [cryptogram:net\authorize\api\contract\v1\CreditCardType:private] => 
                            [tokenRequestorName:net\authorize\api\contract\v1\CreditCardType:private] => 
                            [tokenRequestorId:net\authorize\api\contract\v1\CreditCardType:private] => 
                            [tokenRequestorEci:net\authorize\api\contract\v1\CreditCardType:private] => 
                            [cardNumber:net\authorize\api\contract\v1\CreditCardSimpleType:private] => xxxx-1111
                            [expirationDate:net\authorize\api\contract\v1\CreditCardSimpleType:private] => xxxx
                        )

                    [bankAccount:net\authorize\api\contract\v1\PaymentType:private] => 
                    [trackData:net\authorize\api\contract\v1\PaymentType:private] => 
                    [encryptedTrackData:net\authorize\api\contract\v1\PaymentType:private] => 
                    [payPal:net\authorize\api\contract\v1\PaymentType:private] => 
                    [opaqueData:net\authorize\api\contract\v1\PaymentType:private] => 
                    [emv:net\authorize\api\contract\v1\PaymentType:private] => 
                    [dataSource:net\authorize\api\contract\v1\PaymentType:private] => 
                )

            [profile:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [solution:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [callId:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [terminalNumber:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [authCode:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [refTransId:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [splitTenderId:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [order:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [lineItems:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [tax:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [duty:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [shipping:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [taxExempt:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [poNumber:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [customer:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [billTo:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [shipTo:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [customerIP:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [cardholderAuthentication:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [retail:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [employeeId:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [transactionSettings:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [userFields:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [surcharge:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [merchantDescriptor:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [subMerchant:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [tip:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [processingOptions:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [subsequentAuthInformation:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [otherTax:net\authorize\api\contract\v1\TransactionRequestType:private] => 
            [shipFrom:net\authorize\api\contract\v1\TransactionRequestType:private] => 
        )

    [merchantAuthentication:net\authorize\api\contract\v1\ANetApiRequestType:private] => net\authorize\api\contract\v1\MerchantAuthenticationType Object
        (
            [name:net\authorize\api\contract\v1\MerchantAuthenticationType:private] => 5H7BGXb65tG
            [transactionKey:net\authorize\api\contract\v1\MerchantAuthenticationType:private] => xxxx
            [sessionToken:net\authorize\api\contract\v1\MerchantAuthenticationType:private] => 
            [password:net\authorize\api\contract\v1\MerchantAuthenticationType:private] => 
            [impersonationAuthentication:net\authorize\api\contract\v1\MerchantAuthenticationType:private] => 
            [fingerPrint:net\authorize\api\contract\v1\MerchantAuthenticationType:private] => 
            [clientKey:net\authorize\api\contract\v1\MerchantAuthenticationType:private] => 
            [accessToken:net\authorize\api\contract\v1\MerchantAuthenticationType:private] => 
            [mobileDeviceId:net\authorize\api\contract\v1\MerchantAuthenticationType:private] => 
        )

    [clientId:net\authorize\api\contract\v1\ANetApiRequestType:private] => sdk-php-2.0.0-ALPHA
    [refId:net\authorize\api\contract\v1\ANetApiRequestType:private] => ref1549445443
)

 Wed, 06 Feb 2019 09:30:45 +0000 INFO : [execute] (C:\xampp\htdocs\heia\system-lite\engine\tools\authorize_net\vendor\authorizenet\authorizenet\lib\net\authorize\api\controller\base\ApiOperationBase.php : 126) - Request  Creation End
 Wed, 06 Feb 2019 09:30:45 +0000 INFO : [_sendRequest] (C:\xampp\htdocs\heia\system-lite\engine\tools\authorize_net\vendor\authorizenet\authorizenet\lib\net\authorize\util\HttpClient.php : 75) -  Url: https://apitest.authorize.net/xml/v1/request.api
 Wed, 06 Feb 2019 09:30:45 +0000 INFO : [_sendRequest] (C:\xampp\htdocs\heia\system-lite\engine\tools\authorize_net\vendor\authorizenet\authorizenet\lib\net\authorize\util\HttpClient.php : 77) - Request to AnetApi: 
{"createTransactionRequest":{"merchantAuthentication":{"name":"5H7BGXb65tG","transactionKey":"25wUA4Cf5BjA2t5N"},"clientId":"sdk-php-2.0.0-ALPHA","refId":"ref1549445443","transactionRequest":{"transactionType":"authCaptureTransaction","amount":151.51,"payment":{"creditCard":{"cardNumber":"xxxx","expirationDate":"2038-12"}}}}}
 Wed, 06 Feb 2019 09:30:45 +0000 INFO : [_sendRequest] (C:\xampp\htdocs\heia\system-lite\engine\tools\authorize_net\vendor\authorizenet\authorizenet\lib\net\authorize\util\HttpClient.php : 89) - Sending 'XML' Request type
 Wed, 06 Feb 2019 09:30:45 +0000 INFO : [_sendRequest] (C:\xampp\htdocs\heia\system-lite\engine\tools\authorize_net\vendor\authorizenet\authorizenet\lib\net\authorize\util\HttpClient.php : 94) - Sending http request via Curl
 Wed, 06 Feb 2019 09:30:49 +0000 INFO : [_sendRequest] (C:\xampp\htdocs\heia\system-lite\engine\tools\authorize_net\vendor\authorizenet\authorizenet\lib\net\authorize\util\HttpClient.php : 96) - Response from AnetApi: ﻿{"transactionResponse":{"responseCode":"1","authCode":"FV2NJ9","avsResultCode":"Y","cvvResultCode":"P","cavvResultCode":"2","transId":"***********","refTransID":"","transHash":"CE9A7125E7BA36B5D8A22FBC4D87FBFB","testRequest":"0","accountNumber":"XXXX1111","accountType":"Visa","messages":[{"code":"1","description":"This transaction has been approved."}],"transHashSha2":"","SupplementalDataQualificationIndicator":0},"refId":"ref1549445443","messages":{"resultCode":"Ok","message":[{"code":"I00001","text":"Successful."}]}}