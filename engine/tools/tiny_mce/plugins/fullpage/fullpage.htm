<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>{#fullpage_dlg.title}</title>
	<script type="text/javascript" src="../../tiny_mce_popup.js"></script>
	<script type="text/javascript" src="../../utils/mctabs.js"></script>
	<script type="text/javascript" src="../../utils/form_utils.js"></script>
	<script type="text/javascript" src="js/fullpage.js"></script>
	<link href="css/fullpage.css" rel="stylesheet" type="text/css" />
</head>
<body id="fullpage" style="display: none">
<form onsubmit="FullPageDialog.update();return false;" name="fullpage" action="#">
		<div class="tabs">
			<ul>
				<li id="meta_tab" class="current"><span><a href="javascript:mcTabs.displayTab('meta_tab','meta_panel');" onmousedown="return false;">{#fullpage_dlg.meta_tab}</a></span></li>
				<li id="appearance_tab"><span><a href="javascript:mcTabs.displayTab('appearance_tab','appearance_panel');" onmousedown="return false;">{#fullpage_dlg.appearance_tab}</a></span></li>
			</ul>
		</div>

		<div class="panel_wrapper">
			<div id="meta_panel" class="panel current">
				<fieldset>
					<legend>{#fullpage_dlg.meta_props}</legend>

					<table border="0" cellpadding="4" cellspacing="0">
						<tr>
							<td class="nowrap"><label for="metatitle">{#fullpage_dlg.meta_title}</label>&nbsp;</td>
							<td><input type="text" id="metatitle" name="metatitle" value="" class="mceFocus" /></td>
						</tr>
						<tr>
							<td class="nowrap"><label for="metakeywords">{#fullpage_dlg.meta_keywords}</label>&nbsp;</td>
							<td><textarea id="metakeywords" name="metakeywords" rows="4"></textarea></td>
						</tr>
						<tr>
							<td class="nowrap"><label for="metadescription">{#fullpage_dlg.meta_description}</label>&nbsp;</td>
							<td><textarea id="metadescription" name="metadescription" rows="4"></textarea></td>
						</tr>
						<tr>
							<td class="nowrap"><label for="metaauthor">{#fullpage_dlg.author}</label>&nbsp;</td>
							<td><input type="text" id="metaauthor" name="metaauthor" value="" /></td>
						</tr>
						<tr>
							<td class="nowrap"><label for="metacopyright">{#fullpage_dlg.copyright}</label>&nbsp;</td>
							<td><input type="text" id="metacopyright" name="metacopyright" value="" /></td>
						</tr>
						<tr>
							<td class="nowrap"><label for="metarobots">{#fullpage_dlg.meta_robots}</label>&nbsp;</td>
							<td>
								<select id="metarobots" name="metarobots">
											<option value="">{#not_set}</option> 
											<option value="index,follow">{#fullpage_dlg.meta_index_follow}</option>
											<option value="index,nofollow">{#fullpage_dlg.meta_index_nofollow}</option>
											<option value="noindex,follow">{#fullpage_dlg.meta_noindex_follow}</option>
											<option value="noindex,nofollow">{#fullpage_dlg.meta_noindex_nofollow}</option>
								</select>
							</td>
						</tr>
					</table>
				</fieldset>

				<fieldset>
					<legend>{#fullpage_dlg.langprops}</legend>

					<table border="0" cellpadding="4" cellspacing="0">
						<tr>
							<td class="column1"><label for="docencoding">{#fullpage_dlg.encoding}</label></td> 
							<td>
								<select id="docencoding" name="docencoding"> 
										<option value="">{#not_set}</option>
								</select>
							</td> 
						</tr>
						<tr>
							<td class="nowrap"><label for="doctype">{#fullpage_dlg.doctypes}</label>&nbsp;</td>
							<td>
								<select id="doctype" name="doctype">
										<option value="">{#not_set}</option>
								</select>
							</td>
						</tr>
						<tr>
							<td class="nowrap"><label for="langcode">{#fullpage_dlg.langcode}</label>&nbsp;</td>
							<td><input type="text" id="langcode" name="langcode" value="" /></td>
						</tr>
						<tr>
							<td class="column1"><label for="langdir">{#fullpage_dlg.langdir}</label></td> 
							<td>
								<select id="langdir" name="langdir"> 
										<option value="">{#not_set}</option> 
										<option value="ltr">{#fullpage_dlg.ltr}</option> 
										<option value="rtl">{#fullpage_dlg.rtl}</option> 
								</select>
							</td> 
						</tr>
						<tr>
							<td class="nowrap"><label for="xml_pi">{#fullpage_dlg.xml_pi}</label>&nbsp;</td>
							<td><input type="checkbox" id="xml_pi" name="xml_pi" class="checkbox" /></td>
						</tr>
					</table>
				</fieldset>
			</div>

			<div id="appearance_panel" class="panel">
				<fieldset>
					<legend>{#fullpage_dlg.appearance_textprops}</legend>

					<table border="0" cellpadding="4" cellspacing="0">
						<tr>
							<td class="column1"><label for="fontface">{#fullpage_dlg.fontface}</label></td> 
							<td>
								<select id="fontface" name="fontface" onchange="FullPageDialog.changedStyleProp();">
										<option value="">{#not_set}</option>
								</select>
							</td> 
						</tr>

						<tr>
							<td class="column1"><label for="fontsize">{#fullpage_dlg.fontsize}</label></td> 
							<td>
								<select id="fontsize" name="fontsize" onchange="FullPageDialog.changedStyleProp();">
										<option value="">{#not_set}</option>
								</select>
							</td>
						</tr>

						<tr>
							<td class="column1"><label for="textcolor">{#fullpage_dlg.textcolor}</label></td> 
							<td>
								<table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td><input id="textcolor" name="textcolor" type="text" value="" size="9" onchange="updateColor('textcolor_pick','textcolor');FullPageDialog.changedStyleProp();" /></td>
										<td id="textcolor_pickcontainer">&nbsp;</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</fieldset>

				<fieldset>
					<legend>{#fullpage_dlg.appearance_bgprops}</legend>

					<table border="0" cellpadding="4" cellspacing="0">
						<tr>
							<td class="column1"><label for="bgimage">{#fullpage_dlg.bgimage}</label></td> 
							<td>
								<table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td><input id="bgimage" name="bgimage" type="text" value="" onchange="FullPageDialog.changedStyleProp();" /></td>
										<td id="bgimage_pickcontainer">&nbsp;</td>
									</tr>
								</table>
							</td>
						</tr>
						<tr>
							<td class="column1"><label for="bgcolor">{#fullpage_dlg.bgcolor}</label></td> 
							<td>
								<table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td><input id="bgcolor" name="bgcolor" type="text" value="" size="9" onchange="updateColor('bgcolor_pick','bgcolor');FullPageDialog.changedStyleProp();" /></td>
										<td id="bgcolor_pickcontainer">&nbsp;</td>
									</tr>
								</table>
							</td> 
						</tr>
					</table>
				</fieldset>

				<fieldset>
					<legend>{#fullpage_dlg.appearance_marginprops}</legend>

					<table border="0" cellpadding="4" cellspacing="0">
						<tr>
							<td class="column1"><label for="leftmargin">{#fullpage_dlg.left_margin}</label></td> 
							<td><input id="leftmargin" name="leftmargin" type="text" value="" onchange="FullPageDialog.changedStyleProp();" /></td>
							<td class="column1"><label for="rightmargin">{#fullpage_dlg.right_margin}</label></td> 
							<td><input id="rightmargin" name="rightmargin" type="text" value="" onchange="FullPageDialog.changedStyleProp();" /></td>
						</tr>
						<tr>
							<td class="column1"><label for="topmargin">{#fullpage_dlg.top_margin}</label></td> 
							<td><input id="topmargin" name="topmargin" type="text" value="" onchange="FullPageDialog.changedStyleProp();" /></td>
							<td class="column1"><label for="bottommargin">{#fullpage_dlg.bottom_margin}</label></td> 
							<td><input id="bottommargin" name="bottommargin" type="text" value="" onchange="FullPageDialog.changedStyleProp();" /></td>
						</tr>
					</table>
				</fieldset>

				<fieldset>
					<legend>{#fullpage_dlg.appearance_linkprops}</legend>

					<table border="0" cellpadding="4" cellspacing="0">
						<tr>
							<td class="column1"><label for="link_color">{#fullpage_dlg.link_color}</label></td> 
							<td>
								<table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td><input id="link_color" name="link_color" type="text" value="" size="9" onchange="updateColor('link_color_pick','link_color');FullPageDialog.changedStyleProp();" /></td>
										<td id="link_color_pickcontainer">&nbsp;</td>
									</tr>
								</table>
							</td>

							<td class="column1"><label for="visited_color">{#fullpage_dlg.visited_color}</label></td> 
							<td>
								<table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td><input id="visited_color" name="visited_color" type="text" value="" size="9" onchange="updateColor('visited_color_pick','visited_color');FullPageDialog.changedStyleProp();" /></td>
										<td id="visited_color_pickcontainer">&nbsp;</td>
									</tr>
								</table>
							</td>
						</tr>

						<tr>
							<td class="column1"><label for="active_color">{#fullpage_dlg.active_color}</label></td> 
							<td>
								<table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td><input id="active_color" name="active_color" type="text" value="" size="9" onchange="updateColor('active_color_pick','active_color');FullPageDialog.changedStyleProp();" /></td>
										<td id="active_color_pickcontainer">&nbsp;</td>
									</tr>
								</table>
							</td>

							<td>&nbsp;</td>
							<td>&nbsp;</td>
						</tr>
					</table>
				</fieldset>

				<fieldset>
					<legend>{#fullpage_dlg.appearance_style}</legend>

					<table border="0" cellpadding="4" cellspacing="0">
						<tr>
							<td class="column1"><label for="stylesheet">{#fullpage_dlg.stylesheet}</label></td> 
							<td><table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td><input id="stylesheet" name="stylesheet" type="text" value="" /></td>
										<td id="stylesheet_browsercontainer">&nbsp;</td>
									</tr>
								</table></td>
						</tr>
						<tr>
							<td class="column1"><label for="style">{#fullpage_dlg.style}</label></td> 
							<td><input id="style" name="style" type="text" value="" onchange="FullPageDialog.changedStyle();" /></td>
						</tr>
					</table>
				</fieldset>
			</div>
		</div>

		<div class="mceActionPanel">
			<input type="submit" id="insert" name="update" value="{#update}" />
			<input type="button" id="cancel" name="cancel" value="{#cancel}" onclick="tinyMCEPopup.close();" />
		</div>
</form>
</body>
</html>
