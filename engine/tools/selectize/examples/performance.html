<!DOCTYPE html>
<!--[if lt IE 7]><html class="no-js lt-ie9 lt-ie8 lt-ie7"><![endif]-->
<!--[if IE 7]><html class="no-js lt-ie9 lt-ie8"><![endif]-->
<!--[if IE 8]><html class="no-js lt-ie9"><![endif]-->
<!--[if gt IE 8]><!--><html class="no-js"><!--<![endif]-->
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<title>Selectize.js Demo</title>
		<meta name="description" content="">
		<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">
		<link rel="stylesheet" href="css/normalize.css">
		<link rel="stylesheet" href="css/stylesheet.css">
		<!--[if IE 8]><script src="js/es5.js"></script><![endif]-->
		<script src="js/jquery.min.js"></script>
		<script src="../dist/js/standalone/selectize.js"></script>
		<script src="js/index.js"></script>
	</head>
    <body>
		<div id="wrapper">
			<h1>Selectize.js</h1>
			<div class="demo">
				<h2>Performance</h2>
				<p>This shows how it performs with 25,000 items.</p>
				<div class="control-group">
					<label for="select-junk">Jumbled Mess:</label>
					<select id="select-junk" placeholder="Start Typing..."></select>
				</div>
				<script>
				// <select id="select-junk"></select>

				var letters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUV';
				var options = [];
				for (var i = 0; i < 25000; i++) {
					var title = [];
					for (var j = 0; j < 8; j++) {
						title.push(letters.charAt(Math.round((letters.length - 1) * Math.random())));
					}
					options.push({
						id: i,
						title: title.join('')
					});
				}

				$('#select-junk').selectize({
					maxItems: null,
					maxOptions: 100,
					valueField: 'id',
					labelField: 'title',
					searchField: 'title',
					sortField: 'title',
					options: options,
					create: false
				});
				</script>
			</div>
		</div>
	</body>
</html>