<!DOCTYPE html>
<!--[if lt IE 7]><html class="no-js lt-ie9 lt-ie8 lt-ie7"><![endif]-->
<!--[if IE 7]><html class="no-js lt-ie9 lt-ie8"><![endif]-->
<!--[if IE 8]><html class="no-js lt-ie9"><![endif]-->
<!--[if gt IE 8]><!--><html class="no-js"><!--<![endif]-->
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<title>Selectize.js Demo</title>
		<meta name="description" content="">
		<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">
		<link rel="stylesheet" href="css/normalize.css">
		<link rel="stylesheet" href="css/stylesheet.css">
		<!--[if IE 8]><script src="js/es5.js"></script><![endif]-->
		<script src="js/jquery.min.js"></script>
		<script src="../dist/js/standalone/selectize.js"></script>
		<script src="js/index.js"></script>
	</head>
    <body>
		<div id="wrapper">
			<h1>Selectize.js</h1>
			<div class="demo">
				<h2>Locking</h2>
				<p>Selectize controls can be locked to prevent user interaction.</p>
				<div class="control-group">
					<label for="select-locked-empty">Locked (empty):</label>
					<select id="select-locked-empty" multiple placeholder="No input allowed...">
						<option value="A">Option A</option>
						<option value="B">Option B</option>
						<option value="C">Option C</option>
					</select>
				</div>
				<div class="control-group">
					<label for="select-locked-single">Locked (single):</label>
					<select id="select-locked-single" placeholder="No input allowed...">
						<option value="A">Option A</option>
						<option value="B" selected>Option B</option>
						<option value="C">Option C</option>
					</select>
				</div>
				<div class="control-group">
					<label for="select-locked">Locked:</label>
					<select id="select-locked" multiple placeholder="No input allowed...">
						<option value="A">Option A</option>
						<option value="B" selected>Option B</option>
						<option value="C" selected>Option C</option>
					</select>
				</div>
				<div class="control-group">
					<label for="select-unlocked">Unlocked:</label>
					<select id="select-unlocked" multiple placeholder="Input allowed...">
						<option value="A">Option A</option>
						<option value="B">Option B</option>
						<option value="C">Option C</option>
					</select>
				</div>
				<script>
				$('select').selectize({create: true});
				$('#select-locked-empty')[0].selectize.lock();
				$('#select-locked-single')[0].selectize.lock();
				$('#select-locked')[0].selectize.lock();
				</script>
			</div>
		</div>
	</body>
</html>
