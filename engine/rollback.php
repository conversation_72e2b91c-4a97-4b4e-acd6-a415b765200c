<?php

include("admin/inc/lib.inc.php");

$dbh = get_dbh();

// POST means form was submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $rollback_to = $_POST['rollback_to'];
    $group_key = $_POST['group_key'];
    $table = $_POST['table'];

    $sth = $dbh->prepare("DELETE FROM $table WHERE group_key = :group_key
        AND date > :rollback_to");
    $sth->execute(array(":group_key" => $group_key, ":rollback_to" => $rollback_to));

    ?>
    <p>Rollback completed.</p>
    <?php
}

$table = $_REQUEST['table'];
$group_key = $_REQUEST['group_key'];

$sth = $dbh->prepare("SELECT date FROM $table WHERE group_key = ? ORDER BY date DESC");
$sth->execute(array($group_key));

?>
<form action='#' method='post'>
    <input type='hidden' name='table' value='<?= $table ?>'/>
    <input type='hidden' name='group_key' value='<?= $group_key ?>'/>
    <select name='rollback_to'>
        <?php
        while ($date = $sth->fetchColumn()) {
            ?>
            <option value='<?= $date ?>'><?= $date ?></option>
            <?php
        }
        ?>
    </select>
    <button type="submit">Rollback</button>
</form>
