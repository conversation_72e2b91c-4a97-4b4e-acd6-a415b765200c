<?php

$core_student_current_stage = pull_field('core_students', 'db30487', "WHERE id = $_SESSION[student_id] AND usergroup='$_SESSION[usergroup]' ");
$ref_count_msg = '';
$studentsModel = new Students;
$campaign_id = $studentsModel->get_applicant_navigate_page($applicant_info, true);
dev_debug("applicant_info :" . json_encode($applicant_info));
dev_debug("campaign_id:" . $campaign_id);
if (!empty($core_student_current_stage)) {
	$core_student_current_stage = "'{$core_student_current_stage}'";
}
$page_ids = get_next_applicant_stage_pages($core_student_current_stage, $campaign_id);//

if ($page_ids == '') {
	$page_ids = get_page_list_based_on_rules($_SESSION['student_id']);
}

$missingData = get_missing_data_for_new_forms($_SESSION['student_id'], 'yes', $page_ids, 'yes');

if (!empty($_POST['second_step'])) {
	//update checklist stage
	$uname = $unique_id;//username_id
	$uname_id = $_SESSION['student_id'];//id
	$student_link_url = engine_url . "/direct/proc?pg=4&vw=$uname&ref=$uname_id";
	$dbh = get_dbh();
	$stmt = $dbh->prepare("UPDATE chk_z_107_post_doc_checklist SET db310546='on' WHERE rel_id=:rel_id");
	$stmt->execute(['rel_id' => $uname_id]);
	
	//insert into stage tracker
	$username_id = random();
	$user_id = $_SESSION['uid'];
	$usergroup = $_SESSION['usergroup'];
	//INSERT RECORD INTO STAGE TRACKER
	$insert_record_sql = "INSERT INTO dir_stage_tracker (username_id, rec_id, usergroup ,db1142, rel_id)
        VALUES ('{$username_id}','{$user_id}', '{$usergroup}','db310546', {$uname_id})
        ";
	dev_debug($insert_record_sql);
	$insert_record_ = $dbh->prepare($insert_record_sql);
	$insert_record_->execute();
	
	//update the student stage
	$db41_value = pull_field('system_table', 'figures', "where db_field_name = 'db310546'");
	
	$update_student_sql = "UPDATE core_students SET db30487 ='db310546', db41='{$db41_value}' WHERE id ={$uname_id} AND usergroup='{$usergroup}'";
	dev_debug($update_student_sql);
	$stmt = $dbh->prepare($update_student_sql);
	$stmt->execute();
 
	//send email to admin
	$message_plain =
		"Dear Admissions,
        Applicant Email:$_SESSION[user].
        URL to application: $student_link_url
        The applicant has submitted their application .
        ";
	$message_html = text_to_html($message_plain);
	$emailFrom = master_email;
	$emailTo = pull_field("form_schools", "db1118", "WHERE id='$_SESSION[usergroup]'");//Main
	
	track_use("$emailTo, $message_plain, $message_html, $emailFrom,");
	
	log_email($emailTo, "Application Submission", $message_plain, $message_html, $emailFrom, "Application Submission");
	echo "<strong><br/><br/>Thank you.<br/>
    Your application has been submitted successfully.<br/><br/>
    <p><a href=\"$website_url_applicant/application/Checklist\" class=\"btn btn-info\">Go back to my account >> </a></p>
    </strong>
    ";
}


$checkForSubmission = pull_field("chk_z_107_post_doc_checklist", "db310546", "WHERE rel_id='$_SESSION[student_id]'") == 'on' ? true : false;
if (empty($checkForSubmission)) {
	$disabled = '';
	if (!empty($missingData)) {
		$disabled = "disabled";
		?>
        <div class="text-danger">
            Sorry you <b>CANNOT</b> submit as you have not yet filled in all the required fields..<br/><br/>
            <div class="alert alert-danger">
				<?php
				echo "<h4>Incomplete questions</h4><p>Please go back into the form to complete them</p>";
				get_missing_data_for_new_forms($_SESSION['student_id'], 'yes', $page_ids);
				?>
            </div>
        </div>
	<?php } ?>
	
	<?php if($applicant_info['db889'] == '9305') {
		
		//POST DOC On Post Doc they must give us 1 Primary Mentor and 1 Secondary Mentor.  Primary_Mentor_(Post_Doctoral_Fellowship_only) Secondary_Mentor_(Post_Doctoral_Fellowship_only)
		$count_primary_ref = pull_field("dir_reference", "count(*)", "WHERE usergroup='$_SESSION[usergroup]' AND rel_id='$_SESSION[student_id]' AND db11360 = 'Primary_Mentor_(Post_Doctoral_Fellowship_only)' AND (rec_archive IS NULL OR rec_archive = '')");
		if ($count_primary_ref < 1) {
			$show_button = 7;
			$ref_count_msg .= '<br><div class="alert alert-danger">
            <strong>Sorry!</strong>
            <p>Please add one Primary Mentor <a href = "https://senss.'.env('APP_URL').'/application/mentorship_team">here</a> before you can submit!</p>
        </div>';
		}
		$count_secondary_ref = pull_field("dir_reference", "count(*)", "WHERE usergroup='$_SESSION[usergroup]' AND rel_id='$_SESSION[student_id]' AND db11360 = 'Secondary_Mentor_(Post_Doctoral_Fellowship_only)' AND (rec_archive IS NULL OR rec_archive = '')");
		if ($count_secondary_ref < 1) {
            $show_button = 7;
            $ref_count_msg .= '<br><div class="alert alert-danger">
            <strong>Sorry!</strong>
            <p>Please add one Secondary Mentor <a href = "https://senss.' . env('APP_URL') . '/application/mentorship_team">here</a> before you can submit!</p>
        </div>';
        }

        $count_ref = pull_field("dir_reference", "count(*)", "WHERE usergroup='$_SESSION[usergroup]' AND rel_id='$_SESSION[student_id]' AND db11360 = 'Referee_(Post_Doctoral_Fellowship_only)' AND (rec_archive IS NULL OR rec_archive = '')");
		if ($count_ref < 1) {
			$show_button = 7;
			$ref_count_msg .= '<br><div class="alert alert-danger">
            <strong>Sorry!</strong>
            <p>Please add one Referee <a href = "https://senss.'.env('APP_URL').'/application/mentorship_team">here</a> before you can submit!</p>
        </div>';
		}

        $count_department_ref = pull_field("dir_reference", "count(*)", "WHERE usergroup='$_SESSION[usergroup]' AND rel_id='$_SESSION[student_id]' AND db11360 = 'Head_of_Department_(Post_Doctoral_Fellowship_only)' AND (rec_archive IS NULL OR rec_archive = '')");
        if ($count_department_ref < 1) {
            $show_button = 7;
            $ref_count_msg .= '<br><div class="alert alert-danger">
            <strong>Sorry!</strong>
            <p>Please add one Head of Department <a href = "https://senss.'.env('APP_URL').'/application/mentorship_team">here</a>, before you can submit!</p>
        </div>';
        }

		if (!empty($ref_count_msg)){
			$disabled = "disabled";
			echo $ref_count_msg;
		}
		
		$form_count = pull_field("core_submissions", "count(*)", "WHERE db33770 IN (3989) AND rel_id='{$_SESSION['student_id']}' AND (rec_archive IS NULL OR rec_archive = '')");
		if ($form_count < 1) {
			$disabled = "disabled";
			
			$show_button = 5;
			$equal_form_msg = '<br><div class="alert alert-danger">
            <strong>Sorry!</strong>
            <p>Please fill out <a href ="https://senss.'.env('APP_URL').'/application/equal_opportunities_senss?chld_view=' . encode($_SESSION['student_id']) . '">Equal Opportunities Form</a> before you can submit!</p>
            </div>';
            
            echo $equal_form_msg;
		}
        
        
        //check for uploads
//		632 	Justification of resources
//        2296 	CV (again)
        
        $justificationUploaded =  pull_field('form_file', 'id', "WHERE rel_id={$_SESSION['student_id']} AND (rec_archive is null or rec_archive= '') and db200=632 and (db202 is null or db202= '')");
        if(empty($justificationUploaded)){
	        $disabled = "disabled";
	        
	        $show_button = 5;
	        $justificationErrorMessage = '<br><div class="alert alert-danger">
            <strong> Missing Upload!</strong>
            <p>
                Please click
                <a href ="https://senss.'.env('APP_URL').'/application/media_upload?chld_view=' . encode($_SESSION['student_id']) . '">here</a>,
                to upload a file and select <b>Justification of resources on Description of File Content </b>.
            </p>
            </div>';
	        
	        echo $justificationErrorMessage;
        }
		
		$cvUploaded =  pull_field('form_file', 'id', "WHERE rel_id={$_SESSION['student_id']} AND (rec_archive is null or rec_archive= '') and db200=2296 and (db202 is null or db202= '')");
		if(empty($cvUploaded)){
			$disabled = "disabled";
			
			$show_button = 5;
			$cvErrorMessage = '<br><div class="alert alert-danger">
            <strong> Missing Upload!</strong>
            <p>
                Please click
                <a href ="https://senss.'.env('APP_URL').'/application/media_upload?chld_view=' . encode($_SESSION['student_id']) . '">here</a>,
                to upload a file and select <b>CV (again) on Description of File Content </b>.
            </p>
            </div>';
			
			echo $cvErrorMessage;
		}
 
	}
	?>

    <form action="" method="post">
        <input <?php echo $disabled ?> type="Submit" name="Submit" id="button" class="btn btn-success btn-lg" value="Submit" onclick="return appliSubmit()">
	    <?php if(empty($disabled)) { ?>
            <input name="second_step" type="hidden" value="1"/>
	    <?php } ?>
    </form>
    <script>
        function appliSubmit() {
            return confirm("Are you sure you are ready to submit? \nOnce you submit, you will not be able to edit the forms any further.");
        }

    </script>
	<?php
} elseif ($checkForSubmission && empty($_POST['second_step'])) {
	echo '<div class="alert alert-danger"><h2>'.terminology("You have already submitted your application", $_SESSION['url'], 'Next Stage Submit Page', true).'</h2></div>';
	
}
