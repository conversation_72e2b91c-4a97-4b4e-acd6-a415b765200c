<?php 
global $db;
$usergroup = $_SESSION['usergroup'];
// get all student bookings
$check_sql = "select * from core_courses where usergroup={$usergroup} and db235='public' and db340='on'";
dev_debug("### GOT TO CUSTOM FILE ###");

dev_debug($check_sql);
$res_courses = $db->query($check_sql);

 ?>
<h3 class="text-center">Select a competition</h3>

<?php 

foreach ($res_courses as $re_key => $competition) {?>

<div class="col-xs-12 col-sm-6 col-lg-6">
<div class="box">
<div class="icon">
<h3 class="title"><?php echo $competition['db232'] ?></h3>

<div class="more"><a class="btn btn-success btn-block" href="https://senss.heiapply.com/<?php if ($competition['db231']=='stup102') { ?>application/collaborative_student_phase_intakes<?php }else{ ?>static/senss/registration_redirect.php?cs=<?php echo $competition['db231']  ?> <?php } ?>" title="Direct Applications">Apply Now</a></div>
</div>
</div>
</div>


 <?php } ?>
 