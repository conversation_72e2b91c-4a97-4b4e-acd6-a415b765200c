<?php
session_start();

include("../admin/inc/lib.inc.php");
chk_login(); //check if user is logged in
//submit 

if ($_POST['ref'] == 15078) {
    print_r($_POST);
}

if ($_POST['pg'] && $_POST['vw'] && $_POST['ref']) {
    $remove_redirect = 1;//this will remove the redirect
    dev_debug("$_POST[new_message] -  $_POST[vw] - $_POST[ref]");
    include(base_path."/engine/modules/inc_dir_messages_process.php");
    $success = 1;
}
?>
<link href='https://fonts.googleapis.com/css?family=Roboto:400,100,100italic,300,300italic,400italic,500,500italic,700,700italic,900,900italic'
      rel='stylesheet' type='text/css'>
<!-- Bootstrap core CSS -->
<link href="<?php echo engine_url; ?>/tools/bootstrap/css/bootstrap.min.css" rel="stylesheet">
<link href="<?php echo engine_url; ?>/tools/bootstrap/css/bootstrap-theme.min.css" rel="stylesheet">

<!-- Origional Styles -->
<link href="<?php echo engine_url; ?>/css/styles.css" rel="stylesheet" type="text/css"/>
<script src="<?php echo engine_url; ?>/js/jquery-1.9.1.min.js" type="text/javascript" charset="utf-8"></script>
<!-- TinyMCE -->
<script type="text/javascript" src="<?php echo engine_url; ?>/tools/tinymce4.3/tinymce.min.js"></script>
<script type="text/javascript" src="<?php echo engine_url; ?>/tools/tinymce4.3/tinymce_script.js"></script>
<!-- /TinyMCE -->

<!-- HIDE PROCESSING MAGE AFTER A FEW SECS-->
<script>
    setTimeout(function () {
        $('.entry_success').fadeOut('fast');
    }, 2500); // <-- time in milliseconds
    })
    ;
</script>

<?php if ($success == 1) { ?>
    <div class="alert alert-success"><b>Message Added Successfully</b></div>
<?php } ?>

<br/>
<div class="cm_chat_box">
    <form action="" class="form" method="POST">
        <textarea class="tinymce" placeholder="Write a message..." id="new_message" name="new_message"
                  style="width:80%"></textarea>
        <div class="pull-right" style="margin-top:10px">
            <input name="send_email" type="checkbox" value="yes" checked="checked">Send New Message Alert To Applicant?
            <input type="hidden" name="pg" value="<?php echo $_GET['pg']; ?>">
            <input type="hidden" name="vw" value="<?php echo $_GET['vw']; ?>">
            <input type="hidden" name="ref" value="<?php echo $_GET['ref']; ?>">
            <button type="submit" class="btn btn-success"> Send Message</button>
        </div>
    </form>
</div>