<?php
	$core_student_current_stage = pull_field('core_students', 'db30487', "WHERE id = $_SESSION[student_id] AND usergroup='$_SESSION[usergroup]' ");
	$studentsModel = new Students;
	$campaign_id = $studentsModel->get_applicant_navigate_page($applicant_info, true);
	dev_debug("applicant_info :" . json_encode($applicant_info));
	dev_debug("campaign_id:" . $campaign_id);
	if (!empty($core_student_current_stage)) {
		$core_student_current_stage = "'{$core_student_current_stage}'";
	}
	$page_ids = get_next_applicant_stage_pages($core_student_current_stage, $campaign_id);//
	
	if ($page_ids == '') {
		$page_ids = get_page_list_based_on_rules($_SESSION['student_id']);
	}
	
	$missingData = get_missing_data_for_new_forms($_SESSION['student_id'], 'yes', $page_ids, 'yes');
	
	if (!empty($_POST['second_step'])) {
    //update checklist stage
    $uname = $unique_id;//username_id
    $uname_id = $_SESSION['student_id'];//id
    $student_link_url = engine_url . "/direct/proc?pg=4&vw=$uname&ref=$uname_id";
    $dbh = get_dbh();
    $stmt = $dbh->prepare("UPDATE chk_z_389_default_checklist SET db278003='on' WHERE rel_id=:rel_id");
    $stmt->execute(['rel_id' => $uname_id]);
	
	//insert into stage tracker
	$username_id = random();
	$user_id = $_SESSION['uid'];
	$usergroup = $_SESSION['usergroup'];
	//INSERT RECORD INTO STAGE TRACKER
	$insert_record_sql = "INSERT INTO dir_stage_tracker (username_id, rec_id, usergroup ,db1142, rel_id)
        VALUES ('{$username_id}','{$user_id}', '{$usergroup}','db278003', {$uname_id})
        ";
	dev_debug($insert_record_sql);
	$insert_record_ = $dbh->prepare($insert_record_sql);
	$insert_record_->execute();
	
	//update the student stage
	$db41_value = pull_field('system_table', 'figures', "where db_field_name = 'db278003'");
	
	$update_student_sql = "UPDATE core_students SET db30487 ='db278003', db41='{$db41_value}' WHERE id ={$uname_id} AND usergroup='{$usergroup}'";
	dev_debug($update_student_sql);
	$stmt = $dbh->prepare($update_student_sql);
	$stmt->execute();
    //send email to admin
    $message_plain =
        "Dear Admissions, 
				Applicant Email:$_SESSION[user].  
				URL to application: $student_link_url
				The applicant has submitted their application and has consented.";
    $message_html = text_to_html($message_plain);
    $emailFrom = master_email;
    $emailTo = pull_field("form_schools", "db1118", "WHERE id='$_SESSION[usergroup]'");//Main

    track_use("$emailTo, $message_plain, $message_html, $emailFrom,");

    log_email($emailTo, "Application Submission", $message_plain, $message_html, $emailFrom, "New Consent Admission Alert");
    echo "<strong><br/><br/>Thank you.<br/>
Your submission has been sent successfully.<br/><br/>
<p><a href=\"$website_url_applicant/application/Checklist\" class=\"btn btn-info\">Go back to my account >> </a></p>
		</strong>";
}
$checkForSubmission = pull_field("chk_z_389_default_checklist", "db278003", "WHERE rel_id='$_SESSION[student_id]'") == 'on' ? true : false;
if (!$checkForSubmission) {
	$disabled = '';
	if (!empty($missingData)) {
		$disabled = "disabled";
		?>
        <div class="text-danger">
            Sorry you <b>CANNOT</b> submit as you have not yet filled in all the required fields.<br/><br/>
            <div class="alert alert-danger">
				<?php
					echo "<h4>Incomplete questions</h4><p>Please go back into the form to complete them</p>";
					get_missing_data_for_new_forms($_SESSION['student_id'], 'yes', $page_ids);
				?>
            </div>
        </div>
	<?php } ?>
    
    <form action="" method="post">
        <input <?php echo $disabled ?> type="Submit" name="Submit" id="button" class="btn btn-success btn-lg" value="Submit"
               onclick="return appliSubmit()">
        <?php if(empty($disabled)) { ?>
            <input name="second_step" type="hidden" value="1"/>
        <?php } ?>
    </form>
    <script>
        function appliSubmit() {
            var agree = confirm("Are you sure you are ready to submit? \nOnce you submit, you will not be able to edit the forms any further. We may ask you to upload further files to support your submission, which you will be able to do.");
            if (agree)
                return true;
            else
                return false;
        }

    </script>
    <?php
} elseif ($checkForSubmission && empty($_POST['second_step'])) {
    echo '<div class="alert alert-danger"><h2>You have already submitted.</h2></div>';

}
