<?php
session_start();
include("../admin/inc/lib.inc.php");
chk_login(); //check if user is logged in

//--------------------------------------------------------------------------
// 1) Connect to mysql database
//--------------------------------------------------------------------------

$dbh = get_dbh();
//--------------------------------------------------------------------------
// 2) Query database for data
//--------------------------------------------------------------------------
$employer_id = $_GET['employer_id'];
$status = $_GET['newStage'];
$db27511 = $status;
$sth = $dbh->prepare("update emp_employers set db27511 = '$db27511' where id = ?");
$sth->execute(array($employer_id));

$sth = $dbh->prepare("INSERT INTO emp_employer_status_track (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id, db27512) VALUES (?,?,?,?,?,?,?)");
$sth->execute(array(random(), session_info("uid"), session_info("usergroup"), $employer_id, custom_date_and_time(), session_info("uid"), $db27511));


print json_encode($sth);
