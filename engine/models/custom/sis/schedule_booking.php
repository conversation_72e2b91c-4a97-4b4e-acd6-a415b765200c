<?php
namespace sis_schedule_booking;
use Doctrine\DBAL\DBALException;
use Doctrine\DBAL\Driver\Exception;
use PDO;

https://nsft-nhs.heiapply.com/submit_reqol.php?vw=Mon320802-S2&ref=1034003&rrid=Mon320802-D6
//POST CREATE
function insert_shortcourse_booking_details ($args, $static, $mappings) {
	
	if ($args['db16135']!== '') {
		///usergroup = (MRN) is using booking status and usergroups like 82(SHA) are not using booking status on this form
		///
		$school_type = pull_field('form_schools', 'db30', "WHERE id =".$_SESSION[usergroup]."");
		if($school_type=='12') {///if mrn school
			$booking_status = $args['db14983'];
		}else{
			$booking_status = '';
		}


     list($core_students_id,$core_students_rec_id,$core_students_usergroup,$core_students_rel_id,$core_students_first_name,$core_students_middle_name,$core_students_surname,$core_students_email_address,$core_students_telephone_number,$core_students_date_of_birth,$core_students_gender,$core_students_source_of_applicant,$core_students_cohort,$core_students_course_of_study,$core_students_level_of_entry,$core_students_country_of_origin,$core_students_application_status,$core_students_has_applied,$core_students_archive_record,$unique_id,$core_student_application_route,$core_students_cohort_intake,$core_students_deleted,$core_student_application_ucas,$core_student_ucas_pass,$core_students_course_of_study_id,$core_students_course_audio,$core_students_length_of_study,$core_students_mobile_number)=get_core_students($args['db16135']);// if second value is defined then use it
		
		$dbh = get_dbh();
			$sql = "INSERT INTO sis_sched_booking_detail 
			(username_id, rec_id, usergroup,rel_id,db15054,db15055,db15056,db15057,db15058,db15059,db15073,db15052,db59978) 
			VALUES ('".random()."', '" . session_info("uid") . "', '" . session_info("access") . "', :rel_id,:fname,:surname,:email,:gender,:dob,:country,:tel,:booking_id,:booking_status)";
			$sth = $dbh->prepare($sql);
			$sth->execute(array(
				'rel_id' => $core_students_id, //$args['id'],//recently added id
				'fname' => $core_students_first_name,
				'surname' => $core_students_surname ,
				'email' => $core_students_email_address,
				'gender' => $core_students_gender ,
				'dob' => $core_students_date_of_birth ,
				'country' => $core_students_country_of_origin ,
				'tel' => $core_students_telephone_number,
				'booking_id'=>$args['id'],
				'booking_status' => $booking_status
			));
			/*echo "Applicant Details Added $sql";
			echo "rel_id => $_GET[ref],
				fname=> $core_students_first_name,
				surname=> $core_students_surname ,
				email=> $core_students_email_address,
				gender=> $core_students_gender ,
				dob=> $core_students_date_of_birth ,
				country=> $core_students_country_of_origin ,
				tel=> $core_students_telephone_number";*/

	}
	return array($args, $mappings);

}

//POST CREATE
function update_the_rel_id ($args, $static, $mappings) {
	
	
	if ($args['rel_id'] == '') {
		$dbh = get_dbh();

		$new_booking_rel_id = pull_field("sis_scheduled_booking","db16135","WHERE username_id='$args[username_id]'");
		// update the sis record with the new id
		$sth = $dbh->prepare("UPDATE sis_scheduled_booking SET rel_id=? WHERE username_id=? AND usergroup=?");
		$sth->execute(array($new_booking_rel_id,$args['username_id'],session_info("access")));
	}
	return array($args, $mappings);

}

//POST CREATE POST UPDATE
function send_shortcourse_booking_confirmation ($args, $static, $mappings) {
//error_log("ANITA IN send_shortcourse_booking_confirmation"  );
	//See if this is a shortcourse rather than an event
	//If it is a shortcourse then send out email for booking
	//check if resend is requested
	if($_SESSION["user"]=="<EMAIL>"){
		//print_r($args);
	}
	if ($args['db16136']!== '') {
		//shortcourse
//error_log("ANITA IN send_shortcourse_booking_confirmation db16136 is not blank " .$args['db16136'] );
	//echo "1 > ";

		if ($args['db14979'] > 1 || $args['db19380'] == 'yes') {
//error_log("ANITA IN send_shortcourse_booking_confirmation db19380 is yes ".$args['db19380']  );
			//echo "2 > ";
			
			$dbh = get_dbh();
			$sql = $dbh->prepare("SELECT db14961 as 'event_name', db19828 as 'email_template', 
			DATE_FORMAT(db14947,'%d/%m/%Y') as 'start_date',
			db14948 as 'start_time', 
			DATE_FORMAT(db14949,'%d/%m/%Y') as 'end_date',
			db14950 as 'end_time', 
			(SELECT CONCAT(db14963,'<br/>',db14965,'<br/>',db14966) FROM sis_course_venues WHERE id = db14954) as 'venue',db14956 as 'info' FROM sis_course_schedule WHERE id=$args[db14977]");				

			$sql->execute();
			$event_infos = $sql->fetchAll(\PDO::FETCH_OBJ);
			$event_info = $event_infos[0];
			$event_name = $event_info->event_name;
			$email_template = $event_info->email_template;
			$event_start_date = $event_info->start_date;
			$event_start_time = $event_info->start_time;
			$event_start_time = substr($event_start_time,0,2 ).':'.substr($event_start_time,2,2);
			$event_end_date = $event_info->end_date;
			$event_end_time = $event_info->end_time;
			$event_end_time = substr($event_end_time,0,2 ).':'.substr($event_end_time,2,2);
			$event_venue = $event_info->venue;
			$additional_info = $event_info->info;
			if($_SESSION["user"]=="<EMAIL>"){
				//print_r($event_info);
			}
//echo " a =".$email_template;

			if ($email_template !=null &&  $email_template != '') {
//error_log("ANITA IN send_shortcourse_booking_confirmation email template is not null " .$email_template );
				list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template($email_template);
				list($core_students_id, $core_students_rec_id, $core_students_usergroup, $core_students_rel_id, $core_students_first_name, $core_students_middle_name, $core_students_surname, $core_students_email_address, $core_students_telephone_number, $core_students_date_of_birth, $core_students_gender, $core_students_source_of_applicant, $core_students_cohort, $core_students_course_of_study) = get_core_students($args['db16135']);

				$confirmation_number = $event_info->event_name.'/'.$core_students_id;
				$email_name = $args['db14961'].$event_start_date.$event_start_time;
				$current_state = pull_field("form_email_log", "count(*)", "WHERE db1153='$core_students_email_address' AND usergroup='$_SESSION[usergroup]' AND db1152='$email_name'");
				
			//	echo " b =".$current_state;
//error_log("ANITA IN send_shortcourse_booking_confirmation current state " . $current_state."**".$args['db26213']."**" );

				//current state runs a select and counts all the fields where the email and usergroup are the same and that they have received an email with a particular category group
				if ($current_state == 0 || $args['db26213']== 'yes') {//checks to see if they haven't received an email so current state would be zero, if this is a re-send then send anyway
				
			//	echo "3 > ";
			
				
					$message_html = $coms_template_html_version;
					$message_plain = $coms_template_plain_text_version;

					$message_html = str_replace('{{short_course}}', $core_students_course_of_study, $message_html);
					$message_plain = str_replace('{{short_course}}', $core_students_course_of_study, $message_plain);

					$message_html = str_replace('{{Applicant_1}}', $core_students_first_name . ' ' . $core_students_surname, $message_html);
					$message_plain = str_replace('{{Applicant_1}}', $core_students_first_name . ' ' . $core_students_surname, $message_plain);
					$message_html = str_replace('{{child_name}}', $core_students_first_name, $message_html);
					$message_plain = str_replace('{{child_name}}', $core_students_first_name, $message_plain);	

					$message_html = str_replace('{{event_name}}', $event_name, $message_html);
					$message_plain = str_replace('{{event_name}}', $event_name, $message_plain);
					
					$message_html = str_replace('{{start_date}}', $event_start_date, $message_html);
					$message_plain = str_replace('{{start_date}}', $event_start_date, $message_plain);
					$message_html = str_replace('{{start_time}}', $event_start_time, $message_html);
					$message_plain = str_replace('{{start_time}}', $event_start_time, $message_plain);
					
					$message_html = str_replace('{{end_date}}', $event_end_date, $message_html);
					$message_plain = str_replace('{{end_date}}', $event_end_date, $message_plain);
					$message_html = str_replace('{{end_time}}', $event_end_time, $message_html);
					$message_plain = str_replace('{{end_time}}', $event_end_time, $message_plain);
					
					$message_html = str_replace('{{course venue}}', $event_venue, $message_html);
					$message_plain = str_replace('{{course venue}}', $event_venue, $message_plain);
					$message_html = str_replace('{{additional_information}}', $additional_info, $message_html);
					$message_plain = str_replace('{{additional_information}}', $additional_info, $message_plain);
					
					$message_html = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_html);
					$message_plain = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_plain);
					$message_html = str_replace('{{course_venue}}', $event_venue, $message_html);
					$message_plain = str_replace('{{course_venue}}', $event_venue, $message_plain);

					// $message_html = text_to_html($message_plain);
					$emailTo = $core_students_email_address;// student email
					$message_subject = $coms_template_subject_line . ' - '. $core_students_course_of_study.' '. $event_start_date;//access the subject from the template, this goes straight into the log_email function

					if ($coms_template_email_address_to_send_from && $coms_template_email_address_to_send_from != '') {
						$emailFrom = $coms_template_email_address_to_send_from;
					} else {
						$emailFrom = master_email;
					}

					log_email($emailTo, $message_subject, $message_plain, $message_html, $emailFrom, $email_name, $core_students_id);

				}
			}
		}
	}

//reset the resend field
$args['db26213'] = 'no';
//echo "4 > ";

	return array($args, $mappings);

}

// PRE_INSERT PRE_UPDATE
function booking_setup($args, $static, $mappings) {

	if( !empty($static['ug']) && is_array($static['ug']) ){
		if( !in_array( $_SESSION['usergroup'], $static['ug'] ) ) return array($args, $mappings);
	}
	else if( !empty($static['ug']) && $static['ug'] != $_SESSION['usergroup'] ) return array($args, $mappings);
	else return array($args, $mappings);

$origional_val = $args['db14977'];
$origional_val2 = $args['db14978'];
//if ($_SESSION['usergroup']=='21') {
	//$args['db14977'] = $_POST['db14978']; // get the select scheduled course
//}
	$include_VAT = pull_field('sis_course_schedule','db15434',"WHERE id=$args[db14977]");

	$db14978_scheduled_course_start_date = pull_field('sis_course_schedule','db14947',"WHERE id=$args[db14977]");
	$args['db16136'] = pull_field('sis_course_schedule','db14946',"WHERE id=$args[db14977]");
	$args['db14982'] = pull_field('core_courses','db232',"WHERE id=$args[db16136]");
	if (!$args['db14979'] || $args['db14979'] =='') {
		$args['db14979'] = "1";
	}


	// if they change course update the  core student record
	dev_debug("Got here ".$origional_val.'='.$origional_val2);
	if($origional_val2!==$origional_val){

			$dbh = get_dbh();
			// update the booking with the invoice number db16135
			$sql_a = "UPDATE core_students SET db889 = '".$args['db16136']."' WHERE usergroup='$_SESSION[usergroup]' AND id = '".$args['db16135']."' " ;
			dev_debug("Student record update = ".$sql_a);
			$sth = $dbh->prepare($sql_a);
			$sth->execute();
	}

	$price_per_applicant = pull_field('sis_course_schedule','db14951',"WHERE id=$args[db14977]");
	$VAT = pull_field('lead_preferences','db15030',"WHERE usergroup=$_SESSION[usergroup] LIMIT 1");

	//error_log("price_per_applicant".$price_per_applicant."**".$pdo_preferences['db15030']);
	if ($include_VAT =="yes") {
		$args['db15104'] = $price_per_applicant * (1 + ($VAT / 100));
	}
	else {
		$args['db15104'] = $price_per_applicant;
	}
	$args['db15111'] = pull_field('sis_course_schedule','db15110',"WHERE id=$args[db14977]");
	$args['db15871'] = pull_field('sis_course_schedule','db15432',"WHERE id=$args[db14977]");

	//set the temp payment text box
	$args['db14985'] = $args['db14984'];

	//set the temp event text box
	$args['db19985'] = $args['db14977'];

//start date
	$args['db14978'] = pull_field('sis_course_schedule','db14947',"WHERE id=$args[db14977]");


	//See if this is an event rather than a short course
	//If it is an event then check if all the places have been filled
	if ($args['db16136'] == null || $args['db16136'] == '') {
		//event

		$record = $args['record'];

		//only do a check if event status is booked or confirmed booked
		$check_needed = 0;


		if (isset($record) && $record !='') {
			//check if previous is a booking ot confirmed booking
			$booking_status = pull_field('sis_scheduled_booking','db14983',"WHERE id=$record");
			if ($booking_status!= $args['db14983']) {
				//there is a change in booking status
				if ($args['db14983'] == 2 || $args['db14983'] ==3)  {
					$check_needed = 1;
					if ($booking_status == 2 || $booking_status == 3)  {
						$check_needed = 0;
					}
				}
				else {
					$check_needed = 0;
				}

			}
		}
		else {
			if ($args['db16135']== null ||  $args['db16135']== '') {
				$args['db16135'] = floating_info('ref');
			};
			if ($args['db14983'] == 2 || $args['db14983'] ==3) {
				$check_needed = 1;
			}
		}

		if ($check_needed == 1) {
			//bookings left
			$bookings_left = pull_field('sis_course_schedule', 'db14952 - (SELECT COUNT(*) FROM sis_scheduled_booking WHERE db14977 = sis_course_schedule.id AND (db14983= 2 or db14983= 3))', "WHERE id=$args[db14977]");

			if ($bookings_left <= 0) {
				//Sorry cannot be booked
				$args['error'] = 1;
				$args['error_msg'] = "This event is full.";
				$args['msg'] .= '<div class="error_alert_colour">Sorry! There are no more spaces on this event.</div>';
			}
		}

	}

	return array($args, $mappings);
}

// PRE_INSERT of payment.. if invoice isn't created, create it
function pre_payment_checks($args, $static, $mappings){
	$invoice_ref = $args['db1494'];
	if (!$invoice_ref || $invoice_ref=='') {
		$booking_ref = $args['db16995'];

		if ($booking_ref && $booking_ref != '') {
			//update the status to payment made
			$dbh = get_dbh();

			$scheduled_course_id_vals = explode("|", pull_field("sis_scheduled_booking", "concat_ws('|',db14977,db15104)", "WHERE id = '$booking_ref'"));
			$scheduled_course_id  = $scheduled_course_id_vals[0];
			$db15104  = $scheduled_course_id_vals[1];
			$sql = "SELECT * FROM sis_course_schedule WHERE id = $scheduled_course_id";

			$sth = $dbh->prepare($sql);
			$sth->execute();
			$scheduled_course = $sth->fetch(\PDO::FETCH_ASSOC);

			$sql = "SELECT *, DATE_FORMAT(db14978,'%d/%m/%Y') as 'start_date' FROM sis_scheduled_booking WHERE id = '$booking_ref'";
			$sth = $dbh->prepare($sql);
			$sth->execute();
			$scheduled_booking = $sth->fetch(\PDO::FETCH_ASSOC);

			//create the invoice-setting record
			$db14987_currency = $scheduled_course['db15435'];
			//$db14988_issue_date = now();
			//$db14989_due_date = $scheduled_course[''];
			$db14990_reminder_date = '';
			$db14991_office_address = pull_field('lead_company_office', 'id', "WHERE (rec_archive is NULL or rec_archive='') AND usergroup=$_SESSION[usergroup]");
			$db14992_recipient_type = 'Applicant';
			$db14993_recipient = $args['rel_id'];
			$db14995_payment_option = 'Bank Transfer,Cash,Cheque,Pay online';
			$db14996_number_of_installments_allowed = $scheduled_course['db15432'];
			$db14997_delivery_option = $scheduled_course['db15433'];
			$db14998_include_vat = $scheduled_course['db15434'];
			$db14999_discount_amount = '';
			$db15000_comment = '';
			$db15002_deposit = 'amount';
			$db15003_value = $scheduled_course['db15110'];
			$db15004_due_date = $scheduled_course[''];
			if($_SESSION['usergroup']==76){
				$db15005_status = 'draft';//one sets admin buttons on in invoice
			}else{
				$db15005_status = 'approved';//one sets admin buttons on in invoice
			}
			$db15006_display_total = $scheduled_course['db14951'];
			$db27442_intake = $scheduled_course['db20166'];
			/* payment_status="3"; //pay on invoice
            payment_status="4"; //deposit paid
            payment_status="5"; //installment paid
            payment_status="6"; //all paid
            */
			$inv_username_id = random();
            $db342693_vat_rate = pull_field("lead_preferences", "db15030", "WHERE usergroup=$_SESSION[usergroup] and (rec_archive IS NULL OR rec_archive = '')");

            if ($scheduled_booking['db14984'] == 'Pay deposit and instalments') {
				$sql = "INSERT INTO lead_invoice_settings (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id,db14987,db14988,db14989,db14990,db14991,db14992,db14993,db14995,db14996,db14997,db14998,db14999,db15000,db15002,db15003,db15004,db15005,db15006,db342693)VALUES('" . $inv_username_id . "', '" . session_info("uid") . "', '" . session_info("access") . "', '$args[rel_id]', CURRENT_TIMESTAMP(), '1', '$db14987_currency', CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP(),'$db14990_reminder_date','$db14991_office_address','$db14992_recipient_type','$db14993_recipient','$db14995_payment_option','$db14996_number_of_installments_allowed','$db14997_delivery_option','$db14998_include_vat','$db14999_discount_amount','$db15000_comment','$db15002_deposit','$db15003_value','$db15004_due_date','$db15005_status','$db15006_display_total','$db342693_vat_rate')";
			} else {
				$sql = "INSERT INTO lead_invoice_settings (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id,db14987,db14988,db14989,db14990,db14991,db14992,db14993,db14995,db14996,db14997,db14998,db14999,db15000,db15002,db15003,db15004,db15005,db15006,db342693)VALUES('" . $inv_username_id . "', '" . session_info("uid") . "', '" . session_info("access") . "', '$args[rel_id]', CURRENT_TIMESTAMP(), '1', '$db14987_currency', CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP(),'$db14990_reminder_date','$db14991_office_address','$db14992_recipient_type','$db14993_recipient','$db14995_payment_option','','$db14997_delivery_option','$db14998_include_vat','$db14999_discount_amount','$db15000_comment','','','$db15004_due_date','$db15005_status','$db15006_display_total','$db342693_vat_rate')";
			}
            
            
			$sth = $dbh->prepare($sql);
			$sth->execute();
			$invoice_settings_id = $dbh->lastInsertId();


			
			       //////////start					
				  //intake selected
					$invoice_prefix_generated = $db27442_intake; //code
					$invoice_prefix = $invoice_prefix_generated . '/'; //single prefix
					
					//else
					$invoice_number = $invoice_prefix . $invoice_settings_id;
					
					// update the invoice with the invoice number
					$sql3 = "UPDATE lead_invoice_settings SET db25617 = '$invoice_number' WHERE usergroup = '$_SESSION[usergroup]' AND id='$invoice_settings_id'";
					$sth = $dbh->prepare($sql3);
					$sth->execute();
					////////end
			 
			// update the booking with the invoice number
			if($db15006_display_total >=$db15104){
				$payment_status = "6";
			}else{
				$payment_status = "5";
			}
			// only execute when $invoice_settings_id is not zero
			if ($invoice_settings_id) {
				$sql = "UPDATE sis_scheduled_booking SET db14979 = '$payment_status', db15438 = '$invoice_settings_id' WHERE id = '$booking_ref'";

				$sth = $dbh->prepare($sql);
				$sth->execute();
			}
			

			//add the invoice item
			$db15016_title = $scheduled_booking['db14982'] . ' Fees - Start Date ' . $scheduled_booking['start_date'];
			$db15017_description = '';
			$db15018_category = 'course';
			$db15019_type = '1';
			$db15020_quantity = pull_field("sis_sched_booking_detail", "count(*)", "WHERE usergroup=" . session_info("access") . " AND (rec_archive is null or rec_archive ='') AND db15052 = $scheduled_booking[id]");
			$db15021_units = "Fees";
			$db15022_unit_price = $scheduled_course['db14951'];
			$db15024_course = $scheduled_course_id;
			$db15025_start_date = $scheduled_course['db14947'];
			$db15026_end_date = $scheduled_course['db14949'];
			$db20169_is_shortcourse = 'yes';

			$sql = "INSERT INTO lead_invoice_items (username_id,rec_id,usergroup,rel_id,rec_lstup,rec_lstup_id,db15016,db15017,db15018,db15019,db15020,db15021,db15022,db15024,db15025,db15026,db20169) VALUES('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '$invoice_settings_id', CURRENT_TIMESTAMP(), '1','$db15016_title','$db15017_description','$db15018_category','$db15019_type','$db15020_quantity','$db15021_units','$db15022_unit_price','$db15024_course','$db15025_start_date','$db15026_end_date','$db20169_is_shortcourse')";

			$sth = $dbh->prepare($sql);
			$sth->execute();
			$args['db1494'] = $inv_username_id;
		}
	}

	return array($args, $mappings);
}

//post insert of payment
function post_payment_checks($args, $static, $mappings){
	$invoice_ref = $args['db1494'];
	$booking_ref = $args['db16995'];
	$dbh = get_dbh();
	//get total paid for the invoice
	
    
   	if ($booking_ref && $booking_ref != '') {
		$scheduled_course_id_vals = explode("|", pull_field("sis_scheduled_booking", "concat_ws('|',db14977,db15104)", "WHERE id = '$booking_ref'"));
		$scheduled_course_id  = $scheduled_course_id_vals[0];
		$db15104  = $scheduled_course_id_vals[1];
        
        $invref=pull_field("sis_scheduled_booking","db15438","WHERE id = '$booking_ref'");
		$total_recieved_from_applicant=pull_field("sis_student_fees", "sum(db1495)", "WHERE db1494 = '$invref' AND db34450='Payment'");
	    $total_refunded_to_applicant=pull_field("sis_student_fees", "sum(db1495)", "WHERE db1494 = '$invref' AND db34450='Refund'");
		// update the booking with the invoice number
		if(!$total_refunded_to_applicant){
        	$total_refunded_to_applicant=0;
        }

		if(((int)$total_recieved_from_applicant-(int)$total_refunded_to_applicant) >=$db15104){
			$payment_status = "6";
		}else{
			$payment_status = "5";
		}
		$sql = "UPDATE sis_scheduled_booking SET db14979 = '$payment_status'  WHERE id = '$booking_ref'";

		$sth = $dbh->prepare($sql);
	    $sth->execute();
    }

    if ($invoice_ref && $invoice_ref != '') {
        $total_recieved_from_applicant=pull_field("sis_student_fees", "sum(db1495)", "WHERE db1494 = '$invoice_ref' AND db34450='Payment'");
	    $total_refunded_to_applicant=pull_field("sis_student_fees", "sum(db1495)", "WHERE db1494 = '$invoice_ref' AND db34450='Refund'");
        
        $inv_id=pull_field("lead_invoice_settings","id","WHERE username_id = '$invoice_ref'");
        $booking_ref=pull_field("sis_scheduled_booking","id","WHERE db15438 = '$inv_id'");
		$scheduled_course_id_vals = explode("|", pull_field("sis_scheduled_booking", "concat_ws('|',db14977,db15104)", "WHERE db15438 = '$inv_id'"));
		$scheduled_course_id  = $scheduled_course_id_vals[0];
		$db15104  = $scheduled_course_id_vals[1];
		$inv_id=pull_field("lead_invoice_settings","id","WHERE username_id = '$invoice_ref'");
        $booking_ref=pull_field("sis_scheduled_booking","id","WHERE db15438 = '$inv_id'");
		// update the booking with the invoice number
        if(!$total_refunded_to_applicant){
        	$total_refunded_to_applicant=0;
        }

		if(((int)$total_recieved_from_applicant-(int)$total_refunded_to_applicant) >=$db15104){
			$payment_status = "6";
		}else{
			$payment_status = "5";
		}
		$sql = "UPDATE sis_scheduled_booking SET db14979 = '$payment_status'  WHERE id = '$booking_ref'";
		$sth = $dbh->prepare($sql);
	    $sth->execute();
    }

    

 	return array($args, $mappings);
   
}
###we are doing a pre-insert so we can get the core_student_id and then send it as rel_id to sis_scheduled_booking
function create_core_student($args, $static, $mappings) {
    $school_type = pull_field('form_schools', 'db30', "WHERE id =".$_SESSION['usergroup']."");
    if($school_type=='12') {///if mrn school
//    	print_r($args);
        $profile_core_student_id = $args['rel_id'];
        ///now get the details of this student to add to core_student
        $dbh = get_dbh();
        $sql = $dbh->prepare("SELECT db48566,db48568,db48567,db48573,db48572,db48574,db48593,db48583,db48571,db48597 from sis_profiles where rel_id =" . $profile_core_student_id . "");
        $sql->execute();
        $student_infos = $sql->fetchAll(\PDO::FETCH_OBJ);
        $student_info = $student_infos[0];

        $_db39_first_name = $student_info->db48566;
        $_db40_surname = $student_info->db48568;
        $_db46_middle_name = $student_info->db48567;
        $_db41_application_status = 0;
        $_db44_gender = $student_info->db48573;
        $_db53_dob = $student_info->db48572;
        $_db510_source_applicant = $student_info->db48574;
        $_db889_first_course = $student_info->db48593;
        $_db764_email = $student_info->db48583;
        $_db765_telephone = $student_info->db48571;
        $_db28467_mobile = $student_info->db48597; 
        $db50 = pull_field("core_course_level", "id", "WHERE db16595='short' AND usergroup='$_SESSION[usergroup]' and (rec_archive IS NULL or rec_archive='')");

        $sql = "INSERT INTO core_students (username_id,rec_id,usergroup,rel_id,db39,db40,db41,db44,db46,db53,db510,db889,db764,db765,db28467,db50) 
				VALUES (:username_id,:rec_id,:usergroup,:rel_id, :db39_first_name,:db40_surname,:db41_application_status,:db44_gender,:db46_middle_name,:db53_dob,:db510_source_applicant,:db889_first_course,:db764_email,:db765_telephone,:db28467_mobile,:db50)";
//        echo "<pre>" . $sql . "</pre>";
        dev_debug($sql);
        $sql = $dbh->prepare($sql);
        $sql->execute(array(
			"username_id" => random(),
			"rec_id" => session_info("uid"),
			"usergroup" => session_info("usergroup"),
			"rel_id" => $profile_core_student_id,
			"db39_first_name" => $_db39_first_name,
			"db40_surname" => $_db40_surname,
			"db41_application_status" => $_db41_application_status,
			"db44_gender" => $_db44_gender,
			"db46_middle_name" => $_db46_middle_name,
			"db53_dob" => $_db53_dob,
			"db510_source_applicant" => $_db510_source_applicant,
			"db889_first_course" => $_db889_first_course,
			"db764_email" => $_db764_email,
			"db765_telephone" => $_db765_telephone,
			"db28467_mobile" => $_db28467_mobile,
			"db50" => $db50));
		$core_student_id = $dbh->lastInsertId();
//       	echo "core_student= " . $core_student_id;
        ///now reset rel_id to core_student_id
        $args['rel_id'] = $core_student_id;
        $args['db16135'] = $core_student_id;

        $response = "<div class=\"alert alert-success\">Core Student Created Successfully!</div>";

    }
    return array($args, $mappings);
}

//AFY POST_CREATE (3) of sis_sched_booking_detail
/* create_session_bookings Create the session booking for each scheduled session of the scheduled course */
function create_session_bookings($args, $static, $mappings) {
	$dbh = get_dbh();
	$scheduled_course_booking = $args['db15052'];

	list($scheduled_course,$core_student_id) = explode(',',pull_field ("sis_scheduled_booking","CONCAT(db14977,',',rel_id)","WHERE id = $scheduled_course_booking"));

	//check to see if the course has sessions (3=available, 4=full, 11=available for admins only)
	$sql = "SELECT id, db59840 as 'course_session'  from sis_scheduled_sessions WHERE rel_id ='$scheduled_course' AND (rec_archive IS NULL or rec_archive ='') AND db59908 IN ('3','4','11') ORDER by id ASC";
	$sth = $dbh->prepare($sql);
	$sth->execute();
	$schedule_sessions = $sth->fetchAll(\PDO::FETCH_ASSOC);
	foreach ($schedule_sessions as $schedule_session) {
		//create a session_booking
		$db59900_course_session = $schedule_session['course_session'];
		$db59901_scheduled_session = $schedule_session['id'];
		$db59902_booking_status = $args['db59978'];
		$db59976_schedule_booking = $scheduled_course_booking;
		$db59977_scheduled_booking_detail = $args['id'];

		$sql = "INSERT INTO sis_session_bookings (username_id, rec_id, usergroup, rel_id, db59900, db59901, db59902,db59976,db59977) VALUES (:username_id,:rec_id,:usergroup,:rel_id,:course_session,:scheduled_session,:booking_status,:schedule_booking,:scheduled_booking_detail)";
		dev_debug($sql);
		$sth = $dbh->prepare($sql);
		$sth->execute(array(
			"username_id" => random(),
			"rec_id" => session_info("uid"),
			"usergroup" => session_info("usergroup"),
			"rel_id" => $core_student_id,
			"course_session" => $db59900_course_session,
			"scheduled_session" => $db59901_scheduled_session,
			"booking_status" => $db59902_booking_status,
			"schedule_booking" => $db59976_schedule_booking,
			"scheduled_booking_detail" => $db59977_scheduled_booking_detail));
	}
	return array($args, $mappings);
}

function create_profile_session_bookings($args, $static, $mappings) {
	$dbh = get_dbh();
	$scheduled_course_booking = $args['id'];


	list($scheduled_course,$core_student_id) = explode(',',pull_field ("sis_scheduled_booking","CONCAT(db14977,',',rel_id)","WHERE id = $scheduled_course_booking"));
//	list($db59902_booking_status,$db59977_scheduled_booking_detail_id) = explode(',',pull_field ("sis_sched_booking_detail","CONCAT(db59978,',',id)","WHERE db15052 = $scheduled_course_booking"));
	$sched_id = pull_field ("sis_sched_booking_detail","id","WHERE db15052 = $scheduled_course_booking");

	//check to see if the course has sessions
	$sql = "SELECT id, db59840 as 'course_session'  from sis_scheduled_sessions WHERE rel_id ='$scheduled_course' AND (rec_archive IS NULL or rec_archive ='') AND db59908 = '3' ORDER by id ASC"; //only book those sessions that are available for booking
	$sth = $dbh->prepare($sql);
	$sth->execute();
	$schedule_sessions = $sth->fetchAll(\PDO::FETCH_ASSOC);
	foreach ($schedule_sessions as $schedule_session) {
		//create a session_booking
		$db59900_course_session = $schedule_session['course_session'];
		$db59901_scheduled_session = $schedule_session['id'];
//		$db59902_booking_status = $args['db59978'];
		$db59976_schedule_booking = $scheduled_course_booking;
//		$db59977_scheduled_booking_detail = $args['id'];

		$sql = "INSERT INTO sis_session_bookings (username_id, rec_id, usergroup, rel_id, db59900, db59901, db59902,db59976,db59977) VALUES (:username_id,:rec_id,:usergroup,:rel_id,:course_session,:scheduled_session,:booking_status,:schedule_booking,:scheduled_booking_detail_id)";
		dev_debug($sql);
		$sth = $dbh->prepare($sql);
		$sth->execute(array(
			"username_id" => random(),
			"rec_id" => session_info("uid"),
			"usergroup" => session_info("usergroup"),
			"rel_id" => $core_student_id,
			"course_session" => $db59900_course_session,
			"scheduled_session" => $db59901_scheduled_session,
			"booking_status" => $args['db14983'],
			"schedule_booking" => $db59976_schedule_booking,
			"scheduled_booking_detail_id" => $sched_id));
	}
	return array($args, $mappings);
}

function update_booking_info($args, $static, $mappings){
	if ($_SESSION['usergroup'] == $static['ug']) {
		//only trigger when the logged in person is an applicant
		
		if ($_SESSION['ulevel'] == '4') {
			$dbh = get_dbh();
			if (empty($args['status'])) {
				$booking_detail_sql =  "SELECT sis_scheduled_booking.id as 'booking_id', sis_scheduled_booking.username_id as 'booking_username_id', lead_invoice_settings.username_id as 'invoice_username_id', lead_invoice_settings.id as 'invoice_id'
				FROM sis_sched_booking_detail
				left join sis_scheduled_booking on sis_sched_booking_detail.db15052 = sis_scheduled_booking.id
				left join lead_invoice_settings on sis_scheduled_booking.db15438 = lead_invoice_settings.id 
				WHERE sis_sched_booking_detail.id=". $args['record'];
				$sth = $dbh->prepare($booking_detail_sql);
				$sth->execute();
				$booking_info = $sth->fetchAll(\PDO::FETCH_ASSOC);

				//booker variables
				$name = $args['db15054'];
				$surname = $args['db15055'];
				$link = $_SESSION['domain']."/admin/shortcourses/booking/".$booking_info[0]['booking_id']."?pg=253&vw=".$booking_info[0]['booking_username_id']."&ref=".$booking_info[0]['booking_id']."&invoice=".$booking_info[0]['invoice_username_id']; 

				//get template from preferences
				$edit_booking_template  = pull_field('lead_preferences', 'db67564', "WHERE usergroup = $_SESSION[usergroup]");
				if (!empty($edit_booking_template) && is_numeric($edit_booking_template)){
					list($coms_template_id,$coms_template_rec_id,$coms_template_usergroup,$coms_template_rel_id,$coms_template_template_name,$coms_template_subject_line,$coms_template_plain_text_version,$coms_template_html_version,$coms_template_email_address_to_send_from)=get_coms_template($edit_booking_template);
				}else{
					$coms_template_html_version = "Dear Admin <br />

					{{first_name}} {{surname}} has amended their attendee booking details.<br />
					
					Below are the changes: <br>
					{{changes}}<br>

					To view the amended profile please visit the following link:  {{link}} <br />

					Regards.";
					$coms_template_subject_line = 'Booker amended attendee booking details';
				}
			}else{
				$booking_detail_sql =  "SELECT db15054,db15055, sis_scheduled_booking.id as 'booking_id', sis_scheduled_booking.username_id as 'booking_username_id', lead_invoice_settings.username_id as 'invoice_username_id', lead_invoice_settings.id as 'invoice_id'
				FROM sis_sched_booking_detail
				left join sis_scheduled_booking on sis_sched_booking_detail.db15052 = sis_scheduled_booking.id
				left join lead_invoice_settings on sis_scheduled_booking.db15438 = lead_invoice_settings.id 
				WHERE sis_sched_booking_detail.id=". $args['id'];
				$sth = $dbh->prepare($booking_detail_sql);
				$sth->execute();
				$booking_info = $sth->fetchAll(\PDO::FETCH_ASSOC);

				//booker variables
				$name = $booking_info[0]['db15054'];
				$surname = $booking_info[0]['db15055'];
				$link = $_SESSION['domain']."/admin/shortcourses/booking/".$booking_info[0]['booking_id']."?pg=253&vw=".$booking_info[0]['booking_username_id']."&ref=".$booking_info[0]['booking_id']."&invoice=".$booking_info[0]['invoice_username_id']; 

				//get template from preferences
				// $edit_booking_template  = pull_field('lead_preferences', 'db67077', "WHERE usergroup = $_SESSION[usergroup]");
				// if (!empty($edit_booking_template) && is_numeric($edit_booking_template)){
				// 	list($coms_template_id,$coms_template_rec_id,$coms_template_usergroup,$coms_template_rel_id,$coms_template_template_name,$coms_template_subject_line,$coms_template_plain_text_version,$coms_template_html_version,$coms_template_email_address_to_send_from)=get_coms_template($edit_booking_template);
				// }else{
					$coms_template_html_version = "Dear Admin <br />

					{{first_name}} {{surname}} has cancelled their booking.<br />

					To view the cancelled booking please visit the following link:  {{link}} <br />

					Regards.";
					$coms_template_subject_line = 'Cancelled booking';
				//}
			}
			
			$message_html = email_template_replace_values("{{first_name}}",$name,$coms_template_html_version);
			$message_html = email_template_replace_values("{{surname}}",$surname,$message_html); 
			$message_html = email_template_replace_values("{{link}}",$link,$message_html);
			$changes = isset($_SESSION['attendee_detail_changes']) && $_SESSION['attendee_detail_changes'] ? $_SESSION['attendee_detail_changes'] : '';
			$message_html = email_template_replace_values( "{{changes}}", $changes, $message_html );
			$_SESSION['attendee_detail_changes'] = '';

			$message_plain_msg =$message_html;
			$message_html = $message_html;
			$emailTo=pull_field("form_schools","db1118","WHERE id='$_SESSION[usergroup]'");
			$emailFrom=pull_field("form_schools","db1117","WHERE id='$_SESSION[usergroup]'");
			if (empty($emailFrom)) {
				$emailFrom = master_email;
			}
			log_email($emailTo,$coms_template_subject_line,$message_plain_msg,$message_html,$emailFrom,"Booker amended booking");
		}
	}
	return array($args, $mappings);
}

	// INSERT INTO `system_model_hooks` (`model_id`, `hook_type`, `exec_order`, `script_path`, `namespace`, `function_name`, `static`)
	//VALUES ('257', '5', '2', '/sis/schedule_booking.php', 'sis_schedule_booking', 'booking_invoice_update', '{\"ug\":\"82\"}');
function booking_invoice_update($args, $static, $mappings){
	if ($_SESSION['usergroup'] == $static['ug']) {
		$dbh = get_dbh();
		$booking_detail_sql =  "SELECT sis_scheduled_booking.id as 'booking_id', sis_scheduled_booking.username_id as 'booking_username_id', lead_invoice_settings.username_id as 'invoice_username_id', lead_invoice_settings.id as 'invoice_id', db57181 as short_course
		FROM lead_invoice_account
		left join lead_invoice_settings on lead_invoice_account.rel_id = lead_invoice_settings.id
		left join sis_scheduled_booking on sis_scheduled_booking.db15438 = lead_invoice_settings.id 
		left join sis_sched_booking_detail on sis_sched_booking_detail.db15052 = sis_scheduled_booking.id
		WHERE lead_invoice_account.rel_id=". $args['rel_id'];
		$sth = $dbh->prepare($booking_detail_sql);
		$sth->execute();
		$booking_info = $sth->fetch(\PDO::FETCH_ASSOC);

		if(!isset($booking_info['booking_id'])) return [ $args, $mappings ];

		// only run this for short courses
		if(!$booking_info['short_course']) return [$args, $mappings];

			//booker variables
		$name = pull_field('form_users', 'db106' , "where id = $args[rec_id]");
		$surname = pull_field('form_users', 'db111' , "where id = $args[rec_id]");
		$link = $_SESSION['domain']."/admin/shortcourses/booking/".$booking_info['booking_id']."?pg=253&vw=".$booking_info['booking_username_id']."&ref=".$booking_info['booking_id']."&invoice=".$booking_info['invoice_username_id'];

		//get template from preferences
		$edit_invoice_template  = pull_field('lead_preferences', 'db67565', "WHERE usergroup = $_SESSION[usergroup]");
		if (!empty($edit_invoice_template) && is_numeric($edit_invoice_template)){
			list($coms_template_id,$coms_template_rec_id,$coms_template_usergroup,$coms_template_rel_id,$coms_template_template_name,$coms_template_subject_line,$coms_template_plain_text_version,$coms_template_html_version,$coms_template_email_address_to_send_from)=get_coms_template($edit_invoice_template);
		}else{
			$coms_template_html_version = "Dear Admin <br />

			{{first_name}} {{surname}} has amended their booking invoice details.<br />

			Below are the changes: <br>
			{{changes}}<br>

			To view the profile linked to the amended invoice please visit the following link:  {{link}} <br />

			Regards.";
			$coms_template_subject_line = 'Booking invoice details amended';
		}

		$message_html = email_template_replace_values("{{first_name}}",$name,$coms_template_html_version);
		$message_html = email_template_replace_values("{{surname}}",$surname,$message_html); 
		$message_html = email_template_replace_values("{{link}}",$link,$message_html);
		$changes = isset($_SESSION['invoice_detail_changes']) && $_SESSION['invoice_detail_changes'] ? $_SESSION['invoice_detail_changes'] : '';
		$message_html = email_template_replace_values( "{{changes}}", $changes, $message_html );
		$_SESSION['invoice_detail_changes'] = '';

		$message_plain_msg =$message_html;
		$message_html = $message_html;
		$emailTo=pull_field("form_schools","db1118","WHERE id='$_SESSION[usergroup]'");
		$emailFrom=pull_field("form_schools","db1117","WHERE id='$_SESSION[usergroup]'");
		if (empty($emailFrom)) {
			$emailFrom = master_email;
		}
		log_email($emailTo,$coms_template_subject_line,$message_plain_msg,$message_html,$emailFrom,"Invoice Amended");
	}
	return array($args, $mappings);
}

	// INSERT INTO `system_model_hooks` (`model_id`, `hook_type`, `exec_order`, `script_path`, `namespace`, `function_name`, `static`)
	// VALUES ('261', '4', '3', '/sis/schedule_booking.php', 'sis_schedule_booking', 'attendee_detail_changes', '{\"ug\":\"82\"}');
	function attendee_detail_changes($args, $static, $mappings)
	{
		if($_SESSION['usergroup'] == $static['ug']) {
			//only trigger when the logged in person is an applicant

			if($_SESSION['ulevel'] == '4') {
				$dbh = get_dbh();
				if(empty($args['status'])) {
					$sql = "SELECT * FROM sis_sched_booking_detail WHERE id = ?";
					$sth = $dbh->prepare($sql);
					$sth->execute([$args['record']]);
					$old = $sth->fetch(\PDO::FETCH_ASSOC);

					$sql = "select name,db_field_name as k from system_table where pg_id = 261 and usergroup=?";
					$sth = $dbh->prepare($sql);
					$sth->execute([$_SESSION['usergroup']]);
					$data = $sth->fetchAll(\PDO::FETCH_ASSOC);

					$fields = [];
					foreach($data as $v) {
						$fields[$v['k']] = $v['name'];
					}
					$changes = '';
					$ignore = [ 'id', 'rec_id', 'rel_id', 'username_id', 'rec_archive', 'date', 'rec_lstup_id', 'rec_lstup', 'usergroup' ];
					foreach(  $args as $f=>$v ){
						if( !in_array($f, $ignore) && isset($old[$f]) && isset($args[$f]) && $old[$f] != $args[$f] ){
							$t = isset($fields[$f]) ? $fields[$f] : $f;
							$changes .= "<li><i>{$t}</i> changed from <b> {$old[$f]}</b> to <b>{$args[$f]}</b></li>";
						}
					}
					$_SESSION['attendee_detail_changes'] = $changes ? "<ol>{$changes}</ol>" : 'no changes made.';
					//echo "<pre><ol>{$changes}</ol></pre>";
				}
			}
		}
		return array($args, $mappings);
	}

	// INSERT INTO `system_model_hooks` (`model_id`, `hook_type`, `exec_order`, `script_path`, `namespace`, `function_name`, `static`)
	// VALUES ('257', '4', '1', '/sis/schedule_booking.php', 'sis_schedule_booking', 'invoice_detail_changes', '{\"ug\":\"82\"}');
	function invoice_detail_changes($args, $static, $mappings)
	{
		if($_SESSION['usergroup'] == $static['ug']) {
			//only trigger when the logged in person is an applicant
			if($_SESSION['ulevel'] == '4') {
				$dbh = get_dbh();
				if(empty($args['status'])) {
					$sql = "SELECT * FROM lead_invoice_account WHERE id = ?";
					$sth = $dbh->prepare($sql);
					$sth->execute([$args['record']]);
					$old = $sth->fetch(\PDO::FETCH_ASSOC);

					$sql = "select name,db_field_name as k from system_table where pg_id = 257";
					$sth = $dbh->prepare($sql);
					$sth->execute();
					$data = $sth->fetchAll(\PDO::FETCH_ASSOC);

					$fields = [];
					foreach($data as $v) {
						$fields[$v['k']] = $v['name'];
					}
					$changes = '';
					$ignore = [ 'id', 'rec_id', 'rel_id', 'username_id', 'rec_archive', 'date', 'rec_lstup_id', 'rec_lstup', 'usergroup' ];
					foreach(  $args as $f=>$v ){
						if( !in_array($f, $ignore) && isset($old[$f]) && isset($args[$f]) && $old[$f] != $args[$f] ){
							$t = isset($fields[$f]) ? $fields[$f] : $f;
							$changes .= "<li><i>{$t}</i> changed from <b> {$old[$f]}</b> to <b>{$args[$f]}</b></li>";
						}
					}
					$_SESSION['invoice_detail_changes'] = $changes ? "<ol>{$changes}</ol>" : 'no changes made.';
					//echo "<pre><ol>{$changes}</ol></pre>";
				}
			}
		}
		return array($args, $mappings);
	}

	//post-update hooks sis_sched_booking_detail
	// INSERT INTO `system_model_hooks` (`model_id`, `hook_type`, `exec_order`, `script_path`, `namespace`, `function_name`, `static`)
	// VALUES ('253', '5', '4', '/sis/schedule_booking.php', 'sis_schedule_booking', 'update_mrn_booking_status', '');
	function update_mrn_booking_status($args, $static, $mappings): array
	{
		if (pull_field('form_schools', 'db30', "WHERE id={$_SESSION['usergroup']}") == '12') {
			$dbh = get_dbh();

            $old_value = pull_field("sis_sched_booking_detail","db59978","WHERE sis_sched_booking_detail.id={$args['record']}");

            //update booking status
			$query = "UPDATE sis_sched_booking_detail SET db59978 = '{$args['db14983']}', db64664='{$args['db14981']}' WHERE db15052={$args['record']}";
			dev_debug($query);
			$sth = $dbh->prepare($query);
			$sth->execute();

            //track the booking status if different
            if ($old_value != $args['db14983']) {

                $query = "INSERT INTO `sis_booking_status_track` (`username_id`, `rec_id`, `usergroup`, `rel_id`,  `db67048`, `db333715`, `db333718`, `db333721`)
                            VALUES
                        (".random(). ','. $_SESSION['rec_id'].','. $_SESSION['usergroup'].','. $args['record'].','.  $args['db14983'].','. $old_value.','. 'db59978'.','. 'Updated booking status directly'.")";
                dev_debug($query);
                $sth = $dbh->prepare($query);
                $sth->execute();
            }
		}
		return array($args, $mappings);
	}
	// INSERT INTO `system_model_hooks` (`model_id`, `hook_type`, `exec_order`, `script_path`, `namespace`, `function_name`, `static`)
	// VALUES ('261', '5', '4', '/sis/schedule_booking.php', 'sis_schedule_booking', 'update_mrn_status', '');
/**
 * @throws Exception
 * @throws \Doctrine\DBAL\Exception
 */
function update_mrn_status($args, $static, $mappings): array
	{
		if (pull_field('form_schools', 'db30', "WHERE id={$_SESSION['usergroup']}") == '12') {
			$dbh = get_dbh();
			//update booking status
			$query = "UPDATE sis_scheduled_booking	 SET db14983  = '{$args['db59978']}', db14981 ='{$args['db64664']}' WHERE id ={$args['db15052']}";
			dev_debug($query);
			$sth = $dbh->prepare($query);
			$sth->execute();

			//sessions check
			$session_check_sql= "select sb.* from sis_session_bookings sb
			LEFT JOIN sis_scheduled_sessions ss ON ss.id = db59901
			LEFT JOIN sis_scheduled_booking cb ON cb.id = db59976
			LEFT JOIN sis_sched_booking_detail sbd ON sbd.id=sb.db59977
			WHERE (sb.rec_archive IS NULL OR sb.rec_archive = '') 
			AND sb.usergroup='{$_SESSION['usergroup']}'
			AND sbd.id = '{$args['record']}' 
			AND (cb.rec_archive IS NULL OR cb.rec_archive = '') 
			AND (sbd.rec_archive is null or sbd.rec_archive = '')
			";
			dev_debug($session_check_sql);
			$sth2 = $dbh->prepare($session_check_sql);
			$sth2->execute();
			$results = $sth2->fetchAll(\PDO::FETCH_ASSOC);

			if (!empty($results)){
				foreach ($results as $result){
                    $attendance_status_sql= "";
                    if(!empty($args['db64664'])){
                        $attendance_status_sql = ",db59903 ='{$args['db64664']}'";
                    }

                    if(!empty($result['db59903'])){
                        $attendance_status_sql= "";
                    }
					$update= "update sis_session_bookings set db59902='{$args['db59978']}' {$attendance_status_sql} where id= {$result['id']} ";
					dev_debug($update);
					$sth3 = $dbh->prepare($update);
					$sth3->execute();
				}
			}

		}
		return array($args, $mappings);
	}

    //pre-update hook
// INSERT INTO `system_model_hooks` (`model_id`, `hook_type`, `exec_order`, `script_path`, `namespace`, `function_name`, `static`)
// VALUES ('261', '4', '5', '/sis/schedule_booking.php', 'sis_schedule_booking', 'send_booking_notification', '');
/**
 * @throws Exception
 * @throws \Doctrine\DBAL\Exception
 */
function send_booking_notification($args, $static, $mappings): array
{
    if (pull_field('form_schools', 'db30', "WHERE id={$_SESSION['usergroup']}") == '12') {
		include_once($_SERVER['DOCUMENT_ROOT'] . "/admin/config.php");
		
        $dbh = get_dbh();
        $usergroup = $_SESSION['usergroup'];
        //get the current booking status
        list($current_booking,$current_attendance) = explode(',',pull_field('sis_sched_booking_detail', "CONCAT(IFNULL(db59978,''),',',IFNULL(db64664,''))", "where id ={$args['record']}"));
        //text message preference
        $texting_allowed=pull_field("lead_preferences","db111494","WHERE usergroup='{$usergroup}'");
		$prioritise_email=pull_field("lead_preferences","db236852","WHERE usergroup='{$usergroup}'");
		$allowed_statuses = [1,7,6];
        $booking_statuses = [2,3];

		$booking_id= $args['db15052'];
		$course_schedule_id = pull_field('sis_scheduled_booking', 'db14977', "WHERE id = {$booking_id}");

		//check if we have sessions on the course
		$sql = "SELECT id, db59840 as 'course_session'
				from sis_scheduled_sessions WHERE rel_id ='$course_schedule_id'
                AND (rec_archive IS NULL or rec_archive ='') AND db59908 IN  ('2','3','4','11') ORDER by id ASC"; //only book those sessions that are available for booking
		$sth = $dbh->prepare($sql);
		$sth->execute();
		$schedule_sessions = $sth->fetchAll(\PDO::FETCH_ASSOC);
		if (!empty($schedule_sessions)){
			foreach ($schedule_sessions as $schedule_session){
				//check if session booking exist
				list($check_session,$attendance_status) = explode(',',pull_field('sis_session_bookings',"CONCAT(id,',',db59903)", "WHERE db59977='{$args['record']}' and db59901='{$schedule_session['id']}'"));
                if (!empty($attendance_status)) {
                    $db59903='';
                    if (empty($args['db64664'])) {
                        if ($attendance_status == '4' || $attendance_status == '5') { //canceled by learner prior or cancelled by college (automatically set if booking was previously cancelled
                            $db59903 = ",db59903=''";
                        }
                    }
                    else {
                        $db59903 = ",db59903='{$args['db64664']}'";
                    }
                }
				if (!empty($check_session)){
					$update_sql = "Update sis_session_bookings set db59902='{$args['db59978']}' $db59903 where db59977='{$args['record']}' and db59901='{$schedule_session['id']}'";
				}else{
					$username_id = random();
					$student_id = pull_field('sis_sched_booking_detail', 'rel_id', "WHERE id = {$args['record']}");
					$update_sql = "INSERT INTO sis_session_bookings (username_id, rec_id, usergroup, rel_id, db59900, db59901, db59902,db59976,db59977) 
						VALUES ('{$username_id}', '{$args['rec_id']}','{$args['usergroup']}','{$student_id}','','{$schedule_session['id']}','{$args['db59978']}','{$args['db15052']}','{$args['record']}')
					";

				}
				dev_debug($update_sql);
				$sth8= $dbh->prepare($update_sql);
				$sth8->execute();
			}
		}
		if (!in_array($current_booking, $booking_statuses) && in_array($args['db59978'], $booking_statuses)){
            if ($current_attendance == '4' || $current_attendance == '5' ) {//canceled by learner prior or cancelled by college (automatically set if booking was previously cancelled
                if (!empty($args['db64664']) &&  $args['db64664'] == '4' || $args['db64664'] =='5') {
                    if ($attendance_status == '4' || $attendance_status == '5') { //canceled by learner prior or cancelled by college (automatically set if booking was previously cancelled
                        $sql = "UPDATE sis_sched_booking_detail
    		                LEFT JOIN sis_scheduled_booking on sis_sched_booking_detail.db15052=sis_scheduled_booking.id
    		                SET db64664='',  db14981=''
    		                WHERE sis_scheduled_booking.id={$args['record']}
    		                ";
                        $sth = $dbh->prepare($sql);
                        $sth->execute();
                    }
                }
            }
            //send out the booking notifications
	        $shortcourses = new \ShortcoursesModel;
	        $emails = new \Emails;
            $booking_detail =  $args['record'];
            $booking_detail_rel = pull_field('sis_sched_booking_detail', 'rel_id', "where id ={$args['record']}");
            $core_student_id =  pull_field("core_students", 'rel_id', "WHERE id ={$booking_detail_rel}");
            $sis_profiles_sql  = "SELECT * FROM sis_profiles where rel_id='{$core_student_id}'";
            dev_debug($sis_profiles_sql);
            $sth1 = $dbh->prepare($sis_profiles_sql);
            $sth1->execute();
            $sis_profile_data = $sth1->fetch(\PDO::FETCH_ASSOC);
			
	        $comms_preference  = $shortcourses->comms_preference($sis_profile_data['id']);
			//get course details
			//get course details
			$scheduled_course_info = pull_field("sis_course_schedule", "CONCAT(IFNULL(sis_course_schedule.id, ''),'|||',(SELECT db232 from core_courses where id = db14946),'|||',(SELECT IFNULL(db234,'') from core_courses where id = db14946),
				'|||',IFNULL(DATE_FORMAT(db14947,'%d %M %Y'), ''),'|||',IFNULL(CONCAT(SUBSTRING(db14948,1,2),':',SUBSTRING(db14948,3,2)), ''),'|||',IFNULL(DATE_FORMAT(db14949,'%d %M %Y'),''),'|||',IFNULL(CONCAT(SUBSTRING(db14950,1,2),':',SUBSTRING(db14950,3,2)), ''),
				'|||',IFNULL(db63007,''),'|||',IFNULL(db14956,''),'|||',IFNULL((SELECT db14963 from sis_course_venues where id = db14954),''),'|||',IFNULL(db14946, ''),
				'|||',IFNULL(db15433, ''),'|||',ifnull((SELECT db14966 from sis_course_venues where id = db14954), ''),
				'|||',IFNULL((SELECT db14965 from sis_course_venues where id = db14954), ''),'|||',IFNULL((SELECT db209162 from sis_course_venues where id = db14954), ''),
				'|||',IFNULL(db14953,''),'|||',IFNULL((SELECT db14968 from sis_course_venues where id = db14954), ''),
				'|||',IFNULL((SELECT db237356 from sis_course_venues where id = db14954), ''), '|||',IFNULL(db284645 ,''),'|||', IFNULL(db14952,0),'|||', sis_course_schedule.username_id)",
				"WHERE id= $course_schedule_id"
			);
			list($scheduled_course_id, $scheduled_course_name, $course_summary, $scheduled_course_start_date, $scheduled_course_start_time, $scheduled_course_end_date,
				$scheduled_course_end_time, $scheduled_course_web_conferencing_info, $scheduled_course_additional_info, $scheduled_course_venue_name,
				$course_id, $scheduled_course_evaluation_link, $course_postcode, $venue_address, $venue_whatthreewords, $course_minimum_attendees,
				$venue_access_information, $venue_image, $notificationRequired, $maxAttendees, $course_username_id
				) = explode('|||', $scheduled_course_info);
			
			// auto remove student form waiting list
			$waitingListCheck = pull_field('core_waitinglist', 'id', "WHERE rel_id={$core_student_id} and db16146={$course_id} and (db62944 is null or db62944 != '') and (db62945 is null or db62945 != '')");
			if(!empty($waitingListCheck)){
				$update_sql = "UPDATE core_waitinglist SET rec_lstup = CURRENT_TIMESTAMP, rec_lstup_id = {$_SESSION['uid']},  db62944 ='{$args['db15052']}', db62945=CURRENT_TIMESTAMP  WHERE rel_id = {$core_student_id} AND  db16146 = {$course_id}";
				dev_debug($update_sql);
				$sth = $dbh->prepare($update_sql);
				$sth->execute();
			}
			
			$maxAttendeesReached = false;
			$over_booked = false;
			
			//do this only when we are booking and not adding to waitinglist
				$attendeesSql = "
					SELECT count(sis_sched_booking_detail.id) as cnt FROM sis_sched_booking_detail
					LEFT JOIN sis_scheduled_booking ON sis_scheduled_booking.id =  cast(db15052 as unsigned)
					LEFT JOIN core_students on core_students.id = db16135
					WHERE sis_sched_booking_detail.usergroup = '{$_SESSION['usergroup']}'
					AND (sis_scheduled_booking.rec_archive is NULL or sis_scheduled_booking.rec_archive = '')
					AND (sis_sched_booking_detail.rec_archive is NULL or sis_sched_booking_detail.rec_archive = '')
					AND (core_students.rec_archive is NULL or core_students.rec_archive = '')
					AND (db59978 = '2' OR db59978 = '3' OR db59978 = '8' OR db59978 = '14')
					AND db14977 = {$course_schedule_id}
				";
				dev_debug($attendeesSql);
				$sth8 = $dbh->prepare($attendeesSql);
				$sth8->execute();
				$attendees = $sth8->fetchColumn();
				
			//add 1 cause this a pre update hook
			$attendees = $attendees+1;
				if($attendees == $maxAttendees){
					$maxAttendeesReached = true;
				}
				
				if ($attendees> $maxAttendees){
					$over_booked = true;
				}
			
	        
	        // Get session info
	        $session_info = $shortcourses->get_session_info($scheduled_course_id);
            $session_info_no_venue = $shortcourses->get_session_info($scheduled_course_id,'','no');
	        $session_info_with_webconferencing = $shortcourses->get_session_info($scheduled_course_id,'','no','yes');
	        
	        $name = $sis_profile_data['db48566'];
	        $surname = $sis_profile_data['db48568'];
	        $email = $sis_profile_data['db48583'];
	        $fullname = $name . ' ' .$surname;
	        if (!empty($venue_address)){
		        $course_venue = $scheduled_course_venue_name.'<br />'.$venue_address . ' '. $course_postcode;
	        }else{
		        $course_venue = $scheduled_course_venue_name;
	        }
	        if (!empty($comms_preference) && $notificationRequired!='no'){
		        
		        $has_email = $has_sms = false;
		        //if coms_pref is email
		        if (in_array('email', $comms_preference)){
			        $has_email = true;
			        $booking_template_id = pull_field('sis_course_schedule', 'db19828', "WHERE id ={$course_schedule_id} ");
			        $coms_template_subject_line = '';
                    if (empty($booking_template_id)){
                        $booking_template_id = pull_field("coms_template","id","WHERE db1320 = 'course_booking' AND usergroup ='$_SESSION[usergroup]'");
                    }
                    if (empty($booking_template_id)){
                        $booking_template_id = pull_field("coms_template","id","WHERE db1320 = 'course_booking' AND usergroup ='1'");
                    }
			        if (!empty($booking_template_id)){
				        list($coms_template_id,$coms_template_rec_id,$coms_template_usergroup,$coms_template_rel_id,$coms_template_template_name,$coms_template_subject_line,$coms_template_plain_text_version,$coms_template_html_version,$coms_template_email_address_to_send_from)=get_coms_template($booking_template_id);
			        }
					
			        if (!empty($coms_template_html_version)){
				        //REPLACE TAGS HERE
				        $coms_template_html_version = str_replace('{{course_name}}', $scheduled_course_name, $coms_template_html_version);
                        $coms_template_html_version = str_replace('{{course_summary}}', $course_summary, $coms_template_html_version);
				        $coms_template_html_version = str_replace('{{course_start_date}}', $scheduled_course_start_date, $coms_template_html_version);
				        $coms_template_html_version = str_replace('{{course_start_time}}', $scheduled_course_start_time, $coms_template_html_version);
				        $coms_template_html_version = str_replace('{{course_end_date}}', $scheduled_course_end_date, $coms_template_html_version);
				        $coms_template_html_version = str_replace('{{course_end_time}}', $scheduled_course_end_time, $coms_template_html_version);
				        $coms_template_html_version = str_replace('{{course_venue}}', $course_venue, $coms_template_html_version);
						$coms_template_html_version = str_replace('{{course_venue_no_address}}', $scheduled_course_venue_name, $coms_template_html_version);
						$coms_template_html_version = str_replace('{{web_conferencing_information}}', $scheduled_course_web_conferencing_info, $coms_template_html_version);
				        $coms_template_html_version = str_replace('{{additional_information}}', $scheduled_course_additional_info, $coms_template_html_version);
				        $coms_template_html_version = str_replace('{{session_info}}', $session_info, $coms_template_html_version);
                        $coms_template_html_version = str_replace('{{session_info_no_venue}}', $session_info_no_venue, $coms_template_html_version);
				        $coms_template_html_version = str_replace('{{session_info_with_webconferencing}}', $session_info_with_webconferencing, $coms_template_html_version);
				        $coms_template_html_version = str_replace("{{first_name}}", $name, $coms_template_html_version);
				        $coms_template_html_version = str_replace("{{name}}", $name, $coms_template_html_version);
				        $coms_template_html_version = str_replace("{{surname}}", $surname, $coms_template_html_version);
				        $coms_template_html_version = str_replace("{{course_postcode}}", $course_postcode, $coms_template_html_version);
				        $coms_template_html_version = str_replace("{{course_minimum_attendees}}", $course_minimum_attendees, $coms_template_html_version);
				        $coms_template_html_version = str_replace('{{course_whatthreewords}}', $venue_whatthreewords, $coms_template_html_version);
                        $coms_template_html_version = str_replace('{{course_venue_information}}', $venue_access_information, $coms_template_html_version);
                        if (!empty($venue_image)) {
                            $image_path = engine_url("/media/dl.php?a=yes&fl=".encode($venue_image, 'unsalted'));
                            $venue_image = " <img src=\"$image_path\">" ;
                        }
                        $coms_template_html_version = str_replace('{{course_venue_image}}',  $venue_image, $coms_template_html_version);
                        $coms_template_subject_line = str_replace('{{course_name}}', $scheduled_course_name, $coms_template_subject_line);

				        $email_args=[
					        'to'=> $email,
					        'subject'=>$coms_template_subject_line,
					        'text'=>$coms_template_html_version,
					        'html'=> $coms_template_html_version,
					        'rel_id' => $core_student_id,
					        'priority' => 'urgent',
                            'template_id' => $booking_template_id,
				        ];
				        $emails->send($email_args);
			        }
		        }

				$send_sms= true;
				if ($prioritise_email == 'yes' && !empty($has_email)){
					$send_sms = false;
				}


				//if coms pref is sms
		        if(in_array('sms', $comms_preference) && $texting_allowed == 'yes' && !empty($send_sms)){
			        $has_sms = true;
			       
			        //get sms template
			        $sms_template_id = pull_field('coms_sms_template', 'id', "WHERE db159320 = '2' AND usergroup = {$_SESSION['usergroup']} AND (rec_archive is null or rec_archive ='') and db159983 = 'yes'");
			        if (empty($sms_template_id)){
				        //get default
				        $sms_template_id = pull_field('coms_sms_template', 'id', "WHERE db159320 = '2' AND usergroup = 1 AND (rec_archive is null or rec_archive ='') and db159983='yes'");
			        }
					
			    			 
			        if (!empty($sms_template_id)){
				        $sms_template_sql  = "SELECT  db25661 AS subject, db25663 as content
						FROM coms_sms_template WHERE id = {$sms_template_id}
						";
						dev_debug($sms_template_sql);
						$sth2 = $dbh->prepare($sms_template_sql);
						$sth2->execute();
				        $sms_template = $sth2->fetch(\PDO::FETCH_ASSOC);
				        
				        //send out sms
				        $messages = new \CommunicationMessages;
				        $subject = $sms_template['subject'];
				        $recipient_number = $sis_profile_data['db48597'];
				        $send = 'Send';
				        $student_id = $core_student_id;
				        $sms_text =$sms_template['content'];
				        
				        //replace tags
				        $sms_text = str_replace('{{course_name}}', $scheduled_course_name, $sms_text);
				        $sms_text = str_replace('{{course_start_date}}', $scheduled_course_start_date, $sms_text);
				        $sms_text = str_replace('{{course_start_time}}', $scheduled_course_start_time, $sms_text);
				        $sms_text = str_replace('{{course_end_date}}', $scheduled_course_end_date, $sms_text);
				        $sms_text = str_replace('{{course_end_time}}', $scheduled_course_end_time, $sms_text);
				        $sms_text = str_replace('{{course_venue}}', $course_venue, $sms_text);
						$sms_text = str_replace('{{course_venue_no_address}}', $scheduled_course_venue_name, $sms_text);
						$sms_text = str_replace('{{web_conferencing_information}}', $scheduled_course_web_conferencing_info, $sms_text);
				        $sms_text = str_replace('{{additional_information}}', $scheduled_course_additional_info, $sms_text);
				        $sms_text = str_replace('{{session_info}}', $session_info, $sms_text);
                        $sms_text = str_replace('{{session_info_no_venue}}', $session_info_no_venue, $sms_text);
				        $sms_text = str_replace('{{session_info_with_webconferencing}}', $session_info_with_webconferencing, $sms_text);
				        $sms_text = str_replace("{{first_name}}", $name, $sms_text);
				        $sms_text = str_replace("{{name}}", $name, $sms_text);
				        $sms_text = str_replace("{{surname}}", $surname, $sms_text);
				        $sms_text = str_replace("{{course_postcode}}", $course_postcode, $sms_text);
				        $sms_text = str_replace("{{current_date}}", notification_date_format(date("Y-m-d")), $sms_text);
				        $sms_text = str_replace("{{title}}", $sis_profile_data['db48619'], $sms_text);
				        $sms_text = str_replace("{{full_name}}",$fullname , $sms_text);
				        $sms_text = str_replace("{{postcode}}", $sis_profile_data['db48622'], $sms_text);
				        $sms_text = str_replace(['<br />', '<br>'], ' ', $sms_text);
				        $sms_text = str_replace("{{course_minimum_attendees}}", $course_minimum_attendees, $sms_text);
				        $sms_text = str_replace('{{course_whatthreewords}}', $venue_whatthreewords, $sms_text);
				        $sms = $messages->log_sms($template = "", $subject, $sms_text, $recipient_number, $student_id, $usergroup, $send);
			        }
		        }
		        
		        //if coms_pref is letter
		        if(in_array('letter', $comms_preference) && empty($has_sms) && empty($has_email)){
			        $user_address_with_postcode = $sis_profile_data['db48621'] . '<br>' . $sis_profile_data['db48596']. '<br>' .$sis_profile_data['db48622'];
			        $user_address_no_postcode = $sis_profile_data['db48621'] . '<br>' . $sis_profile_data['db48596'];
			        //get letter template
			        $letter_template_id = pull_field('core_letter_library', 'id', "WHERE db33619 = '5' AND usergroup = {$_SESSION['usergroup']} AND (rec_archive is null or rec_archive ='') and db1490 = 'yes'");
			        if (empty($letter_template_id)){
				        //get default
				        $letter_template_id = pull_field('core_letter_library', 'id', "WHERE db33619 = '5' AND usergroup = 1 AND (rec_archive is null or rec_archive ='') and db1490 = 'yes'");
			        }
					
			        if(!empty($letter_template_id)){
				        $letter_template_sql = "
						SELECT db1488 AS letter_content, db1487 as title
						FROM core_letter_library where id = {$letter_template_id}
						";
				        dev_debug($letter_template_sql);
				        $sth3 = $dbh->prepare($letter_template_sql);
				        $sth3->execute();
				        $letter_template_data = $sth3->fetch(\PDO::FETCH_ASSOC);
				        $letter_template = $letter_template_data['letter_content'];
				        $letter_title = $scheduled_course_name . ' ' . $scheduled_course_start_date . ' ' . $letter_template_data['title'];
				        
				        /// replace tags on the template
				        $letter_template = str_replace('{{course_name}}', $scheduled_course_name, $letter_template);
				        $letter_template = str_replace('{{course_start_date}}', $scheduled_course_start_date, $letter_template);
				        $letter_template = str_replace('{{course_start_time}}', $scheduled_course_start_time, $letter_template);
				        $letter_template = str_replace('{{course_end_date}}', $scheduled_course_end_date, $letter_template);
				        $letter_template = str_replace('{{course_end_time}}', $scheduled_course_end_time, $letter_template);
				        $letter_template = str_replace('{{course_venue}}', $course_venue, $letter_template);
						$letter_template = str_replace('{{course_venue_no_address}}', $scheduled_course_venue_name, $letter_template);
						$letter_template = str_replace('{{web_conferencing_information}}', $scheduled_course_web_conferencing_info, $letter_template);
				        $letter_template = str_replace('{{additional_information}}', $scheduled_course_additional_info, $letter_template);
				        $letter_template = str_replace('{{session_info}}', $session_info, $letter_template);
                        $letter_template = str_replace('{{session_info_no_venue}}', $session_info_no_venue, $letter_template);
				        $letter_template = str_replace('{{session_info_with_webconferencing}}', $session_info_with_webconferencing, $letter_template);
				        $letter_template = str_replace("{{first_name}}", $name, $letter_template);
				        $letter_template = str_replace("{{name}}", $name, $letter_template);
				        $letter_template = str_replace("{{surname}}", $surname, $letter_template);
				        $letter_template = str_replace("{{course_postcode}}", $course_postcode, $letter_template);
				        $letter_template = str_replace("{{current_date}}", notification_date_format(date("Y-m-d")), $letter_template);
				        $letter_template = str_replace("{{title}}", $sis_profile_data['db48619'], $letter_template);
				        $letter_template = str_replace("{{full_name}}", $fullname, $letter_template);
				        $letter_template = str_replace("{{address}}", $user_address_with_postcode, $letter_template);
				        $letter_template = str_replace("{{postcode}}", $sis_profile_data['db48622'], $letter_template);
				        $letter_template = str_replace("{{address_no_postcode}}", $user_address_no_postcode, $letter_template);
				        $letter_template = str_replace("{{course_minimum_attendees}}", $course_minimum_attendees, $letter_template);
				        $letter_template = str_replace('{{course_whatthreewords}}', $venue_whatthreewords, $letter_template);
				        
				        // insert letter
				        $letter_random = random();
				        $db20300 = '';
				        $db69980 = '';
				        $recipient_id = $core_student_id;
				        $letter_insert_sql = "INSERT  INTO  dir_letters_sent (username_id, rec_id, usergroup, rel_id, db20300, db20301,db31870,db69980)
							VALUES  ('{$letter_random}', '{$_SESSION['uid']}', '{$_SESSION['usergroup']}', '{$recipient_id}', '{$db20300}', '{$letter_title}', '{$letter_template}',  '{$db69980}')";
				        $sth7 = $dbh->prepare($letter_insert_sql);
				        $sth7->execute();
				        $last_id = $dbh->lastInsertId();
				        
				        $http_link = engine_url . "/modules/inc_letters_sent.php?&rec=$last_id&pdf=1";
				        $letter_update_sql = "UPDATE dir_letters_sent SET db20300=? WHERE id=? AND usergroup=?";
				        $sql1 = $dbh->prepare($letter_update_sql);
				        $sql1->execute(array($http_link, $last_id, $_SESSION['usergroup']));
			        }
		        }
		        
		        if (in_array('phone', $comms_preference) & sizeof($comms_preference) ==1) {
			        
			        $email_message = "Dear Admin

					{$fullname} has been booked to the {$scheduled_course_name} course, starting on {$scheduled_course_start_date}.

					The student selected home telephone as a method of contact.

					May you please get in touch with the student and notify them of the booking.

					Regards
					
					Mind Recovery Net Team
					";
			        $coms_template_subject_line = 'Booking Notification Request';
			        //notify the admin that the applicant need to be notified by phone
			        $admin_email = pull_field('form_schools', 'db1118', "WHERE id={$_SESSION['usergroup']}");
			        $email_args=[
				        'to'=> $admin_email,
				        'subject'=>$coms_template_subject_line,
				        'text'=>nl2br($email_message),
				        'html'=> nl2br($email_message),
			        ];
			        $emails->send($email_args);
		        }
		        
		        if (in_array('mobile', $comms_preference) & sizeof($comms_preference) ==1) {
			        $email_message = "Dear Admin

					{$fullname} has been booked to the {$scheduled_course_name} course, starting on {$scheduled_course_start_date}.

					The student selected mobile phone as a method of contact.

					May you please get in touch with the student and notify them of the booking.

					Regards
					
					Mind Recovery Net Team
					";
			        $coms_template_subject_line = 'Booking Notification Request';
			        //notify the admin that the applicant need to be notified by phone
			        $admin_email = pull_field('form_schools', 'db1118', "WHERE id={$_SESSION['usergroup']}");
			        $email_args=[
				        'to'=> $admin_email,
				        'subject'=>$coms_template_subject_line,
				        'text'=>nl2br($email_message),
				        'html'=> nl2br($email_message),
			        ];
			        $emails->send($email_args);
		        }
	        }
			
			//notify admin and tutor about booking
			if ($sis_profile_data['db48569'] == 'yes') {
				//get template using tag
				$select_email_template = pull_field("coms_template", "id", "WHERE usergroup='" . session_info('usergroup') . "' AND db1147='119' ");
				//select default
				if (empty($select_email_template)) {
					$select_email_template = pull_field("coms_template", "id", "WHERE db1147='119' AND usergroup='1'");
				}
				
				//get template
				$qry = "SELECT db1085 as template, db1086 as subject FROM coms_template WHERE id='$select_email_template'";
				dev_debug($qry);
				$stmt2 = $dbh->prepare($qry);
				$stmt2->execute();
				$email_template = $stmt2->fetch(\PDO::FETCH_ASSOC);
				$coms_template_html_version = $email_template['template'];
				$coms_template_subject_line = $email_template['subject'];
				
				if (!empty($coms_template_html_version)){
					
					$sis_profile_username = $sis_profile_data['username_id'];
					$sis_profile_rel = $sis_profile_data['rel_id'];
					$link = website_url . "/admin/mrn_profiles/profile/" . $sis_profile_data['id'] . '?vw=' . $sis_profile_username . '&ref=' . $sis_profile_rel . '&profile_id=' . $sis_profile_data['id'];
					
					//REPLACE TAGS HERE
					$coms_template_html_version = str_replace('{{course_name}}', $scheduled_course_name, $coms_template_html_version);
					$coms_template_html_version = str_replace('{{course_summary}}', $course_summary, $coms_template_html_version);
					$coms_template_html_version = str_replace('{{course_start_date}}', $scheduled_course_start_date, $coms_template_html_version);
					$coms_template_html_version = str_replace('{{course_start_time}}', $scheduled_course_start_time, $coms_template_html_version);
					$coms_template_html_version = str_replace('{{course_end_date}}', $scheduled_course_end_date, $coms_template_html_version);
					$coms_template_html_version = str_replace('{{course_end_time}}', $scheduled_course_end_time, $coms_template_html_version);
					$coms_template_html_version = str_replace('{{course_venue}}', $course_venue, $coms_template_html_version);
					$coms_template_html_version = str_replace('{{course_venue_no_address}}', $scheduled_course_venue_name, $coms_template_html_version);
					$coms_template_html_version = str_replace('{{course_venue_information}}', $venue_access_information, $coms_template_html_version);
					$coms_template_html_version = str_replace('{{web_conferencing_information}}', $scheduled_course_web_conferencing_info, $coms_template_html_version);
					$coms_template_html_version = str_replace('{{additional_information}}', $scheduled_course_additional_info, $coms_template_html_version);
					$coms_template_html_version = str_replace('{{session_info}}', $session_info, $coms_template_html_version);
					$coms_template_html_version = str_replace('{{session_info_with_webconferencing}}', $session_info_with_webconferencing, $coms_template_html_version);
					$coms_template_html_version = str_replace("{{first_name}}", $name, $coms_template_html_version);
					$coms_template_html_version = str_replace("{{name}}", $name, $coms_template_html_version);
					$coms_template_html_version = str_replace("{{surname}}", $surname, $coms_template_html_version);
					$coms_template_html_version = str_replace("{{course_postcode}}", $course_postcode, $coms_template_html_version);
					$coms_template_html_version = str_replace("{{link_to_profile}}", $link, $coms_template_html_version);
					$coms_template_html_version = str_replace("{{course_minimum_attendees}}", $course_minimum_attendees, $coms_template_html_version);
					$coms_template_html_version = str_replace('{{course_whatthreewords}}', $venue_whatthreewords, $coms_template_html_version);
					
					$admin_email = pull_field('lead_preferences', 'db107096', "WHERE usergroup = {$_SESSION['usergroup']}");
					//if admin email is not set send to default in form_schools
					if (empty($admin_email)){
						$admin_email = pull_field('form_schools', 'db1118', "WHERE id={$_SESSION['usergroup']}");
					}
					$tutor_email_sql =  "
						SELECT group_concat(sis_course_tutors.db52608) as email
						FROM sis_course_schedule
						LEFT JOIN sis_scheduled_tutors ON sis_scheduled_tutors.rel_id = CAST(sis_course_schedule.id AS CHAR)
                        LEFT JOIN sis_course_tutors ON sis_scheduled_tutors.db59798 = CAST(sis_course_tutors.id AS CHAR)
						WHERE sis_course_schedule.id= {$course_schedule_id}

					";
					dev_debug($tutor_email_sql);
					$stmt3= $dbh->prepare($tutor_email_sql);
					$stmt3->execute();
					
					$t_emails = $stmt3->fetch(\PDO::FETCH_ASSOC);
					$tutor_emails = $t_emails['email'];
					$email_args=[
						'to'=> $admin_email,
						'cc' => $tutor_emails,
						'subject'=>$coms_template_subject_line,
						'text'=>$coms_template_html_version,
						'html'=> $coms_template_html_version,
						'template_id' => $select_email_template,
					];
					$emails->send($email_args);
				}
			}
			
			$scheduleCourseLink = website_url.'/admin/shortcourses/scheduled/'.$course_schedule_id.'?vw='.$course_username_id.'&ref='.$course_schedule_id.'&event=1';
			
			//notify admin and tutor that the course has reached limit
			if (!empty($maxAttendeesReached)){
				$coms_template_subject_line = 'Maximum Number of Attendees Reached';
				$disabled_notifications = pull_field("form_notifications_config", "db37245", "WHERE usergroup='" . $_SESSION['usergroup'] . "'");
				if (!in_array('alert_admin_on_booking_limit_reached', explode(',', $disabled_notifications))) {
					//message to admin
					$max_email_message = "Dear Admin

					The maximum number of attendees specified on the {$scheduled_course_name} course, starting on {$scheduled_course_start_date} at {$scheduled_course_start_time} has been reached.
					
					Link to course: <a href='{$scheduleCourseLink}' rel='noopener' target='_blank'>{$scheduled_course_name}</a>

					Regards
					
					Mind Recovery Net Team

					";
					
					//notify the admin that the applicant need to be notified by phone
					$admin_email = pull_field('form_schools', 'db1118', "WHERE id={$_SESSION['usergroup']}");
					
					$email_args = [
						'to' => $admin_email,
						'subject' => $coms_template_subject_line,
						'text' => $max_email_message,
						'html' => nl2br($max_email_message),
					];
					
					$emails->send($email_args);
				}
				
				// to tutors
				//get the tutors
				if (!in_array('alert_tutor_on_booking_limit_reached', explode(',', $disabled_notifications))) {
					$tutorSql = "
		                SELECT
							CONCAT(db52597,' ',db52598) as 'tutor_name',
							(select db119 from form_users where id =db52599) as 'tutor_email'
							
						FROM sis_scheduled_tutors
						INNER JOIN sis_course_tutors ON sis_course_tutors.id = db59798
						WHERE sis_scheduled_tutors.rel_id= {$course_schedule_id}
						AND (sis_scheduled_tutors.rec_archive IS NULL OR sis_scheduled_tutors.rec_archive = '')
						AND (sis_course_tutors.rec_archive IS NULL OR sis_course_tutors.rec_archive = '')
						AND sis_scheduled_tutors.usergroup={$_SESSION['usergroup']}
					";
					dev_debug($tutorSql);
					$sth9 = $dbh->prepare($tutorSql);
					$sth9->execute();
					$tutors = $sth9->fetchAll(\PDO::FETCH_ASSOC);
					foreach ($tutors as $tutor) {
						$tutor_email_message = "Dear Tutor

						The maximum number of attendees specified on the {$scheduled_course_name} course, starting on {$scheduled_course_start_date} at {$scheduled_course_start_time} has been reached.
						
						Regards
						
						Mind Recovery Net Team
						";
						
						$email_args = [
							'to' => $tutor['tutor_email'],
							'subject' => $coms_template_subject_line,
							'text' => $tutor_email_message,
							'html' => nl2br($tutor_email_message),
						];
						
						$emails->send($email_args);
						
					}
				}
				
			}
			
			
			if(!empty($over_booked)){
				//message to admin
				$over_booked_email_message = "Dear Admin

					The maximum number of attendees specified on the {$scheduled_course_name} course, starting on {$scheduled_course_start_date} at {$scheduled_course_start_time} has been exceeded.
					
					Link to course: <a href='{$scheduleCourseLink}' rel='noopener' target='_blank'>{$scheduled_course_name}</a>

					Regards
					
					Mind Recovery Net Team

				";
				$coms_template_subject_line = 'Maximum Number of Attendees Exceeded';
				
				
				//notify the admin that the applicant need to be notified by phone
				$admin_email = pull_field('form_schools', 'db1118', "WHERE id={$_SESSION['usergroup']}");
				
				$email_args = [
					'to' => $admin_email,
					'subject' => $coms_template_subject_line,
					'text' => $over_booked_email_message,
					'html' => nl2br($over_booked_email_message),
				];
				
				$emails->send($email_args);
			}
		}
			
    }
    return array($args, $mappings);

}

/**
 * @throws Exception
 * @throws DBALException
 */
function update_main_attendance_status($args, $static, $mappings): array
{
	$file = base_path.'admin/config.php';
	if(file_exists($file)){
		require_once($file);
	}
	$short_courses = new \ShortcoursesModel();
	$booking_args = [
		'attendee_id' => $args['record']
	];
	$short_courses->update_main_attendance_status($booking_args);
	return array($args, $mappings);
}

