<?php

namespace ug_107_custom;

function checklist_update_899($args, $static, $mappings)
{
    $dbh = get_dbh();
    if ($args["db94316"] == "yes") {
        $sql = "UPDATE chk_z_107_student_led_checklist SET db90725 = 'on' WHERE usergroup = " . $_SESSION["usergroup"] . " AND rel_id = " . $args['rel_id'];
        $sth = $dbh->prepare($sql);
        $sth->execute();
        //$args["db90725"] = "on";
    }
    if ($args["db94322"] == "yes") {
        $sql = "UPDATE chk_z_107_student_led_checklist SET db90728 = 'on' WHERE usergroup = " . $_SESSION["usergroup"] . " AND rel_id = " . $args['rel_id'];
        $sth = $dbh->prepare($sql);
        $sth->execute();
        //$args["db90728"] = "on";
    }
    if ($args["db94331"] == "Informal Offer") {
        $sql = "UPDATE chk_z_107_student_led_checklist SET db86807 = 'on' WHERE usergroup = " . $_SESSION["usergroup"] . " AND rel_id = " . $args['rel_id'];
        $sth = $dbh->prepare($sql);
        $sth->execute();
        //$args["db86807"] = "on";    
    }

    return array($args, $mappings);
}

function checklist_update_953($args, $static, $mappings)
{
    $dbh = get_dbh();
    if ($args["db94352"] == "yes") {
        $sql = "UPDATE chk_z_107_post_doc_checklist SET db94355 = 'on' WHERE usergroup = " . $_SESSION["usergroup"] . " AND rel_id = " . $args['rel_id'];
        $sth = $dbh->prepare($sql);
        $sth->execute();
        //$args["db94355"] = "on";
    }
    if ($args["db94370"] == "yes") {
        $sql = "UPDATE chk_z_107_post_doc_checklist SET db94379 = 'on' WHERE usergroup = " . $_SESSION["usergroup"] . " AND rel_id = " . $args['rel_id'];
        $sth = $dbh->prepare($sql);
        $sth->execute();
        //$args["db94379"] = "on";
    }
    return array($args, $mappings);
}

function checklist_update_896($args, $static, $mappings)
{
    $dbh = get_dbh();
    if ($args["db94313"] == "yes") {
        $sql = "UPDATE chk_z_107_student_phase_checklist SET db86783 = 'on' WHERE usergroup = " . $_SESSION["usergroup"] . " AND rel_id = " . $args['rel_id'];
        $sth = $dbh->prepare($sql);
        $sth->execute();
        // $args["db86783"] = "on";
    }
    if ($args["db90722"] == "Preferred" || $args['db90722'] == 'Reserve') {
        $sql = "UPDATE chk_z_107_student_phase_checklist SET db86786 = 'on' WHERE usergroup = " . $_SESSION["usergroup"] . " AND rel_id = " . $args['rel_id'];
        $sth = $dbh->prepare($sql);
        $sth->execute();
        //$args["db86786"] = "on";
    }
    return array($args, $mappings);
}

function checklist_update_854($args, $static, $mappings)
{
    $dbh = get_dbh();
    if ($args["db94310"] == "yes") {
        $sql = "UPDATE chk_z_107_default_checklist SET db90701 = 'on' WHERE usergroup = " . $_SESSION["usergroup"] . " AND rel_id = " . $args['rel_id'];
        $sth = $dbh->prepare($sql);
        $sth->execute();
        //$args["db90701"] = "on";
    }
    return array($args, $mappings);
}

function has_reviewers($args, $static, $mappings)
{
    $dbh = get_dbh();
    list($core_students_id, $core_students_rec_id, $core_students_usergroup, $core_students_rel_id, $core_students_first_name, $core_students_middle_name, $core_students_surname, $core_students_email_address, $core_students_telephone_number, $core_students_date_of_birth, $core_students_gender, $core_students_source_of_applicant, $core_students_cohort, $core_students_course_of_study, $core_students_level_of_entry, $core_students_country_of_origin, $core_students_application_status, $core_students_has_applied, $core_students_archive_record, $unique_id, $core_student_application_route, $core_students_cohort_intake, $core_students_deleted, $core_student_application_ucas, $core_student_ucas_pass, $core_students_course_of_study_id, $core_students_course_audio) = get_core_students($args['rel_id']);

    $checklist_name = pull_field("system_pages
            LEFT JOIN system_model_hooks ON system_pages.page_id = system_model_hooks.model_id
            LEFT JOIN system_cat ON system_cat.sys_cat_id = system_pages.project", "page_name", "WHERE system_pages.project='16' AND page_id = '" . $core_student_application_route . "' AND page_name LIKE '%z_107%' GROUP BY page_id");
    $checklist_name = "chk_" . $checklist_name;

//    if ($checklist_name == "chk_z_107_default_checklist") {
//        $sql = "UPDATE chk_z_107_default_checklist SET db86774 = 'on' WHERE usergroup = " . $_SESSION["usergroup"] . " AND rel_id = " . $args['rel_id'];
//        $sth = $dbh->prepare($sql);
//        $sth->execute();
//        //$args["db86774"] = "on";
//    }
    return array($args, $mappings);
}


/*INSERT INTO `system_model_hooks` (`model_id`, `hook_type`, `exec_order`, `script_path`, `namespace`, `function_name`, `static`, `mappings`) VALUES ('899', '0', '1', 'dir/client_107_custom.php', 'ug_107_custom', 'ug_107_custom', '{"ug":"107"}', '');*/