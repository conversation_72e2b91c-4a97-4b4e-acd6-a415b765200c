<?php
session_start();
include("../admin/inc/lib.inc.php");
chk_login(); //check if user is logged in

//--------------------------------------------------------------------------
// 1) Connect to mysql database
//--------------------------------------------------------------------------
$dbh = get_dbh();

//--------------------------------------------------------------------------
// 2) /***************** INSERT FUNCTION *****************/
//--------------------------------------------------------------------------
$mycomment = "Changed Applicant Details:" . $_REQUEST['my_comment'];
$record_id = $_REQUEST['record_id'];
$sql = "INSERT INTO dir_internal_notes (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id, db1129) VALUES ('" . random() . "', '" . session_info("uid") . "', '" . session_info("usergroup") . "', :record_id ,'" . custom_date_and_time() . "', '" . session_info("uid") . "', :my_comment)"; //query
$sth = $dbh->prepare($sql);
$sth->execute(array(
    "record_id" => $record_id,
    "my_comment" => $mycomment
)); 


