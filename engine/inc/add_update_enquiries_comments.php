<?php
session_start();
// This script will update and add core_students_meta data	 
include("../admin/inc/lib.inc.php");
chk_login(); //check if user is logged in
//--------------------------------------------------------------------------
// 1) Connect to mysql database
//--------------------------------------------------------------------------
$dbh = get_dbh();
//--------------------------------------------------------------------------
// 1) /***************** add record to database *****************/
//--------------------------------------------------------------------------


$text = $_POST['stud_additional_info_text'];
$submit = $_POST['stud_additional_info_text_submit'];
$id = $_GET['id'];
//$id = $_POST['meta_id'];

if ($submit) {
    if ($id) {
        $sql = "UPDATE lead_profiles SET db1044=:text_notes WHERE id=:id AND usergroup=:usergroup";
        $params = array(
            'id' => $id,
            'text_notes' => $text,
            'usergroup' => $_SESSION['usergroup']
        );
    } else {
        // $sql ="INSERT INTO dir_core_students_meta (username_id, rec_id,rel_id, usergroup, rec_lstup, rec_lstup_id, db49864) VALUES ('".random()."', '".session_info("uid")."', :rel_id,'".session_info("usergroup")."', '".custom_date_and_time()."', '".session_info("uid")."', :text_notes)"; //query
        // $params = array('text_notes'=>$text, 'rel_id' => $rel_id);
    }
    $sth = $dbh->prepare($sql);
    $result = $sth->execute($params);
    echo json_encode(array('message' => 'General note updated successfully.', 'success' => true, 'data' => $result));
} else {
    echo json_encode(array('message' => 'Kindly submit your request.', 'success' => false));
}
