<pre>
<?php

require('../admin/inc/lib.inc.php');

$dbh = get_dbh();

// Configure your imap mailboxes
$mailbox = array(
    'label' => "Leads",
    'mailbox' => '{mail.applicatalyst.com/notls/imap4}INBOX',
    'username' => "<EMAIL>",
    'password' => "applicat2012"
);

$insert_sth = $dbh->prepare("
    INSERT INTO lead_fwd_email (username_id, rec_id, usergroup,
        rel_id, db1751, db1752, db1753, db1754)
        VALUES (:rnd, 0, 0, 0, :sub, :msg, :head, :from_email)");

$stream = imap_open($mailbox['mailbox'], $mailbox['username'], $mailbox['password']);

if (!$stream) {
    error_log("Could not connect to: $mailbox[label]. Error: " . imap_last_error());
} else {
    //error_log('Fetching stuff coming from '.$school['subdomain'].'@applicatalyst.com');
    $emails = imap_search($stream, 'UNSEEN');
    // Debug stuff
    //if ($school['subdomain'] === 'other') {
    //    $emails = imap_search($stream,'UNSEEN FROM "<EMAIL>"');
    //    error_log('fetching email <NAME_EMAIL>');
    //}

    // Instead of searching for this week's messages, you could search
    // for all the messages in your inbox using: $emails = imap_search($stream, 'ALL');

    if (!count($emails)) {
        echo "No e-mails found.";
    } else {
        // If we've got some email IDs, sort them from new to old and show them
        rsort($emails);

        foreach ($emails as $email_id) {
            // Fetch the email's overview and show subject, from and date.
            $headers = imap_fetchheader($stream, $email_id);
            $overview = imap_fetch_overview($stream, $email_id);
            $message = imap_fetchbody($stream, $email_id, 1);

            // Need to get the from email
            $parsed_headers = imap_rfc822_parse_headers($headers);
            //print_r($parsed_headers);
            $from_obj = $parsed_headers->from[0];
            $from_email = $from_obj->mailbox . '@' . $from_obj->host;

            $tmp = array(
                'rnd' => random(),
                'sub' => $overview[0]->subject,
                'msg' => $message,
                'head' => $headers,
                'from_email' => $from_email);
            //print_r($tmp);

            $insert_sth->execute($tmp);

            // Mark as seen
            if (!imap_setflag_full($stream, $email_id, "\\Seen"))
                error_log('Could not set message status as seen');
        }
    }

    // Close our imap stream.
    imap_close($stream);
}
?>
</pre>
