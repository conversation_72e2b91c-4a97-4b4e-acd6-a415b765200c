<?php
// UPDATE EMAIL LOGS AND SAVE DATA AS SEPAREATE
include_once("../admin/inc/lib.inc.php");
$dbh = get_dbh();

$contents = explode("|", pull_field("core_letter_library", "concat(db1487,'|',db1488)", " WHERE id = '$_GET[id]' AND usergroup='$_SESSION[usergroup]' "));
$message_subject = $contents[0];
$content = $contents[1];

$intake_data = explode("/", pull_field("dir_cohorts a,core_students b", "concat(db1678,'/',db1679)", "  WHERE a.id=b.db1682 AND b.id = '$_GET[ref]' AND b.usergroup='$_SESSION[usergroup]' "));
$university_data = explode("|", pull_field("core_course_uni_link a,core_students b, core_universities c", "CONCAT_WS('|',db20163,'|',db27605,'|',db33573,'|',db27604,'|',db29339,'|',db27606,'|',db20164)", "WHERE a.db31916=b.db889 
AND c.id =a.db31917 AND b.id = '$_GET[ref]' AND b.usergroup='$_SESSION[usergroup]' "));

/*
db20163,'|',db27605,'|',db33573,'|',db27604,'|',db29339,'|',db27606,'|',db20164

db20163 - name
db27605 - Address 1
db33573 - Address 2
db27604 - County/State
db29339 - City
db27606 - Postcode/ZipCode
db20164 - country
*/

print_r($university_data);

$university_name = $university_data[0];

if ($university_data[1]) {
    $university_address .= ', ' . $university_data[1];
}
if ($university_data[2]) {
    $university_address .= ', ' . $university_data[2];
}
if ($university_data[3]) {
    $university_address .= ', ' . $university_data[3];
}
if ($university_data[11]) {
    $university_address .= ', ' . $university_data[11];
}
if ($university_data[14]) {
    $university_address .= ', ' . $university_data[14];
}
if ($university_data[17]) {
    $university_address .= ', ' . $university_data[17];
}

$university_address = trim($university_address, ",");

$content = str_replace("{{course_intake_start_date}}", format_date("j F Y", $intake_data[0]), $content);
$content = str_replace("{{course_intake_end_date}}", format_date("j F Y", $intake_data[1]), $content);
$content = str_replace("{{student_university_name}}", $university_name, $content);
$content = str_replace("{{date}}", format_date("j F Y", custom_date_and_time()), $content);
$content = str_replace("{{university_full_address}}", $university_address, $content);


dev_debug('intake_data = ' . json_encode($intake_data));
$content = str_replace("{{course_intake_start_date}}", format_date("j F Y", $intake_data[0]), $content);
$content = str_replace("{{course_intake_end_date}}", format_date("j F Y", $intake_data[1]), $content);
$content = str_replace("{{student_university_name}}", $university_name, $content);
$content = str_replace("{{date}}", format_date("j F Y", custom_date_and_time()), $content);

$content1 = addslashes(email_template_replace_values_from_db($content, $_GET['ref']));

echo $content1;
