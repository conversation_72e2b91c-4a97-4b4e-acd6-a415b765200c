<?php
require_once(base_path . '/engine/modules/inc_booking_widget.php');
?>

<!-- Latest compiled and minified JavaScript -->
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js" integrity="sha384-Tc5IQib027qvyjSMfHjOMaLkfuWVxZxUPnCJA7l2mCWNIpG9mGCD8wGNIcPD7Txa" crossorigin="anonymous"></script>
<link rel="stylesheet" href="/admin/assets/css/font-awesome-4.7.0/css/font-awesome.min.css">
<script src='https://unpkg.com/vue/dist/vue.js'></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/vuejs-paginate/2.1.0/index.js" integrity="sha512-4+cTzuthboCP2b/N/uQrGFJUgKtwP6CQ/YGAUi/+NLI+fdDAzl/z7tPohUf1KHGoNJcOaziyitKdkj77J0dq9A==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js" integrity="sha512-T/tUfKSV1bihCnd+MxKD0Hm1uBBroVYBOYSk1knyvQ9VyZJpc/ALb4P0r6ubwVPSGB2GvjeoMAJJImBG12TiaQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src='https://unpkg.com/v-calendar'></script>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<div id="app" v-cloak>
    <div class="page_body catalogue_home" :style="{position:'relative', top: 0}">
        <div class="row padding_top_30">
        <div class="col-sm-12">
            <section class="search-sec">
                <h1 class="white_text"><?=terminology('Course', "http://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]")?> Search</h1>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="row">
                            <div class="col-sm-12 p-0">
                                <form action="search" v-on:submit="submitForm($event)">
                                    <div id="custom-search-input">
                                        <div class="row" style="padding: 0;">
                                            <div class="col-lg-3 col-md-3 col-sm-12 p-0 margin_top_14">
                                                <input id="search" name="query" type="text" class="form-control search-slt" placeholder="Search... " v-model="searchQuery" v-on:keyup="fetch_results($event)" aria-autocomplete="both" aria-haspopup="false" autocapitalize="off" autocomplete="off" autocorrect="off" autofocus="" role="combobox" spellcheck="false" title="Search" value="" aria-label="Search">
                                                <div class="instant-results">
                                                    <div class="results_container">
                                                        <ul class="list-unstyled result-bucket">
                                                            <li v-for="course in search_results" class="result-entry" data-suggestion="Target 1" data-position="1" data-type="type" data-analytics-type="merchant">
                                                                <a :href="'programmes/'+course.id" class="result-link">
                                                                    <div class="media">
                                                                        <div class="media-left">
                                                                            <picture content="image">
                                                                                <source :srcset="course.image">
                                                                                <img class="media-object" data-automation="listing-hero-image" alt="">
                                                                            </picture>
                                                                        </div>
                                                                        <div class="media-body">
                                                                            <h4 class="media-heading">{{course.title}}</h4>
                                                                            <p v-html="course.description"></p>
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-3 col-sm-12 p-0 margin_top_14">
                                                <v-date-picker v-model="range" is-range>
                                                    <template v-slot="{ inputValue, inputEvents }">
                                                        <div class="flex justify-center items-center input-group">
                                                            <input
                                                                    :value="inputValue.start"
                                                                    v-on="inputEvents.start"
                                                                    class="border px-2 py-1 w-32 rounded focus:outline-none focus:border-indigo-300 form-control search-slt"
                                                            />
                                                            <div class="input-group-addon">to</div>
                                                            <input
                                                                    :value="inputValue.end"
                                                                    v-on="inputEvents.end"
                                                                    class="border px-2 py-1 w-32 rounded focus:outline-none focus:border-indigo-300 form-control search-slt"
                                                            />
                                                        </div>
                                                    </template>
                                                </v-date-picker>
                                            </div>
                                            <div class="col-lg-3 col-md-3 col-sm-12 p-0 margin_top_14">
                                                <select class="form-control search-slt" id="exampleFormControlSelect1">
                                                    <option>Select Course Level</option>
                                                    <option v-for="level in course_level_counters">{{level.title}}</option>
                                                </select>
                                            </div>
                                            <div class="col-lg-3 col-md-3 col-sm-12 p-0 margin_top_14">
                                                <button type="submit" class="btn btn-primary wrn-btn">Search</button>
                                            </div>

                                        </div>
                                    </div>
<!--                                    <div class="row white_text" style="padding-top: 10px ">-->
<!--                                        <div class="col-sm-12">-->
<!--                                            <div v-if="show_filters" class="row">-->
<!---->
<!--                                            </div>-->
<!--                                            <a href="#" class="white_text" v-on:click="show_filters = !show_filters">{{show_filters?"Show Less ... ":"More Filters ... "}} <span class='fa ' :class="{'fa-arrow-up':show_filters, 'fa-arrow-down': !show_filters }"></span></a>-->
<!--                                        </div>-->
<!--                                    </div>-->
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <div class="clearfix"></div>
            <h2>Course Categories</h2>
            <div class="course_results" >
                <div class="row courses_row">
                    <div class="col-sm-4 small_padding">
                    <a class="course_link" href="all">
                        <div class="panel tile">
                        <div class="course_image" ><span class="fa fa-briefcase"></span> </div>
                        <div class="main_info">
                            <div class="course_level center"><h4>All course categories</h4></div>
                            <div class="catalogue_course_title center">All courses</div>
                        </div>
                    </div>
                    </a>
                        <div class="clearfix hidden-md hidden-lg"></div>
                    </div>
                    <div class="col-sm-4 small_padding" v-for="course_type in course_level_counters">
                    <a class="course_link" :href="((course_type.type==='short' || course_type.type==='yes')?'short_courses':(course_type.type==='online')?'online_courses':'programmes') +'?level='+course_type.id">
                        <div class="panel tile">
                        <div class="course_image" ><span class="fa" :class="{ 'fa-clock-o': (course_type.type==='short' || course_type.type==='yes'), 'fa-rss': course_type.type==='online', 'fa-plane': course_type.type==='visa', 'fa-graduation-cap': !['visa', 'online', 'yes', 'short'].includes(course_type.type) }"></span> </div>
                        <div class="main_info">
                            <div class="course_level center"><h4>{{(course_type.type==='short' || course_type.type==='yes')?'Short Courses':(course_type.type==='online')?'Online Courses':'Standard Courses'}}</h4></div>
                            <div class="catalogue_course_title center">{{course_type.title}}</div>
                        </div>
                    </div>
                    </a>
                        <div class="clearfix hidden-md hidden-lg"></div>
                    </div>
                </div>
            </div>
            <h2>Latest Courses</h2>
            <div class="course_results padding_top_15" >
                <div class="row courses_row">
                    <div class="col-sm-4 col-xs-12 small_padding" v-for="course in latest_courses">
                        <a class="course_link" :href="((course.type==='short' || course.type==='yes')?'short_courses':(course.type==='online')?'online_courses':'programmes') + '/'+course.id">
                            <div class="panel">
                                <span class="fa fa-heart-o" style="color:#fff; font-weight:600; position: absolute; right:15px; top:8px;"></span>
                                <div class="course_image" style='height:200px; width:100%;'
                                     v-bind:style="{background: 'url(' + course.image + ') no-repeat center', backgroundSize: 'cover'}"> </div>
                                <div class="main_info">
                                    <div class="course_level center" v-if="theme_options.show_course_levels==='on'">{{course.course_level}}</div>
                                    <div class="catalogue_course_title center" v-if="theme_options.show_course_title==='on'">{{course.title}}</div>
                                    <!--                                        <div class="course_info course_location"  v-if="theme_options.show_course_locations==='on' && course.has_locations"><label>Location:</label> <span v-if="course.locations.length>0">{{course.locations[0].city}}, {{course.locations[0].country}} <span v-if="course.locations.length>1"> + {{course.locations.length-1}} more locations</span></span><span v-if="course.locations.length===0">No Location Specified</span></div>-->
                                    <!--                                        <div class="course_info course_intakes" v-if="theme_options.show_intakes==='on' && course.has_intakes"><label>Intakes:</label> <span v-if="course.intakes.length>0">{{course.intakes[0].title}} <span v-if="course.intakes.length>1"> + {{course.intakes.length-1}} more intakes</span></span><span v-if="course.intakes.length===0">No Intake Available</span></div>-->
                                    <!--                                        <div class="course_info course_slots" v-if="theme_options.show_course_sessions==='on' && course.has_slots"><label>Available Sessions:</label> <span v-if="course.slots.length>0">{{course.slots[0].event_date}} <span v-if="course.slots.length>1"> + {{course.slots.length-1}} more sessions</span></span><span v-if="course.slots.length===0">No Session Available</span></div>-->
                                    <div class="course_info course_price" v-if="theme_options.show_course_sessions==='on'" ><label>Price:</label> {{course.fee}}</div>
                                </div>
                            </div>
                        </a>
                        <div class="clearfix hidden-md hidden-lg"></div>
                    </div>
                </div>
                <a href="search/latest" class="btn btn-info pull-right">More ...</a>
            </div>
        </div>
    </div>
    </div>
</div>
<div class="popup_message hide">Page info saved successfully</div>
<script src="https://cdn.jsdelivr.net/gh/xcash/bootstrap-autocomplete@v2.3.7/dist/latest/bootstrap-autocomplete.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.4/lodash.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/axios/0.21.1/axios.min.js" integrity="sha512-bZS47S7sPOxkjU/4Bt0zrhEtWx0y0CRkhEp8IckzK+ltifIIE9EMIMTuT/mEzoIMewUINruDBIR/jJnbguonqQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script>


    function debounce(func, wait, immediate) {
        var timeout;
        return function() {
            var context = this, args = arguments;
            var later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            var callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }
    function formatDate (date) {
        var d = new Date(date),
            month = '' + (d.getMonth() + 1),
            day = '' + d.getDate(),
            year = d.getFullYear();

        if (month.length < 2)
            month = '0' + month;
        if (day.length < 2)
            day = '0' + day;

        return [year, month, day].join('-');
    }

    new Vue({
        el: '#app',
        data() {
            return <?php echo json_encode($data)?>
        },
        methods: {
            displayNext(obj) {
                console.log(obj);
            },
            log: function () {
                console.log(this);
            },
            filter_data: function (by, value) {
                let model = this;
                let base_url = new URL(window.location.href);
                base_url.searchParams.append('json', 1)
                base_url.searchParams.append(by, value)
                axios.get(base_url)
                    .then(function (response) {
                        model.courses = response.data;
                    })
                    .catch(function (error) {
                        console.log(error);
                    });
            },
            submitForm: function (event) {
                event.preventDefault();
                window.location.href = this.base_url(`search?query=${this.searchQuery}&start=${formatDate(this.range.start)}&end=${formatDate(this.range.end)}`)
            },
            base_url: function (path=""){

                if(path){
                    if(path.charAt( 0 )==="/"){
                        path = path.slice(1);
                    }
                }

                let domain = "<?php echo $_SERVER['HTTP_HOST'];?>";
                if(domain.includes('heiapplylocal.co.uk') !== false) {
                    return 'http://'+domain+'/catalogue/'+path;
                }else{
                    return 'https://'+domain+'/catalogue/'+path;
                }
            },
            fetch_results: function(event) {
                let self = this;
                let url = new URL(this.base_url('search'));

                url.searchParams.append('json', 1)
                url.searchParams.append('query', event.target.value)
                // clear timeout variable
                if(this.timeout)clearTimeout(this.timeout);

                this.timeout = setTimeout(function () {
                    axios.get(url)
                        .then(function (response) {
                            self.search_results = response.data;
                            hide_message()
                        })
                        .catch(function (error) {
                            console.log(error);
                        });
                    console.log('searching:', event.target.value);
                }, 300);

            },
            pop_message: function(MSG,timeout=3000){
                console.log('popping');
                $('.popup_message').html(MSG).removeClass("hide");
                if(timeout){
                    setTimeout(function() { $('.popup_message').addClass('hide'); }, timeout);
                }
            },
            fetchFromUrl: function (url) {
                this.pop_message("Fetching ...")
                axios.get(url)
                    .then(function (response) {
                        model.courses = response.data;
                        hide_message()
                    })
                    .catch(function (error) {
                        console.log(error);
                    });
            },
            formatDate: function (date) {
                var d = new Date(date),
                    month = '' + (d.getMonth() + 1),
                    day = '' + d.getDate(),
                    year = d.getFullYear();

                if (month.length < 2)
                    month = '0' + month;
                if (day.length < 2)
                    day = '0' + day;

                return [year, month, day].join('-');
            }
        },
        computed: {
            courseChunks() {
                return _.chunk(Object.values(this.course_levels), 3);
            }
        },
        created() {
            // `this` points to the vm instance
            console.log(this)
            this.range = {
                start: new Date(this.start_date),
                end: new Date(this.end_date),
            }
            this.top_height = document.getElementById("topbar").offsetHeight;
        }
    })

    $(document).ready(function () {
        //Hover Menu in Header
        $('ul.nav li.dropdown').hover(function () {
            $(this).find('.mega-dropdown-menu').stop(true, true).delay(200).fadeIn(200);
        }, function () {
            $(this).find('.mega-dropdown-menu').stop(true, true).delay(200).fadeOut(200);
        });
        //Open Search
        $('#search').click(function (event) {
            $(".instant-results").fadeIn('slow').css('height', 'auto');
            event.stopPropagation();
        });

        $('body').click(function () {
            $(".instant-results").fadeOut('500');
        });
    });

</script>