


    $scope.dimensions = <?php echo json_encode($data['dimensions']); ?>;
	  $scope.filters = <?php if($filter['description']){ echo json_encode($filter['description']); }else{ echo "[]";} ?>;
	  $scope.main_filter_types = <?php if($data['filter_types']){ echo json_encode($data['filter_types']); }else{ echo "[]";} ?>;
	  $scope.filter_types = <?php if($filter_types_json){ echo $filter_types_json;}else{ echo "[]";} ?>;
		$scope.filter = <?php if($filter){ echo json_encode($filter);}else{ echo "[]";} ?>;
		$scope.default_filters_list = <?php if($default_filters_list){ echo json_encode($default_filters_list);}else{ echo "[]";} ?>;

	  /* Load Filters
    ----------------------------------------------*/
    $scope.show_filter_bar = 1;
    $scope.submit_filters_through_ajax = <?php echo $data['submit_filters_through_ajax']; ?>;
 		filters_args = {
		  'search': '<?php echo $_REQUEST['search']; ?>',
		  'value': '<?php echo $_REQUEST['search']; ?>',
      'filter': $scope.filter,
      'submit_filters_through_ajax':$scope.submit_filters_through_ajax,
		 <?php if($data['hide_filters']){ ?> 'hide_filters':1 <?php } ?>
		};	
 		var filters_wrap_html = filter_html(filters_args);

 		angular.element(document.getElementById('filters_wrap')).append($compile(filters_wrap_html)($scope));
	  

 		/* View Filters
  	----------------------------------------------*/
	  $scope.view_filters = function view_filters() {
      $scope.create_temp_filters();
      
	  	if(typeof $scope.filters !== 'undefined') {
		  	if($scope.filters.length<1){
		  		$scope.add_filter();
		  	}
	  	}else{
	  		$scope.add_filter();
      }
      console.log("||||1");
      $("#filters_modal").modal("show");
      console.log("||||2");
	  }

	  $scope.delete_filter = function delete_filter(index) {
	  	$scope.filters.splice(index, 1); 
	  	$scope.create_temp_filters();
	  	$scope.apply_filters();
  		return false; 
	  }

	  $scope.delete_temp_filter = function delete_temp_filter(index) {
	  	$scope.edit_filters.splice(index, 1); 
  		return false; 
	  }

	  
	  $scope.get_field_options = function get_field_options(filter) {
      console.log("||||3");
	  	if(filter.type=="checklist_id"){
	  		$http({
			    url: "ajax/students.php",
			    method: "POST",
			    data: {action:"get_checklist_stage",route_id:filter.option}
				}).success(function(data, status, headers, config) {	
					
					angular.forEach($scope.main_filter_types, function(type){
		  			if(type.id=="application_stage"){
		  				// console.log(type.id+"=="+"application_stage");
		  				type.option = "";
		  				type.options = data;
		  			}
				  });


				  angular.forEach($scope.edit_filters, function(fi){
		  			if(fi.type=="application_stage"){
		  				fi.option = "";
		  				fi.options = data;
		  			}
				  });
				}).error(function(data, status, headers, config) {
			    
				});
	  	}
	  }


	  $scope.filter_type_info = function filter_type_info(filter_type) {
      console.log("||||4");
	  	ftype = "";
	  	angular.forEach($scope.main_filter_types, function(type){
  			if(type.id==filter_type){
  				ftype = type;
  			}
      });
      console.log("||||41");

		  // console.log("ftype");
		  // console.log(ftype);

		  if(!ftype){
		  	angular.forEach($scope.filter_types, function(type){
		  		// console.log(type.id+"---"+filter_type);
	  			if(type.id==filter_type){
	  				ftype = type;
	  			}
			  });
      }
      console.log("||||42");
		  return ftype;
	  }

	  $scope.option_title = function option_title(option,filter) {
      console.log("||||5");
	  	option_name = "";
	  	if(filter.options){
		  	angular.forEach(filter.options, function(option_info){
	  			if(option_info.value==option){
	  				option_name = option_info.label;
	  			}
			  });
	  	}

		  return option_name;
	  }

	  $scope.filter_title = function filter_title(filter) {
      type_info = $scope.filter_type_info(filter.type);
      if(type_info==""){
        type_info = {}
      }
		  return type_info.title;
		}


		
		$scope.show_filter_dropdown = function show_filter_dropdown(filterIndex) {
      console.log("||||7");
			$(".filter_select#"+filterIndex+" ul").toggle();
			$(".filter_select#"+filterIndex+" ul input").focus();
		}

		$scope.apply_selected_filter = function apply_selected_filter(filterIndex,itemChoosen) {
			$scope.edit_filters[filterIndex].type = itemChoosen.id;
			$scope.fix_filter_options($scope.edit_filters[filterIndex]);
			$(".filter_select#"+filterIndex+" ul").hide();
			$scope.edit_filters[filterIndex].filter_search_keyword = "";
			$scope.$apply();
		}

	  $scope.fix_filter_options = function fix_filter_options(filter) {	
      type_info = $scope.filter_type_info(filter.type);
      
	  	// console.log("info", type_info);
	  	
	  	filter.value = "";
	  	filter.option = "";
	  	filter.sql = type_info.sql;
	  	if(typeof type_info.extra !== 'undefined') {
		  	filter.extra = type_info.extra;
		  	filter.operator_type = 'Exactly matching' ;
			}else{
				delete filter.extra;
      }
      

			if(typeof type_info.options !== 'undefined') {
		  	filter.options = type_info.options;
			}else{
				delete filter.options;
			}

			if(typeof type_info.operators !== 'undefined') {
		  	filter.operators = type_info.operators;
			}else{
				delete filter.operators;
      }
      

			//Get dynamic listing
			if(filter.sql){

				$scope.form_data = {action: 'get_dynamic_field_values', sql: filter.sql }
		  	$("#SOMETHING").html("Loading...");

				$http({
			    url: "ajax/students.php",
			    method: "POST",
			    data: $scope.form_data
				}).success(function(data, status, headers, config) {
					filter.options = data;
				}).error(function(data, status, headers, config) {
			    
				});

			}

	  }


	  $scope.validate_operator_value = function validate_operator_value(filter) {
	  	if(filter.operator_type!='Is empty'){
	  		filter.value = "";
	  	}
	  }

	  $scope.add_filter = function add_filter() {
      if(!$scope.edit_filters.length>0){
        $scope.edit_filters = [];
      }
      var new_field = {type: '',title:'',status:'',join:'and'};
      $scope.edit_filters.push(new_field);

	  }

	  
	  $scope.create_temp_filters = function create_temp_filters() {      
	  	$scope.edit_filters = angular.copy($scope.filters);
	  	fcount = 1;
	  	angular.forEach($scope.edit_filters, function(filter){

	  		// console.log("||||");
		  	// console.log(filter);


		  	if(!filter.join){
		  		filter.join = 'and';
		  	}

		  	if(fcount==1){
		  		filter.join = 'and';
		  	}
		  	fcount++;
		  });
	  }

	  $scope.apply_filters = function apply_filters() {
	  	if(!$scope.submit_filters_through_ajax){
        pop_message('Loading...',false);
      }

	  	angular.copy($scope.edit_filters, $scope.filters);
  		angular.forEach($scope.filters, function(filter){
		  	filter.status = 'active';
		  	//filter.options = '';
		  	
		  	filter_title = "";
		  	angular.forEach($scope.dimensions[0]['All Fields'], function(option_info){
	  		
		  		if(filter.type==option_info.id){
		  			filter_title = option_info.title;
		  		}
		  	});
		  	filter.title = filter_title;
		  });

  		// console.log("DDDD");
	  	// console.log(filter);
	  	// console.log($scope.dimensions[0]['All Fields']);
	  	// angular.forEach(dimensions[0]['All Fields'], function(option_info){
	  	// 	if(filter.id==option){
	  	// 		option_name = option_info.label;
	  	// 	}
	  	// });

		  
	  	

  		// return false;

		  $("#filters_modal .btn-primary").html("Loading...").addClass("disabled");

		  // console.log($scope.filters);
		  // return false;
		  // return false;
		  var filters_json = JSON.stringify($scope.filters);
		  
		  $("#main_search_form_filters").val(filters_json);
      $("#main_search_form_action").val("update_filter");
      $("#filter_page").val("<?php echo $data['filter_page_name'];?>");
      if($scope.submit_filters_through_ajax){
        // var fdata = $("#main_search_form").serializeArray();
        var fdata = $('div#main_search_form :input');
        console.log(fdata);
        // data = fdata;
        
        var data = {};
        $.map(fdata, function(n, i)
        {
  
          data[n.name] = $(n).val();
        });
        console.log(data);

        // 
        // $(fdata).each(function(index, obj){
        //     data[obj.name] = obj.value;
        // });

        $http({
          url: "<?php echo base_url('settings'); ?>",
          method: "POST",
          data: data
        }).success(function(data, status, headers, config) {
          pop_message('Filters Saved');
          $("#filters_modal .btn-primary").html("Apply filters").removeClass("disabled");
        }).error(function(data, status, headers, config) {
          
        });
      }else{
        $("#main_search_form").submit();
      }
		  
	  }

	  	
	  $scope.apply_fields = function apply_fields(dimension) {
	  	$scope.form_data = {fields: $scope.dimensions, action: 'set_session_student_fields', page:'<?php echo $data['filter_page_name'];?>', filter_id:'<?php echo $this->filter['id'];?>' }
	  	$("#dimensions_dropdown .bottom_apply_button .btn").html("Loading...");

			$http({
		    url: "ajax/students.php",
		    method: "POST",
		    data: $scope.form_data
			}).success(function(data, status, headers, config) {
				window.location.replace(location.href);
			}).error(function(data, status, headers, config) {
		    
			});
	  }


	  $scope.save_filters = function save_filters(dimension) {
	  	$scope.form_data = {id:$scope.filter.id, page:'<?php echo $data['filter_page_name'];?>', title: $scope.filter_name, filters: $scope.filters, action: 'add_new_filter' }
	  	$("#save_filter_modal .btn-primary").html("Loading...").addClass("disabled");
	  	$("#new_filter").val("true");
			$http({
		    url: "ajax/students.php",
		    method: "POST",
		    data: $scope.form_data
			}).success(function(data, status, headers, config) {
				window.location.replace(location.href);
			}).error(function(data, status, headers, config) {
		    
			});
	  }

	  $scope.add_dimension = function add_dimension(dimension) {
	  	if(dimension.active==1){
	  		dimension.active=0;
	  	}else{
	  		dimension.active=1;
	  	}
		};


	$scope.download_dynamic_file = function download_dynamic_file($url) {
    console.log("||||17");
			$http({
		    url: $url,
		    method: "GET",
			}).success(function(data, status, headers, config) {
				var a = document.createElement('A');
				a.href = data.href;
				a.target ="_blank";
				a.download = true;
				// console.log(data);
				document.body.appendChild(a);
				a.click();
				document.body.removeChild(a);
			}).error(function(data, status, headers, config) {
		    
			});
	  }	

		


	$(document).ready(function(){

		$('#dimensions_dropdown').click(function(e) {
		    e.stopPropagation();
		});

		$('#export_btn').click(function(e) {
		    $(this).html("Loading...").addClass("disabled");
		});
		$('#export_to_ukvi_btn').click(function(e) {
			$(this).html("Loading...").addClass("disabled");
		});

		// $('body').on('click', '.dynamic_download', function(e){
		// 	///e.preventDefault();
		// 	var $scope = angular.element($('[ng-controller="demo"]')).scope();
		// 	///$scope.download_dynamic_file($(this).closest('a').attr('href'));
		// });

		jQuery(".main_table .table_wrap").on('scroll', function() {
			that = jQuery(this);
		  if(that.scrollLeft() >= 1) {
		  	$("#applicants_table .shadow").show();
			}else{
				$("#applicants_table .shadow").hide();
			}
		});

		var frozen_fields_width = $("#applicants_table .pics").width();
		$("#applicants_table .main_table").css({'margin-left':frozen_fields_width+"px"});
		$("#applicants_table .shadow").css({'left':frozen_fields_width+"px"});


		//$("#filters_modal").modal("show");		
        $(document).on("click","a[title='Copy Link']", function (e) {
        	e.preventDefault();
		    var hrefval = $( this).attr("href");
		 	var copiedHref = $('<input>').val(hrefval).appendTo('body').select();
		 	document.execCommand('copy');
		});

	});