<div class="filter_tool_bar_row" style="margin-bottom: 20px;">
    <span id="location" class="hidden" data-href="<?php echo $data['location']; ?>"
          data-cohort="<?php echo $data['cohort']; ?>"
          data-filter="<?php echo !empty($data['filter']['id']) ? $data['filter']['id'] : ''; ?>"></span>
    <div class="filter_tool_bar" style="margin-right: 0;">
        <div class="btn-group" role="group" style="margin-right: 2px;">
            <button type="button" class="btn btn-lg btn-default dropdown-toggle" data-toggle="dropdown"
                    aria-haspopup="true" aria-expanded="false">
                Cohort <?php echo "(" . $data['cohort'] . ")"; ?>
                <span class="caret"></span>
                <span class="sr-only">Toggle Dropdown</span>
            </button>
            <ul class="dropdown-menu cohort_list">
                <li><a href="?cohort=all">All</a></li>
                <?php foreach ($data['cohorts'] as $cohort) { ?>
                    <li><a href="?cohort=<?php echo $cohort['title']; ?>"><?php echo $cohort['title']; ?></a></li>
                <?php } ?>
            </ul>
        </div>

        <div class="btn-group" role="group" style="margin-right: 2px;">
            <button type="button" class="btn btn-lg btn-default dropdown-toggle" data-toggle="dropdown"
                    aria-haspopup="true" aria-expanded="false">
                Sort |
                <span class="caret"></span>
                <span class="sr-only">Toggle Dropdown</span>
            </button>
            <?php

            if (isset($_GET['filter'])) {
                $filter = "&filter=" . $_GET['filter'];
            } else {
                $filter = "";
            }
            ?>
            <ul class="dropdown-menu cohort_list">
                <li><a href="?cohort=<?php echo $data['cohort'] . $filter; ?>">None</a></li>
                <?php foreach ($data['sort_by'] as $sort) { ?>
                    <li>
                        <a href="?cohort=<?php echo $data['cohort']; ?>&order=<?php echo $sort['value'] . $filter; ?>">
                            <?php if (strpos($sort['value'], "asc")) { ?>
                                <i class="fa fa-arrow-up"></i>
                            <?php } else { ?>
                                <i class="fa fa-arrow-down"></i>
                            <?php } ?>
                            <?php echo $sort['title']; ?></a></li>
                <?php } ?>
            </ul>
        </div>
        <?php if (!empty($data['show_export_button'])) { ?>
            <a href="?export=1" class="btn btn-default btn-lg">Export</a>
        <?php } ?>
    </div>
    <div class="filter_tool_bar">
        <section id="filters_wrap" style="width:660px;">
            <div class="top_filter" style="padding-bottom: 10px;">
                <form method="get" id="main_search_form" action="<?php echo request()->fullUrl() ?>">
                    <div class="input-group">
                        <div class="input-group-btn">
                            <button type="button" class="btn btn-default" v-on:click="view_filters"
                                    style="height: 40px;">
                                Add
                                Filter <span class="caret"></span></button>
                        </div>
                        <input type="text" class="form-control searchtext" placeholder="Start typing to search..."
                               name="search"
                               value="<?php echo request()->get('search') ?>"></div>
                </form>
                <div id="filters_tabs" v-if="filters.length && filters[0].title!=''">
                    <!-- ngRepeat: filter in filters -->
                    <div class="filter" v-for="(filter,index) in filters" v-if="filter.title!=''">
                        {{filter.title}}
                        <span>{{ filter.operator_type }}</span>
                        <span v-if="filter.operator_type=='Containing'||filter.operator_type=='Exactly matching'">{{ filter.value }}</span>
                        <i class="fa fa-times" aria-hidden="true" v-on:click="delete_filter(index)"></i>
                    </div>
                    <div class="filter" v-on:click="view_filters">
                        <span class="glyphicon glyphicon-plus-sign" aria-hidden="true"></span>Add filter
                    </div>
                </div>
                <div class="clearfix"></div>
            </div>
        </section>
    </div>
    <div class="filter_tool_bar pull-right" style="margin-right: 0; margin-top: 0;">
        <div class="btn-group filter_tool_bar_controls top_control" v-if="view_selected_intakes" role="group"
             style="margin-right: 2px;">
            <a href="#" class="btn btn-lg" v-on:click="show_selected_tiles" style="background: #9e9e9e ;color: #fff;">Show
                Records </a>
            <a href="#" class="btn btn-lg" v-on:click="clear_selected_tiles" style="background: #29b6f6 ;color: #fff;">Reset
                Selection</a>
        </div>
        <div class="btn-group filter_tool_bar_controls bottom_control" v-if="view_selected_intakes==false" role="group"
             style="margin-right: 2px;">
            <a href="#" class="btn btn-lg" v-on:click="view_selected_tiles" style="background: #9e9e9e ;color: #fff;">View
                Selected</a>
            <a href="#" class="btn btn-lg" v-on:click="select_all_tiles" style="background: #29b6f6 ;color: #fff;">Select
                All</a>
        </div>
    </div>
</div>