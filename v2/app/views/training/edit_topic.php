<?php
/**
*
* @name         Edit Topic
* @description  renders page for editing a topic/article on the structured tutorials pages
* <AUTHOR>
*
*/
?>
<link rel="stylesheet" href="<?php echo $this->base_url();?>assets/css/select2.min.css">
<script src="<?php echo $this->base_url();?>assets/js/select2.min.js"></script>
<script type="text/javascript" src="<?php echo engine_url; ?>/tools/tinymce4/tinymce.min.js"></script>
<script type="text/javascript" src="<?php echo engine_url; ?>/tools/tinymce4/tinymce_script_sml.js"></script>

<script>
app.controller('training_manager', ['$scope', '$filter', '$http', 'fileUpload', function($scope, $filter, $http, fileUpload){
    $scope.root = <?php echo json_encode($data['root']); ?> ;
    $scope.new_topic = <?php echo json_encode($data['topic']); ?> ;
    $scope.course = <?php echo json_encode($data['course']); ?> ;
    $scope.select_options = <?php echo json_encode($data['select_options']); ?> ;
    $scope.tracker = 0;
    $scope.copier = 1;
    $scope.sort_required = false;
    $scope.hidden = 'hidden';
    $scope.alert = '';
    
    //console.log($scope.articles);


}]);
</script>

<style type="text/css">


    .myList ul{
        margin-top: 7px;
    }
</style>

<div class="inner_container" ng-controller="training_manager">
    <div class="breadcrumbs">
        <a href="<?php echo $this->base_url("training/courses/");?>"><i class="fa fa-chevron-left" aria-hidden="true"></i> Structured Tutorials</a>
        <span>/</span>
        <a href="<?php echo $this->base_url("training/manage/");?>"> Manage Structured Tutorials</a>
        <span>/</span>
        <a href="<?php echo $this->base_url("training/manage/".num_to_char($data['course']['id']));?>"> {{course.title}}</a>
        <span>/</span>
        <a href="#"> {{new_topic.title}}</a>
    </div>

    <div class="top_section">
            <div class="buttons_wrap">
                <a class="btn btn-default" href="<?php echo base_url("training/manage/".num_to_char($data['course']['id']));?>">Back</a>
                <a href="<?php echo base_url('training/courses/'.num_to_char($data['course']['id']).'/'.num_to_char($data['topic']['id']));?>" class="btn btn-default">View Topic</a>
                <a href="<?php echo base_url('training/create_topic/'.num_to_char($data['course']['id']));?>" class="btn btn-primary">Create Topic</a>
            </div>
            <h1>
                Edit Topic
            </h1>
        </div>

	<div class="stnrd_box">
		
    <?php if(!empty($data['response'])):
        $response = $data['response'];
        if($response['success']) $alert="success";
        else $alert = "danger";
    ?>
        <div class="col-sm-10 col-sm-offset-1">
            <div class="alert alert-<?=$alert?>"><?=$response['message']?></div>
        </div>
    <?php endif;?>
    
  <div class="container-fluid container_main_body">
    <div class="container">
      <div class="row row_main_body">
      
      <div class="row">

<br clear="all"><br clear="all">

        <div class="col-sm-10 col-sm-offset-1">
            <div class="panel panel-default">
                <div class="panel-body">
                    <form method="post" class="form" action="<?php echo base_url('training/edit_topic/'.num_to_char($data['topic']['id']));?>" method="post">
                        <div class="form-group">
                            <label class="control-label">Tutorial Name</label>
                            <span class="form-control" disabled><?php echo $data['course']['title'];?></span>
                        </div>
                        <div class="form-group">
                            <label class="control-label">Parent Topic</label>
                            <span class="form-control" disabled><?php echo $data['topic']['parent_name'];?></span>
                        </div>
                        <div class="form-group">
                            <label class="control-label">Topic Title</label>
                            <input type="text" class="form-control" name="title" value="<?php echo $data['topic']['title'];?>" required>
                        </div>
                        <div class="form-group">
                            <label class="control-label">Linked Knowledgebase Article</label>
                            <select class="linked-articles-ajax form-control" name="linked_article" value="<?=$data['topic']['kba_id']?>">
                                <?php if($data['topic']['kba_id']>0):?>
                                    <option value="<?=$data['topic']['kba_id']?>" selected><?=$data['topic']['kba_title']?></option>
                                <?php endif;?>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="control-label">Summary</label>
                            <textarea name="summary" class="form-control" rows="5"
                                placeholder="Brief introduction to the course."><?php echo $data['topic']['summary'];?></textarea>
                        </div>
                        <div class="form-group">
                            <label class="control-label">Details</label>
                            <textarea name="details" class="form-control tinymce" rows="10"><?php echo $data['topic']['content'];?></textarea>
                        </div>
                        <div class="form-group">
                            <span class="pull-right">
                                <a class="btn btn-default btn-sm" href="<?php echo base_url("training/manage/".$data['course']['id']);?>">Cancel</a>
                                <button type="submit" class="btn btn-primary btn-sm" name="edit_topic" value="<?=$data['topic']['id']?>">Update Topic</button>
                            </span>
                        </div>
                    </form>
                </div>
            </div>
        </div>




       

      </div>
       

      <hr/>
       <?php include('bottom.php'); ?>
      </div>
      <div class="row row_notes_extras">
        <div class="more">
          <div class="support">
            
          </div>
          <div class="quick_links">
            
          </div>
          <div class="tags">
            
          </div>
        </div>
      </div> 
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    $('.linked-articles-ajax').select2({
        ajax: {
            url: '<?php echo base_url("training/autocomplete")?>',
            dataType: 'json',
            data: function (params) {
              var query = {
                term: params.term,
                usergroup: "<?php echo $data['course']['usergroup'];?>"
              }
              return query;
            }
          }
    });
});
</script>