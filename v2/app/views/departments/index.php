<div class="inner_container">
    <div class="top_section">
        <div class="buttons_wrap">

            <div class="btn-group pull-right" style="margin-bottom: 20px; margin-right: 5px;">
                <button type="button" class="btn btn-default">Cohort <?php echo "(" . $data['cohort'] . ")"; ?></button>
                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="false">
                    <span class="caret"></span>
                    <span class="sr-only">Toggle Dropdown</span>
                </button>
                <ul class="dropdown-menu cohort_list">
                    <li><a href="?cohort=all">All</a></li>
                    <?php foreach ($data['cohorts'] as $cohort) { ?>
                        <li><a href="?cohort=<?php echo $cohort['title']; ?>"><?php echo $cohort['title']; ?></a></li>
                    <?php } ?>
                </ul>
            </div>

        </div>
        <h1>
            <?php echo $data['meta_title']; ?>
        </h1>
    </div>


    <div class="row programmes_boxes">
        <?php
        foreach ($data['results'] as $entry) {
            $link = $this->actual_base_url('departments/' . $entry["id"] . "/programmes");
            if (!empty($data['cohort'])) {
                $link = $link . "?cohort=" . $data['cohort'];
            }
            ?>
            <div class="col-xs-4">
                <div class="box">
                    <div class="title">
                        <div class="btn-group btn-group-sm pull-right" style="margin-top: -4px; ">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown"
                                    aria-haspopup="true" aria-expanded="false">
                                <span class="glyphicon glyphicon-cog" aria-hidden="true"></span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a href="#" form_modal="365" id="<?php echo $entry["id"]; ?>">Edit entry</a></li>
                            </ul>
                        </div>
                        <a href="<?php echo $link; ?>" class="main_title"><?php echo $entry["title"] ?></a>
                    </div>
                    <a href="<?php echo $link; ?>" class="content" style="min-height: 100px;">
                        <div class="row">
                            <div class="col-sm-6">Profiles Created</div>
                            <div class="col-sm-6">
                                <div class="stat"><?php if (!empty($entry["total_applicants"])) {
                                        echo $entry["total_applicants"];
                                    } else {
                                        echo "-";
                                    } ?></div>
                            </div>

                            <div class="col-sm-6">Applications Submitted</div>
                            <div class="col-sm-6">
                                <div class="stat"><?php if (!empty($entry["total_submitted"])) {
                                        echo $entry["total_submitted"];
                                    } else {
                                        echo "-";
                                    } ?></div>
                            </div>

                            <div class="col-sm-6">Offers Made</div>
                            <div class="col-sm-6">
                                <div class="stat"><?php if (!empty($entry["total_offers"])) {
                                        echo $entry["total_offers"];
                                    } else {
                                        echo "0";
                                    } ?></div>
                            </div>

                            <div class="col-sm-6">Offers Accepted</div>
                            <div class="col-sm-6">
                                <div class="stat"><?php if (!empty($entry["total_offers_accepted"])) {
                                        echo $entry["total_offers_accepted"];
                                    } else {
                                        echo "-";
                                    } ?></div>
                            </div>

                            <div class="col-sm-6">Applicants Enrolled</div>
                            <div class="col-sm-6">
                                <div class="stat"><?php if (!empty($entry["total_enrolled"])) {
                                        echo $entry["total_enrolled"];
                                    } else {
                                        echo "-";
                                    } ?></div>
                            </div>
                        </div>
                    </a>

                </div>
            </div>
        <?php } ?>

        <?php
        // only show to tech admins
        if ($_SESSION['ulevel'] == 9) {
            ?>
            <div class="col-xs-4">
                <a class="box add_box thickbox"
                   href="<?php echo $this->engine_url('controller.php?width=850&height=600&pick_page=365&ref=&jqmRefresh=true'); ?>">
                    <div class="title">
                        Create department
                    </div>
                    <div class="content text-center" style="min-height: 100px; padding-top: 5px;">
                        <span class="glyphicon glyphicon-plus  add_icon" aria-hidden="true"
                              style="padding-top: 0px; margin-top: 0px;"></span>
                    </div>

                </a>
            </div>
        <?php } //end add element?>

    </div>
</div>