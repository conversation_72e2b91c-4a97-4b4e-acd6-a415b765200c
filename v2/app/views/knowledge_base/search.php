<div class="inner_container" id="knowledge_base">
	<div class="stnrd_box">
		
		<?php $results = $data['results']; ?>
    	  <!--splash section start-->
    <?php include('search_box.php'); ?>
    <!--splash section end-->
    
  <div class="container-fluid container_main_body">
    <div class="container">
      <div class="row main_content_title">
        <h2 class="text-center">Search : <?php print($data['count']) ?> Result(s)</h2>
      </div> 
      <div class="row row_main_body">
       <!--block start-->
        <div class="col-md-12 featured">
          <?php count($results) ? print "<h5> Showing ". count($results)." result(s)</h5>" : "" ?>
          <ul class="section featured_articles articles">
          	<?php   
              foreach ($results as $key => $value) {?>
	            <li class="article_entry">
	              <h4><a href="<?php print base_url('knowledge_base/article/'. $value['id'] ); ?>" class="article_title"><?php print $value['title'] ?></a></h4>
	              <span class="article_meta">25 Feb, 2018 in <a href="<?php print base_url('knowledge_base/category/'. $value['category']['id'] ); ?>" title="View all posts in <?php print $value['category']['title']  ?>"><?php print $value['category']['title']  ?></a> <?php if((int)$_SESSION['ulevel'] === 9){ echo $state = ($value['published'] == 'yes' ? '<span class="badge badge-success">published</span>': '<span class="badge badge-warning">draft</span>'); } ?></span>
	              <span class="like-count">66</span>
	            </li>
        	<?php 
            }//end for each
          ?>
          </ul>
          <?php global $paginator; echo $paginator->links(); ?>
        </div>
       <!--block end-->
      
      <hr/>
      <?php include('bottom.php'); ?>
      </div>
      <div class="row row_notes_extras">
        <div class="more">
          <div class="support">
            
          </div>
          <div class="quick_links">
            
          </div>
          <div class="tags">
            
          </div>
        </div>
      </div> 
    </div>
  </div>
</div>