<div class="inner_container">
	<div class="top_section">		
		<h1>
			Key Recruitment Data
		</h1>
	</div>
	<div class="stnrd_box">
		<div class="" style="overflow-x:auto;">
			<table class="table table-striped" width="100%">
				<thead>
					<tr>
						<th class="freezecol">&nbsp;</th>
						<?php foreach ($data['results']['course_levels'] as $levels){ ?>
							<th class="headings"><?php echo $levels['title']; ?></th>
						<?php } ?>						
						<th class="headings">All Levels</th>
						<th class="headings">Conversion</th>
					</tr>
				</thead>
				<tbody class="sortable_objects">
					<?php $total_yr_applicants =0; $z=0; foreach ($data['results']['yearly_stats'] as $year){?>
					<?php if($z > 0){?>	
					<tr>
						<td class="freezecol">&nbsp;</td>
						<td colspan="5"></td>										
					</tr>
					<?php } ?>
					<tr>
						<td class="freezecol"><h3 style="margin-top: 3px;margin-bottom: 3px;">Cohort <?php echo $year[0]['levels_year']; ?></h3></td>
						<td colspan="5"></td>										
					</tr>
					<tr>
						<td class="freezecol">Total Profiles Created</td>
						
						<?php $grand_total_app=0; for($x=0;$x<count($year);$x++){ ?>
						  	<td> 
						  	<?php 
						  		echo $year[$x]['total_applicants']; 
						  		$grand_total_app = $grand_total_app + $year[$x]['total_applicants'];
						  	?>				
						  	</td>	
						  	<?php if($x === (count($year)-1)){ $total_yr_applicants=$grand_total_app; ?>
						  		<td><?php echo $grand_total_app;?></td>	
						  		<td></td>
						  	<?php } ?>	  
						<?php }?>						
					</tr>
					<tr>
						<td class="freezecol">Total Applications(Submitted)</td>
						<?php $grand_total_sub=0; for($x=0;$x<count($year);$x++){ ?>
						  	<td> 
						  		<?php 
						  			echo $year[$x]['total_submitted']; 
						  			$grand_total_sub = $grand_total_sub + $year[$x]['total_submitted'];
						  		?>				
						  	</td>
						  	<?php if($x === (count($year)-1)){?>
						  		<td><?php echo $grand_total_sub;?></td>	
						  		<td><?php
						  			if($total_yr_applicants>0){ 
						  				$sub_conversion= ($grand_total_sub/$total_yr_applicants)*100;
						  				echo number_format((float)$sub_conversion, 2, '.', '')."%"; 
						  			}
						  			else{
						  				echo "0%";
						  			}
						  			?>
						  		</td>	
						  	<?php } ?>						  		  
						<?php }?>
						
					</tr>
					<tr>
						<td class="freezecol">Total Offers(Made)</td>
						<?php $grand_total_off=0; for($x=0;$x<count($year);$x++){ ?>
						  <td> 
						  	<?php echo $year[$x]['total_offers']; 
						  	$grand_total_off = $grand_total_off + $year[$x]['total_offers'];
						  	?>				
						  </td>	
						  <?php if($x === (count($year)-1)){?>
						  		<td><?php echo $grand_total_off;?></td>	
						  		<td><?php 
						  		if($total_yr_applicants>0){ 
						  			$off_conversion= ($grand_total_off/$total_yr_applicants)*100;
						  			echo number_format((float)$off_conversion, 2, '.', '')."%"; 
						  		}
						  		else{
					  				echo "0%";
					  			}
						  		?>
						  		</td>
						  	<?php } ?>			  
						<?php }?>
						
					</tr>
					<tr>
						<td class="freezecol">Total Offers(Rejected)</td>
						<?php $grand_total_rej=0; for($x=0;$x<count($year);$x++){ ?>
						  <td> 
						  	<?php echo $year[$x]['total_rejected']; 
						  	$grand_total_rej = $grand_total_rej + $year[$x]['total_rejected'];
						  	?>				
						  </td>
						  <?php if($x === (count($year)-1)){?>
						  		<td><?php echo $grand_total_rej;?></td>	
						  		<td><?php 
						  			if($total_yr_applicants>0){ 
							  			$rej_conversion= ($grand_total_rej/$total_yr_applicants)*100;
							  			echo number_format((float)$rej_conversion, 2, '.', '')."%"; 
							  		}
							  		else{
							  			echo "0%";
							  		}?>
						  		</td>
						  <?php } ?>			  
						<?php }?>
						
					</tr>
					<tr>
						<td class="freezecol">Total Acceptances</td>
						<?php $grand_total_acc=0; for($x=0;$x<count($year);$x++){ ?>
						  <td> 
						  	<?php echo $year[$x]['total_offers_accepted']; 
						  	$grand_total_acc = $grand_total_acc + $year[$x]['total_offers_accepted'];
						  	?>				
						  </td>	
						  <?php if($x === (count($year)-1)){?>
						  		<td><?php echo $grand_total_acc;?></td>	
						  		<td><?php 
						  			if($total_yr_applicants>0){
							  			$acc_conversion= ($grand_total_acc/$total_yr_applicants)*100;
							  			echo number_format((float)$acc_conversion, 2, '.', '')."%"; 
							  		}
							  		else{
							  			echo "0%";
							  		}
							  		?>
						  		</td>
						  <?php } ?>		  
						<?php }?>
						
					</tr>
					<tr>					
						<td class="freezecol">Total Enrolled</td>
						<?php $grand_total_enr=0; for($x=0;$x<count($year);$x++){ ?>
						  <td> 
						  	<?php echo $year[$x]['total_enrolled']; 
						  	$grand_total_enr = $grand_total_enr + $year[$x]['total_enrolled'];
						  	?>				
						  </td>
						  <?php if($x === (count($year)-1)){?>
						  		<td><?php echo $grand_total_enr;?></td>	
						  		<td><?php
						  			if($total_yr_applicants>0){ 
						  			$enr_conversion= ($grand_total_enr/$total_yr_applicants)*100;
						  			echo number_format((float)$enr_conversion, 2, '.', '')."%"; 
						  			}
						  			else{
						  				echo "0%";	
						  			}
						  			?>
						  		</td>
						  <?php } ?>			  
						<?php }?>
						
					</tr>
					
					<?php $z++;} ?>		
			
				</tbody>
			</table>
		</div>	

	</div>
</div>	
<style type="text/css">
	.headings{
		font-weight:bold;
		font-size: 10pt;
		min-width: 80px;
	}
	.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
	    padding: 3px;
	}
	.freezecol {
        position:sticky;
        min-width: 206px !important;
        left:0;
        top:auto;
        overflow:hidden;
        background-color : #fff;
    }
</style>	