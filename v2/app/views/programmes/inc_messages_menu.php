<?php
	$messages = new CommunicationMessages;

	$all_messages_count = $messages->get(array('course_id'=>$this->uri_segement(4),'no_replies'=>true,'count'=>true,'unread'=>true,'school_id'=>$this->school_info['id']));
	$general_messages_count = $messages->get(array('course_id'=>$this->uri_segement(4),'no_replies'=>true,'type_id'=>1,'count'=>true,'unread'=>true,'school_id'=>$this->school_info['id']));
	$submission_notifications_count = $messages->get(array('course_id'=>$this->uri_segement(4),'no_replies'=>true,'type_id'=>8,'count'=>true,'unread'=>true,'school_id'=>$this->school_info['id']));
	$file_upload_count = $messages->get(array('course_id'=>$this->uri_segement(4),'no_replies'=>true,'type_id'=>9,'count'=>true,'unread'=>true,'school_id'=>$this->school_info['id']));

	if($data['programme']['id']){
		$pl = $data['programme']['id']."/";
	}

	$messages_base_link = $this->base_url('course_levels/messages/'.$data['level_id']."/".$data['course_id']);
?>
<div class="list-group">
  <a href="<?php echo $messages_base_link.'?cohort='.$data['cohort']; ?>" class="list-group-item <?php if(!$data['type']){ echo "active";}?>">All Messages <span class="badge"><?php echo $all_messages_count; ?></span></a>
  <a href="<?php echo $messages_base_link.'/general_questions?cohort='.$data['cohort']; ?>" class="list-group-item <?php if(strpos($this->uri_string(), "general_questions")!==false){ echo "active";}?>">General Questions <span class="badge"><?php echo $general_messages_count; ?></span></a>
  <a href="<?php echo $messages_base_link.'/submission_notifications?cohort='.$data['cohort'];; ?>" class="list-group-item <?php if(strpos($this->uri_string(), "submission_notifications")!==false){ echo "active";}?>">Submission notifications <span class="badge"><?php echo $submission_notifications_count; ?></span></a>
  <a href="<?php echo $messages_base_link.'/file_upload_notifications?cohort='.$data['cohort'];; ?>" class="list-group-item <?php if(strpos($this->uri_string(), "file_upload_notifications")!==false){echo "active";}?>">File upload notifications <span class="badge"><?php echo $file_upload_count; ?></span></a>
  <a href="<?php echo $messages_base_link.'/search?cohort='.$data['cohort'];; ?>" class="hide list-group-item <?php if(strpos($this->uri_string(), "search")!==false){ echo "active";}?>">Search Messages </a>
</div>