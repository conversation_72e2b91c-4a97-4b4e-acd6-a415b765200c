<?php
$filter_type = $data['filter_types'];


$default_filters_list = array();
foreach ($filter_type as $fff) {
    $default_filters_list[] = ucwords($fff['title']);
}

$default_filters_list = array_values($default_filters_list);

$filter_types_json = json_encode($filter_type);

$filter_type_small = array();
foreach ($filter_type as $ff) {
    if (!$ff['extra']) {
        $ff['options'] = '';
        $filter_type_small[] = $ff;
    }
}
$entry_ids = [];
foreach ($data['course_levels'] as $entry) {
    $entry_ids[] = $entry['id'];
}

$filter_type_small = json_encode($filter_type_small);
if (!empty($data['filter']['description'])) {
    $filters = $data['filter']['description'];
} else {
    $filters[] = [
        'field_type' => '',
        'type' => '',
        'title' => '',
        'operator_type' => '',
        'from_date' => '',
        'to_date' => '',
        'value' => '',
        'status' => 'active',
        'options' => '',
        'option' => '',
        'no_contains' => '',
        'custom_options' => '',
        'join' => 'and'
    ];
}

$filters_json = json_encode($filters);
?>

<div  class="inner_container">
    <div class="top_section">
        <h1>
            <?php terminology('Course Levels', $this->current_url(), 'Course Levels Page Title') ?>
        </h1>
    </div>
    <?php if (!empty($data['filtering_tools'])) {
        require(ABSPATH . "v2/app/views/filters/filter_tools_bar.php");
    } ?>
    <div class="row programmes_boxes" id="app">
        <div class="row" style="margin-bottom: 20px;">
            <div class="col-md-12">
                <span id="location" class="hidden" data-href="<?php echo $data['location']; ?>"
                      data-cohort="<?php echo $data['cohort']; ?>"
                      data-filter="<?php echo !empty($data['filter']['id']) ? $data['filter']['id'] : ''; ?>">
                </span>
                <div class="btn-group pull-right filter_tool_bar_controls top_control" v-show="view_selected_intakes"
                     role="group"
                     style="margin-right: 2px; display: none">
                    <a href="#" class="btn btn-lg" v-on:click="show_selected_tiles"
                       style="background: #9e9e9e ;color: #fff;">Show
                        Records </a>
                    <a href="#" class="btn btn-lg" v-on:click="clear_selected_tiles"
                       style="background: #29b6f6 ;color: #fff;">Reset
                        Selection</a>
                </div>
                <div class="btn-group pull-right filter_tool_bar_controls bottom_control"
                     v-show="view_selected_intakes==false"
                     role="group"
                     style="margin-right: 2px; display: none">
                    <a href="#" class="btn btn-lg" v-on:click="view_selected_tiles"
                       style="background: #9e9e9e ;color: #fff;">View
                        Selected</a>
                    <a href="#" class="btn btn-lg" v-on:click="select_all_tiles"
                       style="background: #29b6f6 ;color: #fff;">Select
                        All</a>
                </div>

                <?php if (!empty($data['show_export_button'])) { ?>
                    <a href="?export=1" class="btn btn-default btn-lg pull-right">Export</a>
                <?php } ?>
                <div class="btn-group pull-right" role="group" style="margin-right: 2px;">
                    <button type="button" class="btn btn-lg btn-default dropdown-toggle" data-toggle="dropdown"
                            aria-haspopup="true" aria-expanded="false">
                        Sort |
                        <span class="caret"></span>
                        <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <?php

                    if (isset($_GET['filter'])) {
                        $filter = "&filter=" . $_GET['filter'];
                    } else {
                        $filter = "";
                    }
                    ?>
                    <ul class="dropdown-menu cohort_list">
                        <li><a href="?cohort=<?php echo $data['cohort'] . $filter; ?>">None</a></li>
                        <?php foreach ($data['sort_by'] as $sort) { ?>
                            <li>
                                <a href="?cohort=<?php echo $data['cohort']; ?>&order=<?php echo $sort['value'] . $filter; ?>">
                                    <?php if (strpos($sort['value'], "asc")) { ?>
                                        <i class="fa fa-arrow-up"></i>
                                    <?php } else { ?>
                                        <i class="fa fa-arrow-down"></i>
                                    <?php } ?>
                                    <?php echo $sort['title']; ?></a></li>
                        <?php } ?>
                    </ul>
                </div>
                <div class="btn-group pull-right" role="group">
                    <button type="button" class="btn btn-lg btn-default dropdown-toggle" data-toggle="dropdown"
                            aria-haspopup="true" aria-expanded="false">
                        Cohort <?php echo "(" . $data['cohort'] . ")"; ?>
                        <span class="caret"></span>
                        <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <ul class="dropdown-menu cohort_list">
                        <li><a href="?cohort=all">All</a></li>
                        <?php foreach ($data['cohorts'] as $cohort) { ?>
                            <li>
                                <a href="?cohort=<?php echo $cohort['title']; ?>"><?php echo $cohort['title']; ?></a>
                            </li>
                        <?php } ?>
                    </ul>
                </div>



            </div>
        </div>
        <div class="row">
        <?php
        foreach ($data['course_levels'] as $entry) {
            $link = $this->actual_base_url('course_levels/' . $entry["id"]).'/programmes';
            $link_extra = $this->actual_base_url('course_levels/' . $entry["id"]).'/applicants';
            $created=$link;
            $submitted = $link_extra . '?app_cat=submitted&course_id=level';
            $offers = $link_extra . '?app_cat=offers&course_id=level';
            $accepted = $link_extra . '?app_cat=accepted&course_id=level';
            $enrolled = $link_extra . '?app_cat=enrolled&course_id=level';
            if ($data['cohort']) {
                $coh = "&cohort=" . $data['cohort'];
                $created .="?cohort=" . $data['cohort'];
                $submitted .= $coh;
                $offers .= $coh;
                $accepted .= $coh;
                $enrolled .= $coh;
            }
            ?>
            <div class="col-xs-4 tile_box" id="box_<?php echo $entry['id'] ?>">
                <div class="box">
                    <div class="title">
                        <input type="checkbox" name="selected[]" class="tile_selected_box"
                               value="<?php echo $entry['id'] ?>" v-model="selected_intakes">
                        <div class="btn-group btn-group-sm pull-right" style="margin-top: -4px; ">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown"
                                    aria-haspopup="true" aria-expanded="false">
                                <span class="glyphicon glyphicon-cog" aria-hidden="true"></span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a href="#" form_modal="38" id="<?php echo $entry["id"]; ?>">Edit level</a></li>
                            </ul>
                        </div>
                        <a href="<?php echo $link; ?>" class="main_title"><?php echo $entry["title"] ?></a>
                    </div>
                    <div class="content">
                        <div class="tab-content row" style="min-height: 340px;">
                            <div id="overall_<?php echo $entry["id"]; ?>" class="tab-pane fade in active col-sm-12">
                                <div class="row" style="min-height: 170px;">
                                    <div class="col-sm-6"><a href="<?php echo $created; ?>">Profiles Created</a></div>
                                    <div class="col-sm-6">
                                        <div class="stat"><?php if ($entry["total_applicants"]) {
                                                echo $entry["total_applicants"];
                                            } else {
                                                echo "-";
                                            } ?></div>
                                    </div>

                                    <div class="col-sm-6"><a href="<?php echo $submitted; ?>">Applications Submitted</a>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="stat"><?php if ($entry["total_submitted"]) {
                                                echo $entry["total_submitted"];
                                            } else {
                                                echo "-";
                                            } ?></div>
                                    </div>

                                    <div class="col-sm-6"><a
                                                href="<?php echo $offers; ?>"><?php echo in_array($_SESSION['usergroup'], array(49)) ? 'App Ok' : 'Offers Made'; ?> </a>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="stat"><?php if ($entry["total_offers"]) {
                                                echo $entry["total_offers"];
                                            } else {
                                                echo "0";
                                            } ?></div>
                                    </div>

                                    <div class="col-sm-6"><a href="<?php echo $accepted; ?>">Offers Accepted</a></div>
                                    <div class="col-sm-6">
                                        <div class="stat"><?php if ($entry["total_offers_accepted"]) {
                                                echo $entry["total_offers_accepted"];
                                            } else {
                                                echo "-";
                                            } ?></div>
                                    </div>

                                    <div class="col-sm-6"><a href="<?php echo $enrolled; ?>">Applicants Enrolled</a>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="stat"><?php if ($entry["total_enrolled"]) {
                                                echo $entry["total_enrolled"];
                                            } else {
                                                echo "-";
                                            } ?></div>
                                    </div>
                                    <div class="col-sm-6"><a href="<?php echo $enrolled; ?>">Rejected</a></div>
                                    <div class="col-sm-6">
                                        <div class="stat"><?php if ($entry["total_rejected"]) {
                                                echo $entry["total_rejected"];
                                            } else {
                                                echo "-";
                                            } ?></div>
                                    </div>
                                    <div class="col-sm-6"><a href="<?php echo $enrolled; ?>">Withdrawn</a></div>
                                    <div class="col-sm-6">
                                        <div class="stat"><?php if ($entry["total_withdrawn"]) {
                                                echo $entry["total_withdrawn"];
                                            } else {
                                                echo "-";
                                            } ?></div>
                                    </div>
                                </div>
                                <hr>
                                <span class="active_pill_content_<?php echo $entry["id"]; ?> small">The total number of profiles and applications at each specific application status</span>
                            </div>
                            <div id="current_<?php echo $entry["id"]; ?>" class="tab-pane fade hidden col-sm-12">
                                <div class="row">
                                    <div class="col-sm-6"><a href="<?php echo $link; ?>">Profiles Created</a></div>
                                    <div class="col-sm-6">
                                        <div class="stat"><?php if ($entry["total_applicants_fv"]) {
                                                echo $entry["total_applicants_fv"];
                                            } else {
                                                echo "-";
                                            } ?></div>
                                    </div>

                                    <div class="col-sm-6"><a href="<?php echo $submitted; ?>">Applications Submitted</a>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="stat"><?php if ($entry["total_submitted_fv"]) {
                                                echo $entry["total_submitted_fv"];
                                            } else {
                                                echo "-";
                                            } ?></div>
                                    </div>

                                    <div class="col-sm-6"><a
                                                href="<?php echo $offers; ?>"> <?php echo in_array($_SESSION['usergroup'], array(49)) ? 'App Ok' : 'Offers Made'; ?> </a>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="stat"><?php if ($entry["total_offers_fv"]) {
                                                echo $entry["total_offers_fv"];
                                            } else {
                                                echo "0";
                                            } ?></div>
                                    </div>

                                    <div class="col-sm-6"><a href="<?php echo $accepted; ?>">Offers Accepted</a></div>
                                    <div class="col-sm-6">
                                        <div class="stat"><?php if ($entry["total_offers_accepted_fv"]) {
                                                echo $entry["total_offers_accepted_fv"];
                                            } else {
                                                echo "-";
                                            } ?></div>
                                    </div>

                                    <div class="col-sm-6"><a href="<?php echo $enrolled; ?>">Applicants Enrolled</a>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="stat"><?php if ($entry["total_enrolled"]) {
                                                echo $entry["total_enrolled"];
                                            } else {
                                                echo "-";
                                            } ?></div>
                                    </div>
                                </div>
                                <hr>
                                <span class="active_pill_content_<?php echo $entry["id"]; ?> small"></span>
                            </div>
                        </div><!--end tabbed content -->
                        <!--and or buttons -->
                        <div class="ui-group-buttons hidden">
                            <a href="#current_<?php echo $entry["id"]; ?>" data-toggle="pill"
                               data-pills="<?php echo $entry["id"]; ?>"
                               class="btn btn-primary btn-xs pills_<?php echo $entry["id"]; ?>"
                               title="The total number of profiles and applications at each specific application status">Current
                                View</a>
                            <div class="or or-xs"></div>
                            <a href="#overall_<?php echo $entry["id"]; ?>" data-pills="<?php echo $entry["id"]; ?>"
                               data-toggle="pill" class="btn btn-default btn-xs pills_<?php echo $entry["id"]; ?>"
                               title="The total number of profiles and applications in your selected recruitment cycle">Funnel
                                View</a>
                        </div>
                        <!--and or buttons end -->
                    </div>

                </div>
            </div>
        <?php } ?>

        <?php
        // only show to tech admins
        if ($_SESSION['ulevel'] == 9) {
            ?>
            <div class="col-xs-4">
                <a class="box add_box thickbox"
                   href="<?php echo $this->engine_url('controller.php?width=850&height=600&pick_page=38&ref=&jqmRefresh=true'); ?>">
                    <div class="title">
                        Create course levels
                    </div>
                    <div class="content text-center" style="min-height: 100px; padding-top: 5px;">
                        <span class="glyphicon glyphicon-plus  add_icon" aria-hidden="true"
                              style="padding-top: 0px; margin-top: 0px;"></span>
                    </div>

                </a>
            </div>
        <?php } //end add element?>

        </div>
    </div>
</div>
<script>
    var app = new Vue({
        el: '#app',
        data: {
            entry_ids: <?php echo json_encode($entry_ids); ?>,
            selected_intakes: [],
            view_selected_intakes: false,
            select_all_intakes: false
        },
        methods: {
            show_selected_tiles() {
                if (this.selected_intakes.length > 0) {
                    var location_data = $("#location").data();
                    if (location_data.href !== '' && location_data.href.length > 0) {
                        $('<form method="get" action="' + location_data.href + '"><input name="filter" value="' + location_data.filter + '"/><input name="cohort" value="' + location_data.cohort + '"/><input name="tiles" value="' + this.selected_intakes.join() + '"/></form>').appendTo('body').submit();
                    }
                }
            }, clear_selected_tiles() {
                this.selected_intakes = [];
                this.view_selected_intakes = false;
                this.entry_ids.forEach((item) => {
                    $("#box_" + item).show();
                })
            }, view_selected_tiles() {
                this.view_selected_intakes = true;
                this.entry_ids.forEach((item) => {
                    if (this.selected_intakes.indexOf(item) == -1) {
                        $("#box_" + item).hide();
                    } else {
                        $("#box_" + item).show();
                    }
                })

            }, select_all_tiles() {
                this.view_selected_intakes = true;
                this.selected_intakes = this.entry_ids;

            },


        }
    });

</script>
