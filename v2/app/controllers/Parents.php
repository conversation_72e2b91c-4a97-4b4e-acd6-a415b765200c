<?php 

/**
* 
*/
namespace App\controllers;
use App\core\Controller;
use App\middleware\Authentication;

class Parents extends Controller
{
    public function __construct()
    {
        $this->middleware(Authentication::class);
    }
	public function index($assigned_to_me_id=""){

    global $request, $db;
    if(!is_numeric($this->uri_segement(3))){
    $enq = new enquirieModel;
    $parents = new parents_model;
    $form_templates = new FormTemplates;
    $fieldClass = new Fields;
    $active_columns = $parents->get_parent_columns();
    $field_indices = array_column($active_columns, 'db_field_name');
    //print_r($field_indices);
    //die("Stage 1");
    $parents = new parents_model;
    $enq_args = array(
      'paginate' => $_GET['export'] ? ! (bool) $_GET['export'] : true,
      'search' => trim($_POST['search']) ?: trim($_GET['search']),
      'assigned' => $assigned_to_me_id ?: false,
      'order' => $_GET['order']
    );

    //--start set filters
    $setup_args = array_merge($enq_args, array('limit' => 1));
    unset($setup_args['paginate']);
    unset($setup_args['search']);
  	$results = $parents->get($setup_args);
    //die("Stage 2");
    $field_types = array('date_created' => 'days_greater_or_lesser', 'date' => 'date');
    $dropdowns = array(
            'assigned_to' => 'users|get|{"school_id":"'.app()->school_info['id'].'", "type_id_in":[2,8]}|["id","full_name"]',
            'course' => 'courses|get|{"school_id":"'.app()->school_info['id'].'"}|["id","title"]',
            'status' => 'enquirieModel|get_enq_stages|{"school_id":"'.app()->school_info['id'].'"}|["id","stage"]',
            'cohort' => 'fields|get|{"db_field_name":"db1680"}|["title","title"]',
            'db1049' => 'students|get_hear_about_us|{"school_id":"'.app()->school_info['id'].'", "test": "true"}|["id","title"]',
        );
    foreach ($field_indices as $key => $value) {
      if($value)
        $rename_fields[$value] = $value."_#_".str_replace(" ", "_", $active_columns[$key]['name']);
    }

    // $rename_fields = array('Date/Time Created' => 'Date Created');
    $filter_types = setup_filter_data($results, 'filter_types', array('Manage', 'id', 'username_id', 'course_column') , $rename_fields, $dropdowns, $field_types);
    $dimens = setup_filter_data($results, 'dimensions' , array('Manage', 'id', "username_id", 'course_column'), $rename_fields, $dropdowns , $field_types);
    unset($results[0]['username_id']);

        $form_args = array('school_id'=>app()->school_info['id'],'category_id'=>2, 'no_fields' => true);
        $rx = $form_templates->get_custom_forms($form_args);

        $embed = array();
        foreach ($rx as $key => $value) {
            $embed[] = array(
                'label' => $value['title'],
                'value' => '/embed/'.$value['id']
            );
        }
        $embed_filter[] = array(
            'id' => 'embeded_form',
            'title' => 'Enquiry Form',
            'extra' => true,
            'options' => $embed,
            'field_type' => false
        );
        $embed_dimen = array(
            'id' => 'embeded_form',
            'title' => 'Enquiry Form',
            'active' => 0,
            'field_type' => false
        );

        $filter_types = array_merge($filter_types, $embed_filter);
        $dimens[0]['All Fields'][] = $embed_dimen;
	
	dev_debug("results=".json_encode($results));
		
    $fields = array_keys($results[0]);
    $all_fields = array();
    $active_fields = $form_templates->get_page_columns(array('page' => 'enquiries', 'filter_id' => $this->filter['id']));
    // if(in_array('date_created', $active_fields)){
    //   $active_fields[] = 'date/time_created';
    // }
    $dimensions = array_map(function($dimen) use($active_fields){
      foreach ($dimen as $key => $value) {
        foreach ($value as $k => $v) {
          if($active_fields)
           $dimen[$key][$k]['active'] = (in_array($v['id'], $active_fields)) ? 1 : 0;
        }
      }
      return $dimen;
    }, $dimens);
#		print_r($fields);
    foreach ($fields as $key => $value) {
      $pointer = array_search($value, $field_indices);
      $fd['title'] =  $pointer !== false ? $active_columns[$pointer]['name'] : $value;
      $fd['id'] = strtolower(str_replace(" ", "_" , $value));
      $fd['db_field_name'] = $value;
      $field_params = array('db_field_name' => $value,'school_id' => $_SESSION['usergroup'], 'masked_fields_only' => true);
		
      $field_data = $fieldClass->get($field_params);
      dev_debug(json_encode($field_data));

      if($field_data){
        if($field_data['type'] =='dynamic_list_group'){

          $query = explode(',', $field_data['default']);
          if($query[1]){
            $from_part = explode("order", $query[0]);
            if($from_part[1]){
              $from_string = $from_part[0] .(strpos($from_part[0], "WHERE") !== false ? " AND (usergroup = '".$_SESSION['usergroup']."' XOR usergroup = '1' )" :" WHERE 1 AND (usergroup = '".$_SESSION['usergroup']."' XOR usergroup = '1' )") . " ORDER ".$from_part[1];
            }else{
              $from_string = $from_part[0] .(strpos($from_part[0], "WHERE") !== false ? " AND (usergroup = '".$_SESSION['usergroup']."' XOR usergroup = '1' )" :" WHERE 1 AND (usergroup = '".$_SESSION['usergroup']."' XOR usergroup = '1' )") ;
            }
            
          
            if(strpos($query[1], "+") !== false){
              $select_array = explode("+", $query[1]);
              $label = $select_array[0];
            }else{
              $label = $query[1];
            }

            $sql = "SELECT id as value , $label as label FROM $from_string";
            $fd['options'] = $db->get_rows($sql);
          }
          
        }
      }
		
      if($active_fields)
        $fd['active'] = in_array($fd['id'], $active_fields) ? 1 : 0;
      else
        $fd['active'] = 1;
      $all_fields[] = $fd;
    }
    //--end set filters
    //set filter type replacement dbcolumns (This replaces a filter name with a dbfield name)
    $filter_ids = array(
      'surname' => 'db1043',
      'first_name' => 'db1041',
	  'status' => 'db1056',
      'cohort' => 'db1050',
      'email_address' => 'db1058',
      'course' => 'core_courses.db232',
      'username_id' => 'lead_profiles.username_id',
      'assigned_to' => 'db69836||dropdown||general||enquiry_reviewer||join_type',
      //'date_created' => "lead_profiles.`date`||date||general||date_search_term_time_like",
      'date_created' => 'lead_profiles.`date`||date||general||date_greater_or_less_than||||{"table":"lead_profiles"}',
      'date' => "lead_profiles.`date`||date||general||date_search_term_time_like",
        'embeded_form'=> 'db32506||string||like_custom'
    );//--end filter type replacement

    $filter_add = array_keys($rename_fields);
    foreach ($filter_add as $f_add) {
      $filter_ids[$f_add] = "lead_profiles.`".$f_add."`";
    }

    if($_GET['what']){
      die(json_encode($filter_ids));
    }

    //start filter manipluation based on $filter_ids array
    if($this->filter['description']){
      dev_debug("filtex -" . json_encode($this->filter['description']));
      $enq_args['filter_sql_lite'] = prepare_filter_sql($this->filter['description'], $filter_ids);
      $filter_args = $f_args = prepare_filter_args($this->filter['description']);
      //uset all elements in $filter_ids array from filters as these will be handled by SQL
      unset($filter_args['surname']); 
      unset($filter_args['first_name']);
      unset($filter_args['cohort']);
      unset($filter_args['course']);
      unset($filter_args['username_id']);
      unset($filter_args['date_created']);
    }
    //get results not paginated if non-query based filters are present and vice versa
    $final_results = $parents->get($enq_args);
    //check if there are still values lest in filter_args
    // if(count($filter_args) > 0){
    //   //if values present carrry out all non-sql query based filtering here
    //   $final_results = filter_numbers_from_filter($final_results, $filter_args);
    //   //get all ids from the filtered results
    //   $ids = filter_ids_from_results($final_results,"Manage");
    //   //check if there are ids present
    //   if(strlen($ids) > 0){
    //     //run the final paginated query using the filtered id
    //     $new_enq_args = array('paginate' => true , 'ids_in' => $ids);  
    //     $final_results = $enq->get_enq($new_enq_args); 
    //   }
    // }
    /** ===================================
      * Export Results
      ====================================  */
      if($_GET['export']){
       $columns = array_keys($final_results[0]);
       $columns_final = array_map(function($key) use ($field_indices, $active_columns){
        $pointer = array_search($key, $field_indices);
        if(($pointer == 0 && $field_indices[0] == $key) || $pointer > 0 ){
          return $active_columns[$pointer]['name'];
        }
        if(in_array($key, array('username_id','id'))) return false;
        return ucwords(str_replace("_", " ", $key));
       }, $columns);
        foreach ($final_results as $key => $value) {
          $export_keys = array_keys($value);
          foreach ($export_keys as $k => $v) {
            $needle = strtolower(str_replace(" ", "_" , $v));
            if($active_fields && ! in_array($needle, $active_fields)){
              unset($final_results[$key][$v]);
            }
          }
          unset($final_results[$key]['username_id']);
        }
        array_unshift($final_results, array_filter($columns_final));
        echo array2csv($final_results, 'enquiries-'.date('dmY-his'));
        exit();
      }

    /** ===================================
      * Do a bulk action
      ====================================  */
    $enq->process_bulk_action(array('results'=>$final_results));

    if($_GET['testy']){
      echo '<pre>';
      print_r($final_results);
      echo '</pre>';
    }

  	$data = array(
		'meta_title'=>'All Parents',
		'view_file'=>'parents/index',
		'results' => $final_results,
		'filter'=>$this->filter,
		'hide_filters'=>true,
		'dimensions' => $dimensions,
		'all_fields' => $all_fields,
		'filter_types' => $filter_types,
		'filters_args'=>$f_args,
		'filter_page_name'=>'enquiries',
	);
}
    else{
      $parents = new parents_model;
      $parent_id = $this->uri_segement(3);
      
      $parents_args = array(
        'id' => $parent_id,
      );
      $parent_name = $parents->get($parents_args);

    global $request;
    $enq = new enquirieModel;
    $form_templates = new FormTemplates;
    $fieldClass = new Fields;
    $active_columns = $enq->get_enq_columns();
    $field_indices = array_column($active_columns, 'db_field_name');
    //die("Stage 1");
    if(!empty($parent_name["Email"])){
      $enq_args = array(
        'paginate' => true,
        'search' => trim($_POST['search']) ?: trim($_GET['search']),
        'parent_email'=>$parent_name["Email"],
        'order' => $_GET['order'],
      );  
    }
    else{
      $enq_args = array(
        'paginate' => true,
        'search' => trim($_POST['search']) ?: trim($_GET['search']),
        'order' => $_GET['order'],
      );    
    }
    
     
    //--start set filters
    $setup_args = array_merge($enq_args, array('limit' => 1));
    unset($setup_args['paginate']);
    unset($setup_args['search']);
    $results = $enq->get_enq($setup_args);
    //die("Stage 2");
    $field_types = array('date_created' => 'days_greater_or_lesser', 'date' => 'date');
    $dropdowns = array(
            'assigned_to' => 'users|get|{"school_id":"'.app()->school_info['id'].'", "type_id_in":[2,8]}|["id","full_name"]',
            'course' => 'courses|get|{"school_id":"'.app()->school_info['id'].'"}|["id","title"]',
            'status' => 'enquirieModel|get_enq_stages|{"school_id":"'.app()->school_info['id'].'"}|["id","stage"]',
            'cohort' => 'fields|get|{"db_field_name":"db1680"}|["title","title"]',
            'db1049' => 'students|get_hear_about_us|{"school_id":"'.app()->school_info['id'].'", "test": "true"}|["id","title"]',
        );
    foreach ($field_indices as $key => $value) {
      if($value)
        $rename_fields[$value] = $value."_#_".str_replace(" ", "_", $active_columns[$key]['name']);
    }

    // $rename_fields = array('Date/Time Created' => 'Date Created');
    $filter_types = setup_filter_data($results, 'filter_types', array('Manage', 'id', 'username_id', 'course_column') , $rename_fields, $dropdowns, $field_types);
    $dimens = setup_filter_data($results, 'dimensions' , array('Manage', 'id', "username_id", 'course_column'), $rename_fields, $dropdowns , $field_types);
    unset($results[0]['username_id']);

        $form_args = array('school_id'=>app()->school_info['id'],'category_id'=>2, 'no_fields' => true);
        $rx = $form_templates->get_custom_forms($form_args);

        $embed = array();
        foreach ($rx as $key => $value) {
            $embed[] = array(
                'label' => $value['title'],
                'value' => '/embed/'.$value['id']
            );
        }
        $embed_filter[] = array(
            'id' => 'embeded_form',
            'title' => 'Enquiry Form',
            'extra' => true,
            'options' => $embed,
            'field_type' => false
        );
        $embed_dimen = array(
            'id' => 'embeded_form',
            'title' => 'Enquiry Form',
            'active' => 0,
            'field_type' => false
        );

        $filter_types = array_merge($filter_types, $embed_filter);
        $dimens[0]['All Fields'][] = $embed_dimen;
  
  dev_debug("results=".json_encode($results));
    
    $fields = array_keys($results[0]);
    $all_fields = array();
    $active_fields = $form_templates->get_page_columns(array('page' => 'enquiries', 'filter_id' => $this->filter['id']));
    // if(in_array('date_created', $active_fields)){
    //   $active_fields[] = 'date/time_created';
    // }
    $dimensions = array_map(function($dimen) use($active_fields){
      foreach ($dimen as $key => $value) {
        foreach ($value as $k => $v) {
          if($active_fields)
           $dimen[$key][$k]['active'] = (in_array($v['id'], $active_fields)) ? 1 : 0;
        }
      }
      return $dimen;
    }, $dimens);
#   print_r($fields);
    foreach ($fields as $key => $value) {
      $pointer = array_search($value, $field_indices);
      $fd['title'] =  $pointer !== false ? $active_columns[$pointer]['name'] : $value;
      $fd['id'] = strtolower(str_replace(" ", "_" , $value));
      $fd['db_field_name'] = $value;
      $field_params = array('db_field_name' => $value,'school_id' => $_SESSION['usergroup'], 'masked_fields_only' => true);
    
      $field_data = $fieldClass->get($field_params);
      dev_debug(json_encode($field_data));

      if($field_data){
        if($field_data['type'] =='dynamic_list_group'){

          $query = explode(',', $field_data['default']);
          if($query[1]){
            $from_part = explode("order", $query[0]);
            if($from_part[1]){
              $from_string = $from_part[0] .(strpos($from_part[0], "WHERE") !== false ? " AND (usergroup = '".$_SESSION['usergroup']."' XOR usergroup = '1' )" :" WHERE 1 AND (usergroup = '".$_SESSION['usergroup']."' XOR usergroup = '1' )") . " ORDER ".$from_part[1];
            }else{
              $from_string = $from_part[0] .(strpos($from_part[0], "WHERE") !== false ? " AND (usergroup = '".$_SESSION['usergroup']."' XOR usergroup = '1' )" :" WHERE 1 AND (usergroup = '".$_SESSION['usergroup']."' XOR usergroup = '1' )") ;
            }
            
          
            if(strpos($query[1], "+") !== false){
              $select_array = explode("+", $query[1]);
              $label = $select_array[0];
            }else{
              $label = $query[1];
            }

            $sql = "SELECT id as value , $label as label FROM $from_string";
            $fd['options'] = $db->get_rows($sql);
          }
          
        }
      }
    
      if($active_fields)
        $fd['active'] = in_array($fd['id'], $active_fields) ? 1 : 0;
      else
        $fd['active'] = 1;
      $all_fields[] = $fd;
    }
    //--end set filters
    //set filter type replacement dbcolumns (This replaces a filter name with a dbfield name)
    $filter_ids = array(
      'surname' => 'db1043',
      'first_name' => 'db1041',
    'status' => 'db1056',
      'cohort' => 'db1050',
      'email_address' => 'db1058',
      'course' => 'core_courses.db232',
      'username_id' => 'lead_profiles.username_id',
      'assigned_to' => 'db69836||dropdown||general||enquiry_reviewer||join_type',
      //'date_created' => "lead_profiles.`date`||date||general||date_search_term_time_like",
      'date_created' => 'lead_profiles.`date`||date||general||date_greater_or_less_than||||{"table":"lead_profiles"}',
      'date' => "lead_profiles.`date`||date||general||date_search_term_time_like",
        'embeded_form'=> 'db32506||string||like_custom'
    );//--end filter type replacement

    $filter_add = array_keys($rename_fields);
    foreach ($filter_add as $f_add) {
      $filter_ids[$f_add] = "lead_profiles.`".$f_add."`";
    }

    if($_GET['what']){
      die(json_encode($filter_ids));
    }

    //start filter manipluation based on $filter_ids array
    if($this->filter['description']){
      dev_debug("filtex -" . json_encode($this->filter['description']));
      $enq_args['filter_sql_lite'] = prepare_filter_sql($this->filter['description'], $filter_ids);
      $filter_args = $f_args = prepare_filter_args($this->filter['description']);
      //uset all elements in $filter_ids array from filters as these will be handled by SQL
      unset($filter_args['surname']); 
      unset($filter_args['first_name']);
      unset($filter_args['cohort']);
      unset($filter_args['course']);
      unset($filter_args['username_id']);
      unset($filter_args['date_created']);
    }
    //get results not paginated if non-query based filters are present and vice versa
    
    $final_results = $enq->get_enq($enq_args);

    $applicants = new students;
      $courses = new courses;
      $form_templates = new FormTemplates;
      $users = new Users;


      $applicants_args = array('school_id'=>app()->school_info['id'],'no_one_entry'=>true , 'cohort' => $this->current_cohort,'rec_id' => $parent_id);

      //Check if there are any extra args on the url
      if(count($_GET)){
        $applicants_args = array_merge($applicants_args,$_GET);
      }


      if($_REQUEST['search']){ $applicants_args['search'] = $_REQUEST['search']; }

      if($_GET['order']){ $applicants_args['order'] = $_GET['order']; }else{  $applicants_args['order'] = 'last_name'; }

      if($_GET['export'] || $_GET['export_to_ukvi'] || $_GET['export_data'] ||$_POST['select_all_entries']){
        ini_set('memory_limit', '-1');
      }else{
       $applicants_args['paginate'] = true;
      }

      if($this->filter['description']){
        $filters_args['exploded'] = $applicants->filters_students_args($this->filter['description']);
        //if($_SESSION['usergroup'] == 3){
        $applicants_args['filter_sql_lite'] = prepare_filter_sql($this->filter['description'], $applicants->filter_columns());
          //var_dump($applicants_args);
        //}
        $applicants_args['filter'] = $this->filter;
        $applicants_args = array_merge($applicants_args,$filters_args);
      }else{
        $this->filter = array("page"=>'applicants');
      }

      //Check if the default columns exist
      $columns_args = array('page'=>'applicants');
      $page_columns = $form_templates->get_page_columns($columns_args);

      if(!$page_columns){


        //check if the HEIAPPLY super admin user has a list
        $user_info = $users->get(array('search'=>'_superadmin','school_id'=>app()->school_info['id'],'order'=>'form_users.id ASC'));
        $user_info = $user_info[0];

        if($user_info['id']){
          $columns_args['user_id']=  $user_info['id'];

          $columns = $form_templates->get_page_columns($columns_args);
          $df = json_encode($columns);
        }else{
          $df = '["id","internal_reference","first_name","middle_name","email_address"]';
        }

        // echo '<pre>';
        //  print_r($df);
        //  exit();

        $column_args = array(
          'columns'=>$df,
          'page'=>'applicants'
        );
        $form_templates->add_page_columns($column_args);
      }

      $results = $applicants->get($applicants_args);

  		$data = array(
  			'meta_title'=>'Parent Details',
  			'view_file'=>'parents/details',
        'parent_name'=>$parent_name["First Name"]." ".$parent_name["Surname"],
        'filter'=>$this->filter,
        'dimensions' => $dimensions,
        'all_fields' => $all_fields,
        'filter_types' => $filter_types,
        'filters_args'=>$f_args,
        'enq_results' => $final_results,
        'results' => $results,
  		);
    }
	$this->view($this->layout,$data);
  }
}
?>
