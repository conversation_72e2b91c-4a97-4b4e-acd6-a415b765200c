<?php 

/**
* 
*/
namespace App\controllers;
use App\core\Controller;
use App\middleware\Authentication;

class Notifications extends Controller
{
	private $cohorts;
	private $current_cohort;
    public function __construct()
    {
        $this->middleware(Authentication::class);
    }

	/** ===================================
	* Assigned to currently logged in user
	====================================  */
	public function index($type ='priority'){

		$applicants = new students;
		$courses = new courses;
		$students = new Students;
		$form_templates = new FormTemplates;
		$users = new users;
		$hooks = new Hooks;
		$messages = new CommunicationMessages;
		$files = new FilesClass;
		$e_chats = new enq_chats;

		//Cohort
		if($_GET['cohort']){
			$cohort = $_GET['cohort'];
		}else{
			$cohort = app()->school_info['cohort'];
		}	

		/** ===================================
		* Mark Selected items as read
		====================================  */
		if($_POST['action']=="mark_selected_as_read"){
			foreach ($_POST['messages_ids'] as $message) {
				$msg_args = array(
					'id'=>$message,
					'mark_as_read'=>true,
					'school_id'=>app()->school_info['id']
				);
				$messages->update_or_insert_note($msg_args);
			}

			header("Location: ".$this->base_url($this->uri_string())."?marking_done=1");
			exit();
		}



		/** ===================================
		* Bulk Reply
		====================================  */
		if($_POST['action']=="bulk_reply"){
			foreach($_POST['user_ids'] as $user_id) {
				$msg_args = array(
					'message'=>$_POST['bulk_message'],
					'student_id'=>$user_id,
					'template_name'=>$_POST['bulk_template_name'],
				);
				
	
				$messages->new_general_message($msg_args);
			}


			header("Location: ".$this->base_url($this->uri_string())."?bulk_reply_done=1");
			exit();
		}

		if($_GET['read']){
			$unread_messages_only = false;
		}else{
			$unread_messages_only = true;
		}
		/** ===================================
		* All message
		====================================  */
		// if(!$type){
		// 	$all_messages = array('reviewer_id'=>$_SESSION['uid'],'no_replies'=>true,'paginate'=>true,'unread'=>$unread_messages_only,'school_id'=>app()->school_info['id']);
		// 	if($course_id != 'level'){
		// 		$all_messages['course_id'] = $course_id;
		// 	}
		// 	$message = $messages->get($all_messages);
		// }

		if($_GET['order']){
			$order = "date ".$_GET['order'];
		}else{
			$order = "date ASC";
		}

		/** ===================================
		* Tagged Internal Notes
		====================================  */
		if($type=="tagged"){
			$general_questions = array('reviewer_id'=>$_SESSION['uid'],'no_replies'=>true,'type_id'=>1,'paginate'=>true,'unread'=>$unread_messages_only,'school_id'=>app()->school_info['id'], 'order' => $order);
			$message = $messages->get_internal_notes($general_questions);
		}

		$tag_args = array('reviewer_id'=>$_SESSION['uid'],'no_replies'=>true,'type_id'=>1,'count'=>true,'unread'=>$unread_messages_only,'school_id'=>app()->school_info['id']);
		$tag_count = $messages->get_internal_notes($tag_args);

		/** ===================================
		* Submission Notification
		====================================  */
		if($type=="reminders"){
			$reminders = array('assigned_to_me'=>$_SESSION['uid'],'due_by'=>'soon','no_replies'=>true,'type_id'=>8,'paginate'=>true,'unread'=>$unread_messages_only,'school_id'=>app()->school_info['id'],'order' => $order);
			$message = $hooks->get_tasks($reminders);
		}

		$reminders_count_args = array('assigned_to_me'=>$_SESSION['uid'],'due_by'=>'soon','no_replies'=>true,'type_id'=>8,'count'=>true,'unread'=>$unread_messages_only, 'school_id'=>app()->school_info['id']);
		$reminders_count = $hooks->get_tasks($reminders_count_args);

		if($type=="priority"){
			$reminders_p = array('assigned_to_me'=>$_SESSION['uid'],'due_by'=>'today','no_replies'=>true,'type_id'=>8,'paginate'=>true,'unread'=>$unread_messages_only,'school_id'=>app()->school_info['id'],'order' => $order);
			$message = $hooks->get_tasks($reminders_p);
		}

		$reminders_p_count_args = array('assigned_to_me'=>$_SESSION['uid'],'due_by'=>'today','no_replies'=>true,'type_id'=>8,'count'=>true,'unread'=>$unread_messages_only, 'school_id'=>app()->school_info['id']);
		$reminders_p_count = $hooks->get_tasks($reminders_p_count_args);

		/**===================================
		* Enquiry Chat Notifications
		====================================  */
		if($type=="enquirychat"){
			$e_args = array('assigned_to_me'=>$_SESSION['uid'],'due_by'=>'today','no_replies'=>true,'type_id'=>8,'paginate'=>true,'unread'=>$unread_messages_only,'school_id'=>app()->school_info['id'],'order' => $order);
			$message = $e_chats->get_notifications($e_args);
		}
		$e_chats_count_args = array('assigned_to_me'=>$_SESSION['uid'],'due_by'=>'today','no_replies'=>true,'type_id'=>8,'count'=>true,'unread'=>$unread_messages_only, 'school_id'=>app()->school_info['id']);
		$e_chats_count = $e_chats->get_notifications($e_chats_count_args);


		/** ===================================
		* File Upload Notifications
		====================================  */
		if($type=="general"){
			$file_upload_notifications = array('reviewer_id'=>$_SESSION['uid'],'no_replies'=>true,'type_id'=>9,'paginate'=>true,'unread'=>$unread_messages_only,'school_id'=>app()->school_info['id'],'order' => $order);
			$message = $files->get_file_notifications($file_upload_notifications);
		}

		$file_upload_count = array('reviewer_id'=>$_SESSION['uid'],'no_replies'=>true,'type_id'=>9,'count'=>true,'school_id'=>app()->school_info['id'], 'unread'=>$unread_messages_only);
		$files_count = $files->get_file_notifications($file_upload_count);

		if($type == 'json_count'){
			$array = array(
				'success' => true,
				'data' => ($files_count[0]['count'] + $reminders_count[0]['count'] + $reminders_p_count[0]['count'] + $tag_count[0]['count']),
				'method' => __FUNCTION__
			);
			echo json_encode($array);
			exit();
		}


		$view = 'notifications/index';
		$data = array(
			'meta_title'=>'Notifications',
			'custom_title'=>'Notifications',
			'view_file'=>$view,
			'results'=>$message,
			'type'=>$type,
			'cohort' => $cohort,
			'paginator'=>$paginator,
			'filter'=>$this->filter,
			'filters_args'=>$filters_args,
			'filter_page_name'=>'course_applicants',
			'active_tab'=>'notifications',
			'alert_message'=>$results_prep['alert_message'],
			'hide_export_button'=>true,
			'hide_send_private_messages'=>true,
			'hide_edit_checklist'=>true,
			'hide_review_media'=>true,
			'back_link'=>false,
			'show_tabs' => true,
			'back_link_title'=>false,
			'saved_filters_dropdown' => true,
			'file_upload_count' => $files_count[0],
			'reminders_count' => $reminders_count[0],
			'reminders_p_count' => $reminders_p_count[0],
			'tag_count' => $tag_count[0]

		);
		$this->view($this->layout,$data);

	}


	public function assigned_to_me_reviews()
	{

		$students = new Students;

		//Cohort
		if($_GET['cohort']){
			$cohort = $_GET['cohort'];
		}else{
			$cohort = app()->school_info['cohort'];
		}	

		/** ===================================
		* Reviews Results
		====================================  */
		$reviews_args = array(
			"reviewer_id"=>$_SESSION['uid'],
			'school_id'=>app()->school_info['id'],
			'paginate'=>true,
			);		
		$reviews_args['cohort'] = $cohort;
		if($_GET['search']){
			$reviews_args['search'] = $_GET['search'];
		}

		if($_GET['order']){
			$reviews_args['order'] = $_GET['order'];
		}
		$results = $students->reviews($reviews_args);

		//Links
		$back_link = false;
		$back_link_title = "Programmes";


		$data = array(
			'meta_title'=>'Applicants assigned to me - Reviews',
			'view_file'=>'applicants/reviews',
			'results'=>$results,
			'active_tab' => 'reviews',
			'level'=>$level,
			'cohorts'=>$cohorts,
			'cohort'=>$cohort,
			'programme'=>$programme,
			'back_link'=>$back_link,
			'back_link_title'=>false,
			'saved_filters_dropdown' => true
		);
		$this->view($this->layout,$data);
	}

	/** ===================================
	* Screening New
	====================================  */
	public function assigned_to_me_screening()
	{
		
		$applicants = new students;
		$courses = new courses;
		$students = new Students;
		$form_templates = new FormTemplates;

		//Prep the applicants screen
		$prep_args = array(
			"extra_applicants_args"=>array("reviewer_id"=>$_SESSION['uid'],'no_one_entry'=>true,'has_submitted'=>true),
			"school_id"=>app()->school_info['id'],
			"filter"=>$this->filter,
			"filter_page_name"=>"screening_applicants"
		);
		$results_prep = $students->prepare_applicants_screen($prep_args);
		$this->filter = $results_prep['filter'];



		//Links
		//$back_link = $this->base_url('course_levels/'.$this->uri_segement(2).'/programmes/');
		$back_link =false;
		$back_link_title = "Programmes";

		$data = array(
			'main_title'=>'Applicants assigned to me - Screening',
			'meta_title'=>'Applicants assigned to me - Screening',
			'view_file'=>'applicants/screening_new',
			'type_id'=>$type_id,
			'programme'=>$programme,
			'results'=>$results_prep['results'],
			'active_tab' => 'screening',
			'paginator'=>$paginator,
			'filter'=>$this->filter,
			'filters_args'=>$filters_args,
			'filter_page_name'=>'screening_applicants',
			'active_tab'=>'screening',
			'alert_message'=>$results_prep['alert_message'],
			'hide_export_button'=>false,
			'back_link'=>$back_link,
			'back_link_title'=>$back_link_title,
			'saved_filters_dropdown' => true
		);
		$this->view($this->layout,$data);

	}

}
?>