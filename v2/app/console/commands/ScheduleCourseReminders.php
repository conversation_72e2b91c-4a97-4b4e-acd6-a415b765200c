<?php

	namespace App\console\commands;

	use App\core\helpers\Collection;
    use App\models\CommunicationMessages;
    use App\models\Emails;
	use App\models\Enquiries;
	use App\models\Fields;
	use App\models\FormTemplates;
	use App\models\ShortCourses;
	use Doctrine\DBAL\Driver\Exception;
	use Symfony\Component\Console\Command\Command;
	use Symfony\Component\Console\Input\InputArgument;
	use Symfony\Component\Console\Input\InputInterface;
	use Symfony\Component\Console\Output\OutputInterface;
	use App\console\commands\reports\ExportAllStudentsDataJob;
	use Twilio\Rest\Client;

//require_once __DIR__ . '/twilio-php-master/Twilio/autoload.php';

	class ScheduleCourseReminders extends Command
	{
		protected static $defaultName = 'reminders:send--schedule-course-reminders';
		public $processed_jobs = 0;
		public $failed_jobs = 0;
		public $pending_jobs = 0;
		
		protected $debug_mode;
		
		
		protected function configure()
		{
			$this->setDescription('Process bulk enquiry email reminders for scheduled courses')
				->setHelp('Process bulk enquiry email reminders for scheduled courses');
			
		}
		
		/**
		 * @throws Exception
		 * @throws \Doctrine\DBAL\Exception
		 */
		protected function execute(InputInterface $input, OutputInterface $output)
		{
			$output->writeln('Starting scheduled course reminder'.date('m/d/Y h:i:s a', time()));
			$dbh = dbh();
            $_SESSION['uid'] = 1;
			$school_type_filter=" db30='12' AND ";//db=12 if for MRN schools only


            //send out notification 1
			//all course bookings with no sessions sql
			$sql_select = "SELECT
	            cb.id ,
	            cb.rel_id,
	            CONCAT(db48566, ' ' , db48568) AS full_name,
	            db48619 as 'title',
	            db48566 AS student_name,
	            db48599 AS student_number,
	            db48583 AS student_email,
	            db48621 as student_address,
	            db48596 as student_town,
	            db48622 as student_postcode,
	            db48597 as student_phone,
	            db232 AS course_title,
	            IFNULL(db234,'') AS course_summary,
	            db14947 AS course_start_date,
	            db14948 AS course_start_time,
	            db14949 AS course_end_date,
	            db14950 AS course_end_time,
	            db63007 as 'scheduled_course_information',
	            db14956 as 'additional_information',
	            IFNULL(db14953,'') as course_minimum_attendees,
	            CONCAT_WS(', ',db14963, db14965,db14966) as course_venue,
	            db14968 as 'course_venue_information',
	            db14966 AS course_postcode,
	            IFNULL(db209162,'') AS course_whatthreewords,
	            IFNULL(db237356,'') AS course_venue_image,
	            db14970 AS booking_status,
	            scs.id as course_schedule,
	            cb.usergroup,
	            db19831 as 'reminder_template',
	            lp.id as 'sis_profile_id',
	            lp.rel_id as 'sis_profile_rel_id',
	            cbd.id as 'sched_detail_id',
	            8 as letter_tag,
	            5 as sms_template_tag
	            FROM sis_sched_booking_detail cbd
	            LEFT JOIN form_schools on cbd.usergroup = form_schools.id
	            LEFT JOIN sis_scheduled_booking cb ON cbd.db15052 = cb.id
	            LEFT JOIN core_students cs ON cs.id=cbd.rel_id
	            LEFT JOIN sis_profiles lp ON lp.rel_id=cs.rel_id
	            LEFT JOIN sis_course_schedule scs ON scs.id=db14977
	            LEFT JOIN sis_booking_status bs ON bs.id=db14983
	            LEFT JOIN core_courses cm ON cm.id=db16136
	            LEFT JOIN sis_course_venues cv ON cv.id=db14954
	            WHERE $school_type_filter 
                (cb.rec_archive IS NULL OR cb.rec_archive ='')
	            AND (lp.rec_archive IS NULL OR lp.rec_archive ='')
	            AND (scs.rec_archive IS NULL OR scs.rec_archive ='')
	            AND (cbd.rec_archive IS NULL OR cbd.rec_archive ='')
                AND (db14983 = 2 or db14983 = 3 or db59978= 2 or db59978=3)
	            AND db14959 != 5
	            AND (db66749 IS NULL OR db66749 !='yes')
	            AND (SELECT count(*) from sis_scheduled_sessions 
                    where sis_scheduled_sessions.rel_id = db14977 and (sis_scheduled_sessions.rec_archive is null or sis_scheduled_sessions.rec_archive ='')
                ) = 0
	            AND (
                db14947 > CURDATE() 
                AND db14947 = DATE_ADD(CURDATE(), INTERVAL (SELECT db111500 FROM lead_preferences WHERE usergroup= cb.usergroup) DAY)
	            )
                AND (db284645 ='yes' or db284645 IS NULL or db284645 = '')
            ";
            $output->writeln('Starting with_no_sessions_not1'.date('m/d/Y h:i:s a', time()));
			$sth = $dbh->prepare("$sql_select");
			$sth->execute();
			$all_notifications_to_be_sent = $sth->fetchAll(\PDO::FETCH_OBJ);
            $output->writeln('middle with_no_sessions_not1'.date('m/d/Y h:i:s a', time()));
			$type_of_sql = 'with_no_sessions_not1';
			$template_tag = 'course_booking_reminder';
			$interval_db_field = 'db111500';
			$processing_response_with_no_sessions = $this->process_notifications($all_notifications_to_be_sent,$type_of_sql,$template_tag,$interval_db_field);
            $output->writeln('ending with_no_sessions_not1'.date('m/d/Y h:i:s a', time()));
			//send out notification 1
			//all course bookings(WITH SESSIONS) but session reminders switched off send out as normal course reminders
			$sql1_select = "SELECT
                cb.id ,
                cb.rel_id,
                CONCAT(db48566, ' ' , db48568) AS full_name,
                db48619 as 'title',
                db48566 AS student_name,
                db48599 AS student_number,
                db48583 AS student_email,
                db48621 as student_address,
                db48596 as student_town,
                db48622 as student_postcode,
                db48597 as student_phone,
                db232 AS course_title,
                IFNULL(db234,'') AS course_summary,
                db14947 AS course_start_date,
                db14948 AS course_start_time,
                db14949 AS course_end_date,
                db14950 AS course_end_time,
                db63007 as 'scheduled_course_information',
                db14956 as 'additional_information',
                IFNULL(db14953,'') as course_minimum_attendees,
                CONCAT_WS(', ',db14963, db14965,db14966) as course_venue,
                db14968 as 'course_venue_information',
                db14966 AS course_postcode,
                IFNULL(db209162,'') AS course_whatthreewords,
                IFNULL(db237356,'') AS course_venue_image,
                db14970 AS booking_status,
	            scs.id as course_schedule,
                cb.usergroup,
                db19831 as 'reminder_template',
                lp.id as 'sis_profile_id',
                lp.rel_id as 'sis_profile_rel_id',
                cbd.id as 'sched_detail_id',
                8 as letter_tag,
	            5 as sms_template_tag
                FROM sis_sched_booking_detail cbd
                LEFT JOIN form_schools on cbd.usergroup = form_schools.id
                LEFT JOIN sis_scheduled_booking cb ON cbd.db15052 = cb.id
                LEFT JOIN core_students cs ON cs.id=cbd.rel_id
                LEFT JOIN sis_profiles lp ON lp.rel_id=cs.rel_id
                LEFT JOIN sis_course_schedule scs ON scs.id=db14977
                LEFT JOIN sis_booking_status bs ON bs.id=db14983
                LEFT JOIN core_courses cm ON cm.id=db16136
                LEFT JOIN sis_course_venues cv ON cv.id=db14954
                WHERE $school_type_filter 
                (cb.rec_archive IS NULL OR cb.rec_archive ='')
                AND (lp.rec_archive IS NULL OR lp.rec_archive ='')
                AND (scs.rec_archive IS NULL OR scs.rec_archive ='')
                AND (cbd.rec_archive IS NULL OR cbd.rec_archive ='')
                AND (db14983 = 2 or db14983 = 3 or db59978= 2 or db59978=3)
                AND db14959 != 5
                AND (db66749 IS NULL OR db66749 !='yes')
                AND (SELECT count(*) from sis_scheduled_sessions where sis_scheduled_sessions.rel_id = db14977 and (sis_scheduled_sessions.rec_archive is null or sis_scheduled_sessions.rec_archive ='')) > 0
                AND (db14947 > CURDATE()
                	AND db14947 = DATE_ADD(CURDATE(), INTERVAL (SELECT db111500 FROM lead_preferences WHERE usergroup= cb.usergroup) DAY)
                )
                AND (SELECT db111515 FROM lead_preferences WHERE usergroup= cb.usergroup) ='yes'
                AND (db284645 ='yes' or db284645 IS NULL or db284645 = '' )
            ";
            $output->writeln('starting with_sessions_not1'.date('m/d/Y h:i:s a', time()));
			$sth1 = $dbh->prepare("$sql1_select");
			$sth1->execute();
			$all_notifications_to_be_sent1 = $sth1->fetchAll(\PDO::FETCH_OBJ);
            $output->writeln('middle with_sessions_not1'.date('m/d/Y h:i:s a', time()));
			$type_of_sql1 = 'with_sessions_not1';
			$template_tag1 = 'course_booking_reminder';
			$interval_db_field1 = 'db111500';
			$processing_response_with_sessions = $this->process_notifications($all_notifications_to_be_sent1,$type_of_sql1,$template_tag1,$interval_db_field1);
            $output->writeln('ending with_sessions_not1'.date('m/d/Y h:i:s a', time()));
			//send out session reminder notification 1
            //all sessions booking with pending bookings not sent
            //usergroup has not disabled session reminders
			$sql2_select = "SELECT
	            ssb.id ,
	            cb.rel_id,
	            CONCAT(db48566, ' ' , db48568) AS full_name,
	            db48619 as 'title',
	            db48566 AS student_name,
	            db48599 AS student_number,
	            db48583 AS student_email,
	            db48621 as student_address,
	            db48596 as student_town,
	            db48622 as student_postcode,
	            db48597 as student_phone,
	            db232 AS course_title,
	            IFNULL(db234,'') AS course_summary,
	            db59906 AS course_session_name,
	            db59835 AS course_start_date,
	            db59836 AS course_start_time,
	            db59837 AS course_end_date,
	            db59838 AS course_end_time,
	            IFNULL(db14953,'') as course_minimum_attendees,
	            CONCAT_WS(', ',db14963, db14965,db14966) as course_venue,
	            db63007 as 'scheduled_course_information',
                db14956 as 'additional_information',
	            db14968 as 'course_venue_information',
	            db14966 AS course_postcode,
	            IFNULL(db209162,'') AS course_whatthreewords,
	            IFNULL(db237356,'') AS course_venue_image,
	            db14970 AS booking_status,
	            db63025 AS 'scheduled_session_information',
	            cb.usergroup,
	            db19831 as 'reminder_template',
	            scs.id as course_schedule,
	            lp.id as 'sis_profile_id',
	            lp.rel_id as 'sis_profile_rel_id',
	            cbd.id as 'sched_detail_id',
	            8 as letter_tag,
	            8 as sms_template_tag
	            FROM sis_session_bookings ssb
	            LEFT JOIN form_schools on ssb.usergroup = form_schools.id
	            LEFT JOIN sis_scheduled_booking cb ON ssb.db59976 = cb.id
	            LEFT JOIN sis_sched_booking_detail cbd ON CAST(ssb.db59977 AS UNSIGNED)=cbd.id
	            LEFT JOIN core_students cs ON cs.id=cbd.rel_id
	            LEFT JOIN sis_profiles lp ON lp.rel_id=cs.rel_id
	            LEFT JOIN sis_course_schedule scs ON scs.id=db14977
	            LEFT JOIN sis_booking_status bs ON bs.id=db14983
	            LEFT JOIN core_courses cm ON cm.id=db16136
	            LEFT JOIN sis_scheduled_sessions ss on ss.id = db59901
	            LEFT JOIN sis_course_venues cv ON cv.id=db59839
	            LEFT JOIN sis_course_sessions cses on cses.id = db59900
	            WHERE $school_type_filter 
                (cb.rec_archive IS NULL OR cb.rec_archive ='')
	            AND (cs.rec_archive IS NULL OR cs.rec_archive ='')
	            AND db14959 != 5
	            AND db59908 != 5
	            AND (ssb.rec_archive IS NULL OR ssb.rec_archive ='')
	            AND (lp.rec_archive IS NULL OR lp.rec_archive ='')
	            AND (cbd.rec_archive IS NULL OR cbd.rec_archive ='')
	            AND (ss.rec_archive IS NULL OR ss.rec_archive ='')
	            AND (scs.rec_archive IS NULL OR scs.rec_archive ='')
                AND (db14983 = 2 or db14983 = 3 or db59978= 2 or db59978=3)
	        	AND (db59902 = 2 OR db59902 =3)
	            AND (db59904 IS NULL OR db59904 !='yes')
	            AND (db284645 ='yes' or db284645 IS NULL or db284645 = '')
	            AND (db59835  > CURDATE() AND db59835 = DATE_ADD(CURDATE(), INTERVAL (SELECT db111500  FROM lead_preferences WHERE usergroup= cb.usergroup) DAY))
	            AND ((SELECT db111515 FROM lead_preferences WHERE usergroup= cb.usergroup) !='yes' OR (SELECT db111515 FROM lead_preferences WHERE usergroup= cb.usergroup) IS NULL)
            ";
            $output->writeln('starting with_all_sessions_notification1'.date('m/d/Y h:i:s a', time()));
			$sth2 = $dbh->prepare("$sql2_select");
			$sth2->execute();
			$all_notifications_to_be_sent2 = $sth2->fetchAll(\PDO::FETCH_OBJ);
            $output->writeln('middle with_all_sessions_notification1'.date('m/d/Y h:i:s a', time()));
			$type_of_sql2 = 'with_all_sessions_notification1';
			$template_tag2 = 'course_session_reminder';
			$interval_db_field2 = 'db111500';
			$processing_response_with_sessions_not1 = $this->process_notifications($all_notifications_to_be_sent2,$type_of_sql2,$template_tag2,$interval_db_field2);
            $output->writeln('ending with_all_sessions_notification1'.date('m/d/Y h:i:s a', time()));
			//course reminder notification 2
            // bookings with zero sessions
			$sql_select3 = "SELECT
	            cb.id ,
	            cb.rel_id,
	            CONCAT(db48566, ' ' , db48568) AS full_name,
	            db48619 as 'title',
	            db48566 AS student_name,
	            db48599 AS student_number,
	            db48583 AS student_email,
	            db48621 as student_address,
	            db48596 as student_town,
	            db48622 as student_postcode,
	            db48597 as student_phone,
	            db232 AS course_title,
	            IFNULL(db234,'') AS course_summary,
	            db14947 AS course_start_date,
	            db14948 AS course_start_time,
	            db14949 AS course_end_date,
	            db14950 AS course_end_time,
	            IFNULL(db14953,'') as course_minimum_attendees,
	            db63007 as 'scheduled_course_information',
	            db14956 as 'additional_information',
	            CONCAT_WS(', ',db14963, db14965,db14966) as course_venue,
	            db14968 as 'course_venue_information',
	            db14966 AS course_postcode,
	            IFNULL(db209162,'') AS course_whatthreewords,
	            IFNULL(db237356,'') AS course_venue_image,
	            db14970 AS booking_status,
	            scs.id as course_schedule,
	            cb.usergroup,
	            db19831 as 'reminder_template',
	            lp.id as 'sis_profile_id',
	            lp.rel_id as 'sis_profile_rel_id',
	            cbd.id as 'sched_detail_id',
	            8 as letter_tag,
	            5 as sms_template_tag
	            FROM sis_sched_booking_detail cbd
	            LEFT JOIN form_schools on cbd.usergroup = form_schools.id
	            LEFT JOIN sis_scheduled_booking cb ON cbd.db15052 = cb.id
	            LEFT JOIN core_students cs ON cs.id=cbd.rel_id
	            LEFT JOIN sis_profiles lp ON lp.rel_id=cs.rel_id
	            LEFT JOIN sis_course_schedule scs ON scs.id=db14977
	            LEFT JOIN sis_booking_status bs ON bs.id=db14983
	            LEFT JOIN core_courses cm ON cm.id=db16136
	            LEFT JOIN sis_course_venues cv ON cv.id=db14954
	            WHERE $school_type_filter (cb.rec_archive IS NULL OR cb.rec_archive ='')
	              AND (cs.rec_archive IS NULL OR cs.rec_archive ='')
	              AND db14959 != 5
	              AND (lp.rec_archive IS NULL OR lp.rec_archive ='')
	              AND (cbd.rec_archive IS NULL OR cbd.rec_archive ='')
	              AND (scs.rec_archive IS NULL OR scs.rec_archive ='')
                  AND (db14983 = 2 or db14983 = 3 or db59978= 2 or db59978=3)
	              AND (db66750 IS NULL OR db66750 !='yes')
                  AND (db284645 ='yes' or db284645 IS NULL or db284645 = '')
	              AND (SELECT count(*) from sis_scheduled_sessions where sis_scheduled_sessions.rel_id = db14977 and (sis_scheduled_sessions.rec_archive is null or sis_scheduled_sessions.rec_archive ='')) = 0
	              AND (db14947  > CURDATE() AND db14947 = DATE_ADD(CURDATE(), INTERVAL (SELECT db111503 FROM lead_preferences WHERE usergroup=cb.usergroup) DAY))
            ";
            $output->writeln('starting with_no_sessions_not2'.date('m/d/Y h:i:s a', time()));
			$sth3 = $dbh->prepare("$sql_select3");
			$sth3->execute();
			$all_notifications_to_be_sent3 = $sth3->fetchAll(\PDO::FETCH_OBJ);
            $output->writeln('middle with_no_sessions_not2'.date('m/d/Y h:i:s a', time()));
			$type_of_sql3 = 'with_no_sessions_not2';
			$template_tag3 = 'course_booking_reminder';
			$interval_db_field3 = 'db111503';
			$processing_response_with_no_sessions3 = $this->process_notifications($all_notifications_to_be_sent3,$type_of_sql3,$template_tag3,$interval_db_field3);
            $output->writeln('ending with_no_sessions_not2'.date('m/d/Y h:i:s a', time()));
		    //course reminder 2
            //all bookings with sessions but session reminders switched off
			$sql_select4 = "SELECT
                cb.id ,
                cb.rel_id,
                CONCAT(db48566, ' ' , db48568) AS full_name,
                db48619 as 'title',
                db48566 AS student_name,
                db48599 AS student_number,
                db48583 AS student_email,
                db48621 as student_address,
                db48596 as student_town,
                db48622 as student_postcode,
                db48597 as student_phone,
                db232 AS course_title,
                IFNULL(db234,'') AS course_summary,
                db14947 AS course_start_date,
                db14948 AS course_start_time,
                db14949 AS course_end_date,
                db14950 AS course_end_time,
                db63007 as 'scheduled_course_information',
                db14956 as 'additional_information',
                IFNULL(db14953,'') as course_minimum_attendees,
                CONCAT_WS(', ',db14963, db14965,db14966) as course_venue,
                db14968 as 'course_venue_information',
                db14966 AS course_postcode,
                IFNULL(db209162,'') AS course_whatthreewords,
                IFNULL(db237356,'') AS course_venue_image,
                db14970 AS booking_status,
	            scs.id as course_schedule,
                cb.usergroup,
                db19831 as 'reminder_template',
                lp.id as 'sis_profile_id',
                lp.rel_id as 'sis_profile_rel_id',
                cbd.id as 'sched_detail_id',
                8 as letter_tag,
	            5 as sms_template_tag
                FROM sis_sched_booking_detail cbd
                LEFT JOIN form_schools on cbd.usergroup = form_schools.id
                LEFT JOIN sis_scheduled_booking cb ON cbd.db15052 = cb.id
                LEFT JOIN core_students cs ON cs.id=cbd.rel_id
                LEFT JOIN sis_profiles lp ON lp.rel_id=cs.rel_id
                LEFT JOIN sis_course_schedule scs ON scs.id=db14977
                LEFT JOIN sis_booking_status bs ON bs.id=db14983
                LEFT JOIN core_courses cm ON cm.id=db16136
                LEFT JOIN sis_course_venues cv ON cv.id=db14954
                WHERE $school_type_filter (cb.rec_archive IS NULL OR cb.rec_archive ='')
                  AND (cs.rec_archive IS NULL OR cs.rec_archive ='')
                  AND db14959 != 5
                  AND (lp.rec_archive IS NULL OR lp.rec_archive ='')
                  AND (cbd.rec_archive IS NULL OR cbd.rec_archive ='')
                  AND (scs.rec_archive IS NULL OR scs.rec_archive ='')
                  AND (db14983 = 2 or db14983 = 3 or db59978= 2 or db59978=3)
                  AND (db66750 IS NULL OR db66750 !='yes')
                  AND (db284645 ='yes' or db284645 IS NULL or db284645 = '')
                  AND (SELECT count(*) from sis_scheduled_sessions where sis_scheduled_sessions.rel_id = db14977 and (sis_scheduled_sessions.rec_archive is null or sis_scheduled_sessions.rec_archive ='')) > 0
                  AND (db14947  > CURDATE() AND db14947 = DATE_ADD(CURDATE(), INTERVAL (SELECT db111503 FROM lead_preferences WHERE usergroup=cb.usergroup) DAY))
                  AND (SELECT db111515 FROM lead_preferences WHERE usergroup= cb.usergroup) ='yes' 
            ";
            $output->writeln('starting with_sessions_not2'.date('m/d/Y h:i:s a', time()));
			$sth4 = $dbh->prepare("$sql_select4");
			$sth4->execute();
			$all_notifications_to_be_sent4 = $sth4->fetchAll(\PDO::FETCH_OBJ);
            $output->writeln('middle with_sessions_not2'.date('m/d/Y h:i:s a', time()));
			$type_of_sql4 = 'with_sessions_not2';
			$template_tag4 = 'course_booking_reminder';
			$interval_db_field4 = 'db111503';
			$processing_response_with_no_sessions4 = $this->process_notifications($all_notifications_to_be_sent4,$type_of_sql4,$template_tag4,$interval_db_field4);
            $output->writeln('ending with_sessions_not2'.date('m/d/Y h:i:s a', time()));
			
			//reminder notification 2
            // all session bookings that are to get session reminder 2
			$sql_select5 = "SELECT
                ssb.id ,
                cb.rel_id,
                CONCAT(db48566, ' ' , db48568) AS full_name,
                db48619 as 'title',
                db48566 AS student_name,
                db48599 AS student_number,
                db48583 AS student_email,
                db48621 as student_address,
                db48596 as student_town,
                db48622 as student_postcode,
                db48597 as student_phone,
                db232 AS course_title,
                IFNULL(db234,'') AS course_summary,
                db59906 AS course_session_name,
                db59835 AS course_start_date,
                db59836 AS course_start_time,
                db59837 AS course_end_date,
                db59838 AS course_end_time,
                IFNULL(db14953,'') as course_minimum_attendees,
                CONCAT_WS(', ',db14963, db14965,db14966) as course_venue,
                db14968 as 'course_venue_information',
                db14966 AS course_postcode,
                IFNULL(db209162,'') AS course_whatthreewords,
                IFNULL(db237356,'') AS course_venue_image,
                db14970 AS booking_status,
                db63025 AS 'scheduled_session_information',
                db63007 as 'scheduled_course_information',
                db14956 as 'additional_information',
                cb.usergroup,
                db19831 as 'reminder_template',
                scs.id as course_schedule,
                lp.id as 'sis_profile_id',
                lp.rel_id as 'sis_profile_rel_id',
                cbd.id as 'sched_detail_id',
                8 as letter_tag,
	            8 as sms_template_tag
                FROM sis_session_bookings ssb
                LEFT JOIN form_schools on ssb.usergroup = form_schools.id
                LEFT JOIN sis_scheduled_booking cb ON ssb.db59976 = cb.id
                LEFT JOIN sis_sched_booking_detail cbd ON CAST(ssb.db59977 AS UNSIGNED)=cbd.id
                LEFT JOIN core_students cs ON cs.id=cbd.rel_id
                LEFT JOIN sis_profiles lp ON lp.rel_id=cs.rel_id
                LEFT JOIN sis_course_schedule scs ON scs.id=db14977
                LEFT JOIN sis_booking_status bs ON bs.id=db59978
                LEFT JOIN core_courses cm ON cm.id=db16136
                LEFT JOIN sis_scheduled_sessions ss on ss.id = db59901
                LEFT JOIN sis_course_venues cv ON cv.id=db59839
                LEFT JOIN sis_course_sessions cses on cses.id = db59900
                WHERE $school_type_filter
                (cb.rec_archive IS NULL OR cb.rec_archive ='')
                AND (cs.rec_archive IS NULL OR cs.rec_archive ='')
                AND db14959 != 5
                AND db59908 != 5
                AND (db284645 ='yes' or db284645 IS NULL or db284645 = '')
                AND (ssb.rec_archive IS NULL OR ssb.rec_archive ='')
                AND (lp.rec_archive IS NULL OR lp.rec_archive ='')
                AND (cbd.rec_archive IS NULL OR cbd.rec_archive ='')
                AND (ss.rec_archive IS NULL OR ss.rec_archive ='')
                AND (scs.rec_archive IS NULL OR scs.rec_archive ='')
                AND (db14983 = 2 or db14983 = 3 or db59978= 2 or db59978=3)
                AND (db59902 = 2 OR db59902 =3)
                AND (db59905 IS NULL OR db59905 !='yes')
                AND (db59835  > CURDATE() AND db59835 = DATE_ADD(CURDATE(), INTERVAL (SELECT db111503  FROM lead_preferences WHERE usergroup= cb.usergroup) DAY))
                AND ((SELECT db111515 FROM lead_preferences WHERE usergroup= cb.usergroup) !='yes' OR (SELECT db111515 FROM lead_preferences WHERE usergroup= cb.usergroup) IS NULL)
            ";
            $output->writeln('starting with_all_sessions_notification2'.date('m/d/Y h:i:s a', time()));
			$sth5 = $dbh->prepare("$sql_select5");
			$sth5->execute();
			$all_notifications_to_be_sent5 = $sth5->fetchAll(\PDO::FETCH_OBJ);
            $output->writeln('middle with_all_sessions_notification2'.date('m/d/Y h:i:s a', time()));
			$type_of_sql5 = 'with_all_sessions_notification2';
			$template_tag5 = 'course_session_reminder';
			$interval_db_field5 = 'db111503';
			$processing_response_with_sessions_not5 = $this->process_notifications($all_notifications_to_be_sent5,$type_of_sql5,$template_tag5,$interval_db_field5);
            $output->writeln('ending with_all_sessions_notification2'.date('m/d/Y h:i:s a', time()));
			// confirmation notifications
			$confirm_sql = "SELECT
	            cb.id ,
	            cb.rel_id,
	            CONCAT(db48566, ' ' , db48568) AS full_name,
	            db48619 as 'title',
	            db48566 AS student_name,
	            db48599 AS student_number,
	            db48583 AS student_email,
	            db48621 as student_address,
	            db48596 as student_town,
	            db48622 as student_postcode,
	            db48597 as student_phone,
	            db232 AS course_title,
	            IFNULL(db234,'') AS course_summary,
	            db14947 AS course_start_date,
	            db14949 AS course_end_date,
	            db14948 AS course_start_time,
	            db14950 AS course_end_time,
	            db63007 as 'scheduled_course_information',
	            db14956 as 'additional_information',
	            IFNULL(db14953,'') as course_minimum_attendees,
	            CONCAT_WS(', ',db14963, db14965,db14966) as course_venue,
	            db14968 as 'course_venue_information',
	            db14966 AS course_postcode,
	            IFNULL(db209162,'') AS course_whatthreewords,
	            IFNULL(db237356,'') AS course_venue_image,
	            db14970 AS booking_status,
	            scs.id as course_schedule,
	            cb.usergroup,
	            db237314 as 'reminder_template',
	            lp.id as 'sis_profile_id',
	            lp.rel_id as 'sis_profile_rel_id',
	            cbd.id as 'sched_detail_id',
	            20 as letter_tag,
	            23 as sms_template_tag
	            FROM sis_sched_booking_detail cbd
	            LEFT JOIN form_schools on cbd.usergroup = form_schools.id
	            LEFT JOIN sis_scheduled_booking cb ON cbd.db15052 = cb.id
	            LEFT JOIN core_students cs ON cs.id=cbd.rel_id
	            LEFT JOIN sis_profiles lp ON lp.rel_id=cs.rel_id
	            LEFT JOIN sis_course_schedule scs ON scs.id=db14977
	            LEFT JOIN sis_booking_status bs ON bs.id=db14983
	            LEFT JOIN core_courses cm ON cm.id=db16136
	            LEFT JOIN sis_course_venues cv ON cv.id=db14954
	            WHERE $school_type_filter 
                (cb.rec_archive IS NULL OR cb.rec_archive ='')
	            AND (lp.rec_archive IS NULL OR lp.rec_archive ='')
	            AND (scs.rec_archive IS NULL OR scs.rec_archive ='')
	            AND (cbd.rec_archive IS NULL OR cbd.rec_archive ='')
                AND (db14983 = 2 or db14983 = 3 or db59978= 2 or db59978=3)
	            AND db14959 != 5
                AND (db284645 ='yes' or db284645 IS NULL or db284645 = '')
	            AND (db250781 IS NULL OR db250781 != 'yes')
	            AND (db14947 > CURDATE() 
                AND db14947 = DATE_ADD(CURDATE(), INTERVAL (SELECT db237194 FROM lead_preferences WHERE usergroup= cb.usergroup) DAY)
	            )
            ";
            $output->writeln('starting confirm_email_notification'.date('m/d/Y h:i:s a', time()));
			$sth7 = $dbh->prepare("$confirm_sql");
			$sth7->execute();
			$all_notifications_to_be_sent7 = $sth7->fetchAll(\PDO::FETCH_OBJ);
            $output->writeln('middle confirm_email_notification'.date('m/d/Y h:i:s a', time()));
			$type_of_sql7 = 'confirm_email_notification';
			$template_tag7 = 'confirm_email_notification';
			$interval_db_field7 = 'db237194';
            $this->process_notifications($all_notifications_to_be_sent7,$type_of_sql7,$template_tag7,$interval_db_field7);
            $output->writeln('ending confirm_email_notification'.date('m/d/Y h:i:s a', time()));
			
			/// sms course reminder hours before course starts
			$smsReminderSql1 = "SELECT
	            cb.id ,
	            cb.rel_id,
	            CONCAT(db48566, ' ' , db48568) AS full_name,
	            db48619 as 'title',
	            db48566 AS student_name,
	            db48599 AS student_number,
	            db48583 AS student_email,
	            db48621 as student_address,
	            db48596 as student_town,
	            db48622 as student_postcode,
	            db48597 as student_phone,
	            db232 AS course_title,
	            IFNULL(db234,'') AS course_summary,
	            db14947 AS course_start_date,
	            db14948 AS course_start_time,
	            db14949 AS course_end_date,
	            db14950 AS course_end_time,
	            db63007 as 'scheduled_course_information',
	            db14956 as 'additional_information',
	            IFNULL(db14953,'') as course_minimum_attendees,
	            CONCAT_WS(', ',db14963, db14965,db14966) as course_venue,
	            db14968 as 'course_venue_information',
	            db14966 AS course_postcode,
	            IFNULL(db209162,'') AS course_whatthreewords,
	            IFNULL(db237356,'') AS course_venue_image,
	            db14970 AS booking_status,
	            scs.id as course_schedule,
	            cb.usergroup,
	            db237314 as 'reminder_template',
	            lp.id as 'sis_profile_id',
	            lp.rel_id as 'sis_profile_rel_id',
	            cbd.id as 'sched_detail_id',
	            5 as sms_template_tag
	            FROM sis_sched_booking_detail cbd
	            LEFT JOIN form_schools on cbd.usergroup = form_schools.id
	            LEFT JOIN sis_scheduled_booking cb ON cbd.db15052 = cb.id
	            LEFT JOIN core_students cs ON cs.id=cbd.rel_id
	            LEFT JOIN sis_profiles lp ON lp.rel_id=cs.rel_id
	            LEFT JOIN sis_course_schedule scs ON scs.id=db14977
	            LEFT JOIN sis_booking_status bs ON bs.id=db14983
	            LEFT JOIN core_courses cm ON cm.id=db16136
	            LEFT JOIN sis_course_venues cv ON cv.id=db14954
	            LEFT JOIN (select id, concat (substring(db14948, 1, 2), ':', substring(db14948, 3, 2), ' 00') as startTime from sis_course_schedule) as scs2 on scs.id = scs2.id
	            WHERE $school_type_filter
                (cb.rec_archive IS NULL OR cb.rec_archive ='')
	            AND (lp.rec_archive IS NULL OR lp.rec_archive ='')
	            AND (scs.rec_archive IS NULL OR scs.rec_archive ='')
	            AND (cbd.rec_archive IS NULL OR cbd.rec_archive ='')
                AND (db14983 = 2 or db14983 = 3 or db59978= 2 or db59978=3)
	            AND db14959 != 5
                AND (db284645 ='yes' or db284645 IS NULL or db284645 = '')
                AND (SELECT count(*) from sis_scheduled_sessions
                	where sis_scheduled_sessions.rel_id = db14977
                	AND (sis_scheduled_sessions.rec_archive is null or sis_scheduled_sessions.rec_archive ='')
                ) = 0
	            AND (db262322  IS NULL OR db262322  != 'yes')
                AND db262277 = 'yes'
	            AND (db14947 = CURDATE()
                AND (TIMEDIFF(CONCAT(db14947, ' ', scs2.startTime), CURRENT_TIMESTAMP) <= TIME(concat('240','','0000'))) 
                AND  CONCAT(db14947, ' ', scs2.startTime)>CURRENT_TIMESTAMP
            ";
            $output->writeln('starting sms_course_reminder_no_sessions'.date('m/d/Y h:i:s a', time()));
			$sth77 = $dbh->prepare("$smsReminderSql1");
			$sth77->execute();
            $output->writeln('middle sms_course_reminder_no_sessions'.date('m/d/Y h:i:s a', time()));
			$all_notifications_to_be_sent77 = $sth77->fetchAll(\PDO::FETCH_OBJ);
			$type_of_sql77 = 'sms_course_reminder_no_sessions';
			$template_tag77 = 'sms_course_reminder_no_sessions';
			$interval_db_field77 = 'db262367';
			$this->process_notifications($all_notifications_to_be_sent77,$type_of_sql77,$template_tag77,$interval_db_field77, true);
            $output->writeln('ending sms_course_reminder_no_sessions'.date('m/d/Y h:i:s a', time()));
			$smsReminderSql2 = "SELECT
                ssb.id ,
                cb.rel_id,
                CONCAT(db48566, ' ' , db48568) AS full_name,
                db48619 as 'title',
                db48566 AS student_name,
                db48599 AS student_number,
                db48583 AS student_email,
                db48621 as student_address,
                db48596 as student_town,
                db48622 as student_postcode,
                db48597 as student_phone,
                db232 AS course_title,
                IFNULL(db234,'') AS course_summary,
                db59906 AS course_session_name,
                db59835 AS course_start_date,
                db59836 AS course_start_time,
                db59837 AS course_start_date,
                db59838 AS course_end_time,
                IFNULL(db14953,'') as course_minimum_attendees,
                CONCAT_WS(', ',db14963, db14965,db14966) as course_venue,
                db14968 as 'course_venue_information',
                db14966 AS course_postcode,
                IFNULL(db209162,'') AS course_whatthreewords,
                IFNULL(db237356,'') AS course_venue_image,
                db14970 AS booking_status,
                db63025 AS 'scheduled_session_information',
                db63007 as 'scheduled_course_information',
                db14956 as 'additional_information',
                cb.usergroup,
                db19831 as 'reminder_template',
                scs.id as course_schedule,
                lp.id as 'sis_profile_id',
                lp.rel_id as 'sis_profile_rel_id',
                cbd.id as 'sched_detail_id',
                8 as letter_tag,
	            8 as sms_template_tag
                FROM sis_session_bookings ssb
                LEFT JOIN form_schools on ssb.usergroup = form_schools.id
                LEFT JOIN sis_scheduled_booking cb ON ssb.db59976 = cb.id
                LEFT JOIN sis_sched_booking_detail cbd ON CAST(ssb.db59977 AS UNSIGNED)=cbd.id
                LEFT JOIN core_students cs ON cs.id=cbd.rel_id
                LEFT JOIN sis_profiles lp ON lp.rel_id=cs.rel_id
                LEFT JOIN sis_course_schedule scs ON scs.id=db14977
                LEFT JOIN sis_booking_status bs ON bs.id=db59978
                LEFT JOIN core_courses cm ON cm.id=db16136
                LEFT JOIN sis_scheduled_sessions ss on ss.id = db59901
                LEFT JOIN sis_course_venues cv ON cv.id=db59839
                LEFT JOIN sis_course_sessions cses on cses.id = db59900
				LEFT JOIN (select id, concat (substring(db59836, 1, 2), ':', substring(db59836, 3, 2), ' 00') as startTime from sis_scheduled_sessions) as sss2 on ss.id = sss2.id
                WHERE $school_type_filter
                (cb.rec_archive IS NULL OR cb.rec_archive ='')
                AND (cs.rec_archive IS NULL OR cs.rec_archive ='')
                AND db14959 != 5
                AND db59908 != 5
                AND (ssb.rec_archive IS NULL OR ssb.rec_archive ='')
                AND (lp.rec_archive IS NULL OR lp.rec_archive ='')
                AND (cbd.rec_archive IS NULL OR cbd.rec_archive ='')
                AND (ss.rec_archive IS NULL OR ss.rec_archive ='')
                AND (scs.rec_archive IS NULL OR scs.rec_archive ='')
                AND (db14983 = 2 or db14983 = 3 or db59978= 2 or db59978=3)
                AND (db59902 = 2 OR db59902 =3)
                AND (db284645 ='yes' or db284645 IS NULL or db284645 = '')
                 AND (SELECT count(*) from sis_scheduled_sessions
                	where sis_scheduled_sessions.rel_id = db14977
                	AND (sis_scheduled_sessions.rec_archive is null or sis_scheduled_sessions.rec_archive ='')
                ) > 0
	            AND (db262640 IS NULL OR db262640  != 'yes')
                AND db262277 = 'yes'
	            AND (TIMEDIFF(CONCAT(db14947, ' ', sss2.startTime), CURRENT_TIMESTAMP) <= TIME(concat((SELECT db262367  FROM lead_preferences WHERE usergroup= cb.usergroup),'','0000'))) 
                AND  CONCAT(db14947, ' ', sss2.startTime)>CURRENT_TIMESTAMP
                AND ((SELECT db111515 FROM lead_preferences WHERE usergroup= cb.usergroup) !='yes' OR (SELECT db111515 FROM lead_preferences WHERE usergroup= cb.usergroup) IS NULL)
            ";
            $output->writeln('starting sms_course_reminder_with_sessions'.date('m/d/Y h:i:s a', time()));
			$sth78 = $dbh->prepare("$smsReminderSql2");
			$sth78->execute();
			$all_notifications_to_be_sent78 = $sth78->fetchAll(\PDO::FETCH_OBJ);
            $output->writeln('middle sms_course_reminder_with_sessions'.date('m/d/Y h:i:s a', time()));
			$type_of_sql78 = 'sms_course_reminder_with_sessions';
			$template_tag78 = 'sms_course_reminder_with_sessions';
			$interval_db_field78 = 'db262367';
			$this->process_notifications($all_notifications_to_be_sent78,$type_of_sql78,$template_tag78,$interval_db_field78, true);
            $output->writeln('ending sms_course_reminder_with_sessions'.date('m/d/Y h:i:s a', time()));
			
			/// confirmation email on waitling list courses
			// confirmation notifications
            //if there are people on a waiting list send a confirmation email x days to the attendees to confirm they are coming
			$WaitingListConfirmSql = "SELECT
	            cb.id ,
	            cb.rel_id,
	            CONCAT(db48566, ' ' , db48568) AS full_name,
	            db48619 as 'title',
	            db48566 AS student_name,
	            db48599 AS student_number,
	            db48583 AS student_email,
	            db48621 as student_address,
	            db48596 as student_town,
	            db48622 as student_postcode,
	            db48597 as student_phone,
	            db232 AS course_title,
	            IFNULL(db234,'') AS course_summary,
	            db14947 AS course_start_date,
	            db14948 AS course_start_time,
	            db14949 AS course_end_date,
	            db14950 AS course_end_time,
	            db63007 as 'scheduled_course_information',
	            db14956 as 'additional_information',
	            IFNULL(db14953,'') as course_minimum_attendees,
	            CONCAT_WS(', ',db14963, db14965,db14966) as course_venue,
	            db14968 as 'course_venue_information',
	            db14966 AS course_postcode,
	            IFNULL(db209162,'') AS course_whatthreewords,
	            IFNULL(db237356,'') AS course_venue_image,
	            db14970 AS booking_status,
	            scs.id as course_schedule,
	            cb.usergroup,
	            db237314 as 'reminder_template',
	            lp.id as 'sis_profile_id',
	            lp.rel_id as 'sis_profile_rel_id',
	            cbd.id as 'sched_detail_id',
	            20 as letter_tag,
	            23 as sms_template_tag
	            FROM sis_sched_booking_detail cbd
	            LEFT JOIN form_schools on cbd.usergroup = form_schools.id
	            LEFT JOIN sis_scheduled_booking cb ON cbd.db15052 = cb.id
	            LEFT JOIN core_students cs ON cs.id=cbd.rel_id
	            LEFT JOIN sis_profiles lp ON lp.rel_id=cs.rel_id
	            LEFT JOIN sis_course_schedule scs ON scs.id=db14977
	            LEFT JOIN sis_booking_status bs ON bs.id=db14983
	            LEFT JOIN core_courses cm ON cm.id=db16136
	            LEFT JOIN sis_course_venues cv ON cv.id=db14954
	            LEFT JOIN (
	            	SELECT CAST(db14977 AS UNSIGNED) as sch_course_id,count(sis_sched_booking_detail.id) as cnt FROM sis_sched_booking_detail
					LEFT JOIN sis_scheduled_booking ON sis_scheduled_booking.id = db15052
					LEFT JOIN core_students on core_students.id = db16135
					LEFT JOIN form_schools on sis_sched_booking_detail.usergroup = form_schools.id
					WHERE $school_type_filter (sis_scheduled_booking.rec_archive is NULL or sis_scheduled_booking.rec_archive = '')
					AND (sis_sched_booking_detail.rec_archive is NULL or sis_sched_booking_detail.rec_archive = '')
					AND (core_students.rec_archive is NULL or core_students.rec_archive = '')
					AND db59978 = '1'
					GROUP BY db14977
				) as status_waiting_list ON status_waiting_list.sch_course_id=scs.id
	            WHERE $school_type_filter
                (cb.rec_archive IS NULL OR cb.rec_archive ='')
	            AND (lp.rec_archive IS NULL OR lp.rec_archive ='')
	            AND (scs.rec_archive IS NULL OR scs.rec_archive ='')
	            AND (cbd.rec_archive IS NULL OR cbd.rec_archive ='')
                AND (db59978= '2' or db59978='3')
	            AND db14959 != '5'
                AND (db284645 ='yes' or db284645 IS NULL or db284645 = '')
                AND status_waiting_list.cnt > 0
	            AND (db261929  IS NULL OR db261929  != 'yes')
	            AND (db14947 > CURDATE()
                AND db14947 = DATE_ADD(CURDATE(), INTERVAL (SELECT db261218 FROM lead_preferences WHERE usergroup= cb.usergroup) DAY)
	            )
            ";
            $output->writeln('starting waitinglist_confirm_email_notification'.date('m/d/Y h:i:s a', time()));
			$sth07 = $dbh->prepare("$WaitingListConfirmSql");
			$sth07->execute();
			$all_notifications_to_be_sent07 = $sth07->fetchAll(\PDO::FETCH_OBJ);
            $output->writeln('middle waitinglist_confirm_email_notification'.date('m/d/Y h:i:s a', time()));
			$type_of_sql07 = 'waitinglist_confirm_email_notification';
			$template_tag07 = 'waitinglist_confirm_email_notification';
			$interval_db_field07 = 'db261218';
			$this->process_notifications($all_notifications_to_be_sent07,$type_of_sql07,$template_tag07,$interval_db_field07);
            $output->writeln('ending waitinglist_confirm_email_notification'.date('m/d/Y h:i:s a', time()));
            $output->writeln('Ending scheduled course reminder'.date('m/d/Y h:i:s a', time()));
		}


		//process the notifications
		
		/**
		 * @throws Exception
		 * @throws \Doctrine\DBAL\Exception
		 */
		protected function process_notifications ($sql='', $type_of_sql='', $template_tag='', $interval_db_field='', $alwaysSend= ''): string
		{
			$dbh = dbh();
            $shortcoursesModel = new ShortCourses();
			foreach ($sql as $notification_to_be_sent) {
				$usergroup = $notification_to_be_sent->usergroup;
                $_SESSION['usergroup'] =  $notification_to_be_sent->usergroup;
				$texting_allowed=pull_field("lead_preferences","db111494","WHERE usergroup='{$usergroup}'");
				$prioritise_email=pull_field("lead_preferences","db236852","WHERE usergroup='{$usergroup}'");

				$comms_preference = $this->get_method_of_communication($notification_to_be_sent->sis_profile_id);
                if (empty($notification_to_be_sent->course_session_name)) {
                    $notification_to_be_sent->course_session_name = '';
                }
                if (empty($notification_to_be_sent->scheduled_session_information)) {
                    $notification_to_be_sent->scheduled_session_information = '';
                }

				list($course_booking_notification_allowed, $start_notification_time, $end_notification_time, $interval) = explode(',', pull_field("lead_preferences", "CONCAT_WS(',',db111497,db111506,db111509,$interval_db_field )", "WHERE usergroup='$notification_to_be_sent->usergroup' LIMIT 1"));
				$between_times = false;
				$reminders_sent = '';
				if ($course_booking_notification_allowed != 'None' && $interval != '0' && $interval != '') {
					if ($start_notification_time && $start_notification_time != '' && $end_notification_time && $end_notification_time != '') {
						date_default_timezone_set('Europe/London');
						if ($this->is_between_times($start_notification_time, $end_notification_time)) {
							$between_times = true;
						}
					}
					
					// set this to override reminder settings when sending out reminders hours before the course strta
					if(!empty($alwaysSend)){
						$between_times = true;
						$comms_preference= ['sms'];
						$texting_allowed = 'yes';
						$prioritise_email= 'no';
					}
					
					if ($between_times && !empty($comms_preference)) {
                        $course_date = date_create($notification_to_be_sent->course_start_date);
                        ///format the date according to the preferences else default setting.
                        $course_date = format_date_for_notifications($notification_to_be_sent->course_start_date);
                        $course_end_date = format_date_for_notifications($notification_to_be_sent->course_end_date);
                        $session_info = $shortcoursesModel->get_session_info($notification_to_be_sent->course_schedule);
                        $session_info_no_venue = $shortcoursesModel->get_session_info($notification_to_be_sent->course_schedule,'','no');
                        $session_info_with_webconferencing = $shortcoursesModel->get_session_info($notification_to_be_sent->course_schedule,'','no','yes');
                        $remaining_session_info = $shortcoursesModel->get_session_info($notification_to_be_sent->course_schedule,$notification_to_be_sent->course_start_date);
                        $remaining_session_info_no_venue = $shortcoursesModel->get_session_info($notification_to_be_sent->course_schedule,$notification_to_be_sent->course_start_date,'no');
                        $remaining_session_info_with_webconferencing = $shortcoursesModel->get_session_info($notification_to_be_sent->course_schedule,$notification_to_be_sent->course_start_date,'no','yes');
						$has_email = $has_sms = false;
						$course_start_time = substr($notification_to_be_sent->course_start_time, 0, 2) . ':' . substr($notification_to_be_sent->course_start_time, 2, 2);
						$course_end_time = substr($notification_to_be_sent->course_end_time, 0, 2) . ':' . substr($notification_to_be_sent->course_end_time, 2, 2);
						$course_time = $course_start_time. ' - ' . $course_end_time;
						
						//if coms_pref is email
						if (in_array('email', $comms_preference)) {
							$has_email = true;
							$reminder_template = $notification_to_be_sent->reminder_template;
							if (!empty($reminder_template)) {
								$template = $reminder_template;
								$qry = "SELECT db1085 as template, db1086 as subject FROM coms_template WHERE id='{$template}'";
								$sth8 = $dbh->prepare($qry);
								$sth8->execute();
								$template_data = $sth8->fetch(\PDO::FETCH_OBJ);
								$message_html = $template_data->template;
								$message_subject = $template_data->subject;

								$message_html = email_template_replace_values("{{name}}", $notification_to_be_sent->student_name, $message_html);
								$message_html = email_template_replace_values("{{first_name}}", $notification_to_be_sent->student_name, $message_html);
                                $message_html = email_template_replace_values('{{course_id}}', $notification_to_be_sent->course_schedule, $message_html);
								$message_html = email_template_replace_values("{{course_name}}", $notification_to_be_sent->course_title, $message_html);
                                $message_html = email_template_replace_values("{{course_summary}}", $notification_to_be_sent->course_summary, $message_html);
                                $message_html = email_template_replace_values("{{course_date}}", $course_date, $message_html);
								$message_html = email_template_replace_values("{{course_start_date}}", $course_date, $message_html);
                                $message_html = email_template_replace_values("{{course_end_date}}", $course_end_date, $message_html);
								$message_html = email_template_replace_values("{{course_start_time}}", $course_start_time, $message_html);
								$message_html = email_template_replace_values("{{course_end_time}}", $course_end_time, $message_html);
								$message_html = email_template_replace_values("{{course_time}}", $course_time, $message_html);
								$message_html = email_template_replace_values("{{course_venue}}", $notification_to_be_sent->course_venue, $message_html);
								$message_html = email_template_replace_values("{{course_venue_information}}", $notification_to_be_sent->course_venue_information, $message_html);
								$message_html = email_template_replace_values("{{course_postcode}}", $notification_to_be_sent->course_postcode, $message_html);
                                $message_html = email_template_replace_values("{{course_whatthreewords}}", $notification_to_be_sent->course_whatthreewords, $message_html);
                                if (!empty($notification_to_be_sent->course_venue_image)) {
                                    $image_path = engine_url("/media/dl.php?a=yes&fl=".encode($notification_to_be_sent->course_venue_image, 'unsalted'));
                                    $venue_image = " <img src=\"$image_path\">" ;
                                }
                                $message_html = email_template_replace_values('{{course_venue_image}}',  $venue_image, $message_html);

								$message_html = email_template_replace_values("{{full_name}}", $notification_to_be_sent->full_name, $message_html);
								$message_html = email_template_replace_values("{{title}}", $notification_to_be_sent->title, $message_html);
								$message_html = email_template_replace_values("{{web_conferencing_information}}", $notification_to_be_sent->scheduled_course_information, $message_html);
								$message_html = email_template_replace_values("{{additional_information}}", $notification_to_be_sent->additional_information, $message_html);
								$message_html = email_template_replace_values("{{course_session_name}}", $notification_to_be_sent->course_session_name, $message_html);
								$message_html = email_template_replace_values("{{session_start_time}}", $course_time, $message_html);
                                $message_html = email_template_replace_values("{{session_start_date}}", $course_date, $message_html);
                                $message_html = email_template_replace_values("{{session_end_date}}", $course_end_date, $message_html);
								$message_html = email_template_replace_values("{{scheduled_session_information}}", $notification_to_be_sent->scheduled_session_information, $message_html);
								$message_html = str_replace("{{session_info}}", $session_info, $message_html);
                                $message_html = str_replace("{{session_info_no_venue}}", $session_info_no_venue, $message_html);
								$message_html = str_replace("{{session_info_with_webconferencing}}", $session_info_with_webconferencing, $message_html);
                                $message_html = str_replace("{{remaining_session_info}}", $remaining_session_info, $message_html);
                                $message_html = str_replace("{{remaining_session_info_no_venue}}", $remaining_session_info_no_venue, $message_html);
                                $message_html = str_replace("{{remaining_session_info_with_webconferencing}}", $remaining_session_info_with_webconferencing, $message_html);
                                $message_html = str_replace("{{course_minimum_attendees}}", $notification_to_be_sent->course_minimum_attendees, $message_html);


                                //mail merge the subject
                                $message_subject = email_template_replace_values("{{name}}", $notification_to_be_sent->student_name, $message_subject);
                                $message_subject = email_template_replace_values("{{first_name}}", $notification_to_be_sent->student_name, $message_subject);
                                $message_subject = email_template_replace_values('{{course_id}}', $notification_to_be_sent->course_schedule, $message_subject);
                                $message_subject = email_template_replace_values("{{course_name}}", $notification_to_be_sent->course_title, $message_subject);
                                $message_subject = email_template_replace_values("{{course_date}}", $course_date, $message_subject);
                                $message_subject = email_template_replace_values("{{course_start_date}}", $course_date, $message_subject);
                                $message_subject = email_template_replace_values("{{course_start_time}}", $course_start_time, $message_subject);
                                $message_subject = email_template_replace_values("{{course_end_date}}", $course_end_date, $message_subject);
                                $message_subject = email_template_replace_values("{{course_end_time}}", $course_end_time, $message_subject);
                                $message_subject = email_template_replace_values("{{course_time}}", $course_time, $message_subject);
                                $message_subject = email_template_replace_values("{{course_venue}}", $notification_to_be_sent->course_venue, $message_subject);
                                $message_subject = email_template_replace_values("{{course_postcode}}", $notification_to_be_sent->course_postcode, $message_subject);
                                $message_subject = email_template_replace_values("{{course_whatthreewords}}", $notification_to_be_sent->course_whatthreewords, $message_subject);
                                $message_subject = email_template_replace_values("{{full_name}}", $notification_to_be_sent->full_name, $message_subject);
                                $message_subject = email_template_replace_values("{{title}}", $notification_to_be_sent->title, $message_subject);
                                $message_subject = email_template_replace_values("{{course_session_name}}", $notification_to_be_sent->course_session_name, $message_subject);
                                $message_subject = email_template_replace_values("{{session_start_time}}", $course_time, $message_subject);
                                $message_subject = email_template_replace_values("{{session_start_date}}", $course_date, $message_subject);
                                $message_subject = str_replace("{{course_minimum_attendees}}", $notification_to_be_sent->course_minimum_attendees, $message_subject);

                                $emails = new Emails();
								$message_subject = $notification_to_be_sent->course_title . " " . $message_subject;



								$email_args = array(
									'to' => $notification_to_be_sent->student_email,
									'subject' => $message_subject,
									'html' => $message_html,
									'from' => pull_field("form_schools", "db1117", "WHERE id='$notification_to_be_sent->usergroup'"),
									'usergroup' => $notification_to_be_sent->usergroup,
                                    'rel_id' => $notification_to_be_sent->sis_profile_rel_id,
                                    'template_id' => $reminder_template,
								);
								$emails->send($email_args);
								$reminders_sent = 1;
							}
						}
						$send_sms= true;
						if ($prioritise_email == 'yes' && !empty($has_email)){
							$send_sms = false;
						}

						//if coms pref is sms
						if (in_array('sms', $comms_preference) && $texting_allowed == 'yes' && !empty($send_sms)){
							$has_sms = true;
							//get sms template
							$sms_template_tag = $notification_to_be_sent->sms_template_tag;
							$sms_template_id = pull_field('coms_sms_template', 'id', "WHERE db159320 = '{$sms_template_tag}' AND usergroup = {$_SESSION['usergroup']} AND (rec_archive is null or rec_archive ='') and db159983 = 'yes'");
							if (empty($sms_template_id)){
								//get default
								$sms_template_id = pull_field('coms_sms_template', 'id', "WHERE db159320 = '{$sms_template_tag}' AND usergroup = 1 AND (rec_archive is null or rec_archive ='') and db159983='yes'");
							}
							if (!empty($sms_template_id)){
								$sms_template_sql  = "SELECT  db25661 AS subject, db25663 as content
                                FROM coms_sms_template WHERE id = {$sms_template_id}
                                ";
								$sth6 = $dbh->prepare($sms_template_sql);
								$sth6->execute();
								$sms_template = $sth6->fetch(\PDO::FETCH_ASSOC);
								
								//send out sms
								$messages = new CommunicationMessages();
								$subject = $sms_template['subject'];
								$recipient_number = $notification_to_be_sent->student_phone;
								$send = 'Send';
								$student_id = $notification_to_be_sent->sis_profile_rel_id;
								$sms_text =$sms_template['content'];
								$usergroup = $_SESSION['usergroup'];
								
								$sms_text = email_template_replace_values("{{name}}", $notification_to_be_sent->student_name, $sms_text);
								$sms_text = email_template_replace_values("{{first_name}}", $notification_to_be_sent->student_name, $sms_text);
                                $sms_text = email_template_replace_values("{{course_id}}", $notification_to_be_sent->course_schedule, $sms_text);
								$sms_text = email_template_replace_values("{{course_name}}", $notification_to_be_sent->course_title, $sms_text);
                                $sms_text = email_template_replace_values("{{course_summary}}", $notification_to_be_sent->course_summary, $sms_text);
								$sms_text = email_template_replace_values("{{course_date}}", $course_date, $sms_text);
								$sms_text = email_template_replace_values("{{course_start_date}}", $course_date, $sms_text);
                                $sms_text = email_template_replace_values("{{course_end_date}}", $course_end_date, $sms_text);
								$sms_text = email_template_replace_values("{{course_start_time}}", $course_start_time, $sms_text);
								$sms_text = email_template_replace_values("{{course_end_time}}", $course_end_time, $sms_text);
								$sms_text = email_template_replace_values("{{course_time}}", $course_time, $sms_text);
								$sms_text = email_template_replace_values("{{course_venue}}", $notification_to_be_sent->course_venue, $sms_text);
								$sms_text = email_template_replace_values("{{course_venue_information}}", $notification_to_be_sent->course_venue_information, $sms_text);
								$sms_text = email_template_replace_values("{{course_postcode}}", $notification_to_be_sent->course_postcode, $sms_text);
								$sms_text = email_template_replace_values("{{full_name}}", $notification_to_be_sent->full_name, $sms_text);
								$sms_text = email_template_replace_values("{{title}}", $notification_to_be_sent->title, $sms_text);
								$sms_text = email_template_replace_values("{{web_conferencing_information}}", $notification_to_be_sent->scheduled_course_information, $sms_text);
								$sms_text = email_template_replace_values("{{additional_information}}", $notification_to_be_sent->additional_information, $sms_text);
								$sms_text = email_template_replace_values("{{course_session_name}}", $notification_to_be_sent->course_session_name, $sms_text);
								$sms_text = email_template_replace_values("{{session_start_time}}", $course_time, $sms_text);
								$sms_text = email_template_replace_values("{{session_start_date}}", $course_date, $sms_text);
                                $sms_text = email_template_replace_values("{{session_end_date}}", $course_end_date, $sms_text);
								$sms_text = email_template_replace_values("{{scheduled_session_information}}", $notification_to_be_sent->scheduled_session_information, $sms_text);
								$sms_text = str_replace("{{session_info}}", $session_info, $sms_text);
                                $sms_text = str_replace("{{session_info_no_venue}}", $session_info_no_venue, $sms_text);
								$sms_text = str_replace("{{session_info_with_webconferencing}}", $session_info_with_webconferencing, $sms_text);
								$sms_text = str_replace("{{remaining_session_info}}", $remaining_session_info, $sms_text);
                                $sms_text = str_replace("{{remaining_session_info_no_venue}}", $remaining_session_info_no_venue, $sms_text);
								$sms_text = str_replace("{{remaining_session_info_with_webconferencing}}", $remaining_session_info_with_webconferencing, $sms_text);
								
								
								$sms_text = str_replace(['<br />', '<br>','<br/>','<p>','</p>'], ' ', $sms_text);
								$sms = $messages->log_sms($template = "", $subject, $sms_text, $recipient_number, $student_id, $usergroup, $send);
								$reminders_sent = 1;
								
							}
							
							
						}
						
						//if coms_pref is letter
						if(in_array('letter', $comms_preference) && empty($has_sms) && empty($has_email)){
							$letter_tag = $notification_to_be_sent->letter_tag;

							//get letter template
							 //if reminder is a a course reminder
							$letter_template_id = pull_field('core_letter_library', 'id', "WHERE db33619 = '{$letter_tag}' AND usergroup = {$_SESSION['usergroup']} AND (rec_archive is null or rec_archive ='') and db1490 = 'yes'");
							if (empty($letter_template_id)){
								//get default
								$letter_template_id = pull_field('core_letter_library', 'id', "WHERE db33619 = '{$letter_tag}' AND usergroup = 1 AND (rec_archive is null or rec_archive ='') and db1490 = 'yes'");
							}

							$letter_template_sql = "
								SELECT db1488 AS letter_content, db1487 as title
								FROM core_letter_library where id = {$letter_template_id}
							";
							$sth7 = $dbh->prepare($letter_template_sql);
							$sth7->execute();
							$letter_template_data = $sth7->fetch(\PDO::FETCH_ASSOC);
							$letter_template = $letter_template_data['letter_content'];
							$letter_title = $notification_to_be_sent->course_title . ' ' . $notification_to_be_sent->course_start_date . ' ' . $letter_template_data['title'];


							$user_address_with_postcode = $notification_to_be_sent->student_address . '<br>' . $notification_to_be_sent->student_town . '<br>' . $notification_to_be_sent->student_postcode;
							$user_address_no_postcode = $notification_to_be_sent->student_address . '<br>' . $notification_to_be_sent->student_town;

							$letter_template = str_replace("{{address}}", $user_address_with_postcode, $letter_template);
							$letter_template = str_replace("{{address_no_postcode}}", $user_address_no_postcode, $letter_template);
							$letter_template = str_replace("{{postcode}}", $notification_to_be_sent->student_postcode, $letter_template);
							$letter_template = str_replace("{{current_date}}", format_date_for_notifications(date("Y-m-d")), $letter_template);
                            $letter_template = email_template_replace_values("{{name}}", $notification_to_be_sent->student_name, $letter_template);
                            $letter_template = email_template_replace_values("{{first_name}}", $notification_to_be_sent->student_name, $letter_template);
                            $letter_template = email_template_replace_values("{{course_id}}", $notification_to_be_sent->course_schedule, $letter_template);
                            $letter_template = email_template_replace_values("{{course_name}}", $notification_to_be_sent->course_title, $letter_template);
                            $letter_template = email_template_replace_values("{{course_summary}}", $notification_to_be_sent->course_summary, $letter_template);
                            $letter_template = email_template_replace_values("{{course_date}}", $course_date, $letter_template);
                            $letter_template = email_template_replace_values("{{course_start_date}}", $course_date, $letter_template);
                            $letter_template = email_template_replace_values("{{course_end_date}}", $course_end_date, $letter_template);
                            $letter_template = email_template_replace_values("{{course_start_time}}", $course_start_time, $letter_template);
                            $letter_template = email_template_replace_values("{{course_end_time}}", $course_end_time, $letter_template);
                            $letter_template = email_template_replace_values("{{course_time}}", $course_time, $letter_template);
                            $letter_template = email_template_replace_values("{{course_venue}}", $notification_to_be_sent->course_venue, $letter_template);
                            $letter_template = email_template_replace_values("{{course_venue_information}}", $notification_to_be_sent->course_venue_information, $letter_template);
                            $letter_template = email_template_replace_values("{{course_postcode}}", $notification_to_be_sent->course_postcode, $letter_template);
                            $letter_template = email_template_replace_values("{{full_name}}", $notification_to_be_sent->full_name, $letter_template);
                            $letter_template = email_template_replace_values("{{title}}", $notification_to_be_sent->title, $letter_template);
                            $letter_template = email_template_replace_values("{{web_conferencing_information}}", $notification_to_be_sent->scheduled_course_information, $letter_template);
                            $letter_template = email_template_replace_values("{{additional_information}}", $notification_to_be_sent->additional_information, $letter_template);
                            $letter_template = email_template_replace_values("{{course_session_name}}", $notification_to_be_sent->course_session_name, $letter_template);
                            $letter_template = email_template_replace_values("{{session_start_time}}", $course_time, $letter_template);
                            $letter_template = email_template_replace_values("{{session_start_date}}", $course_date, $letter_template);
                            $letter_template = email_template_replace_values("{{session_end_date}}", $course_end_date, $letter_template);
                            $letter_template = email_template_replace_values("{{scheduled_session_information}}", $notification_to_be_sent->scheduled_session_information, $letter_template);
                            $letter_template = str_replace("{{session_info}}", $session_info, $letter_template);
                            $letter_template = str_replace("{{session_info_no_venue}}", $session_info_no_venue, $letter_template);
                            $letter_template = str_replace("{{session_info_with_webconferencing}}", $session_info_with_webconferencing, $letter_template);
                            $letter_template = str_replace("{{remaining_session_info}}", $remaining_session_info, $letter_template);
                            $letter_template = str_replace("{{remaining_session_info_no_venue}}", $remaining_session_info_no_venue, $letter_template);
                            $letter_template = str_replace("{{remaining_session_info_with_webconferencing}}", $remaining_session_info_with_webconferencing, $letter_template);
							// insert letter
							$letter_random = random();
                            $letter_template = addslashes($letter_template);
                            $letter_title=addslashes($letter_title);
							$db20300 = '';
							$db69980 = '';
							$recipient_id = $notification_to_be_sent->sis_profile_rel_id;
							$letter_insert_sql = "INSERT  INTO  dir_letters_sent (username_id, rec_id, usergroup, rel_id, db20300, db20301,db31870,db69980)
								VALUES  ('{$letter_random}', '{$_SESSION['uid']}', '{$_SESSION['usergroup']}', '{$recipient_id}', '{$db20300}', '{$letter_title}', '{$letter_template}',  '{$db69980}')";
							$sth7 = $dbh->prepare($letter_insert_sql);
							$sth7->execute();
							$last_id = $dbh->lastInsertId();

                            $sub_domain = pull_field('form_schools', 'db985', "WHERE id = {$_SESSION['usergroup']}");
							
							$http_link = env("PROTOCOL") .$sub_domain.'.'. env("APP_URL"). "/engine/modules/inc_letters_sent.php?&rec=$last_id&pdf=1";
							$letter_update_sql = "UPDATE dir_letters_sent SET db20300=? WHERE id=? AND usergroup=?";
							$sql1 = $dbh->prepare($letter_update_sql);
                            $sql1->execute(array($http_link, $last_id, $_SESSION['usergroup']));
							$reminders_sent = 1;
						}
                      
						if (in_array('phone', $comms_preference) & sizeof($comms_preference) ==1) {
							$emails = new Emails;
							$admin_email = pull_field('form_schools', 'db1118', "WHERE id={$usergroup}");
							$email_message = "Dear Admin
								$notification_to_be_sent->full_name selected telephone number or home phone or phone call as a preferred method of communication.
								
								May you please get in touch with them and remind them of the booking for the $notification_to_be_sent->course_title starting on $notification_to_be_sent->course_start_date.
								
								The venue of the course is on/at $notification_to_be_sent->course_venue.
								
								May you please get in touch with the student, and remind them of this booking.
							
								Regards
					
							";
							$coms_template_subject_line = 'Scheduled Course Reminder Request';
							
							$email_args=[
								'to'=> $admin_email,
								'subject'=>$coms_template_subject_line,
								'html'=> nl2br($email_message),
							];
							$emails->send($email_args);
							$reminders_sent = 1;
						}
						
						if (in_array('mobile', $comms_preference) & sizeof($comms_preference) ==1) {
							$emails = new Emails;
							$admin_email = pull_field('form_schools', 'db1118', "WHERE id={$usergroup}");
							$email_message = "Dear Admin
								$notification_to_be_sent->full_name selected mobile number as a preferred method of communication.
								
								May you please get in touch with them and remind them of the booking for the $notification_to_be_sent->course_title starting on $notification_to_be_sent->course_start_date.
								
								The venue of the course is on/at $notification_to_be_sent->course_venue.
								
								May you please get in touch with the student, and remind them of this booking.
							
								Regards
					
							";
							$coms_template_subject_line = 'Scheduled Course Reminder Request';
							
							$email_args=[
								'to'=> $admin_email,
								'subject'=>$coms_template_subject_line,
								'html'=> nl2br($email_message),
							];
							$emails->send($email_args);
							$reminders_sent = 1;
							
						}
					}
				}
				
				if ($between_times && !empty($reminders_sent)) {
					if ($template_tag == 'course_booking_reminder') {
						if ($type_of_sql == 'with_no_sessions_not1') {
							//update booking to say notification 1 has been sent
							$sql_update = "UPDATE sis_sched_booking_detail SET rec_lstup=NOW() , rec_lstup_id= '1' ,  db66749='yes' WHERE id=?";
							$sth = $dbh->prepare("$sql_update");
							$sth->execute(array($notification_to_be_sent->sched_detail_id));

						} else if ($type_of_sql == 'with_sessions_not1') {
							//update booking to say notification 1 has been sent
							$sql_update = "UPDATE sis_sched_booking_detail SET rec_lstup=NOW() , rec_lstup_id= '1' , db66749='yes' WHERE id=?";
							$sth = $dbh->prepare("$sql_update");
							$sth->execute(array($notification_to_be_sent->sched_detail_id));

                            //mark all sessions that they have received notification 1
                            $session_ids_sql ="UPDATE sis_session_bookings SET rec_lstup=NOW() , rec_lstup_id= '1' , db59904 = 'yes' WHERE db59977='{$notification_to_be_sent->sched_detail_id}'";
                            $sth2 = $dbh->prepare($session_ids_sql);
                            $sth2->execute();

						} else if ($type_of_sql == 'with_no_sessions_not2') {
							$sql_update = "UPDATE sis_sched_booking_detail SET rec_lstup=NOW() , rec_lstup_id= '1' , db66750='yes' WHERE id=?";
							$sth = $dbh->prepare("$sql_update");
							$sth->execute(array($notification_to_be_sent->sched_detail_id));
						} else if ($type_of_sql == 'with_sessions_not2') {
							//update booking to say notification 2 has been sent
							$sql_update = "UPDATE sis_sched_booking_detail SET rec_lstup=NOW() , rec_lstup_id= '1' , db66750='yes' WHERE id=?";
							$sth = $dbh->prepare("$sql_update");
							$sth->execute(array($notification_to_be_sent->sched_detail_id));

                            //mark all sessions that they have received notification 2
                            $session_ids_sql ="UPDATE sis_session_bookings SET rec_lstup=NOW() , rec_lstup_id= '1' , db59905 = 'yes' WHERE db59977='{$notification_to_be_sent->sched_detail_id}'";
                            $sth2 = $dbh->prepare($session_ids_sql);
                            $sth2->execute();

						}
						
					} else if ($template_tag == 'course_session_reminder') {
						
						if ($type_of_sql == 'with_all_sessions_notification1') {
							$sql_update2 = "UPDATE sis_session_bookings SET rec_lstup=NOW(), rec_lstup_id= '1', db59904='yes' WHERE id=?";
							$sth2 = $dbh->prepare("$sql_update2");
							$sth2->execute(array($notification_to_be_sent->id));

						} else if ($type_of_sql == 'with_all_sessions_notification2') {
							$sql_update = "UPDATE sis_session_bookings SET rec_lstup=NOW(), rec_lstup_id= '1',  db59905='yes' WHERE id=?";
							$sth = $dbh->prepare("$sql_update");
							$sth->execute(array($notification_to_be_sent->id));
						}
						
					} else if ($template_tag =='confirm_email_notification') {
                        //update booking to say confirmation notification  has been sent
                        $sql_update = "UPDATE sis_sched_booking_detail SET rec_lstup=NOW(), rec_lstup_id= '1', db250781='yes' WHERE id=?";
                        $sth = $dbh->prepare("$sql_update");
                        $sth->execute(array($notification_to_be_sent->sched_detail_id));
                    }elseif($template_tag == 'waitinglist_confirm_email_notification'){
                        //update booking to say confirmation notification  has been sent
                        $sql_update = "UPDATE sis_sched_booking_detail SET rec_lstup=NOW(), rec_lstup_id= '1', db261929='yes' WHERE id=?";
                        $sth = $dbh->prepare("$sql_update");
                        $sth->execute(array($notification_to_be_sent->sched_detail_id));
                    }elseif($template_tag == 'sms_course_reminder_no_sessions'){
                        //update booking to say confirmation notification  has been sent
                        $sql_update = "UPDATE sis_sched_booking_detail SET rec_lstup=NOW(), rec_lstup_id= '1', db262322='yes' WHERE id=?";
                        $sth = $dbh->prepare("$sql_update");
                        $sth->execute(array($notification_to_be_sent->sched_detail_id));
                    }else if ($template_tag == 'sms_course_reminder_with_sessions') {
						$sql_update = "UPDATE sis_session_bookings SET rec_lstup=NOW(), rec_lstup_id= '1',  db262640='yes' WHERE id=?";
						$sth = $dbh->prepare("$sql_update");
						$sth->execute(array($notification_to_be_sent->id));
					}
					
				}
			}
			
			return 'Processed';
		}
		
		//get the preferred method of communication for an eoi
		protected function get_method_of_communication ($student_id) {
			
			if (!$student_id || $student_id =='') {
				return 'Email';
			}
			
			// coms_preference
			$shortcoursesModel = new ShortCourses();
			return $shortcoursesModel->comms_preference($student_id);
		}
		protected function is_between_times( $start = null, $end = null ): bool
		{
			if ( $start == null ) $start = '0000';
			if ( $end == null ) $end = '2359';
			return ( $start <= date( 'Hi' ) && date( 'Hi' ) <= $end );
		}
		
		

	}
