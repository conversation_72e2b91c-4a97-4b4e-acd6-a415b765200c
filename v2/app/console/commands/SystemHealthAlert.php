<?php


namespace App\console\commands;

use App\models\Emails;
use Carbon\Carbon;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class SystemHealthAlert extends Command
{
    protected static $defaultName = 'system:check-health';

    protected function configure()
    {
        $this->setDescription('Checks system health')
            ->setHelp('This command checks system health');

    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $output->writeln(['========', "Checking storage space left"]);
        $capacity = round(disk_total_space('/') / 1024 / 1024 / 1024);
        $freeSpace = round(disk_free_space('/') / 1024 / 1024 / 1024);
        $percentageFreeSpace = round(($freeSpace) * 100 / $capacity);
        if ($percentageFreeSpace < 10) {
            $output->writeln(['========', "Less than 10% of space left: " . $percentageFreeSpace . '%']);
            $emails = new Emails();

            $email_args = array(
                'usergroup' => 1,
                'from' => '<EMAIL>',
                'to' => "<EMAIL>",
                'subject' => "Disk Space Alert",
                'text' => "You are running out of disk space. Only $freeSpace GB ($percentageFreeSpace %) left. Free up disk space now.",
                'html' => "You are running out of disk space. Only $freeSpace GB ($percentageFreeSpace %) left. Free up disk space now.",
            );
            $emails->send($email_args);
        }

    }


}