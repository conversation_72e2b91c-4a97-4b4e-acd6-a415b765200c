<?php


namespace App\console\commands;


use App\models\Emails;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use App\console\commands\reports\ExportAllStudentsDataJob;


class ExportAllStudentsData extends Command
{
    protected static $defaultName = 'applicants:xml-export';
    public $processed_jobs = 0;
    public $failed_jobs = 0;
    public $pending_jobs = 0;
  
    protected $debug_mode;

    protected function configure()
    {
        $this->setDescription('Process applicants XML export till completion')
            ->setHelp('Process applicants XML export');

    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {

        
      //todo Create a worker class rather than using cron jobs
        $output->writeln([
            'Start processing queue',
            '======================'
        ]);
        $app = app();
        $processed_jobs = 0;
        $failed_jobs = 0;
        $pending_jobs = dbh()->fetchAll("Select * from form_jobs");
        $output->writeln([
            'Jobs in queue:' . count($pending_jobs)
        ]);
        foreach ($pending_jobs as $pending_job) {
            $job_still_available = dbh()->fetchAll("Select * from form_jobs where id='".$pending_job['id']."'");
            //echo "<pre>".print_r($job_still_available,1)."</pre>";
            if (count($job_still_available)==0) {
                $output->writeln([
                    'skipping unavailable job id :'.$pending_job['id'],
                    '======================'
                ]);
                 continue;
            }else{
                $output->writeln([
                    'executing  job id :'.$pending_job['id'],
                    '======================'
                ]);  
            }
            $pending_job['db57005'] = json_decode($pending_job['db57005']);
            $payload = (object)$pending_job;
            if ($payload->db57005->category == 'export_applicants_xml') {
                $export_applicants_xml = new ExportAllStudentsDataJob($payload, $output);
                if ($export_applicants_xml->process()) {
                    $processed_jobs++;
                    dbh()->delete("form_jobs", ["id" => $pending_job["id"]]);
                    
                } else {
                    //update tries
                    dbh()->update("form_jobs", ["db57007" => $pending_job['db57007']++], ["id" => $pending_job["id"]]);
                    $failed_jobs++;
                }
            }

         }
        

        $output->writeln([
            'Processed Jobs:' . $processed_jobs
        ]);
        $output->writeln([
            'Failed Jobs:' . $failed_jobs
        ]);

    }
    
}