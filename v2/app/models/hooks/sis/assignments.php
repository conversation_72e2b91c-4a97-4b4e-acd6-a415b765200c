<?php
namespace sis_assignments;

// POST_ALL
function send_email ($args, $static, $mappings) {
//if project is set to live and user is assigned an email is sent to the user
    if($args['db1622']=='live'){
        $core_students_email=explode("|",pull_field("form_users,sis_student_data","concat(db119,'|',db1444)","WHERE form_users.id=sis_student_data.db1469 AND sis_student_data.id=$args[db1623]"));
	    
	    // check if an invite has already been sent
	    $core_students_email_address=$core_students_email[0];// email
	    $core_students_name=$core_students_email[1];// name
	    
	    $tag_email="sis_proj_$args[db1616]_invite";
	    
	    $check_for_invite = pull_field("form_invite_tracker","count(id)","WHERE db1077='$core_students_email_address' AND db1089='$tag_email'"); // get application id
	    
	    if($check_for_invite == '0'){
		    //////////////////////////////////////
		    // SEND OUT EMAIL
		    //////////////////////////////////////
		    
		    $emailTo = $core_students_email_address;//<EMAIL>"; //$core_students_email_address;
		    $emailFrom = master_email;
		    $subject = addslashes("A new project has been assigned to you");
		    
		    ////////////////////////
		    $message_plain="
Dear $core_students_name
			
This is an notice to let you know that you have been assigned as producer for the project /$args[db1616]/.
You role as a producer will require you to 
			
	* task 1
	* task 2
	* task 3
	* task 4
			
Please follow this link to login to the student's portal where you can submit your documents.
			
Regards
			
Admin
";
	        $message_html = text_to_html($message_plain);
	        	
	        $email_address_of_invitee=$emailTo;
	        //$message_sent=$message_plain;
	        $args['db1091']='';
	        					
	        log_email($emailTo,$subject,$message_plain,$message_html,$emailFrom,$tag_email);
	        $args['msg'] .= '<div class="success">Email notification sent to '.$emailTo.' !</div>';
		
	        //and record that it has been sent
	        $sql_tracker="INSERT INTO form_invite_tracker(username_id, rec_id, usergroup, rel_id, db1077, db1078, db1081, db1089, db1091) VALUES ('".random()."', '".session_info("uid")."', '".session_info("ulevel")."', '".$record."', '$email_address_of_invitee', '".custom_date_and_time()."','$message_sent', '$tag_email','$args[db1091]')";
	        mysqli_query($dbcon,$sql_tracker);
	    } else{
	    	$args['msg'] .= '<div class="success">Email notification already sent</div>';
	    }
    }
    return array($args, $mappings);
}

?>
