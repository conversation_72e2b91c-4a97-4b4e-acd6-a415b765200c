<?php

namespace referee_functions;

// INVITE REVIEWERS
// SEND REVIEW INVITE

function reviewer_invite_type_check($args, $static, $mappings)
{

    #if($_SESSION['usergroup']==$static['ug']){
    if ($args['db11360'] == 'not specified') {
        die("<div class=\"alert alert-warning\">Sorry you have not selected a reference type. Please <a href=\"$_SESSION[url]\">go back and select one</a></div>");
    }

    if (isset($_GET['ref']) && isset($_GET['vw'])) {
        $core_stud_id = $_GET['ref'];
    } else {
        $core_stud_id = $_SESSION['student_id'];
    }

    //check if email already added
    $allow_multiple_references =  pull_field('lead_preferences', 'db352933', "WHERE  usergroup={$_SESSION['usergroup']}");
    if ($allow_multiple_references != 'yes'){
        $check_id_exists = pull_field("dir_reference", "count(*)", "WHERE db11357='$args[db11357]' AND usergroup='$_SESSION[usergroup]' AND rel_id='$core_stud_id' AND (rec_archive ='' OR rec_archive IS NULL) ");
        #echo "1=".$check_id_exists;
        if ($check_id_exists > 0) {
            die("<div class=\"alert alert-warning\">Sorry, email address already added. You cannot add the same referee more than twice <a href=\"$_SESSION[url]\">go back and try again</a></div>");
        }

    }

    //check if email is theirs
    $check_id_exists2 = pull_field("dir_reference", "count(*)", "WHERE db11357='$_SESSION[user]' AND usergroup='$_SESSION[usergroup]'  AND rel_id='$core_stud_id'  AND (rec_archive ='' OR rec_archive IS NULL)");
    #echo "2=".$check_id_exists2;
    if ($check_id_exists2 > 0) {
        die("<div class=\"alert alert-warning\">Sorry, email address is the same as yours.<a href=\"$_SESSION[url]\">go back and try again</a></div>");
    }

    #}

    return array($args, $mappings);
}

function reviewer_invite($args, $static, $mappings)
{
    if (!empty($_SESSION['references_data_copied'])) {
        //this is from withdraw and duplicate
        return array($args, $mappings);
    }
    /*
                     data sample coming from forms
                     
                     INSERT ARGS
    Array ( [db32510] => [db32511] => [db32512] => [db32514] => [db32515] => [db32516] => [db32517] => [db32518] => [db11360] => Professional_Reference [db11354] => Likely Candidate [db11357] => <EMAIL> [db11359] => [db11358] => [db32513] => yes [db19274] => yes [db11356] => [db11355] => [record] => [username_id] => Mon205019-D1 [rec_id] => 58233 [usergroup] => 52 [rel_id] => 73890 [id] => 2496 )
                    
                    UPDATE ARGS
    Array ( [db32510] => [db32511] => [db32512] => [db32514] => [db32515] => [db32516] => [db32517] => [db32518] => [db11360] => Professional_Reference [db11354] => Likely Candidate [db11357] => <EMAIL> [db11359] => [db11358] => [db32513] => yes [db19274] => yes [db11356] => [db11355] => [record] => 2496 [username_id] => Mon205109-B13 [rec_id] => 58233 [usergroup] => 52 [rel_id] => 73890 )
    
                    */

    //student details
    /* Incase this script is called from an external file the student id needs to be assigned */
    if (isset($_GET['ref']) && isset($_GET['vw'])) {
        $core_stud_id = $_GET['ref'];
    } else {
        $core_stud_id = $_SESSION['student_id'];
    }

    // get user
    $userdata = get_core_students($core_stud_id);
    list($core_students_id, $core_students_rec_id, $core_students_usergroup, $core_students_rel_id, $core_students_first_name, $core_students_middle_name,
        $core_students_surname, $core_students_email_address, $core_students_telephone_number, $core_students_date_of_birth, $core_students_gender,
        $core_students_source_of_applicant, $core_students_cohort, $core_students_course_of_study, $core_students_level_of_entry, $core_students_country_of_origin,
        $core_students_application_status, $core_students_has_applied, $core_students_archive_record, $unique_id, $core_student_application_route,
        $core_students_cohort_intake, $core_students_deleted, $core_student_application_ucas, $core_student_ucas_pass, $core_students_course_of_study_id,
        $core_students_course_audio, $core_students_length_of_study) = $userdata;// if second value is defined then use it

    //check if message already sent
    $current_state = pull_field("form_email_log", "count(*)", "WHERE db1153='$args[db11357]' AND usergroup='$_SESSION[usergroup]' AND db1152='Referee Invite - $core_students_id'");

    //carry out some checks for blanks
    if ($args['db11357'] == '' || $args['db11354'] == '') return array($args, $mappings); // exit function
    if ($args['db19274'] == 'no' || $args['db19274'] == '') return array($args, $mappings); // send email is not ticket as yes
    if ($args['db11360'] == 'not specified' && $_SESSION['usergroup'] == 52) {
        echo '<div class="alert alert-warning">Sorry you have not selected a reference type</div>';
        return array($args, $mappings);
    } // check for ug 52

    //check if we should send email
    if ($current_state > '0') {
        echo '<div class="alert alert-warning">Sorry, email not sent as it has been previously sent</div>';
        return array($args, $mappings);
    } // exit function

    $student_id = '';
    $reviewer_email = $args['db11357'];
    $school_name = $_SESSION['school_name'];

    if ($_SESSION['usergroup'] == 10) {//.stanselm
        $template = ($core_students_course_of_study == 'Residential' ? '62' : '63');
        $student_prefered_name1 = pull_field("dir_registration1", "db845", "WHERE rel_id='$core_students_id'");
        $student_prefered_name = ($student_prefered_name1 == '' ? "$core_students_first_name" : "$student_prefered_name1");
    } elseif ($_SESSION['usergroup'] == 52) { //sras
        $student_prefered_name = $core_students_first_name;
        $ref_type = $args['db11360'];

        switch ($ref_type) {
            case "Academic_Reference":
                $template = 359;
                break;
            case "Professional_Reference":
                $template = 360;
                break;
            case "Study_Abroad_Reference":
                $template = 361;
                break;
            default:
                $template = 361;

        }//end
    } else {
        $template = pull_field("coms_template", "id", "WHERE usergroup='$_SESSION[usergroup]' AND db1147='12' AND (rec_archive IS NULL OR rec_archive = '')");//Main International Email Address
        if ($template < 1) {
            $template = 149;
        } // set to default
        $student_prefered_name = $core_students_first_name;
        //echo "---".$template;
    }

    // send email
    ////////////////////////////////////////////////////end///////////////////////////////////////////////
    // select the right email template
    //get template
    list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template($template);

    $programme_detail = explode("|", pull_field("core_courses", "CONCAT(db29048,'|',id)", "WHERE id='$core_students_course_of_study_id' "));
    $programme_detail = explode("|", pull_field("dir_cohorts", "CONCAT(db32332,'|',id)", "WHERE id='" . pull_field("core_students", "db1682", "WHERE id = $core_students_id") . "' "));
    $application_deadline = format_date("F j, Y", $programme_detail[0]);//db29048 = start date
    $programme_location = pull_field("core_course_locations_rel a,core_course_locations b", "db32336", "WHERE a.db32202=b.id AND a.rel_id='$core_students_course_of_study_id' "); //db32194 = location

    //unique reference
    if ($args['record'] > 0) {
        $unique_reference_link = pull_field("dir_reference", "username_id", "WHERE id='$args[record]' "); //$args['username_id'];
    } else {
        $unique_reference_link = $args['username_id'];
    }

    dev_debug('unique link = ' . $unique_reference_link);

    //search replace
    $message_html = email_template_replace_values("{{interviewee}}", $args['db11354'], $coms_template_html_version);//full name of referee
    $message_html = email_template_replace_values("{{referee}}", $args['db11354'], $message_html);//full name of referee
    $message_html = email_template_replace_values("{{first_name_of_applicant}}", $student_prefered_name, $message_html);
    $message_html = email_template_replace_values("{{last_name_of_applicant}}", $core_students_surname, $message_html);
    $message_html = email_template_replace_values("{{programme}}", $core_students_course_of_study, $message_html);
    $message_html = email_template_replace_values("{{intake}}", $core_students_cohort_intake, $message_html);
    $message_html = email_template_replace_values("{{school_name}}", $school_name, $message_html);
    $message_html = email_template_replace_values("{{location}}", $programme_location, $message_html);
    $message_html = email_template_replace_values("{{application_deadline}}", $application_deadline, $message_html);
    $message_html = email_template_replace_values("{{referee_link}}", '<a href="https://' . $_SESSION['subdomain'] . '.heiapply.com/application/reference-submission/' . $unique_reference_link . '">this link</a>', $message_html);

    //$message_html.='<br/>Follow <a href="https://'.$_SESSION['subdomain'].'.heiapply.com/application/reference-submission/'.$args['username_id'].'">this link</a> to submit your reference.<br/> If the link is not clickable, please copy and paste the following link direct into your browser ( https://'.$_SESSION['subdomain'].'.heiapply.com/application/reference-submission/'.$args['username_id'].' ) ';

    $email_add = pull_field("form_schools", "db1118", "WHERE id='$_SESSION[usergroup]'");//Main International Email Address
    $emailTo = $reviewer_email;//$student_email;// useres email address
    $subject = $coms_template_subject_line;
    $message_plain_txt = $message_plain;
    $message_html = $message_html;
    $emailFrom = $email_add;


    log_email($emailTo, $subject, $message_plain_txt, $message_html, $emailFrom, "Referee Invite - $core_students_id");

    //echo $sql;
    if (empty($_SESSION['withdraw_and_duplicate'])) {
        echo "<div class=\"alert alert-success\"><h2>Success!</h2>An email has been sent to " . $args['db11354'] . " ($reviewer_email) asking them to provide us with a reference in relation to your application</div>";
    }

    return array($args, $mappings);
}

function alert_admin_when_reference_added($args, $static, $mappings)
{
    $unique_id = $args['db33617'];
    $dbh = get_dbh();
    $query = "SELECT *,a.rel_id as sudent_rel_id FROM dir_reference a,core_students b WHERE a.rel_id=b.id
AND a.usergroup=$_SESSION[usergroup]
AND a.username_id = '$unique_id' LIMIT 1";
    $sth = $dbh->prepare($query);
    $sth->execute();

    $results_list = array();
//while($row_result = $sth->fetchAll()) {
    foreach ($row_result = $sth->fetchAll() as $row) {
        $ref_rec_id = $row['rec_id'];
        $ref_rel_id = $row['sudent_rel_id'];
        $ref_fname = $row['db39'];
        $ref_surname = $row['db40'];
        $ref_email = $row['db764'];
        $ref_type = $row['db11360'];
        $ref_name = $row['db11354'];
        $ref_usergroup = $row['usergroup'];
    }


    //send a message to admin
    $uname = pull_field("core_students", "username_id", "WHERE id = $ref_rel_id");
    $student_link_url = engine_url . "/direct/proc?pg=4&vw=$uname&ref=$ref_rel_id";
    $message = terminology('Dear Admissions', $_SESSION['url'], 'Dear Admissions', true) . ' ' . $ref_name . ' has just provided a ' . str_replace('_', ' ', $ref_type) . ' for ' . $ref_fname . ' ' . $ref_surname;
    $sql = "INSERT INTO core_notes
      (username_id, rec_id, usergroup, rel_id, db73, db76, db77, db80, db79, db91, db139)
      VALUES ('" . random() . "', '$ref_rec_id', '" . $ref_usergroup . "', '$ref_rel_id', '4', '$message', '$ref_rec_id', 'yes', '$student_link_url', '3', 'no')";// if users does not want alerts lock using this

    $sth_1 = $dbh->prepare($sql);
    $sth_1->execute();

    //send email to admin
    $message_plain =
        "Dear $ref_fname,
		
		The following message has been posted to Admin on your behalf:
		
		$message
		
		Thank you!
		";

    $message_html = text_to_html($message_plain);
    $emailFrom = master_email;
    track_use("$ref_email, $message_plain, $message_html, $emailFrom,");

    log_email($ref_email, "New Reference has been posted", $message_plain, $message_html, $emailFrom, "Reference Alert");
}

?>
