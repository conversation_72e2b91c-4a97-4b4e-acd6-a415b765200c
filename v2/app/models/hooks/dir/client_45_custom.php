<?php
namespace ug_45_custom;

function supplementary_page_update_208($args, $static, $mappings){
	$dbh = get_dbh();
 //send notification to admin when student updates a field
	// start check on usergroup
	if($_SESSION['usergroup']==45){

		$flag = false;
		if($args['db6820'] && $args['db6820'] !=''){
			$flag = true;
		}
		elseif($args['db6822'] && $args['db6822'] !=''){
			$flag = true;
		}
		elseif($args['db6823'] && $args['db6823'] !=''){
			$flag = true;
		}
		$unique = $args['db6820']."-".$args['db6822']."-".$args['db6823'];
		$u = substr($unique, 0,255);
		$message_comment_notes_email='I have updated my Tier 4 Visa Application';

		$sql = "select count(*) as nu from core_notes c WHERE c.db79 = '".$u."' AND c.db76 = '".$message_comment_notes_email."' AND c.rel_id = '".$_SESSION['student_id']."'";
		$sth = $dbh->prepare($sql);
		$sth->execute();
		$pdo_preferences = $sth->fetch(\PDO::FETCH_ASSOC);

		if (($flag == true) && ($pdo_preferences["nu"]==0)) {
			//Insert note with response
			
			$sql4="INSERT INTO core_notes (username_id, rec_id, usergroup, rel_id, db73, db76, db77, db80, db79, db91, db139) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";// if users does not want alerts lock using this
			$sth = $dbh->prepare($sql4);
			$sth->execute(array(random(),session_info("uid"),session_info("access"),$_SESSION['student_id'],'0',$message_comment_notes_email, session_info("uid"),'yes', $u, '3', 'no'));

		}
		
		$flag = false;
		if($args['db6824'] && $args['db6824'] !=''){
			$flag = true;
		}
		elseif($args['31623'] && $args['31623'] !=''){
			$flag = true;
		}
		elseif($args['db31624'] && $args['db31624'] !=''){
			$flag = true;
		}

		$unique = $args['db6824']."-".$args['31623']."-".$args['db31624'];
		$u = substr($unique, 0,255);
		$message_comment_notes_email='I have updated my Visa Issued Information';

		$sql = "select count(*) as nu from core_notes c WHERE c.db79 = '".$u."' AND c.db76 = '".$message_comment_notes_email."' AND c.rel_id = '".$_SESSION['student_id']."'";
		$sth = $dbh->prepare($sql);
		$sth->execute();
		$pdo_preferences = $sth->fetch(\PDO::FETCH_ASSOC);
		
		if (($flag == true) && ($pdo_preferences["nu"]==0)) {
			//Insert note with response
			$message_comment_notes_email='I have updated my Visa Issued Information';
			$sql4="INSERT INTO core_notes (username_id, rec_id, usergroup, rel_id, db73, db76, db77, db80, db79, db91, db139) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";// if users does not want alerts lock using this
			$sth = $dbh->prepare($sql4);
			$sth->execute(array(random(),session_info("uid"),session_info("access"),$_SESSION['student_id'],'0',$message_comment_notes_email, session_info("uid"),'yes', $u, '3', 'no'));

		}

	}

	return array($args, $mappings);

}
function supplementary_page_update_213($args, $static, $mappings){
 	$dbh = get_dbh();
	if($_SESSION['usergroup']==45) {
		$flag = false;
		if($args['db8844'] && $args['db8844'] != ''){
			$flag = true;
		}
		elseif($args['db8845'] && $args['db8845'] != ''){
			$flag = true;
		}
		
		$unique = $args['db8844']."-".$args['db8845'];
		$u = substr($unique, 0,255);
		$message_comment_notes_email = 'I have updated my Tier 4 Visa Application';

		$sql = "select count(*) as nu from core_notes c WHERE c.db79 = '".$u."' AND c.db76 = '".$message_comment_notes_email."' AND c.rel_id = '".$_SESSION['student_id']."'";
		$sth = $dbh->prepare($sql);
		$sth->execute();
		$pdo_preferences = $sth->fetch(\PDO::FETCH_ASSOC);

		if (($flag == true) && ($pdo_preferences["nu"]==0)) {
			//Insert note with response
			$message_comment_notes_email = 'I have updated my Tier 4 Visa Application';
			$sql4 = "INSERT INTO core_notes (username_id, rec_id, usergroup, rel_id, db73, db76, db77, db80, db79, db91, db139) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";// if users does not want alerts lock using this
			$sth = $dbh->prepare($sql4);
			$sth->execute(array(random(), session_info("uid"), session_info("access"), $_SESSION['student_id'], '0', $message_comment_notes_email, session_info("uid"),'yes', $u, '3', 'no'));

		}
		$flag = false;
		if ($args['db8846'] && $args['db8846'] != ''){
			$flag = true;
		}
		elseif($args['db8847'] && $args['db8847'] != ''){
			$flag = true;
		}
		elseif($args['db8848'] && $args['db8848'] != ''){
			$flag = true;
		}
		elseif($args['db8849'] && $args['db8849'] != ''){
			$flag = true;
		}
		elseif($args['db8854'] && $args['db8854'] != ''){
			$flag = true;
		}

		$unique = $args['db8846']."-".$args['db8847']."-".$args['db8848']."-".$args['db8849']."-".$args['db8854'];
		$u = substr($unique, 0,255);
		$message_comment_notes_email = 'I have updated my Flight Details';

		$sql = "select count(*) as nu from core_notes c WHERE c.db79 = '".$u."' AND c.db76 = '".$message_comment_notes_email."' AND c.rel_id = '".$_SESSION['student_id']."'";
		$sth = $dbh->prepare($sql);
		$sth->execute();
		$pdo_preferences = $sth->fetch(\PDO::FETCH_ASSOC);

		if (($flag == true) && ($pdo_preferences["nu"]==0)) {
			//Insert note with response
			$message_comment_notes_email = 'I have updated my Flight Details';
			$sql4 = "INSERT INTO core_notes (username_id, rec_id, usergroup, rel_id, db73, db76, db77, db80, db79, db91, db139) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";// if users does not want alerts lock using this
			$sth = $dbh->prepare($sql4);
			$sth->execute(array(random(), session_info("uid"), session_info("access"), $_SESSION['student_id'], '0', $message_comment_notes_email, session_info("uid"),'yes', $u, '3', 'no'));

		}

		$flag = false;
		if ($args['db8855'] && $args['db8855'] != ''){
			$flag = true;
		}
		elseif($args['db8837'] && $args['db8837'] != ''){
			$flag = true;
		}
		elseif($args['db10277'] && $args['db10277'] != ''){
			$flag = true;
		}
		elseif($args['db10278'] && $args['db10278'] != ''){
			$flag = true;
		}

		$unique = $args['db8855']."-".$args['db8837']."-".$args['db10277']."-".$args['db10278'];
		$u = substr($unique, 0,255);
		$message_comment_notes_email = 'I have updated my UK Address';

		$sql = "select count(*) as nu from core_notes c WHERE c.db79 = '".$u."' AND c.db76 = '".$message_comment_notes_email."' AND c.rel_id = '".$_SESSION['student_id']."'";
		$sth = $dbh->prepare($sql);
		$sth->execute();
		$pdo_preferences = $sth->fetch(\PDO::FETCH_ASSOC);

		if (($flag == true) && ($pdo_preferences["nu"]==0)) {
			//Insert note with response
			$message_comment_notes_email = 'I have updated my UK Address';
			$sql4 = "INSERT INTO core_notes (username_id, rec_id, usergroup, rel_id, db73, db76, db77, db80, db79, db91, db139) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";// if users does not want alerts lock using this
			$sth = $dbh->prepare($sql4);
			$sth->execute(array(random(), session_info("uid"), session_info("access"), $_SESSION['student_id'], '0', $message_comment_notes_email, session_info("uid"),'yes', $u, '3', 'no'));

		}
	}
	return array($args, $mappings);

}



function core_info_waterfall($args, $static, $mappings){
//update fields when core students is updated

	// start check on usergroup
	if($_SESSION['usergroup']==45){

		if ($args['db6763'] && $args['db6763']!=='') {
			//update the telephone
			$db765 = $args['db6763']; //telephone Current Residential Phone Number

			//update the db Telephone Number (international format)
			$dbh = get_dbh();
			$sql = "UPDATE core_students SET db765 =? WHERE usergroup=? AND id=?";
			//echo $sql;
			$update_sql = $sql;
			$sth = $dbh->prepare($update_sql);
			$sth->execute(array($db765,$_SESSION['usergroup'],$args['rel_id']));

		}
		if ($args['db31681'] && $args['db31681']!=='') {

			//$db765 = $args['db6763']; //mobile
			$db28467 = $args['db31681']; //mobile US cell phone number

			//update the db Mobile Number
			$dbh = get_dbh();
			$sql = "UPDATE core_students SET db28467=? WHERE usergroup=? AND id=?";
			//echo $sql;
			$update_sql = $sql;
			$sth = $dbh->prepare($update_sql);
			$sth->execute(array($db28467,$_SESSION['usergroup'],$args['rel_id']));

		}
		
		if ($args['db769'] && $args['db769']!=='') {

			$db769 = $args['db769']; //nationality

			//update the db Mobile Number
			$dbh = get_dbh();
			$sql = "UPDATE core_students SET db763=? WHERE usergroup=? AND id=?";
			//echo $sql;
			$update_sql = $sql;
			$sth = $dbh->prepare($update_sql);
			$sth->execute(array($db769,$_SESSION['usergroup'],$args['rel_id']));

		}

	}

	return array($args, $mappings);

}

function core_info_waterfall_69($args, $static, $mappings){
//update fields when core students is updated

    // start check on usergroup
    if($_SESSION['usergroup']==45){
		if ($args['db762'] && $args['db762'] !='') {

			//update the date of birth and gender if not filled in in the invite
			$db53 = $args['db762']; // date of birth

			//update the db
			$dbh = get_dbh();
			$sql = "UPDATE core_students SET db53 = ? WHERE usergroup=? AND id=?";
			//echo $sql;
			$update_sql = $sql;
			$sth = $dbh->prepare($update_sql);
			$sth->execute(array($db53, $_SESSION['usergroup'], $args['rel_id']));
		}
		if ($args['db779'] && $args['db779'] !='') {

			//update the date of birth and gender if not filled in in the invite
			$db44 = $args['db779']; //gender

			//update the db
			$dbh = get_dbh();
			$sql = "UPDATE core_students SET db44 = ? WHERE usergroup=? AND id=?";
			//echo $sql;
			$update_sql = $sql;
			$sth = $dbh->prepare($update_sql);
			$sth->execute(array($db44, $_SESSION['usergroup'], $args['rel_id']));
		}


	}


    return array($args, $mappings);

}

function send_interview_invite_confirmation_notification($args, $static, $mappings) {

    $db = get_dbh();
// start check on usergroup
/*	if($_SESSION['usergroup']==45) {
		if ($args['db12599'] || $args['db30212'] ) {

			if ($args['db12599'] ) { //tier 4
				$interview_date = $args['db12600'];
				$interview_time = $args['db12603'];
			} else if  ($args['db30212'] ) { //tier 5
				$interview_date = $args['db30213'];
				$interview_time = $args['db30216'];
			}

			$sql = $dbh->prepare("SELECT db1085, db1086, db1083 FROM coms_template WHERE id = '348'"); //selects the appropriate template
			$sql->execute();
			$message_templates = $sql->fetchAll(\PDO::FETCH_OBJ);//retrieves an array of objects
			$message_template = $message_templates[0];//retrieves first object
			$message_subject = $message_template->db1086;//access the subject from the template, this goes straight into the log_email function
			$message_content = $message_template->db1085;//accesses the content, this is with the placeholders
			$message_category = $message_template->db1083;//access category group

			$sql = $dbh->prepare("SELECT CONCAT(db39, ' ' , db40) AS 'full_name', db764 as 'email' FROM core_students WHERE id = $_GET[ref]");//getting user details, joining first name and last for email, not used individually, email used for log_email function TO
			$sql->execute();
			$user_infos = $sql->fetchAll(\PDO::FETCH_OBJ);
			$user_info = $user_infos[0];
			$user_name = $user_info->full_name;
			$user_email = $user_info->email;


			$interview_date_time = date("l jS F Y G:i", strtotime("$interview_date $interview_time")). ' GMT';
			//$interview_location = $args['db14791'];
			//$student_portal = '<a href="https://brainexpansions.heiapply.com/application/login">student portal</a>';

			$message_content = str_replace('{{name}}', $user_name, $message_content);
			$message_content = str_replace('{{interview_date_time}}', $interview_date_time, $message_content);
			//$message_content = str_replace('{{interview_time}}', $interview_time, $message_content);
			$message_cat = $message_category.' - '.$interview_date.' '.$interview_time;

			$current_state = pull_field("form_email_log", "count(*)", "WHERE db1153='$user_email' AND usergroup='$_SESSION[usergroup]' AND db1152='$message_cat'");
			if ($current_state > '0') return array($args, $mappings); // exit function


			log_email($user_email, $message_subject, '', $message_content, '<EMAIL>', $message_cat);
		}
	}*/
	return array($args, $mappings);

}