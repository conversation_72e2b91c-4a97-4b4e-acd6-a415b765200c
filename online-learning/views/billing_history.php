<div class="w-screen overflow-x-hidden">
    <div class="w-3/4 mx-auto p-8 gap-4 flex">
        <div class="w-9/12">
            <div class="bg-white rounded shadow-lg p-8 overflow-hidden mb-8 space-y-4">
                <h1 class="font-bold text-2xl">Billing History</h1>
                <?php
                $invoice_args = array('student_id' => $_SESSION['student_id']);
                $your_billing_history = $OL->get_invoices($invoice_args);
                if (count($your_billing_history) == 0) {
                    ?>

                <?php } else { ?>
                    <table class="w-full text-sm">
                        <thead>
                        <tr class="text-left">
                            <th>Inv no.</th>
                            <th>Date</th>
                            <th>Payment Status</th>
                            <th>Plan</th>
                            <th>Amount</th>
                            <th>&nbsp;</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php
                        foreach ($your_billing_history as $billing_history) {
                            ?>
                            <tr class="border-b">
                                <td><?php echo $billing_history['invoice_number']; ?></td>
                                <td><?php echo $billing_history['date']; ?></td>
                                <td>Paid</td>
                                <td><?php echo $billing_history['description']; ?></td>
                                <td><?php echo $billing_history['display_total']; ?></td>
                                <td class="text-right w-1/6"><a
                                            href="<?php echo engine_url . '/accounts/invoice/' . $billing_history['username_id'] ?>"
                                            target="_blank"
                                            class="bg-white cursor-pointer border focus:ring-4 w-full focus:outline-none font-medium rounded-full text-sm px-3 py-1 text-center inline-flex items-center">Download
                                        PDF</a></td>
                            </tr>

                            </tr>
                        <?php } ?>
                        </tbody>
                    </table>
                <?php } ?>
            </div>
        </div>
        <div class="w-3/12">
            <?php include("includes/settings_side_menu.php"); ?>
        </div>
    </div>

</div>