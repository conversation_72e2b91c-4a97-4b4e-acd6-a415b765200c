/*** Shortcodes Ultimate - galleries elements ***/

.su-slider {
	position: relative;
	margin-bottom: 1.5em;
	overflow: hidden;
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	-ms-backface-visibility: hidden;
	-o-backface-visibility: hidden;
	backface-visibility: hidden;
	z-index: 1;
}
.su-slider-centered { margin: 0 auto 1.5em auto }
.su-slider-slides {
	position: relative;
	width: 100%;
	-webkit-transition-property: -webkit-transform, left, top;
	-webkit-transition-duration: 0s;
	-webkit-transform: translate3d(0px, 0, 0);
	-webkit-transition-timing-function: ease;
	-moz-transition-property: -moz-transform, left, top;
	-moz-transition-duration: 0s;
	-moz-transform: translate3d(0px, 0, 0);
	-moz-transition-timing-function: ease;
	-o-transition-property: -o-transform, left, top;
	-o-transition-duration: 0s;
	-o-transform: translate3d(0px, 0, 0);
	-o-transition-timing-function: ease;
	-o-transform: translate(0px, 0px);
	-ms-transition-property: -ms-transform, left, top;
	-ms-transition-duration: 0s;
	-ms-transform: translate3d(0px, 0, 0);
	-ms-transition-timing-function: ease;
	transition-property: transform, left, top;
	transition-duration: 0s;
	transform: translate3d(0px, 0, 0);
	transition-timing-function: ease;
}
.swiper-free-mode > .su-slider-slides {
	-webkit-transition-timing-function: ease-out;
	-moz-transition-timing-function: ease-out;
	-ms-transition-timing-function: ease-out;
	-o-transition-timing-function: ease-out;
	transition-timing-function: ease-out;
	margin: 0 auto;
}
.su-slider-slide {
	position: relative;
	float: left;
}
.su-slider-slide > a,
.su-slider-slide > img,
.su-slider-slide > a > img {
	position: relative;
	display: block;
	width: 100%;
	height: 100%;
	text-decoration: none;
}
.su-slider-responsive-yes .su-slider-slide > a,
.su-slider-responsive-yes .su-slider-slide > img,
.su-slider-responsive-yes .su-slider-slide > a > img { height: auto }
.su-slider-pages-no .su-slider-pagination { display: none }
.swiper-wp8-horizontal { -ms-touch-action: pan-y }
.swiper-wp8-vertical { -ms-touch-action: pan-x }
.su-slider-slide-title {
	position: absolute;
	left: 20px;
	bottom: 20px;
	display: block;
	max-width: 90%;
	padding: 5px 10px;
	color: #fff;
	line-height: 1.3;
	background: rgb(0, 0, 0);
	background: rgba(0, 0, 0, 0.5);
	border-radius: 5px;
	-moz-border-radius: 5px;
	-webkit-border-radius: 5px;
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	filter: alpha(opacity=0);
	opacity: 0;
	transition: opacity 1s;
	-o-transition: opacity 1s;
	-ie-transition: opacity 1s;
	-moz-transition: opacity 1s;
	-webkit-transition: opacity 1s;
}
.su-slider-slide-visible .su-slider-slide-title {
	filter: alpha(opacity=100);
	opacity: 1;
}
.su-slider-prev,
.su-slider-next {
	position: absolute;
	top: 50%;
	display: block;
	width: 40px;
	height: 40px;
	margin-top: -20px !important;
	background: rgb(0, 0, 0) 0 0 url('../../images/shortcodes/swiper/default.png') no-repeat;
	background-color: rgba(0, 0, 0, 0.5);
	cursor: pointer;
	border-radius: 50%;
	-moz-border-radius: 50%;
	-webkit-border-radius: 50%;
	filter: alpha(opacity=0);
	opacity: 0;
	transition: all .2s;
	-o-transition: all .2s;
	-ie-transition: all .2s;
	-moz-transition: all .2s;
	-webkit-transition: all .2s;
}
.su-slider-prev {
	right: 10px;
	margin-right: 30px;
	background-position: -40px 0;
}
.su-slider-next {
	left: 10px;
	margin-left: 30px;
	background-position: 0 0;
}
.su-slider:hover .su-slider-prev,
.su-slider:hover .su-slider-next {
	margin-left: 0;
	margin-right: 0;
	filter: alpha(opacity=70);
	opacity: 0.7;
}
.su-slider .su-slider-prev:hover,
.su-slider .su-slider-next:hover {
	filter: alpha(opacity=100);
	opacity: 1;
}
.su-slider-pagination {
	position: absolute;
	left: 15%;
	right: 15%;
	top: 50%;
	width: 70%;
	height: 0;
	margin-top: -7px;
	text-align: center;
	cursor: default;
	filter: alpha(opacity=0);
	opacity: 0;
	transition: all .2s;
	-o-transition: all .2s;
	-ie-transition: all .2s;
	-moz-transition: all .2s;
	-webkit-transition: all .2s;
}
.su-slider:hover .su-slider-pagination {
	bottom: 20px;
	filter: alpha(opacity=100);
	opacity: 1;
}
.su-slider-pagination span {
	display: inline-block;
	width: 14px;
	height: 14px;
	margin: 0;
	background: rgb(0, 0, 0) 0 -999px url('../../images/shortcodes/swiper/default.png') no-repeat;
	background-color: rgba(0, 0, 0, 0.5);
	cursor: pointer;
	border-radius: 50%;
	-moz-border-radius: 50%;
	-webkit-border-radius: 50%;
	transition: all .2s;
	-o-transition: all .2s;
	-ie-transition: all .2s;
	-moz-transition: all .2s;
	-webkit-transition: all .2s;
}
.su-slider:hover .su-slider-pagination span { margin: 0 3px }
.su-slider .su-slider-pagination span.swiper-visible-switch {
	background-color: rgb(255, 255, 255);
	background-color: rgba(255, 255, 255, 0.5);
}
.su-slider-pagination span:hover,
.su-slider-pagination span.swiper-active-switch { background-position: 0 -40px }

/*		Carousel
---------------------------------------------------------------*/

.su-carousel {
	position: relative;
	margin-bottom: 1.5em;
	overflow: hidden;
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	-ms-backface-visibility: hidden;
	-o-backface-visibility: hidden;
	backface-visibility: hidden;
	z-index: 1;
}
.su-carousel-centered { margin: 0 auto 1.5em auto }
.su-carousel-slides {
	position: relative;
	width: 100%;
	-webkit-transition-property: -webkit-transform, left, top;
	-webkit-transition-duration: 0s;
	-webkit-transform: translate3d(0px, 0, 0);
	-webkit-transition-timing-function: ease;
	-moz-transition-property: -moz-transform, left, top;
	-moz-transition-duration: 0s;
	-moz-transform: translate3d(0px, 0, 0);
	-moz-transition-timing-function: ease;
	-o-transition-property: -o-transform, left, top;
	-o-transition-duration: 0s;
	-o-transform: translate3d(0px, 0, 0);
	-o-transition-timing-function: ease;
	-o-transform: translate(0px, 0px);
	-ms-transition-property: -ms-transform, left, top;
	-ms-transition-duration: 0s;
	-ms-transform: translate3d(0px, 0, 0);
	-ms-transition-timing-function: ease;
	transition-property: transform, left, top;
	transition-duration: 0s;
	transform: translate3d(0px, 0, 0);
	transition-timing-function: ease;
}
.swiper-free-mode > .su-carousel-slides {
	-webkit-transition-timing-function: ease-out;
	-moz-transition-timing-function: ease-out;
	-ms-transition-timing-function: ease-out;
	-o-transition-timing-function: ease-out;
	transition-timing-function: ease-out;
	margin: 0 auto;
}
.su-carousel-slide {
	position: relative;
	float: left;
}
.su-carousel-slide > a,
.su-carousel-slide > img,
.su-carousel-slide > a > img {
	position: relative;
	display: block;
	width: 100%;
	height: 100%;
	text-decoration: none;
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
}
.su-carousel-responsive-yes .su-carousel-slide > a,
.su-carousel-responsive-yes .su-carousel-slide > img,
.su-carousel-responsive-yes .su-carousel-slide > a > img { height: auto }
.su-carousel-pages-no .su-carousel-pagination { display: none }
.swiper-wp8-horizontal { -ms-touch-action: pan-y }
.swiper-wp8-vertical { -ms-touch-action: pan-x }
.su-carousel { }
.su-carousel .su-carousel-slide a { padding: 0 5px }
.su-carousel .su-carousel-slide img {
	padding: 3px;
	border: 1px solid #ccc;
	background: #fff;
	border-radius: 5px;
	-moz-border-radius: 5px;
	-webkit-border-radius: 5px;
}
.su-carousel .su-carousel-slide-title {
	position: absolute;
	left: 9px;
	right: 9px;
	bottom: 4px;
	display: block;
	padding: 2px 3px;
	color: #fff;
	line-height: 1.2;
	font-size: 11px;
	background: rgb(0, 0, 0);
	background: rgba(0, 0, 0, 0.5);
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	transition: opacity 1s;
	-o-transition: opacity 1s;
	-ie-transition: opacity 1s;
	-moz-transition: opacity 1s;
	-webkit-transition: opacity 1s;
}
.su-carousel .su-carousel-prev,
.su-carousel .su-carousel-next {
	position: absolute;
	top: 50%;
	display: block;
	width: 40px;
	height: 40px;
	margin-top: -20px;
	background: rgb(0, 0, 0) 0 0 url('../../images/shortcodes/swiper/default.png') no-repeat;
	background-color: rgba(0, 0, 0, 0.5);
	cursor: pointer;
	border-radius: 50%;
	-moz-border-radius: 50%;
	-webkit-border-radius: 50%;
	filter: alpha(opacity=0);
	opacity: 0;
	transition: all .2s;
	-o-transition: all .2s;
	-ie-transition: all .2s;
	-moz-transition: all .2s;
	-webkit-transition: all .2s;
}
.su-carousel .su-carousel-prev {
	right: 20px;
	margin-right: 40px;
	background-position: -40px 0;
}
.su-carousel .su-carousel-next {
	left: 20px;
	margin-left: 40px;
	background-position: 0 0;
}
.su-carousel:hover .su-carousel-prev,
.su-carousel:hover .su-carousel-next {
	margin-left: 0;
	margin-right: 0;
	filter: alpha(opacity=70);
	opacity: 0.7;
}
.su-carousel .su-carousel-prev:hover,
.su-carousel .su-carousel-next:hover {
	filter: alpha(opacity=100);
	opacity: 1;
}
.su-carousel .su-carousel-pagination {
	position: absolute;
	left: 15%;
	right: 15%;
	top: 50%;
	width: 70%;
	height: 0;
	margin-top: -7px;
	text-align: center;
	cursor: default;
	filter: alpha(opacity=0);
	opacity: 0;
	transition: all .2s;
	-o-transition: all .2s;
	-ie-transition: all .2s;
	-moz-transition: all .2s;
	-webkit-transition: all .2s;
}
.su-carousel:hover .su-carousel-pagination {
	bottom: 20px;
	filter: alpha(opacity=100);
	opacity: 1;
}
.su-carousel .su-carousel-pagination span {
	display: inline-block;
	width: 14px;
	height: 14px;
	margin: 0;
	background: rgb(0, 0, 0) 0 -999px url('../../images/shortcodes/swiper/default.png') no-repeat;
	background-color: rgba(0, 0, 0, 0.5);
	cursor: pointer;
	border-radius: 50%;
	-moz-border-radius: 50%;
	-webkit-border-radius: 50%;
	transition: all .2s;
	-o-transition: all .2s;
	-ie-transition: all .2s;
	-moz-transition: all .2s;
	-webkit-transition: all .2s;
}
.su-carousel:hover .su-carousel-pagination span { margin: 0 3px }
.su-carousel .su-carousel-pagination span.swiper-visible-switch {
	background-color: rgb(255, 255, 255);
	background-color: rgba(255, 255, 255, 0.5);
}
.su-carousel .su-carousel-pagination span:hover,
.su-carousel .su-carousel-pagination span.swiper-active-switch { background-position: 0 -40px }

/*		Custom gallery
---------------------------------------------------------------*/

.su-custom-gallery { margin-bottom: 1.5em }
.su-custom-gallery:after,
.su-custom-gallery:before {
	content: "";
	display: table;
}
.su-custom-gallery:after { clear: both }
.su-custom-gallery-slide {
	position: relative;
	float: left;
	margin: 0.5em;
	overflow: hidden;
}
.su-custom-gallery-slide a {
	display: block;
	width: 100%;
	height: 100%;
}
.su-custom-gallery-slide img {
	display: block;
	margin: 0;
	padding: 3px;
	border: 1px solid #ccc;
	background: #fff;
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	border-radius: 5px;
	-moz-border-radius: 5px;
	-webkit-border-radius: 5px;
	box-shadow: none;
	-moz-box-shadow: none;
	-webkit-box-shadow: none;
	transition: all .2s;
	-o-transition: all .2s;
	-ie-transition: all .2s;
	-moz-transition: all .2s;
	-webkit-transition: all .2s;
}
.su-custom-gallery-slide:hover img {
	background: #eee;
	border: 1px solid #aaa;
}
.su-custom-gallery-slide span {
	position: absolute;
	left: 4px;
	bottom: 4px;
	right: 4px;
	display: block;
	padding: 2px 4px;
	font-size: 12px;
	color: #fff;
	background: rgb(0, 0, 0);
	background: rgba(0, 0, 0, 0.5);
	line-height: 1.3;
	opacity: 0;
	filter: alpha(opacity=0);
	transition: all .2s;
	-o-transition: all .2s;
	-ie-transition: all .2s;
	-moz-transition: all .2s;
	-webkit-transition: all .2s;
}
.su-custom-gallery-slide:hover span {
	opacity: 1;
	filter: alpha(opacity=100);
}
.su-custom-gallery-title-never span { display: none }
.su-custom-gallery-title-always span {
	opacity: 1;
	filter: alpha(opacity=100);
}