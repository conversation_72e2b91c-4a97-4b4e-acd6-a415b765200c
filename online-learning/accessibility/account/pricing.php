<?php include("./accessibility/includes/header_bootstrap.php"); // <-- You would replace or remove this if not using Bootstrap. ?>

<div class="nhsuk-width-container">
  <main class="nhsuk-main-wrapper" id="maincontent" role="main">
  
    <div class="nhsuk-grid-row">
      <div class="nhsuk-grid-column-full">

        <div class="intro nhsuk-u-margin-top-5" id="tiny">
          <div class="nhsuk-body">
            <h1 class="nhsuk-heading-xl">Plans</h1>

            <form class="check_access_plan_code_form" method="POST">
              <?php if (isset($_REQUEST['accode'])): ?>
                <p class="nhsuk-body nhsuk-u-text-align-center">
                  You already have an 
                  <span style="font-size:150%;">ACCESS CODE</span>
                </p>
              <?php else: ?>
                <p class="nhsuk-body nhsuk-u-text-align-center">
                  If you already have an 
                  <span style="font-size:150%;">ACCESS CODE</span>
                  please enter it below
                </p>
              <?php endif; ?>

              <!-- Start of input “group” -->
              <div class="nhsuk-form-group nhsuk-u-text-align-center" style="max-width: 350px; margin: 0 auto;">
                <label class="nhsuk-label nhsuk-u-visually-hidden" for="access_plan_code_field">
                  Enter code
                </label>
                <input 
                  type="text" 
                  class="nhsuk-input" 
                  required="required" 
                  id="access_plan_code_field"
                  placeholder="Enter code"
                  <?php if (isset($_REQUEST['accode'])): ?>
                     value="<?php echo $_REQUEST['accode']; ?>" hidden
                  <?php endif; ?>
                >
              </div>

              <button class="nhsuk-button nhsuk-u-margin-bottom-3" type="submit">
                Apply ACCESS Code
              </button>

              <input type="hidden" name="check_access_code" value="1">
              <input 
                type="hidden" 
                id="must_log_in" 
                name="must_log_in"
                value="<?php 
                  if ($_SESSION['ulevel'] == 19 && !$_SESSION['uid']) {
                      echo 'yes';
                  } else {
                      echo 'no';
                  } 
                ?>"
              >
              <!-- End of input group -->

            </form>
          </div>
        </div>

        <div id="plans">
          <div class="nhsuk-grid-row">
            
            <?php
            $username_id_is_set = 0;
            $access_plan_levels = pull_field("ols_usrlvl_pln_crs_link", "db37368", "WHERE db37366 = $_SESSION[ulevel] AND usergroup = $_SESSION[usergroup]");

            if (isset($_REQUEST['access_plan']) && $_REQUEST['access_plan'] != '') {
              $plan_args = array(
                'username_id' => $_REQUEST['access_plan'],
                'school_id'   => $_SESSION['usergroup'],
              );
              $username_id_is_set = 1;
            } else {
              $plan_args = array(
                'search'    => $_POST['search'],
                'school_id' => $_SESSION['usergroup'],
              );
            }

            if ($username_id_is_set == 1) {
              if ($access_plan_levels) {
                $plan_args['access_plan_level'] = $access_plan_levels;
              }
              $plans = array($OL->get_plans($plan_args));
            } else {
              if (isset($_REQUEST['course']) && $_REQUEST['course'] != '') {
                $plan_args = array(
                  'course'    => $_REQUEST['course'],
                  'school_id' => $_SESSION['usergroup'],
                );
              } else {
                $plan_args = array(
                  'dont_show_hidden_plans' => true,
                  'school_id'              => $_SESSION['usergroup']
                );
              }
              if ($access_plan_levels) {
                $plan_args['access_plan_level'] = $access_plan_levels;
              }
              $plans = $OL->get_plans($plan_args);
            }

            $plan_i = 0;
            foreach ($plans as $plan):
              $plan_i++;
              $courses = $plan['courses'];
              $lang_token = (isset($plan['access_plan_language']) && $plan['access_plan_language'] != '')
                ? "&lang=$plan[access_plan_language]"
                : '';
            ?>
            
            <div class="nhsuk-grid-column-one-third">
              <div class="nhsuk-card" style="min-height: 180px;">
                <div class="nhsuk-card__content">
                  <h3 class="nhsuk-heading-m"><?php echo $plan['title']; ?></h3>
                  <div class="nhsuk-u-font-size-24 nhsuk-u-margin-bottom-3">
                    £<?php echo $plan['price']; ?>
                  </div>
                  
                  <p class="nhsuk-body nhsuk-u-text-align-center">
                    <a href="#"
                       data-bs-toggle="modal"
                       data-bs-target="#myModal<?php echo $plan['id']; ?>">
                      View the courses included
                    </a>
                  </p>

                  <div class="info nhsuk-u-visually-hidden">
                    <strong>Courses Included</strong>
                    <ul class="nhsuk-list nhsuk-list--bullet">
                      <?php if (!empty($courses)) {
                        foreach ($courses as $course) { ?>
                          <li><?php echo $course['title']; ?></li>
                        <?php }
                      } ?>
                    </ul>
                  </div>

                  <div class="action nhsuk-u-margin-top-4">
                    <?php if ($_SESSION['uid']): ?>
                      <?php if ($plan['price'] > 0): ?>
                        <a href="<?php echo home_url('register_new') . '?plan=' . $plan['username_id'] . $lang_token; ?>" 
                           class="nhsuk-button nhsuk-u-width-full">
                          <?php echo text_translate(terminology("Buy access", $_SESSION['url'], 'Online learning buy course', true), $_SESSION['lang']); ?>
                        </a>
                      <?php else: ?>
                        <a href="<?php echo home_url('register_new') . '?plan=' . $plan['username_id'] . $lang_token; ?>" 
                           class="nhsuk-button nhsuk-u-width-full">
                          <?php echo text_translate(terminology("Get started", $_SESSION['url'], 'Online learning buy course', true), $_SESSION['lang']); ?>
                        </a>
                      <?php endif; ?>
                    <?php else: ?>
                      <?php if ($_SESSION['ulevel'] != 19): // professionals should already be registered ?>
                        <a href="<?php echo home_url('register_new') . '?plan=' . $plan['username_id'] . $lang_token; ?>" 
                           class="nhsuk-button nhsuk-u-width-full">
                          <?php echo text_translate(terminology("New learner?", $_SESSION['url'], 'New learner should register to access', true), $_SESSION['lang']); ?>
                        </a>
                      <?php endif; ?>
                      <a href="<?php echo home_url('login') . '?redirect=' . home_url('register_new') . '%3Fplan=' . $plan['username_id'] . $lang_token; ?>" 
                         class="nhsuk-button nhsuk-u-width-full">
                        <?php echo text_translate(terminology("Existing learner?", $_SESSION['url'], 'Existing learner should login to access', true), $_SESSION['lang']); ?>
                      </a>
                    <?php endif; ?>
                  </div>
                </div>
              </div>
            </div>

            <?php if ($plan_i % 3 == 0): ?>
              <!-- In NHSUK, a new “row” is often just another nhsuk-grid-row, but you may remove or adapt as needed -->
              <div class="nhsuk-grid-row nhsuk-u-margin-top-3"></div>
            <?php endif; ?>

            <!-- Modal structure (custom) -->
            <div class="nhsuk-modal" id="myModal<?php echo $plan['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
              <div class="nhsuk-modal__dialog" role="document">
                <div class="nhsuk-modal__content">
                  <div class="nhsuk-modal__header">
                    <!-- In NHSUK you’d typically have a close button or link: 
                         <button class="nhsuk-modal__close" aria-label="Close">x</button>
                    -->
                    <h4 class="nhsuk-heading-m" id="myModalLabel">
                      <?php echo $plan['title']; ?> Courses (£<?php echo $plan['price']; ?>)
                    </h4>
                  </div>
                  <div class="nhsuk-modal__body">
                    <div class="info">
                      <ul class="nhsuk-list nhsuk-list--bullet">
                        <?php if (!empty($courses)) {
                          foreach ($courses as $course) { ?>
                            <li><?php echo $course['title']; ?></li>
                          <?php }
                        } ?>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- End Modal -->

            <?php endforeach; ?>

          </div>
        </div>
      </div>
    </div>

    <!-- Modal shown when checking the access plan -->
    <div class="nhsuk-modal" id="access_plan_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
      <div class="nhsuk-modal__dialog" role="document">
        <div class="nhsuk-modal__content nhsuk-u-text-align-center">
          <h3 class="nhsuk-heading-m nhsuk-u-margin-top-3">
            <i class="fa fa-spinner fa-spin fa-3x fa-fw"></i>
            <br><br>
            Checking access plan code...
          </h3>
        </div>
      </div>
    </div>

    <!-- Modal shown when VERIFIED the access plan -->
    <div class="nhsuk-modal" id="verified_access_plan_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
      <div class="nhsuk-modal__dialog" role="document">
        <div class="nhsuk-modal__content nhsuk-u-text-align-center">
          <h3 class="nhsuk-heading-m nhsuk-u-margin-top-3">
            <i class="fa fa-check-circle fa-3x fa-fw" aria-hidden="true"></i>
            <br><br>
            The code was verified
          </h3>
        </div>
      </div>
    </div>

    <!-- Modal shown when INVALID the access plan -->
    <div class="nhsuk-modal" id="invalid_access_plan_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
      <div class="nhsuk-modal__dialog" role="document">
        <div class="nhsuk-modal__content nhsuk-u-text-align-center">
          <h3 class="nhsuk-heading-m nhsuk-u-margin-top-3">
            <i class="fa fa-meh-o fa-3x fa-fw" aria-hidden="true"></i>
            <br><br>
            You have entered an invalid access code
          </h3>
          <a class="nhsuk-button" href="#" data-dismiss="modal" aria-label="Close">Try again</a>
        </div>
      </div>
    </div>

    <!-- Modal shown when PROFESSIONAL must login/register first -->
    <div class="nhsuk-modal" id="must_register_or_login_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
      <div class="nhsuk-modal__dialog" role="document">
        <div class="nhsuk-modal__content nhsuk-u-text-align-center">
          <h3 class="nhsuk-heading-m nhsuk-u-margin-top-3">
            <i class="fa fa-meh-o fa-3x fa-fw" aria-hidden="true"></i>
            <br><br>
            You must register or login as a Professional before using this access code.
          </h3>
          <a class="nhsuk-button" href="#" data-dismiss="modal" aria-label="Close">Close</a>
        </div>
      </div>
    </div>

    <!-- Modal shown when INVALID but might be a coupon -->
    <div class="nhsuk-modal" id="invalid_access_plan_maybe_coupon_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
      <div class="nhsuk-modal__dialog" role="document">
        <div class="nhsuk-modal__content nhsuk-u-text-align-center">
          <h3 class="nhsuk-heading-m nhsuk-u-margin-top-3">
            <i class="fa fa-meh-o fa-3x fa-fw" aria-hidden="true"></i>
            <br><br>
            Oops, it looks like you have entered a discount code. Please 'buy' a course and enter your discount code at check out.
          </h3>
          <a class="nhsuk-button" href="#" data-dismiss="modal" aria-label="Close">Try again</a>
        </div>
      </div>
    </div>

  </main>
</div>

<?php include("./accessibility/includes/footer_bootstrap.php"); // <-- Similarly, replace or remove if not using Bootstrap. ?>
