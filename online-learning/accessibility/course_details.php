<?php include("includes/header_bootstrap.php"); ?>
    <style>
        .sticky-div {
            background-color: #fff;
            position: sticky;
            top: 0px;
            padding: 10px 0px;
            z-index: 10000;
        }
        .row_free {
            display: flex;
            width: 100%;
        }

        .hlf_ffty {
            flex: 1; /* Make sure both .hlf_ffty divs take equal space */
            display: flex;
            flex-direction: column;
        }
        .no_right{
           border-right: none!important;
        } 
        .no_left{
            border-left: none!important;
        }

        .rem_free .highlight-section {
            /* padding-left: 0px !important; */
            flex: 1;
            border: 1px solid rgb(216, 221, 224);
            padding: 32px;
            margin-bottom: 20px;
/*            box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);*/
        }
        .rem_free .highlight-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #000000;
        }
        .rem_free .highlight-link {
            text-decoration: none;
            color: #005eb8;
            /*background-color: #198754;*/
           /* padding: 10px 15px;
            border-radius: 5px;*/
            font-size: 19px;
        }
        /*.rem_free .highlight-link:hover {
            background-color: #155d40;
        }*/
        .rem_free .highlight-link i{
            color: white;
            background-color: #198754;
        }

        /* Add the border between the two .hlf_ffty divs */
        .row_free::after {
            content: '';
            position: absolute;
            top: 25px;
            bottom: 0;
            left: 50%;
            width: 1px;
            background-color: rgb(216, 221, 224);
            transform: translateX(-50%);
            height: 65%;
        }

        .nhsuk-list--bullet, ul {
            list-style-type: none;
            padding-left: 0px;
        }
        
        .card{
            background: #f0f4f500;
            width: 100%;
        }

        .accordion-item {
            background-color: #ffffff2e;
            border:none;
        }

        .accordion {
            --bs-accordion-bg: #ffffff00;
        }

        .accordion-buttons {
    position: relative;
    display: flex
;
    align-items: center;
    width: 100%;
    padding: var(--bs-accordion-btn-padding-y) var(--bs-accordion-btn-padding-x);
    font-size: 19px;
    font-weight: 400;
    color: var(--bs-accordion-btn-color);
    text-align: left;
    border: 0;
    border-radius: 0;
    overflow-anchor: none;
    transition: var(--bs-accordion-transition);
}


        .accordion-buttons::after {
            width: 0px;
            height: 0px;
        }

        .nhsuk-card {
            background: none;
            border: none;
        }

        .accordion-buttons:not(.collapsed) {
            box-shadow: unset;
        }

        .accordion-item .accordion-collapse{
            margin-left: 15px;
        }
        .nhsuk-list--bullet a {
            text-decoration: none; 
        }

        .nhsuk-tag {
            font-size: 16px;
            font-size: .7rem;
            line-height: 1;
            margin-left: 5px;
        }
         .accordion{
             --bs-accordion-btn-padding-y: 0.4rem;
         }

         .det_category_new{
            text-align: left;
            font: normal normal 600 24px/56px Noto Sans;
            letter-spacing: 0px;
            color: #4C6272;
            opacity: 1;
         }
         .det_course_title{
            text-align: left;
            font: normal normal bold 48px/62px Noto Sans;
            letter-spacing: 0px;
            color: #212B32;
            opacity: 1;
         }
         #course-details .row{
           /*--bs-gutter-x: 0px;*/
           /* padding-left: 0px;
            margin-left: 0px;*/
         }

         .related_translationsh2{
            text-align: left;
            font: normal normal 600 22px/30px Noto Sans;
            letter-spacing: 0px;
            color: #212B32;
            opacity: 1;
         }

         .bstart_courselink{
            text-align: left;
            font: normal normal 600 19px/28px Noto Sans;
            letter-spacing: 0px;
            color: #005EB8;
            opacity: 1;
         }
         .outfreeh4{
            text-align: left;
            font: normal normal 600 24px/33px Noto Sans;
            letter-spacing: 0px;
            color: #212B32;
            opacity: 1;
         }

         .outfreepara{
           text-align: left;
            font: normal normal normal 16px/24px Arial;
            letter-spacing: 0px;
            color: #212B32;
            opacity: 1;
         }
         ./*freoutcard .row{
           --bs-gutter-x: 0rem;
         }
         .nopadd .rowclass{
           --bs-gutter-x: 0rem;
         }*/

         .nhsuk-card__content {
            padding: 0px;
            text-align: left;
            font: normal normal normal 19px/24px Arial;
            letter-spacing: 0px;
            color: #005EB8;
            opacity: 1;
         }

         hr.accessbility_styles{
            border: 1px solid #a9a9a9;
            width: 99%;
        }


          .course-titlehr{
        margin-bottom:10px;
    }

        .nhsuk-card--feature {
        margin-top: 20px;
    }

    .nhsuk-card__content {
        padding: 0px;
    }

    .rowclass{
        display: flex;
        flex-wrap: wrap;
    }

    #accordion-modules a{
        text-decoration: none;
    }
    @media screen and (min-width: 768px) {
        .course-hd-img{
          /* padding: 300px;*/
        }
    }


    </style>
    <div id="appwrapper">
    <div class="nhsuk-width-container d-md-none p-2 sticky-div" id="scroll-home">
            <p class="fw-bold text-center" data-translate>Navigate this page:</p>
            <div class="d-flex gap-2 justify-content-evenly mx-auto">
                <a class="nav-link text-primary text-decoration-underline" href="#about" role="button" data-translate>About</a>
                <a class="nav-link text-primary text-decoration-underline" href="#modules" role="button" data-translate>Modules</a>
                <a class="nav-link text-primary text-decoration-underline" href="#free-access" role="button" data-translate>Free Access</a>
                <a class="nav-link text-primary text-decoration-underline" href="#related" role="button" data-translate>Related</a>
            </div>
    </div>
     <div v-if="!course.id" class="text-center p-5">
        <!-- You can style this however you like -->
        <div class="spinner-border" role="status"></div>
        <p class="mt-3">Loading...</p>
      </div>

      <!-- When loading is done, show the main page -->
      <div v-else id="course-details" class="min-vh-100">
        <div class="nhsuk-width-container overflow-x-hidden0">

            <div class="nhsuk-grid-row">
                <div class="col-12 col-md-12 col-xl-12 px-0 course-hd-img"
                         :style="{'background-image' : `url(${course.image})`,'border-bottom' : `10px solid ${course.colour}`,'border-radius':'unset','border-right': '1px solid #d8dde0',' background-position': 'center', 'background-repeat': 'no-repeat','background-size': 'cover','height':'400px'}">
                </div>
            </div>

            <div class="nhsuk-grid-row course_title_row">
                    <h1 class="nhsuk-heading-l">
                      {{course.title}}<br>
                      <span class="nhsuk-caption-l">{{course?.category_new?.name}}</span>
                    </h1>
            </div>
            
            <!-- <div class="nhsuk-grid-row course_title_row">
                <div class="col-12 col-md-8 col-xl-9"  >
                    <div class="">
                        
                        <h1 class="det_course_title">{{course.title}}</h1>
                        
                    </div>
                </div>

                <div class="col-12 col-md-4 col-xl-3"  >
                </div>
            </div>
            <div class="nhsuk-grid-row"><h2 class="pt-4 det_category_new"  data-translate>
                            {{course.category_new.name}}</h2></div> -->
            <div class="nhsuk-grid-row features-row">
                <ul class="list-inline banner-details">
                            


                             <li  class="list-inline-item">
                                <svg xmlns="http://www.w3.org/2000/svg" width="1.5em" height="1.5em"
                                     fill="currentColor"
                                     viewBox="0 0 16 16" class="bi bi-chat-left-text-fill"
                                     style="font-size: 14px;" >
                                    <path d="M0 2a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H4.414a1 1 0 0 0-.707.293L.854 15.146A.5.5 0 0 1 0 14.793zm3.5 1a.5.5 0 0 0 0 1h9a.5.5 0 0 0 0-1zm0 2.5a.5.5 0 0 0 0 1h9a.5.5 0 0 0 0-1zm0 2.5a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1z"></path>
                                </svg>
                                <span
                                        class="link-underline"
                                        style="color: inherit;margin-left: 0px;" data-translate>{{course?.language?.name}}</span>
                            </li>
                            <li class="list-inline-item mt-4"><i class="fa fa-list-alt"
                                                                 style="font-size: 1.1rem;"
                                                                 ></i>{{course.module_count}}
                                <span
                                        class="link-underline"
                                        style="color: inherit;" data-translate>Modules</span>
                            </li>
                            <li v-if="course.has_audio" class="list-inline-item">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                     stroke-width="1.5"
                                     stroke="currentColor" class="size-6" style="width: 1.5rem;height: 1.5rem"
                                     >
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                          d="M19.114 5.636a9 9 0 0 1 0 12.728M16.463 8.288a5.25 5.25 0 0 1 0 7.424M6.75 8.25l4.72-4.72a.75.75 0 0 1 1.28.53v15.88a.75.75 0 0 1-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.009 9.009 0 0 1 2.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75Z"/>
                                </svg>
                                <span
                                        class="link-underline" 
                                        style="color: inherit;" data-translate>Voiceover</span>
                            </li>
                        </ul>
            </div>
        </div>
        
        <div class="nhsuk-width-container nopadd" data-bs-spy="scroll" data-bs-target="#scroll-home" data-bs-offset="0">
            <div class="nhsuk-grid-row rowclass">
                
          
                <div class="col-xs-12 col-md-7 col-lg-6 col-xl-6 col-xxl-9" id="about" style="border-radius: 2px;margin-top: 14px;">
                 <hr class=" accessbility_styles course-titlehr">
                 <!-- convert this part to nhsuk styles-->
                 <div class="nhsuk-card nhsuk-card--feature" id="modules">
                        <div class="nhsuk-card__content">

                             <a v-if="account && (course.completion_percentage == 0 && course.bought )" :href="redirectLink(course.resume_link)"
                                type="submit"
                                class="nhsuk-button nhsuk-button--success" style="background: #007F3B 0% 0% no-repeat padding-box;border-radius: 26px;">
                                start course
                            </a>

                            <a v-else-if="account && (course.completion_percentage < 100 && course.completion_percentage > 0)" :href="redirectLink(course.resume_link)" type="submit"
                                class="nhsuk-button nhsuk-button--success" style="background: #007F3B 0% 0% no-repeat padding-box;border-radius: 26px;" data-translate> Resume course</a> 
                            <a v-else-if="account && (course.completion_percentage >= 100 )" :href="redirectLink(course.resume_link)" type="submit"
                                class="nhsuk-button nhsuk-button--success" style="background: rgb(107 111 109) 0% 0% no-repeat padding-box;border-radius: 26px;" data-translate> Resume course</a> 
                                                    <!--<a v-else-if=" course.completion_percentage >= 100" :href="redirectLink(course.href)">View course</a>--> 
                            <a v-if="account && (course.completion_percentage >= 100 && course.enable_download_certificate === 'yes')" target="_blank" :href="'/online-learning/views/pdf_create_certificate.php?certificate='+course.username_id+'&uid='+encodeduid" type="submit"
                                class="nhsuk-button nhsuk-button--success" style="background: #007F3B 0% 0% no-repeat padding-box;border-radius: 26px;" data-translate> Download certificate</a>


                            <!-- Course Modules Header with Toggle -->
                            <h2 class="nhsuk-heading-m d-flex justify-content-left align-items-left" @click="showModules = !showModules" id="#modulesshower" style="cursor: pointer;text-align: left;font: normal normal normal 19px/24px Arial;letter-spacing: 0px;color: #005EB8;opacity: 1;">
                                <a href="#modulesshower" class="nhsuk-link" style="text-decoration:none;">
                                <span>
                                    <!-- Toggle SVG Icons -->
                                    <svg v-if="showModules" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                        <path d="M1.5 8a.5.5 0 0 1 .5-.5h12a.5.5 0 0 1 0 1h-12a.5.5 0 0 1-.5-.5z"/>
                                    </svg>
                                    <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M7.00003 8.5C6.59557 8.5 6.23093 8.74364 6.07615 9.11732C5.92137 9.49099 6.00692 9.92111 6.29292 10.2071L11.2929 15.2071C11.6834 15.5976 12.3166 15.5976 12.7071 15.2071L17.7071 10.2071C17.9931 9.92111 18.0787 9.49099 17.9239 9.11732C17.7691 8.74364 17.4045 8.5 17 8.5H7.00003Z" fill="#000000"/>
                                    </svg>
                                </span>
                                <span data-translate>Course Modules</span>
                               </a>
                            </h2>

                            <!-- Modules List -->
                            <div v-show="showModules">
                                <div class="accordion" role="tablist" id="accordion-modules" style="border-radius: 2px;border-left: 3px solid rgb(216, 221, 224);margin-bottom: 14px;">
                                    <div class="accordion-item" v-for="(module, module_index) in course.modules" :key="module.id">
                                        <!-- Accordion Header -->
                                        <h3 class="accordion-header" role="tab">
                                            <a  :href="`#moduleid-${module.id}`"
                                                class="accordion-buttons d-flex justify-content-left align-items-left nhsuk-link"
                                                @click="toggleModule(module.id)"
                                                :aria-expanded="module.isOpen"
                                                :aria-controls="`module-${module.id}`"
                                                :id="`moduleid-${module.id}`"
                                            >
                                                {{ module.title }}
                                                <!-- Toggle SVG Icons for Individual Module -->
                                                <span>
                                                    <svg v-if="module.isOpen" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                        <path d="M1.5 8a.5.5 0 0 1 .5-.5h12a.5.5 0 0 1 0 1h-12a.5.5 0 0 1-.5-.5z"/>
                                                    </svg>
                                                    <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M7.00003 8.5C6.59557 8.5 6.23093 8.74364 6.07615 9.11732C5.92137 9.49099 6.00692 9.92111 6.29292 10.2071L11.2929 15.2071C11.6834 15.5976 12.3166 15.5976 12.7071 15.2071L17.7071 10.2071C17.9931 9.92111 18.0787 9.49099 17.9239 9.11732C17.7691 8.74364 17.4045 8.5 17 8.5H7.00003Z" fill="#000000"/>
                                                    </svg>
                                                </span>
                                            </a>
                                        </h3>
                                        <!-- Accordion Content -->
                                        <div v-show="module.isOpen" class="accordion-collapse">
                                            <div class="accordion-body">
                                                <ul class="nhsuk-list nhsuk-list--bullet">
                                                 <li v-for="(unit, unit_index) in module.units" :key="unit.id">
                                                  <template v-if="'#'!=showUnitLink(unit, unit_index, module_index) &&(course.bought || unit.trail == 'yes' || course.next_unit_visibility=='all_visible')">
                                                    <!-- Only show as clickable link if user has unlocked -->
                                                    <a :href="redirectLink(showUnitLink(unit, unit_index, module_index))" class="nhsuk-link">
                                                      {{ unit.title }} <span v-if="unit.completed_date" class="nhsuk-tag nhsuk-tag--green" data-translate>Completed</span><span v-else class="nhsuk-tag nhsuk-tag--grey" data-translate>Not started</span>
                                                    </a>
                                                  </template>
                                                  <template v-else>
                                                    <!-- Just display text or show a locked icon, etc. -->
                                                    <span class="locked-module nhsuk-link">
                                                      {{ unit.title }}
                                                    </span>
                                                  </template>
                                                    
                                                        
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="py-4 pr-1 my-4">
                        <h4 class="fw-bold"
                            style="text-align: left;font: normal normal bold 32px/43px Noto Sans;letter-spacing: 0px;color: #212B32;opacity: 1;" data-translate>About this course</h4>
                        <div class="fs-6 course_description" style="text-align: left;font: normal normal normal 19px/24px Arial;letter-spacing: 0px;color: #212B32;opacity: 1;"
                             v-html="course.description"></div>

                             <!--  -->
                             <a v-if="account && (course.completion_percentage == 0 && course.bought)" :href="redirectLink(course.resume_link)"
                                type="submit"
                                class="nhsuk-button nhsuk-button--success" style="background: #007F3B 0% 0% no-repeat padding-box;border-radius: 26px;">
                                start course
                            </a>

                            <a v-else-if="account && (course.completion_percentage < 100 && course.completion_percentage > 0)" :href="redirectLink(course.resume_link)" type="submit"
                                class="nhsuk-button nhsuk-button--success" style="background: #007F3B 0% 0% no-repeat padding-box;border-radius: 26px;" data-translate> Resume course</a> 
                            <a v-else-if="account && (course.completion_percentage >= 100 )" :href="redirectLink(course.resume_link)" type="submit"
                                class="nhsuk-button nhsuk-button--success" style="background: rgb(107 111 109) 0% 0% no-repeat padding-box;border-radius: 26px;" data-translate> Resume course</a> 
                                                    <!--<a v-else-if=" course.completion_percentage >= 100" :href="redirectLink(course.href)">View course</a>--> 
                            <a v-if="account && (course.completion_percentage >= 100 && course.enable_download_certificate === 'yes')" target="_blank" :href="'/online-learning/views/pdf_create_certificate.php?certificate='+course.username_id+'&uid='+encodeduid" type="submit"
                                class="nhsuk-button nhsuk-button--success" style="background: #007F3B 0% 0% no-repeat padding-box;border-radius: 26px;" data-translate> Download certificate</a>

                             <!--  -->
                    </div>
                </div>
                <div class="col-sm-12 col-md-12 col-lg-4 col-xxl-3" id="free-access" style="border-radius: 2px;margin-top: 14px;padding-left: 20px;">
                    <hr class=" accessbility_styles course-titlehr">
                    <div class="third-col-sidebar">
                        <?php if ('on'!=$ols_preferences->hide_free_access_checker) {?>
                        <div class="card"
                             style="border-radius: 2px;border-bottom: 3px solid #d8dde0;margin-bottom: 14px;"
                             v-if="!course.bought && !(freein_uk_plan && paying_plan)">
                            <div class="card-body d-block" style="margin-top: 0px;padding-right: 16px;">
                                <h4 class="fw-bold card-title"
                                    style="font-family: 'Noto Sans', sans-serif;font-size: 18px;" data-translate>Free for
                                    me?</h4>
                                <h6 class="fw-normal text-muted card-subtitle mb-2" style="font-size: 15px;" data-translate> Find out if you are in a pre-paid area:</h6>
                                <a href="/<?php echo $ol_rl; ?>/free-access-checker" 
                                        data-translate>Free access checker
                                </a>
                            </div>
                        </div>
                        <?php } ?>

                         <div class="card freoutcard" v-if="!course.bought && (freein_uk_plan && paying_plan)" style="border: none;">
                            <div class="nhsuk-grid-row">
                               <!-- <div class="row"> -->
                                    <div class="">
                                        <div class="highlight-section no_right">
                                            <h4 class="highlight-title outfreeh4"><strong data-translate>Free</strong> <br> <span data-translate>in the UK:</span></h4>
                                            <p class="outfreepara" data-translate>This course is free to UK residents.</p>
                                            <a :href="`/<?php echo $ol_rl; ?>/register_new?plan=${freein_uk_plan.username_id}&redirect=successiful-reg`" class="highlight-link mt-1 d-inline-block bstart_courselink"><svg class="nhsuk-icon nhsuk-icon__arrow-right-circle" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" aria-hidden="true" width="36" height="36">
                                                  <path d="M0 0h24v24H0z" fill="none"></path>
                                                  <path d="M12 2a10 10 0 0 0-9.95 9h11.64L9.74 7.05a1 1 0 0 1 1.41-1.41l5.66 5.65a1 1 0 0 1 0 1.42l-5.66 5.65a1 1 0 0 1-1.41 0 1 1 0 0 1 0-1.41L13.69 13H2.05A10 10 0 1 0 12 2z"></path>
                                                </svg>
                                                <span class="nhsuk-action-link__text" data-translate>Start Course</span></a>
                                        </div>
                                    </div>
                                    <div class="">
                                        <div class="highlight-section no_left">
                                            <h4 class="highlight-title outfreeh4" data-translate>Outside the UK?</h4>
                                            <a :href="`/<?php echo $ol_rl; ?>/register_new?plan=${paying_plan.username_id}&redirect=successiful-reg`" class="highlight-link mt-1 d-inline-block bstart_courselink"><svg class="nhsuk-icon nhsuk-icon__arrow-right-circle" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" aria-hidden="true" width="36" height="36">
                                              <path d="M0 0h24v24H0z" fill="none"></path>
                                              <path d="M12 2a10 10 0 0 0-9.95 9h11.64L9.74 7.05a1 1 0 0 1 1.41-1.41l5.66 5.65a1 1 0 0 1 0 1.42l-5.66 5.65a1 1 0 0 1-1.41 0 1 1 0 0 1 0-1.41L13.69 13H2.05A10 10 0 1 0 12 2z"></path>
                                            </svg>
                                            <span class="nhsuk-action-link__text" data-translate>Buy Course</span></a>
                                        </div>
                                    </div>
                                <!-- </div>   -->
                            </div>
                        </div>
                        <?php if ('on'!=$ols_preferences->hide_free_access_checker) {?>
                        <div class="card" style="border-radius: 2px;border-bottom: 3px solid #d8dde0;"
                             v-if="!course.bought">
                            <div class="card-body d-block pe-4">
                                <h6 class="fw-normal text-muted card-subtitle mb-2" style="font-size: 15px;" data-translate>Apply Access
                                    Code</h6>
                                <div class="mt-2 pt-2">

                                     <form class="d-sm-inline-block me-auto my-2 my-md-0 mw-100 navbar-search" style="width: 100%;" role="search">
                                          <div class="from-group">
                                            <div v-if="access_code_error!= ''" style="text-align: left;font: normal normal bold 19px/23px Arial;letter-spacing: 0px;color: #D5281A;opacity: 1;">{{access_code_error}}</div>
                                            <input
                                              :class="[
                                              'nhsuk-input',
                                              (access_code_error != '') ? 'nhsuk-input--error' : '']"
                                              type="text"
                                              v-model="code"
                                              aria-label="Access Code"
                                            >
                                            <button
                                              class="btn btn-success py-2 btn-styles"
                                              role="button"
                                              @click.prevent="verifyCode"
                                              aria-label="Apply Access Code"
                                              style="margin-top: 5px;width: 50%;background: #4c6272;;"
                                              data-translate
                                            >
                                              Apply Code
                                            </button>
                                          </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <?php } ?>
                        <div class="card" style="border-radius: 2px;border-bottom: 3px solid #d8dde0 ;border: none;"
                             v-if="!course.bought && !(freein_uk_plan && paying_plan)"
                             v-for="plan in course.plans" :key="plan.id">
                            <div class="card-body" v-if="Number(plan.price) > 0">
                                <h4 class="fw-bold card-title h5" data-translate>
                                    {{course.plans.length == 1 ?
                                            'Buy Course'
                                            :
                                            'Outside the UK?'}}</h4>
                                <h6 v-if="course.plans.length > 1"
                                    class="fw-bold text-muted card-subtitle mb-2 d-flex gap-1">

                                    <span class="fs-6" data-translate>Buy Course</span>
                                </h6>
                                <h6 class="fw-bold card-subtitle mb-2 d-flex gap-1" style=" margin-top: 15px;">
                                    <span class="fs-2">£ {{plan.price}}</span>
                                    <!-- <span class="ms-2 small fw-light" data-translate>Coupons* can be applied before checkout</span> -->
                                </h6>
                                <div class="d-grid">
                                    <a class="highlight-link mt-1 d-inline-block bstart_courselink"
                                       :href="`/<?php echo $ol_rl; ?>/register_new?plan=${plan.username_id}&redirect=successiful-reg`" type="button"
                                   >
                                        <svg class="nhsuk-icon nhsuk-icon__arrow-right-circle" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" aria-hidden="true" width="36" height="36">
                                              <path d="M0 0h24v24H0z" fill="none"></path>
                                              <path d="M12 2a10 10 0 0 0-9.95 9h11.64L9.74 7.05a1 1 0 0 1 1.41-1.41l5.66 5.65a1 1 0 0 1 0 1.42l-5.66 5.65a1 1 0 0 1-1.41 0 1 1 0 0 1 0-1.41L13.69 13H2.05A10 10 0 1 0 12 2z"></path>
                                            </svg>
                                        <span class="nhsuk-action-link__text"  data-translate>Buy Course</span>
                                    </a>
                                </div>
                                <!-- <p class="text-mute mt-2" style="font-size: 0.6rem" data-translate>*Purchased via invoice from <a
                                            href="https://solihullapproachparenting.com/order">https://solihullapproachparenting.com/order</a>
                                </p> -->
                            </div>
                            <div class="card-body" v-else-if="plan.access_plan_type == '3'">UK residents.
                                <h4 class="fw-bold card-title h5">In the UK ?</h4>
                                <h6 class="fw-bold text-muted card-subtitle mb-2 d-flex gap-1">

                                    <span class="fs-6" data-translate>Free for all UK residents.</span>
                                </h6>
                                <div class="d-grid">
                                    <a class="btn btn-success fw-normal fs-6 rounded-pill text-white"
                                       :href="`/<?php echo $ol_rl; ?>/register_new?plan=${plan.username_id}&redirect=successiful-reg`" type="button" role="button"
                                    data-translate>
                                        Get started
                                    </a>
                                </div>

                            </div>
                            <div class="card-body"
                                 v-else-if="Number(plan.price) == 0 && plan.challenge_questions.length == 0">
                                <h4 class="fw-bold card-title h5">Free Course</h4>
                                <h6 class="fw-bold text-muted card-subtitle mb-2 d-flex gap-1">

                                    <span class="fs-6" data-translate>Free for all</span>
                                </h6>
                                <div class="d-grid">
                                    <a class="btn btn-success fw-normal fs-6 rounded-pill text-white"
                                       :href="`/<?php echo $ol_rl; ?>/register_new?plan=${plan.username_id}&redirect=successiful-reg`" type="button" role="button" data-translate
                                    >
                                        Get started
                                    </a>
                                </div>

                            </div>
                              
                            
                        </div>
                    </div>
                       <section class="related-courses" id="related">
                        <h2 class="nhsuk-heading-m related_translationsh2"  data-translate>Related courses</h2>
                        <ul class="nhsuk-list nhsuk-list--bullet">
                            <li v-for="rCourse in relatedCourses" :key="rCourse.id"><a :href="redirectLink(rCourse.href)">{{rCourse.title}}</a></li>
                        </ul>
                    </section>

                    <section v-if="course.other_languages?.length>0" class="related-courses" id="related">
                        <h2 class="nhsuk-heading-m related_translationsh2"  data-translate>Translations for this course</h2>
                        <ul class="nhsuk-list nhsuk-list--bullet">
                            <li v-for="rCourse in course.other_languages" :key="rCourse.id"><a :href="redirectLink(rCourse.href)">{{rCourse.title}}</a></li>
                        </ul>
                    </section>
                </div>
            </div>
        </div>
    </div>
    </div>
    <script>
        Vue.createApp({
            data: () => ({
                courses: [],
                code: null,
                ePlans: [],
                user: user,
                auth: null,
                email: "",
                postcode: "",
                freeAccessCodeError: "",
                course: {
                    id: null,
                    category_new: {name: null},
                    language: {name: null},
                    description: null,
                    plans: []
                },
                base: window.location.origin,
                id: '<?= $slugs[3]?>',
                checkingEligibility: false,
                encodeduid:'',
                showModules: true, // Toggle Course Modules
                access_code_error:"",
                loading: true, 

            }),
            async created() {
                this.db = new IndexDB();
                this.ready = true;
                this.courses = await this.db.getDataFromDb('courses');
                this.categories = await this.db.getDataFromDb('categories');
                await this.getData();
                await this.getCoursesData();

                this.loading = false;

                // this.$nextTick(() => { 
                //       // e.g. translatePage(this.course.language.id);
                //     });
            },
            async mounted() {
                this.auth = auth;
                this.encodeduid=(auth?.user?.encoded_id)
                console.log('auth user')
                console.log(auth?.user)
                this.$nextTick(() => {
                    console.log('mounted loading')
                    
                });

                const data = JSON.parse(decodeFromBase64(window.localStorage.getItem('eligibility') || "{}"));
                if (data?.postcode) {
                    this.postcode = data.postcode;
                }
                if (data?.email) {
                    this.email = data.email;
                }
            },
            computed: {
                account: function () {
                    return (this.auth !== null && this.auth.user !== undefined && this.auth.user !== null);
                },
                onLocal() {
                    const course = this.courses.filter((course) => course.id === this.id);
                    return course[0] !== undefined ? course[0] : false;
                },
                relatedCourses() {
                  if (
                    this.course.id &&
                    this.course.category_new &&
                    this.course.category_new.id &&
                    this.course.language &&
                    this.course.language.code
                  ) {
                    return this.courses.filter((course) => {
                      return (
                        course.category_new &&
                        course.language &&
                        course.category_new.id === this.course.category_new.id &&
                        course.language.code === 'eng' &&
                        course.id !== this.course.id
                      );
                    });
                  }
                  return [];
                }
                ,
                plan() {
                    return this.ePlans.length ? this.ePlans[0] : {id: null, sponsor: {company_name: ''}};
                },
                paying_plan(){
                    const plans = this.course.plans?.filter((plan) => Number(plan.price) > 0);
                    console.log("paying_plan")
                    console.log(plans)
                    return plans?.[0] !== undefined ? plans[0] : false;
                },
                freein_uk_plan(){
                    const plans = this.course.plans?.filter((plan) => plan.access_plan_type == '3');
                    console.log("freein_uk_plan")
                    console.log(plans)
                    return plans?.[0] !== undefined ? plans[0] : false;
                }
            },
            methods: {
                 toggleModule(moduleId) {
                    this.course.modules = this.course.modules.map((moduule) => {
                        if (moduule.id === moduleId) {
                            moduule.isOpen = !moduule.isOpen; // Toggle open/close state
                        } else {
                            moduule.isOpen = false; // Collapse other modules
                        }
                        return moduule;
                    });
                },
                redirectLink(link) {
                    const currentUrl = window.location.href;
                    //console.log(currentUrl)
                    // Check if the current URL contains '/online-learning-dev'
                    if (currentUrl.includes('/online-learning-dev')) {
                        // Replace '/online-learning-dev' with '/online-learning'
                        link = link.replace('/online-learning', '/online-learning-dev');
                        //window.location.href = updatedUrl;
                    }
                    // Otherwise, redirect to the provided href
                    return link;
                },
               showUnitLink(unit, unitIndex, moduleIndex) {
              // If the course setting is "all_visible," then all units are accessible:
              if (this.course.next_unit_visibility === 'all_visible') {
                return unit.href;
              }

              // If this unit is already completed, always allow access:
              if (unit.completed_date) {
                return unit.href;
              }

              // Always allow the very first unit (first module, first unit):
              if (moduleIndex === 0 && unitIndex === 0) {
                return unit.href;
              }

              // Otherwise, require that the previous unit is completed:
              if (unitIndex > 0) {
                // Check the *previous* unit in the *same* module:
                const prevUnit = this.course.modules[moduleIndex].units[unitIndex - 1];
                if (prevUnit && prevUnit.completed_date) {
                  return unit.href;
                }
              } else if (moduleIndex > 0) {
                // If it's the *first* unit of a *later* module, check the final unit of the previous module:
                const prevModule = this.course.modules[moduleIndex - 1];
                const lastUnit = prevModule.units[prevModule.units.length - 1];
                if (lastUnit && lastUnit.completed_date) {
                  return unit.href;
                }
              }

              // If none of the above conditions are met, return "#" to lock the link:
              return "#";
            },
                closeEligibility() {
                    this.ePlans = [];
                },
                hexToRgb(hex) {
                    // Remove the hash at the start if it's there
                    hex = hex.replace(/^#/, '');

                    // Parse r, g, b values
                    let bigint = parseInt(hex, 16);
                    let r = (bigint >> 16) & 255;
                    let g = (bigint >> 8) & 255;
                    let b = bigint & 255;

                    return [r, g, b];
                },

                getLuminance(r, g, b) {
                    // Convert sRGB to linear RGB
                    r = r / 255;
                    g = g / 255;
                    b = b / 255;

                    r = (r <= 0.03928) ? r / 12.92 : Math.pow((r + 0.055) / 1.055, 2.4);
                    g = (g <= 0.03928) ? g / 12.92 : Math.pow((g + 0.055) / 1.055, 2.4);
                    b = (b <= 0.03928) ? b / 12.92 : Math.pow((b + 0.055) / 1.055, 2.4);

                    // Calculate luminance
                    return (0.2126 * r) + (0.7152 * g) + (0.0722 * b);
                },
                getContrastingColor(hex) {
                    let [r, g, b] = this.hexToRgb(hex);
                    let luminance = this.getLuminance(r, g, b);

                    // If luminance is greater than 0.5, return black, otherwise white
                    return (luminance > 0.5) ? '#000000' : '#FFFFFF';
                },
                verifyCode() {
                    let vm=this
                    if (this.code.trim().length > 0) {
                        const raw = JSON.stringify({
                            "must_log_in": "no",
                            "code": this.code
                        });
                        const myHeaders = new Headers();
                        myHeaders.append("X-Api-Key", apiKey);
                        if (token) {
                            myHeaders.append("Authorization", `Bearer ${token}`);
                        }
                        const requestOptions = {
                            method: "POST",
                            body: raw,
                            headers: myHeaders,
                            redirect: "follow"
                        };

                        fetch(`${this.base}/admin/api/v2/access_codes/verify`, requestOptions)
                            .then((response) => response.json())
                            .then((result) => {
                                if (result.success) {
                                    vm.access_code_error=""
                                    window.sessionStorage.removeItem('plan');
                                    window.sessionStorage.removeItem('coupon');
                                    window.location.replace(`/<?php echo $ol_rl; ?>/register_new?plan=${result.data.plan.username_id}&lang=${result.data.plan.plan.access_plan_language}&redirect=successiful-reg`)
                                } else {
                                    // Swal.fire({
                                    //     icon: 'error',
                                    //     title: result.message
                                    // });
                                    console.log(result.message)
                                    vm.access_code_error=result.message

                                }
                            })
                            .catch((error) => console.error(error))
                    }
                    return false;
                },
                async getData() {
                    //if (!this.onLocal) {
                    const raw = JSON.stringify({
                        "categories": true,
                    });
                    const myHeaders = new Headers();
                    myHeaders.append("X-Api-Key", apiKey);
                    if (token !== null) {
                        myHeaders.append("Authorization", `
                                    Bearer ${token}`);
                    }
                    const requestOptions = {
                        method: "POST",
                        body: raw,
                        headers: myHeaders,
                        redirect: "follow"
                    };

                    //fetch(`${this.base} / admin / api / v2 / online_courses /${this.id}`, requestOptions)
                    fetch(`${this.base}/admin/api/v2/online_courses/${this.id}`, requestOptions)
                        .then((response) => response.json())
                        .then((result) => {
                            this.course = result.data;
                            this.loading = false;
                            if (!this.user) {
                                if (this.course.course_external_url!="") {
                                    window.location.replace(this.course.course_external_url)
                                }
                            }
                            setTimeout(() => {
                               translatePage(this.course.language.id);
                            }, 1000);

                        })
                        .catch((error) => console.error(error))
                    // }
                },
                async getCoursesData() {
                    if (this.courses.length === 0 || this.db.getReset() === 1) {
                        const raw = JSON.stringify({
                            "featured": true,
                            "categories": true,
                            "inProgress": false,
                            "recommended": false,
                            "search": "",
                            "published": true,
                            "languages": true
                        });
                        const myHeaders = new Headers();
                        myHeaders.append("X-Api-Key", apiKey);
                        if (token) {
                            myHeaders.append("Authorization", `
                                    Bearer ${token}`);
                        }
                        const requestOptions = {
                            method: "POST",
                            body: raw,
                            headers: myHeaders,
                            redirect: "follow"
                        };

                        //fetch(`${this.base} / admin / api / v2 / online_courses`, requestOptions)
                        fetch(`${this.base}/admin/api/v2/online_courses`, requestOptions)
                            .then((response) => response.json())
                            .then((result) => {
                                this.courses = result.courses;
                                this.db.addToDb('courses', result.courses);
                                this.db.clearReset();
                            })
                            .catch((error) => console.error(error))
                    }
                    return this.courses;
                },
            }
        }).mount("#appwrapper");
    </script>

<?php include("includes/footer_bootstrap.php"); ?>