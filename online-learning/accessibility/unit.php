<?php include("includes/header_bootstrap.php"); ?>
<?php include("includes/form_field.php"); ?>

<style type="text/css">

    .nhsuk-loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(243, 242, 241, 0.9);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }
    .nhsuk-loading-spinner {
      width: 3rem;
      height: 3rem;
      border: 0.4rem solid #003087;
      border-top-color: transparent;
      border-radius: 50%;
      animation: nhsuk-spinner-rotate 0.8s linear infinite;
    }
    @keyframes nhsuk-spinner-rotate {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
</style>

    <div id="unit-view">
        <div class="nhsuk-width-container py-5">
            <nav aria-label="breadcrumb" style="--bs-breadcrumb-divider: '>';">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item" v-if="course?.title"><a
                                :href="`/<?php echo $ol_rl; ?>/course/${course.id}`">{{course?.title}}</a>
                    </li>
                    <li class="breadcrumb-item placeholder" v-else>
                        <a class="placeholder-glow w-25" href="#">{{course?.title}}</a>
                    </li>
                    <li class="breadcrumb-item active">{{currentModule?.title}}</li>
                    <li class="breadcrumb-item active" v-if="uid !=='complete'" aria-current="page">{{unit?.title}}</li>
                </ol>
            </nav>
        </div>
        <div  v-if="course.bought || unit.trail == 'yes'" dir="ltr" id="single_course" class="nhsuk-width-container ltr position-relative  mx-auto">
            <div id="intro" :style="`background-color: ${course.colour}`" class="rounded  shadow-xl">
                <div class="top pt-2" dir='ltr' style="background-color: rgba(255,255,255,0.3)">
                    <button class="btn btn-default d-lg-none d-flex gap-2 justify-content-center align-items-center"
                            type="button" data-bs-toggle="offcanvas"
                            data-bs-target="#offcanvasResponsive" aria-controls="offcanvasResponsive">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                             class="bi bi-arrow-right" viewBox="0 0 16 16">
                            <path fill-rule="evenodd"
                                  d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8"/>
                        </svg>
                        <span data-translate>View Units</span>
                    </button>
                    <div v-if="currentModule && currentModule.units.length" class=" d-none d-lg-flex flex-row justify-content-around">
                        <div v-for="(item,index) in currentModule.units" :key="item.id" class="text-white p-0"
                             data-bs-placement="bottom" 
                             data-bs-toggle="tooltip" :title="item?.title"
                        >
                            <a v-if="item.href" :href="redirectLink(item.href)"
                               class="btn btn-link text-white"
                               :class="{'disabled' : (!course.bought  ||  ! previousUnitCompleted(index) )}">
                                <i class="fa fa-circle-o-notch fs-5"
                                   v-if="item.id == uid"
                                ></i>
                                <i v-else-if="item.completed_date !== null" class="fa fa-check-circle fs-4"
                                   aria-hidden="true"
                                ></i>
                                <i v-else class="fa fa-circle fs-5"
                                   aria-hidden="true"
                                ></i>
                            </a>
                        </div>
                    </div>
                </div>
                <div v-if="uid ==='complete'  && course.completion_percentage < 98"
                     class="d-flex flex-column justify-evenly p-4 text-white">
                    <h1 v-if="uid === 'complete'" data-translate>Module Complete</h1>
                    <h5>
                        <small data-translate>Congratulations you have completed the module</small>
                    </h5>
                </div>
                <div v-else-if="uid ==='complete' && course.completion_percentage > 98"
                     class="d-flex flex-column justify-evenly p-4 text-white">
                    <h1 data-translate>Course Complete</h1>
                    <h5>
                        <small data-translate>Congratulations, you have completed this course</small>
                    </h5>
                </div>
                <div v-else class="d-flex flex-column justify-evenly p-4 text-white">
                    <h1>{{unit?.title}}</h1>
                    <h5>
                        <small data-translate>Scroll to the bottom to "Mark as Completed" to progress through the Units</small>
                    </h5>
                </div>
                <div v-if="user && user.audio =='on'" class="audio_wrap">
                    <audio id="player1" style="width:100%; opacity: 0;" controls preload="none"
                           :autoplay="unit.mp3_auto_play == '1' && ( user && user.audio =='on')">
                        <source :src="unit.mp3_file" type="audio/mpeg"/>
                        <span data-translate>Your browser does not support the audio element.</span>
                    </audio>
                </div>
            </div>
            <div id="content" class="mt-4" v-html="unitHtml" v-if="unit.description">

            </div>
            <div v-if="!unit.description && uid!=='complete'">
                <div class="text-center mt-5">
                    <div class="spinner-border" role="status" style="width: 4rem; height: 4rem">
                        <span class="visually-hidden" data-translate>Loading...</span>
                    </div>
                </div>
            </div>

            <div v-if="unit.quiz && !(unit.quiz_response?.length>0)">
                <h4 class="h4">{{unit?.quiz?.title}}</h4>
                <v-form method="post" class="form" @submit="submitQuiz">
                    <template v-for="question in quiz">
                        <field-component :field="question" :index="`quiz-${question.id}`"></field-component>
                    </template>
                    <button type="submit" class="btn btn-success" data-translate>Submit Quiz</button>
                </v-form>
            </div>

            <div v-if="unit.survey && !(unit?.survey_response?.length>0)">
                
                    <h4 class="h4">{{unit.survey?.title}}</h4>
                    
                    <div id="alertPlaceholder"></div>
                    <v-form method="post" class="form" @submit="submitSurvey">
                    <template v-for="question in unit.survey?.fields">
                        <field-component :field="question" :index="`quiz-${question.id}`"></field-component>
                    </template>
                    <button type="submit" class="btn btn-success" data-translate>Submit Survey</button>
                    </v-form> 
                
                <!--  <h4 class="h4">{{unit?.survey?.title}}</h4>
                <v-form method="post" class="form" @submit="submitQuiz">
                    <template v-for="question in quiz">
                        <field-component :field="question" :index="`quiz-${question.id}`"></field-component>
                    </template>
                    <button type="submit" class="btn btn-success" data-translate>Submit Survey</button>
                </v-form>  -->
            </div>
            <!-- <div v-if="unit.has_form && unit.questionaire?.form?.id">
                <h4 class="h4">{{unit?.questionaire?.form?.title}}</h4>
                <div v-if="errors.length" class="alert alert-danger">
                    <ul class="list-unstyled">
                        <li v-for="error in errors">{{error.error}}</li>
                    </ul>
                </div>
                <v-form method="post" @submit="submitQuestionnaire">
                    <template v-for="question in questionnaire" :key="question.id">
                        <field-component :field="question" :index="`quest-${question.id}`"></field-component>
                    </template> 
                    <button type="submit" class="btn btn-success" data-translate>Submit Questionnaire</button>
                </v-form>
            </div> -->
            <div v-if="unit.quiz?.questions?.length>0 && unit.quiz_response?.length>0">
                <div class=" d-flex flex-column gap-2 p-2 rounded mt-4">
                    <h2 class="h3 text-dark fw-bold"><span data-translate>Your quiz results</span> <span><button
                                    class="btn btn-success"
                                    @click.prevent="unit.quiz_response=null" data-translate>Redo</button></span></h2>
                    <div class="card mb-3"
                         :class="{'' : !result.correct, 'bg-default' : result.correct}"
                         v-for="(result, index) in unit.quiz_response" :key="index">
                        <div class="card-body">
                            <p><strong data-translate>Question:</strong> {{result.question}}</p>
                            <p><strong data-translate>Your Answer:</strong> {{result.response}}</p>
                            <p v-if="result.correct"><span class="text-success"><i
                                            class="fa fa-check-circle"></i><span data-translate>Correct</span></span></p>
                            <p v-else><span class="text-danger"><i
                                            class="fa fa-times-circle"></i><span data-translate>Incorrect</span></span></p>

                        </div>
                    </div>
                </div>
            </div>
            <div v-if="uid ==='complete'">
                <div class="nhsuk-width-container my-5">
                    <div class="row justify-content-center">
                        <div class="col-auto">
                            <div role="progressbar" :aria-valuenow="course.completion_percentage" aria-valuemin="0"
                                 aria-valuemax="100"
                                 :style="`--value:${course.completion_percentage}`"
                                 class="d-flex flex-column justify-content-center align-items-center">
                                <img src="/online-learning/assets/images/trophy-fill.svg" class="percentage"
                                     style="width: 80px;height: 80px;"
                                     alt="trophy fill"/>
                                <span>{{course.completion_percentage}}%</span>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex flex-column justify-content-center align-items-center gap-2 mt-5">
                        <h5 class="h5 text-muted">{{course.completed_units_count}} <span data-translate>out of</span>  {{course.units_count}} <span data-translate>units completed.</span></h5>
                    </div>
                </div>
            </div>
            <div class="d-flex mt-5" v-if="unit.completed_date == null && uid !=='complete'">
                <button class="btn btn-block btn-primary" @click.prevent="markAsCompleted" data-translate>Mark as completed</button>
            </div>
            <div class="d-flex justify-content-center align-items-center" v-if="uid ==='complete'">
                <button class="btn btn-block btn-primary" v-if="nextModule?.id" @click.prevent="goToNextModule"><span data-translate>Go to next module :</span><span>{{nextModule?.title}}</span>
                </button>
                <a href="/<?php echo $ol_rl; ?>/dashboard" class="btn btn-block btn-primary" v-else data-translate>Go to dashboard
                </a>

                <a v-if="uid ==='complete' && course.completion_percentage > 98" class="btn btn-block" style="margin-left: 10px;border: 1px solid #0a58ca;" target="_blank" :href="'/online-learning/views/pdf_create_certificate.php?certificate='+course.username_id+'&uid='+encodeduid" data-translate>Download certificate</a>

               <a  v-if="uid==='complete' && currentmodulelastunit.progress_certificate==='1' && currentmodulelastunit.progress_certificate_title !== '' && course.completion_percentage < 98" class="btn btn-block" style="margin-left: 10px; border: 1px solid #0a58ca;" target="_blank"
                     :href="'/online-learning/views/pdf_create_unit_certificate.php?certificate=' + currentmodulelastunit.username_id + '&uid=' + encodeduid">
                    Download progress Certificate: {{ currentmodulelastunit.progress_certificate_title }}
                  </a> 
            </div>
            <div v-if="unit.questionaire != null && unit.questionaire.form_available == false"
                 class="alert alert-primary d-flex align-items-center mt-5"
                 role="alert">
                <div>
                    {{ unit.questionaire.message}}
                </div>
            </div>
            <div v-show="unit?.completed_date && unit.completed_date !== 'Invalid date' && unit.completed_date.trim() !== ''"
                 class="alert alert-primary align-items-center mt-5"
                 role="alert">
                <div>
                    <span data-translate>You completed this unit on </span><span>{{ unit.completed_date }}</span>
                </div>
            </div>

            <div
                  class="nhsuk-error-summary"
                  role="alert"
                  aria-labelledby="error-summary-title"
                  tabindex="-1"
                  data-module="nhsuk-error-summary"
                  v-if="errors.length > 0"
                >
                  <h2 class="nhsuk-error-summary__title" id="error-summary-title">
                    There is a problem
                  </h2>
                  <div class="nhsuk-error-summary__body">
                    <ul class="nhsuk-list nhsuk-error-summary__list">
                      <li v-for="error in errors" :key="error">{{ error }}</li>
                    </ul>
                  </div>
            </div>
           <div v-if="currentModule && currentModule.units.length && uid !== 'complete'" class="d-flex justify-content-between mt-5 w-100 flex-wrap">
                <div class="flex-fill text-start">
                    <button v-if="previousUnit?.href"
                            class="btn btn-outline-secondary"
                            @click.prevent="viewPreviousUnit"
                            :class="{'disabled': currentModule.units[0].id === unit.id}">
                        <span data-translate>Previous Unit</span>
                    </button>
                </div>
                <div class="flex-fill text-end">
                    <button v-if="nextUnit?.href || currentModule.units[(currentModule.units.length - 1)].id == unit.id"
                            class="btn btn-outline-secondary"
                            @click.prevent="viewNextUnit"
                            :class="{'disabled': unit.completed_date == null}">
                        <span v-if="currentModule.units[(currentModule.units.length - 1)].id == unit.id" data-translate>Complete Module</span>
                        <span v-else data-translate>Next Unit</span>
                    </button>
                </div>
            </div>

        </div>
         <div v-else class="nhsuk-width-container" style="height:60vh;">
           <div v-if="!page_loaded"> 

               <div class="nhsuk-loading-overlay" id="pageLoadingOverlay" hidden>
                  <div class="nhsuk-loading-spinner" aria-hidden="true"></div>
                  <span class="nhsuk-u-visually-hidden">
                    Loading content, please wait...
                  </span>
                </div>

           </div>
            <div v-else>
                <h1 class="nhsuk-heading-xl" style="text-align: left;font: normal normal bold 48px/32px Noto Sans;letter-spacing: 0px;color: #212B32;">Course Access</h1>
                <p class="nhsuk-body-l" style="text-align: left;font: normal normal normal 24px/32px Arial;letter-spacing: 0px;color: #212B32;">
                 You do not have access to this course yet . You can buy this course <a :href="redirectLink(course.href)" >here</a> 
                </p>
            </div>
      </div>
        <div v-if="auth.user" id="side_notes" :class="`${course.language_direction}`"
             :dir="course.language_direction">
            <div class="notes_icons"><i class="fa fa-pencil-square-o" aria-hidden="true"></i></div>
            <form method="POST" id="note_form" @submit.prevent="saveNote">
            <textarea placeholder="Write a note.."
                      class="form-control" v-model="note"></textarea>
                <input class="btn" type="submit"
                       value="Save">

                <input type="hidden" class="unit_id" :value="unit.id">
                <input type="hidden" class="current_unit_id" :value="unit.id">
            </form>
            <div class="content">
                <div class="d-flex gap-2 justify-content-between" style="margin-bottom: 8px;">
                    <a @click="printNotes"
                       class="btn btn-info"><span data-translate>Print</span></a>
                    <a href="#"
                       class="btn btn-warning close_notes"><span data-translate>Close Notes</span></a>
                </div>
                <ul id="notes_ul">
                    <li :class="`note_${note.id}_wrap`" v-for="note in notes" :key="note.id">
                        <div class="unit_top_info">
                            <i class="fa fa-pencil edit_note" aria-hidden="true"
                               @click.prevent="editNote(note)"></i>
                            <i class="fa fa-trash-o delete_note" aria-hidden="true"
                               @click.prevent="deleteNote(note)"></i>
                            {{note.description}}
                        </div>

                        <div class="unit_bottom_info">
                            <span data-translate>From:</span><a v-if="note && note.unit && note.unit.href"
                                    :href="redirectLink(note.unit.href)">{{note?.unit?.title}}</a>
                        </div>
                    </li>
                </ul>
            </div>
        </div>


        <div v-if="auth.user && course.show_feedback_on_each_unit == 1" id="side_feedback"
             :class="`${course.language_direction}`"
             :dir="course.language_direction">
            <div class="feedback_icons"><i class="fa fa-comment-o" aria-hidden="true"></i></div>
            <form method="POST" id="feedback_form" @submit.prevent="saveFeedback">
            <textarea
                    placeholder="Want to tell us something? Please provide your feedback here."
                    class="form-control feedback_description mb-2" v-model="feedback"></textarea>
                <input class="btn" type="submit" value="Submit">
            </form>
        </div>


        <div class="offcanvas-lg offcanvas-end d-lg-none" tabindex="-1" id="offcanvasResponsive"
             aria-labelledby="offcanvasResponsiveLabel">
            <div class="offcanvas-header">
                <h5 class="offcanvas-title" id="offcanvasResponsiveLabel">{{currentModule?.title}}</h5>
                <button type="button" class="btn btn-default" data-bs-dismiss="offcanvas"
                        data-bs-target="#offcanvasResponsive" aria-label="Close">x
                </button>
            </div>
            <div class="offcanvas-body">
                <div v-if="currentModule && currentModule.units.length" class="d-flex flex-column gap-2 justify-content-start align-items-start">
                    <div v-for="(item,index) in currentModule.units" :key="item.id" class="p-0 text-start"
                         data-bs-placement="bottom"
                         data-bs-toggle="tooltip" :title="item?.title"
                    >
                        <a v-if="item.href" :href="redirectLink(item.href)"
                           class=""
                           :class="{'disabled' : (!course.bought ||  ! previousUnitCompleted(index) ), 'text-muted' : item.id != uid, 'text-primary': item.id == uid}">
                            <i class="fa fa-circle-o-notch fs-5"
                               v-if="item.id == uid"
                            ></i>
                            <i v-else-if="item.completed_date !== null" class="fa fa-check-circle fs-4"
                               aria-hidden="true"
                            ></i>
                            <i v-else class="fa fa-circle fs-5"
                               aria-hidden="true"
                            ></i>
                            {{item?.title}}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const _u = Vue.createApp({
            components: {
                vForm: Form,
                vField: Field,
                ErrorMessage
            },
            data: () => ({
                course: {modules: [], colour: null},
                unit: {
                    id: null,
                    category_new: {name: null},
                    language: {name: null},
                    description: ''
                },
                errors: [],
                base: window.location.origin,
                id: '<?= $slugs[3] ?>',
                uid: '<?= $slugs[5]?>',
                module: '<?= $slugs[4]?>',
                auth: auth,
                user: user,
                notes: [],
                note: null,
                feedback: null,
                activeNote: null,
                db: new IndexDB(),
                encodeduid:'',
                currentPage: 1,
                limit: 100,
                notesrequestactive:false,
                hasMoreNotes: true, // Track if more notes are available
                hasSubmitedNote: false,
                page_loaded:false,
                errors: []

            }),
           async created() {
                this.getCourse();
                
            },
            async mounted() {
                this.page_loaded=false
                this.encodeduid=(auth?.user?.encoded_id)
                this.getNotes();
                this.$nextTick(() => {
                    console.log('mounted loading')
                    console.log(this.course)
                    //translatePage(this.course.language.id);
                });
               // Add scroll event listener to parent of #notes_ul
                const notesParent = document.getElementById("notes_ul")?.parentElement;
                if (notesParent) {
                    notesParent.addEventListener("scroll", this.handleScroll);
                }
            },

            beforeUnmount() {
                // Remove the scroll event listener to avoid memory leaks
                const notesParent = document.getElementById("notes_ul")?.parentElement;
                if (notesParent) {
                    notesParent.removeEventListener("scroll", this.handleScroll);
                }
            },
            computed: {
                unitHtml() {
                    let unit = $(`<div>${this.unit.description}</div>`);
                    unit.find("img").each((index, img) => {
                        $(img).addClass('img-fluid');
                    });
                    return unit.html();
                },
                currentModule() {
                    if (this.course.modules.length) {
                        return this.course.modules.filter((module) => {
                            return this.module == module.id;
                        })[0];
                    }
                    return {id: null, units: [{id: null}]};
                },
                currentmodulelastunit() {
                    if (!this.currentModule.units || !this.currentModule.units.length) {
                      return {};
                    }
                    return this.currentModule.units.reduce((last, unit) => {
                      return Number(unit.order) > Number(last.order) ? unit : last;
                    });
                },
                previousModule() {
                    let index = null;
                    this.course.modules.forEach((module, i) => {
                        if (module.id === this.currentModule.id) {
                            index = i;
                        }
                    })
                    return (index === 0 ? {id: null, completed_date: null} : this.course.modules[index - 1]);
                },
                nextModule() {
                    let index = null;
                    this.course.modules.forEach((module, i) => {
                        if (module.id === this.currentModule.id) {
                            index = i;
                        }
                    })
                    return (index === this.course.modules.length - 1 ? {
                        id: null,
                        completed_date: null
                    } : this.course.modules[index + 1]);
                },
                previousUnit() {
                    let index = null;
                    this.currentModule.units.forEach((unit, i) => {
                        if (unit.id === this.unit.id) {
                            index = i;
                        }
                    })
                    return (index === 0 ? {id: null, completed_date: null} : this.currentModule.units[index - 1]);
                },
                nextUnit() {
                    let index = null;
                    this.currentModule.units.forEach((unit, i) => {
                        if (unit.id === this.unit.id) {
                            index = i;
                        }
                    })
                    return (index === this.currentModule.units.length - 1 ? {
                        id: null,
                        completed_date: null
                    } : this.currentModule.units[index + 1]);
                },
                quiz() {
                    if (this.unit.quiz) {
                        const mandatory = Number(this.unit.quiz_mandetory) === 1;
                        return this.unit.quiz.questions.map((question) => ({
                            ...question,
                            current_value: '',
                            required: question.required ? question.required : mandatory
                        }));
                    }
                },
                questionnaire() {
                    const mandatory = Number(this.unit.questionnaire_mandetory) === 1;
                    if (this.unit.questionaire && this.unit.questionaire.form) {
                        return this.unit.questionaire.form.fields.map((question) => ({
                            ...question,
                            current_value: '',
                            hide_optional_label:true,
                            required: question.required ? question.required : mandatory
                        }));
                    }
                }
            },
            methods: {
                submitSurvey(){
                    let form = {
                        form_id: this.unit.survey.id,
                        module_id: this.unit.module_id,
                        unit_id: this.unit.id,
                        course_id: this.unit.course_id,
                        new_implementation:"yes",
                    };

                    this.unit.survey?.fields.forEach((item) => {
                        form[item.db_field_name] = item.current_value;
                    });

                    const raw = JSON.stringify(form);
                    const myHeaders = new Headers();
                    myHeaders.append("X-Api-Key", apiKey);

                    if (this.auth.user.token !== undefined) {
                        myHeaders.append("Authorization", `Bearer ${this.auth.user.token.token}`);
                    }
                    const requestOptions = {
                        method: "POST",
                        body: raw,
                        headers: myHeaders,
                        redirect: "follow" 
                    };
                    fetch(`${this.base}/admin/api/v2/submit_form`, requestOptions) 
                        .then((response) => response.json())
                        .then((result) => {
                            if (result) {
                                // Create and display a Bootstrap success alert
                                const alertPlaceholder = document.getElementById('alertPlaceholder');
                                if (alertPlaceholder) {
                                    alertPlaceholder.innerHTML = `
                                        <div class="alert alert-success alert-dismissible fade show" role="alert" >
                                            <span data-translate>Survey submitted successfully.</span>
                                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                        </div>
                                    `;
                                    // Optionally, remove the alert after 1.5 seconds
                                    setTimeout(() => {
                                        // Using Bootstrap's alert close functionality if available
                                        let alertNode = bootstrap.Alert.getInstance(alertPlaceholder.querySelector('.alert'));
                                        if (alertNode) {
                                            alertNode.close();
                                        } else {
                                            alertPlaceholder.innerHTML = '';
                                        }
                                    }, 4500);
                                }
                                this.unit = result;

                                 setTimeout(() => {
                                       translatePage(this.course.language.id);
                                    }, 1000);
                            }
                        })
                        .catch((error) => console.error(error));
                },
                submitQuiz() {
                    let form = {
                        form_id: this.unit.quiz.id,
                        module_id: this.unit.module_id,
                        unit_id: this.unit.id,
                        course_id: this.unit.course_id,
                    };

                    this.quiz.forEach((item) => {
                        form[item.db_field_name] = item.current_value;
                    })

                    const raw = JSON.stringify(form);
                    const myHeaders = new Headers();
                    myHeaders.append("X-Api-Key", apiKey);

                    if (this.auth.user.token !== undefined) {
                        myHeaders.append("Authorization", `Bearer ${this.auth.user.token.token}`);
                    }
                    const requestOptions = {
                        method: "POST",
                        body: raw,
                        headers: myHeaders,
                        redirect: "follow" 
                    };
                    fetch(`${this.base}/admin/api/v2/submit_form`, requestOptions) 
                        .then((response) => response.json())
                        .then((result) => {
                            if (result) {
                                this.unit = result;
                                 setTimeout(() => {
                                   translatePage(this.course.language.id);
                                }, 1000);
                            }
                        })
                        .catch((error) => console.error(error))
                },
                submitQuestionnaire() {
                    let errors = [];
                    this.errors = [];
                    let form = {
                        form_id: this.unit.questionaire.form.id,
                        module_id: this.unit.module_id,
                        unit_id: this.unit.id,
                        course_id: this.unit.course_id,
                    };

                    this.questionnaire.forEach((item) => {
                        if (item.required && !item.current_value) {
                            errors.push({...item, error: item?.title + ' is required'});
                        } else {
                            form[item.db_field_name] = item.current_value;
                        }
                    })

                    if (errors.length > 0) {
                        this.errors = errors;
                        return false;
                    }

                    const raw = JSON.stringify(form);
                    const myHeaders = new Headers();
                    myHeaders.append("X-Api-Key", apiKey);

                    if (this.auth.user.token !== undefined) {
                        myHeaders.append("Authorization", `Bearer ${this.auth.user.token.token}`);
                    }
                    const requestOptions = {
                        method: "POST",
                        body: raw,
                        headers: myHeaders,
                        redirect: "follow"
                    };
                    fetch(`${this.base}/admin/api/v2/submit_form`, requestOptions)
                        .then((response) => response.json())
                        .then((result) => {
                            if (result) {
                                // Swal.fire({
                                //     position: "top-end",
                                //     title: "Submitted successfully",
                                //     showConfirmButton: false,
                                //     timer: 1500
                                // });

                                 setTimeout(() => {
                                   translatePage(this.course.language.id);
                                }, 1000);
                            }
                        })
                        .catch((error) => console.error(error))
                },
                saveFeedback() {
                    if (this.feedback.trim().length > 0) {
                        const raw = JSON.stringify({
                            "feedback": this.feedback,
                            "feedback_id": null,
                            "unit_id": this.unit.id
                        });
                        const myHeaders = new Headers();
                        myHeaders.append("X-Api-Key", apiKey);

                        if (this.auth.user.token !== undefined) {
                            myHeaders.append("Authorization", `Bearer ${this.auth.user.token.token}`);
                        }
                        const requestOptions = {
                            method: "POST",
                            body: raw,
                            headers: myHeaders,
                            redirect: "follow"
                        };
                        fetch(`${this.base}/admin/api/v2/ol_notes/feedback`, requestOptions)
                            .then((response) => response.json())
                            .then((result) => {
                                if (result.success) {
                                    this.feedback = null;
                                }
                            })
                            .catch((error) => console.error(error))
                    }
                },
                editNote(note) {
                    this.activeNote = note.id;
                    this.note = note.description;
                },
                printNotes() {
                    window.open(`/<?php echo $ol_rl; ?>/print_notes/?course=${this.id}&student_id=${this.auth?.user?.student_id}`, '_blank', 'location=yes,height=570,width=800,scrollbars=yes,status=yes');
                },
                saveNote() {
                    if (this.note.trim().length > 0) {
                        const raw = JSON.stringify({
                            "note": this.note,
                            "note_id": this.activeNote,
                            "unit_id": this.unit.id
                        });
                        const myHeaders = new Headers();
                        myHeaders.append("X-Api-Key", apiKey);

                        if (this.auth.user.token !== undefined) {
                            myHeaders.append("Authorization", `Bearer ${this.auth.user.token.token}`);
                        }
                        const requestOptions = {
                            method: "POST",
                            body: raw,
                            headers: myHeaders,
                            redirect: "follow"
                        };
                        fetch(`${this.base}/admin/api/v2/ol_notes/store`, requestOptions)
                            .then((response) => response.json())
                            .then((result) => {
                                console.log("HHHHHHH")
                                console.log(result)
                                if (result.success) {
                                    if (!this.activeNote) {
                                        this.hasSubmitedNote=true
                                        this.getNotes();
                                        this.hasSubmitedNote=false
                                        this.note = null;
                                        this.activeNote = null;
                                    }else{

                                        let updatedNote = this.note; 
                                        // or however your updated note comes back from the API
                                        var acnote=this.activeNote

                                        // find the old note in local array
                                        const index = this.notes.findIndex(n => n.id === acnote);
                                        if (index !== -1) {
                                          // overwrite old note object with new
                                          this.notes[index].description = updatedNote;
                                        }

                                        // reset the form
                                        this.note = null;
                                        this.activeNote = null;
                                    }
                                }
                            })
                            .catch((error) => console.error(error))
                    }
                },
                deleteNote(note) {
                    const raw = JSON.stringify({
                        "note": this.note,
                        "unit_id": this.unit.id
                    });
                    const myHeaders = new Headers();
                    myHeaders.append("X-Api-Key", apiKey);

                    if (this.auth.user.token !== undefined) {
                        myHeaders.append("Authorization", `Bearer ${this.auth.user.token.token}`);
                    }
                    const requestOptions = {
                        method: "POST",
                        body: raw,
                        headers: myHeaders,
                        redirect: "follow"
                    };
                    fetch(`${this.base}/admin/api/v2/ol_notes/delete/${note.id}`, requestOptions)
                        .then((response) => response.json())
                        .then((result) => {
                            if (result.success) {
                                this.notes = this.notes.filter(n => n.id !== note.id);
                                //this.getNotes();
                                this.note = null;
                            }
                        })
                        .catch((error) => console.error(error))
                },
                async getCourse() {
                    // this.db.getDataFromTable('courses', Number(this.id)).then((courses) => {
                    //     this.course = JSON.parse(courses.payload);
                    // }).catch(error => {
                    const raw = JSON.stringify({
                        "categories": true,
                    });
                    const myHeaders = new Headers();
                    myHeaders.append("X-Api-Key", apiKey);

                    if (this.auth.user.token !== undefined) {
                        myHeaders.append("Authorization", `Bearer ${this.auth.user.token.token}`);
                    }
                    const requestOptions = {
                        method: "POST",
                        body: raw,
                        headers: myHeaders,
                        redirect: "follow"
                    };
                    fetch(`${this.base}/admin/api/v2/online_courses/${this.id}`, requestOptions)
                        .then((response) => response.json())
                        .then((result) => {
                            this.course = result.data;
                            this.getUnit();
                            //console.log('<<<getCourse>>>>>')
                            //console.log(this.course)
                            
                        })
                        .catch((error) => console.error(error))
                    /// });
                },
                 async getNotes() {
                    if (!this.hasMoreNotes && !this.hasSubmitedNote) return; // Stop if no more notes

                    let raw = JSON.stringify({
                        page: this.currentPage,
                        limit: this.limit,
                        course_id: this.id,
                    });
                    //console.log('this.hasSubmitedNote')
                    //console.log(this.hasSubmitedNote)

                    if (this.hasSubmitedNote) {
                        raw = JSON.stringify({
                            get_submited_note: true,
                            course_id: this.id,
                        });
                    }
                    console.log('raw')
                    console.log(raw)
                    const myHeaders = new Headers();
                    myHeaders.append("X-Api-Key", apiKey);
                    if (token) {
                        myHeaders.append("Authorization", `Bearer ${token}`);
                    }
                    const requestOptions = {
                        method: "POST",
                        body: raw,
                        headers: myHeaders,
                        redirect: "follow"
                    };
                    if (!this.notesrequestactive) {
                    this.notesrequestactive=true
                    fetch(`${this.base}/admin/api/v2/ol_notes`, requestOptions)
                        .then((response) => response.json())
                        .then((result) => {
                            if (result.success) {
                                if (result?.notes?.length < this.limit) {
                                    this.hasMoreNotes = false; // No more notes to load
                                }
                                this.notesrequestactive=false
                                this.notes = [...this.notes, ...result.notes]; // Append new notes
                                this.currentPage++; // Increment page number
                            }
                        })
                        .catch((error) => console.error(error));
                    }
                },

                loadMoreNotes() {
                    this.getNotes();
                },

                handleScroll(event) {
                    const element = event.target;
                    const scrollTop = element.scrollTop;
                    const scrollHeight = element.scrollHeight;
                    const clientHeight = element.clientHeight;

                    if (scrollTop + clientHeight >= scrollHeight - 10) {
                        this.loadMoreNotes();
                    }
                },
               async getUnit() {
                    if (this.uid === 'complete') {

                         console.log('currentModule')
                          console.log(this.currentModule)
                           setTimeout(() => {
                               translatePage(this.course.language.id);
                            }, 1000);

                        return false;
                    }
                    const raw = JSON.stringify({
                        "categories": true,
                        "course_bought": this.course.bought
                    });
                    const myHeaders = new Headers();
                    myHeaders.append("X-Api-Key", apiKey);
                    if (token) {
                        myHeaders.append("Authorization", `Bearer ${token}`);
                    }
                    const requestOptions = {
                        method: "POST",
                        body: raw,
                        headers: myHeaders,
                        redirect: "follow"
                    };
                    fetch(`${this.base}/admin/api/v2/units/${this.uid}`, requestOptions)
                        .then((response) => response.json())
                        .then((result) => {
                            this.page_loaded=true
                            this.unit = result; 
                            setTimeout(() => {
                               translatePage(this.course.language.id);
                            }, 1000);
                            if (this.unit.mp3_file) {
                            setTimeout(() => {
                                const player1 = new MediaElementPlayer('player1', {
                                    // This is needed to make Jump Forward to work correctly
                                    autoRewind: false,
                                    autoplay: true,
                                    features: ['playpause', 'current', 'progress', 'duration', 'speed', 'skipback', 'jumpforward', 'tracks',
                                        'markers', 'volume', 'chromecast', 'ads', 'vast', 'contextmenu'],

                                });
                            }, 1000);
                            }
                        })
                        .catch((error) => {
                            window.sessionStorage.setItem('redirect', "-1");
                            //window.location.replace('/<?php echo $ol_rl; ?>/accessibility/auth/login.php');
                        });
                },
                goToNextModule() {
                    if (this.nextModule.id) {
                        window.location.href = this.redirectLink(this.nextModule.units[0].href)
                    } else {
                        window.location.href = this.redirectLink(`/<?php echo $ol_rl; ?>/accessibility/complete.php?course=${this.course.id}`);
                    }
                },
               async markAsCompleted() {
                    if (this.unit.quiz && Number(this.unit.quiz_mandetory) === 1 && !this.unit.quiz_response) {
                        Swal.fire({
                            icon: 'warning',
                            position: "top-end",
                            title: "Submitted successfully",
                            showConfirmButton: false,
                            timer: 1500
                        });
                        return false;
                    }
                    const raw = JSON.stringify({
                        "course_id": this.unit.course_id
                    });
                    const myHeaders = new Headers();
                    myHeaders.append("X-Api-Key", apiKey);
                    if (token !== null) {
                        myHeaders.append("Authorization", `Bearer ${token}`);
                    }
                    const requestOptions = {
                        method: "POST",
                        body: raw,
                        headers: myHeaders,
                        redirect: "follow"
                    };
                    fetch(`${this.base}/admin/api/v2/units/mark_as_complete/${this.unit.id}`, requestOptions)
                        .then((response) => response.json())
                        .then((result) => {
                            this.unit.completed_date = new Date().toLocaleDateString();
                        })
                        .catch((error) => console.error(error))
                },
                previousUnitCompleted(index) {
                    if (index === 0) {
                        return this.previousModule.completed_date !== null;
                    } else {
                        return this.previousUnit?.completed_date !== null;
                    }
                },
               viewNextUnit() {
                        // Check if the unit has a mandatory quiz or questionnaire and determine completion status
                        const hasMandatoryQuiz = Number(this.unit.quiz_mandetory) === 1 && this.unit?.quiz?.questions?.length > 0;
                        const hasMandatoryQuestionnaire = Number(this.unit.questionnaire_mandetory) === 1 && this.unit?.questionaire?.form?.fields?.length > 0;

                        // If a mandatory quiz exists but has no questions, mark it as not mandatory
                        if (hasMandatoryQuiz && this.unit?.quiz?.questions?.length === 0) {
                            this.unit.quiz_mandetory = 0;
                        }

                        // If a mandatory questionnaire exists but has no questions, mark it as not mandatory
                        if (hasMandatoryQuestionnaire && this.unit?.questionaire?.form?.fields?.length === 0) {
                            this.unit.questionnaire_mandetory = 0;
                        }

                        // Recalculate mandatory statuses
                        const quizMandatory = Number(this.unit.quiz_mandetory) === 1;
                        const questionnaireMandatory = Number(this.unit.questionnaire_mandetory) === 1;

                        const quizCompleted = !quizMandatory || (this.unit.quiz_response?.length > 0);
                        const questionnaireCompleted = !questionnaireMandatory || (this.unit.questionaire?.submitted === true);

                        // Proceed if:
                        // 1) Mandatory quiz is completed OR
                        // 2) Mandatory questionnaire is completed OR
                        // 3) The unit does not have a mandatory quiz or questionnaire
                        if (quizCompleted && questionnaireCompleted) {
                            this.handlenext_unitview();
                        } else {
                            // Swal.fire({
                            //     icon: 'warning',
                            //     position: "top-end",
                            //     title: "Please complete the required quiz or questionnaire before proceeding.",
                            //     showConfirmButton: true
                            // });
                            var txt=""
                            if (!quizCompleted) {
                                txt='quiz'
                            }
                            if (!questionnaireCompleted) {
                                txt='questionnaire'
                            }
                            this.errors.push('Please complete the required '+txt+' before proceeding.');
                            //this.errors = errors;
                        }
                    },

                    handlenext_unitview() {
                        const next = this.nextUnit;
                        if (next.id) {
                            window.location.href = this.redirectLink(next.href);
                        } else {
                            window.location.href = (`/<?php echo $ol_rl; ?>/course/${this.unit.course_slug??this.unit.course_id}/${this.unit.module_slug??this.unit.module_id}/complete`);
                        }
                    },
                viewPreviousUnit() {
                    const previous = this.previousUnit;
                    if (previous.id) {
                        window.location.href = (previous.href);
                    }
                },
                handlePageRedirect(link) {
                    const currentUrl = window.location.href;
                    //console.log(currentUrl)
                    // Check if the current URL contains '/online-learning-dev'
                    if (currentUrl.includes('/online-learning-dev')) {
                        // Replace '/online-learning-dev' with '/online-learning'
                        link = link.replace('/online-learning', '/online-learning-dev');
                        //window.location.href = updatedUrl;
                    }
                    // Otherwise, redirect to the provided href
                    window.location.href = link;
                },
                redirectLink(link) {
                    const currentUrl = window.location.href;
                    //console.log(currentUrl)
                    // Check if the current URL contains '/online-learning-dev'
                    if (currentUrl.includes('/online-learning-dev')) {
                        // Replace '/online-learning-dev' with '/online-learning'
                        link = link.replace('/online-learning', '/online-learning-dev');
                        //window.location.href = updatedUrl;
                    }
                    // Otherwise, redirect to the provided href
                    return link;
                },
            }
        });
        _u.component('field-component', FormField);
        _u.mount("#unit-view");
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/normalize/5.0.0/normalize.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/mediaelement@4.2.17/build/mediaelementplayer.min.css">
    <link rel="stylesheet"
          href="/online-learning/assets/other/mediaelement/dist/jump-forward/jump-forward.css">
    <link rel="stylesheet" href="/online-learning/assets/other/mediaelement/dist/skip-back/skip-back.css">
    <link rel="stylesheet" href="/online-learning/assets/other/mediaelement/dist/speed/speed.css">
    <link rel="stylesheet" href="/online-learning/assets/other/mediaelement/dist/chromecast/chromecast.css">
    <link rel="stylesheet" href="/online-learning/assets/other/mediaelement/dist/ads/ads.css">
    <link rel="stylesheet"
          href="/online-learning/assets/other/mediaelement/dist/context-menu/context-menu.css">
    <style type="text/css">
        .audio_wrap {
            background-color: rgba(0, 0, 0, .3);
        }

        .mejs__container {
            background-color: transparent;
        }

        .mejs__controls:not([style*='display: none']) {
            background: transparent;
            background: -webkit-linear-gradient(transparent, rgba(0, 0, 0, 0));
            background: linear-gradient(transparent, rgba(0, 0, 0, 0));
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/mediaelement@4.2.17/build/mediaelement-and-player.js"></script>
    <script src="/online-learning/assets/other/mediaelement/dist/jump-forward/jump-forward.js"></script>
    <script src="/online-learning/assets/other/mediaelement/dist/skip-back/skip-back.js"></script>
    <script src="/online-learning/assets/other/mediaelement/dist/markers/markers.js"></script>
    <script src="/online-learning/assets/other/mediaelement/dist/speed/speed.js"></script>
    <script src="/online-learning/assets/other/mediaelement/dist/chromecast/chromecast.js"></script>
    <script src="/online-learning/assets/other/mediaelement/dist/ads/ads.js"></script>
    <script src="/online-learning/assets/other/mediaelement/dist/ads-vast-vpaid/ads-vast-vpaid.js"></script>
    <script src="/online-learning/assets/other/mediaelement/dist/context-menu/context-menu.js"></script>
    <script id="mejs-code">
        document.addEventListener("DOMContentLoaded", function () {

        });

    </script>
<?php include("includes/footer_bootstrap.php"); ?>
