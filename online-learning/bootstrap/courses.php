<?php
$featured_courses = [];
$courses = [];
//if (!empty($_SESSION['personalization'])) {
$featured_courses_args = array(
    'featured' => 1,
    'school_id' => $_SESSION['usergroup'],
    'raw_html' => false,
    'published_courses' => true,
    'no_quiz_information' => "true",
    'no_unit_description' => true,
    'count_modules' => true,
    'no_modules' => true,
    'with_categories' => true,
);
dev_debug("get_courses_v2 1");

$featured_courses = $OL->get_courses_v2($featured_courses_args);

$course_levels = pull_field("ols_usrlvl_pln_crs_link", "db37367", "WHERE db37366 = '" . $_SESSION['ulevel'] . "' AND usergroup = '" . $_SESSION['usergroup'] . "'");

$course_args = array(
    'search' => str_replace("'", "\'", $_POST['search']),
    'raw_html' => true,
    'school_id' => $_SESSION['usergroup'],
    'published_courses' => true,
    'no_unit_description' => true,
    'no_quiz_information' => "true",
    'count_modules' => true,
    'with_categories' => true,
    'no_modules' => true
);

if (!empty($slugs) && $slugs[3]) {
    $course_args['category'] = $slugs[3];
}
if (!empty($course_levels)) {
    $course_args['course_level'] = $course_levels;
}

$course_args['order'] = "ORDER BY db31066 DESC";
unset($course_args['order']);
$course_args['with_languages'] = true;
$course_args['main_language_courses'] = true;
dev_debug("get_courses_v2 3");
$courses = $OL->get_courses_v2($course_args);
//}
$preferences = $OL->preferences($_SESSION['usergroup']);

$hero = $preferences->db253406 ? '/static/' . $_SESSION['subdomain'] . '/resources/img/banner.jpg' : '/static/' . $_SESSION['subdomain'] . '/resources/img/hero_placeholder.jpg';

$categories = $_SESSION['categories'] = $_SESSION['categories'] ?: $OL->get_category(['usergroup' => $_SESSION['usergroup']]);;
$languages = $_SESSION['languages'] = $_SESSION['languages'] ?: $OL->get_languages(['ug_specific' => true]);

?>
<div id="course-library">
    <section>
        <div class="d-block course-bg" data-bss-parallax-bg="true">
            <div class="container h-100">
                <div class="row h-100">
                    <div class="col-md-6 text-center text-md-start d-flex float-start d-sm-flex d-md-flex justify-content-center align-items-center justify-content-md-start align-items-md-center justify-content-lg-start justify-content-xl-start justify-content-xxl-start py-xs">
                        <div class="my-sm-2" style="max-width: 350px;">
                            <h1 class="text-capitalize fw-bold c-header">Course Library</h1>
                            <p class="my-3 c-colors">Find your course to start learning about emotional health,
                                wellbeing
                                and development.</p><a class="btn btn-link btn-lg fw-bold me-2" role="button" href="#"
                                                       style="font-family: 'Noto Sans', sans-serif;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 20 20"
                                     fill="none" class="text-success" style="font-size: 38px;">

                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                          d="M10 18C14.4183 18 18 14.4183 18 10C18 5.58172 14.4183 2 10 2C5.58172 2 2 5.58172 2 10C2 14.4183 5.58172 18 10 18ZM13.7071 9.29289L10.7071 6.29289C10.3166 5.90237 9.68342 5.90237 9.29289 6.29289C8.90237 6.68342 8.90237 7.31658 9.29289 7.70711L10.5858 9L7 9C6.44772 9 6 9.44771 6 10C6 10.5523 6.44772 11 7 11H10.5858L9.29289 12.2929C8.90237 12.6834 8.90237 13.3166 9.29289 13.7071C9.68342 14.0976 10.3166 14.0976 10.7071 13.7071L13.7071 10.7071C14.0976 10.3166 14.0976 9.68342 13.7071 9.29289Z"
                                          fill="currentColor"></path>
                                </svg>
                                Take a Tour</a>
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-6 text-center text-md-start d-flex d-sm-flex d-md-flex justify-content-center align-items-center justify-content-md-end align-items-md-center justify-content-lg-end justify-content-xl-end justify-content-xxl-end">
                        <div class="mt-3" style="max-width: 350px;">
                            <div class="card"
                                 style="border-radius: 2px;border-bottom: 3px solid #d8dde0;margin-bottom: 14px;">
                                <div class="card-body d-block" style="margin-top: 0px;padding-right: 16px;">
                                    <h4 class="fw-bold card-title"
                                        style="font-family: 'Noto Sans', sans-serif;font-size: 18px;">Free for me?</h4>
                                    <h5 class="fw-normal text-muted card-subtitle mb-2" style="font-size: 15px;">Find
                                        out if
                                        you are in a prepaid area:</h5>
                                    <button class="btn btn-success fw-normal" type="button"
                                            style="background: var(--bs-btn-border-color);border-radius: 26px;font-family: 'Noto Sans', sans-serif;font-size: 16px;border-width: 0px;border-color: rgba(255,255,255,0);border-right-style: none;padding-top: 10px;padding-bottom: 10px;padding-right: 18px;padding-left: 18px;width: 100%;"
                                            data-bs-toggle="modal" data-bs-target="#modal-1">Free access checker
                                    </button>
                                </div>
                            </div>
                            <div class="card" style="border-radius: 2px;border-bottom: 3px solid #d8dde0;">
                                <div class="card-body d-block pe-4">
                                    <h4 class="fw-bold card-title"
                                        style="font-family: 'Noto Sans', sans-serif;font-size: 18px;">Apply Access
                                        Code</h4>
                                    <h5 class="fw-normal text-muted card-subtitle mb-2" style="font-size: 15px;">If you
                                        have
                                        an Access Code please enter it below:</h5>
                                    <div class="mt-2 pt-2">
                                        <form class="d-sm-inline-block me-auto ms-md-3 my-2 my-md-0 mw-100 navbar-search"
                                              style="width: 100%;">
                                            <div class="input-group input-group-sm"><input class="bg-light form-control"
                                                                                           type="text"
                                                                                           placeholder="Enter Access Code"
                                                                                           style="border-radius: 20px 0px 0px 20px;border: 1.8px solid var(--bs-gray-800) ;"><a
                                                        class="btn btn-primary py-2" role="button"
                                                        style="border-radius: 0px 20px 20px 0px;background: var(--bs-gray-700);border-color: var(--bs-body-color);">Apply
                                                    Code</a></div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="container-fluid p-sm-3 p-md-4 p-lg-4 p-xl-4 p-xxl-4 px-sm-5 px-md-5 px-lg-5 px-xl-5 px-xxl-5 mobile-xs"
         style="background: #f1fafb;">
        <div>
            <h4 class="fw-bold pt-3 my-2" style="font-family: 'Noto Sans', sans-serif;/*color: rgb(15,129,144);*/">
                Featured
                Courses</h4>
        </div>
    </div>
    <div class="container-fluid d-flex justify-content-center align-items-center p-sm-3 p-md-4 p-lg-4 p-xl-4 p-xxl-4 px-sm-5 px-md-5 px-lg-5 px-xl-5 px-xxl-5 mobile-xs"
         style="background: #f1fafb;">
        <div class="carousel slide" data-bs-ride="carousel" data-bs-interval="6000" data-bs-pause="hover"
             data-bs-keyboard="false" id="carousel-1">
            <div class="carousel-inner">
                <div class="carousel-item active" v-for="(chunk, index) in chunkedFeaturedCourses" :key="index">
                    <div class="row gy-4 row-cols-1 row-cols-md-2 row-cols-xl-3">
                        <div class="col" v-for="course in chunk" :key="course.id">
                            <div class="row mx-0 col-shadow">
                                <div class="col-1 col-xxl-2 img-col-bg ps-0 rounded-start"
                                     style="background-size: cover;background-position: center"
                                     :style="{'background-image': `url(${course.blob})` }">
                                    <!--                        <img class="course-img-block" :src="course.blob">-->
                                </div>
                                <div class="col img-col-bg px-0">
                                    <div class="ps-xs-2 ps-sm-3 ps-md-1 ps-lg-3 p-1 course-block course-border1">
                                        <h6 class="text-start pt-3 pb-0 course-subt">
                                            {{course.category_new ? course.category_new.name : 'n/a'}}</h6>
                                        <h6 class="text-start"
                                            style="font-family: 'Noto Sans', sans-serif;font-weight: bold;">
                                            {{course.title}}</h6>
                                        <p class="text-start">{{course.excerpt}}</p>
                                        <ul class="list-inline d-flex px-2 px-md-0 px-xl-1 options-list pt-4">
                                            <li class="list-inline-item mx-0 list-md mid-list1">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                                     fill="currentColor" viewBox="0 0 16 16"
                                                     class="bi bi-chat-left-text-fill" style="font-size: 17px;">
                                                    <path d="M0 2a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H4.414a1 1 0 0 0-.707.293L.854 15.146A.5.5 0 0 1 0 14.793zm3.5 1a.5.5 0 0 0 0 1h9a.5.5 0 0 0 0-1zm0 2.5a.5.5 0 0 0 0 1h9a.5.5 0 0 0 0-1zm0 2.5a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1z"></path>
                                                </svg>
                                                {{course.language ? course.language.name : 'English'}}
                                            </li>
                                            <li class="list-inline-item mx-0 list-md mid-list2">
                                                <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 24 24"
                                                     width="1em" fill="currentColor" style="font-size: 17px;">
                                                    <path d="M0 0h24v24H0z" fill="none"></path>
                                                    <path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z"></path>
                                                </svg>
                                                {{course.module_count}} Modules
                                            </li>
                                            <li class="list-inline-item mx-0 list-md mid-list3" v-if="course.has_audio">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                                     viewBox="0 0 20 20" fill="none" style="font-size: 18px;">
                                                    <path d="M9.04893 2.92707C9.34828 2.00576 10.6517 2.00576 10.951 2.92707L12.0206 6.21886C12.1545 6.63089 12.5384 6.90985 12.9717 6.90985H16.4329C17.4016 6.90985 17.8044 8.14946 17.0207 8.71886L14.2205 10.7533C13.87 11.0079 13.7233 11.4593 13.8572 11.8713L14.9268 15.1631C15.2261 16.0844 14.1717 16.8506 13.3879 16.2812L10.5878 14.2467C10.2373 13.9921 9.76269 13.9921 9.4122 14.2467L6.61203 16.2812C5.82832 16.8506 4.77384 16.0844 5.07319 15.1631L6.14276 11.8713C6.27663 11.4593 6.12997 11.0079 5.77949 10.7533L2.97932 8.71886C2.1956 8.14946 2.59838 6.90985 3.5671 6.90985H7.0283C7.46153 6.90985 7.84548 6.63089 7.97936 6.21886L9.04893 2.92707Z"
                                                          fill="currentColor"></path>
                                                </svg>
                                                Audio Support
                                            </li>
                                        </ul>
                                        <hr class="mb-2" style="border: 2px solid #a9a9a9;">
                                        <a class="text-end pt-2 pt-md-0 pe-3 float-right d-block"
                                           :href="course.href">Find out more</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div><a class="carousel-control-prev" href="#carousel-1" role="button" data-bs-slide="prev"><span
                            class="carousel-control-prev-icon"></span><span
                            class="visually-hidden">Previous</span></a><a
                        class="carousel-control-next" href="#carousel-1" role="button" data-bs-slide="next"><span
                            class="carousel-control-next-icon"></span><span class="visually-hidden">Next</span></a>
            </div>
            <div class="carousel-indicators">
                <button type="button" data-bs-target="#carousel-1" data-bs-slide-to="0" class="active"></button>
                <button type="button" data-bs-target="#carousel-1" data-bs-slide-to="1"></button>
                <button type="button" data-bs-target="#carousel-1" data-bs-slide-to="2"></button>
            </div>
        </div>
    </div>
    <div class="container-fluid py-2 px-5" style="background: #f1fafb;">
        <div class="row">
            <div class="col-md-12">
                <div>
                    <h4 class="fw-bold pt-5" style="font-family: 'Noto Sans', sans-serif;/*color: rgb(15,129,144);*/">
                        Full
                        range of courses</h4>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-10">
                <div class="tab-course mt-1">
                    <nav class="navbar navbar-expand-lg" style="background: rgb(241,250,251);">
                        <div class="container-fluid"><a class="navbar-brand" href="#" style="font-size: 14px;">All
                                Courses</a>
                            <button data-bs-toggle="collapse" class="navbar-toggler" data-bs-target="#navcol-3"><span
                                        class="visually-hidden">Toggle navigation</span><span
                                        class="navbar-toggler-icon"></span></button>
                            <div class="collapse navbar-collapse" id="navcol-3"
                                 style="font-family: 'Noto Sans', sans-serif;font-size: 14px;">
                                <ul class="navbar-nav featured-nav mx-0 px-0">
                                    <li class="nav-item bg-success"><a class="nav-link active active-color" href="#">Antenatal</a>
                                    </li>
                                    <li class="nav-item"><a class="nav-link" href="#" style="font-size: 14px;">Postnatal
                                            (0-6 months)&nbsp;</a></li>
                                    <li class="nav-item"><a class="nav-link" href="#" style="font-size: 14px;">6 months
                                            to
                                            19 years</a></li>
                                    <li class="nav-item"><a class="nav-link" href="#" style="font-size: 14px;">Preteen
                                            to
                                            teenager</a></li>
                                    <li class="nav-item"><a class="nav-link" href="#"
                                                            style="font-size: 14px;">Adults</a>
                                    </li>
                                    <li class="nav-item"><a class="nav-link" href="#" style="font-size: 14px;">Professional
                                            Transalations</a></li>
                                    <li class="nav-item"><a class="nav-link" href="#" style="font-size: 14px;">Courses
                                            for
                                            Professionals</a></li>
                                </ul>
                            </div>
                        </div>
                    </nav>
                </div>
            </div>
            <div class="col-2">
                <button class="btn btn-success" type="button"
                        style="padding-right: 24px;padding-left: 24px;padding-bottom: 10px;padding-top: 10px;border-radius: 27px;">
                    Personalise
                </button>
            </div>
        </div>
    </div>
    <div class="container-fluid p-sm-3 p-md-4 p-lg-4 p-xl-4 p-xxl-4 px-sm-5 px-md-5 px-lg-5 px-xl-5 px-xxl-5 mobile-xs"
         style="background: #f1fafb;">
        <div class="row gy-4 row-cols-1 row-cols-md-2 row-cols-xl-3">
            <div class="col" v-for="course in courses" v-if="courses.length" :key="course.id">
                <div class="row mx-0 col-shadow">
                    <div class="col-1 col-xxl-2 img-col-bg ps-0 rounded-start"
                         style="background-size: cover;background-position: center"
                         :style="{'background-image': `url(${course.blob})` }">
                        <!--                        <img class="course-img-block" :src="course.blob">-->
                    </div>
                    <div class="col img-col-bg px-0">
                        <div class="ps-xs-2 ps-sm-3 ps-md-1 ps-lg-3 p-1 course-block course-border1">
                            <h6 class="text-start pt-3 pb-0 course-subt">
                                {{course.category_new ? course.category_new.name : 'n/a'}}</h6>
                            <h6 style="font-family: 'Noto Sans', sans-serif;font-weight: bold;">{{course.title}}</h6>
                            <p>{{course.excerpt}}</p>
                            <ul class="list-inline d-flex px-2 px-md-0 px-xl-1 options-list pt-4">
                                <li class="list-inline-item mx-0 list-md mid-list1">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="currentColor"
                                         viewBox="0 0 16 16" class="bi bi-chat-left-text-fill" style="font-size: 17px;">
                                        <path d="M0 2a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H4.414a1 1 0 0 0-.707.293L.854 15.146A.5.5 0 0 1 0 14.793zm3.5 1a.5.5 0 0 0 0 1h9a.5.5 0 0 0 0-1zm0 2.5a.5.5 0 0 0 0 1h9a.5.5 0 0 0 0-1zm0 2.5a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1z"></path>
                                    </svg>
                                    {{course.language ? course.language.name : 'English'}}
                                </li>
                                <li class="list-inline-item mx-0 list-md mid-list2">
                                    <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 24 24" width="1em"
                                         fill="currentColor" style="font-size: 17px;">
                                        <path d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z"></path>
                                    </svg>
                                    {{course.module_count}} Modules
                                </li>
                                <li class="list-inline-item mx-0 list-md mid-list3" v-if="course.has_audio">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 20 20"
                                         fill="none" style="font-size: 18px;">
                                        <path d="M9.04893 2.92707C9.34828 2.00576 10.6517 2.00576 10.951 2.92707L12.0206 6.21886C12.1545 6.63089 12.5384 6.90985 12.9717 6.90985H16.4329C17.4016 6.90985 17.8044 8.14946 17.0207 8.71886L14.2205 10.7533C13.87 11.0079 13.7233 11.4593 13.8572 11.8713L14.9268 15.1631C15.2261 16.0844 14.1717 16.8506 13.3879 16.2812L10.5878 14.2467C10.2373 13.9921 9.76269 13.9921 9.4122 14.2467L6.61203 16.2812C5.82832 16.8506 4.77384 16.0844 5.07319 15.1631L6.14276 11.8713C6.27663 11.4593 6.12997 11.0079 5.77949 10.7533L2.97932 8.71886C2.1956 8.14946 2.59838 6.90985 3.5671 6.90985H7.0283C7.46153 6.90985 7.84548 6.63089 7.97936 6.21886L9.04893 2.92707Z"
                                              fill="currentColor"></path>
                                    </svg>
                                    Audio Support
                                </li>
                            </ul>
                            <hr class="mb-3" style="border: 2px solid #a9a9a9;">
                            <a class="text-end pt-2 pt-md-0 pe-3" :href="course.href">Find
                                out more</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    function googleTranslateElementInitCourses() {
        new google.translate.TranslateElement({
            pageLanguage: 'en-GB',
            layout: google.translate.TranslateElement.InlineLayout.SIMPLE
        }, 'courses_google_translate_element');
    }
</script>
<script type="text/javascript"
        src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInitCourses"></script>
<script>
    const {Carousel, Navigation, Slide, Pagination} = VueCarousel;

    const courses = Vue.createApp({
            components: {
                Carousel, Navigation, Slide, Pagination,
                vSelect: window["vue-select"]
            },
            mounted() {
                this.featured = <?php echo json_encode($featured_courses) ?: [] ?>;
                this.featuredCourses = this.featured;
                this.courses = this.coursesList;
                this.featuredCoursesMethod();
                this.coursesMethod();
            },
            data: () => ({
                featured: [],
                categories: <?php echo json_encode($categories) ?: [] ?>,
                activeCategory: {},
                languages: <?php echo json_encode($languages) ?: [] ?>,
                activeLanguage: {},
                coursesList: <?php echo json_encode($courses) ?: [] ?>,
                search: "",
                code: "<?php echo $_REQUEST['accode'] ?? '' ?>",
                code_error: "",
                ref: null,
                level: '<?= $_SESSION['ulevel'] ?: 0 ?>',
                code_loading: false,
                mustLogIn: "<?php echo ($_SESSION['ulevel'] == 19 & !$_SESSION['uid']) ? 'yes' : 'no' ?>",
                pulse: false,
                courses: [],
                featuredCourses: [],
                key: Date.now()
            }),
            computed: {
                chunkedFeaturedCourses() {
                    const chunkSize = 3;
                    const chunks = [];
                    for (let i = 0; i < this.featuredCourses.length; i += chunkSize) {
                        chunks.push(this.featuredCourses.slice(i, i + chunkSize));
                    }
                    return chunks;
                }
            },
            methods: {
                coursesMethod: async function () {
                    const list = await this.courseListMap();
                    if (this.search.trim().length > 0) {
                        this.courses = list.filter((course) => {
                            return course.title.toLowerCase().indexOf(this.search.toLowerCase()) >= 0
                                || course.description.toLowerCase().indexOf(this.search.toLowerCase()) >= 0
                                || course.excerpt.toLowerCase().indexOf(this.search.toLowerCase()) >= 0;
                        })
                    } else if (this.activeCategory.id !== undefined) {
                        this.courses = list.filter((course) => {
                            return course.category_new !== null && course.category_new.id === this.activeCategory.id;
                        });
                    } else {
                        this.courses = list;
                    }
                },
                courseListMap: async function () {
                    return await Promise.all(
                        this.coursesList.map((course) => {
                            const blob = window.sessionStorage.getItem('course_' + course.id);
                            if (blob == null) {
                                this.fetchImageData(course.thumbnail.replace(".co.uk", ".com")).then((image) => {
                                    course.blob = image;
                                    window.sessionStorage.setItem('course_' + course.id, course.blob);
                                });
                            } else {
                                course.blob = blob;
                            }
                            const icon = window.sessionStorage.getItem(course.language.code);
                            if (icon == null) {
                                this.fetchImageData(course.language_icon).then((image) => {
                                    course.icon = image;
                                    window.sessionStorage.setItem(course.language.code, course.icon);
                                });
                            } else {
                                course.icon = icon;
                            }
                            return course;
                        })
                    );
                },
                featuredCoursesMethod: async function () {
                    this.featuredCourses = await Promise.all(
                        this.featured.map((course) => {
                            const blob = window.sessionStorage.getItem('course_' + course.id);
                            if (blob == null) {
                                this.fetchImageData(course.thumbnail).then((image) => {
                                    course.blob = image;
                                    window.sessionStorage.setItem('course_' + course.id, course.blob);
                                });
                            } else {
                                course.blob = blob;
                            }
                            const icon = window.sessionStorage.getItem(course.language.code);
                            if (icon == null) {
                                this.fetchImageData(course.language_icon).then((image) => {
                                    course.icon = image;
                                    window.sessionStorage.setItem(course.language.code, course.icon);
                                });
                            } else {
                                course.icon = icon;
                            }
                            return course;
                        })
                    );
                    this.key = Date.now();
                },
                handleGoogleTranslateSelect: (language) => {
                    console.log(language)
                },
                async fetchImageData(url) {
                    try {
                        // Make an HTTP request to fetch binary image data
                        const response = await fetch(url, {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'image/*',
                                'Cache-Control': 'public,max-age=31536000'
                            }
                        });
                        const blob = await response.blob();
                        // Assuming the response data is an array of binary image data
                        return await this.blobToBase64(blob);
                    } catch (error) {
                        console.error('Error fetching image data:', error);
                    }
                },
                blobToBase64(blob) {
                    return new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.onload = () => {
                            resolve(reader.result);
                        };
                        reader.onerror = reject;
                        reader.readAsDataURL(blob);
                    });
                },
                getImageSource(blob) {
                    try {
                        return URL.createObjectURL(blob);
                    } catch (error) {
                        console.error('Error creating object URL:', error);
                        return ''; // Return empty string or handle the error accordingly
                    }
                },
                setCategory(category) {
                    if (category.tag === 'professional') {
                        const el = this.$refs.professional;
                        if (el) {
                            el.scrollIntoView({behavior: "smooth"});
                            this.pulse = true;
                            setTimeout(() => this.pulse = false, 5000);
                        }
                    } else {
                        this.activeCategory = category;
                        this.coursesMethod();
                    }
                },
                setLanguage(language) {
                    this.activeLanguage = language;
                },
                async checkAccessCode() {
                    if (this.code) {
                        this.code_loading = true;
                        try {
                            const response = await fetch(
                                `${window.location.origin}/online-learning/includes/ajax.php`,
                                {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/x-www-form-urlencoded',
                                    },
                                    body: `action=check_access_plan_code&must_log_in=${this.mustLogIn}&code=${this.code}`,
                                }
                            );

                            const info = await response.json();
                            this.handleAccessPlanResponse(info);
                        } catch (error) {
                            console.error('Error:', error);
                            // Handle error
                        }
                    }
                },
                handleAccessPlanResponse(info) {
                    this.code_loading = false;
                    this.code_error = "";
                    if (info.success) {
                        if (info.plan) {
                            window.location.replace(`/online-learning/register_new?plan=${info.plan.username_id}`);
                        } else if (info.coupon) {
                            window.location.replace(`/online-learning/register_new?plan=${info.plan.username_id}`);
                        } else {
                            this.code_error = info.message;
                            Swal.fire({
                                position: "top-end",
                                icon: "warning",
                                title: info.message,
                                showConfirmButton: false,
                                timer: 2500
                            });
                        }
                    } else {
                        this.code_error = info.message;
                        Swal.fire({
                            position: "top-end",
                            icon: "error",
                            title: info.message,
                            showConfirmButton: false,
                            timer: 2500
                        });
                    }
                },
            }
        })
    ;
    courses.component('language-dropdown', {
        props: ['id', 'languages', 'icon'],
        template: `
        <button :id="id" :data-dropdown-toggle="dropdownId" class="flex items-center text-sm pe-2 font-medium text-gray-900 rounded-full shadow-2xl shadow-black hover:text-blue-600 dark:hover:text-blue-500 md:me-0 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:text-white" type="button">
            <span class="sr-only">Open user menu</span>
            <img v-if="icon && icon.length" class="w-7 h-7 me-1 rounded-full" :src="icon" />
            <span v-else class="w-7 h-7 me-1 rounded-full bg-gray-100"></span>
            <svg class="w-2.5 h-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
            </svg>
        </button>

<!-- Dropdown menu -->
<div :id="dropdownId" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600">

    <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownInformationButton">
      <li v-for="language in langs" :key="language.id">
        <a :href="language.href" target="_blank" class="flex gap-2  px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
            <img :src="language.language_icon" class="w-7 h-7 me-1 rounded-full"/>
            <span>{{language.language.name}}</span>
        </a>
      </li>
      <li>
        <a @click="scrollToElement" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Auto-translate</a>
      </li>
    </ul>
</div>
`,
        computed: {
            dropdownId() {
                return `dropdown-${this.id}`
            }
        },
        mounted() {
            this.languagesMethod();
        },
        data: () => ({
            langs: {}
        }),
        methods: {
            scrollToElement() {
                this.$emit('scrolldown', {id: '17', tag: 'professional'});
            },
            languagesMethod: async function () {
                this.langs = await Promise.all(
                    this.languages.map((lang) => {
                        const blob = window.sessionStorage.getItem(lang.language.code);
                        if (blob == null) {
                            this.fetchImageData(lang.language_icon).then((image) => {
                                lang.icon = image;
                                window.sessionStorage.setItem(lang.language.code, image);
                            });
                        } else {
                            lang.icon = blob;
                        }
                        return lang;
                    })
                );
            },
            async fetchImageData(url) {
                try {
                    // Make an HTTP request to fetch binary image data
                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'image/*',
                            'Cache-Control': 'public,max-age=31536000'
                        }
                    });
                    const blob = await response.blob();
                    // Assuming the response data is an array of binary image data
                    return await this.blobToBase64(blob);
                } catch (error) {
                    console.error('Error fetching image data:', error);
                }
            },
            blobToBase64(blob) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => {
                        resolve(reader.result);
                    };
                    reader.onerror = reject;
                    reader.readAsDataURL(blob);
                });
            },
        }
    })
    courses.mount('#course-library');
</script>