import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor, HttpResponse
} from '@angular/common/http';
import { Observable } from 'rxjs';
import {AuthService} from "../_services/auth/auth.service";
import {environment} from "../../environments/environment";
import {map} from "rxjs/operators";

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

  constructor(public auth: AuthService) {}
  intercept(request: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    if (request.headers.get("skip") || request.url.includes("translation") || this.auth.login_status !== this.auth.AUTH_STATUSES.loggedIn){
      if(this.auth.login_status !== this.auth.AUTH_STATUSES.loggedIn && !request.url.includes("translation")){
        request = request.clone({
          setHeaders: {
            'Content-Type': 'application/json',
            'X-Api-Key': environment.heiapply_api_key
          }
        });
      }
      return next.handle(request).pipe(
        map((event: HttpEvent<any>) => {
            return event;
          })
        );
      }
    request = request.clone({
      setHeaders: {
        'Content-Type': 'application/json',
        'X-Api-Key': environment.heiapply_api_key,
        Authorization: `Bearer ${this.auth.user.token.token}`
      }
    });
    return next.handle(request).pipe(
      map((event: HttpEvent<any>) => {
        return event;
      })
    );
  }
}
