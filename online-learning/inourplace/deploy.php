<?php

//require('engine/admin/inc/lib.inc.php');
$env_file = file(__DIR__ . '/../../.env');
if (!function_exists('env')) {
  function env($key, $default = null)
  {
    global $env_file;
    $value = '';
    foreach ($env_file as $config) {
      $config = explode('=', $config);
      if (strtolower($config[0]) == strtolower($key)) {
        $value = trim(str_replace("\"", "", $config[1]));
        break;
      }
    }
    return $value ?: $default;
  }
}
date_default_timezone_set('Europe/London');

class Deploy {

  /**
   * A callback function to call after the deploy has finished.
   *
   * @var callback
   */
  public $post_deploy;

  /**
   * The name of the file that will be used for logging deployments. Set to
   * FALSE to disable logging.
   *
   * @var string
   */
  private $_log = 'deployments.log';

  /**
   * The timestamp format used for logging.
   *
   * @link    http://www.php.net/manual/en/function.date.php
   * @var     string
   */
  private $_date_format = 'Y-m-d H:i:sP';

  /**
   * The name of the branch to pull from.
   *
   * @var string
   */
  private $_branch = 'hybridmaster';


  /**
   * The name of the remote to pull from.
   *
   * @var string
   */
  private $_remote = 'origin';

  /**
   * The directory where your website and git repository are located, can be
   * a relative or absolute path
   *
   * @var string
   */
  private $_directory;

  /**
   * Sets up defaults.
   *
   * @param  string  $directory  Directory where your website is located
   * @param  array   $data       Information about the deployment
   */
  public function __construct($directory, $options = array())
  {
    // Determine the directory path
    $this->_directory = realpath($directory).DIRECTORY_SEPARATOR;

    $available_options = array('log', 'date_format', 'branch', 'remote');

    foreach ($options as $option => $value)
    {
      if (in_array($option, $available_options))
      {
        $this->{'_'.$option} = $value;
      }
    }

    $this->log('Attempting deployment...');
  }

  /**
   * Writes a message to the log file.
   *
   * @param  string  $message  The message to write
   * @param  string  $type     The type of log message (e.g. INFO, DEBUG, ERROR, etc.)
   */
  public function log($message, $type = 'INFO')
  {
    echo "<pre>";
    if ($this->_log)
    {
      //        // Set the name of the log file
      //        $filename = $this->_log;

      //        if ( ! file_exists($filename))
      //        {
      //            // Create the log file
      //            file_put_contents($filename, '');

      //            // Allow anyone to write to log files
      //            chmod($filename, 0666);
      //        }

      //        // Write the message into the log file
      //        // Format: time --- type: message
      //        file_put_contents($filename, date($this->_date_format).' --- '.$type.': '.$message.PHP_EOL, FILE_APPEND);
      echo "$message\n";
    }
    echo "</pre>";
  }

  /**
   * Executes the necessary commands to deploy the website.
   */
  public function execute()
  {
    try
    {
      exec("export PATH=/usr/bin", $npm_i_output, $ret_val);
      $this->log('Installing NPM packages');
      exec("export PATH=/usr/bin npm -v", $ng_output, $ret_val);
      $this->log(implode(' <br>', $ng_output));
      exec("npm i ", $npm_i_output, $ret_val);
      if($ret_val==0){
        $this->log(implode(' <br>', $npm_i_output));
      }else{
        $this->log(implode(' <br>', $npm_i_output));
        $this->log("Command Failed With Error: " . $ret_val);
      }
      $this->log('Building Angular App');
      exec("ng build --configuration=".env('APP_ENV')." 2>&1", $ng_build_output, $ret_val);
      if($ret_val==0) {
        $this->log(implode(' <br>', $ng_build_output));
        $this->log('Deployment successful.');
      }else{
        $this->log(implode(' <br>', $ng_build_output));
        $this->log("Command Failed With Error: " . $ret_val);
      }
      if (is_callable($this->post_deploy))
      {
        call_user_func($this->post_deploy, $this->_data);
      }

    }
    catch (Exception $e)
    {
      $this->log($e, 'ERROR');
    }
  }

}

//////////////
//get server name and process
$svn = explode(".",$_SERVER['SERVER_NAME']);
if($svn[0] =="heiapplylocal"){
  $svn = explode(".",$_SERVER['HTTP_HOST']);
}
$new_server_name=$svn[1].'.'.$svn[2];

if($svn[3]){
  $new_server_name = $new_server_name.'.'.$svn[3];
}

// This is just an example
if ($new_server_name == 'heiapply.co.uk'){
  //Dev Server
  $options['branch'] = 'UAT';
  $domain = "heiapply.co.uk";

}elseif ($new_server_name == 'heiapplylocal.co.uk'){
  //Dev Server
  $options['branch'] = 'hybriddevelop';
  $domain = "heiapplylocal.co.uk";

}elseif ($new_server_name == 'hub-apply.com'){
  //Dev Server
  $options['branch'] = 'hybridmaster';
  $domain = "hub-apply.com";

}elseif ($new_server_name == 'eduagentcrm.com'){
  //Dev Server
  $options['branch'] = 'hybridmaster';
  $domain = "eduagentcrm.com";

}elseif ($new_server_name == 'applicatalyst.co.uk'){
  //Hotfix Server
  $options['branch'] = 'uat3';
  $domain = "applicatalyst.co.uk";

}elseif ($new_server_name == 'applicatalyst.com'){
  //Dev Server
  $options['branch'] = 'hybriddevelop';
  $domain = "applicatalyst.com";
}else{
  //Dev Server
  $options['branch'] = 'hybridmaster';
  $domain = "heiapply.com";
}


echo $domain;

$deploy = new Deploy('/var/www/vhosts/'.$domain.'/httpdocs', $options);

// $deploy->post_deploy = function() use ($deploy) {
//   // hit the wp-admin page to update any db changes
//   exec('curl http://www.foobar.com/wp-admin/upgrade.php?step=upgrade_db');
//   $deploy->log('Updating wordpress database... ');
// };

$deploy->execute();


?>
