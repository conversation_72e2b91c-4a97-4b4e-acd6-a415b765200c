<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="pt_sansbold_italic" horiz-adv-x="545" >
<font-face units-per-em="1000" ascent="750" descent="-250" />
<missing-glyph horiz-adv-x="253" />
<glyph unicode="&#xfb01;" horiz-adv-x="526" d="M180 0q-8 -51 -20.5 -90t-33 -65t-51 -39.5t-74.5 -13.5q-36 0 -70.5 5.5t-59.5 22.5l34 91q17 -7 34 -9t43 -2q32 0 49.5 25t25.5 75l60 397h-66l21 103h65l6 28q20 95 76 138.5t144 43.5q66 0 114.5 -11t75.5 -29l-50 -93q-22 14 -56.5 22.5t-77.5 8.5 q-28 0 -46.5 -7.5t-30.5 -22t-19.5 -34.5t-12.5 -44h253l-75 -500h-123l60 397h-135z" />
<glyph unicode="&#xfb02;" horiz-adv-x="559" d="M118 397h-66l21 103h65l6 28q20 94 73 139.5t158 45.5q22 0 50 -2t55 -4.5t50.5 -5.5t36.5 -6l-90 -533q-2 -11 -3.5 -19.5t-1.5 -16.5q0 -35 27 -35q11 0 22.5 2t28.5 8l-4 -89q-9 -4 -22 -8.5t-29 -8t-33.5 -5.5t-33.5 -2q-44 0 -67 17t-23 57q0 19 4 38l84 502 q-11 3 -32 5.5t-41 2.5q-44 0 -63 -30.5t-29 -79.5h72l-20 -103h-72l-61 -397q-9 -51 -21.5 -90t-32.5 -65t-50.5 -39.5t-74.5 -13.5q-36 0 -70.5 5.5t-59.5 22.5l34 91q17 -7 34 -9t43 -2q32 0 49.5 25t25.5 75z" />
<glyph horiz-adv-x="1000" />
<glyph horiz-adv-x="1000" />
<glyph unicode="&#xd;" horiz-adv-x="253" />
<glyph unicode=" "  horiz-adv-x="253" />
<glyph unicode="&#x09;" horiz-adv-x="253" />
<glyph unicode="&#xa0;" horiz-adv-x="253" />
<glyph unicode="!" horiz-adv-x="283" d="M217 700h128l-70 -329l-59 -163h-78l9 163zM74 66q0 35 22 55.5t58 20.5q37 0 59 -20.5t22 -55.5t-22 -56t-59 -21q-36 0 -58 21t-22 56z" />
<glyph unicode="&#x22;" horiz-adv-x="354" d="M70 484zM116 700h108l-79 -216h-75zM256 700h108l-79 -216h-75z" />
<glyph unicode="#" d="M318 205h-89l-36 -154h-107l36 154h-74l22 97h75l25 106h-72l22 97h73l35 144h107l-35 -144h89l35 144h107l-35 -144h72l-25 -97h-70l-25 -106h69l-24 -97h-68l-36 -154h-107zM252 302h89l25 106h-89z" />
<glyph unicode="$" d="M192 -10q-52 7 -84 23t-49 30l47 97q14 -10 42.5 -23t66.5 -20l47 221q-23 15 -45 32.5t-39.5 40t-28 51t-10.5 66.5q0 48 16.5 84t45 61t65.5 39t79 19l20 89h99l-19 -90q38 -5 67 -14.5t50 -20.5l-50 -102q-14 8 -38 16t-53 13l-43 -203q23 -16 46 -34t41 -41.5t29 -53 t11 -67.5q0 -48 -16 -85.5t-44.5 -64.5t-68 -43t-86.5 -21l-18 -89h-99zM272 94q53 4 78 31t25 63q0 32 -18 54t-45 42zM364 606q-48 -2 -71.5 -25t-23.5 -57q0 -29 16.5 -49.5t42.5 -40.5z" />
<glyph unicode="%" horiz-adv-x="804" d="M55 0zM55 499q0 54 18.5 94t48.5 66.5t66.5 39.5t72.5 13q66 0 100.5 -30.5t34.5 -89.5q0 -57 -17 -98.5t-46 -68.5t-66.5 -40t-77.5 -13q-62 0 -98 34.5t-36 92.5zM165 505q-1 -22 10 -35.5t32 -13.5q33 0 56 34.5t23 88.5q0 49 -43 49q-17 0 -31 -10t-25 -26.5t-17 -39 t-5 -47.5zM420 128q0 54 18.5 94t48.5 66.5t66.5 39.5t72.5 13q66 0 100.5 -30.5t34.5 -89.5q0 -57 -17 -98.5t-46 -68.5t-66.5 -40t-77.5 -13q-62 0 -98 34.5t-36 92.5zM530 134q-1 -22 10 -35.5t32 -13.5q33 0 56 34.5t23 88.5q0 49 -43 49q-17 0 -31 -10t-25 -26.5 t-17 -39t-5 -47.5zM656 714l73 -66l-576 -662l-73 69z" />
<glyph unicode="&#x26;" horiz-adv-x="783" d="M73 162q0 46 18.5 87t49 75t69 61.5t79.5 47.5q-17 33 -21.5 60t-4.5 54q0 33 11 63t35 53t61 37t88 14q40 0 65.5 -10t40.5 -26.5t20.5 -37t5.5 -40.5q0 -51 -40.5 -97.5t-115.5 -87.5q22 -54 45.5 -99t55.5 -89q26 25 52 62.5t48 76.5l79 -50q-10 -18 -25 -39.5 t-31.5 -43.5t-33 -41.5t-29.5 -33.5q24 -29 52.5 -54.5t56.5 -37.5l-87 -96q-15 6 -31 17.5t-31.5 25t-29.5 28.5t-24 29q-43 -33 -99.5 -58.5t-131.5 -25.5q-46 0 -82.5 13.5t-62 37t-39 55.5t-13.5 70zM443 149q-37 53 -66 105.5t-48 99.5q-59 -40 -92.5 -80.5 t-33.5 -87.5q0 -42 27 -66t80 -24q38 0 74.5 16.5t58.5 36.5zM384 553q0 -22 3.5 -37.5t14.5 -39.5q42 25 65 49t23 51q0 19 -9 30t-34 11q-31 0 -47 -19t-16 -45z" />
<glyph unicode="'" horiz-adv-x="218" d="M137 700h108l-79 -216h-75z" />
<glyph unicode="(" horiz-adv-x="310" d="M157 -218q-29 38 -47 77.5t-29 78.5t-15 76.5t-4 72.5q0 96 23 186.5t61.5 171t89.5 148.5t107 119l64 -54q-47 -52 -87.5 -116.5t-70.5 -137.5t-47.5 -151.5t-17.5 -158.5q0 -63 9 -124.5t41 -133.5z" />
<glyph unicode=")" horiz-adv-x="310" d="M183 711q45 -60 65.5 -135.5t20.5 -166.5q0 -96 -22.5 -187t-61 -172t-89 -150t-106.5 -120l-67 55q45 50 86 115.5t71.5 139.5t48.5 152t18 153q0 83 -12 146.5t-39 113.5z" />
<glyph unicode="*" horiz-adv-x="356" d="M182 731l28 -46l15 -59l15 55l28 49l63 -35l-30 -52l-47 -43l64 16h57v-73h-54l-61 16l50 -47l25 -42l-64 -37l-27 46l-20 64l-13 -58l-29 -49l-65 37l30 49l45 37l-55 -16h-59v74h59l59 -17l-50 44l-28 50z" />
<glyph unicode="+" horiz-adv-x="505" d="M58 394h162v169h117v-169h163v-112h-163v-171h-117v171h-162v112z" />
<glyph unicode="," horiz-adv-x="216" d="M60 -5q-17 1 -32 17t-15 42q0 40 25.5 62t60.5 22t52.5 -23t16.5 -53q-1 -49 -20 -85.5t-45.5 -62t-55 -41t-47.5 -21.5l-26 54q29 11 53 36t33 53z" />
<glyph unicode="-" horiz-adv-x="343" d="M81 340h239l-25 -104h-239z" />
<glyph unicode="." horiz-adv-x="251" d="M33 66q0 35 22 55.5t58 20.5q37 0 59 -20.5t22 -55.5t-22 -56t-59 -21q-36 0 -58 21t-22 56z" />
<glyph unicode="/" horiz-adv-x="388" d="M432 712l86 -46l-510 -808l-85 48z" />
<glyph unicode="0" d="M55 229q0 104 23.5 192.5t65.5 153.5t99.5 102t125.5 37q39 0 73.5 -12t60.5 -40t41 -75t15 -116q0 -93 -21.5 -180.5t-62 -155t-99 -108.5t-131.5 -41q-49 0 -85 19t-59 51.5t-34.5 77t-11.5 95.5zM179 230q0 -41 7 -67t18.5 -41t27.5 -20.5t34 -5.5q35 0 65.5 33 t53.5 86t36.5 119.5t13.5 134.5q0 31 -3.5 56t-13 42.5t-25.5 27t-41 9.5q-33 0 -64 -31t-55.5 -82.5t-39 -119.5t-14.5 -141z" />
<glyph unicode="1" d="M81 110h132l85 395l28 68l-58 -59l-97 -62l-39 77l257 183h70l-128 -602h129l-23 -110h-379z" />
<glyph unicode="2" d="M540 560q0 -56 -26 -113.5t-66 -112.5t-88 -104t-91 -88l-59 -41v-5l70 14h179l-23 -110h-400l14 67q28 26 66 61.5t79 76t80.5 84t70.5 85.5t50.5 81t19.5 71q0 33 -17 54t-65 21q-31 0 -63.5 -14.5t-64.5 -37.5l-30 94q48 35 96.5 53t112.5 18q74 0 114.5 -42.5 t40.5 -111.5z" />
<glyph unicode="3" d="M193 389l147 165l64 45l-76 -9h-159l24 110h363l-15 -71l-171 -190l-48 -27v-5l40 6q20 -2 42.5 -12t41.5 -29.5t31.5 -50t12.5 -74.5q0 -57 -18.5 -105t-55.5 -82.5t-92 -54t-127 -19.5q-43 0 -87 8t-69 23l57 111q20 -13 49 -21t67 -8q39 0 67.5 11t47.5 29t28 41.5 t9 47.5q0 48 -25.5 70t-92.5 22h-70z" />
<glyph unicode="4" d="M535 195h-105l-41 -195h-115l41 195h-287l15 74l393 436h102l-86 -407h105zM370 452l26 84h-3l-48 -72l-126 -135l-55 -38l62 7h111z" />
<glyph unicode="5" d="M201 99q42 0 72 12t49.5 32t29 46t9.5 55q0 50 -30.5 71t-101.5 21l-77 -3l77 367h327l-25 -122h-220l-29 -138l37 4q40 0 70.5 -13t51.5 -36t32 -55t11 -69q0 -138 -78 -211.5t-224 -73.5q-20 0 -41 2.5t-40.5 6.5t-36 9.5t-28.5 11.5l56 106q20 -10 45.5 -16.5 t63.5 -6.5z" />
<glyph unicode="6" d="M516 255q0 -54 -16 -103t-48.5 -85.5t-81.5 -58.5t-114 -22q-32 0 -66.5 11.5t-63 38t-46.5 69.5t-18 106q0 102 38 190.5t100.5 155t142.5 107.5t165 50l7 -103q-50 -8 -98 -28.5t-89.5 -52t-73.5 -71.5t-49 -86q27 29 66.5 44t81.5 15q77 0 120 -48.5t43 -128.5z M394 232q0 40 -20.5 65t-68.5 25q-38 0 -71 -17t-50 -43q-2 -10 -3.5 -22.5t-1.5 -20.5q0 -54 22.5 -88.5t69.5 -34.5q57 0 90 38t33 98z" />
<glyph unicode="7" d="M62 0l329 539l51 49l-58 -10h-235l25 122h430l-8 -38l-405 -662h-129z" />
<glyph unicode="8" d="M54 153q0 38 13 70.5t35 59t51 46.5t61 32q-14 13 -26.5 26.5t-22 30.5t-15 39.5t-5.5 52.5q0 49 18.5 87.5t50.5 64t74 39t89 13.5q84 0 128 -43.5t44 -111.5q0 -35 -11.5 -63.5t-31 -52.5t-45 -43.5t-53.5 -34.5q45 -35 64.5 -74.5t19.5 -85.5q0 -51 -18.5 -91.5 t-51.5 -69t-79 -43.5t-101 -15q-39 0 -73.5 11t-60 32t-40 52t-14.5 72zM374 190q0 23 -7.5 41t-20 32.5t-29.5 27t-35 23.5q-51 -26 -81 -60.5t-30 -76.5q0 -35 21 -58t69 -23q22 0 42.5 6t36.5 17.5t25 29t9 41.5zM263 511q0 -35 22 -59.5t56 -44.5q18 13 34 28t28.5 31 t20 33t7.5 33q0 36 -18 54t-51 18q-42 0 -70.5 -24t-28.5 -69z" />
<glyph unicode="9" d="M99 456q0 60 20 108t55.5 81t83 51t103.5 18q42 0 78 -13t62.5 -40.5t42 -69t15.5 -98.5q0 -80 -20.5 -147.5t-55 -122.5t-80 -98t-95.5 -73t-101.5 -46.5t-97.5 -19.5l-6 102q55 10 103.5 31.5t87.5 51t67.5 66t44.5 75.5q-29 -23 -63 -34.5t-82 -11.5q-28 0 -57 10.5 t-52 33t-38 59t-15 87.5zM221 475q0 -29 7.5 -48t20.5 -31t28.5 -17t32.5 -5q38 0 71 12t52 32q3 11 5.5 31t2.5 29q0 57 -22.5 92.5t-73.5 35.5q-24 0 -46 -7.5t-39.5 -23.5t-28 -41t-10.5 -59z" />
<glyph unicode=":" horiz-adv-x="314" d="M165 428q0 35 22 55.5t58 20.5q37 0 59 -20.5t22 -55.5t-22 -56t-59 -21q-36 0 -58 21t-22 56zM90 78q0 35 22 55.5t58 20.5q37 0 59 -20.5t22 -55.5t-22 -56t-59 -21q-36 0 -58 21t-22 56z" />
<glyph unicode=";" horiz-adv-x="307" d="M138 -5q-17 1 -32 17t-15 42q0 40 25.5 62t60.5 22t52.5 -23t16.5 -53q-1 -49 -20 -85.5t-45.5 -62t-55 -41t-47.5 -21.5l-26 54q29 11 53 36t33 53zM158 428q0 35 22 55.5t58 20.5q37 0 59 -20.5t22 -55.5t-22 -56t-59 -21q-36 0 -58 21t-22 56z" />
<glyph unicode="&#x3c;" horiz-adv-x="505" d="M51 285v70l386 231l57 -90l-224 -139l-91 -35l90 -31l230 -137l-57 -90z" />
<glyph unicode="=" horiz-adv-x="505" d="M58 187zM58 490h442v-112h-442v112zM58 299h442v-112h-442v112z" />
<glyph unicode="&#x3e;" horiz-adv-x="505" d="M508 365v-70l-387 -231l-56 91l223 138l91 36l-90 30l-229 138l56 89z" />
<glyph unicode="?" horiz-adv-x="430" d="M127 208q6 54 24.5 92.5t43 67t52 50t50 41.5t37.5 41t15 49q0 27 -15.5 42t-55.5 15q-31 0 -68 -13.5t-65 -32.5l-31 94q41 25 89 42.5t117 17.5q84 0 123 -35.5t39 -95.5q0 -53 -16 -89.5t-41 -64t-55 -49t-57.5 -45.5t-49.5 -54t-30 -73h-106zM82 66q0 35 22 55.5 t58 20.5q37 0 59 -20.5t22 -55.5t-22 -56t-59 -21q-36 0 -58 21t-22 56z" />
<glyph unicode="@" horiz-adv-x="1059" d="M706 484h70l-49 -271q-2 -12 -3 -22.5t-1 -18.5q0 -48 37 -48q26 0 51.5 12t45.5 37t32 63t12 90q0 75 -25 128.5t-68.5 88t-102.5 50.5t-126 16q-78 0 -145.5 -28t-117 -77.5t-77.5 -117t-28 -146.5q0 -78 23.5 -140t69 -106t112 -67.5t152.5 -23.5q29 0 66 7.5t67 21.5 l31 -96q-42 -20 -84 -28t-97 -8q-98 0 -180.5 28t-142.5 83t-94 136.5t-34 188.5q0 109 39 197t105 150t153 95.5t184 33.5q92 0 171 -26.5t137 -76t91 -121t33 -162.5q0 -64 -23 -120t-62 -97t-91.5 -65t-112.5 -24q-25 0 -46.5 5.5t-36.5 18.5t-22 34t-4 52h-4 q-15 -21 -32 -41t-38 -35.5t-46 -24.5t-55 -9q-25 0 -46.5 10.5t-37.5 29.5t-25.5 46t-9.5 60q0 62 20 120.5t54 103.5t79.5 72.5t96.5 27.5q35 0 59 -10.5t45 -27.5zM643 363q-14 11 -28 17.5t-34 6.5q-30 0 -55.5 -17.5t-44 -44.5t-29 -60.5t-10.5 -65.5q0 -33 14 -54.5 t47 -21.5q14 0 30 8.5t31 23t28.5 32.5t24.5 38z" />
<glyph unicode="A" horiz-adv-x="582" d="M387 155h-204l-83 -155h-138l389 705h96l91 -705h-131zM240 265h142l-16 156l1 110h-5l-40 -111z" />
<glyph unicode="B" horiz-adv-x="568" d="M166 695q19 3 43 6t51 5t55.5 3t55.5 1q46 0 85.5 -9.5t68 -29.5t45 -51.5t16.5 -75.5q0 -33 -11 -63.5t-31.5 -54.5t-49 -40t-64.5 -21v-5q19 -4 38 -14.5t34 -27t24 -39t9 -50.5q0 -64 -25 -109.5t-67 -74t-98 -42t-118 -13.5h-41.5t-54.5 1.5t-58 4.5t-53 9zM167 109 q4 -2 14.5 -3t23 -1.5t25.5 -1t22 -0.5q25 0 51.5 6t48 20t35.5 36t14 54q0 25 -11 41t-29 25.5t-41.5 13t-48.5 3.5h-64zM230 406h43q14 0 33 0.5t32 2.5q18 3 38.5 10t37 20t27.5 33t11 50q0 43 -28.5 59t-72.5 16q-26 0 -48 -1t-34 -3z" />
<glyph unicode="C" horiz-adv-x="537" d="M483 29q-67 -43 -164 -43q-66 0 -115 22t-81.5 61t-48.5 91t-16 112q0 118 33 201.5t87 137t123.5 78.5t142.5 25q57 0 97.5 -8.5t63.5 -19.5l-51 -116q-20 11 -48 16.5t-71 5.5q-51 0 -95 -21t-76 -60t-50.5 -95t-18.5 -127q0 -85 42.5 -133t113.5 -48q45 0 74.5 9.5 t55.5 23.5z" />
<glyph unicode="D" horiz-adv-x="620" d="M168 700q21 3 46 5t46.5 3t37.5 1q9 0 25.5 0.5t36.5 0.5q61 0 111 -15.5t85.5 -47.5t55 -82t19.5 -119q0 -44 -7.5 -94.5t-25.5 -101.5t-49.5 -98t-79 -83t-114 -58t-154.5 -22q-15 0 -30 0.5t-21 0.5q-20 1 -44.5 2.5l-49 3t-36.5 3.5zM174 114q4 -1 12.5 -1.5t18.5 -1 t19 -0.5h14q70 0 119 31.5t79.5 79t44 103t13.5 103.5q0 80 -33.5 120t-111.5 40q-20 0 -42 -1t-34 -3z" />
<glyph unicode="E" horiz-adv-x="503" d="M168 700h130h264l-26 -122h-264l-35 -163h241l-27 -122h-239l-37 -171h269l-25 -122h-269h-130z" />
<glyph unicode="F" horiz-adv-x="489" d="M168 700h130h264l-26 -122h-264l-37 -173h243l-25 -122h-243l-60 -283h-130z" />
<glyph unicode="G" horiz-adv-x="585" d="M369 365h210l-66 -312q-45 -35 -94.5 -51t-106.5 -16q-55 0 -101.5 19t-80.5 55t-53 89t-19 121q0 115 31.5 199t85.5 138.5t126 80.5t152 26q57 0 101.5 -9.5t67.5 -19.5l-52 -115q-21 10 -52.5 16t-76.5 6q-53 0 -98 -20t-78 -59t-51.5 -96.5t-18.5 -131.5 q0 -83 39 -130t105 -47q49 0 86 25l28 133l-102 16z" />
<glyph unicode="H" horiz-adv-x="624" d="M455 293h-243l-62 -293h-130l148 700h130l-60 -285h243l60 285h130l-148 -700h-130z" />
<glyph unicode="I" horiz-adv-x="278" d="M183 700h129l-148 -700h-129z" />
<glyph unicode="J" horiz-adv-x="324" d="M239 700h130l-109 -512q-9 -42 -23 -78.5t-37 -63.5t-57.5 -42.5t-83.5 -15.5q-28 0 -59 6t-51 17l50 112q23 -13 54 -13q21 0 35 8.5t23.5 23.5t15.5 35.5t12 45.5z" />
<glyph unicode="K" horiz-adv-x="593" d="M260 299h-47l-63 -299h-130l148 700h130l-66 -310l31 11l237 299h156l-252 -297l-60 -35l44 -39l152 -329h-145z" />
<glyph unicode="L" horiz-adv-x="495" d="M441 0h-291h-130l148 700h130l-123 -578h291z" />
<glyph unicode="M" horiz-adv-x="767" d="M613 363l44 138h-6l-66 -113l-197 -259h-41l-94 262l-18 110h-5l-10 -137l-77 -364h-123l148 700h115l105 -320l13 -80h5l46 82l236 318h125l-148 -700h-129z" />
<glyph unicode="N" horiz-adv-x="631" d="M267 366l-33 112h-5l-9 -112l-77 -366h-123l150 705h90l172 -374l31 -109h6l7 109l79 369h123l-150 -705h-90z" />
<glyph unicode="O" horiz-adv-x="644" d="M58 262q0 93 25.5 175.5t72.5 144t113.5 97t148.5 35.5q54 0 97.5 -16t74 -49.5t47.5 -84.5t17 -121q0 -94 -25.5 -177t-72 -145.5t-111 -98.5t-142.5 -36q-118 0 -181 69.5t-64 206.5zM195 266q0 -72 27.5 -115t90.5 -43q46 0 83.5 27.5t64.5 74t41.5 106.5t14.5 125 q0 79 -31 115t-85 36q-45 0 -82.5 -26t-65 -70.5t-43 -104t-15.5 -125.5z" />
<glyph unicode="P" horiz-adv-x="550" d="M167 693q42 8 89.5 12.5t95.5 4.5q51 0 94.5 -11.5t76 -35.5t50.5 -61.5t18 -89.5q0 -78 -27 -130.5t-71.5 -85t-101.5 -46t-116 -13.5h-15.5t-22 0.5t-22 1.5t-15.5 2l-50 -241h-130zM226 359q4 -1 11 -2t15.5 -1.5t16 -0.5h11.5q34 0 65.5 7.5t55.5 24t38.5 43.5 t14.5 66q0 29 -9.5 48t-25.5 29.5t-37.5 14.5t-45.5 4q-18 0 -34.5 -1.5t-27.5 -3.5z" />
<glyph unicode="Q" horiz-adv-x="644" d="M58 262q0 93 25.5 175.5t72.5 144t113.5 97t148.5 35.5q54 0 97.5 -16t74 -49.5t47.5 -84.5t17 -121q0 -94 -25.5 -177t-72 -145.5t-111 -98.5t-142.5 -36q-118 0 -181 69.5t-64 206.5zM195 266q0 -72 27.5 -115t90.5 -43q46 0 83.5 27.5t64.5 74t41.5 106.5t14.5 125 q0 79 -31 115t-85 36q-45 0 -82.5 -26t-65 -70.5t-43 -104t-15.5 -125.5zM627 -202q-48 -13 -95 -13q-48 0 -92 10.5t-84.5 22.5t-77.5 22.5t-73 10.5q-23 0 -45 -6l25 118q28 6 56 6q40 0 77.5 -10t76 -21.5t79.5 -21.5t88 -10q21 0 43.5 2.5t46.5 8.5z" />
<glyph unicode="R" horiz-adv-x="584" d="M167 693q48 9 97 13t88 4q46 0 87 -11t71 -33.5t47.5 -58t17.5 -83.5q0 -55 -16.5 -95.5t-43.5 -68t-61.5 -43t-70.5 -20.5l39 -31l97 -266h-144l-96 274l-69 13l-60 -287h-130zM230 380h53q29 0 57 5.5t49.5 20t35 38.5t13.5 61q0 34 -23 58.5t-74 24.5 q-20 0 -37.5 -1.5t-29.5 -3.5z" />
<glyph unicode="S" horiz-adv-x="510" d="M502 559q-19 12 -56 24.5t-82 12.5q-23 0 -45 -4.5t-39 -14.5t-27 -26t-10 -38q0 -29 18 -49t45.5 -37.5t59 -35.5t59 -42t45.5 -57.5t18 -82.5q0 -59 -20.5 -101.5t-58 -69.5t-90 -39.5t-116.5 -12.5q-35 0 -66.5 4t-58.5 10t-47.5 13.5t-32.5 14.5l67 116q10 -6 26 -13 t36.5 -13t44.5 -10t48 -4q28 0 53 4.5t43.5 15t29 26.5t10.5 40q0 27 -18 46t-45.5 36t-59 35.5t-59 43.5t-45.5 60.5t-18 86.5q0 58 20.5 99t55.5 67t82 38t101 12q29 0 58 -3t55 -8.5t48 -13t37 -16.5z" />
<glyph unicode="T" horiz-adv-x="547" d="M614 578h-194l-123 -578h-129l122 578h-194l26 122h517z" />
<glyph unicode="U" horiz-adv-x="602" d="M531 700h122l-95 -452q-15 -72 -39 -121.5t-58 -80.5t-80 -44.5t-106 -13.5q-58 0 -98 13.5t-65.5 38.5t-37 59t-11.5 75q0 20 2.5 41t7.5 44l94 441h129l-92 -440q-8 -38 -8 -62q0 -48 25 -68t73 -20q60 0 93 35t50 113z" />
<glyph unicode="V" horiz-adv-x="579" d="M288 290l-7 -119h4l44 119l216 410h146l-395 -705h-94l-101 705h142z" />
<glyph unicode="W" horiz-adv-x="826" d="M249 310l-14 -124h5l39 126l198 388h88l29 -390l-13 -124h5l40 126l164 388h140l-333 -705h-91l-37 389l8 107h-6l-38 -108l-199 -388h-91l-34 705h137z" />
<glyph unicode="X" horiz-adv-x="608" d="M256 357l-113 343h141l60 -202l10 -71l37 71l149 202h156l-263 -338l125 -362h-139l-72 216l-12 74l-40 -74l-161 -216h-158z" />
<glyph unicode="Y" horiz-adv-x="569" d="M231 260l-125 440h144l65 -263l-2 -74h4l31 76l175 261h149l-311 -439l-55 -261h-129z" />
<glyph unicode="Z" horiz-adv-x="518" d="M15 122l360 416l58 40h-321l25 122h461l-25 -122l-362 -419l-56 -37h321l-26 -122h-461z" />
<glyph unicode="[" horiz-adv-x="314" d="M198 700h223l-22 -109h-105l-151 -711h105l-24 -110h-222z" />
<glyph unicode="\" horiz-adv-x="424" d="M472 -95l-101 -45l-335 808l103 43z" />
<glyph unicode="]" horiz-adv-x="313" d="M145 -230h-223l24 110h105l150 711h-105l22 109h224z" />
<glyph unicode="^" horiz-adv-x="505" d="M266 705h70l185 -292h-131l-71 122l-22 70l-25 -71l-77 -121h-125z" />
<glyph unicode="_" horiz-adv-x="444" d="M-46 -117h444v-107h-444v107z" />
<glyph unicode="`" horiz-adv-x="238" d="M430 566h-69l-91 124l7 30h129z" />
<glyph unicode="a" horiz-adv-x="503" d="M444 200q-8 -38 -11.5 -77t-3.5 -74q0 -14 0.5 -26.5t1.5 -23.5h-91l-14 86h-4q-11 -18 -28.5 -35.5t-38.5 -32t-44.5 -23t-48.5 -8.5q-66 0 -97.5 43t-31.5 115q0 73 18.5 140t57.5 118t98.5 81.5t141.5 30.5q35 0 76.5 -6.5t80.5 -17.5zM209 89q18 0 34 7.5t29.5 19.5 t24 26t18.5 26l50 233q-11 5 -25.5 7.5t-28.5 2.5q-36 0 -64 -21.5t-47.5 -56t-29.5 -78t-10 -87.5q0 -32 10.5 -55.5t38.5 -23.5z" />
<glyph unicode="b" horiz-adv-x="512" d="M164 700h123l-62 -288h4q13 18 30 35.5t37.5 32t44.5 23.5t53 9q54 0 81 -38t27 -101q0 -95 -26.5 -167t-69.5 -120.5t-97 -73t-109 -24.5q-61 0 -107.5 11.5t-70.5 28.5zM160 105q11 -8 27 -11t35 -3q25 0 52 16.5t49.5 48.5t37 78.5t14.5 105.5q0 69 -40 69 q-20 0 -39.5 -11t-37.5 -27.5t-32.5 -35.5t-23.5 -36z" />
<glyph unicode="c" horiz-adv-x="412" d="M393 387q-19 11 -39.5 15t-44.5 4q-27 0 -52.5 -17.5t-46 -46t-32.5 -66.5t-12 -78q0 -51 22 -77.5t58 -26.5q30 0 55.5 9.5t45.5 21.5l23 -84q-32 -24 -74.5 -39.5t-84.5 -15.5q-48 0 -81.5 14t-54.5 38t-31 55t-10 65q0 84 24 150t63.5 111.5t90 70t103.5 24.5 q47 0 74.5 -9t49.5 -20z" />
<glyph unicode="d" horiz-adv-x="507" d="M442 176q-9 -42 -13 -78t-4 -68v-15.5t1 -15.5h-84l-14 86h-4q-11 -18 -28.5 -35.5t-38.5 -32t-44.5 -23t-46.5 -8.5q-62 0 -95.5 44t-33.5 121q0 70 19 135t53.5 115.5t84 80.5t109.5 30q29 0 48 -3.5t36 -11.5l43 203h123zM365 394q-12 8 -27.5 11.5t-41.5 3.5 q-30 0 -54.5 -20.5t-41.5 -55t-26.5 -79.5t-9.5 -94q0 -29 13 -50t37 -21q15 0 30.5 7.5t29 19.5t25 26t18.5 26z" />
<glyph unicode="e" horiz-adv-x="466" d="M402 48q-31 -26 -80 -44t-114 -18q-81 0 -127.5 47.5t-46.5 132.5q0 80 25 144.5t65.5 109.5t91.5 69.5t102 24.5q41 0 70.5 -10.5t48.5 -29t28 -42.5t9 -51q0 -41 -21 -72t-58.5 -51.5t-90.5 -31t-116 -10.5q-8 0 -15 0.5t-15 0.5q-2 -12 -3 -21.5t-1 -17.5 q0 -45 23.5 -67t66.5 -22q44 0 79 13t53 26zM176 295q28 0 59.5 2t58.5 9.5t45 22t18 39.5q0 15 -11 30.5t-46 15.5q-44 0 -76.5 -34t-47.5 -85z" />
<glyph unicode="f" horiz-adv-x="294" d="M246 397l-74 -431q-6 -37 -17 -68.5t-30.5 -55t-49.5 -37t-74 -13.5q-36 0 -70.5 5.5t-59.5 22.5l34 91q17 -7 34 -9t43 -2q32 0 49.5 25.5t26.5 82.5l63 389h-70l22 103h64l13 67q7 37 18 64.5t29.5 46t45 27.5t65.5 9q16 0 36.5 -1.5t40.5 -5t39 -8.5t34 -12l-29 -91 q-23 9 -47 12t-47 3q-32 0 -46.5 -19t-20.5 -59l-6 -33h100l-20 -103h-96z" />
<glyph unicode="g" horiz-adv-x="505" d="M407 0q-13 -59 -35 -99.5t-52.5 -65.5t-70 -36t-87.5 -11q-35 0 -62.5 5t-48.5 12t-36.5 14.5t-24.5 13.5l46 91q9 -4 21 -9.5t27.5 -11t36 -9t47.5 -3.5q50 0 79.5 31t42.5 85l19 78h-4q-23 -36 -62.5 -64.5t-89.5 -28.5q-54 0 -87.5 40.5t-33.5 108.5q0 77 22 144.5 t61.5 118t93.5 79.5t119 29q61 0 106.5 -10.5t73.5 -23.5zM371 399q-11 6 -28 8t-34 2q-32 0 -59.5 -19t-47.5 -52t-31.5 -77.5t-11.5 -95.5q0 -32 13.5 -51t35.5 -19q17 0 35 10.5t34.5 26.5t29.5 35.5t21 36.5z" />
<glyph unicode="h" horiz-adv-x="528" d="M309 0l60 284q10 44 10 73q0 26 -10 37.5t-31 11.5q-19 0 -40 -12t-40 -30t-34.5 -38.5t-23.5 -37.5l-59 -288h-123l148 700h123l-63 -288h4q13 16 31.5 34t41 33t49 25t57.5 10q48 0 74.5 -24t26.5 -85q0 -37 -12 -94l-66 -311h-123z" />
<glyph unicode="i" horiz-adv-x="256" d="M27 0zM133 500h123l-106 -500h-123zM152 642q0 33 21.5 51t54.5 18q32 0 54.5 -18t22.5 -51q0 -32 -22.5 -50.5t-54.5 -18.5q-33 0 -54.5 18.5t-21.5 50.5z" />
<glyph unicode="j" horiz-adv-x="244" d="M0 0zM123 500h124l-111 -521q-19 -90 -64 -137.5t-122 -47.5q-31 0 -62 7l24 106q25 -2 43 4.5t30.5 20.5t21 35.5t14.5 48.5zM141 642q0 33 21.5 51t54.5 18q32 0 54.5 -18t22.5 -51q0 -32 -22.5 -50.5t-54.5 -18.5q-33 0 -54.5 18.5t-21.5 50.5z" />
<glyph unicode="k" horiz-adv-x="465" d="M224 207h-40l-43 -207h-123l148 700h123l-88 -413l37 13l141 200h137l-149 -201l-60 -38l45 -38l76 -223h-132z" />
<glyph unicode="l" horiz-adv-x="272" d="M168 148q-6 -29 0.5 -43t21.5 -14q28 0 53 11l-3 -89q-18 -11 -52 -18t-67 -7q-43 0 -65.5 16.5t-22.5 61.5q0 23 6 53l123 581h123z" />
<glyph unicode="m" horiz-adv-x="767" d="M283 0l57 272q6 28 9.5 49.5t3.5 38.5q0 46 -35 46q-17 0 -34 -9t-31.5 -23t-27 -30.5t-20.5 -31.5l-64 -312h-123l106 500h96l-6 -88h4q15 16 32.5 34t39.5 33t49 25t59 10q40 0 60.5 -29t18.5 -77q15 19 34.5 38t42 34t48 24.5t52.5 9.5q46 0 70.5 -21.5t24.5 -80.5 q0 -43 -15 -112l-63 -300h-123l59 281q5 23 8 43t3 35q0 23 -8.5 35t-28.5 12q-16 0 -32.5 -9.5t-31.5 -24t-27.5 -31.5t-19.5 -32l-64 -309h-123z" />
<glyph unicode="n" horiz-adv-x="521" d="M299 0l66 310q6 28 6 49q0 22 -9 34.5t-33 12.5q-18 0 -36 -9t-34 -24t-30 -33.5t-24 -37.5l-64 -302h-123l106 500h93l-5 -88h4q13 16 30.5 34t40.5 33t51.5 25t64.5 10q45 0 72 -24.5t27 -81.5q0 -15 -1.5 -32t-6.5 -37l-72 -339h-123z" />
<glyph unicode="o" horiz-adv-x="496" d="M34 162q0 79 22.5 144t60 111t87 71.5t103.5 25.5q48 0 81 -15t53.5 -40t29.5 -57t9 -66q0 -78 -22 -142.5t-59 -110.5t-86 -71.5t-103 -25.5q-47 0 -80 15.5t-54.5 40.5t-31.5 56.5t-10 63.5zM161 179q0 -90 65 -90q24 0 47 21t41 55t28.5 77t10.5 86q0 40 -13.5 61.5 t-50.5 21.5q-24 0 -47 -20t-41 -52.5t-29 -74.5t-11 -85z" />
<glyph unicode="p" horiz-adv-x="506" d="M122 500h91l-5 -88h4q14 19 32 37.5t40 32.5t47 23t54 9q51 0 82 -35t31 -110q0 -85 -22.5 -155.5t-60 -121t-88 -78.5t-105.5 -28q-26 0 -49 5t-34 13l-43 -204h-122zM160 106q25 -17 56 -17t59 21t49.5 56t34 81.5t12.5 97.5q0 29 -13.5 47.5t-36.5 18.5q-18 0 -35 -9 t-32 -22.5t-27 -30.5t-21 -33z" />
<glyph unicode="q" horiz-adv-x="501" d="M356 -200h-120l61 278h-4q-10 -17 -24.5 -33t-33 -29.5t-40.5 -21.5t-47 -8q-33 0 -55.5 12.5t-36.5 34t-20.5 49t-6.5 57.5q0 76 24.5 144t67 119t99 80.5t119.5 29.5q48 0 93.5 -10.5t70.5 -24.5zM364 399q-14 6 -29 8t-30 2q-30 0 -57.5 -21.5t-47.5 -57t-32 -81.5 t-12 -94q0 -31 11.5 -48.5t31.5 -17.5q19 0 38 11.5t35.5 29t30 38t21.5 38.5z" />
<glyph unicode="r" horiz-adv-x="343" d="M215 500l1 -88h4q22 40 53.5 68t79.5 28q21 0 50 -8l-31 -107q-10 3 -19 5t-19 2q-41 0 -75.5 -26.5t-52.5 -61.5l-65 -312h-123l106 500h91z" />
<glyph unicode="s" horiz-adv-x="396" d="M38 117q20 -11 51.5 -19.5t66.5 -8.5t53.5 13.5t19.5 41.5q0 20 -12 34.5t-30.5 27.5t-39.5 26.5t-39.5 31t-30.5 41.5t-12 58q0 33 13 61t36.5 48t57 31t75.5 11q58 0 96 -8.5t60 -21.5l-34 -94q-20 9 -48.5 15t-59.5 6q-32 0 -50.5 -11t-18.5 -35q0 -16 12 -28.5 t30.5 -25.5t39.5 -26.5t39.5 -32.5t30.5 -43.5t12 -58.5q0 -83 -54 -123.5t-144 -40.5q-51 0 -93.5 9.5t-65.5 23.5z" />
<glyph unicode="t" horiz-adv-x="320" d="M64 500h65l20 94l131 37l-28 -131h117l-23 -103h-116l-49 -226q-6 -27 -6 -44q0 -22 10 -30t30 -8q19 0 34.5 4t32.5 13v-92q-11 -6 -26.5 -11.5t-33.5 -9t-37 -5.5t-35 -2q-51 0 -78.5 23.5t-27.5 71.5q0 10 1.5 21t3.5 23l58 272h-67z" />
<glyph unicode="u" horiz-adv-x="512" d="M241 500l-65 -309q-6 -30 -6 -50t8 -31t28 -11q17 0 34.5 8.5t33.5 22t29.5 30t22.5 32.5l64 308h123l-63 -300q-5 -20 -8.5 -47t-6.5 -55.5t-4 -54.5t0 -43h-99l-5 90h-4q-14 -19 -32.5 -37.5t-41 -33.5t-49 -24t-56.5 -9q-47 0 -76 23.5t-29 86.5q0 37 10 81l69 323 h123z" />
<glyph unicode="v" horiz-adv-x="448" d="M209 238l2 -77h3l30 79l137 260h139l-306 -505h-76l-98 505h136z" />
<glyph unicode="w" horiz-adv-x="678" d="M445 500l32 -256l-1 -83h5l30 84l116 255h120l-255 -505h-91l-35 280v63h-4l-26 -64l-150 -279h-94l-46 505h126l16 -251l-6 -89h5l34 90l132 250h92z" />
<glyph unicode="x" horiz-adv-x="496" d="M166 256l-90 244h139l38 -107l13 -70l44 70l87 107h143l-195 -240l100 -260h-136l-48 120l-15 74l-45 -74l-99 -120h-143z" />
<glyph unicode="y" horiz-adv-x="452" d="M216 219l1 -78h6l27 79l136 280h131l-240 -449q-30 -56 -56.5 -103t-53.5 -81t-56 -53t-62 -19q-44 0 -66 15l33 98q6 -3 12.5 -4t12.5 -1q24 0 49 23t50 74l-94 500h141z" />
<glyph unicode="z" horiz-adv-x="435" d="M9 108l225 239l71 45h-236l24 108h372l-23 -108l-230 -243l-64 -41h234l-23 -108h-372z" />
<glyph unicode="{" horiz-adv-x="356" d="M122 96q11 51 -4.5 70t-52.5 19l22 100q36 0 60 20.5t33 64.5l44 209q11 54 42 88t85 34h114l-23 -110h-48q-26 0 -40.5 -14t-21.5 -46l-41 -195q-9 -44 -34 -66.5t-52 -27.5l-2 -10q25 -4 39.5 -30t5.5 -68l-41 -194q-7 -32 2 -46t36 -14h47l-24 -110h-114 q-50 0 -69 31.5t-6 89.5z" />
<glyph unicode="|" horiz-adv-x="232" d="M94 700h106v-830h-106v830z" />
<glyph unicode="}" horiz-adv-x="359" d="M267 375q-11 -51 5 -70t53 -19l-21 -100q-76 0 -94 -85l-45 -209q-11 -54 -43.5 -88t-86.5 -34h-110l24 110h48q25 0 39 13.5t21 46.5l42 195q9 44 33.5 66.5t52.5 27.5l3 10q-26 4 -41 30t-6 68l41 194q7 31 -1.5 45.5t-35.5 14.5h-46l22 110h113q50 0 69.5 -31.5 t7.5 -89.5z" />
<glyph unicode="~" horiz-adv-x="505" d="M45 378q47 38 84.5 52.5t69.5 14.5q29 0 54 -8.5t48 -18t45.5 -18t45.5 -8.5q18 0 37 6t40 23l47 -103q-36 -27 -66 -38t-56 -11q-28 0 -52 9t-46.5 20t-45.5 20t-49 9q-24 0 -51.5 -10.5t-60.5 -38.5z" />
<glyph unicode="&#xa1;" horiz-adv-x="283" d="M0 0zM118.014 -199.957h-127.992l69.9951 328.979l58.9971 162.99h77.9951l-9 -162.99zM261.005 434.004q0 -34.998 -21.999 -55.4961q-21.999 -20.499 -57.9961 -20.499q-36.998 0 -58.9971 20.499q-21.998 20.498 -21.998 55.4961t21.998 55.9971 q21.999 20.998 58.9971 20.998q35.9971 0 57.9961 -20.998q21.999 -20.999 21.999 -55.9971z" />
<glyph unicode="&#xa2;" d="M447 41q-21 -17 -51.5 -29.5t-62.5 -18.5l-19 -93h-115l19 91q-33 7 -57 23t-39 38.5t-22 49.5t-7 56q0 75 19 135.5t51.5 105.5t75.5 73t90 37l19 91h115l-20 -92q46 -9 72 -23l-45 -98q-11 6 -25.5 10t-30.5 6l-65 -305q22 5 41.5 12t32.5 15zM327 398 q-22 -9 -42.5 -27t-36 -44t-25 -58.5t-9.5 -70.5q0 -42 14 -65t36 -32z" />
<glyph unicode="&#xa3;" d="M113 399h70q-5 20 -9 43.5t-4 55.5q0 56 21.5 97t57.5 67.5t82 39t95 12.5q45 0 85.5 -9.5t63.5 -25.5l-42 -97q-17 9 -46.5 16.5t-69.5 7.5q-24 0 -46.5 -8.5t-39 -24.5t-26.5 -38t-10 -50q0 -29 4 -48.5t10 -37.5h140l-23 -103h-95q0 -4 0.5 -6.5t0.5 -6.5 q0 -50 -21 -91t-46 -63l-47 -30l-1 -6l67 15h218l-24 -108h-448l24 109h2q30 1 57.5 14t48.5 35.5t33.5 52.5t12.5 65v10t-1 10h-117z" />
<glyph unicode="&#xa4;" d="M115 622l78 -77l19 -37q20 15 47.5 22.5t57.5 7.5q29 0 57.5 -7.5t47.5 -21.5l20 36l78 77l77 -80l-77 -78l-37 -16q14 -21 20 -47.5t6 -55.5q0 -30 -6.5 -56.5t-19.5 -44.5l37 -16l77 -78l-77 -80l-78 77l-18 37q-19 -14 -48.5 -22t-58.5 -8q-30 0 -57.5 7.5t-47.5 21.5 l-19 -36l-78 -77l-76 80l77 78l36 17q-12 21 -19 45.5t-7 54.5q0 29 7.5 54t20.5 48l-38 17l-77 78zM235 346q0 -38 23 -62t60 -24t59.5 24t22.5 62q0 36 -22.5 61t-59.5 25t-60 -25t-23 -61z" />
<glyph unicode="&#xa5;" d="M112 337h85l-103 363h152l67 -263l-3 -74h5l30 76l173 261h142l-257 -363h87l-17 -80h-125l-12 -57h125l-17 -80h-125l-25 -120h-130l25 120h-124l17 80h124l12 57h-123z" />
<glyph unicode="&#xa6;" horiz-adv-x="227" d="M60 213h107v-343h-107v343zM167 357h-107v343h107v-343z" />
<glyph unicode="&#xa7;" horiz-adv-x="505" d="M85 123q20 -11 50.5 -22.5t71.5 -11.5q35 0 56 13.5t21 37.5q0 16 -15.5 27t-38.5 21t-50.5 21.5t-50.5 27.5t-38.5 39.5t-15.5 58.5q0 45 24 76.5t56 54.5l48 16q-22 16 -33 38.5t-12 47.5q0 30 14.5 57t40.5 46.5t62.5 31t79.5 11.5q56 0 93 -11.5t63 -25.5l-37 -95 q-20 11 -53.5 20t-66.5 9q-35 0 -54 -14.5t-19 -37.5q0 -17 15.5 -27.5t38.5 -19t50 -17.5t50 -24t38.5 -38.5t15.5 -59.5q0 -48 -24.5 -82.5t-54.5 -55.5l-51 -16q23 -16 34 -36.5t10 -52.5q-1 -69 -55 -106.5t-143 -37.5q-55 0 -97 13.5t-66 30.5zM297 270q23 12 44 32 t21 48q0 17 -6 28t-18 20t-31 16.5t-44 16.5q-22 -12 -41.5 -32t-19.5 -48q0 -31 22.5 -48t72.5 -33z" />
<glyph unicode="&#xa8;" horiz-adv-x="245" d="M180 651q0 33 18.5 51.5t47.5 18.5t47.5 -18.5t18.5 -51.5q0 -32 -18.5 -50t-47.5 -18t-47.5 18t-18.5 50zM388 652q0 32 18.5 50.5t47.5 18.5t47.5 -18.5t18.5 -50.5t-18.5 -50.5t-47.5 -18.5t-47.5 18.5t-18.5 50.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="836" d="M80 290q0 83 28 149.5t76.5 113.5t114.5 72.5t142 25.5t141.5 -25.5t114.5 -72.5t77 -113.5t28 -149.5t-28 -149.5t-77 -113.5t-114.5 -72.5t-141.5 -25.5q-83 0 -150 25.5t-114 72.5t-72 113.5t-25 149.5zM178 290q0 -62 19.5 -112t55 -84.5t83.5 -53t105 -18.5 t105.5 18.5t84 53t55 84.5t19.5 112t-19.5 112t-55 84.5t-84 53t-105.5 18.5t-105 -18.5t-83.5 -53t-55 -84.5t-19.5 -112zM566 131q-22 -14 -55.5 -22.5t-71.5 -8.5q-81 0 -123.5 51t-42.5 139q0 91 45.5 140.5t120.5 49.5q37 0 65.5 -8.5t54.5 -23.5l-32 -87 q-18 9 -32.5 12t-28.5 3q-29 0 -46 -19t-17 -67q0 -86 70 -86q19 0 34 3.5t30 11.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="401" d="M121 674q28 14 72 24t101 10q60 0 84.5 -25t24.5 -62q0 -25 -7 -56.5t-15.5 -64.5t-15 -64.5t-6.5 -55.5h-90l-3 46h-3q-14 -17 -41 -33.5t-69 -16.5q-23 0 -40.5 7t-29 19t-17.5 27.5t-6 31.5q0 34 15 56.5t40 36.5t58 20t69 6q12 0 24 -0.5t24 -1.5q8 25 -1 40.5 t-42 15.5q-38 0 -64.5 -7t-47.5 -15zM277 524q-8 1 -16.5 1.5t-16.5 0.5q-30 0 -51 -9t-21 -33q0 -13 7.5 -21.5t21.5 -8.5q30 0 44.5 11.5t24.5 23.5z" />
<glyph unicode="&#xab;" horiz-adv-x="465" d="M30 259l200 230l60 -64l-106 -126l-55 -39l36 -37l60 -123l-84 -62zM216 259l200 230l60 -64l-106 -126l-55 -39l36 -37l60 -123l-84 -62z" />
<glyph unicode="&#xac;" horiz-adv-x="527" d="M66 412h442v-226h-120v114h-322v112z" />
<glyph unicode="&#xad;" horiz-adv-x="343" d="M81 340h239l-25 -104h-239z" />
<glyph unicode="&#xae;" horiz-adv-x="693" d="M117 451q0 67 22.5 119.5t61.5 88.5t90.5 55t108.5 19q59 0 110.5 -19t89.5 -55t60 -88.5t22 -119.5q0 -68 -22.5 -120.5t-61 -88.5t-90 -54.5t-108.5 -18.5q-59 0 -110.5 18.5t-90 54.5t-60.5 88.5t-22 120.5zM211 451q0 -48 15.5 -84.5t41.5 -60.5t60 -36t72 -12 q40 0 74.5 11.5t59.5 35.5t39.5 60.5t14.5 85.5q0 48 -15 84t-40.5 60t-60 36t-72.5 12q-42 0 -77 -13t-60 -38t-38.5 -60.5t-13.5 -80.5zM297 581q16 5 45 7.5t55 2.5q42 0 72 -17.5t30 -60.5q0 -32 -20.5 -49t-50.5 -19l28 -14l65 -109h-61l-63 104l-48 15v-119h-52v259z M386 546q-11 0 -21 -0.5t-16 -3.5v-71h34q29 0 45 9t16 31q0 35 -58 35z" />
<glyph unicode="&#xaf;" horiz-adv-x="313" d="M313 658h308l-19 -92h-308z" />
<glyph unicode="&#xb0;" horiz-adv-x="438" d="M121 548q0 37 13 67.5t35.5 52.5t52 34t63.5 12t64.5 -11t53.5 -32.5t36 -52.5t13 -70t-13 -69.5t-36 -51.5t-53.5 -32t-64.5 -11t-63.5 11t-52 32t-35.5 51.5t-13 69.5zM221 548q0 -32 19 -50.5t46 -18.5t47 18.5t20 50.5t-20 51.5t-47 19.5t-46 -19.5t-19 -51.5z" />
<glyph unicode="&#xb1;" horiz-adv-x="505" d="M58 517h162v165h117v-165h162v-112h-162v-106h-117v106h-162v112zM58 253h441v-112h-441v112z" />
<glyph unicode="&#xb2;" horiz-adv-x="402" d="M96 380zM446 707q0 -60 -38 -110t-102 -98l-49 -25v-4l57 11h101l-22 -101h-297l15 71q29 18 67 44t72 55t57.5 59.5t23.5 57.5q0 17 -9.5 27t-35.5 10t-51 -8.5t-44 -19.5l-14 91q37 21 76.5 30.5t73.5 9.5q57 0 88 -26t31 -74z" />
<glyph unicode="&#xb3;" horiz-adv-x="402" d="M104 373zM227 468q50 0 70 18.5t20 41.5q0 20 -14.5 30t-62.5 10h-41l8 38l86 81l45 24l-56 -6h-108l21 95h269l-14 -62l-102 -93l-35 -17v-5l28 3q35 -1 60 -25t24 -64q0 -42 -15.5 -73t-42.5 -51t-64 -30t-80 -10q-36 0 -67.5 6.5t-51.5 17.5l40 90q40 -19 83 -19z" />
<glyph unicode="&#xb4;" horiz-adv-x="238" d="M405 720h125l-6 -30l-133 -124h-69z" />
<glyph unicode="&#xb6;" horiz-adv-x="538" d="M398 700h107v-830h-107v830zM214 294q-33 0 -61 16.5t-48.5 45t-31.5 65t-11 77.5q0 40 13 76.5t37 64.5t58 44.5t76 16.5h74v-830h-106v424z" />
<glyph unicode="&#xb7;" horiz-adv-x="253" d="M73 239zM73 316q0 35 22 55.5t58 20.5q37 0 59 -20.5t22 -55.5t-22 -56t-59 -21q-36 0 -58 21t-22 56z" />
<glyph unicode="&#xb8;" horiz-adv-x="363" d="M388 -43q34 -2 52 -21t18 -48q0 -26 -11.5 -45t-31 -31.5t-46 -18.5t-56.5 -6q-38 0 -77 9l20 48q51 -5 70 6t19 25q0 32 -78 30l75 95h79z" />
<glyph unicode="&#xb9;" horiz-adv-x="402" d="M119 380zM140 480h93l36 164l19 46l-36 -32l-63 -37l-34 73l176 113h81l-70 -327h83l-21 -100h-285z" />
<glyph unicode="&#xba;" horiz-adv-x="399" d="M60 499q0 54 18.5 94t48.5 66.5t66.5 39.5t72.5 13q66 0 100.5 -30.5t34.5 -89.5q0 -57 -17 -98.5t-46 -68.5t-66.5 -40t-77.5 -13q-62 0 -98 34.5t-36 92.5zM170 505q-1 -22 10 -35.5t32 -13.5q33 0 56 34.5t23 88.5q0 49 -43 49q-17 0 -31 -10t-25 -26.5t-17 -39 t-5 -47.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="465" d="M281 268l-200 -230l-62 61l108 127l55 41l-35 39l-62 124l86 59zM465 268l-200 -230l-62 61l108 127l55 41l-35 39l-62 124l86 59z" />
<glyph unicode="&#xbc;" horiz-adv-x="840" d="M121 0zM618 715l85 -50l-430 -679l-86 53zM771 78h-72l-17 -78h-96l17 78h-196l14 67l265 283h87l-55 -261h72zM640 253l18 53h-5l-32 -49l-60 -66l-41 -32l53 8h50zM236 544l20 46l-35 -32l-63 -36l-37 75l178 110h82l-91 -427h-110z" />
<glyph unicode="&#xbd;" horiz-adv-x="862" d="M121 0zM619 715l85 -50l-430 -679l-86 53zM830 327q0 -60 -38 -110t-102 -98l-49 -25v-4l57 11h101l-22 -101h-297l15 71q29 18 67 44t72 55t57.5 59.5t23.5 57.5q0 17 -9.5 27t-35.5 10t-51 -8.5t-44 -19.5l-14 91q37 21 76.5 30.5t73.5 9.5q57 0 88 -26t31 -74z M236 544l20 46l-35 -32l-63 -36l-37 75l178 110h82l-91 -427h-110z" />
<glyph unicode="&#xbe;" horiz-adv-x="864" d="M69 0zM643 715l85 -50l-430 -679l-86 53zM796 78h-72l-17 -78h-96l17 78h-196l14 67l265 283h87l-55 -261h72zM665 253l18 53h-5l-32 -49l-60 -66l-41 -32l53 8h50zM192 368q50 0 70 18.5t20 41.5q0 20 -14.5 30t-62.5 10h-41l8 38l86 81l45 24l-56 -6h-108l21 95h269 l-14 -62l-102 -93l-35 -17v-5l28 3q35 -1 60 -25t24 -64q0 -42 -15.5 -73t-42.5 -51t-64 -30t-80 -10q-36 0 -67.5 6.5t-51.5 17.5l40 90q40 -19 83 -19z" />
<glyph unicode="&#xbf;" horiz-adv-x="430" d="M0 0zM332.008 294.013q-6 -53.9971 -24.499 -92.4941q-18.498 -38.498 -42.9971 -66.9961q-24.498 -28.498 -51.9971 -49.9971q-27.498 -21.499 -49.9961 -41.4971q-22.499 -19.999 -37.498 -40.998q-14.999 -20.998 -14.999 -48.9971q0 -26.998 15.499 -41.9971 t55.4961 -14.999q30.998 0 67.9961 13.499t64.9961 32.498l30.998 -93.9941q-40.9971 -24.999 -88.9941 -42.4971q-47.9971 -17.499 -116.993 -17.499q-83.9951 0 -122.992 35.4971q-38.998 35.498 -38.998 95.4941q0 52.9971 15.999 89.4951t40.998 63.9961 q24.998 27.498 54.9961 48.9971q29.998 21.498 57.4971 45.4971q27.498 23.998 49.4961 53.9971q21.999 29.998 29.999 72.9951h105.993zM377.005 436.004q0 -34.998 -21.999 -55.4961q-21.998 -20.499 -57.9961 -20.499q-36.998 0 -58.9961 20.499 q-21.999 20.498 -21.999 55.4961t21.999 55.9971q21.998 20.998 58.9961 20.998q35.998 0 57.9961 -20.998q21.999 -20.999 21.999 -55.9971z" />
<glyph unicode="&#xc0;" horiz-adv-x="582" d="M0 0zM387 155h-204l-83 -155h-138l389 705h96l91 -705h-131zM240 265h142l-16 156l1 110h-5l-40 -111zM492 752h-90l-131 88l6 30h140z" />
<glyph unicode="&#xc1;" horiz-adv-x="582" d="M0 0zM387 155h-204l-83 -155h-138l389 705h96l91 -705h-131zM240 265h142l-16 156l1 110h-5l-40 -111zM441 870h157l-6 -30l-172 -88h-104z" />
<glyph unicode="&#xc2;" horiz-adv-x="582" d="M0 0zM387 155h-204l-83 -155h-138l389 705h96l91 -705h-131zM240 265h142l-16 156l1 110h-5l-40 -111zM389 870h87l101 -93l-8 -39h-88l-45 41l-13 34l-27 -33l-67 -42h-94l9 40z" />
<glyph unicode="&#xc3;" horiz-adv-x="582" d="M0 0zM387 155h-204l-83 -155h-138l389 705h96l91 -705h-131zM240 265h142l-16 156l1 110h-5l-40 -111zM241 800q38 35 69 48t58 13q21 0 40.5 -5.5t38 -12t36 -12t35.5 -5.5q15 0 30 5t31 19l10 -67q-33 -27 -60 -37t-51 -10q-21 0 -39.5 5.5t-36.5 11.5t-35.5 11.5 t-36.5 5.5q-18 0 -37.5 -7t-42.5 -26z" />
<glyph unicode="&#xc4;" horiz-adv-x="582" d="M0 0zM387 155h-204l-83 -155h-138l389 705h96l91 -705h-131zM240 265h142l-16 156l1 110h-5l-40 -111zM246 798q0 29 20 43.5t52 14.5t52 -14.5t20 -43.5t-20 -43t-52 -14t-52 14t-20 43zM450 798q0 29 20 43.5t52 14.5t52 -14.5t20 -43.5t-20 -43t-52 -14t-52 14t-20 43 z" />
<glyph unicode="&#xc5;" horiz-adv-x="582" d="M286 771q0 18 6 34.5t20.5 29t39 19.5t62.5 7q70 0 99 -24t29 -66q0 -32 -20.5 -55.5t-72.5 -29.5l89 -686h-131l-20 155h-204l-83 -155h-138l382 693q-32 11 -45 31t-13 47zM382 265l-16 156l1 110h-5l-40 -111l-82 -155h142zM370 771q0 -31 44 -31q25 0 35 8t10 23 q0 33 -45 33q-44 0 -44 -33z" />
<glyph unicode="&#xc6;" horiz-adv-x="826" d="M449 530l-44 -94l-116 -155h114l52 249h-6zM379 170h-171l-128 -170h-146l547 700h133h271l-26 -122h-264l-35 -163h241l-26 -122h-240l-36 -171h269l-26 -122h-261h-138z" />
<glyph unicode="&#xc7;" horiz-adv-x="537" d="M483 29q-65 -42 -157 -43l-22 -29q34 -2 52 -20.5t18 -47.5q0 -26 -11.5 -45t-31 -31.5t-46 -18.5t-56.5 -6q-38 0 -77 9l20 48q51 -5 70 6t19 25q0 17 -20 23.5t-58 5.5l69 88q-49 10 -85.5 35.5t-60.5 62.5t-36 83t-12 98q0 118 33 201.5t87 137t123.5 78.5t142.5 25 q57 0 97.5 -8.5t63.5 -19.5l-51 -116q-20 11 -48 16.5t-71 5.5q-51 0 -95 -21t-76 -60t-50.5 -95t-18.5 -127q0 -85 42.5 -133t113.5 -48q45 0 74.5 9.5t55.5 23.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="503" d="M20 0zM168 700h130h264l-26 -122h-264l-35 -163h241l-27 -122h-239l-37 -171h269l-25 -122h-269h-130zM453 752h-90l-131 88l6 30h140z" />
<glyph unicode="&#xc9;" horiz-adv-x="503" d="M20 0zM168 700h130h264l-26 -122h-264l-35 -163h241l-27 -122h-239l-37 -171h269l-25 -122h-269h-130zM424 870h157l-6 -30l-172 -88h-104z" />
<glyph unicode="&#xca;" horiz-adv-x="503" d="M20 0zM168 700h130h264l-26 -122h-264l-35 -163h241l-27 -122h-239l-37 -171h269l-25 -122h-269h-130zM360 870h87l101 -93l-8 -39h-88l-45 41l-13 34l-27 -33l-67 -42h-94l9 40z" />
<glyph unicode="&#xcb;" horiz-adv-x="503" d="M20 0zM168 700h130h264l-26 -122h-264l-35 -163h241l-27 -122h-239l-37 -171h269l-25 -122h-269h-130zM210 798q0 29 20 43.5t52 14.5t52 -14.5t20 -43.5t-20 -43t-52 -14t-52 14t-20 43zM414 798q0 29 20 43.5t52 14.5t52 -14.5t20 -43.5t-20 -43t-52 -14t-52 14t-20 43 z" />
<glyph unicode="&#xcc;" horiz-adv-x="278" d="M35 0zM183 700h129l-148 -700h-129zM334 752h-90l-131 88l6 30h140z" />
<glyph unicode="&#xcd;" horiz-adv-x="278" d="M35 0zM183 700h129l-148 -700h-129zM304 870h157l-6 -30l-172 -88h-104z" />
<glyph unicode="&#xce;" horiz-adv-x="278" d="M35 0zM183 700h129l-148 -700h-129zM241 870h87l101 -93l-8 -39h-88l-45 41l-13 34l-27 -33l-67 -42h-94l9 40z" />
<glyph unicode="&#xcf;" horiz-adv-x="278" d="M35 0zM183 700h129l-148 -700h-129zM92 798q0 29 20 43.5t52 14.5t52 -14.5t20 -43.5t-20 -43t-52 -14t-52 14t-20 43zM296 798q0 29 20 43.5t52 14.5t52 -14.5t20 -43.5t-20 -43t-52 -14t-52 14t-20 43z" />
<glyph unicode="&#xd0;" horiz-adv-x="640" d="M40 401h85l63 299q21 3 46 5t47.5 3t36.5 1q10 0 26 0.5t35 0.5q62 0 112 -15.5t85.5 -47.5t55 -82t19.5 -119q0 -44 -7.5 -94.5t-25.5 -101.5t-49.5 -98t-79 -83t-114 -58t-154.5 -22q-15 0 -29 0.5t-22 0.5q-18 1 -43.5 2.5t-50 3t-36.5 3.5l68 322h-85zM254 401h132 l-17 -80h-132l-43 -206q4 -1 12.5 -1.5t18.5 -1t19 -0.5h14q70 -1 119 30.5t79.5 79t44 103t13.5 103.5q0 80 -33.5 120t-112.5 40q-19 0 -41 -1t-34 -3z" />
<glyph unicode="&#xd1;" horiz-adv-x="631" d="M20 0zM267 366l-33 112h-5l-9 -112l-77 -366h-123l150 705h90l172 -374l31 -109h6l7 109l79 369h123l-150 -705h-90zM259 800q38 35 69 48t58 13q21 0 40.5 -5.5t38 -12t36 -12t35.5 -5.5q15 0 30 5t31 19l10 -67q-33 -27 -60 -37t-51 -10q-21 0 -39.5 5.5t-36.5 11.5 t-35.5 11.5t-36.5 5.5q-18 0 -37.5 -7t-42.5 -26z" />
<glyph unicode="&#xd2;" horiz-adv-x="644" d="M58 0zM58 262q0 93 25.5 175.5t72.5 144t113.5 97t148.5 35.5q54 0 97.5 -16t74 -49.5t47.5 -84.5t17 -121q0 -94 -25.5 -177t-72 -145.5t-111 -98.5t-142.5 -36q-118 0 -181 69.5t-64 206.5zM195 266q0 -72 27.5 -115t90.5 -43q46 0 83.5 27.5t64.5 74t41.5 106.5 t14.5 125q0 79 -31 115t-85 36q-45 0 -82.5 -26t-65 -70.5t-43 -104t-15.5 -125.5zM511 752h-90l-131 88l6 30h140z" />
<glyph unicode="&#xd3;" horiz-adv-x="644" d="M58 0zM58 262q0 93 25.5 175.5t72.5 144t113.5 97t148.5 35.5q54 0 97.5 -16t74 -49.5t47.5 -84.5t17 -121q0 -94 -25.5 -177t-72 -145.5t-111 -98.5t-142.5 -36q-118 0 -181 69.5t-64 206.5zM195 266q0 -72 27.5 -115t90.5 -43q46 0 83.5 27.5t64.5 74t41.5 106.5 t14.5 125q0 79 -31 115t-85 36q-45 0 -82.5 -26t-65 -70.5t-43 -104t-15.5 -125.5zM491 870h157l-6 -30l-172 -88h-104z" />
<glyph unicode="&#xd4;" horiz-adv-x="644" d="M58 0zM58 262q0 93 25.5 175.5t72.5 144t113.5 97t148.5 35.5q54 0 97.5 -16t74 -49.5t47.5 -84.5t17 -121q0 -94 -25.5 -177t-72 -145.5t-111 -98.5t-142.5 -36q-118 0 -181 69.5t-64 206.5zM195 266q0 -72 27.5 -115t90.5 -43q46 0 83.5 27.5t64.5 74t41.5 106.5 t14.5 125q0 79 -31 115t-85 36q-45 0 -82.5 -26t-65 -70.5t-43 -104t-15.5 -125.5zM424 870h87l101 -93l-8 -39h-88l-45 41l-13 34l-27 -33l-67 -42h-94l9 40z" />
<glyph unicode="&#xd5;" horiz-adv-x="644" d="M58 0zM58 262q0 93 25.5 175.5t72.5 144t113.5 97t148.5 35.5q54 0 97.5 -16t74 -49.5t47.5 -84.5t17 -121q0 -94 -25.5 -177t-72 -145.5t-111 -98.5t-142.5 -36q-118 0 -181 69.5t-64 206.5zM195 266q0 -72 27.5 -115t90.5 -43q46 0 83.5 27.5t64.5 74t41.5 106.5 t14.5 125q0 79 -31 115t-85 36q-45 0 -82.5 -26t-65 -70.5t-43 -104t-15.5 -125.5zM268 800q38 35 69 48t58 13q21 0 40.5 -5.5t38 -12t36 -12t35.5 -5.5q15 0 30 5t31 19l10 -67q-33 -27 -60 -37t-51 -10q-21 0 -39.5 5.5t-36.5 11.5t-35.5 11.5t-36.5 5.5q-18 0 -37.5 -7 t-42.5 -26z" />
<glyph unicode="&#xd6;" horiz-adv-x="644" d="M58 0zM58 262q0 93 25.5 175.5t72.5 144t113.5 97t148.5 35.5q54 0 97.5 -16t74 -49.5t47.5 -84.5t17 -121q0 -94 -25.5 -177t-72 -145.5t-111 -98.5t-142.5 -36q-118 0 -181 69.5t-64 206.5zM195 266q0 -72 27.5 -115t90.5 -43q46 0 83.5 27.5t64.5 74t41.5 106.5 t14.5 125q0 79 -31 115t-85 36q-45 0 -82.5 -26t-65 -70.5t-43 -104t-15.5 -125.5zM270 798q0 29 20 43.5t52 14.5t52 -14.5t20 -43.5t-20 -43t-52 -14t-52 14t-20 43zM474 798q0 29 20 43.5t52 14.5t52 -14.5t20 -43.5t-20 -43t-52 -14t-52 14t-20 43z" />
<glyph unicode="&#xd7;" horiz-adv-x="505" d="M146 547l131 -130l130 130l79 -81l-130 -127l130 -129l-79 -82l-130 131l-132 -132l-79 81l131 131l-129 127z" />
<glyph unicode="&#xd8;" horiz-adv-x="644" d="M58 262q0 93 25.5 175.5t72.5 144t113.5 97t148.5 35.5q86 0 142 -40l27 32l72 -62l-37 -45q15 -30 23.5 -69t8.5 -87q0 -94 -25.5 -177t-72 -145.5t-111 -98.5t-142.5 -36q-90 0 -149 41l-27 -32l-70 64l34 42q-16 32 -24.5 72t-8.5 89zM474 568q-29 24 -73 24 q-45 0 -82.5 -26t-65 -70.5t-43 -104t-15.5 -125.5v-19t2 -18zM239 132q28 -24 74 -24t83.5 27.5t64.5 74t41.5 106.5t14.5 125v14.5t-1 13.5z" />
<glyph unicode="&#xd9;" horiz-adv-x="602" d="M63 0zM531 700h122l-95 -452q-15 -72 -39 -121.5t-58 -80.5t-80 -44.5t-106 -13.5q-58 0 -98 13.5t-65.5 38.5t-37 59t-11.5 75q0 20 2.5 41t7.5 44l94 441h129l-92 -440q-8 -38 -8 -62q0 -48 25 -68t73 -20q60 0 93 35t50 113zM490 752h-90l-131 88l6 30h140z" />
<glyph unicode="&#xda;" horiz-adv-x="602" d="M63 0zM531 700h122l-95 -452q-15 -72 -39 -121.5t-58 -80.5t-80 -44.5t-106 -13.5q-58 0 -98 13.5t-65.5 38.5t-37 59t-11.5 75q0 20 2.5 41t7.5 44l94 441h129l-92 -440q-8 -38 -8 -62q0 -48 25 -68t73 -20q60 0 93 35t50 113zM487 870h157l-6 -30l-172 -88h-104z" />
<glyph unicode="&#xdb;" horiz-adv-x="602" d="M63 0zM531 700h122l-95 -452q-15 -72 -39 -121.5t-58 -80.5t-80 -44.5t-106 -13.5q-58 0 -98 13.5t-65.5 38.5t-37 59t-11.5 75q0 20 2.5 41t7.5 44l94 441h129l-92 -440q-8 -38 -8 -62q0 -48 25 -68t73 -20q60 0 93 35t50 113zM410 870h87l101 -93l-8 -39h-88l-45 41 l-13 34l-27 -33l-67 -42h-94l9 40z" />
<glyph unicode="&#xdc;" horiz-adv-x="602" d="M63 0zM531 700h122l-95 -452q-15 -72 -39 -121.5t-58 -80.5t-80 -44.5t-106 -13.5q-58 0 -98 13.5t-65.5 38.5t-37 59t-11.5 75q0 20 2.5 41t7.5 44l94 441h129l-92 -440q-8 -38 -8 -62q0 -48 25 -68t73 -20q60 0 93 35t50 113zM256 798q0 29 20 43.5t52 14.5t52 -14.5 t20 -43.5t-20 -43t-52 -14t-52 14t-20 43zM460 798q0 29 20 43.5t52 14.5t52 -14.5t20 -43.5t-20 -43t-52 -14t-52 14t-20 43z" />
<glyph unicode="&#xdd;" horiz-adv-x="569" d="M106 0zM231 260l-125 440h144l65 -263l-2 -74h4l31 76l175 261h149l-311 -439l-55 -261h-129zM451 870h157l-6 -30l-172 -88h-104z" />
<glyph unicode="&#xde;" horiz-adv-x="550" d="M169 700h129l-15 -71q13 1 26 1h26q51 0 94.5 -11.5t76 -35.5t50.5 -61.5t18 -89.5q0 -78 -27 -130.5t-71.5 -85t-101.5 -46t-116 -13.5h-15.5t-22 0.5t-22 1t-15.5 1.5l-33 -160h-130zM257 506q-12 -57 -24 -113t-24 -113q4 -1 11 -2t15.5 -1.5t16 -1t11.5 -0.5 q34 0 65.5 7t55.5 23.5t38.5 43.5t14.5 66q0 30 -9.5 48.5t-25.5 29t-37.5 14.5t-45.5 4q-18 0 -34.5 -1.5t-27.5 -3.5z" />
<glyph unicode="&#xdf;" horiz-adv-x="608" d="M153 528q12 57 35 92t55 54.5t72 26.5t87 7q51 0 86 -11t57 -29.5t32 -43t10 -52.5q0 -35 -11.5 -60t-28.5 -43t-37.5 -31t-37.5 -25t-28.5 -25t-11.5 -30t11.5 -28t28.5 -20t37.5 -19t37.5 -25.5t28.5 -39t11.5 -59.5q0 -39 -17.5 -72.5t-48 -57.5t-72 -37.5 t-89.5 -13.5q-41 0 -81 11.5t-65 28.5l56 97q21 -13 44.5 -23t57.5 -10q15 0 30 5t27 14t19.5 22.5t7.5 30.5q0 19 -11.5 31.5t-29 22.5t-38 20.5t-38 25t-29 35.5t-11.5 53q0 29 11.5 50.5t29 37.5t37.5 28.5t37.5 24.5t29 25.5t11.5 31.5q0 23 -18 37t-52 14 q-48 0 -77 -26t-40 -85l-89 -488q-9 -51 -21 -90t-32.5 -65t-50.5 -39.5t-74 -13.5q-36 0 -70.5 5.5t-59.5 22.5l34 91q17 -7 34 -9t43 -2q32 0 49.5 25t25.5 75l71 398h-82l20 102h81l6 28v0z" />
<glyph unicode="&#xe0;" horiz-adv-x="503" d="M33 0zM444 200q-8 -38 -11.5 -77t-3.5 -74q0 -14 0.5 -26.5t1.5 -23.5h-91l-14 86h-4q-11 -18 -28.5 -35.5t-38.5 -32t-44.5 -23t-48.5 -8.5q-66 0 -97.5 43t-31.5 115q0 73 18.5 140t57.5 118t98.5 81.5t141.5 30.5q35 0 76.5 -6.5t80.5 -17.5zM209 89q18 0 34 7.5 t29.5 19.5t24 26t18.5 26l50 233q-11 5 -25.5 7.5t-28.5 2.5q-36 0 -64 -21.5t-47.5 -56t-29.5 -78t-10 -87.5q0 -32 10.5 -55.5t38.5 -23.5zM388 566h-69l-91 124l7 30h129z" />
<glyph unicode="&#xe1;" horiz-adv-x="503" d="M33 0zM444 200q-8 -38 -11.5 -77t-3.5 -74q0 -14 0.5 -26.5t1.5 -23.5h-91l-14 86h-4q-11 -18 -28.5 -35.5t-38.5 -32t-44.5 -23t-48.5 -8.5q-66 0 -97.5 43t-31.5 115q0 73 18.5 140t57.5 118t98.5 81.5t141.5 30.5q35 0 76.5 -6.5t80.5 -17.5zM209 89q18 0 34 7.5 t29.5 19.5t24 26t18.5 26l50 233q-11 5 -25.5 7.5t-28.5 2.5q-36 0 -64 -21.5t-47.5 -56t-29.5 -78t-10 -87.5q0 -32 10.5 -55.5t38.5 -23.5zM382 720h125l-6 -30l-133 -124h-69z" />
<glyph unicode="&#xe2;" horiz-adv-x="503" d="M33 0zM444 200q-8 -38 -11.5 -77t-3.5 -74q0 -14 0.5 -26.5t1.5 -23.5h-91l-14 86h-4q-11 -18 -28.5 -35.5t-38.5 -32t-44.5 -23t-48.5 -8.5q-66 0 -97.5 43t-31.5 115q0 73 18.5 140t57.5 118t98.5 81.5t141.5 30.5q35 0 76.5 -6.5t80.5 -17.5zM209 89q18 0 34 7.5 t29.5 19.5t24 26t18.5 26l50 233q-11 5 -25.5 7.5t-28.5 2.5q-36 0 -64 -21.5t-47.5 -56t-29.5 -78t-10 -87.5q0 -32 10.5 -55.5t38.5 -23.5zM346 732h82l67 -182h-96l-24 66l-4 49l-27 -49l-57 -66h-97z" />
<glyph unicode="&#xe3;" horiz-adv-x="503" d="M33 0zM444 200q-8 -38 -11.5 -77t-3.5 -74q0 -14 0.5 -26.5t1.5 -23.5h-91l-14 86h-4q-11 -18 -28.5 -35.5t-38.5 -32t-44.5 -23t-48.5 -8.5q-66 0 -97.5 43t-31.5 115q0 73 18.5 140t57.5 118t98.5 81.5t141.5 30.5q35 0 76.5 -6.5t80.5 -17.5zM209 89q18 0 34 7.5 t29.5 19.5t24 26t18.5 26l50 233q-11 5 -25.5 7.5t-28.5 2.5q-36 0 -64 -21.5t-47.5 -56t-29.5 -78t-10 -87.5q0 -32 10.5 -55.5t38.5 -23.5zM197 658q35 37 61.5 51t48.5 14q18 0 33.5 -7t31 -15t31 -15t33.5 -7q14 0 30.5 6t36.5 21l4 -70q-32 -32 -57 -44.5t-46 -12.5 q-18 0 -34 6.5t-32 14.5t-32 14.5t-34 6.5q-15 0 -33 -6t-38 -22z" />
<glyph unicode="&#xe4;" horiz-adv-x="503" d="M33 0zM444 200q-8 -38 -11.5 -77t-3.5 -74q0 -14 0.5 -26.5t1.5 -23.5h-91l-14 86h-4q-11 -18 -28.5 -35.5t-38.5 -32t-44.5 -23t-48.5 -8.5q-66 0 -97.5 43t-31.5 115q0 73 18.5 140t57.5 118t98.5 81.5t141.5 30.5q35 0 76.5 -6.5t80.5 -17.5zM209 89q18 0 34 7.5 t29.5 19.5t24 26t18.5 26l50 233q-11 5 -25.5 7.5t-28.5 2.5q-36 0 -64 -21.5t-47.5 -56t-29.5 -78t-10 -87.5q0 -32 10.5 -55.5t38.5 -23.5zM171 651q0 33 18.5 51.5t47.5 18.5t47.5 -18.5t18.5 -51.5q0 -32 -18.5 -50t-47.5 -18t-47.5 18t-18.5 50zM379 652 q0 32 18.5 50.5t47.5 18.5t47.5 -18.5t18.5 -50.5t-18.5 -50.5t-47.5 -18.5t-47.5 18.5t-18.5 50.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="503" d="M33 0zM444 200q-8 -38 -11.5 -77t-3.5 -74q0 -14 0.5 -26.5t1.5 -23.5h-91l-14 86h-4q-11 -18 -28.5 -35.5t-38.5 -32t-44.5 -23t-48.5 -8.5q-66 0 -97.5 43t-31.5 115q0 73 18.5 140t57.5 118t98.5 81.5t141.5 30.5q35 0 76.5 -6.5t80.5 -17.5zM209 89q18 0 34 7.5 t29.5 19.5t24 26t18.5 26l50 233q-11 5 -25.5 7.5t-28.5 2.5q-36 0 -64 -21.5t-47.5 -56t-29.5 -78t-10 -87.5q0 -32 10.5 -55.5t38.5 -23.5zM255 647q0 42 28.5 69t82.5 27q51 0 81 -24.5t30 -71.5q0 -39 -29.5 -66.5t-81.5 -27.5q-53 0 -82 25.5t-29 68.5zM327 647 q0 -16 11.5 -24.5t27.5 -8.5q17 0 28 8t11 25t-11.5 25.5t-27.5 8.5t-27.5 -8t-11.5 -26z" />
<glyph unicode="&#xe6;" horiz-adv-x="744" d="M312 223q-8 1 -16.5 1h-16.5q-57 0 -96 -18.5t-39 -57.5q0 -27 14 -39t37 -12q18 0 34.5 7.5t30.5 18t25 22t18 20.5zM315 77q-32 -36 -74 -60.5t-99 -24.5q-33 0 -56.5 10.5t-38.5 28t-22.5 40.5t-7.5 47q0 49 22 84t60 57t88 32.5t107 10.5h16.5t16.5 -1q11 38 11 59 q0 24 -15 35.5t-51 11.5q-33 0 -70 -8t-69 -21l-18 93q41 20 94.5 29.5t103.5 9.5q46 0 78 -14.5t45 -45.5q29 31 72.5 47.5t88.5 16.5q42 0 71.5 -9.5t48 -26.5t27 -39.5t8.5 -49.5q0 -56 -26.5 -90t-71.5 -53.5t-104 -26.5t-123 -7q-1 -8 -1.5 -15.5t-0.5 -14.5 q0 -45 23 -69t76 -24q38 0 71.5 10.5t53.5 22.5l23 -86q-34 -24 -85.5 -37t-102.5 -13q-66 0 -106.5 24t-57.5 67h-5zM447 292q32 0 65 1.5t60 9t44 23t17 42.5q0 17 -12 31.5t-45 14.5q-53 0 -83.5 -35.5t-45.5 -86.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="412" d="M370 41q-26 -19 -58 -33t-66 -19l-24 -32q34 -2 52 -20.5t18 -47.5q0 -26 -11.5 -45t-31 -31.5t-46 -18.5t-56.5 -6q-38 0 -77 9l20 48q51 -5 70 6t19 25q0 17 -20 23.5t-58 5.5l66 85q-36 6 -61 21.5t-41 38t-23.5 50.5t-7.5 58q0 84 24 150t63.5 111.5t90 70 t103.5 24.5q47 0 74.5 -9t49.5 -20l-46 -98q-19 11 -39.5 15t-44.5 4q-27 0 -52.5 -17.5t-46 -46t-32.5 -66.5t-12 -78q0 -51 22 -77.5t58 -26.5q30 0 55.5 9.5t45.5 21.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="466" d="M34 0zM402 48q-31 -26 -80 -44t-114 -18q-81 0 -127.5 47.5t-46.5 132.5q0 80 25 144.5t65.5 109.5t91.5 69.5t102 24.5q41 0 70.5 -10.5t48.5 -29t28 -42.5t9 -51q0 -41 -21 -72t-58.5 -51.5t-90.5 -31t-116 -10.5q-8 0 -15 0.5t-15 0.5q-2 -12 -3 -21.5t-1 -17.5 q0 -45 23.5 -67t66.5 -22q44 0 79 13t53 26zM176 295q28 0 59.5 2t58.5 9.5t45 22t18 39.5q0 15 -11 30.5t-46 15.5q-44 0 -76.5 -34t-47.5 -85zM368 566h-69l-91 124l7 30h129z" />
<glyph unicode="&#xe9;" horiz-adv-x="466" d="M34 0zM402 48q-31 -26 -80 -44t-114 -18q-81 0 -127.5 47.5t-46.5 132.5q0 80 25 144.5t65.5 109.5t91.5 69.5t102 24.5q41 0 70.5 -10.5t48.5 -29t28 -42.5t9 -51q0 -41 -21 -72t-58.5 -51.5t-90.5 -31t-116 -10.5q-8 0 -15 0.5t-15 0.5q-2 -12 -3 -21.5t-1 -17.5 q0 -45 23.5 -67t66.5 -22q44 0 79 13t53 26zM176 295q28 0 59.5 2t58.5 9.5t45 22t18 39.5q0 15 -11 30.5t-46 15.5q-44 0 -76.5 -34t-47.5 -85zM366 720h125l-6 -30l-133 -124h-69z" />
<glyph unicode="&#xea;" horiz-adv-x="466" d="M34 0zM402 48q-31 -26 -80 -44t-114 -18q-81 0 -127.5 47.5t-46.5 132.5q0 80 25 144.5t65.5 109.5t91.5 69.5t102 24.5q41 0 70.5 -10.5t48.5 -29t28 -42.5t9 -51q0 -41 -21 -72t-58.5 -51.5t-90.5 -31t-116 -10.5q-8 0 -15 0.5t-15 0.5q-2 -12 -3 -21.5t-1 -17.5 q0 -45 23.5 -67t66.5 -22q44 0 79 13t53 26zM176 295q28 0 59.5 2t58.5 9.5t45 22t18 39.5q0 15 -11 30.5t-46 15.5q-44 0 -76.5 -34t-47.5 -85zM323 732h82l67 -182h-96l-24 66l-4 49l-27 -49l-57 -66h-97z" />
<glyph unicode="&#xeb;" horiz-adv-x="466" d="M34 0zM402 48q-31 -26 -80 -44t-114 -18q-81 0 -127.5 47.5t-46.5 132.5q0 80 25 144.5t65.5 109.5t91.5 69.5t102 24.5q41 0 70.5 -10.5t48.5 -29t28 -42.5t9 -51q0 -41 -21 -72t-58.5 -51.5t-90.5 -31t-116 -10.5q-8 0 -15 0.5t-15 0.5q-2 -12 -3 -21.5t-1 -17.5 q0 -45 23.5 -67t66.5 -22q44 0 79 13t53 26zM176 295q28 0 59.5 2t58.5 9.5t45 22t18 39.5q0 15 -11 30.5t-46 15.5q-44 0 -76.5 -34t-47.5 -85zM164 651q0 33 18.5 51.5t47.5 18.5t47.5 -18.5t18.5 -51.5q0 -32 -18.5 -50t-47.5 -18t-47.5 18t-18.5 50zM372 652 q0 32 18.5 50.5t47.5 18.5t47.5 -18.5t18.5 -50.5t-18.5 -50.5t-47.5 -18.5t-47.5 18.5t-18.5 50.5z" />
<glyph unicode="&#xec;" horiz-adv-x="256" d="M27 0zM133 500h123l-106 -500h-123zM265 566h-69l-91 124l7 30h129z" />
<glyph unicode="&#xed;" horiz-adv-x="256" d="M27 0zM133 500h123l-106 -500h-123zM238 720h125l-6 -30l-133 -124h-69z" />
<glyph unicode="&#xee;" horiz-adv-x="256" d="M27 0zM133 500h123l-106 -500h-123zM204 732h82l67 -182h-96l-24 66l-4 49l-27 -49l-57 -66h-97z" />
<glyph unicode="&#xef;" horiz-adv-x="256" d="M27 0zM133 500h123l-106 -500h-123zM55 651q0 33 18.5 51.5t47.5 18.5t47.5 -18.5t18.5 -51.5q0 -32 -18.5 -50t-47.5 -18t-47.5 18t-18.5 50zM263 652q0 32 18.5 50.5t47.5 18.5t47.5 -18.5t18.5 -50.5t-18.5 -50.5t-47.5 -18.5t-47.5 18.5t-18.5 50.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="508" d="M262 592l70 29q-18 18 -39.5 31t-44.5 21l97 41q45 -17 82 -54l73 30l28 -54l-64 -27q17 -31 27 -69t10 -83q0 -123 -23.5 -212t-63.5 -146.5t-92.5 -85t-110.5 -27.5q-80 0 -127 47.5t-47 138.5q0 70 20.5 130t55 104t80 68.5t96.5 24.5q69 0 101 -38q2 32 -4 58.5 t-17 49.5l-75 -31zM164 177q0 -42 16.5 -65t45.5 -23q39 0 66.5 28t44.5 68.5t25 86.5t7 81q-8 20 -26.5 32t-44.5 12q-30 0 -54.5 -19t-42 -50t-27.5 -70.5t-10 -80.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="521" d="M14 0zM295 0l66 310q6 28 6 49q0 22 -9 34.5t-33 12.5q-18 0 -36 -9t-34 -24t-30 -33.5t-24 -37.5l-64 -302h-123l106 500h93l-5 -88h4q13 16 30.5 34t40.5 33t51.5 25t64.5 10q45 0 72 -24.5t27 -81.5q0 -15 -1.5 -32t-6.5 -37l-72 -339h-123zM203 658q35 37 61.5 51 t48.5 14q18 0 33.5 -7t31 -15t31 -15t33.5 -7q14 0 30.5 6t36.5 21l4 -70q-32 -32 -57 -44.5t-46 -12.5q-18 0 -34 6.5t-32 14.5t-32 14.5t-34 6.5q-15 0 -33 -6t-38 -22z" />
<glyph unicode="&#xf2;" horiz-adv-x="496" d="M34 0zM34 162q0 79 22.5 144t60 111t87 71.5t103.5 25.5q48 0 81 -15t53.5 -40t29.5 -57t9 -66q0 -78 -22 -142.5t-59 -110.5t-86 -71.5t-103 -25.5q-47 0 -80 15.5t-54.5 40.5t-31.5 56.5t-10 63.5zM161 179q0 -90 65 -90q24 0 47 21t41 55t28.5 77t10.5 86 q0 40 -13.5 61.5t-50.5 21.5q-24 0 -47 -20t-41 -52.5t-29 -74.5t-11 -85zM376 566h-69l-91 124l7 30h129z" />
<glyph unicode="&#xf3;" horiz-adv-x="496" d="M34 0zM34 162q0 79 22.5 144t60 111t87 71.5t103.5 25.5q48 0 81 -15t53.5 -40t29.5 -57t9 -66q0 -78 -22 -142.5t-59 -110.5t-86 -71.5t-103 -25.5q-47 0 -80 15.5t-54.5 40.5t-31.5 56.5t-10 63.5zM161 179q0 -90 65 -90q24 0 47 21t41 55t28.5 77t10.5 86 q0 40 -13.5 61.5t-50.5 21.5q-24 0 -47 -20t-41 -52.5t-29 -74.5t-11 -85zM364 720h125l-6 -30l-133 -124h-69z" />
<glyph unicode="&#xf4;" horiz-adv-x="496" d="M34 0zM34 162q0 79 22.5 144t60 111t87 71.5t103.5 25.5q48 0 81 -15t53.5 -40t29.5 -57t9 -66q0 -78 -22 -142.5t-59 -110.5t-86 -71.5t-103 -25.5q-47 0 -80 15.5t-54.5 40.5t-31.5 56.5t-10 63.5zM161 179q0 -90 65 -90q24 0 47 21t41 55t28.5 77t10.5 86 q0 40 -13.5 61.5t-50.5 21.5q-24 0 -47 -20t-41 -52.5t-29 -74.5t-11 -85zM320 732h82l67 -182h-96l-24 66l-4 49l-27 -49l-57 -66h-97z" />
<glyph unicode="&#xf5;" horiz-adv-x="496" d="M34 0zM34 162q0 79 22.5 144t60 111t87 71.5t103.5 25.5q48 0 81 -15t53.5 -40t29.5 -57t9 -66q0 -78 -22 -142.5t-59 -110.5t-86 -71.5t-103 -25.5q-47 0 -80 15.5t-54.5 40.5t-31.5 56.5t-10 63.5zM161 179q0 -90 65 -90q24 0 47 21t41 55t28.5 77t10.5 86 q0 40 -13.5 61.5t-50.5 21.5q-24 0 -47 -20t-41 -52.5t-29 -74.5t-11 -85zM179 658q35 37 61.5 51t48.5 14q18 0 33.5 -7t31 -15t31 -15t33.5 -7q14 0 30.5 6t36.5 21l4 -70q-32 -32 -57 -44.5t-46 -12.5q-18 0 -34 6.5t-32 14.5t-32 14.5t-34 6.5q-15 0 -33 -6t-38 -22z " />
<glyph unicode="&#xf6;" horiz-adv-x="496" d="M34 0zM34 162q0 79 22.5 144t60 111t87 71.5t103.5 25.5q48 0 81 -15t53.5 -40t29.5 -57t9 -66q0 -78 -22 -142.5t-59 -110.5t-86 -71.5t-103 -25.5q-47 0 -80 15.5t-54.5 40.5t-31.5 56.5t-10 63.5zM161 179q0 -90 65 -90q24 0 47 21t41 55t28.5 77t10.5 86 q0 40 -13.5 61.5t-50.5 21.5q-24 0 -47 -20t-41 -52.5t-29 -74.5t-11 -85zM165 651q0 33 18.5 51.5t47.5 18.5t47.5 -18.5t18.5 -51.5q0 -32 -18.5 -50t-47.5 -18t-47.5 18t-18.5 50zM373 652q0 32 18.5 50.5t47.5 18.5t47.5 -18.5t18.5 -50.5t-18.5 -50.5t-47.5 -18.5 t-47.5 18.5t-18.5 50.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="505" d="M58 394h442v-112h-442v112zM198 532q0 35 22 55.5t58 20.5q37 0 59 -20.5t22 -55.5t-22 -56t-59 -21q-36 0 -58 21t-22 56zM198 145q0 35 22 55.5t58 20.5q37 0 59 -20.5t22 -55.5t-22 -56t-59 -21q-36 0 -58 21t-22 56z" />
<glyph unicode="&#xf8;" horiz-adv-x="496" d="M34 162q0 79 22.5 144t60 111t87 71.5t103.5 25.5q32 0 57 -7t44 -19l34 38l64 -59l-42 -47q16 -38 16 -84q0 -78 -22 -142.5t-59 -110.5t-86 -71.5t-103 -25.5q-34 0 -60.5 8.5t-46.5 22.5l-37 -41l-66 60l47 54q-13 33 -13 72zM176 114q16 -25 50 -25q23 0 45 19.5 t39.5 51t29 71.5t13.5 81zM337 392q-16 19 -48 19q-23 0 -45 -18.5t-39.5 -48.5t-29.5 -69t-14 -81z" />
<glyph unicode="&#xf9;" horiz-adv-x="512" d="M39 0zM241 500l-65 -309q-6 -30 -6 -50t8 -31t28 -11q17 0 34.5 8.5t33.5 22t29.5 30t22.5 32.5l64 308h123l-63 -300q-5 -20 -8.5 -47t-6.5 -55.5t-4 -54.5t0 -43h-99l-5 90h-4q-14 -19 -32.5 -37.5t-41 -33.5t-49 -24t-56.5 -9q-47 0 -76 23.5t-29 86.5q0 37 10 81 l69 323h123zM373 566h-69l-91 124l7 30h129z" />
<glyph unicode="&#xfa;" horiz-adv-x="512" d="M39 0zM241 500l-65 -309q-6 -30 -6 -50t8 -31t28 -11q17 0 34.5 8.5t33.5 22t29.5 30t22.5 32.5l64 308h123l-63 -300q-5 -20 -8.5 -47t-6.5 -55.5t-4 -54.5t0 -43h-99l-5 90h-4q-14 -19 -32.5 -37.5t-41 -33.5t-49 -24t-56.5 -9q-47 0 -76 23.5t-29 86.5q0 37 10 81 l69 323h123zM352 720h125l-6 -30l-133 -124h-69z" />
<glyph unicode="&#xfb;" horiz-adv-x="512" d="M39 0zM241 500l-65 -309q-6 -30 -6 -50t8 -31t28 -11q17 0 34.5 8.5t33.5 22t29.5 30t22.5 32.5l64 308h123l-63 -300q-5 -20 -8.5 -47t-6.5 -55.5t-4 -54.5t0 -43h-99l-5 90h-4q-14 -19 -32.5 -37.5t-41 -33.5t-49 -24t-56.5 -9q-47 0 -76 23.5t-29 86.5q0 37 10 81 l69 323h123zM326 732h82l67 -182h-96l-24 66l-4 49l-27 -49l-57 -66h-97z" />
<glyph unicode="&#xfc;" horiz-adv-x="512" d="M39 0zM241 500l-65 -309q-6 -30 -6 -50t8 -31t28 -11q17 0 34.5 8.5t33.5 22t29.5 30t22.5 32.5l64 308h123l-63 -300q-5 -20 -8.5 -47t-6.5 -55.5t-4 -54.5t0 -43h-99l-5 90h-4q-14 -19 -32.5 -37.5t-41 -33.5t-49 -24t-56.5 -9q-47 0 -76 23.5t-29 86.5q0 37 10 81 l69 323h123zM173 651q0 33 18.5 51.5t47.5 18.5t47.5 -18.5t18.5 -51.5q0 -32 -18.5 -50t-47.5 -18t-47.5 18t-18.5 50zM381 652q0 32 18.5 50.5t47.5 18.5t47.5 -18.5t18.5 -50.5t-18.5 -50.5t-47.5 -18.5t-47.5 18.5t-18.5 50.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="452" d="M0 0zM216 219l1 -78h6l27 79l136 280h131l-240 -449q-30 -56 -56.5 -103t-53.5 -81t-56 -53t-62 -19q-44 0 -66 15l33 98q6 -3 12.5 -4t12.5 -1q24 0 49 23t50 74l-94 500h141zM331 720h125l-6 -30l-133 -124h-69z" />
<glyph unicode="&#xfe;" horiz-adv-x="506" d="M165 700h123l-62 -268h4q12 16 27 31t34 26t42 18t52 7q51 0 82 -35t31 -110q0 -85 -22.5 -155.5t-60 -121t-88 -78.5t-105.5 -28q-26 0 -49 5t-34 13l-43 -204h-122zM160 106q25 -17 56 -17t58.5 21t48 56t32.5 81.5t12 97.5q0 29 -11.5 47.5t-34.5 18.5q-18 0 -35 -9 t-32 -22.5t-27 -30.5t-21 -33z" />
<glyph unicode="&#xff;" horiz-adv-x="452" d="M0 0zM216 219l1 -78h6l27 79l136 280h131l-240 -449q-30 -56 -56.5 -103t-53.5 -81t-56 -53t-62 -19q-44 0 -66 15l33 98q6 -3 12.5 -4t12.5 -1q24 0 49 23t50 74l-94 500h141zM131 651q0 33 18.5 51.5t47.5 18.5t47.5 -18.5t18.5 -51.5q0 -32 -18.5 -50t-47.5 -18 t-47.5 18t-18.5 50zM339 652q0 32 18.5 50.5t47.5 18.5t47.5 -18.5t18.5 -50.5t-18.5 -50.5t-47.5 -18.5t-47.5 18.5t-18.5 50.5z" />
<glyph unicode="&#x131;" horiz-adv-x="256" d="M133 500h123l-106 -500h-123z" />
<glyph unicode="&#x152;" horiz-adv-x="877" d="M514 567q-18 12 -42.5 18.5t-64.5 6.5q-47 0 -85.5 -24t-67 -67t-44 -103t-15.5 -132q0 -26 5 -53.5t18.5 -51t38 -38.5t63.5 -15q28 0 49.5 3.5t51.5 15.5zM394 0q-20 -7 -46.5 -10.5t-54.5 -3.5q-43 0 -85 13t-75.5 44.5t-54 85t-20.5 133.5q0 105 27 189t76 142.5 t116 89.5t147 31q38 0 65.5 -4t52.5 -10h394l-26 -122h-264l-35 -163h241l-26 -122h-240l-36 -171h268l-25 -122h-399z" />
<glyph unicode="&#x153;" horiz-adv-x="769" d="M161 179q0 -90 65 -90q24 0 47 21t41 55t28.5 77t10.5 86q0 40 -13.5 61.5t-50.5 21.5q-24 0 -47 -20t-41 -52.5t-29 -74.5t-11 -85zM371 65q-25 -34 -68 -56.5t-94 -22.5q-47 0 -80 15.5t-54 40.5t-31 56.5t-10 63.5q0 79 22.5 144t60.5 111t87.5 71.5t103.5 25.5 t88.5 -22t53.5 -60q30 35 72.5 58.5t98.5 23.5q41 0 70.5 -10.5t48.5 -29t28 -42.5t9 -51q0 -41 -21 -72t-58.5 -51.5t-90.5 -31t-116 -10.5q-8 0 -15 0.5t-15 0.5q-2 -12 -3 -21.5t-1 -17.5q0 -45 23.5 -67t66.5 -22q44 0 79 13t53 26l26 -80q-31 -26 -80 -44t-114 -18 q-29 0 -52 6.5t-40 18t-29 25.5t-19 29zM479 295q28 0 59.5 2t58.5 9.5t45 22t18 39.5q0 15 -11 30.5t-46 15.5q-44 0 -76.5 -34t-47.5 -85z" />
<glyph unicode="&#x178;" horiz-adv-x="569" d="M106 0zM231 260l-125 440h144l65 -263l-2 -74h4l31 76l175 261h149l-311 -439l-55 -261h-129zM240 798q0 29 20 43.5t52 14.5t52 -14.5t20 -43.5t-20 -43t-52 -14t-52 14t-20 43zM444 798q0 29 20 43.5t52 14.5t52 -14.5t20 -43.5t-20 -43t-52 -14t-52 14t-20 43z" />
<glyph unicode="&#x237;" horiz-adv-x="244" d="M123 500h124l-111 -521q-19 -90 -64 -137.5t-122 -47.5q-31 0 -62 7l24 106q25 -2 43 4.5t30.5 20.5t21 35.5t14.5 48.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="431" d="M456 732h82l67 -182h-96l-24 66l-4 49l-27 -49l-57 -66h-97z" />
<glyph unicode="&#x2d9;" horiz-adv-x="266" d="M313 642q0 33 21.5 51t54.5 18q32 0 54.5 -18t22.5 -51q0 -32 -22.5 -50.5t-54.5 -18.5q-33 0 -54.5 18.5t-21.5 50.5z" />
<glyph unicode="&#x2da;" horiz-adv-x="401" d="M382 647q0 42 28.5 69t82.5 27q51 0 81 -24.5t30 -71.5q0 -39 -29.5 -66.5t-81.5 -27.5q-53 0 -82 25.5t-29 68.5zM454 647q0 -16 11.5 -24.5t27.5 -8.5q17 0 28 8t11 25t-11.5 25.5t-27.5 8.5t-27.5 -8t-11.5 -26z" />
<glyph unicode="&#x2dc;" horiz-adv-x="349" d="M306 658q35 37 61.5 51t48.5 14q18 0 33.5 -7t31 -15t31 -15t33.5 -7q14 0 30.5 6t36.5 21l4 -70q-32 -32 -57 -44.5t-46 -12.5q-18 0 -34 6.5t-32 14.5t-32 14.5t-34 6.5q-15 0 -33 -6t-38 -22z" />
<glyph unicode="&#x2000;" horiz-adv-x="435" />
<glyph unicode="&#x2001;" horiz-adv-x="870" />
<glyph unicode="&#x2002;" horiz-adv-x="435" />
<glyph unicode="&#x2003;" horiz-adv-x="870" />
<glyph unicode="&#x2004;" horiz-adv-x="290" />
<glyph unicode="&#x2005;" horiz-adv-x="217" />
<glyph unicode="&#x2006;" horiz-adv-x="145" />
<glyph unicode="&#x2007;" horiz-adv-x="145" />
<glyph unicode="&#x2008;" horiz-adv-x="108" />
<glyph unicode="&#x2009;" horiz-adv-x="174" />
<glyph unicode="&#x200a;" horiz-adv-x="48" />
<glyph unicode="&#x2010;" horiz-adv-x="343" d="M81 340h239l-25 -104h-239z" />
<glyph unicode="&#x2011;" horiz-adv-x="343" d="M81 340h239l-25 -104h-239z" />
<glyph unicode="&#x2012;" horiz-adv-x="343" d="M81 340h239l-25 -104h-239z" />
<glyph unicode="&#x2013;" horiz-adv-x="627" d="M130 340h419l-25 -104h-419z" />
<glyph unicode="&#x2014;" horiz-adv-x="779" d="M130 340h571l-25 -104h-571z" />
<glyph unicode="&#x2018;" horiz-adv-x="215" d="M238 631q0 -39 -23 -58t-50 -19q-17 0 -29.5 6.5t-20.5 17t-12 23t-4 25.5q0 40 17 71t40.5 52.5t47 34t37.5 15.5l19 -54q-20 -8 -37.5 -25.5t-23.5 -37.5q17 0 28 -15t11 -36z" />
<glyph unicode="&#x2019;" horiz-adv-x="216" d="M121 637q0 18 7.5 31.5t18.5 22.5t24 13.5t25 4.5q35 0 50 -18t15 -49q0 -41 -15.5 -72t-38 -53t-47 -35t-42.5 -19l-19 55q21 9 38.5 24t24.5 38q-18 2 -30 16.5t-11 40.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="226" d="M8 0zM30 55q0 18 7.5 31.5t18.5 22.5t24 13.5t25 4.5q35 0 50 -18t15 -49q0 -41 -15.5 -72t-38 -53t-47 -35t-42.5 -19l-19 55q21 9 38.5 24t24.5 38q-18 2 -30 16.5t-11 40.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="379" d="M99 554zM402 631q0 -39 -23 -58t-50 -19q-17 0 -29.5 6.5t-20.5 17t-12 23t-4 25.5q0 40 17 71t40.5 52.5t47 34t37.5 15.5l19 -54q-20 -8 -37.5 -25.5t-23.5 -37.5q17 0 28 -15t11 -36zM238 631q0 -39 -23 -58t-50 -19q-17 0 -29.5 6.5t-20.5 17t-12 23t-4 25.5 q0 40 17 71t40.5 52.5t47 34t37.5 15.5l19 -54q-20 -8 -37.5 -25.5t-23.5 -37.5q17 0 28 -15t11 -36z" />
<glyph unicode="&#x201d;" horiz-adv-x="379" d="M99 463zM284 637q0 18 7.5 31.5t18.5 22.5t24 13.5t25 4.5q35 0 50 -18t15 -49q0 -41 -15.5 -72t-38 -53t-47 -35t-42.5 -19l-19 55q21 9 38.5 24t24.5 38q-18 2 -30 16.5t-11 40.5zM121 637q0 18 7.5 31.5t18.5 22.5t24 13.5t25 4.5q35 0 50 -18t15 -49q0 -41 -15.5 -72 t-38 -53t-47 -35t-42.5 -19l-19 55q21 9 38.5 24t24.5 38q-18 2 -30 16.5t-11 40.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="389" d="M8 0zM192 55q0 18 7.5 31.5t18.5 22.5t24 13.5t25 4.5q35 0 50 -18t15 -49q0 -41 -15.5 -72t-38 -53t-47 -35t-42.5 -19l-19 55q21 9 38.5 24t24.5 38q-18 2 -30 16.5t-11 40.5zM30 55q0 18 7.5 31.5t18.5 22.5t24 13.5t25 4.5q35 0 50 -18t15 -49q0 -41 -15.5 -72 t-38 -53t-47 -35t-42.5 -19l-19 55q21 9 38.5 24t24.5 38q-18 2 -30 16.5t-11 40.5z" />
<glyph unicode="&#x2022;" horiz-adv-x="452" d="M98 311q0 34 12 62t32.5 48t48 31.5t59.5 11.5t60 -10.5t48.5 -30t32.5 -48t12 -64.5t-12 -64t-32.5 -47.5t-48.5 -30t-60 -10.5t-59.5 10.5t-48 30t-32.5 47.5t-12 64z" />
<glyph unicode="&#x2026;" horiz-adv-x="769" d="M535 66q0 35 22 55.5t58 20.5q37 0 59 -20.5t22 -55.5t-22 -56t-59 -21q-36 0 -58 21t-22 56zM276 66q0 35 22 55.5t58 20.5q37 0 59 -20.5t22 -55.5t-22 -56t-59 -21q-36 0 -58 21t-22 56zM18 66q0 35 22 55.5t58 20.5q37 0 59 -20.5t22 -55.5t-22 -56t-59 -21 q-36 0 -58 21t-22 56z" />
<glyph unicode="&#x202f;" horiz-adv-x="174" />
<glyph unicode="&#x2039;" horiz-adv-x="300" d="M40 259l200 230l60 -64l-106 -126l-55 -39l36 -37l60 -123l-84 -62z" />
<glyph unicode="&#x203a;" horiz-adv-x="293" d="M293 268l-200 -230l-62 61l108 127l55 41l-35 39l-62 124l86 59z" />
<glyph unicode="&#x2044;" horiz-adv-x="184" d="M293 715l85 -50l-430 -679l-86 53z" />
<glyph unicode="&#x205f;" horiz-adv-x="217" />
<glyph unicode="&#x2081;" horiz-adv-x="402" d="M38 0h93l36 164l19 46l-36 -32l-63 -37l-34 73l176 113h81l-70 -327h83l-21 -100h-285z" />
<glyph unicode="&#x2082;" horiz-adv-x="402" d="M344 227q0 -60 -38 -110t-102 -98l-49 -25v-4l57 11h101l-22 -101h-297l15 71q29 18 67 44t72 55t57.5 59.5t23.5 57.5q0 17 -9.5 27t-35.5 10t-51 -8.5t-44 -19.5l-14 91q37 21 76.5 30.5t73.5 9.5q57 0 88 -26t31 -74z" />
<glyph unicode="&#x2083;" horiz-adv-x="402" d="M125 -12q50 0 70 18.5t20 41.5q0 20 -14.5 30t-62.5 10h-41l8 38l86 81l45 24l-56 -6h-108l21 95h269l-14 -62l-102 -93l-35 -17v-5l28 3q35 -1 60 -25t24 -64q0 -42 -15.5 -73t-42.5 -51t-64 -30t-80 -10q-36 0 -67.5 6.5t-51.5 17.5l40 90q40 -19 83 -19z" />
<glyph unicode="&#x2084;" horiz-adv-x="402" d="M345 -22h-72l-17 -78h-96l17 78h-196l14 67l265 283h87l-55 -261h72zM214 153l18 53h-5l-32 -49l-60 -66l-41 -32l53 8h50z" />
<glyph unicode="&#x20ac;" d="M45 324h64q4 30 10 58h-94l52 93h73q34 76 83 128q56 58 123 84.5t127 26.5q49 0 85 -9.5t60 -23.5l-53 -90q-18 9 -44.5 14t-63.5 5q-39 0 -82.5 -21t-80.5 -64q-19 -22 -34 -50h254l-46 -93h-244q-7 -27 -10 -58h222l-46 -93h-178q2 -22 6 -41q7 -31 24.5 -53.5 t46 -34.5t72.5 -12q37 0 67 9t56 23l17 -91q-34 -22 -83 -33.5t-97 -11.5q-59 0 -101.5 18.5t-70.5 51.5t-41 78q-13 44 -13 97h-111z" />
<glyph unicode="&#x2122;" horiz-adv-x="953" d="M458 590h-118v-250h-130v250h-118v110h366v-110zM863 484l17 103h-6l-36 -85l-68 -117h-78l-68 114l-39 88h-5l23 -102v-145h-111v360h130l91 -150l24 -63h1l25 65l84 148h129v-360h-113v144z" />
<glyph unicode="&#x2212;" horiz-adv-x="505" d="M58 394h442v-112h-442v112z" />
<glyph unicode="&#xe000;" horiz-adv-x="500" d="M0 500h500v-500h-500v500z" />
<glyph unicode="&#xf408;" horiz-adv-x="431" d="M466 870h87l101 -93l-8 -39h-88l-45 41l-13 34l-27 -33l-67 -42h-94l9 40z" />
<glyph unicode="&#xf40d;" horiz-adv-x="349" d="M258 800q38 35 69 48t58 13q21 0 40.5 -5.5t38 -12t36 -12t35.5 -5.5q15 0 30 5t31 19l10 -67q-33 -27 -60 -37t-51 -10q-21 0 -39.5 5.5t-36.5 11.5t-35.5 11.5t-36.5 5.5q-18 0 -37.5 -7t-42.5 -26z" />
<glyph unicode="&#xf662;" horiz-adv-x="402" d="M243 544l20 46l-35 -32l-63 -36l-37 75l178 110h82l-91 -427h-110z" />
<glyph unicode="&#xf6c9;" horiz-adv-x="238" d="M434 870h157l-6 -30l-172 -88h-104z" />
<glyph unicode="&#xf6cb;" horiz-adv-x="242" d="M191 798q0 29 20 43.5t52 14.5t52 -14.5t20 -43.5t-20 -43t-52 -14t-52 14t-20 43zM395 798q0 29 20 43.5t52 14.5t52 -14.5t20 -43.5t-20 -43t-52 -14t-52 14t-20 43z" />
<glyph unicode="&#xf6ce;" horiz-adv-x="238" d="M494 752h-90l-131 88l6 30h140z" />
<glyph horiz-adv-x="184" d="M342 714l73 -66l-576 -662l-73 69z" />
<glyph horiz-adv-x="244" d="M0 0z" />
<hkern u1="&#x3a;" u2="&#x2f;" k="-20" />
<hkern u1="B" u2="T" k="29" />
<hkern u1="D" u2="J" k="21" />
<hkern u1="O" u2="J" k="21" />
<hkern u1="Q" u2="J" k="21" />
<hkern u1="T" u2="&#x2122;" k="-16" />
<hkern u1="T" u2="&#x2026;" k="105" />
<hkern u1="T" u2="&#x201e;" k="110" />
<hkern u1="T" u2="&#x201a;" k="110" />
<hkern u1="T" u2="&#x2014;" k="122" />
<hkern u1="T" u2="&#x2013;" k="122" />
<hkern u1="T" u2="&#x237;" k="108" />
<hkern u1="T" u2="&#x178;" k="67" />
<hkern u1="T" u2="&#x153;" k="104" />
<hkern u1="T" u2="&#x152;" k="38" />
<hkern u1="T" u2="&#xff;" k="104" />
<hkern u1="T" u2="&#xfd;" k="104" />
<hkern u1="T" u2="&#xfc;" k="16" />
<hkern u1="T" u2="&#xfb;" k="16" />
<hkern u1="T" u2="&#xfa;" k="16" />
<hkern u1="T" u2="&#xf9;" k="16" />
<hkern u1="T" u2="&#xf8;" k="104" />
<hkern u1="T" u2="&#xf6;" k="35" />
<hkern u1="T" u2="&#xf5;" k="50" />
<hkern u1="T" u2="&#xf4;" k="73" />
<hkern u1="T" u2="&#xf3;" k="116" />
<hkern u1="T" u2="&#xf2;" k="82" />
<hkern u1="T" u2="&#xf1;" k="16" />
<hkern u1="T" u2="&#xf0;" k="58" />
<hkern u1="T" u2="&#xef;" k="16" />
<hkern u1="T" u2="&#xee;" k="16" />
<hkern u1="T" u2="&#xed;" k="16" />
<hkern u1="T" u2="&#xec;" k="16" />
<hkern u1="T" u2="&#xeb;" k="30" />
<hkern u1="T" u2="&#xea;" k="71" />
<hkern u1="T" u2="&#xe9;" k="104" />
<hkern u1="T" u2="&#xe8;" k="87" />
<hkern u1="T" u2="&#xe7;" k="104" />
<hkern u1="T" u2="&#xe6;" k="108" />
<hkern u1="T" u2="&#xe5;" k="104" />
<hkern u1="T" u2="&#xe4;" k="38" />
<hkern u1="T" u2="&#xe3;" k="58" />
<hkern u1="T" u2="&#xe2;" k="93" />
<hkern u1="T" u2="&#xe1;" k="104" />
<hkern u1="T" u2="&#xe0;" k="104" />
<hkern u1="T" u2="&#xdd;" k="67" />
<hkern u1="T" u2="&#xd8;" k="38" />
<hkern u1="T" u2="&#xd6;" k="38" />
<hkern u1="T" u2="&#xd5;" k="38" />
<hkern u1="T" u2="&#xd4;" k="38" />
<hkern u1="T" u2="&#xd3;" k="38" />
<hkern u1="T" u2="&#xd2;" k="38" />
<hkern u1="T" u2="&#xc7;" k="38" />
<hkern u1="T" u2="&#xc6;" k="113" />
<hkern u1="T" u2="&#xc5;" k="75" />
<hkern u1="T" u2="&#xc4;" k="75" />
<hkern u1="T" u2="&#xc3;" k="75" />
<hkern u1="T" u2="&#xc2;" k="75" />
<hkern u1="T" u2="&#xc1;" k="75" />
<hkern u1="T" u2="&#xc0;" k="75" />
<hkern u1="T" u2="&#xae;" k="-16" />
<hkern u1="T" u2="&#xad;" k="122" />
<hkern u1="T" u2="&#xa0;" k="38" />
<hkern u1="T" u2="&#x7d;" k="-13" />
<hkern u1="T" u2="z" k="100" />
<hkern u1="T" u2="y" k="104" />
<hkern u1="T" u2="x" k="113" />
<hkern u1="T" u2="w" k="96" />
<hkern u1="T" u2="v" k="104" />
<hkern u1="T" u2="u" k="108" />
<hkern u1="T" u2="t" k="35" />
<hkern u1="T" u2="s" k="108" />
<hkern u1="T" u2="r" k="108" />
<hkern u1="T" u2="q" k="104" />
<hkern u1="T" u2="p" k="108" />
<hkern u1="T" u2="o" k="104" />
<hkern u1="T" u2="n" k="108" />
<hkern u1="T" u2="m" k="108" />
<hkern u1="T" u2="j" k="16" />
<hkern u1="T" u2="i" k="16" />
<hkern u1="T" u2="g" k="104" />
<hkern u1="T" u2="e" k="104" />
<hkern u1="T" u2="d" k="104" />
<hkern u1="T" u2="c" k="104" />
<hkern u1="T" u2="a" k="104" />
<hkern u1="T" u2="]" k="-13" />
<hkern u1="T" u2="Z" k="47" />
<hkern u1="T" u2="Y" k="67" />
<hkern u1="T" u2="X" k="44" />
<hkern u1="T" u2="W" k="28" />
<hkern u1="T" u2="V" k="31" />
<hkern u1="T" u2="T" k="-20" />
<hkern u1="T" u2="Q" k="38" />
<hkern u1="T" u2="O" k="38" />
<hkern u1="T" u2="G" k="38" />
<hkern u1="T" u2="C" k="38" />
<hkern u1="T" u2="A" k="75" />
<hkern u1="T" u2="&#x2e;" k="105" />
<hkern u1="T" u2="&#x2d;" k="122" />
<hkern u1="T" u2="&#x2c;" k="105" />
<hkern u1="T" u2="&#x2a;" k="-16" />
<hkern u1="T" u2="&#x29;" k="-13" />
<hkern u1="T" u2="&#x20;" k="38" />
<hkern u1="V" u2="&#xff;" k="-13" />
<hkern u1="V" u2="&#xf6;" k="13" />
<hkern u1="V" u2="&#xf5;" k="30" />
<hkern u1="V" u2="&#xf4;" k="47" />
<hkern u1="V" u2="&#xf2;" k="43" />
<hkern u1="V" u2="&#xf0;" k="40" />
<hkern u1="V" u2="&#xeb;" k="10" />
<hkern u1="V" u2="&#xe8;" k="43" />
<hkern u1="V" u2="&#xe4;" k="23" />
<hkern u1="V" u2="&#xe3;" k="36" />
<hkern u1="W" u2="&#xff;" k="-13" />
<hkern u1="W" u2="&#xf6;" k="17" />
<hkern u1="W" u2="&#xf5;" k="31" />
<hkern u1="W" u2="&#xeb;" k="11" />
<hkern u1="W" u2="&#xe4;" k="24" />
<hkern u1="W" u2="&#xe3;" k="31" />
<hkern u1="Y" u2="&#xf6;" k="31" />
<hkern u1="Y" u2="&#xf5;" k="51" />
<hkern u1="Y" u2="&#xf4;" k="65" />
<hkern u1="Y" u2="&#xf2;" k="61" />
<hkern u1="Y" u2="&#xf0;" k="61" />
<hkern u1="Y" u2="&#xeb;" k="31" />
<hkern u1="Y" u2="&#xea;" k="73" />
<hkern u1="Y" u2="&#xe8;" k="61" />
<hkern u1="Y" u2="&#xe4;" k="38" />
<hkern u1="Y" u2="&#xe3;" k="58" />
<hkern u1="a" u2="T" k="104" />
<hkern u1="n" u2="T" k="137" />
<hkern u1="&#xd0;" u2="J" k="21" />
<hkern u1="&#xd2;" u2="J" k="21" />
<hkern u1="&#xd3;" u2="J" k="21" />
<hkern u1="&#xd4;" u2="J" k="21" />
<hkern u1="&#xd5;" u2="J" k="21" />
<hkern u1="&#xd6;" u2="J" k="21" />
<hkern u1="&#xd8;" u2="J" k="21" />
<hkern u1="&#xdd;" u2="&#xf6;" k="31" />
<hkern u1="&#xdd;" u2="&#xf5;" k="51" />
<hkern u1="&#xdd;" u2="&#xf4;" k="65" />
<hkern u1="&#xdd;" u2="&#xf2;" k="61" />
<hkern u1="&#xdd;" u2="&#xf0;" k="61" />
<hkern u1="&#xdd;" u2="&#xeb;" k="31" />
<hkern u1="&#xdd;" u2="&#xea;" k="73" />
<hkern u1="&#xdd;" u2="&#xe8;" k="61" />
<hkern u1="&#xdd;" u2="&#xe4;" k="38" />
<hkern u1="&#xdd;" u2="&#xe3;" k="58" />
<hkern u1="&#xe9;" u2="T" k="63" />
<hkern u1="&#xea;" u2="T" k="63" />
<hkern u1="&#xeb;" u2="T" k="63" />
<hkern u1="&#x178;" u2="&#xf6;" k="31" />
<hkern u1="&#x178;" u2="&#xf5;" k="51" />
<hkern u1="&#x178;" u2="&#xf4;" k="65" />
<hkern u1="&#x178;" u2="&#xf2;" k="61" />
<hkern u1="&#x178;" u2="&#xf0;" k="61" />
<hkern u1="&#x178;" u2="&#xeb;" k="31" />
<hkern u1="&#x178;" u2="&#xea;" k="73" />
<hkern u1="&#x178;" u2="&#xe8;" k="61" />
<hkern u1="&#x178;" u2="&#xe4;" k="38" />
<hkern u1="&#x178;" u2="&#xe3;" k="58" />
<hkern u1="&#x2026;" u2="&#xad;" k="117" />
<hkern u1="&#x2026;" u2="&#xa0;" k="40" />
<hkern u1="&#x2026;" u2="&#x20;" k="40" />
<hkern g1="C,Ccedilla" 	g2="space,uni00A0" 	k="38" />
<hkern g1="C,Ccedilla" 	g2="hyphen,uni00AD,endash,emdash" 	k="66" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="42" />
<hkern g1="C,Ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="42" />
<hkern g1="C,Ccedilla" 	g2="V" 	k="63" />
<hkern g1="C,Ccedilla" 	g2="W" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="48" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="52" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="47" />
<hkern g1="C,Ccedilla" 	g2="m,n,p,r,s,u,ae,uni0237" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="33" />
<hkern g1="C,Ccedilla" 	g2="v,y,yacute,ydieresis" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="22" />
<hkern g1="C,Ccedilla" 	g2="AE" 	k="115" />
<hkern g1="C,Ccedilla" 	g2="i,j,igrave,iacute,icircumflex,idieresis,ntilde,ugrave,uacute,ucircumflex,udieresis" 	k="11" />
<hkern g1="C,Ccedilla" 	g2="z" 	k="8" />
<hkern g1="L" 	g2="space,uni00A0" 	k="48" />
<hkern g1="L" 	g2="asterisk" 	k="156" />
<hkern g1="L" 	g2="hyphen,uni00AD,endash,emdash" 	k="66" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="117" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="88" />
<hkern g1="L" 	g2="T" 	k="124" />
<hkern g1="L" 	g2="V" 	k="113" />
<hkern g1="L" 	g2="W" 	k="82" />
<hkern g1="L" 	g2="X" 	k="135" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="121" />
<hkern g1="L" 	g2="Z" 	k="84" />
<hkern g1="L" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="23" />
<hkern g1="L" 	g2="t" 	k="29" />
<hkern g1="L" 	g2="v,y,yacute,ydieresis" 	k="85" />
<hkern g1="L" 	g2="w" 	k="55" />
<hkern g1="L" 	g2="AE" 	k="114" />
<hkern g1="L" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="135" />
<hkern g1="L" 	g2="quotesinglbase,quotedblbase" 	k="-6" />
<hkern g1="L" 	g2="registered,trademark" 	k="172" />
<hkern g1="L" 	g2="z" 	k="13" />
<hkern g1="L" 	g2="x" 	k="11" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="space,uni00A0" 	k="13" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="parenright,bracketright,braceright" 	k="16" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="asterisk" 	k="9" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="29" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="26" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="38" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="36" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="26" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="52" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="45" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="23" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="t" 	k="-10" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="v,y,yacute,ydieresis" 	k="-10" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="w" 	k="-10" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="58" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="38" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="38" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="7" />
<hkern g1="P,Thorn" 	g2="space,uni00A0" 	k="38" />
<hkern g1="P,Thorn" 	g2="hyphen,uni00AD,endash,emdash" 	k="9" />
<hkern g1="P,Thorn" 	g2="comma,period,ellipsis" 	k="124" />
<hkern g1="P,Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="73" />
<hkern g1="P,Thorn" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="11" />
<hkern g1="P,Thorn" 	g2="T" 	k="8" />
<hkern g1="P,Thorn" 	g2="V" 	k="9" />
<hkern g1="P,Thorn" 	g2="W" 	k="6" />
<hkern g1="P,Thorn" 	g2="X" 	k="51" />
<hkern g1="P,Thorn" 	g2="Y,Yacute,Ydieresis" 	k="26" />
<hkern g1="P,Thorn" 	g2="Z" 	k="19" />
<hkern g1="P,Thorn" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="7" />
<hkern g1="P,Thorn" 	g2="t" 	k="-13" />
<hkern g1="P,Thorn" 	g2="v,y,yacute,ydieresis" 	k="-25" />
<hkern g1="P,Thorn" 	g2="w" 	k="-16" />
<hkern g1="P,Thorn" 	g2="AE" 	k="124" />
<hkern g1="P,Thorn" 	g2="quotesinglbase,quotedblbase" 	k="120" />
<hkern g1="R" 	g2="hyphen,uni00AD,endash,emdash" 	k="38" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="36" />
<hkern g1="R" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="R" 	g2="T" 	k="46" />
<hkern g1="R" 	g2="V" 	k="46" />
<hkern g1="R" 	g2="W" 	k="38" />
<hkern g1="R" 	g2="X" 	k="53" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="57" />
<hkern g1="R" 	g2="Z" 	k="23" />
<hkern g1="R" 	g2="m,n,p,r,s,u,ae,uni0237" 	k="11" />
<hkern g1="R" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="49" />
<hkern g1="R" 	g2="t" 	k="13" />
<hkern g1="R" 	g2="v,y,yacute,ydieresis" 	k="11" />
<hkern g1="R" 	g2="w" 	k="8" />
<hkern g1="R" 	g2="AE" 	k="123" />
<hkern g1="R" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="19" />
<hkern g1="R" 	g2="i,j,igrave,iacute,icircumflex,idieresis,ntilde,ugrave,uacute,ucircumflex,udieresis" 	k="8" />
<hkern g1="R" 	g2="z" 	k="21" />
<hkern g1="R" 	g2="x" 	k="11" />
<hkern g1="V" 	g2="space,uni00A0" 	k="29" />
<hkern g1="V" 	g2="asterisk" 	k="-37" />
<hkern g1="V" 	g2="hyphen,uni00AD,endash,emdash" 	k="51" />
<hkern g1="V" 	g2="comma,period,ellipsis" 	k="106" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="64" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="36" />
<hkern g1="V" 	g2="T" 	k="31" />
<hkern g1="V" 	g2="V" 	k="34" />
<hkern g1="V" 	g2="W" 	k="28" />
<hkern g1="V" 	g2="X" 	k="34" />
<hkern g1="V" 	g2="Y,Yacute,Ydieresis" 	k="39" />
<hkern g1="V" 	g2="Z" 	k="34" />
<hkern g1="V" 	g2="m,n,p,r,s,u,ae,uni0237" 	k="49" />
<hkern g1="V" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="58" />
<hkern g1="V" 	g2="t" 	k="10" />
<hkern g1="V" 	g2="AE" 	k="115" />
<hkern g1="V" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-38" />
<hkern g1="V" 	g2="quotesinglbase,quotedblbase" 	k="101" />
<hkern g1="V" 	g2="registered,trademark" 	k="-13" />
<hkern g1="V" 	g2="z" 	k="35" />
<hkern g1="V" 	g2="x" 	k="28" />
<hkern g1="W" 	g2="space,uni00A0" 	k="26" />
<hkern g1="W" 	g2="asterisk" 	k="-33" />
<hkern g1="W" 	g2="hyphen,uni00AD,endash,emdash" 	k="29" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="81" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="42" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="26" />
<hkern g1="W" 	g2="T" 	k="28" />
<hkern g1="W" 	g2="V" 	k="28" />
<hkern g1="W" 	g2="W" 	k="31" />
<hkern g1="W" 	g2="X" 	k="28" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="39" />
<hkern g1="W" 	g2="Z" 	k="31" />
<hkern g1="W" 	g2="m,n,p,r,s,u,ae,uni0237" 	k="35" />
<hkern g1="W" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="44" />
<hkern g1="W" 	g2="t" 	k="9" />
<hkern g1="W" 	g2="AE" 	k="92" />
<hkern g1="W" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-38" />
<hkern g1="W" 	g2="quotesinglbase,quotedblbase" 	k="78" />
<hkern g1="W" 	g2="registered,trademark" 	k="-16" />
<hkern g1="W" 	g2="z" 	k="30" />
<hkern g1="W" 	g2="x" 	k="21" />
<hkern g1="K,X" 	g2="space,uni00A0" 	k="29" />
<hkern g1="K,X" 	g2="hyphen,uni00AD,endash,emdash" 	k="76" />
<hkern g1="K,X" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="K,X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="52" />
<hkern g1="K,X" 	g2="T" 	k="44" />
<hkern g1="K,X" 	g2="V" 	k="34" />
<hkern g1="K,X" 	g2="W" 	k="28" />
<hkern g1="K,X" 	g2="X" 	k="34" />
<hkern g1="K,X" 	g2="Y,Yacute,Ydieresis" 	k="38" />
<hkern g1="K,X" 	g2="Z" 	k="13" />
<hkern g1="K,X" 	g2="m,n,p,r,s,u,ae,uni0237" 	k="11" />
<hkern g1="K,X" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="36" />
<hkern g1="K,X" 	g2="t" 	k="41" />
<hkern g1="K,X" 	g2="v,y,yacute,ydieresis" 	k="48" />
<hkern g1="K,X" 	g2="w" 	k="40" />
<hkern g1="K,X" 	g2="AE" 	k="120" />
<hkern g1="K,X" 	g2="quotesinglbase,quotedblbase" 	k="-13" />
<hkern g1="K,X" 	g2="i,j,igrave,iacute,icircumflex,idieresis,ntilde,ugrave,uacute,ucircumflex,udieresis" 	k="11" />
<hkern g1="K,X" 	g2="z" 	k="8" />
<hkern g1="K,X" 	g2="x" 	k="11" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="space,uni00A0" 	k="26" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="asterisk" 	k="-23" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="65" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="112" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="84" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="45" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="67" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="V" 	k="39" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="28" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="X" 	k="38" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="39" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="55" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,s,u,ae,uni0237" 	k="73" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="91" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="43" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v,y,yacute,ydieresis" 	k="24" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="27" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="117" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-34" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="110" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="registered,trademark" 	k="-16" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="56" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="53" />
<hkern g1="Z" 	g2="space,uni00A0" 	k="21" />
<hkern g1="Z" 	g2="hyphen,uni00AD,endash,emdash" 	k="52" />
<hkern g1="Z" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="24" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="23" />
<hkern g1="Z" 	g2="T" 	k="23" />
<hkern g1="Z" 	g2="V" 	k="34" />
<hkern g1="Z" 	g2="W" 	k="28" />
<hkern g1="Z" 	g2="X" 	k="26" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="55" />
<hkern g1="Z" 	g2="Z" 	k="10" />
<hkern g1="Z" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="21" />
<hkern g1="Z" 	g2="t" 	k="10" />
<hkern g1="Z" 	g2="v,y,yacute,ydieresis" 	k="-13" />
<hkern g1="Z" 	g2="AE" 	k="123" />
<hkern g1="Z" 	g2="z" 	k="11" />
<hkern g1="c,ccedilla" 	g2="space,uni00A0" 	k="30" />
<hkern g1="c,ccedilla" 	g2="parenright,bracketright,braceright" 	k="13" />
<hkern g1="c,ccedilla" 	g2="hyphen,uni00AD,endash,emdash" 	k="27" />
<hkern g1="c,ccedilla" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="c,ccedilla" 	g2="v,y,yacute,ydieresis" 	k="-10" />
<hkern g1="c,ccedilla" 	g2="w" 	k="-7" />
<hkern g1="c,ccedilla" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="20" />
<hkern g1="c,ccedilla" 	g2="x" 	k="-10" />
<hkern g1="e,egrave,eacute,ecircumflex,edieresis,oe" 	g2="parenright,bracketright,braceright" 	k="13" />
<hkern g1="e,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="129" />
<hkern g1="e,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="-10" />
<hkern g1="e,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="46" />
<hkern g1="e,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="8" />
<hkern g1="f" 	g2="space,uni00A0" 	k="11" />
<hkern g1="f" 	g2="parenright,bracketright,braceright" 	k="-86" />
<hkern g1="f" 	g2="asterisk" 	k="-113" />
<hkern g1="f" 	g2="hyphen,uni00AD,endash,emdash" 	k="11" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="30" />
<hkern g1="f" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-10" />
<hkern g1="f" 	g2="t" 	k="-25" />
<hkern g1="f" 	g2="w" 	k="-11" />
<hkern g1="f" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="-106" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="31" />
<hkern g1="f" 	g2="registered,trademark" 	k="-111" />
<hkern g1="f" 	g2="i,j,igrave,iacute,icircumflex,idieresis,ntilde,ugrave,uacute,ucircumflex,udieresis" 	k="-30" />
<hkern g1="f" 	g2="z" 	k="-13" />
<hkern g1="f" 	g2="x" 	k="-10" />
<hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="parenright,bracketright,braceright" 	k="28" />
<hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="asterisk" 	k="30" />
<hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,ellipsis" 	k="11" />
<hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v,y,yacute,ydieresis" 	k="18" />
<hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="w" 	k="6" />
<hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="68" />
<hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotesinglbase,quotedblbase" 	k="11" />
<hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="registered,trademark" 	k="30" />
<hkern g1="b,o,p,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="x" 	k="27" />
<hkern g1="t" 	g2="v,y,yacute,ydieresis" 	k="13" />
<hkern g1="t" 	g2="w" 	k="13" />
<hkern g1="t" 	g2="x" 	k="-16" />
<hkern g1="v,y,yacute,ydieresis" 	g2="space,uni00A0" 	k="13" />
<hkern g1="v,y,yacute,ydieresis" 	g2="asterisk" 	k="-20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="comma,period,ellipsis" 	k="54" />
<hkern g1="v,y,yacute,ydieresis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="v,y,yacute,ydieresis" 	g2="t" 	k="-22" />
<hkern g1="v,y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="24" />
<hkern g1="v,y,yacute,ydieresis" 	g2="z" 	k="-5" />
<hkern g1="v,y,yacute,ydieresis" 	g2="x" 	k="18" />
<hkern g1="w" 	g2="space,uni00A0" 	k="11" />
<hkern g1="w" 	g2="parenright,bracketright,braceright" 	k="13" />
<hkern g1="w" 	g2="asterisk" 	k="-16" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="30" />
<hkern g1="w" 	g2="t" 	k="-20" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="31" />
<hkern g1="w" 	g2="z" 	k="8" />
<hkern g1="w" 	g2="x" 	k="8" />
<hkern g1="k,x" 	g2="space,uni00A0" 	k="27" />
<hkern g1="k,x" 	g2="hyphen,uni00AD,endash,emdash" 	k="33" />
<hkern g1="k,x" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="k,x" 	g2="v,y,yacute,ydieresis" 	k="10" />
<hkern g1="z" 	g2="space,uni00A0" 	k="11" />
<hkern g1="z" 	g2="hyphen,uni00AD,endash,emdash" 	k="13" />
<hkern g1="z" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="z" 	g2="v,y,yacute,ydieresis" 	k="-16" />
<hkern g1="z" 	g2="w" 	k="-13" />
<hkern g1="space,uni00A0" 	g2="asterisk" 	k="35" />
<hkern g1="space,uni00A0" 	g2="hyphen,uni00AD,endash,emdash" 	k="44" />
<hkern g1="space,uni00A0" 	g2="comma,period,ellipsis" 	k="70" />
<hkern g1="space,uni00A0" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="40" />
<hkern g1="space,uni00A0" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="13" />
<hkern g1="space,uni00A0" 	g2="T" 	k="36" />
<hkern g1="space,uni00A0" 	g2="V" 	k="30" />
<hkern g1="space,uni00A0" 	g2="W" 	k="25" />
<hkern g1="space,uni00A0" 	g2="X" 	k="30" />
<hkern g1="space,uni00A0" 	g2="Y,Yacute,Ydieresis" 	k="30" />
<hkern g1="space,uni00A0" 	g2="Z" 	k="11" />
<hkern g1="space,uni00A0" 	g2="v,y,yacute,ydieresis" 	k="13" />
<hkern g1="space,uni00A0" 	g2="w" 	k="11" />
<hkern g1="space,uni00A0" 	g2="AE" 	k="22" />
<hkern g1="space,uni00A0" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="84" />
<hkern g1="space,uni00A0" 	g2="quotesinglbase,quotedblbase" 	k="84" />
<hkern g1="space,uni00A0" 	g2="z" 	k="11" />
<hkern g1="space,uni00A0" 	g2="x" 	k="27" />
<hkern g1="comma,period,ellipsis" 	g2="space,uni00A0" 	k="60" />
<hkern g1="comma,period,ellipsis" 	g2="asterisk" 	k="132" />
<hkern g1="comma,period,ellipsis" 	g2="hyphen,uni00AD,endash,emdash" 	k="117" />
<hkern g1="comma,period,ellipsis" 	g2="comma,period,ellipsis" 	k="-13" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="55" />
<hkern g1="comma,period,ellipsis" 	g2="V" 	k="62" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="40" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="70" />
<hkern g1="comma,period,ellipsis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="11" />
<hkern g1="comma,period,ellipsis" 	g2="v,y,yacute,ydieresis" 	k="53" />
<hkern g1="comma,period,ellipsis" 	g2="w" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="140" />
<hkern g1="comma,period,ellipsis" 	g2="registered,trademark" 	k="88" />
<hkern g1="comma,period,ellipsis" 	g2="z" 	k="-8" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="space,uni00A0" 	k="76" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="parenright,bracketright,braceright" 	k="34" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="asterisk" 	k="39" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="hyphen,uni00AD,endash,emdash" 	k="57" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="comma,period,ellipsis" 	k="112" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="21" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="T" 	k="67" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="V" 	k="28" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="W" 	k="19" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="X" 	k="42" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="39" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Z" 	k="9" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="AE" 	k="39" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	k="177" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="quotesinglbase,quotedblbase" 	k="113" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="z" 	k="13" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="x" 	k="33" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="space,uni00A0" 	k="86" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="hyphen,uni00AD,endash,emdash" 	k="191" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="comma,period,ellipsis" 	k="124" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="122" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="38" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="V" 	k="-38" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="W" 	k="-38" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-34" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="60" />
<hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright" 	g2="AE" 	k="190" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="space,uni00A0" 	k="91" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="asterisk" 	k="100" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="hyphen,uni00AD,endash,emdash" 	k="153" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-13" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="57" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="118" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="V" 	k="105" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="82" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="X" 	k="-13" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="118" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="11" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v,y,yacute,ydieresis" 	k="51" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="28" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="AE" 	k="-25" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="registered,trademark" 	k="75" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="z" 	k="-13" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="hyphen,uni00AD,endash,emdash" 	k="34" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="asterisk" 	g2="space,uni00A0" 	k="34" />
<hkern g1="asterisk" 	g2="hyphen,uni00AD,endash,emdash" 	k="182" />
<hkern g1="asterisk" 	g2="comma,period,ellipsis" 	k="238" />
<hkern g1="asterisk" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="52" />
<hkern g1="asterisk" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="9" />
<hkern g1="asterisk" 	g2="T" 	k="-16" />
<hkern g1="asterisk" 	g2="V" 	k="-9" />
<hkern g1="asterisk" 	g2="W" 	k="-13" />
<hkern g1="asterisk" 	g2="Y,Yacute,Ydieresis" 	k="-9" />
<hkern g1="asterisk" 	g2="v,y,yacute,ydieresis" 	k="-20" />
<hkern g1="asterisk" 	g2="w" 	k="-10" />
<hkern g1="asterisk" 	g2="AE" 	k="119" />
<hkern g1="asterisk" 	g2="quotesinglbase,quotedblbase" 	k="94" />
<hkern g1="seven" 	g2="space,uni00A0" 	k="51" />
<hkern g1="seven" 	g2="hyphen,uni00AD,endash,emdash" 	k="61" />
<hkern g1="seven" 	g2="comma,period,ellipsis" 	k="113" />
<hkern g1="seven" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="63" />
<hkern g1="seven" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="28" />
<hkern g1="seven" 	g2="AE" 	k="86" />
<hkern g1="seven" 	g2="quotesinglbase,quotedblbase" 	k="109" />
<hkern g1="r" 	g2="hyphen,uni00AD,endash,emdash" 	k="13" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="80" />
</font>
</defs></svg> 