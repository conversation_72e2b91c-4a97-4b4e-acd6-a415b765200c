<?php

function personal_data_custom_page_ids ()
{
	$dirname = $_SERVER['DOCUMENT_ROOT'] . "/portal/media/" . $_SESSION['subdomain'] . "/";
	
	if (file_exists($dirname . "themesettings.set")) {
		
		$data_info = json_decode(file_get_contents($dirname . "themesettings.set"), true);
		if ($_GET['test1']) {
			echo json_encode($data_info);
			die();
		}
		
	}
	$pages = "";
	unset($_SESSION['personal_data']);
	if(!empty($_GET['personal_data'])){
		$_SESSION['personal_data'] = $_GET['personal_data'];
		$all_pages =  array_keys($data_info['dashboard']['personal_info_config']);
		$pages = implode(",", $all_pages);
	}
	return $pages;
}
