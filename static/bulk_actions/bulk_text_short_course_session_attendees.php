<?php
include('../../engine/admin/inc/lib.inc.php');
include_once(base_path."/admin/config.php");
$css =  website_url."/static/bulk_actions/modal.css";
$ids = rtrim($_GET['ids'],',');
$admin_url = website_url."/admin/";
?>
<link rel="stylesheet" href="<?php echo $css; ?>" type="text/css" media="screen">

<div class="modal-dialog" role="document">
    <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="myModalLabel">Bulk SMS</h4>
        </div>
        <div class="modal-body">
            <div id="message" name="message" style="width: 100%;float: left;"></div>
            <form style="">
                <?php
                $messages = new CommunicationMessages;
                $args =[
                    'school_id' => $_SESSION['usergroup']
                ];

                $templates_list = $messages->sms_templates($args);
                $email_templates_list_json = json_encode($templates_list);

                if(count($templates_list)){
                    ?>
                    <div class="message_top_option">
                        <div class="btn-group">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                Choose a template <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu bulk_email_templates_dropdown">
                                <?php
                                $ti=0;
                                foreach ($templates_list as $template) { ?>
                                    <li>
                                        <a href="#" id="<?php echo $ti; ?>">
                                            <?php echo $template['title']; ?>
                                        </a>
                                    </li>
                                    <?php $ti++;
                                } ?>
                            </ul>
                        </div>
                    </div>
                <?php } ?>
                <label for="bulk_sms_content">Message Content</label>
                <div id="maxCharacter" class="alert alert-danger hide">
                    The number of characters in the message exceed the maximum number of characters allowed (360),
                    therefore you can not add more characters to the template if the number of characters is above or equal to 360.0.
                </div>
                <textarea oninput="characterCount()" name="email" id="bulk_sms_content" class="form-control" placeholder="SMS content" style="min-height: 200px;" maxlength="360"></textarea>
                <input type="hidden" id="bulk_sms_template">
                <input type="hidden" id="sc_id" name="sc_id"  value="<?php echo $_GET['ref']; ?>">
                <input type="hidden" name="ids"  id="ids" value="<?php echo $ids; ?>">
            </form>

            <script type="text/javascript">
                function characterCount(){
                    let textMessage = document.getElementById("bulk_sms_content").value;
                    if (textMessage.length >= 360){
                        $("#maxCharacter").removeClass('hide')
                    }else{
                        $("#maxCharacter").addClass('hide')
                    }
                }
                $(document).ready(function(){
                    templates_list =<?php echo $email_templates_list_json; ?>

                        $(document).on("click", ".bulk_email_templates_dropdown li a", function() {

                            const idy = $(this).attr("id");
                            const html_message = templates_list[idy];

                            $("#bulk_sms_content").val(html_message.message).focus();
                            $('.bulk_email_templates_dropdown .dropdown-toggle').dropdown('toggle');
                            $("#bulk_sms_template").val(html_message.id);
                            characterCount();
                            return false;
                        });

                });
            </script>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            <button  id="bulk_send_sms" type="button" class="btn btn-primary" onclick="submit_bulk_email_form()">Send SMS</button>
        </div>
    </div>
</div>
<script type="text/javascript">
    function submit_bulk_email_form(){
        $('#bulk_send_sms').html('Sending SMS...').attr('disabled', true);
        const attendees = $('#ids').val();
        const sms = $('#bulk_sms_content').val();
        const sc_id = $('#sc_id').val();
        const template_id = $('#bulk_sms_template').val();
        $.ajax({
            type: "POST",
            url: "<?php echo $admin_url ?>shortcourses/bulk_sms.json",
            data: {
                attendees: attendees,
                sms: sms,
                sc_id: sc_id,
                template_id: template_id,
                session_attendee:true
            },
            success: function(result) {
                pop_message("SMSes sent successfully, please visit the logs to check the message statuses");
                timedRefresh(2000);

            },
            error: function(result) {
                pop_message(result);
            }
        });
    }
    function timedRefresh(timeoutPeriod) {
        setTimeout("location.reload(true);",timeoutPeriod);
    }
</script>
