<?php
include('../../engine/admin/inc/lib.inc.php');
include_once(base_path . "/admin/config.php");
$css =  website_url . "/static/bulk_actions/modal.css";
$ids = rtrim($_GET['ids'], ',');
$scheduled_booking_id = $_GET['ref'];
//get all booking except for number 1 which is register_interest_list
$admin_url = website_url . "/admin/";
?>
<link rel="stylesheet" href="<?php echo $css; ?>" type="text/css" media="screen">
<script type="text/javascript" src="<?php echo website_url; ?>/admin/assets/js/jquery.smartWizard.min.js"></script>
<link href="<?php echo website_url; ?>/admin/assets/css/smart_wizard_theme_dots.css" rel="stylesheet" type="text/css" />

<div class="modal-header">
	<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
	<h4 class="modal-title" id="myModalLabel">Bulk Add Students to Waiting List</h4>
</div>
<div class="modal-body">
	<div id="register_interest_list_message" name="message" style="width: 100%;display: block;"></div>
	
	<div id="smartwizard">
		<ul>
			<li><a href="#step-1">1<br /><small>Add to Waiting List</small></small></a></li>
			<li><a href="#step-2">2<br /><small>Confirm</small></a></li>
		</ul>

		<div id="step-1" class="" style="padding: 15px 0 0;">
			<form class="form-group">
				<input type="hidden" name="ids" id="ids" value="<?php echo $ids; ?>">
				<div class="col-sm-12">
					<div class="row">
						<label for="notes">Waiting List Notes</label>
						<textarea name="register_interest_waiting_list_notes" id="register_interest_waiting_list_notes" class="col-sm-12"></textarea>
					</div>
				</div>

			</form>
		</div>
		<div id="step-2" style="display: none;">
			<div id="message" name="message" style="width: 100%;float: left;display: block;"></div>

			<div class="alert alert-info" id="book_form" style="display: block;">
                Do you want to remove students from register interest list?</p>
			</div>
		</div>
		
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-default pull-left" data-dismiss="modal">Cancel</button>
		<button type="button" class="btn btn-primary pull-left" id="prev-btn">Previous</button>
		<button type="button" class="btn btn-primary" id="next-btn">Next</button>
		<button type="button" class="btn btn-primary hidden" id="close-btn" data-dismiss="modal">Close</button>
		<button type="button" class="btn btn-success hidden" id="yes-btn" onclick="add_students_from_register_interest_list_to_waiting_list('yes')">Yes</button>
		<button type="button" class="btn btn-danger hidden" id="no-btn" onclick="add_students_from_register_interest_list_to_waiting_list('no')">No</button>
	</div>
</div>



<script type="text/javascript">
    $(document).ready(function() {
	// Step show event
	$("#smartwizard").on("showStep", function(e, anchorObject, stepNumber, stepDirection, stepPosition) {
		if (stepNumber == 0) {
			$('#next-btn').removeClass('hidden');
			$('#prev-btn').addClass('hidden');
			$('#close-btn').addClass('hidden');
			$('#yes-btn').addClass('hidden');
			$('#no-btn').addClass('hidden');
		} else if (stepNumber == 1) {
			$("div#message").html("");
			$('#prev-btn').removeClass('hidden');
			$('#next-btn').addClass('hidden');
			$('#yes-btn').removeClass('hidden');
			$('#no-btn').removeClass('hidden');
			$('#close-btn').addClass('hidden');
		}
	});


	// Smart Wizard
	$('#smartwizard').smartWizard({
		selected: 0,
		theme: 'dots',
		transitionEffect: 'fade',
		showStepURLhash: false,
		enableAllSteps: true,
		keyNavigation: false,
		anchorSettings: {
			anchorClickable: false, // Enable/Disable anchor navigation
			enableAllAnchors: false, // Activates all anchors clickable all times
			markDoneStep: true, // add done css
			enableAnchorOnDoneStep: true // Enable/Disable the done steps navigation
		},
		toolbarSettings: {
			showNextButton: false,
			showPreviousButton: false
		}
	});


	// External Button Events
	$(document).on("click", "#reset-btn", function() {
		// Reset wizard
		$('#smartwizard').smartWizard("reset");
		return true;
	});

	$(document).on("click", "#prev-btn", function() {
		// Navigate previous
		$('#smartwizard').smartWizard("prev");
		return true;
	});

	$(document).on("click", "#next-btn", function() {
		// Navigate next
        //check if booking status is selected
        //const register_interest_booking_status = $('#register_interest_booking_status').val();
        //if (register_interest_booking_status=='') {
            //$("div#register_interest_list_message").html("<div class=\"alert alert-warning\" role=\"alert\"> Booking status is required</div>");
            //return false;

        //}
		$('#smartwizard').smartWizard("next");
		return true;
	});


	$(document).on("change", "#theme_selector", function() {
		// Change theme
		$('#smartwizard').smartWizard("theme", $(this).val());
		return true;
	});

	// Set selected theme on page refresh
	$("#theme_selector").change();
	$("#send_email_modal").on('shown.bs.modal', function(e) {
		$('#smartwizard').smartWizard("prev");
		$('#smartwizard').smartWizard("prev");
	});
    });
	function timedRefresh(timeoutPeriod) {
		setTimeout("location.reload(true);", timeoutPeriod);
	}

    function add_students_from_register_interest_list_to_waiting_list(choice) {
        $('#no-btn').addClass('hidden');
        $('#yes-btn').addClass('disabled');
        $('#yes-btn').html('Adding Students Please Wait!');
        const register_interests = $('#ids').val();
        const register_interest_waiting_list_notes = $('#register_interest_waiting_list_notes').val();

        $.ajax({
            type: "POST",
            url: "<?php echo $admin_url ?>shortcourses/bulk_add_to_waiting_list_from_register_interest.json",
            dataType: 'json',
            data: {
                register_interests: register_interests,
                register_interest_waiting_list_notes: register_interest_waiting_list_notes,
                register_interest_remove: choice
            },
            success: function(result) {
                //var html = '<div class="alert alert-success" role="alert">' + result + '!</div>';
                $('#book_form').css('display', 'none');
                $("div#register_interest_list_message").html(result);
                pop_message(result);
                timedRefresh(2000);
            },
            error: function(result) {
                $("div#register_interest_list_message").html(result);
            }
        });

    }


</script>