.events_booking .single_appointment{
        border-top: 1px solid #ddd;
        margin-bottom: 10px;
    }
    * {
        /*font-family: 'Bitter', serif;*/
    }
	.events-content a {
    color: #333333;
    text-decoration: none;
	}
    .events_booking .title_text{
        padding: 0px;
        margin: 0px;
    }
    .events_booking .title{
        text-align: left;
        margin: 5px 0px 10px 0px;
    }
    .events_booking .time{
        border-right: 1px solid #ddd;
        line-height: 18px;
    }
    .events_booking .time,.events_booking .events_booking .slot,.events_booking .book, .events_booking .event_name{
        padding-top: 10px;
        padding-bottom: 10px;
        font-size: 13px;
    }
    .events_booking .book{
        text-align: center;
    }
    .events_booking .event_name{
        text-align: left;    
    }
    .events_booking .btn{
       /* background-color: #56c477;
        border-color: #56c477;*/
        font-size: 14px;
    }
    .cd-horizontal-timeline .filling-line{
        background-color:#0159a3!important;
    }
    .events_booking .cd-horizontal-timeline .events a:hover{
        text-decoration: none;
    }
    .events_booking .cd-horizontal-timeline .events a.selected {
    pointer-events: all!important;
    text-decoration: none;
    }
    .events_booking .cd-horizontal-timeline .events a{
        color: #398439;
        font-size: 15px;
    }
    .events_booking .cd-horizontal-timeline .events a.num.btn.disabled{
        color: #959595;
    }
    .events_booking .cd-horizontal-timeline .events{
        top: 80px!important;
    }
    .wizard-step .form-group {
    width: 100%;
    float: left;
    }
    .wizard-step label {
        width: 100%;
        float: left;
        font-size: 12px;
        font-family: "lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
        font-weight: bold;
    }
    .wizard-step .form-control {
    display: block;
    width: 100%;
    height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.428571429;
    color: #555;
    vertical-align: middle;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,0.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,0.075);
    -webkit-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
}

.wizard-step textarea.form-control {
    height: auto;
}
.mb {
    margin-bottom: 15px;
}
.mb-al{ 
color: #c09853;
background-color: #fcf8e3;
border-color: #faebcc;
padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
    font-size: 12px;
}
.progress-bar-info {
    background-color: #5bc0de!important;
}
.progress, .progress-bar-info{
    border-radius: 10px;
}
.progress-bar{
    line-height: 31px;
}
.progress {
    border: 1px solid #e5e5e5;
    background-color: #ccc;
    height: 30px;
}
.add-ress {
   
    border: 1px solid #a59e9e;
    border-radius: 4px;
    padding: 10px;
}

.checklist input[type="checkbox"] {
    float: left;
}

.checklist li {
    width: 100%;
    float: left;
    display: block;
}

.checklist input[type="checkbox"] {
    float: left;
}
.checklist {
    padding-top: 10px;
    float: left;
}
span.chkico {
    float: left;
    padding-top: 5px;
    padding-left: 10px;
}

.bm {
    float: left;
    width: 100%;
    margin-bottom: 10px;
}

.regular-checkbox {
    display: none;
}

.regular-checkbox + label {
    background-color: #fafafa;
    border: 1px solid #cacece;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05);
    padding: 9px;
    border-radius: 3px;
    display: inline-block;
    position: relative;
    width: initial;
}

.regular-checkbox + label:active, .regular-checkbox:checked + label:active {
    box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px 1px 3px rgba(0,0,0,0.1);
}

.regular-checkbox:checked + label {
    background-color: #e9ecee;
    border: 1px solid #adb8c0;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05), inset 15px 10px -12px rgba(255,255,255,0.1);
    color: #99a1a7;
}

.regular-checkbox:checked + label:after {
    content: '\2714';
    font-size: 14px;
    position: absolute;
    top: 0px;
    left: 3px;
    color: #99a1a7;
}

/* RADIO */

.regular-radio {
    display: none;
}

.regular-radio + label {
    -webkit-appearance: none;
    background-color: #fafafa;
    border: 1px solid #cacece;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05);
    padding: 9px;
    border-radius: 50px;
    display: inline-block;
    position: relative;
}

.regular-radio:checked + label:after {
    content: ' ';
    width: 12px;
    height: 12px;
    border-radius: 50px;
    position: absolute;
    top: 3px;
    background: #99a1a7;
    box-shadow: inset 0px 0px 10px rgba(0,0,0,0.3);
    text-shadow: 0px;
    left: 3px;
    font-size: 32px;
}
.cd-horizontal-timeline .events a.older-event::after {
    border-color: #0159a3!important;
}
.cd-horizontal-timeline .events a.selected::after {
    background-color: #0159a3!important;
    border-color: #0159a3!important;
}
.regular-radio:checked + label {
    background-color: #e9ecee;
    color: #99a1a7;
    border: 1px solid #adb8c0;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05), inset 15px 10px -12px rgba(255,255,255,0.1), inset 0px 0px 10px rgba(0,0,0,0.1);
}

.regular-radio + label:active, .regular-radio:checked + label:active {
    box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px 1px 3px rgba(0,0,0,0.1);
}
.radio-float {
    float: left;
    width: 100%;
    margin-top: 6px;
}
.wizard-body{
    overflow-y: auto;
    height: 450px;
}
.single_appointment {
    border-bottom: 2px solid #dddddd;
    padding-bottom: 3px;
    padding-top: 9px;
}

.single_appointment .col-md-3 {
    padding-left: 0px;
}
.single_appointment .btn{
    line-height: 1.1;
    margin-top: 0px;
    float: right;
}

.single_appointment .collapse {
    width: 100%;
    float: left;
    margin-top: 10px;
    margin-bottom: 10px;
}
.single_appointment .collapse th{
    padding-left: 0px;
    font-weight: bold;
}
.events_booking{
    height: auto;
    overflow-y: auto;
}
.cd-horizontal-timeline .events a{
    padding-bottom: 5px;
}
.single_appointment .btn
{
    /*background: #7b9d6f;*/
}
.cd-horizontal-timeline .events a.selected{
	pointer-events: none;
    background: #0159a3;
    color: #fff;
    //border-radius: 50px;
	opacity: 1;
}
span.mil.tiri:hover{
	opacity: 1;
	padding: 6px;
	-webkit-transition: background-color 2s ease-out;
	-moz-transition: background-color 2s ease-out;
	-o-transition: background-color 2s ease-out;
	transition: background-color 2s ease-out;
}
.cd-horizontal-timeline .events a.selected span.mil.tiri{
	opacity: 1;
	padding: 0px;
		-webkit-transition: background-color 2s ease-out;
	-moz-transition: background-color 2s ease-out;
	-o-transition: background-color 2s ease-out;
	transition: background-color 2s ease-out;

}
span.mil.tiri {
        background: #0159a3;
    color: #fff;
    display: block;
    opacity: 0.7;
	    padding: 4px;
			-webkit-transition: background-color 2s ease-out;
	-moz-transition: background-color 2s ease-out;
	-o-transition: background-color 2s ease-out;
	transition: background-color 2s ease-out;

}
.p_status {
    margin: 4px;
    width: 100%;
    float: left;
    color: #f5cc00;
}