<?php
// list pull
// FUNCTION TO GET_CMS
$page_id = 7439;

list($page_id, $cms_category, $cms_page_name, $cms_heading, $cms_brief, $cms_article, $cms_data, $cms_publish, $cms_page_title, $cms_keywords, $cms_page_description, $cms_included_form, $cms_privacy, $cms_refreshpage_on_add, $nextpage_url, $cms_introductory_instructions, $cms_pull_file, $cms_save_action, $cms_system_form_id, $cms_system_quiz_id) = get_cms('id', $page_id);

if ($show_reference_form == true) {
// echo admin only instructions
    echo "<h1>$cms_heading</h1>";
    echo $cms_introductory_instructions;
    echo '<b>' . $cms_brief . '</b><br/>';
    echo $cms_article;
}
