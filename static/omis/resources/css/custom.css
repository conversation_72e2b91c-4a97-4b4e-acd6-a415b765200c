/*
 * Style tweaks
 * --------------------------------------------------
 */
 
html {
  overflow-x: hidden; /* Prevent scroll on narrow devices */
}
body {font-family: 'Droid Arabic Kufi', Arial, sans-serif;
	background: #fff;
	-webkit-background-size: cover;
	-moz-background-size: cover;
	-o-background-size: cover;
	background-size: cover;
  padding-top: 0px;
}
a{ color:#333; }
h1, .h1 {
    font-size: 30px;
}
#footer {
  padding: 30px 0;
}
.footer-links{  background-color:#fff;  font-size:0.85em}
.footer-links a{}
ul.footer-links > li {
    display: inline;
	border-right:1px solid #f0f0f0;
	padding:0 10px;
}

#wrapper {
	background-color:#fff;
	}
	
#topbar {
    background: #fff;
    color: #333;
    padding: 5px;
    -moz-border-radius: 50%;
    -webkit-border-radius: 10px;
    border-radius: 10px 10px 0 0px;
    box-sizing: border-box;
    border-bottom: 0px;
    border-bottom-color: #fbc624;
    border-bottom-style: solid;
}
#topbar a{ font-size:12px; color:#000;}
.backbutton{padding-top: 15px; text-align: center;}
.backbutton a{color:#fff}
#topbar h2{ font-size:2em; font-weight:bold; padding:10px; margin:0; font-family: 'Open Sans', "Helvetica Neue", Helvetica, Arial, sans-serif;}
#topbar h3{ font-size:1.4px; padding:10px 10px 0 10px; color:#fff} 
#topbar span{ margin-right:10px} 

.holding_bar{
	background:#3a4249;
    border-bottom: 1px;
    border-bottom-color: #fbc624;
    border-bottom-style: solid;
	padding:10px;
	color:#fff;
}
.breadcrumbs{ text-align:left}

.custom_navigation{margin-top:10px;}
.btn a{color:#fff}

.profile_image{
	  padding:10px;
	  float:left;
}
.thumbnail{ margin-right:5px; width:150px; max-height:130px}
.pic_lrg{ margin-right:10px; margin-bottom:10px; width:250px; max-height:200px}
/*HEADER IMAGE*/
.splash{
	font-size:1em;
	padding:5px 0;
	border-bottom:1px dashed #f0f0f0;
}

.jumbotron{
	font-size:1em;
	padding:10px 0;
    overflow:auto;
	background-color:#fff;
	border-bottom:5px solid #f0f0f0;
}
.jumbotron h1{
	font-size:2.5em;
}
.jumbotron td,th{
	padding:0 10px;
	margin:0px;
}


/*RIGHTS HAND NAVIGATION*/
.list-group_home { line-height:40px; font-size:0.88em}
.list-group_home .completed{
	color:#ccc;
}
.list-group_home .completed:hover{
	color:#ccc;
}
.list-group-item {
    background-color: #eeeeee;
	border:1px solid #ccc;
}

.active {
    background-color: #fbc624;
    border-color: #fbc624;
}
.active a{color:#fff; font-weight:bold}
.circle-text {
    width:3em;
	float:left;
	clear:both;
	margin-right:10px;
}
.circle-text:after {
    content: "";
    display: block;
    width: 100%;
    height:0;
    padding-bottom: 100%;
    background: #fbc624; 
    -moz-border-radius: 50%; 
    -webkit-border-radius: 50%; 
    border-radius: 50%;
}
.circle-text span {
    float:left;
    width:100%;
    padding-top:20%;
    margin-top:-0.5em;
    text-align:center;
    color:white;
}

.box {
display: block;
background-color: rgb(255, 255, 255);
border: 1px solid rgb(214, 214, 214);
margin: 0 0 20px 0;
padding: 0 20px;
-webkit-border-radius: 3px;
-moz-border-radius: 3px;
border-radius: 3px;
}
.chat_stream{overflow: auto;height: 350px;margin: 0px -20px; padding: 20px;}
.widget-chat .chat-message{padding:10px 0;*zoom:1;overflow:auto}
.widget-chat .chat-message:before,.widget-chat .message:after{display:table;content:"";line-height:0}
.widget-chat .chat-message:after{clear:both}
.widget-chat .chat-message:first-child{padding-top:0}
.widget-chat .chat-message>img{display:block;float:left;height:40px;margin-bottom:-40px;width:40px;-webkit-border-radius:3px;-moz-border-radius:3px;border-radius:3px}
.widget-chat .chat-message>div{display:block;float:left;font-size:12px;margin-top:-3px;padding-left:55px;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
.widget-chat .chat-message>div>div{background:rgba(0,0,0,0.04);font-size:13px;padding:7px 12px 9px 12px;-webkit-border-radius:3px;-moz-border-radius:3px;border-radius:3px}
.widget-chat .chat-message>div>div:before{border-bottom:6px solid transparent;border-right:6px solid rgba(0,0,0,0.04);border-top:6px solid transparent;content:"";display:block;height:0;margin:0 0 -12px -18px;position:relative;width:0}
.widget-chat .chat-message>div>span{color:#bbb}
.widget-chat .chat-message.right>div>div:before{border-left:6px solid rgba(0,0,0,0.04);border-right:0;float:right;left:18px;margin-left:0}
.widget-chat .chat-message.right>img{float:right}
.widget-chat .chat-message.right>div{padding-left:0;padding-right:55px}
.widget-chat .widget-actions{border-bottom:1px solid rgba(0,0,0,0.08);margin-bottom:10px}
.widget-chat form{margin:0}
.widget-chat form>div{margin:-30px 100px 0 0}
.widget-chat form>div textarea,.widget-chat form>div input[type=text]{display:block;width:100%}
.widget-chat form .btn{display:inline-block;width:70px}
.widget-messages .chat-message{border-bottom:1px solid rgba(0,0,0,0.08);padding:5px 0;min-height:20px;*zoom:1}
.widget-messages .chat-message:before,.widget-messages .message:after{display:table;content:"";line-height:0}
.widget-messages .chat-message:after{clear:both}
.widget-messages .chat-message div,.widget-messages .chat-message a{display:block;height:20px;float:left;position:relative;z-index:2}
.widget-messages .chat-message .date{color:#888;float:right;width:50px}
.widget-messages .chat-message .action-checkbox{width:20px;line-height:0;font-size:0}
.widget-messages .chat-message .from{width:115px;padding-left:5px;overflow:hidden;color:#444}
.widget-messages .chat-message .title{display:block!important;height:auto;min-height:20px;white-space:normal;z-index:1;width:100%;margin-top:-20px;padding:0 55px 0 145px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
.widget-messages .chat-message.unread .title{font-weight:600}
.box.widget-messages .chat-message,.box .widget-messages .chat-message{margin:0 -20px;padding-left:20px;padding-right:20px}
.widget-status-panel{*zoom:1}
.widget-status-panel:before,.widget-status-panel:after{display:table;content:"";line-height:0}
.widget-status-panel:after{clear:both}
.widget-status-panel .status{border-right:1px solid rgba(0,0,0,0.12);text-align:center}
.widget-status-panel .status:last-child{border-right:0}
.widget-status-panel .status>a{color:#444;display:block;line-height:12px;height:12px;margin:10px 0 -22px 0;position:relative;width:30px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;opacity:.15;filter:alpha(opacity=15);-webkit-transition:all .2s;-moz-transition:all .2s;-o-transition:all .2s;transition:all .2s}.widget-status-panel .status>a:hover{text-decoration:none;opacity:.6;filter:alpha(opacity=60)}
.widget-status-panel .status .value,.widget-status-panel .status .caption{width:100%}
.widget-status-panel .status .value{color:#888;font-size:31px;height:100px;line-height:100px}
.widget-status-panel .status .caption{border-top:1px solid rgba(0,0,0,0.12);color:#666;font-size:12px;height:30px;line-height:30px;text-transform:uppercase;-webkit-box-shadow:rgba(255,255,255,0.75) 0 1px 0 inset;-moz-box-shadow:rgba(255,255,255,0.75) 0 1px 0 inset;box-shadow:rgba(255,255,255,0.75) 0 1px 0 inset}
.widget-status-panel.light{background:0;border:1px solid rgba(0,0,0,0.12);-webkit-box-shadow:rgba(255,255,255,0.75) 0 1px 0,rgba(255,255,255,0.75) 0 1px 0 inset;-moz-box-shadow:rgba(255,255,255,0.75) 0 1px 0,rgba(255,255,255,0.75) 0 1px 0 inset;box-shadow:rgba(255,255,255,0.75) 0 1px 0,rgba(255,255,255,0.75) 0 1px 0 inset}

@media(max-width:767px){.widget-status-panel .status{border-bottom:1px solid rgba(0,0,0,0.12);border-right:none!important;-webkit-box-shadow:rgba(255,255,255,0.75) 0 1px 0;-moz-box-shadow:rgba(255,255,255,0.75) 0 1px 0;box-shadow:rgba(255,255,255,0.75) 0 1px 0}
.widget-status-panel .status:last-child{border-bottom:0;-webkit-box-shadow:none;-moz-box-shadow:none;box-shadow:none}
}

.widget-stars-rating,.widget-stars-rating li{padding:0;margin:0;list-style:none;display:inline-block}
.widget-stars-rating a,.widget-stars-rating li a{display:block;color:#bbb;text-decoration:none;text-align:center;font-size:15px}.widget-stars-rating .active a{color:#3690e6}
.widget-stream .stream-empty{color:#ccc;font-size:16px;font-weight:600;font-style:italic;text-align:center}
.widget-stream .stream-event{border-bottom:1px solid rgba(0,0,0,0.08);min-height:34px;padding:15px 0;opacity:0;filter:alpha(opacity=0)}
.widget-stream .stream-event:last-child{border-bottom:0;padding-bottom:0}
.widget-stream .stream-icon{border-radius:3px;display:block;font-size:17px;line-height:34px;height:34px;margin-bottom:-34px;text-align:center;width:34px}
.widget-stream .stream-time{color:#888;display:block;float:right;font-size:12px;line-height:15px;height:15px}
.widget-stream .stream-message,.widget-stream .stream-caption{display:block;margin-left:46px;margin-right:50px}
.widget-stream .stream-caption{font-size:12px;font-weight:600;line-height:15px;text-transform:uppercase}
.box .widget-stream{margin:0 -20px}
.box .widget-stream .stream-event{padding-left:20px;padding-right:20px}.widget-maps img{max-width:none!important}
.box .widget-actions {
margin: 0 -20px 0 -20px;
padding: 15px 20px;
}
.widget-actions {
background: rgba(0, 0, 0, 0.03);
display: block;
padding: 15px 0;
text-align: right;
}

ul.footer-links > li {
    display: inline;
}


/*
 * Off Canvas
 * --------------------------------------------------
 */
@media screen and (max-width: 767px) {
  .row-offcanvas {
    position: relative;
    -webkit-transition: all 0.25s ease-out;
    -moz-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;
  }

  .row-offcanvas-right
  .sidebar-offcanvas {
    right: -50%; /* 6 columns */
  }

  .row-offcanvas-left
  .sidebar-offcanvas {
    left: -50%; /* 6 columns */
  }

  .row-offcanvas-right.active {
    right: 50%; /* 6 columns */
  }

  .row-offcanvas-left.active {
    left: 50%; /* 6 columns */
  }

  .sidebar-offcanvas {
    position: absolute;
    top: 0;
    width: 50%; /* 6 columns */
  }
}

.baseline{ border-bottom: 1px dashed #ccc; padding:5px 0}
.baseline form{ margin-bottom: 0px;}

/*new chat*/
/*	--------------------------------------------------
	:: Table Filter
	-------------------------------------------------- */
.panel {
	border: 1px solid #ddd;
	background-color: #fcfcfc;
}
.panel .btn-group {
	margin: 15px 0 30px;
}
.panel .btn-group .btn {
	transition: background-color .3s ease;
}
.table-filter {
	background-color: #fff;
	border-bottom: 1px solid #eee;
}
.table-filter tbody tr:hover {
	cursor: pointer;
	background-color: #eee;
}
.table-filter tbody tr td {
	padding: 10px;
	vertical-align: middle;
	border-top-color: #eee;
}
.table-filter tbody tr.selected td {
	background-color: #eee;
}
.table-filter tr td:first-child {
	width: 38px;
}
.table-filter tr td:nth-child(2) {
	width: 35px;
}
.ckbox {
	position: relative;
}
.ckbox input[type="checkbox"] {
	opacity: 0;
}
.ckbox label {
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
.ckbox label:before {
	content: '';
	top: 1px;
	left: 0;
	width: 18px;
	height: 18px;
	display: block;
	position: absolute;
	border-radius: 2px;
	border: 1px solid #bbb;
	background-color: #fff;
}
.ckbox input[type="checkbox"]:checked + label:before {
	border-color: #2BBCDE;
	background-color: #2BBCDE;
}
.ckbox input[type="checkbox"]:checked + label:after {
	top: 3px;
	left: 3.5px;
	content: '\e013';
	color: #fff;
	font-size: 11px;
	font-family: 'Glyphicons Halflings';
	position: absolute;
}
.table-filter .star {
	color: #ccc;
	text-align: center;
	display: block;
}
.table-filter .star.star-checked {
	color: #F0AD4E;
}
.table-filter .star:hover {
	color: #ccc;
}
.table-filter .star.star-checked:hover {
	color: #F0AD4E;
}
.table-filter .media-photo {
	width: 35px;
}
.table-filter .media-body {
    display: block;
    /* Had to use this style to force the div to expand (wasn't necessary with my bootstrap version 3.3.6) */
}
.table-filter .media-meta {
	font-size: 11px;
	color: #999;
}
.table-filter .media .title {
	color: #2BBCDE;
	font-size: 14px;
	font-weight: bold;
	line-height: normal;
	margin: 0;
}
.table-filter .media .title span {
	font-size: .8em;
	margin-right: 20px;
}
.table-filter .media .title span.pagado {
	color: #5cb85c;
}
.table-filter .media .title span.pendiente {
	color: #f0ad4e;
}
.table-filter .media .title span.cancelado {
	color: #d9534f;
}
.table-filter .media .summary {
	font-size: 14px;
}
.alert-default {
    color: #848484;
    background-color: #F1F1F1;
    border-color: #DADCD8;
}
.alert-default h3{
	colour:#fbc624;
}

.btn-custom {
    color: #fff;
    background-color: #fbc624;
    border-color: #fbc624;
}

.progress-bar-custom {
    background-color: #fbc624;
}

li.list-group-item.completed.tick_class {
	color: black;
}