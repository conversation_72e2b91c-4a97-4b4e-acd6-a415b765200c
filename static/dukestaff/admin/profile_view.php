<?php
$telephone_number = pull_field('dir_registration1', 'db774', "WHERE dir_registration1.rel_id='{$core_students_id}'");
?>
<style type="text/css">
  .profile_div{
    display: flex;
    flex-direction: row;
    gap: 3px;
    justify-content: space-between;
  }
  .profile_div .title{
    width: auto !important;
  }
</style>
      	<div class="col-sm-12 col-md-4 col-lg-4">
			<img src="<?=$avatar?>" class="img-circle" alt="no image" width="100" border="0"  id="tour_profile_photo" onerror="this.src='/engine/images/male_icon.jpg'"/>
        </div>
        <div class="col-sm-12 col-md-8 col-lg-4 name">
          <h2 style="color:#F60"><?php echo $core_students_has_applied ; ?></h2>
          <div class="info_detail">
            <?php
              $last_login_date = last_logged_in($core_students_rec_id,"D jS \of M Y h:i:s A");  
              if($last_login_date){
            ?>
            <b>Last Login:</b>
            <?php echo $last_login_date; ?>
            <?php }else{ echo "Hasn't logged in yet"; } ?>
            </div>
        </div>

        
        <div class="col-sm-12 col-md-12 col-lg-12">
        <hr>	  
          <div class="profile_div">
            <div class="title">Application Year:</div>
            <div class="info_detail"><?php echo $core_students_cohort; ?></div>    
          </div>  
          <div class="profile_div">
            <div class="title">Role:</div>
            <div class="info_detail"><?php echo $core_students_course_of_study; ?></div> 
          </div>
          <div class="profile_div">
            <div class="title">Telephone:</div>
            <div class="info_detail"><?php echo $telephone_number; ?></div>   
          </div>
          <div class="profile_div">
            <div class="title">Date of Birth:</div>
            <div class="info_detail"><?php echo format_date("d-m-Y",$core_students_date_of_birth); ?></div>
          </div>
          <?php if($_SESSION['ulevel']!=='8'){ ?>
           <div class="profile_div">    
            <div class="title">Email:</div>
            <div class="info_detail"><?=$core_students_email_address?></div>
           </div> 
          <div class="profile_div">
            <div class="title">Source:</div>
            <div class="info_detail"><?=$core_students_source_of_applicant?></div>
          </div>
            
             <?php } ?>
                  <?php
	         if ($short_course_level !=='yes') {?>
           <!--ACTION BUTTONS-->
           <div class="clearfix"></div><br/>
           <div class="btn-group btn-group-sml">     
            <a href="<?=engine_url?>/includes/inc_view_data.php?vw=<?=$_GET['vw'];?>&ref=<?=$_GET['ref'];?>&width=950&height=600&jqmRefresh=false" class="thickbox btn btn-warning btn-sm" title="View Application"><i class="fa fa-file-text-o"></i> View Application Form Data</a>
<!--            <a href="/admin/dynamic/export_system_student_xml/--><?php //echo $_GET['ref'] ?><!--" target="_blank" class="btn btn-warning btn-sm" title="Export to XML" style="margin-left: 5px;"><i class="fa fa-download"></i> Export to XML</a>-->
            </div>
           <!--ACTION BUTTONS END-->
            <?php 
			}
			?>