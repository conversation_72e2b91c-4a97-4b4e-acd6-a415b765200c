/* CUSTOMISE THE MAIN 3/4 SECTION */
.portal{background-color:#fff;}
.portal li{}

/* enrolment */
.enrolment li{ background-color:#CCC; padding:10px;}
.enrolment li a{ font-weight:bold}

/* CUSTOMISE THE MAIN 1/4 SECTION/NAV */
.custom_navigation{}
h1{font-size:1.6em; margin-bottom:10px}
h2{border-bottom:0px solid #ccc; max-width:85%; margin-bottom:5px; padding-bottom:5px;}
h3{ font-weight:normal; color:#666}

.login_options li{ border:0; float:left; width:450px; right-padding:10px; padding:10px; background-color:rgb(0,100,130); list-style:none; margin:10px 30px 0 10px; font-size:16px; font-weight:bold; color:#fff}
.login_options a:hover{ text-decoration:underline}
.login_options a{color:#fff}
.link_color{ color:#fff}
.support{ padding:5px; margin:5px 0; color:#fff; font-size:12px; text-shadow:0px 0px 0px #fff;}
.support h3{ color:#fff;}
.alert{ background-color:#F66; padding:5px}
.alert_positive{ background-color:#b5e5fc; padding:5px}

a{ text-decoration:none;  border-bottom:0px solid #333; }
. float_right{float:right}
. float_left{float:left;}
.clear{clear:both}

.orange {
	color: #e81a1a;
}

.small_text {
	font-size: 10px;
}
.form_txt{ color:#333; padding:5px; background-color:#f0f0f0; border:1px solid #ccc}
.form_txt:hover{ background-color:#ccc;}

/*
This div is for the overlay on the
video
*/

#status {
  position: relative;
  padding: 0;
  /*margin: -300px 10px 0 330px;*/
  /*width: 210px;*/
  /*height: 130px;*/
  margin:-353px 0px 0 0px;
  width:610px;
  height: 343px;
  background: transparent;
}

/*** ends ***/
.main_nav{float:right; width:200px; background:#f0f0f0; padding:10px}
.main_nav li a{ color:#000;}
.main_nav li a:hover{ color:#fff; text-decoration:none}
.main_nav li{ font-size:12px; padding:5px 5px; border-bottom:1px solid #ccc;}
.main_nav li:hover{ font-size:12px; padding:5px 5px; border-bottom:1px solid #ccc; color:#fff; background-color:#749ba0;  text-decoration:none}

/* This imageless css button was generated by CSSButtonGenerator.com */
.main_nav {
	background:-webkit-gradient( linear, left top, left bottom, color-stop(0.05, #749ba0), color-stop(1, #749ba0) );
	background:-moz-linear-gradient( center top, #749ba0 5%, #749ba0 100% );
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#749ba0', endColorstr='#749ba0');
	background-color:#749ba0;
	display:inline-block;
	color:#ffffff;
	font-family:arial;
	font-size:15px;
	font-weight:bold;
	padding:10px 15px;
	text-decoration:none;
}.main_nav:hover {
	background:-webkit-gradient( linear, left top, left bottom, color-stop(0.05, #749ba0), color-stop(1, #749ba0) );
	background:-moz-linear-gradient( center top, #749ba0 5%, #749ba0 100% );
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#749ba0', endColorstr='#749ba0');
	background-color:#fd7d42;
}.main_nav:active {
	position:relative;
	top:1px;
	background-color:#fd7d42;
}


.main_nav li a:hover{ color:#fff; text-decoration:none}
.nav_selected{ background:#e81a1a; color:#000; 	text-shadow:0px 0px 0px #fff;}
/* This imageless css button was generated by CSSButtonGenerator.com */


.start_button {
	-moz-box-shadow:inset 0px 1px 0px 0px #fce2c1;
	-webkit-box-shadow:inset 0px 1px 0px 0px #fce2c1;
	box-shadow:inset 0px 1px 0px 0px #fce2c1;
	background:-webkit-gradient( linear, left top, left bottom, color-stop(0.05, #ffc477), color-stop(1, #fb9e25) );
	background:-moz-linear-gradient( center top, #ffc477 5%, #fb9e25 100% );
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffc477', endColorstr='#fb9e25');
	background-color:#ffc477;
	-moz-border-radius:6px;
	-webkit-border-radius:6px;
	border-radius:6px;
	border:1px solid #eeb44f;
	display:inline-block;
	color:#ffffff;
	font-family:arial;
	font-size:19px;
	font-weight:bold;
	padding:11px 34px;
	text-decoration:none;
	text-shadow:1px 1px 0px #cc9f52;
}.start_button:hover {
	background:-webkit-gradient( linear, left top, left bottom, color-stop(0.05, #fb9e25), color-stop(1, #ffc477) );
	background:-moz-linear-gradient( center top, #fb9e25 5%, #ffc477 100% );
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#fb9e25', endColorstr='#ffc477');
	background-color:#fb9e25;
 text-decoration:underline
}.start_button:active {
	position:relative;
	top:1px;
}
.start_button a{ color:#fff;}

/* ------------- TERMS AND CONDITIONS CSS -------------------------------------*/
#terms-and-conditions {
	width:100%;
	height:400px;
	overflow:auto;
	border:4px solid #f0f0f0;
	padding:5px;
	background-color:#f4f4f4;

}
#terms-and-conditions p, #terms-and-conditions ul li {
	font-weight: bold;
}