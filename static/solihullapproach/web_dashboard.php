<?php
//moved finance stuff to main functions file form_settings
// cleanout this session variable
//$_SESSION['prog_course_selected']='';//reset message
dev_debug($_SESSION, "print_r");
dev_debug($pdo_course_data, "print_r");
?>

<?php
if (isset($_POST["submit_button"]) & isset($_POST["clid"])) {
    // get the course data
    $sql_course_level_data = "
	SELECT *
	FROM core_course_level
	WHERE id = '$_POST[clid]'
	AND usergroup = '$_SESSION[usergroup]'
	";

    // execute and get results
    dev_debug($sql_course_level_data);

    $dbh = get_dbh();
    $sql = $dbh->prepare($sql_course_level_data);
    $sql->execute();
    $pdo_course_level_data_rows = $sql->fetchAll(PDO::FETCH_OBJ);
    $pdo_course_level_data = $pdo_course_level_data_rows[0];

    // get the url of where they are coming from
    $from_url = $_SERVER["HTTP_REFERER"];

    //set session values to populate the hidden fields on the form
    $_SESSION['prog_course_selected'] = "<h2>Apply now for:<br/><b>" . $pdo_course_level_data->db343 . "</b></h2>
	<p>Applying is quick and easy, simply complete the form below and start preparing your application</p>"; // Instructions telling them what they have selected
    $_SESSION['prog_level'] = $pdo_course_level_data->id; //- course level
    echo "<script>
$(window).load(function(){
    $('#registration').modal('show');
});
</script>";
}
?>

<style type="text/css">
    #small_welcome_box {
        background: #fbfbfb;
        border: 1px solid #ddd;
        border-bottom: 7px solid #e6e6e6;
        padding: 15px;
        margin-bottom: 30px;
    }

    #small_welcome_box .pic {
        display: block;
        background: #e1e1e1;
        -webkit-border-radius: 100px;
        border-radius: 100px;
        height: 100px;
        max-width: 100px;
        margin: 0 auto;
        -webkit-background-size: cover;
        -o-background-size: cover;
        background-size: cover;
        background-position: center;
    }

    #small_welcome_box .info {
        background: transparent;
        margin: 0;
        padding: 26px 0;
        border: none;
    }

    #small_welcome_box .edit_pic_link {
        background: #fff;
        border: solid 1px #e1e1e1;
    }

    #small_welcome_box h3 {
        font-size: 24px;
        margin: 0;
        margin-top: 10px;
    }


    .course_list {
        background: #fbfbfb;
        border: 1px solid #ddd;
        border-bottom: 1px solid #e6e6e6;
        margin-bottom: 0px;
    }

    .course_list .chld_view_form2 {
        text-align: right;
        padding-right: 10px;
    }

    .course_list .course_title {
        padding-top: 10px;
    }

    .course_list .corse_info {
        padding: 30px;
        background: #FFF;
        border-top: 1px solid #e1e1e1;
    }
</style>

<div id="small_welcome_box">
    <div class="row">
        <div class="col-sm-2 text-center">
            <a href="#" data-toggle="modal" data-target="#photo_update" class="pic"
               style="background-image: url('<?= $avatar ?>');"></a>
        </div>
        <div class="col-sm-10">
            <div class="info">
                <div class="row">
                    <div class="col-sm-7">
                        <h3>Welcome back, <?= $_SESSION['name'] ?></h3>
                    </div>
                    <div class="col-sm-5 text-right">
                        <a class="btn btn-light edit_pic_link" href="#" data-toggle="modal" data-target="#photo_update"><i
                                    class="fa fa-cog" aria-hidden="true"></i> Update your user details</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-xs-12 col-md-7 dash_title"><h2>Manage All Training</h2></div>
    <?php $_SESSION['scheduled_course_booking'] = ''; ?>
    <div class="col-xs-12 col-md-5 btns">
        <!--<form name="trainings_form" method="post" action="/application/checklist">
            <input type="hidden" name="clid" value="252">
            <button class="btn btn-block btn-primary" name = "submit_button" type="submit">Register For a Training</button>
        </form>-->
    </div>
</div>

<div class="row btm_base">
    <div class="col-xs-12 col-md-12">
        <div class="splash">
            <?php
            include("resources/views/short.php");
            ?>
        </div><!--splash-->
    </div>
</div><!--row-->

<!--<div class="row">
    <div class="col-xs-12 col-md-6 dash_title"><h2>Manage Accreditations / Re-accreditations</h2></div>
    <div class="col-xs-12 col-md-3 btns">
        <form name="trainings_form" method="post" action="/application/checklist">
            <input type="hidden" name="clid" value="242">
            <button class="btn btn-block btn-primary" name = "submit_button" type="submit">Register For Initial Accreduitation</button>
        </form>
    </div>
    <div class="col-xs-12 col-md-3 btns">
        <form name="trainings_form" method="post" action="/application/checklist">
            <input type="hidden" name="clid" value="240">
            <button class="btn btn-block btn-primary" name = "submit_button" type="submit">Register For Re Accreduitation</button>
        </form>
    </div>
</div>-->

<div class="row btm_base">
    <div class="col-xs-12 col-md-12">
        <div class="splash">
            <?php
            include("resources/views/standard.php");
            ?>
        </div><!--splash-->
    </div>
</div><!--row-->

<div class="clearfix"></div>

<div class="row">
    <div class="col-xs-12 col-lg-12">
        <h2><?= translate("Communication Inbox", $_SESSION['lang']) ?></h2>
        <p><?= translate("Send and receive communication from our administration team below. To respond to a message or ask a question, simply select the application you want to communicate in relation to, and type your message.", $_SESSION['lang']) ?></p>

        <!--CHAT RELATED-->
        <script src="<?php echo $front_web_url_file_loc; ?>/js/dashboard.js" type="text/javascript"
                charset="utf-8"></script>

        <script type="text/javascript">
            $(document).ready(function () {

                $('.star').on('click', function () {
                    $(this).toggleClass('star-checked');
                });

                $('.ckbox label').on('click', function () {
                    $(this).parents('tr').toggleClass('selected');
                });

                $('#comm_wrap .btn-filter2').on('click', function () {
                    var $target = $(this).data('target');
                    if ($target != 'all') {
                        $('#comm_wrap .table tbody tr').css('display', 'none');
                        $('#comm_wrap .table tbody tr[data-status="' + $target + '"]').fadeIn('slow');
                    } else {
                        $('#comm_wrap .table tbody tr').css('display', 'none').fadeIn('slow');
                    }
                });

            });
        </script>

        <?php //if($_SESSION['uid']==1787){ ?>
        <!--new chat-->
        <section class="content">
            <div class="col-md-12">
                <div class="panel panel-default" id="comm_wrap">
                    <div class="panel-body">


                        <span class="pull-right"><?= translate("(#) means you have new messages", $_SESSION['lang']) ?></span>
                        <?= translate("View messages relating to", $_SESSION['lang']) ?><br/>
                        <div class="btn-group">
                            <button type="button" class="btn btn-default btn-filter2" id="all"
                                    data-target="all"><?= translate("All", $_SESSION['lang']) ?>
                            </button>
                            <?php
                            // loop through kids
                            foreach ($list_applications as $application_form) {
                                $new_msgs = 0;
                                foreach ($messages as $message) {
                                    if ($message['rel_id'] == $application_form['applicant_id'] && $message['rec_id'] != $student_uid && $message['mark_as_read'] == 'no') {
                                        $new_msgs++;
                                    }
                                }
                                ?>
                                <button type="button" class="btn btn-primary btn-filter2"
                                        id="<?= $application_form["applicant_id"] ?>"
                                        data-target="<?= $application_form["applicant_id"] ?>"><?= $application_form["course"] ?>
                                    <?php if ($new_msgs > 0) { ?>
                                        (<?= $new_msgs ?>)
                                    <?php } ?>
                                </button>
                                <?php
                                // loop through kids end
                            }
                            ?>

                        </div>

                        <div id="coms" class="box widget-chat">
                            <div class="widget-actions">
                                <form class="form-inline"
                                      action="<?= website_url ?>/static/inc/inc_dir_messages_process.php" method="POST">
                                    <button class="btn btn-primary">
                                        <?= translate("Send", $_SESSION['lang']) ?>
                                    </button>
                                    <div>
                                                    <textarea name="new_message" rows="1" id="textarea-chat-example"
                                                              style="overflow: hidden; word-wrap: break-word; resize: vertical; height: 100px;"></textarea>
                                        <!--<select name="category">
                                            <option value="">Question</option>
                                        </select>-->
                                    </div>
                                    <input type="hidden" name="child_id" id="hdn_child_id" value="">
                                </form>
                            </div>
                        </div>

                        <div class="clearfix"></div>

                        <div class="table-container">
                            <table class="table table-filter">
                                <thead>
                                <tr>
                                    <th style="width:10%"><?= translate("Marked as read", $_SESSION['Send']) ?></th>
                                    <th><?= translate("Message", $_SESSION['Send']) ?></th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php
                                $count_messages = 0;
                                foreach ($messages as $msg) {

                                    $count_messages++;
                                    $content = text_to_html($msg["msginfo"]);
                                    $date = format_date("d M y", $msg["date"]);
                                    $child_name = pull_field("core_students", "db39", "WHERE id='$msg[rel_id]'");

                                    if ($msg['type'] == "form_email_log") {
                                        $data_status = "emails";
                                        $data_type = "Email sent by ";
                                        //                                            echo 'EMAILS';
                                    } else if ($msg['type'] == "core_notes") {
                                        $data_status = "messages";
                                        $data_type = "Private message sent by ";
                                        //                                            echo 'MESSAGES';
                                    }

                                    if ($msg['rec_id'] === $student_uid) {
                                        $sender = 'You';//
                                        $checked = 'checked="checked"';
                                        //echo "<br> marked as read: ".$msg['mark_as_read']."<br>";
                                        if ($msg['mark_as_read'] == 'no') {
                                            $checked = '';
                                        }
                                        echo "<tr data-status=\"" . $msg['rel_id'] . "\">
                                                    <td>
                                                        <div class=\"\">
                                                            <input type=\"checkbox\" id=\"$msg[id]\" $checked disabled=\"disabled\" title=\"if ticked, this message has been read\">
                                                            <label for=\"checkbox1\"></label>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class=\"media\">
                                                            <a href=\"#\" class=\"pull-left\">
                                                                <img src=\"" . $avatar . "\" class=\"media-photo img-circle\">
                                                            </a>
                                                            <div class=\"media-body\">
                                                                
                                                                <div class=\"summary\">
																<span class=\"media-meta pull-right\">$date</span>
																	<h4 class=\"title\">
																		 $data_type$sender
																	</h4>
																	$content
																</div>
																
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>";
                                    } else {
                                        $sender = pull_field("form_users", "db106", "WHERE id='$msg[rec_id]'");
                                        $checked = 'checked="checked"';
                                        if ($msg['mark_as_read'] == 'no') {
                                            $checked = '';
                                        }
                                        echo "<tr data-status=\"" . $msg['rel_id'] . "\">
                                                    <td>
                                                        <div class=\"\">
														
                                                            <input type=\"checkbox\" id=\"$msg[id]\" $checked title=\"mark as read\">
                                                            <label for=\"checkbox1\"></label>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class=\"media\">
                                                            <a href=\"#\" class=\"pull-right\">
                                                                <img src=\"" . get_avatar($msg['rec_id']) . "\" class=\"media-photo img-circle\">
                                                            </a>
                                                            <div class=\"media-body\">
															
                                                                <div class=\"summary\">
																<span class=\"media-meta pull-right\">$date</span>
																	<h4 class=\"title\">
																		 $data_type$sender
																	</h4>
																	$content
																</div>
																
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>";


                                    }
                                }
                                if ($count_messages == 0) {
                                    echo "<tr><td></td><td>" . translate("Sorry, no messages found", $_SESSION['Send']) . "</td></tr>";
                                }
                                ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!--new chat-->
        <?php //} // end session check ?>
        <div class="clearfix"></div>

    </div><!--/span-->

</div> <!-- .row -->
<div class="row">
    <?php $class_resources = "col-xs-12 col-lg-12";
    include('static/inc/inc_shared_resources.php'); ?>
</div>


<!--
<div class="row">
    <div class="col-xs-12">
    <h3>Please read this important information before you begin your application</h3>
        <div style="width:650px; height:400px; overflow:auto; border:4px solid #f0f0f0; padding:5px; background-color:#f4f4f4; margin-bottom:10px">
          <?php
// FUNCTION TO GET_CMS
//list($page_id,$cms_category,$cms_page_name,$cms_heading,$cms_brief,$cms_article)=get_cms('page_name',"Tier4_Information");
//echo $cms_article;//pull_field("form_cms","db650","WHERE db647='Tier4_Information'");
?>
        </div>
    </div>
</div>
-->
<script src="https://checkout.stripe.com/checkout.js"></script>
<script>
    $(document).ready(function () {
        var handler = StripeCheckout.configure({
            key: '<?php echo $stripe['publishable_key']; ?>',
            locale: 'auto',
            closed: function () {
                // if user clicks close button
                $('#pay-now-form').find('button').prop('disabled', false);
            },
            token: function (token) {
                // Use the token to create the charge with a server-side script.
                // You can access the token ID with `token.id`
                var $form = $('#pay-now-form');

                if (token.error) {
                    // Show the errors on the form
                    $form.find('.payment-errors').text(token.error.message);

                    $form.find('button').prop('disabled', false);
                } else {
                    // response contains id and card, which contains additional card details
                    var token_id = token.id;
                    // Insert the token into the form so it gets submitted to the server
                    $form.append($('<input type="hidden" name="stripeToken" />').val(token.id));
                    $form.append($('<input type="hidden" name="stripeEmail" />').val(token.email));
                    // and submit

                    $form.submit();
                }
            }
        });

        $('.pay_now_button').on('click', function (e) {
            var my_form = $(this).closest("form");

            // Open Checkout with further options
            // Disable the submit button to prevent repeated clicks
            //form.find('button').prop('disabled', true);
            var reference = $(this).data('reference');
            var link = "https://stripe.com/gb/features#seamless-security";
//
            //var total_amount = $('#total_amount').val();
            var total_amount = my_form.find('.total_amount_class').val();
            handler.open({
                name: 'Stripe.com',
                description: 'Reference ' + reference,
                amount: total_amount,
                currency: 'GBP',
                email: '<?=$_SESSION['user']?>'
            });
            e.preventDefault();
//

        });


        // Close Checkout on page navigation
        $(window).on('popstate', function () {
            handler.close();
        });
    });
</script>
