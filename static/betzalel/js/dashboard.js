/**
 * Created by <PERSON> on 25/05/2016.
 */
$(document).ready(function(){
    var buttons = $('.btn-group :button');
    var coms = $('#coms');
    var child_id = $('#hdn_child_id');
    coms.hide();
    $('#hdn_child_id').val("all");
    buttons.each(function () {
        $(this).on('click', function(){
            if($(this).attr('id') == "all"){
                coms.hide();
                child_id.val("all");
            }
            else{
                coms.show();
                child_id.val($(this).attr('id'));
            }
        });
    });
    var checkboxes = $('input:checkbox');
    checkboxes.each(function () {
        console.log($(this).attr('id') + " " + $(this).attr('checked'));
        if($(this).prop('checked') == true){
            $(this).attr('disabled', 'disabled');
        }
        $(this).on('click', function(){
            $(this).prop('checked', true);
            $(this).prop('disabled', 'disabled');
            var id = $(this).attr('id');
            $.ajax({
                method: "POST",
                url: '../../../engine/ajax/mark_note_read.php',
                data: {id : id},
                dataType: 'json',
                success: function(data){
                    console.log("marked as read? " + data['status']);
                }
            });
        });

    });
});