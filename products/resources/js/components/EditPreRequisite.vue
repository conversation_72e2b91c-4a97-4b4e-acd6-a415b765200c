<template>
  <div class="modal fade" id="PreReqModal" tabindex="-1" aria-labelledby="PreReqModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content" style="padding: 10px;">
      <div class="modal-header">
        <h5 class="modal-title" id="PreReqModalLabel"><span id="pre_req_modal_function">Edit</span> Prerequisite</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="col-sm-12 mb-3">
        <label title="required"> Requirement <span class="text-danger">*</span></label>
    
        <select class="form-select" v-model="pre_req_temp.prereq_uploaded" >
          <option disabled>Choose...</option>
          <option v-if="order_prerequisites.length>0" v-for="(item,i) in order_prerequisites" :key="i" :value="item.product_ids+'-'+item.prereq_id" :hidden="item.preq_count==0">{{item.prereq_title}} for product "{{item.products}}" ({{item.preq_count}})</option>
        </select>
                
      </div>
      <div class="col-sm-12 mb-3">
        <label>Training date (required) <span class="text-danger">*</span></label>
        <vuejs-datepicker  v-model="pre_req_temp.short_course_date" name="short_course_date" :format="customFormatter"></vuejs-datepicker>
      </div>
      <div class="col-sm-12 mb-3">
        <label title="required">Training Location <span class="text-danger">*</span></label>
        <input type="text" class="form-control" v-model="pre_req_temp.short_course_location" name="short_course_location">
      </div>
      <div class="col-sm-12 mb-3">
        <label>Proof of training</label>
                <file-upload ref='upload'></file-upload>

      </div>
      <div class="modal-footer">
        <input type="" name="" id="delete_cart_basket_id" hidden>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="closePreReqModal">Close</button>
        <button v-if="''!=pre_req_temp.short_course_date&&''!=pre_req_temp.short_course_location" class="btn btn-warning btn-lg" type="button" v-on:click="submit_prerequisite">
          <span class="px-2">Submit</span>
        </button>
        <button v-else class="btn btn-default btn-lg" type="button" disabled>
          <span class="px-2">Submit</span>
        </button>
      </div>
    </div>
  </div>
</div>

  
</template>

<script>
import axios from "axios";
import {getters,mutations,actions,filters} from "../../../src/store";
import vSelect from "vue-select";
import FileUpload from "./FileUpload";
import * as moment from 'moment';
export default {
  name: "EditPreRequisite",
  data: function (){
    return {
        //short_course_date:'',short_course_location:'',product_ids:''

    }
  },
  components: {
      'v-select': vSelect,
      'file-upload': FileUpload
    },
  methods: {
    ...actions,
    ...mutations,
    dialogToggle: function(dialogState) {
      console.log(dialogState)
    },
    submit_prerequisite: function(){
        let vm = this;
        let new_upload=''
        let url = '/admin/sis_products/submit_prerequisite';
        const params = new URLSearchParams();
        params.append( 'short_course_date', moment(vm.pre_req_temp.short_course_date).format('yyyy-MM-DD') );
        params.append( 'short_course_location', vm.pre_req_temp.short_course_location );
        if (vm.short_course_certificate !='') {
          new_upload =vm.short_course_certificate;
        }else{
          new_upload=vm.pre_req_temp.short_course_certificate;
        }
        params.append( 'short_course_certificate', new_upload);
        if (''!=vm.pre_req_temp.pre_req_id) {
        params.append( 'pre_req_id', vm.pre_req_temp.pre_req_id );
        }

        if (''!=vm.pre_req_temp.order_id) {
          params.append( 'order_id', vm.pre_req_temp.order_id );
          vm.order_prerequisites.forEach(function(currentValue){
          if (currentValue.product_ids+'-'+currentValue.prereq_id==vm.pre_req_temp.prereq_uploaded) {
            params.append('order_item_id',currentValue.order_item_id);
          }
        })
        }

        if (typeof vm.pre_req_temp.order_status!='undefined') {
          params.append( 'order_status', vm.pre_req_temp.order_status );

        }

        var fields = vm.pre_req_temp.prereq_uploaded.split('-');

        params.append( 'product_ids', fields[0] );
        params.append( 'prereq_id', fields[1] );

        

        //return


        axios.post(url,params)
          .then(function (r) {
            const flag = r.data.success ? 'success' : 'warning';
            //vm.short_course_certificate="";
            vm.get_cart_prerequisites();
            vm.get_cart();
            vm.get_baskets_details();
            vm.push_message(r.data.message, flag);
            vm.setPreReqTemp({});
            //const id = this.$route.params.id;
            //if(id){
              vm.get_order_details(vm.order.id);
            //}
            vm.$refs.upload.reset()
            let btn = document.getElementById('closePreReqModal');
             if(btn) btn.click();
          })
          .catch(function (error) {
            vm.push_message( error, 'error' );
          });
      },
  },
  computed:{
    ...getters,
     customFormatter() {
        let vm = this;
          return moment(vm.pre_req_temp.short_course_date).format('yyyy-MM-DD');
        },
  },
  mounted() {
    this.get_countries();
    //this.get_basket_data(2)
    
  }
  
}
</script>

<style scoped>

</style>
