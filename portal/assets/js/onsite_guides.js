onsite_data = {
    loading_guides: 1,
    onsite_guides: [],
    current_user: $("#guides_current_user").val(),
    current_userlevel: $("#guides_current_userlevel").val(),
    student_unique_id: ''
}

guides = new Vue({
    el: '#guides_wrap',
    data: {},

    components: {
        guidesHtml: {
            template: "#guidesHtml",
            data: function(){
                return onsite_data
            },
            mounted: function () {

                //Trigger Single chat
                const student_id = $("#guides_wrap").attr("student_id");
                if(student_id){
                    this.showGuides(student_id);
                }

            },
            methods: {
                siteURL:function(){
                    domain = document.URL;
                    let protocol;
                    if (domain.indexOf("https") != -1) {
                        protocol = "https";
                    } else {
                        protocol = "http";
                    }
                    let siteURL = protocol + "://" + document.domain;
                    return siteURL;
                },
                showGuides: function (student_unique_id) {
                    this.student_unique_id = student_unique_id;
                    if(!this.student_unique_id){
                        alert("Student id is required for the student news and updates");
                        return false;
                    }

                    //get the news and updates
                    this.loading_guides = 1;
                    const guides_url = this.siteURL() + '/portal/api/news_and_updates/guides';
                    axios.get(guides_url)
                        .then(response => {
                            guides.$refs.html.loading_guides = 2;
                            guides.$refs.html.onsite_guides = response.data.data;
                            $.each( guides.$refs.html.onsite_guides, function( key, news ) {
                                if (!news.db1695) {
                                    news.db1695 ="Unknown Title";
                                }
                                const update = news.db1696;
                                news.db1696 = update.substring(0,311)+ '...';
                            });
                        })
                        .catch(error => {
                            console.log(error)
                            this.errored = true
                        });
                }
            },
        },
    },
})

