<?php

/**
 *
 */
class scheduled_courses extends Controller
{



    public function index()
    {
        global $user_info, $school_info, $db;
        $courses = new ShortcoursesModel;

        /** ===================================
         * Get the results
         * ====================================  */
     
        //Course Levels
        $course_args = array('paginate'=>true,'school_id'=>$school_info['id']);
    
        if ($_GET['search']) {
            $course_levels_args['search'] = $_GET['search'];
        }

        //Table to use: sis_course_tutors holds the tutors
        //sis_course_schedule: link to the tutors 1 (db16888); #AND (db16888 ='46' OR db17213 ='46')
        // if ($user_info['partner']['id']) {
        //     if ($this->settings['applicants_partner_link'] == "programmes") {
        //         $course_levels_args['partner_id'] = $user_info['partner']['id'];
        //         $course_levels_args['show_based_on_partner_programmes'] = $user_info['partner']['id'];
        //     }
        // }

        $results = $courses->get($course_args);

        /** ===================================
         * Render View
         * ====================================  */
        $data = array(
            'meta_title' => 'Scheduled Courses - ' . $school_info['title'] . '',
            'view_file' => 'scheduled_courses/index',
            'title' => 'Scheduled Courses',
            'active_tab' => 'all',
            'results' => $results
        );
        $this->view($this->layout, $data);
    }


    public function info($username_id="")
    {
        global $user_info, $school_info, $db;
        $courses = new ShortcoursesModel;


        $course_args = array('username_id'=>$username_id);
        $entry = $courses->get($course_args);
        
        //This should come form DB
        $bookings = array(
          array("first_name"=>"John", "last_name"=>"Doe","email"=>"<EMAIL>"),
          array("first_name"=>"Stacy", "last_name"=>"Doe","email"=>"<EMAIL>"),
          array("first_name"=>"Anna", "last_name"=>"Doe","email"=>"<EMAIL>"),
        );

        /** ===================================
         * Render View
         * ====================================  */
        $data = array(
            'meta_title' => 'Scheduled Courses - ' . $school_info['title'] . '',
            'view_file' => 'scheduled_courses/details',
            'entry' => $entry,
            'bookings' => $bookings,
        );
        $this->view($this->layout, $data);
    }


    public function download_learn_support_form($username_id="")
    {
        global $user_info, $school_info, $db;
        $courses = new ShortcoursesModel;

        //Course Levels
        $course_args = array('username_id'=>$username_id);
        $entry = $courses->get($course_args);
        
        echo "Download learn_support_form script comes here";
        exit();
    }


    public function downlodad_course_attendance_sheet($username_id="")
    {
        global $user_info, $school_info, $db;
        $courses = new ShortcoursesModel;

        //Course Levels
        $course_args = array('username_id'=>$username_id);
        $entry = $courses->get($course_args);
        
        echo "Download _course_attendance_sheet script comes here";
        exit();
    }

	/**
	 * @throws \Doctrine\DBAL\Exception
	 * @throws \Doctrine\DBAL\Driver\Exception
	 */
	public function all_courses(){
		$id =  $_SESSION['uid'];
		$mrn = new mrnProfilesModel;
		$args = [
			'id' => $id,
			'search_term' => trim($_REQUEST['search']),
			'order' => 'sis_scheduled_booking.date',
			'paginate' => true
		];
		$info = $mrn->get_booking_portal($args);
		$all_courses =  $info['booked_courses'];

		$data = array(
			'meta_title' => 'Course Booking History',
			'view_file' => 'scheduled_courses/history',
			'results' => $all_courses,
		);
		$this->view($this->layout, $data);

	}
}
