<?php

class tags extends Controller{
	
	/** ===================================
	 * Get Tags
	====================================  */
	public function index_get($user_level=""){
		
		$studentsModel = new Students;
		
		if(!$user_level){
			$response = array(
				'error'=>'The user_level is required'
			);
			$this->response($response, 400);
		}
		
		//get the user level info
		$dbh = get_dbh();
		$sql = $dbh->prepare("SELECT * FROM form_custom_userlevels WHERE username_id='".$user_level."'");
		$sql->execute();
		$user_level_info = $sql->fetchAll(PDO::FETCH_OBJ)[0];
		
		
		$tags_args = array();
		if($user_level_info->id){
			$tags_args['user_level'] = $user_level_info->id;
		}else{
			$response = array(
				'error'=>'The user_level was not found'
			);
			$this->response($response, 400);
		}
		
		//Get the userlevel tags
		$data = $studentsModel->get_tags($tags_args);
		
		$response = array(
			'data'=>$data
		);
		$this->response($response, 200);
	}
	
	/** ===================================
	 * Update student tag for user logged
	====================================  */
	public function tag_link_post($student_unique_id="", $tag_username_id="")
	{
		global $user_info;
		$studentsModel = new Students;
		$entry_args = array();
		
		
		
		//Check the student
		if(!$student_unique_id){
			$response = array("error"=>'The student username_id is required');
			$this->response($response, 400);
		}else{
			//get the student info
			$dbh = get_dbh();
			$sql = $dbh->prepare("SELECT * FROM core_students WHERE username_id='".$student_unique_id."'");
			$sql->execute();
			$student_info = $sql->fetchAll(PDO::FETCH_OBJ)[0];
			$entry_args['student_id'] = $student_info->id;
		}
		
		//check if tag exists
		$tags_args = array('username_id'=>$tag_username_id);
		$tag_info = $studentsModel->get_tags($tags_args);
		$entry_args['tag_id'] = $tag_info['id'];
		
		//Delete current tags by the user
		$dbh = get_dbh();
		$sql = "DELETE FROM sis_student_tags_rel WHERE rec_id = '".$user_info['id']."' AND rel_id='".$student_info->id."'";
		$sth = $dbh->prepare($sql);
		$sth->execute();
		
		//insert
		$entry = $studentsModel->link_student_tag($entry_args);
		
		if($entry['error']){
			$response = array("error"=>$entry['error']);
			$this->response($response, 400);
		}
		
		$partner = new PartnersModel;
		$partnerTrack = [
			'rel_id'=>$student_info->id,
			'rec_id' => $_SESSION['uid'],
			'usergroup' => $_SESSION['usergroup'],
			'action_name'=> 'Tag Update',
			'record_id'=> $entry
		];
		
		$partner->addToTracker($partnerTrack);
		
		//Get the new message from the DB
		$data = 'success';
		
		//Show response
		$response = array(
			'data'=>$data
		);
		
		$this->response($response, 200);
	}
	
	
	/** ===================================
	 * Add a tag
	====================================  */
	public function index_post()
	{
		$studentsModel = new Students;
		
		$entry_args = $this->post();
		
		//get the user level id from username id
		$dbh = get_dbh();
		$sql = $dbh->prepare("SELECT * FROM form_custom_userlevels WHERE username_id='".$entry_args['user_level']."'");
		$sql->execute();
		$user_level_info = $sql->fetchAll(PDO::FETCH_OBJ)[0];
		$entry_args['user_level'] = $user_level_info->id;
		
		//insert
		$entry = $studentsModel->insert_update_tags($entry_args);
		
		if($entry['error']){
			$response = array("error"=>$entry['error']);
			$this->response($response, 400);
		}
		
		//Get the new message from the DB
		$data = $studentsModel->get_tags(array('id'=>$entry));
		
		//Show response
		$response = array(
			'data'=>$data
		);
		
		$this->response($response, 200);
	}
	
	/** ===================================
	 * Add a tag
	====================================  */
	public function index_put($tag_usernae_id="")
	{
		$studentsModel = new Students;
		
		$entry_args = $this->post();
		
		//get the user level id from username id
		$dbh = get_dbh();
		$sql = $dbh->prepare("SELECT * FROM form_custom_userlevels WHERE username_id='".$this->post('user_level')."'");
		$sql->execute();
		$user_level_info = $sql->fetchAll(PDO::FETCH_OBJ)[0];
		$entry_args['user_level'] = $user_level_info->id;
		
		if(!$tag_usernae_id){
			$response = array("error"=>"Tag username_id is required");
			$this->response($response, 400);
		}else{
			$entry_args['username_id'] = $tag_usernae_id;
		}
		
		
		//insert
		$entry = $studentsModel->insert_update_tags($entry_args);
		
		if($entry['error']){
			$response = array("error"=>$entry['error']);
			$this->response($response, 400);
		}
		
		//Get the new message from the DB
		$data = $studentsModel->get_tags(array('username_id'=>$tag_usernae_id));
		
		//Show response
		$response = array(
			'data'=>$data
		);
		
		$this->response($response, 200);
	}
	
	/** ===================================
	 * Delete a tag
	====================================  */
	public function index_delete($tag)
	{
		$studentsModel = new Students;
		
		// $entry_args = $this->delete();
		
		$message_args = [
			'username_id'=> $tag
		];
		
		//delete
		$entry = $studentsModel->delete_tag($message_args);
		
		if($entry['error']){
			$response = array("error"=>$entry['error']);
			$this->response($response, 400);
		}
		
		$data = 'success';
		
		//Show response
		$response = array(
			'data'=>$data
		);
		
		$this->response($response, 200);
	}
}