<?php 

/**
* 
*/
class programme extends Controller
{
	
	public function index($course_id)
	{

		$courses = new Courses;
		$students = new Students;
		$courses_args = array(
			'id'=>$course_id,
			'process_shortcodes'=>true,
			'school_id'=>$_SESSION['usergroup']
		);

		$course = $courses->get($courses_args);

		/** ===================================
		* Render View
		====================================  */
		$back_link = $_GET['back_link'];
		if($back_link){
			$back_link = str_replace("|", "=", $back_link);	
			$back_link = str_replace("*", "&", $back_link);	
			$back_link = front_end_url('search')."?".$back_link;
		}else{
			$back_link = front_end_url('search');
		}

		if (isset($_GET['debugging'])) {
        	echo "<pre>".print_r($this->settings,1)."</pre>";exit();
        }
        if (isset($this->settings['course_details'])) {
        	$settings=$this->settings['course_details'];
        }else{
        	$settings=array();
        }

		/** ===================================
		* Render View
		====================================  */
		$data = array(
			'meta_title'=>'Programme',
			'view_file'=>'programme/details',
			'programme'=>$course,
			'back_link'=>$back_link,
			'settings'=>$settings
		);

		$this->view($this->layout,$data);
	}


}
 ?>