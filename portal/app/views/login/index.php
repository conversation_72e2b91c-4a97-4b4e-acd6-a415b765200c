<?php
$_SESSION['lang'] = 'eng';
if (!empty($_COOKIE['selectedLanguage']) && empty($_SESSION['uid']) && !empty($this->settings['dashboard']['widgets']['showTranslations'])){
    $consent = json_decode($_COOKIE['cookie_consent'], true);
    if ($consent['status'] == 'accepted') {
        $_SESSION['lang'] = $_COOKIE['selectedLanguage'];
    } else {
        $_SESSION['lang'] = 'eng';
    }
}
$registrationLink = '';
$schoolID = pull_field('form_schools', 'db30', "WHERE id={$_SESSION['usergroup']}");
if ($schoolID == 12) {
    $registrationLink= pull_field('lead_preferences', 'db221219', "WHERE usergroup={$_SESSION['usergroup']} and (rec_archive ='' or rec_archive is null)");
}
?>

<div class="row ">
    <div class="col-sm-7">
        <div class="side_box">
            <div id="login" style="padding:15px;">
                <h5 style="padding-left: 0 !important;">
                    <?php echo translate('Login to your Account', $_SESSION['lang']) ?>
                </h5>
                <form method="POST">
                    <div class="row">
                        <div class="col-sm-12">
                            <?php if($_GET['error']){ ?>
                                <div class="alert alert-danger">
                                    <i class="fa fa-exclamation-triangle"></i> <?php echo translate($_GET['error'], $_SESSION['lang']); ?>
                                </div>
                            <?php } ?>

                            <div class="form-group">
                                <label>
                                    <?php echo translate('Email', $_SESSION['lang'])?>
                                </label>
                                <input class="form-control" type="email" placeholder="<?php echo translate('Enter your email address', $_SESSION['lang']) ?>" name="email_address" value="<?php echo $_POST["username"];?>">
                            </div>

                            <div class="form-group" style="margin-bottom: 0 !important;">
                                <label>
                                    <?php echo translate('Password', $_SESSION['lang'])?>
                                </label>
                                <input type="password" class="form-control" name="userpass" id="userpass" placeholder="<?php echo translate('Enter your password', $_SESSION['lang'])?>" value="" required="" />
                            </div>

                            <a title="<?php echo translate('Click Here To Reset Password',$_SESSION['lang'])?>" href="<?php echo $_SESSION['domain']; ?>/forgot" >
                                <?php echo translate('Forgot Password', $_SESSION['lang'])?>?
                            </a>

                            <button style="margin-top: 1rem;" type="submit" class="btn btn-primary btn-block">
                                <?php echo translate('Log In', $_SESSION['lang'])?>
                            </button>

                            <?php if (!empty($registrationLink)) : ?>
                                <div style="text-align: center">
                                    <?php echo translate("Don't Have An Account", $_SESSION['lang']) ?>?
                                    <a title="<?php echo translate('Click Here To Register', $_SESSION['lang']) ?>" href="<?php echo $registrationLink ?>">
                                         <?php echo translate(" Register Here", $_SESSION['lang'])?>
                                    </a>
                                </div>
                            <?php endif; ?>

                            <input type="hidden" name="login" value="TRUE" />
                            <input type="hidden" name="redirect" value="<?php echo $_REQUEST["redirect"]; ?>" />
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="col-sm-5">

        <?php
        $folder =explode(".", $_SERVER['HTTP_HOST']);
        $file = FRONTEND_PATH.'app/views/quicklinks/'.$folder[0].'/quicklinks.php';
        $usergroupLanguages= pull_field('lead_preferences','db50794', "WHERE usergroup={$_SESSION['usergroup']} AND (rec_archive is null or rec_archive='')" );
        $languages = explode(',',$usergroupLanguages);
        $allLanguages = [];
        foreach($languages as $language){
            $allLanguages[] = pull_field('form_languages','concat(db21281,"|", db253286)', "WHERE id ='{$language}'");
        }

        $consent = json_decode($_COOKIE['cookie_consent'], true);

        if($consent['status'] == 'accepted' && !empty($allLanguages) && !empty($this->settings['dashboard']['widgets']['showTranslations'])) :  ?>
            <div style="margin: 0.2rem;" title="<?php echo translate('Select Language', $_SESSION['lang']) ?>">
                <?php foreach ($allLanguages as $languages) {
                    $languages = explode('|',$languages);
                    $language = $languages[0];
                    if($language == 'eng'){
                        $languages[1] = 'gb';
                    }
                    ?>
                    <button id="<?php echo $language ?>" style="color: #000000" type="button" class="btn btn-xs" onclick="setLanguage('<?php echo $language?>')">
                        <span class="fi fi-<?php echo $languages[1] ?> fis"></span>

                    </button>
                <?php } ?>
            </div>
        <?php endif; ?>
        <div class="clearfix"></div>

        <?php
        if ($schoolID != 12) :
            //if quick links file exists added it
            if (file_exists($file)) {
                include($file);
            }else{ ?>
                <div class="side_box">
                    <h5><?=translate("Quicklinks", $_SESSION['lang'])?></h5>
                    <div class="content">
                        <ul class="list-group">
                            <?php
                            //hide all second stage pages

                            get_cms_nav("application","public","db656 IN ('forms','information','submit_page','upload','faq') and db647 NOT IN('home')","yes","list-group-item");
                            ?>
                        </ul>
                    </div>
                </div>
            <?php } ?>
        <?php endif; ?>
    </div>
</div>
<?php if($consent['status'] == 'accepted'  && !empty($this->settings['dashboard']['widgets']['showTranslations'])) :  ?>
    <script src="//cdn.jsdelivr.net/npm/js-cookie@3.0.5/dist/js.cookie.min.js"></script>

    <script>
		$(function() {
			//check if the language storage is already there
			let selectedLanguage = localStorage.getItem('selectedLanguage');
			if (selectedLanguage !== null) {
				Cookies.set('selectedLanguage', selectedLanguage);
				$('#'+selectedLanguage).addClass('active');
			}else{
				Cookies.set('selectedLanguage', 'eng');
				$('#eng').addClass('active');
			}


		});

		function setLanguage(language){
			localStorage.setItem('selectedLanguage', language);
			Cookies.set('selectedLanguage', language);
			$('#'+language).addClass('active');
			location.reload();
		}
    </script>

<?php endif; ?>

<script>
	$(function (){

		$(document).on('click','.cc-btn-reject',function(){
			location.reload();
		});

		$(document).on('click','.cc-btn-accept-all',function(){
			location.reload();
		});

	});
</script>
<style>
    .active {
        background-color: #C4E0EF;
    }
</style>
