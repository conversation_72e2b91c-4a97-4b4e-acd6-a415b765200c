<?php
global $user_info, $school_info, $db, $paginator;

?>
</div>

<div id="table_serach">
	<div class="container">
		<nav aria-label="breadcrumb">
			<ol class="breadcrumb">
				<li>
					<a href="<?php echo front_end_url('dashboard'); ?>">
						<i class="fa fa-arrow-left" aria-hidden="true"> Back To Dashboard</i>
					</a>
				</li>
			</ol>
		</nav>

		<h3><?php echo $data['meta_title']; ?></h3>

		<form method="GET" action="<?php echo current_url(); ?>" id="search_form">
			<div class="input-group mb-3">
				<input type="text" name="search" class="form-control" placeholder="Start typing to search..." aria-label="Recipient's username" aria-describedby="button-addon2" style=" border: solid 1px #e1e1e1;" value="<?php echo $_GET['search']; ?>">
				<div class="input-group-append">
					<?php if ($_GET['search']) { ?>
						<button class="btn btn-outline-secondary clear_form" type="button" id="button-addon2" style="background-color: #fff; border: solid 1px #e1e1e1;">
							<i class="fa fa-times"></i>
						</button>
					<?php } ?>
					<button class="btn btn-outline-secondary" type="submit" id="button-addon2" style="background-color: #fff; border: solid 1px #e1e1e1;">Search</button>
				</div>
			</div>
		</form>

		<script>
			$(document).ready(function() {
				$(".clear_form").click(function() {
					$("#search_form .form-control").val("");
					$("#search_form").submit();
				});
			});
		</script>
		<div class="total" style="padding-top: 10px;">
			<?php if ($paginator->total) {
				echo $paginator->total; ?> results found <?php } ?>
			<div class="btn-group btn-sm" role="group" style="margin-right: 2px;">

			</div>
			<!-- 			<a href="<?php //echo front_end_url('course_levels/all/applicants?export_to_csv=1'); 
										?>" class="btn btn-sm btn-info" style="margin:5px">Export</a>
 -->
		</div>
		<ul class="nav" id="table_tabs">
			<li class="nav-item">
				<a class="nav-link <?php if ($data['active_tab'] == "all") {
										echo 'active';
									} ?>" href="<?php echo front_end_url("/tutor_courses/evaluations/{$data['sch_course_id']}"); ?>">All</a>
			</li>


		</ul>
	</div>
</div>

<div class="container">
	<div class="main_content_wrap">
		<div id="university_students">

			<?php if (!count($data['results']['evaluations'])) { ?>
				<div class="no_results text-center">
					<span class="fa fa-search"></span>
					<h4>No evaluations found</h4>
				</div>
			<?php } else { ?>

				<div class="table-responsive" style="overflow: auto;">
					<table class="table table-hover table_with_tabs">
						<thead>
							<tr>
								<th scope="col"><a href="#">ID</a></th>
								<th scope="col"><a href="#">Date</a></th>
								<?php foreach ($data['results']['fields'] as $fields) { ?>
									<th scope="col">
										<a href="#"> <?php echo $fields['name']; ?></a>
									</th>

								<?php } ?>
							</tr>
						</thead>
						<tbody>
							<?php
							foreach ($data['results']['evaluations'] as $result) {
							?>
								<tr>
									<td><?php echo $result['id'] ?></td>
									<td><?php echo $result['date'] ?></td>
									<?php foreach ($data['results']['fields'] as $fields) { ?>
										<td><?php echo str_replace('_', ' ', $result[$fields['db_field_name']]); ?></td>
									<?php } ?>

								</tr>
							<?php } ?>
						</tbody>
					</table>
				</div>

			<?php } ?>

			<div class="text-center" style="padding: 30px; display: flex; justify-content: center;">
				<?php echo $paginator->links(); ?>
			</div>
		</div>


	</div>