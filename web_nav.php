    <!--NAVIGATION-->

    <hr width="100%" size="0" noshade="noshade"/>
    
    <div class="navigation">
    <ul>
    <div class="info" id="instructions">This is the navigation. It is fully customisable to suit your needs</div>
    <h3>Navigation</h3>
    <?php
    error_log('front_website_url before printing links: '.$front_website_url);
	if($_SESSION['loggedin']){ // only show to logged in users
	echo '<li><h4 style=\"color:#fff\">Welcome Back '.$_SESSION['fullname'].'</h4></li>
	<li><a href="'.$front_website_url.'/application/logout" style="font-weight:bold">Log Out!</a></li>';
	echo'<div class="clear"></div>';
	echo '<li class="first" style="color:#144362; font-weight:bold;"><h3>Your Application</h3><hr/></li>';
    echo '<ul>';
		get_cms_nav("application","private","db656 IN ('forms','information','faq')");
    echo '</ul>';
    }
    ?>
    </ul>
    <ul>
    <?php
        get_cms_nav("application","public","db656 IN ('forms','information','faq')");
    ?>
    <div class="clear"></div>
    </ul>
	<?php
        if(!$_SESSION['loggedin']){ // only show to logged in users
    ?>
    <ul>
    <li class=" "><a href="<?php echo $front_website_url; ?>/application/register">Start a new application >></a></li>
    <li class=" "><a href="<?php echo $front_website_url; ?>/application/login">Continue  an application you have already started >></a></li>
    <li class=" "><a href="<?php echo $front_website_url; ?>/application/login">Login to view the status of a completed application >></a></li>
    </ul>
    <?php
        }
    ?>
    
    </div>
    <!--NAVIGATION END-->
