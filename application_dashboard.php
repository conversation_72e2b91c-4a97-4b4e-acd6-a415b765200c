<?PHP
session_start();

include_once "engine/admin/inc/lib.inc.php";// call functions
include_once "engine/admin/inc/site_lib.inc.php";// call functions
define("path_to_engine", "engine");// this is used by the form to

$survey_pages = survey_pages;
$survey_table = survey_table;

// add margin if this is not controller
if (getpagename() == 'form.php') {
    $cntr = "0px";
} else {
    $cntr = "3px";
}
?>
<script src="<?php echo engine_url; ?>/js/global.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript" language="JavaScript" src="js/pop_msg.js"></script><!-- Popup-->


<?php
####### Select the correct page view request ######
if (isset($_REQUEST['pick_page'])) {
    $pick_page = $_REQUEST['pick_page'];
} else {
    $pick_page = $pick_page;
}

if ($pick_page) {
    $option4 = $pick_page;
} else {
    $option4 = $_GET['pg'];
}

// IF AN INTERNAL REC EXISTS PULL IT above the get post
if ($int_rec) {
    $rec_link = $int_rec; // internal record
} else {
    $rec_link = $_GET['rec'];     // get record
}

// if this is a copy then simply remove record value
if (!isset($_GET['cpy'])) {
    $record = $rec_link;
}
$record_pull = $rec_link;

// pick up the posted page id if one is not presented.
if (!isset($option4)) {
    $option4 = $_POST['option4'];
}


// if admin only then don't show this
//if($show_editbutton==1){
//edit record
/*echo '<a title="Edit entry" class="thickbox remove_underline positive" href="controller.php?width=800&amp;height=600&amp;vw='.$_GET['vw'].'&amp;pg='.$_GET['pg'].'&amp;pick_page='.$pick_page.'&amp;rec='.$record_pull.'&amp;ref='.$_GET['ref'].'&amp;jqmRefresh=true"><div style="margin: 10px 0;" class="float_right quicklinks">Edit Entry</div></a>';*/
//}


// get the nameof the page
$og_page_name = pull_field($survey_pages, "page_name", "WHERE page_id='$option4'");
$og_page_id = pull_field($survey_pages, "project", "WHERE page_id='$option4'");
$og_pagecat_name = pull_field("system_cat", "sys_cat_abv", "WHERE sys_cat_id='$og_page_id'");
$og_page_name = str_replace(" ", "_", strtolower($og_page_name));
$page_table = $og_pagecat_name . "_" . $og_page_name;

// select all the fields
$dbh = get_dbh();

$query = "SELECT * FROM $survey_table WHERE pg_id='$option4' AND locked='2' ORDER BY form_order,name";

//pull results
$stmt = $dbh->prepare($query);
$stmt->execute();
$result = $stmt->fetchAll(PDO::FETCH_ASSOC);
$num = $stmt->rowCount();
$_token = generateCsrfToken();
// write html
echo "<form value=\"?action=process\" method=\"post\" enctype=\"multipart/form-data\" onSubmit=\"return formCheck(this)\">\n";
echo "<table width=\"100%\" bordercolor=\"#ECE9D8\" border=\"0\" cellspacing=\"0\" cellpadding=\"4\" class=\"maintxt\">";
echo "\n";

// for the purpose of selecting the same forom once the page is posted, send this hidden field carrying with it the page value
echo '<input name="option4" type="hidden" value="' . $option4 . '" />';
echo '<input name="_token" type="hidden" value="' . $_token . '" />';

if ($_POST['process'] == "1") {

    if (!verifyCsrfToken($_POST['_token'])) {
        die("CSRF token validation failed.");
    }
    // include the processing file
    if (isset($prefix)) {
        $filename = "$prefix/models/scpt_" . $page_table . ".php";
    } else {
        $filename = "models/scpt_" . $page_table . ".php";
    }

    //// Run the processing file //////
    if (file_exists($filename)) {
        //// check if submission is an upload. If so then inclue upload module
        if (isset($_POST['uploadsNeeded']) && $_POST['uploadsNeeded'] !== '') {
            include("models/scpt_uploader.php");
        }
        include("$filename"); // insert script
    } else {
        $msg_response = "<div style=\"background-color:#FFFF99; padding:5px; border:#FFFF90 solid 1px; text-align:center;margin:2px auto;\">Sorry this form has not been fully published as yet</div>";
    }

    // echo message
    echo $msg_response;

}

// run though loop
foreach ($result as $user) {

    $form_id = $user['form_id'];
    $name = $user['name'];
    $db_field_name = $user['db_field_name'];
    $form_type = $user['type'];
    $box_size = $user['box_size'];
    $description = $user['description'];
    $required = $user['required'];
    $default_values = $user['figures'];
    $pg_id = $user['pg_id'];
    $form_order = $user['form_order'];
    $locked = $user['locked'];

    // GET CURRENT VALUE
    if ($form_type !== 'title') {
        $current_value = pull_field($page_table, $db_field_name, "WHERE id='$record_pull' AND usergroup='$_SESSION[usergroup]'"); // select but only select content in the user's group

        if ($current_value == '') {
            $current_value = "_";
        }
    }

    // SELECT ALL TITLE FIELDS
    if ($form_type == "title") {

        // replace number in front with blank
        $name = str_replace("Stage 1 - ", " ", $name);
        $name = str_replace("Stage 3 - ", " ", $name);
        echo '<tr>
		  <td class="form_title" colspan="2"><h2>' . $name . '</h2></td>
		  </tr>';
    }

    // SELECT ALL TITLE FIELDS
    if ($form_type == "subtitle") {
        // replace number in front with blank
        //$name=str_replace("Stage 1 - "," ",$name);
        $name = str_replace("Stage 3 - ", "Stage 2 - ", $name);
        $name = str_replace("Stage 4 - ", "Stage 3 - ", $name);
        $name = str_replace("Stage 5 - ", "Stage 4 - ", $name);
        $name = str_replace("Stage 7 - ", "Stage 5 - ", $name);
        $name = str_replace("Assigned", " ", $name);
        echo '<tr>
		  <td class="form_subtitle" colspan="2"><h2>' . $name . '</h2></td>
		  </tr>';
    }

    // SELECT ALL GENERAL TEXT
    if ($form_type == "textonly") {
        echo '<tr>
		  <td class="maintxt norm_txt" colspan="2">' . $name . '</td>
		  </tr>';
    }


    // SELECT ALL NUMBER FIELDS
    if ($form_type == "number") {
        echo '<tr>
		  <td class="form_field">' . $name . '</td>
		  <td class="form_field norm_txt">' . $current_value . '</td>
		  </tr>';
    }

    // SELECT ALL TEXT FIELDS
    if ($form_type == "text") {
        echo '<tr>
		  <td class="form_field">' . $name . '</td>
		  <td class="form_field norm_txt">' . $current_value . '</td>
		  </tr>';
    }

    // SELECT ALL BOX FIELDS
    if ($form_type == "box") {
        echo '<tr>
		  <td class="form_field" valign="top">' . $name . '</td>
		  <td class="form_field norm_txt">' . $current_value . '</td>
		  </tr>';
    }


    //Add A Checkbox Field ########
    if ($form_type == "radio_yes_no") {
        // get predefined field
        // 1 = current value, 2 = current in loop

        //show symbols
        if ($current_value == "yes") {
            $show_current_val = '<img src="' . website_url . '/images/icon_tick.png" title="' . $current_value . '" alt="' . $current_value . '">';
        } else {
            //$show_current_val='<img src="'.website_url.'/images/icon_del.png" title="'.$current_value.'" alt="'.$current_value.'">';
            $show_current_val = "-";
        }

        if ($show_icons == "no") {
            $show_current_val = $current_value;
        }

        echo '<tr>
		  <td class="form_field" valign="top">' . $name . '</td>
		  <td class="form_field norm_txt">' . $show_current_val . '</td>
		  </tr>';
    }


    // SELECT ALL CODE FIELDS
    if ($form_type == "code") {
        echo '<tr>
		  <td class="form_field" valign="top">' . $name . '</td>
		  <td class="form_field norm_txt">' . $current_value . '</td>
		  </tr>';
    }

    // SELECT ALL HTML FIELDS
    if ($form_type == "html") {
        echo '<tr>
		  <td class="form_field" valign="top">' . $name . '</td>
		  <td class="form_field norm_txt">' . $current_value . '</td>
		  </tr>';
    }


    // SELECT ALL BOX FIELDS
    if ($form_type == "hidden_get") {
        $val_get = $_GET["$default_values"];// get the value requested
        echo '<input value="' . $val_get . '" type="hidden" name="' . $db_field_name . '" id="' . $db_field_name . '" />';
    }

    // SELECT ALL BOX FIELDS
    if ($form_type == "hidden_session") {
        $val_session = $_SESSION["$default_values"];// get the value requested
        echo '<input type="hidden" name="' . $db_field_name . '" id="' . $db_field_name . '" value="' . $val_session . '"/>';
    }

    // SELECT DROPDOWN
    if ($form_type == "dropdown") {

        //if current value show nothing
        if ($current_value == "not specified") {
            $current_value = '';
        }
        echo '<tr>
    <td class="form_field">' . $name . '</td>
	<td  class="form_field norm_txt">' . $current_value . '</td></tr>';
    }


// SELECT LOOKUP
    if ($form_type == "include_file") {
        echo '<tr>
    <td class="form_field">' . $name . '<br/></td>
	<td  class="form_field norm_txt">';
        ////////////// go and get the files///////////
        lookup_file($default_values, $db_field_name, $current_value);
        /////////////////////////
        /*
        if(isset($current_value) && $current_value!==''){
        echo '<br/><a href="media/uploads/'.$current_value.'" target="_blank">Open [ '.$current_value,' ]</a>';
        }
        //echo '  <span class="button" style="width:75px"><a href="#"> Upload File </a></span>';
            echo '<a href="inc/uploadForm.php?width=350&height=150&vw='.$_GET['vw'].'&pg='.$_GET['pg'].'&jqmRefresh=true" class="thickbox remove_underline positive float_left" title="Upload a New File"><div class="quicklinks" style="margin-top:10px">Upload New File</div></a>';
      echo '</td></tr>';
      */
    }

// SELECT LOOKUP lIST
    if ($name == "Evidence feedback") {
        $random_style = 'style="font-weight:normal; background:#f0f0f0"';
    }
    if ($form_type == "dynamic_list") {
        echo '<tr>
    <td class="form_field" ' . $random_style . '>' . $name . '</td>';
        ////////////// go and get the files///////////
        // get default values and break into two as first part is the table name and the next part is the filed name
        list($default_table, $default_field) = explode(",", $default_values);
        $display_val = list_of_views($default_table, $default_field, $db_field_name, $current_value, "current_value_only");
        //////////////////////////////////////////////
        echo '<td  class="form_field norm_txt"  ' . $random_style . '>' . $display_val . '</td></tr>';
    }

    // SELECT LOOKUP lIST USERGROUP ONLY
    if ($form_type == "dynamic_list_group") {
        echo '<tr>
		<td class="form_field">' . $name . '</td>';
        ////////////// go and get the files///////////
        // get default values and break into two as first part is the table name and the next part is the filed name
        list($default_table, $default_field) = explode(",", $default_values);
        $display_val = list_of_views($default_table, $default_field, $db_field_name, $current_value, "current_value_only");
        //////////////////////////////////////////////
        echo '<td  class="form_field norm_txt">' . $display_val . '</td></tr>';
    }

// SELECT FORMS lIST
    if ($form_type == "dynamic_forms") {
        echo '<tr>
    <td class="form_field" $random_style>' . $name . '</td>
	<td  class="form_field norm_txt">' . $current_value . '</td></tr>';
    }
    //reset
    $random_style = '';

// Predefined Date Value
    if ($form_type == "date_field") {

        // if 000000 then show nothing
        if ($current_value == "0000-00-00") {
            $current_value = '';
        } else {
            $current_value = $current_value;
            $current_value = date('d/m/Y', strtotime($current_value));//change date format
            if ($current_value == "01/01/1970") {
                $current_value = '';
            }
        }

        echo '<tr>
    <td class="form_field">' . $name . '</td>
	<td  class="form_field norm_txt">' . $current_value . '</td></tr>';
    }


// IMAGE UPLOAD
    if ($form_type == "upload") {
        echo '<tr>
		  <td class="form_field">' . $name . '</td>';

        echo '<td class="form_field">' . $current_value . '</td>';
        echo '</tr>';
    }


// SELECT CHECKBOX
    if ($form_type == "checkboxes") {
        echo '<tr>
    <td class="form_field" valign="top">' . $name . '</td>
	<td  class="form_field norm_txt">' . $current_value . '</td></tr>';
    }


// reset
    $current_value = "";
// end of functions
}    // end of whiie loop


echo "</table>\n";
echo "</form>\n";
?>
<div style="clear:both"></div> 